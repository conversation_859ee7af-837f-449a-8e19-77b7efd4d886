{"version": 3, "sources": ["../../@medusajs/dashboard/dist/reservation-list-CSLD2U6C.mjs"], "sourcesContent": ["import {\n  DateCell\n} from \"./chunk-5HNRTDDS.mjs\";\nimport \"./chunk-LQTHYS2Z.mjs\";\nimport {\n  PlaceholderCell\n} from \"./chunk-P3UUX2T6.mjs\";\nimport {\n  _DataTable,\n  useDataTable\n} from \"./chunk-UE6PO4FK.mjs\";\nimport \"./chunk-YEDAFXMB.mjs\";\nimport \"./chunk-AOFGTNG6.mjs\";\nimport \"./chunk-EMIHDNB7.mjs\";\nimport \"./chunk-SXYXTC2L.mjs\";\nimport \"./chunk-M3VFKDXJ.mjs\";\nimport {\n  SingleColumnPage\n} from \"./chunk-2RQLKDBF.mjs\";\nimport {\n  useQueryParams\n} from \"./chunk-C76H5USB.mjs\";\nimport \"./chunk-KSV3NQOT.mjs\";\nimport {\n  useExtension\n} from \"./chunk-C5P5PL3E.mjs\";\nimport \"./chunk-LPEUYMRK.mjs\";\nimport {\n  ActionMenu\n} from \"./chunk-3NJTXRIY.mjs\";\nimport \"./chunk-OC7BQLYI.mjs\";\nimport \"./chunk-NYXYHZE6.mjs\";\nimport {\n  useStockLocations\n} from \"./chunk-32IQRUVY.mjs\";\nimport {\n  useDeleteReservationItem,\n  useReservationItems\n} from \"./chunk-FVC7M755.mjs\";\nimport \"./chunk-6I62UDJA.mjs\";\nimport \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-RPUOO7AV.mjs\";\n\n// src/routes/reservations/reservation-list/components/reservation-list-table/reservation-list-table.tsx\nimport { Button, Container, Heading, Text } from \"@medusajs/ui\";\nimport { useTranslation as useTranslation4 } from \"react-i18next\";\nimport { Link } from \"react-router-dom\";\n\n// src/routes/reservations/reservation-list/components/reservation-list-table/use-reservation-table-columns.tsx\nimport { createColumnHelper } from \"@tanstack/react-table\";\nimport { useMemo } from \"react\";\nimport { useTranslation as useTranslation2 } from \"react-i18next\";\n\n// src/routes/reservations/reservation-list/components/reservation-list-table/reservation-actions.tsx\nimport { PencilSquare, Trash } from \"@medusajs/icons\";\nimport { usePrompt } from \"@medusajs/ui\";\nimport { useTranslation } from \"react-i18next\";\nimport { jsx } from \"react/jsx-runtime\";\nvar ReservationActions = ({\n  reservation\n}) => {\n  const { t } = useTranslation();\n  const prompt = usePrompt();\n  const { mutateAsync } = useDeleteReservationItem(reservation.id);\n  const handleDelete = async () => {\n    const res = await prompt({\n      title: t(\"general.areYouSure\"),\n      description: t(\"reservations.deleteWarning\"),\n      confirmText: t(\"actions.delete\"),\n      cancelText: t(\"actions.cancel\")\n    });\n    if (!res) {\n      return;\n    }\n    await mutateAsync();\n  };\n  return /* @__PURE__ */ jsx(\n    ActionMenu,\n    {\n      groups: [\n        {\n          actions: [\n            {\n              label: t(\"actions.edit\"),\n              to: `${reservation.id}/edit`,\n              icon: /* @__PURE__ */ jsx(PencilSquare, {})\n            }\n          ]\n        },\n        {\n          actions: [\n            {\n              label: t(\"actions.delete\"),\n              onClick: handleDelete,\n              icon: /* @__PURE__ */ jsx(Trash, {})\n            }\n          ]\n        }\n      ]\n    }\n  );\n};\n\n// src/routes/reservations/reservation-list/components/reservation-list-table/use-reservation-table-columns.tsx\nimport { jsx as jsx2 } from \"react/jsx-runtime\";\nvar columnHelper = createColumnHelper();\nvar useReservationTableColumns = () => {\n  const { t } = useTranslation2();\n  return useMemo(\n    () => [\n      columnHelper.accessor(\"inventory_item\", {\n        header: t(\"fields.sku\"),\n        cell: ({ getValue }) => {\n          const inventoryItem = getValue();\n          if (!inventoryItem || !inventoryItem.sku) {\n            return /* @__PURE__ */ jsx2(PlaceholderCell, {});\n          }\n          return /* @__PURE__ */ jsx2(\"div\", { className: \"flex size-full items-center overflow-hidden\", children: /* @__PURE__ */ jsx2(\"span\", { className: \"truncate\", children: inventoryItem.sku }) });\n        }\n      }),\n      /**\n       * TEMP: hide this column until a link is added\n       */\n      // columnHelper.accessor(\"line_item\", {\n      //   header: t(\"fields.order\"),\n      //   cell: ({ getValue }) => {\n      //     const inventoryItem = getValue()\n      //\n      //     if (!inventoryItem || !inventoryItem.order?.display_id) {\n      //       return <PlaceholderCell />\n      //     }\n      //\n      //     return (\n      //       <div className=\"flex size-full items-center overflow-hidden\">\n      //         <LinkButton to={`/orders/${inventoryItem.order.id}`}>\n      //           <span className=\"truncate\">\n      //             #{inventoryItem.order.display_id}\n      //           </span>\n      //         </LinkButton>\n      //       </div>\n      //     )\n      //   },\n      // }),\n      columnHelper.accessor(\"description\", {\n        header: t(\"fields.description\"),\n        cell: ({ getValue }) => {\n          const description = getValue();\n          if (!description) {\n            return /* @__PURE__ */ jsx2(PlaceholderCell, {});\n          }\n          return /* @__PURE__ */ jsx2(\"div\", { className: \"flex size-full items-center overflow-hidden\", children: /* @__PURE__ */ jsx2(\"span\", { className: \"truncate\", children: description }) });\n        }\n      }),\n      columnHelper.accessor(\"created_at\", {\n        header: t(\"fields.created\"),\n        cell: ({ getValue }) => {\n          const created = getValue();\n          return /* @__PURE__ */ jsx2(DateCell, { date: created });\n        }\n      }),\n      columnHelper.accessor(\"quantity\", {\n        header: () => /* @__PURE__ */ jsx2(\"div\", { className: \"flex size-full items-center justify-end overflow-hidden text-right\", children: /* @__PURE__ */ jsx2(\"span\", { className: \"truncate\", children: t(\"fields.quantity\") }) }),\n        cell: ({ getValue }) => {\n          const quantity = getValue();\n          return /* @__PURE__ */ jsx2(\"div\", { className: \"flex size-full items-center justify-end overflow-hidden text-right\", children: /* @__PURE__ */ jsx2(\"span\", { className: \"truncate\", children: quantity }) });\n        }\n      }),\n      columnHelper.display({\n        id: \"actions\",\n        cell: ({ row }) => {\n          const reservation = row.original;\n          return /* @__PURE__ */ jsx2(ReservationActions, { reservation });\n        }\n      })\n    ],\n    [t]\n  );\n};\n\n// src/routes/reservations/reservation-list/components/reservation-list-table/use-reservation-table-filters.tsx\nimport { useTranslation as useTranslation3 } from \"react-i18next\";\nvar useReservationTableFilters = () => {\n  const { t } = useTranslation3();\n  const { stock_locations } = useStockLocations({\n    limit: 1e3\n  });\n  const filters = [];\n  if (stock_locations) {\n    const stockLocationFilter = {\n      type: \"select\",\n      options: stock_locations.map((s) => ({\n        label: s.name,\n        value: s.id\n      })),\n      key: \"location_id\",\n      searchable: true,\n      label: t(\"fields.location\")\n    };\n    filters.push(stockLocationFilter);\n  }\n  filters.push({\n    type: \"date\",\n    key: \"created_at\",\n    label: t(\"fields.createdAt\")\n  });\n  return filters;\n};\n\n// src/routes/reservations/reservation-list/components/reservation-list-table/use-reservation-table-query.tsx\nvar useReservationTableQuery = ({\n  pageSize = 20,\n  prefix\n}) => {\n  const raw = useQueryParams(\n    [\"location_id\", \"offset\", \"created_at\", \"quantity\", \"updated_at\", \"order\"],\n    prefix\n  );\n  const { location_id, created_at, updated_at, quantity, offset, ...rest } = raw;\n  const searchParams = {\n    limit: pageSize,\n    offset: offset ? parseInt(offset) : void 0,\n    location_id,\n    created_at: created_at ? JSON.parse(created_at) : void 0,\n    updated_at: updated_at ? JSON.parse(updated_at) : void 0,\n    ...rest\n  };\n  return {\n    searchParams,\n    raw\n  };\n};\n\n// src/routes/reservations/reservation-list/components/reservation-list-table/reservation-list-table.tsx\nimport { jsx as jsx3, jsxs } from \"react/jsx-runtime\";\nvar PAGE_SIZE = 20;\nvar ReservationListTable = () => {\n  const { t } = useTranslation4();\n  const { searchParams } = useReservationTableQuery({\n    pageSize: PAGE_SIZE\n  });\n  const { reservations, count, isPending, isError, error } = useReservationItems({\n    ...searchParams\n  });\n  const filters = useReservationTableFilters();\n  const columns = useReservationTableColumns();\n  const { table } = useDataTable({\n    data: reservations || [],\n    columns,\n    count,\n    enablePagination: true,\n    getRowId: (row) => row.id,\n    pageSize: PAGE_SIZE\n  });\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsxs(Container, { className: \"divide-y p-0\", children: [\n    /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center justify-between px-6 py-4\", children: [\n      /* @__PURE__ */ jsxs(\"div\", { children: [\n        /* @__PURE__ */ jsx3(Heading, { children: t(\"reservations.domain\") }),\n        /* @__PURE__ */ jsx3(Text, { className: \"text-ui-fg-subtle\", size: \"small\", children: t(\"reservations.subtitle\") })\n      ] }),\n      /* @__PURE__ */ jsx3(Button, { variant: \"secondary\", size: \"small\", asChild: true, children: /* @__PURE__ */ jsx3(Link, { to: \"create\", children: t(\"actions.create\") }) })\n    ] }),\n    /* @__PURE__ */ jsx3(\n      _DataTable,\n      {\n        table,\n        columns,\n        pageSize: PAGE_SIZE,\n        count,\n        isLoading: isPending,\n        filters,\n        pagination: true,\n        navigateTo: (row) => row.id,\n        search: false\n      }\n    )\n  ] });\n};\n\n// src/routes/reservations/reservation-list/reservation-list.tsx\nimport { jsx as jsx4 } from \"react/jsx-runtime\";\nvar ReservationList = () => {\n  const { getWidgets } = useExtension();\n  return /* @__PURE__ */ jsx4(\n    SingleColumnPage,\n    {\n      widgets: {\n        before: getWidgets(\"reservation.list.before\"),\n        after: getWidgets(\"reservation.list.after\")\n      },\n      children: /* @__PURE__ */ jsx4(ReservationListTable, {})\n    }\n  );\n};\nexport {\n  ReservationList as Component\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoDA,mBAAwB;AAOxB,yBAAoB;AA+CpB,IAAAA,sBAA4B;AAiI5B,IAAAC,sBAAkC;AAiDlC,IAAAA,sBAA4B;AAhO5B,IAAI,qBAAqB,CAAC;AAAA,EACxB;AACF,MAAM;AACJ,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,QAAM,SAAS,UAAU;AACzB,QAAM,EAAE,YAAY,IAAI,yBAAyB,YAAY,EAAE;AAC/D,QAAM,eAAe,YAAY;AAC/B,UAAM,MAAM,MAAM,OAAO;AAAA,MACvB,OAAO,EAAE,oBAAoB;AAAA,MAC7B,aAAa,EAAE,4BAA4B;AAAA,MAC3C,aAAa,EAAE,gBAAgB;AAAA,MAC/B,YAAY,EAAE,gBAAgB;AAAA,IAChC,CAAC;AACD,QAAI,CAAC,KAAK;AACR;AAAA,IACF;AACA,UAAM,YAAY;AAAA,EACpB;AACA,aAAuB;AAAA,IACrB;AAAA,IACA;AAAA,MACE,QAAQ;AAAA,QACN;AAAA,UACE,SAAS;AAAA,YACP;AAAA,cACE,OAAO,EAAE,cAAc;AAAA,cACvB,IAAI,GAAG,YAAY,EAAE;AAAA,cACrB,UAAsB,wBAAI,cAAc,CAAC,CAAC;AAAA,YAC5C;AAAA,UACF;AAAA,QACF;AAAA,QACA;AAAA,UACE,SAAS;AAAA,YACP;AAAA,cACE,OAAO,EAAE,gBAAgB;AAAA,cACzB,SAAS;AAAA,cACT,UAAsB,wBAAI,OAAO,CAAC,CAAC;AAAA,YACrC;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAIA,IAAI,eAAe,mBAAmB;AACtC,IAAI,6BAA6B,MAAM;AACrC,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,aAAO;AAAA,IACL,MAAM;AAAA,MACJ,aAAa,SAAS,kBAAkB;AAAA,QACtC,QAAQ,EAAE,YAAY;AAAA,QACtB,MAAM,CAAC,EAAE,SAAS,MAAM;AACtB,gBAAM,gBAAgB,SAAS;AAC/B,cAAI,CAAC,iBAAiB,CAAC,cAAc,KAAK;AACxC,uBAAuB,oBAAAC,KAAK,iBAAiB,CAAC,CAAC;AAAA,UACjD;AACA,qBAAuB,oBAAAA,KAAK,OAAO,EAAE,WAAW,+CAA+C,cAA0B,oBAAAA,KAAK,QAAQ,EAAE,WAAW,YAAY,UAAU,cAAc,IAAI,CAAC,EAAE,CAAC;AAAA,QACjM;AAAA,MACF,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAwBD,aAAa,SAAS,eAAe;AAAA,QACnC,QAAQ,EAAE,oBAAoB;AAAA,QAC9B,MAAM,CAAC,EAAE,SAAS,MAAM;AACtB,gBAAM,cAAc,SAAS;AAC7B,cAAI,CAAC,aAAa;AAChB,uBAAuB,oBAAAA,KAAK,iBAAiB,CAAC,CAAC;AAAA,UACjD;AACA,qBAAuB,oBAAAA,KAAK,OAAO,EAAE,WAAW,+CAA+C,cAA0B,oBAAAA,KAAK,QAAQ,EAAE,WAAW,YAAY,UAAU,YAAY,CAAC,EAAE,CAAC;AAAA,QAC3L;AAAA,MACF,CAAC;AAAA,MACD,aAAa,SAAS,cAAc;AAAA,QAClC,QAAQ,EAAE,gBAAgB;AAAA,QAC1B,MAAM,CAAC,EAAE,SAAS,MAAM;AACtB,gBAAM,UAAU,SAAS;AACzB,qBAAuB,oBAAAA,KAAK,UAAU,EAAE,MAAM,QAAQ,CAAC;AAAA,QACzD;AAAA,MACF,CAAC;AAAA,MACD,aAAa,SAAS,YAAY;AAAA,QAChC,QAAQ,UAAsB,oBAAAA,KAAK,OAAO,EAAE,WAAW,sEAAsE,cAA0B,oBAAAA,KAAK,QAAQ,EAAE,WAAW,YAAY,UAAU,EAAE,iBAAiB,EAAE,CAAC,EAAE,CAAC;AAAA,QAChO,MAAM,CAAC,EAAE,SAAS,MAAM;AACtB,gBAAM,WAAW,SAAS;AAC1B,qBAAuB,oBAAAA,KAAK,OAAO,EAAE,WAAW,sEAAsE,cAA0B,oBAAAA,KAAK,QAAQ,EAAE,WAAW,YAAY,UAAU,SAAS,CAAC,EAAE,CAAC;AAAA,QAC/M;AAAA,MACF,CAAC;AAAA,MACD,aAAa,QAAQ;AAAA,QACnB,IAAI;AAAA,QACJ,MAAM,CAAC,EAAE,IAAI,MAAM;AACjB,gBAAM,cAAc,IAAI;AACxB,qBAAuB,oBAAAA,KAAK,oBAAoB,EAAE,YAAY,CAAC;AAAA,QACjE;AAAA,MACF,CAAC;AAAA,IACH;AAAA,IACA,CAAC,CAAC;AAAA,EACJ;AACF;AAIA,IAAI,6BAA6B,MAAM;AACrC,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,QAAM,EAAE,gBAAgB,IAAI,kBAAkB;AAAA,IAC5C,OAAO;AAAA,EACT,CAAC;AACD,QAAM,UAAU,CAAC;AACjB,MAAI,iBAAiB;AACnB,UAAM,sBAAsB;AAAA,MAC1B,MAAM;AAAA,MACN,SAAS,gBAAgB,IAAI,CAAC,OAAO;AAAA,QACnC,OAAO,EAAE;AAAA,QACT,OAAO,EAAE;AAAA,MACX,EAAE;AAAA,MACF,KAAK;AAAA,MACL,YAAY;AAAA,MACZ,OAAO,EAAE,iBAAiB;AAAA,IAC5B;AACA,YAAQ,KAAK,mBAAmB;AAAA,EAClC;AACA,UAAQ,KAAK;AAAA,IACX,MAAM;AAAA,IACN,KAAK;AAAA,IACL,OAAO,EAAE,kBAAkB;AAAA,EAC7B,CAAC;AACD,SAAO;AACT;AAGA,IAAI,2BAA2B,CAAC;AAAA,EAC9B,WAAW;AAAA,EACX;AACF,MAAM;AACJ,QAAM,MAAM;AAAA,IACV,CAAC,eAAe,UAAU,cAAc,YAAY,cAAc,OAAO;AAAA,IACzE;AAAA,EACF;AACA,QAAM,EAAE,aAAa,YAAY,YAAY,UAAU,QAAQ,GAAG,KAAK,IAAI;AAC3E,QAAM,eAAe;AAAA,IACnB,OAAO;AAAA,IACP,QAAQ,SAAS,SAAS,MAAM,IAAI;AAAA,IACpC;AAAA,IACA,YAAY,aAAa,KAAK,MAAM,UAAU,IAAI;AAAA,IAClD,YAAY,aAAa,KAAK,MAAM,UAAU,IAAI;AAAA,IAClD,GAAG;AAAA,EACL;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAIA,IAAI,YAAY;AAChB,IAAI,uBAAuB,MAAM;AAC/B,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,QAAM,EAAE,aAAa,IAAI,yBAAyB;AAAA,IAChD,UAAU;AAAA,EACZ,CAAC;AACD,QAAM,EAAE,cAAc,OAAO,WAAW,SAAS,MAAM,IAAI,oBAAoB;AAAA,IAC7E,GAAG;AAAA,EACL,CAAC;AACD,QAAM,UAAU,2BAA2B;AAC3C,QAAM,UAAU,2BAA2B;AAC3C,QAAM,EAAE,MAAM,IAAI,aAAa;AAAA,IAC7B,MAAM,gBAAgB,CAAC;AAAA,IACvB;AAAA,IACA;AAAA,IACA,kBAAkB;AAAA,IAClB,UAAU,CAAC,QAAQ,IAAI;AAAA,IACvB,UAAU;AAAA,EACZ,CAAC;AACD,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,0BAAK,WAAW,EAAE,WAAW,gBAAgB,UAAU;AAAA,QAC5D,0BAAK,OAAO,EAAE,WAAW,+CAA+C,UAAU;AAAA,UAChF,0BAAK,OAAO,EAAE,UAAU;AAAA,YACtB,oBAAAC,KAAK,SAAS,EAAE,UAAU,EAAE,qBAAqB,EAAE,CAAC;AAAA,YACpD,oBAAAA,KAAK,MAAM,EAAE,WAAW,qBAAqB,MAAM,SAAS,UAAU,EAAE,uBAAuB,EAAE,CAAC;AAAA,MACpH,EAAE,CAAC;AAAA,UACa,oBAAAA,KAAK,QAAQ,EAAE,SAAS,aAAa,MAAM,SAAS,SAAS,MAAM,cAA0B,oBAAAA,KAAK,MAAM,EAAE,IAAI,UAAU,UAAU,EAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;AAAA,IAC5K,EAAE,CAAC;AAAA,QACa,oBAAAA;AAAA,MACd;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,QACA,UAAU;AAAA,QACV;AAAA,QACA,WAAW;AAAA,QACX;AAAA,QACA,YAAY;AAAA,QACZ,YAAY,CAAC,QAAQ,IAAI;AAAA,QACzB,QAAQ;AAAA,MACV;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;AAIA,IAAI,kBAAkB,MAAM;AAC1B,QAAM,EAAE,WAAW,IAAI,aAAa;AACpC,aAAuB,oBAAAC;AAAA,IACrB;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP,QAAQ,WAAW,yBAAyB;AAAA,QAC5C,OAAO,WAAW,wBAAwB;AAAA,MAC5C;AAAA,MACA,cAA0B,oBAAAA,KAAK,sBAAsB,CAAC,CAAC;AAAA,IACzD;AAAA,EACF;AACF;", "names": ["import_jsx_runtime", "import_jsx_runtime", "jsx2", "jsx3", "jsx4"]}