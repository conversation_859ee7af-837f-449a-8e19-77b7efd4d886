import {
  useCustomerGroupTableColumns
} from "./chunk-EHZUZSWH.js";
import {
  useCustomerGroupTableQuery
} from "./chunk-I2ZOQM4X.js";
import {
  useOrderTableColumns
} from "./chunk-4FV466FW.js";
import "./chunk-QF476XOZ.js";
import {
  useOrderTableQuery
} from "./chunk-MKZD3R7Z.js";
import "./chunk-ZJX5R5NM.js";
import "./chunk-5ZQBU3TD.js";
import "./chunk-5AXVXNEZ.js";
import "./chunk-C43B7AQX.js";
import "./chunk-UDMOPZAP.js";
import "./chunk-OVCKROM5.js";
import "./chunk-EGRHWZRV.js";
import "./chunk-S5JEKJNE.js";
import {
  _DataTable,
  useDataTable
} from "./chunk-VCX3BVQR.js";
import "./chunk-VCBFQV64.js";
import "./chunk-QLJZR2JY.js";
import {
  NoRecords
} from "./chunk-X3TOWPPJ.js";
import {
  TwoColumnPage
} from "./chunk-3LNIL4XX.js";
import "./chunk-H3DTEG3J.js";
import "./chunk-32T72GVU.js";
import {
  useCustomerGroupTableFilters
} from "./chunk-XNFM7P3M.js";
import {
  useOrderTableFilters
} from "./chunk-2TO4KOWC.js";
import "./chunk-MX43XOWY.js";
import "./chunk-QX6SXRUW.js";
import "./chunk-7ANVLPZR.js";
import "./chunk-2E2FUO6N.js";
import {
  useExtension
} from "./chunk-FTD3ZWHZ.js";
import {
  SingleColumnPageSkeleton
} from "./chunk-YXT43UJF.js";
import {
  ActionMenu
} from "./chunk-CFRQOB2M.js";
import "./chunk-66DVUN72.js";
import "./chunk-DL4QDYPT.js";
import "./chunk-NV2N3EWM.js";
import "./chunk-BF7OBKIN.js";
import "./chunk-7UAYECTW.js";
import "./chunk-Y3NYV3NU.js";
import {
  t
} from "./chunk-MPXR7HT5.js";
import "./chunk-WISME5HP.js";
import "./chunk-WAUSOHFQ.js";
import "./chunk-3ORK3QZ4.js";
import "./chunk-3A5TVVNI.js";
import "./chunk-EZ62MM7J.js";
import "./chunk-3R6LYIXM.js";
import "./chunk-2P2O4TMQ.js";
import "./chunk-KI3HEITX.js";
import "./chunk-2VUCAO7L.js";
import "./chunk-PTIULTKD.js";
import "./chunk-KISXY2LD.js";
import "./chunk-L7C3XPHJ.js";
import "./chunk-T7MG2EIJ.js";
import "./chunk-MER4VYPR.js";
import {
  useBatchCustomerCustomerGroups,
  useCustomer,
  useCustomerGroups,
  useDeleteCustomer,
  useDeleteCustomerAddress,
  useRemoveCustomersFromGroup
} from "./chunk-2AWKOUCD.js";
import "./chunk-YL3TYBFR.js";
import "./chunk-TZWW72YW.js";
import "./chunk-MSQ25CWB.js";
import "./chunk-ONYSAQ5Z.js";
import {
  useOrders
} from "./chunk-NIH7SAXN.js";
import "./chunk-UEJFWAFE.js";
import "./chunk-GR55MFKT.js";
import "./chunk-AAFHKNJG.js";
import "./chunk-7XDBFDTZ.js";
import "./chunk-3JKGO5XL.js";
import "./chunk-OISUTS7G.js";
import {
  productsQueryKeys
} from "./chunk-AJYMIHLQ.js";
import {
  queryClient
} from "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import {
  sdk
} from "./chunk-JFCIYOH4.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-5GF3RGIE.js";
import {
  Link,
  useLoaderData,
  useNavigate,
  useParams
} from "./chunk-T7YBVUWZ.js";
import {
  ArrowPath,
  Button,
  Checkbox,
  Container,
  Heading,
  PencilSquare,
  StatusBadge,
  Text,
  Trash,
  clx,
  createColumnHelper,
  toast,
  usePrompt
} from "./chunk-YP2LLXWB.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-ZSQ362B4.js";
import {
  keepPreviousData
} from "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/customer-detail-VL22X5MT.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime4 = __toESM(require_jsx_runtime(), 1);
var import_react = __toESM(require_react(), 1);
var import_jsx_runtime5 = __toESM(require_jsx_runtime(), 1);
var import_react2 = __toESM(require_react(), 1);
var import_jsx_runtime6 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime7 = __toESM(require_jsx_runtime(), 1);
var CustomerDetailBreadcrumb = (props) => {
  const { id } = props.params || {};
  const { customer } = useCustomer(id, void 0, {
    initialData: props.data,
    enabled: Boolean(id)
  });
  if (!customer) {
    return null;
  }
  const name = [customer.first_name, customer.last_name].filter(Boolean).join(" ");
  const display = name || customer.email;
  return (0, import_jsx_runtime.jsx)("span", { children: display });
};
var Listicle = ({
  labelKey,
  descriptionKey,
  children
}) => {
  return (0, import_jsx_runtime2.jsx)("div", { className: "flex flex-col gap-2 px-2 pb-2", children: (0, import_jsx_runtime2.jsx)("div", { className: "shadow-elevation-card-rest bg-ui-bg-component transition-fg hover:bg-ui-bg-component-hover active:bg-ui-bg-component-pressed group-focus-visible:shadow-borders-interactive-with-active rounded-md px-4 py-2", children: (0, import_jsx_runtime2.jsxs)("div", { className: "flex items-center gap-4", children: [
    (0, import_jsx_runtime2.jsxs)("div", { className: "flex flex-1 flex-col", children: [
      (0, import_jsx_runtime2.jsx)(Text, { size: "small", leading: "compact", weight: "plus", children: labelKey }),
      (0, import_jsx_runtime2.jsx)(Text, { size: "small", leading: "compact", className: "text-ui-fg-subtle", children: descriptionKey })
    ] }),
    (0, import_jsx_runtime2.jsx)("div", { className: "flex size-7 items-center justify-center", children })
  ] }) }) });
};
var CustomerAddressSection = ({
  customer
}) => {
  const { t: t2 } = useTranslation();
  const prompt = usePrompt();
  const navigate = useNavigate();
  const { mutateAsync: deleteAddress } = useDeleteCustomerAddress(customer.id);
  const addresses = customer.addresses ?? [];
  const handleDelete = async (address) => {
    const confirm = await prompt({
      title: t2("general.areYouSure"),
      description: t2("general.areYouSureDescription", {
        entity: t2("fields.address"),
        title: address.address_name ?? "n/a"
      }),
      verificationInstruction: t2("general.typeToConfirm"),
      verificationText: address.address_name ?? "address",
      confirmText: t2("actions.delete"),
      cancelText: t2("actions.cancel")
    });
    if (!confirm) {
      return;
    }
    await deleteAddress(address.id, {
      onSuccess: () => {
        toast.success(
          t2("general.success", { name: address.address_name ?? "address" })
        );
        navigate(`/customers/${customer.id}`, { replace: true });
      },
      onError: (e) => {
        toast.error(e.message);
      }
    });
  };
  return (0, import_jsx_runtime3.jsxs)(Container, { className: "p-0", children: [
    (0, import_jsx_runtime3.jsxs)("div", { className: "flex items-center justify-between px-6 py-4", children: [
      (0, import_jsx_runtime3.jsx)(Heading, { level: "h2", children: t2("addresses.title") }),
      (0, import_jsx_runtime3.jsx)(Link, { to: `create-address`, className: "text-ui-fg-muted text-xs", children: "Add" })
    ] }),
    addresses.length === 0 && (0, import_jsx_runtime3.jsx)(
      NoRecords,
      {
        className: clx({
          "flex h-full flex-col overflow-hidden border-t p-6": true
        }),
        icon: null,
        title: t2("general.noRecordsTitle"),
        message: t2("general.noRecordsMessage")
      }
    ),
    addresses.map((address) => {
      return (0, import_jsx_runtime3.jsx)(
        Listicle,
        {
          labelKey: address.address_name ?? "n/a",
          descriptionKey: [address.address_1, address.address_2].join(" "),
          children: (0, import_jsx_runtime3.jsx)(
            ActionMenu,
            {
              groups: [
                {
                  actions: [
                    {
                      icon: (0, import_jsx_runtime3.jsx)(Trash, {}),
                      label: t2("actions.delete"),
                      onClick: async () => {
                        await handleDelete(address);
                      }
                    }
                  ]
                }
              ]
            }
          )
        },
        address.id
      );
    })
  ] });
};
var CustomerGeneralSection = ({
  customer
}) => {
  const { t: t2 } = useTranslation();
  const prompt = usePrompt();
  const navigate = useNavigate();
  const { mutateAsync } = useDeleteCustomer(customer.id);
  const name = [customer.first_name, customer.last_name].filter(Boolean).join(" ");
  const statusColor = customer.has_account ? "green" : "orange";
  const statusText = customer.has_account ? t2("customers.fields.registered") : t2("customers.fields.guest");
  const handleDelete = async () => {
    const res = await prompt({
      title: t2("customers.delete.title"),
      description: t2("customers.delete.description", {
        email: customer.email
      }),
      verificationInstruction: t2("general.typeToConfirm"),
      verificationText: customer.email,
      confirmText: t2("actions.delete"),
      cancelText: t2("actions.cancel")
    });
    if (!res) {
      return;
    }
    await mutateAsync(void 0, {
      onSuccess: () => {
        toast.success(
          t2("customers.delete.successToast", {
            email: customer.email
          })
        );
        navigate("/customers", { replace: true });
      },
      onError: (error) => {
        toast.error(error.message);
      }
    });
  };
  return (0, import_jsx_runtime4.jsxs)(Container, { className: "divide-y p-0", children: [
    (0, import_jsx_runtime4.jsxs)("div", { className: "flex items-center justify-between px-6 py-4", children: [
      (0, import_jsx_runtime4.jsx)(Heading, { children: customer.email }),
      (0, import_jsx_runtime4.jsxs)("div", { className: "flex items-center gap-x-2", children: [
        (0, import_jsx_runtime4.jsx)(StatusBadge, { color: statusColor, children: statusText }),
        (0, import_jsx_runtime4.jsx)(
          ActionMenu,
          {
            groups: [
              {
                actions: [
                  {
                    label: t2("actions.edit"),
                    icon: (0, import_jsx_runtime4.jsx)(PencilSquare, {}),
                    to: "edit"
                  }
                ]
              },
              {
                actions: [
                  {
                    label: t2("actions.delete"),
                    icon: (0, import_jsx_runtime4.jsx)(Trash, {}),
                    onClick: handleDelete
                  }
                ]
              }
            ]
          }
        )
      ] })
    ] }),
    (0, import_jsx_runtime4.jsxs)("div", { className: "text-ui-fg-subtle grid grid-cols-2 items-center px-6 py-4", children: [
      (0, import_jsx_runtime4.jsx)(Text, { size: "small", leading: "compact", weight: "plus", children: t2("fields.name") }),
      (0, import_jsx_runtime4.jsx)(Text, { size: "small", leading: "compact", children: name || "-" })
    ] }),
    (0, import_jsx_runtime4.jsxs)("div", { className: "text-ui-fg-subtle grid grid-cols-2 items-center px-6 py-4", children: [
      (0, import_jsx_runtime4.jsx)(Text, { size: "small", leading: "compact", weight: "plus", children: t2("fields.company") }),
      (0, import_jsx_runtime4.jsx)(Text, { size: "small", leading: "compact", children: customer.company_name || "-" })
    ] }),
    (0, import_jsx_runtime4.jsxs)("div", { className: "text-ui-fg-subtle grid grid-cols-2 items-center px-6 py-4", children: [
      (0, import_jsx_runtime4.jsx)(Text, { size: "small", leading: "compact", weight: "plus", children: t2("fields.phone") }),
      (0, import_jsx_runtime4.jsx)(Text, { size: "small", leading: "compact", children: customer.phone || "-" })
    ] })
  ] });
};
var PAGE_SIZE = 10;
var PREFIX = "cusgr";
var CustomerGroupSection = ({
  customer
}) => {
  const prompt = usePrompt();
  const [rowSelection, setRowSelection] = (0, import_react.useState)({});
  const { raw, searchParams } = useCustomerGroupTableQuery({
    pageSize: PAGE_SIZE,
    prefix: PREFIX
  });
  const { customer_groups, count, isLoading, isError, error } = useCustomerGroups(
    {
      ...searchParams,
      fields: "+customers.id",
      customers: { id: customer.id }
    },
    {
      placeholderData: keepPreviousData
    }
  );
  const { mutateAsync: batchCustomerCustomerGroups } = useBatchCustomerCustomerGroups(customer.id);
  const filters = useCustomerGroupTableFilters();
  const columns = useColumns(customer.id);
  const { table } = useDataTable({
    data: customer_groups ?? [],
    columns,
    count,
    getRowId: (row) => row.id,
    enablePagination: true,
    enableRowSelection: true,
    pageSize: PAGE_SIZE,
    prefix: PREFIX,
    rowSelection: {
      state: rowSelection,
      updater: setRowSelection
    }
  });
  const handleRemove = async () => {
    const customerGroupIds = Object.keys(rowSelection);
    const res = await prompt({
      title: t("general.areYouSure"),
      description: t("customers.groups.removeMany", {
        groups: customer_groups == null ? void 0 : customer_groups.filter((g) => customerGroupIds.includes(g.id)).map((g) => g.name).join(",")
      }),
      confirmText: t("actions.remove"),
      cancelText: t("actions.cancel")
    });
    if (!res) {
      return;
    }
    await batchCustomerCustomerGroups(
      { remove: customerGroupIds },
      {
        onSuccess: () => {
          toast.success(
            t("customers.groups.removed.success", {
              groups: customer_groups.filter((cg) => customerGroupIds.includes(cg.id)).map((cg) => cg == null ? void 0 : cg.name)
            })
          );
        },
        onError: (error2) => {
          toast.error(error2.message);
        }
      }
    );
  };
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime5.jsxs)(Container, { className: "divide-y p-0", children: [
    (0, import_jsx_runtime5.jsxs)("div", { className: "flex items-center justify-between px-6 py-4", children: [
      (0, import_jsx_runtime5.jsx)(Heading, { level: "h2", children: t("customerGroups.domain") }),
      (0, import_jsx_runtime5.jsx)(Link, { to: `/customers/${customer.id}/add-customer-groups`, children: (0, import_jsx_runtime5.jsx)(Button, { variant: "secondary", size: "small", children: t("general.add") }) })
    ] }),
    (0, import_jsx_runtime5.jsx)(
      _DataTable,
      {
        table,
        columns,
        pageSize: PAGE_SIZE,
        isLoading,
        count,
        prefix: PREFIX,
        navigateTo: (row) => `/customer-groups/${row.id}`,
        filters,
        search: true,
        pagination: true,
        orderBy: [
          { key: "name", label: t("fields.name") },
          { key: "created_at", label: t("fields.createdAt") },
          { key: "updated_at", label: t("fields.updatedAt") }
        ],
        commands: [
          {
            action: handleRemove,
            label: t("actions.remove"),
            shortcut: "r"
          }
        ],
        queryObject: raw,
        noRecords: {
          message: t("customers.groups.list.noRecordsMessage")
        }
      }
    )
  ] });
};
var CustomerGroupRowActions = ({
  group,
  customerId
}) => {
  const prompt = usePrompt();
  const { t: t2 } = useTranslation();
  const { mutateAsync } = useRemoveCustomersFromGroup(group.id);
  const onRemove = async () => {
    const res = await prompt({
      title: t2("general.areYouSure"),
      description: t2("customers.groups.remove", {
        name: group.name
      }),
      confirmText: t2("actions.remove"),
      cancelText: t2("actions.cancel")
    });
    if (!res) {
      return;
    }
    await mutateAsync([customerId], {
      onError: (error) => {
        toast.error(error.message);
      }
    });
  };
  return (0, import_jsx_runtime5.jsx)(
    ActionMenu,
    {
      groups: [
        {
          actions: [
            {
              label: t2("actions.edit"),
              icon: (0, import_jsx_runtime5.jsx)(PencilSquare, {}),
              to: `/customer-groups/${group.id}/edit`
            },
            {
              label: t2("actions.remove"),
              onClick: onRemove,
              icon: (0, import_jsx_runtime5.jsx)(Trash, {})
            }
          ]
        }
      ]
    }
  );
};
var columnHelper = createColumnHelper();
var useColumns = (customerId) => {
  const columns = useCustomerGroupTableColumns();
  return (0, import_react.useMemo)(
    () => [
      columnHelper.display({
        id: "select",
        header: ({ table }) => {
          return (0, import_jsx_runtime5.jsx)(
            Checkbox,
            {
              checked: table.getIsSomePageRowsSelected() ? "indeterminate" : table.getIsAllPageRowsSelected(),
              onCheckedChange: (value) => table.toggleAllPageRowsSelected(!!value)
            }
          );
        },
        cell: ({ row }) => {
          return (0, import_jsx_runtime5.jsx)(
            Checkbox,
            {
              checked: row.getIsSelected(),
              onCheckedChange: (value) => row.toggleSelected(!!value),
              onClick: (e) => {
                e.stopPropagation();
              }
            }
          );
        }
      }),
      ...columns,
      columnHelper.display({
        id: "actions",
        cell: ({ row }) => (0, import_jsx_runtime5.jsx)(
          CustomerGroupRowActions,
          {
            group: row.original,
            customerId
          }
        )
      })
    ],
    [columns, customerId]
  );
};
var PREFIX2 = "cusord";
var PAGE_SIZE2 = 10;
var DEFAULT_RELATIONS = "*customer,*items,*sales_channel";
var DEFAULT_FIELDS = "id,status,display_id,created_at,email,fulfillment_status,payment_status,total,currency_code";
var CustomerOrderSection = ({
  customer
}) => {
  const { t: t2 } = useTranslation();
  const { searchParams, raw } = useOrderTableQuery({
    pageSize: PAGE_SIZE2,
    prefix: PREFIX2
  });
  const { orders, count, isLoading, isError, error } = useOrders(
    {
      customer_id: customer.id,
      fields: DEFAULT_FIELDS + "," + DEFAULT_RELATIONS,
      ...searchParams
    },
    {
      placeholderData: keepPreviousData
    }
  );
  const columns = useColumns2();
  const filters = useOrderTableFilters();
  const { table } = useDataTable({
    data: orders ?? [],
    columns,
    enablePagination: true,
    count,
    pageSize: PAGE_SIZE2,
    prefix: PREFIX2
  });
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime6.jsxs)(Container, { className: "divide-y p-0", children: [
    (0, import_jsx_runtime6.jsx)("div", { className: "flex items-center justify-between px-6 py-4", children: (0, import_jsx_runtime6.jsx)(Heading, { level: "h2", children: t2("orders.domain") }) }),
    (0, import_jsx_runtime6.jsx)(
      _DataTable,
      {
        columns,
        table,
        pagination: true,
        navigateTo: (row) => `/orders/${row.original.id}`,
        filters,
        count,
        isLoading,
        pageSize: PAGE_SIZE2,
        orderBy: [
          { key: "display_id", label: t2("orders.fields.displayId") },
          { key: "created_at", label: t2("fields.createdAt") },
          { key: "updated_at", label: t2("fields.updatedAt") }
        ],
        search: true,
        queryObject: raw,
        prefix: PREFIX2
      }
    )
  ] });
};
var CustomerOrderActions = ({ order }) => {
  const { t: t2 } = useTranslation();
  return (0, import_jsx_runtime6.jsx)(
    ActionMenu,
    {
      groups: [
        {
          actions: [
            {
              label: t2("transferOwnership.label"),
              to: `${order.id}/transfer`,
              icon: (0, import_jsx_runtime6.jsx)(ArrowPath, {})
            }
          ]
        }
      ]
    }
  );
};
var columnHelper2 = createColumnHelper();
var useColumns2 = () => {
  const base = useOrderTableColumns({ exclude: ["customer"] });
  return (0, import_react2.useMemo)(
    () => [
      ...base,
      columnHelper2.display({
        id: "actions",
        cell: ({ row }) => (0, import_jsx_runtime6.jsx)(CustomerOrderActions, { order: row.original })
      })
    ],
    [base]
  );
};
var CustomerDetail = () => {
  const { id } = useParams();
  const initialData = useLoaderData();
  const { customer, isLoading, isError, error } = useCustomer(
    id,
    { fields: "+*addresses" },
    { initialData }
  );
  const { getWidgets } = useExtension();
  if (isLoading || !customer) {
    return (0, import_jsx_runtime7.jsx)(SingleColumnPageSkeleton, { sections: 2, showJSON: true, showMetadata: true });
  }
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime7.jsxs)(
    TwoColumnPage,
    {
      widgets: {
        before: getWidgets("customer.details.before"),
        after: getWidgets("customer.details.after"),
        sideAfter: getWidgets("customer.details.side.after"),
        sideBefore: getWidgets("customer.details.side.before")
      },
      data: customer,
      hasOutlet: true,
      showJSON: true,
      showMetadata: true,
      children: [
        (0, import_jsx_runtime7.jsxs)(TwoColumnPage.Main, { children: [
          (0, import_jsx_runtime7.jsx)(CustomerGeneralSection, { customer }),
          (0, import_jsx_runtime7.jsx)(CustomerOrderSection, { customer }),
          (0, import_jsx_runtime7.jsx)(CustomerGroupSection, { customer })
        ] }),
        (0, import_jsx_runtime7.jsx)(TwoColumnPage.Sidebar, { children: (0, import_jsx_runtime7.jsx)(CustomerAddressSection, { customer }) })
      ]
    }
  );
};
var customerDetailQuery = (id) => ({
  queryKey: productsQueryKeys.detail(id),
  queryFn: async () => sdk.admin.customer.retrieve(id, {
    fields: "+*addresses"
  })
});
var customerLoader = async ({ params }) => {
  const id = params.id;
  const query = customerDetailQuery(id);
  return queryClient.ensureQueryData(query);
};
export {
  CustomerDetailBreadcrumb as Breadcrumb,
  CustomerDetail as Component,
  customerLoader as loader
};
//# sourceMappingURL=customer-detail-VL22X5MT-SEUZO55Q.js.map
