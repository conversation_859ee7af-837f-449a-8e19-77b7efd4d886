import "./chunk-EGRHWZRV.js";
import {
  KeyboundForm
} from "./chunk-DP54EP6X.js";
import {
  RouteDrawer,
  useRouteModal
} from "./chunk-MVVOBQIC.js";
import {
  t
} from "./chunk-WHQIBI5S.js";
import {
  objectType,
  stringType
} from "./chunk-4XXECALA.js";
import {
  languages
} from "./chunk-BF7OBKIN.js";
import "./chunk-Y3NYV3NU.js";
import {
  Form,
  useForm
} from "./chunk-XXJU43CK.js";
import {
  useMe,
  useUpdateUser
} from "./chunk-T7MG2EIJ.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import "./chunk-JFCIYOH4.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-5GF3RGIE.js";
import "./chunk-T7YBVUWZ.js";
import {
  Button,
  Heading,
  Input,
  Select,
  toast
} from "./chunk-YP2LLXWB.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-ZSQ362B4.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/profile-edit-XO4IRFZY.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var EditProfileSchema = objectType({
  first_name: stringType().optional(),
  last_name: stringType().optional(),
  language: stringType()
  // usage_insights: zod.boolean(),
});
var EditProfileForm = ({ user }) => {
  const { t: t2, i18n } = useTranslation();
  const { handleSuccess } = useRouteModal();
  const form = useForm({
    defaultValues: {
      first_name: user.first_name ?? "",
      last_name: user.last_name ?? "",
      language: i18n.language
      // usage_insights: usageInsights,
    },
    resolver: t(EditProfileSchema)
  });
  const changeLanguage = async (code) => {
    await i18n.changeLanguage(code);
  };
  const sortedLanguages = languages.sort(
    (a, b) => a.display_name.localeCompare(b.display_name)
  );
  const { mutateAsync, isPending } = useUpdateUser(user.id);
  const handleSubmit = form.handleSubmit(async (values) => {
    await mutateAsync(
      {
        first_name: values.first_name,
        last_name: values.last_name
      },
      {
        onError: (error) => {
          toast.error(error.message);
          return;
        }
      }
    );
    await changeLanguage(values.language);
    toast.success(t2("profile.toast.edit"));
    handleSuccess();
  });
  return (0, import_jsx_runtime.jsx)(RouteDrawer.Form, { form, children: (0, import_jsx_runtime.jsxs)(KeyboundForm, { onSubmit: handleSubmit, className: "flex flex-1 flex-col", children: [
    (0, import_jsx_runtime.jsx)(RouteDrawer.Body, { children: (0, import_jsx_runtime.jsxs)("div", { className: "flex flex-col gap-y-8", children: [
      (0, import_jsx_runtime.jsxs)("div", { className: "grid grid-cols-2 gap-4", children: [
        (0, import_jsx_runtime.jsx)(
          Form.Field,
          {
            control: form.control,
            name: "first_name",
            render: ({ field }) => (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
              (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("fields.firstName") }),
              (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(Input, { ...field }) }),
              (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
            ] })
          }
        ),
        (0, import_jsx_runtime.jsx)(
          Form.Field,
          {
            control: form.control,
            name: "last_name",
            render: ({ field }) => (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
              (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("fields.lastName") }),
              (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(Input, { ...field }) }),
              (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
            ] })
          }
        )
      ] }),
      (0, import_jsx_runtime.jsx)(
        Form.Field,
        {
          control: form.control,
          name: "language",
          render: ({ field: { ref, ...field } }) => {
            var _a;
            return (0, import_jsx_runtime.jsxs)(Form.Item, { className: "gap-y-4", children: [
              (0, import_jsx_runtime.jsxs)("div", { children: [
                (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("profile.fields.languageLabel") }),
                (0, import_jsx_runtime.jsx)(Form.Hint, { children: t2("profile.edit.languageHint") })
              ] }),
              (0, import_jsx_runtime.jsxs)("div", { children: [
                (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsxs)(Select, { ...field, onValueChange: field.onChange, children: [
                  (0, import_jsx_runtime.jsx)(Select.Trigger, { ref, className: "py-1 text-[13px]", children: (0, import_jsx_runtime.jsx)(
                    Select.Value,
                    {
                      placeholder: t2("profile.edit.languagePlaceholder"),
                      children: (_a = sortedLanguages.find(
                        (language) => language.code === field.value
                      )) == null ? void 0 : _a.display_name
                    }
                  ) }),
                  (0, import_jsx_runtime.jsx)(Select.Content, { children: languages.map((language) => (0, import_jsx_runtime.jsx)(
                    Select.Item,
                    {
                      value: language.code,
                      children: language.display_name
                    },
                    language.code
                  )) })
                ] }) }),
                (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
              ] })
            ] });
          }
        }
      )
    ] }) }),
    (0, import_jsx_runtime.jsx)(RouteDrawer.Footer, { children: (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center gap-x-2", children: [
      (0, import_jsx_runtime.jsx)(RouteDrawer.Close, { asChild: true, children: (0, import_jsx_runtime.jsx)(Button, { size: "small", variant: "secondary", children: t2("actions.cancel") }) }),
      (0, import_jsx_runtime.jsx)(Button, { size: "small", type: "submit", isLoading: isPending, children: t2("actions.save") })
    ] }) })
  ] }) });
};
var ProfileEdit = () => {
  const { user, isPending: isLoading, isError, error } = useMe();
  const { t: t2 } = useTranslation();
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime2.jsxs)(RouteDrawer, { children: [
    (0, import_jsx_runtime2.jsx)(RouteDrawer.Header, { className: "capitalize", children: (0, import_jsx_runtime2.jsx)(RouteDrawer.Title, { asChild: true, children: (0, import_jsx_runtime2.jsx)(Heading, { children: t2("profile.edit.header") }) }) }),
    !isLoading && user && (0, import_jsx_runtime2.jsx)(EditProfileForm, { user })
  ] });
};
export {
  ProfileEdit as Component
};
//# sourceMappingURL=profile-edit-XO4IRFZY-J42ZEKW7.js.map
