{"version": 3, "sources": ["../../@medusajs/dashboard/dist/order-request-transfer-BESJQ4LK.mjs"], "sourcesContent": ["import {\n  DEFAULT_FIELDS\n} from \"./chunk-7I5DQGWY.mjs\";\nimport {\n  useComboboxData\n} from \"./chunk-YIZSVS2R.mjs\";\nimport {\n  Combobox\n} from \"./chunk-GZBFGV7Y.mjs\";\nimport \"./chunk-IUCDCPJU.mjs\";\nimport {\n  KeyboundForm\n} from \"./chunk-6HTZNHPT.mjs\";\nimport {\n  RouteDrawer,\n  useRouteModal\n} from \"./chunk-4TC5YS65.mjs\";\nimport {\n  Form\n} from \"./chunk-OBQI23QM.mjs\";\nimport \"./chunk-Z5UDPQIH.mjs\";\nimport \"./chunk-KOSCMAIC.mjs\";\nimport \"./chunk-X6DSNTTX.mjs\";\nimport \"./chunk-I6E6CALJ.mjs\";\nimport \"./chunk-B4GODIOW.mjs\";\nimport \"./chunk-F6IJV2I2.mjs\";\nimport \"./chunk-QTCZFYFH.mjs\";\nimport \"./chunk-ENV6YVOM.mjs\";\nimport \"./chunk-PIR2H25N.mjs\";\nimport \"./chunk-RLY2SL5E.mjs\";\nimport \"./chunk-C5LYZZZ5.mjs\";\nimport \"./chunk-2ZKVRTBW.mjs\";\nimport \"./chunk-FO3VP56P.mjs\";\nimport \"./chunk-YS65UGPC.mjs\";\nimport \"./chunk-F6PXCY3N.mjs\";\nimport \"./chunk-3OHH43G6.mjs\";\nimport \"./chunk-G2H6MAK7.mjs\";\nimport \"./chunk-GRT22PE5.mjs\";\nimport \"./chunk-32IQRUVY.mjs\";\nimport {\n  useOrder,\n  useRequestTransferOrder\n} from \"./chunk-FNYASI54.mjs\";\nimport \"./chunk-FVC7M755.mjs\";\nimport \"./chunk-ZJ3OFMHB.mjs\";\nimport \"./chunk-PNU5HPGY.mjs\";\nimport \"./chunk-V2LANK5S.mjs\";\nimport \"./chunk-QZ6PT4QV.mjs\";\nimport \"./chunk-QL4XKIVL.mjs\";\nimport \"./chunk-6I62UDJA.mjs\";\nimport \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport {\n  sdk\n} from \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-RPUOO7AV.mjs\";\n\n// src/routes/orders/order-request-transfer/order-request-transfer.tsx\nimport { Heading } from \"@medusajs/ui\";\nimport { useTranslation as useTranslation2 } from \"react-i18next\";\nimport { useParams } from \"react-router-dom\";\n\n// src/routes/orders/order-request-transfer/components/create-order-transfer-form/create-order-transfer-form.tsx\nimport * as zod from \"zod\";\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { Button, Input, toast } from \"@medusajs/ui\";\nimport { useForm } from \"react-hook-form\";\nimport { useTranslation } from \"react-i18next\";\n\n// src/routes/orders/order-request-transfer/components/create-order-transfer-form/transfer-header.tsx\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nfunction TransferHeader() {\n  return /* @__PURE__ */ jsxs(\n    \"svg\",\n    {\n      width: \"200\",\n      height: \"128\",\n      viewBox: \"0 0 200 128\",\n      fill: \"none\",\n      xmlns: \"http://www.w3.org/2000/svg\",\n      children: [\n        /* @__PURE__ */ jsx(\n          \"rect\",\n          {\n            x: \"0.00428286\",\n            y: \"-0.742904\",\n            width: \"33.5\",\n            height: \"65.5\",\n            rx: \"6.75\",\n            transform: \"matrix(0.865865 0.500278 -0.871576 0.490261 149.756 60.938)\",\n            className: \"stroke-ui-fg-subtle fill-ui-bg-base\",\n            strokeWidth: \"1.5\"\n          }\n        ),\n        /* @__PURE__ */ jsx(\n          \"rect\",\n          {\n            x: \"0.00428286\",\n            y: \"-0.742904\",\n            width: \"33.5\",\n            height: \"65.5\",\n            rx: \"6.75\",\n            transform: \"matrix(0.865865 0.500278 -0.871576 0.490261 149.756 57.9383)\",\n            className: \"stroke-ui-fg-subtle fill-ui-bg-base\",\n            strokeWidth: \"1.5\"\n          }\n        ),\n        /* @__PURE__ */ jsxs(\"g\", { clipPath: \"url(#clip0_20787_38934)\", children: [\n          /* @__PURE__ */ jsx(\n            \"path\",\n            {\n              d: \"M140.579 79.6421L139.126 80.4592\",\n              className: \"stroke-ui-fg-subtle\",\n              strokeWidth: \"1.5\",\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\"\n            }\n          ),\n          /* @__PURE__ */ jsx(\n            \"path\",\n            {\n              opacity: \"0.88\",\n              d: \"M142.305 82.046L140.257 82.0342\",\n              className: \"stroke-ui-fg-subtle\",\n              strokeWidth: \"1.5\",\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\"\n            }\n          ),\n          /* @__PURE__ */ jsx(\n            \"path\",\n            {\n              opacity: \"0.75\",\n              d: \"M140.552 84.4297L139.108 83.5959\",\n              className: \"stroke-ui-fg-subtle\",\n              strokeWidth: \"1.5\",\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\"\n            }\n          ),\n          /* @__PURE__ */ jsx(\n            \"path\",\n            {\n              opacity: \"0.63\",\n              d: \"M136.347 85.3975L136.354 84.23\",\n              className: \"stroke-ui-fg-subtle\",\n              strokeWidth: \"1.5\",\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\"\n            }\n          ),\n          /* @__PURE__ */ jsx(\n            \"path\",\n            {\n              opacity: \"0.5\",\n              d: \"M132.154 84.3813L133.606 83.5642\",\n              className: \"stroke-ui-fg-subtle\",\n              strokeWidth: \"1.5\",\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\"\n            }\n          ),\n          /* @__PURE__ */ jsx(\n            \"path\",\n            {\n              opacity: \"0.38\",\n              d: \"M130.428 81.9775L132.476 81.9893\",\n              className: \"stroke-ui-fg-subtle\",\n              strokeWidth: \"1.5\",\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\"\n            }\n          ),\n          /* @__PURE__ */ jsx(\n            \"path\",\n            {\n              opacity: \"0.25\",\n              d: \"M132.181 79.5938L133.625 80.4275\",\n              className: \"stroke-ui-fg-subtle\",\n              strokeWidth: \"1.5\",\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\"\n            }\n          ),\n          /* @__PURE__ */ jsx(\n            \"path\",\n            {\n              opacity: \"0.13\",\n              d: \"M136.386 78.626L136.379 79.7935\",\n              className: \"stroke-ui-fg-subtle\",\n              strokeWidth: \"1.5\",\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\"\n            }\n          )\n        ] }),\n        /* @__PURE__ */ jsx(\n          \"rect\",\n          {\n            width: \"12\",\n            height: \"3\",\n            rx: \"1.5\",\n            transform: \"matrix(0.865865 0.500278 -0.871576 0.490261 156.447 64.7927)\",\n            className: \"fill-ui-fg-muted\"\n          }\n        ),\n        /* @__PURE__ */ jsx(\n          \"rect\",\n          {\n            x: \"0.00428286\",\n            y: \"-0.742904\",\n            width: \"33.5\",\n            height: \"65.5\",\n            rx: \"6.75\",\n            transform: \"matrix(0.865865 0.500278 -0.871576 0.490261 77.0232 18.9148)\",\n            className: \"stroke-ui-fg-subtle fill-ui-fg-muted\",\n            strokeWidth: \"1.5\"\n          }\n        ),\n        /* @__PURE__ */ jsx(\n          \"rect\",\n          {\n            x: \"0.00428286\",\n            y: \"-0.742904\",\n            width: \"33.5\",\n            height: \"65.5\",\n            rx: \"6.75\",\n            transform: \"matrix(0.865865 0.500278 -0.871576 0.490261 77.0232 15.9148)\",\n            className: \"stroke-ui-fg-subtle fill-ui-bg-base\",\n            strokeWidth: \"1.5\"\n          }\n        ),\n        /* @__PURE__ */ jsx(\n          \"rect\",\n          {\n            width: \"12\",\n            height: \"3\",\n            rx: \"1.5\",\n            transform: \"matrix(0.865865 0.500278 -0.871576 0.490261 83.7141 22.7693)\",\n            className: \"fill-ui-fg-muted\"\n          }\n        ),\n        /* @__PURE__ */ jsx(\n          \"rect\",\n          {\n            width: \"17\",\n            height: \"3\",\n            rx: \"1.5\",\n            transform: \"matrix(0.865865 0.500278 -0.871576 0.490261 57.5554 39.458)\",\n            className: \"fill-ui-fg-muted\"\n          }\n        ),\n        /* @__PURE__ */ jsx(\n          \"rect\",\n          {\n            width: \"12\",\n            height: \"3\",\n            rx: \"1.5\",\n            transform: \"matrix(0.865865 0.500278 -0.871576 0.490261 53.1975 41.9094)\",\n            className: \"fill-ui-fg-muted\"\n          }\n        ),\n        /* @__PURE__ */ jsx(\"g\", { clipPath: \"url(#clip1_20787_38934)\", children: /* @__PURE__ */ jsx(\n          \"path\",\n          {\n            d: \"M52.3603 36.4564C50.9277 35.6287 48.59 35.6152 47.148 36.4264C45.7059 37.2375 45.6983 38.5703 47.1308 39.398C48.5634 40.2257 50.9011 40.2392 52.3432 39.428C53.7852 38.6169 53.7929 37.2841 52.3603 36.4564ZM48.4382 38.6626C47.7221 38.2488 47.726 37.5822 48.4468 37.1768C49.1676 36.7713 50.3369 36.7781 51.0529 37.1918C51.769 37.6055 51.7652 38.2722 51.0444 38.6776C50.3236 39.083 49.1543 39.0763 48.4382 38.6626Z\",\n            className: \"fill-ui-fg-subtle\"\n          }\n        ) }),\n        /* @__PURE__ */ jsx(\n          \"rect\",\n          {\n            width: \"17\",\n            height: \"3\",\n            rx: \"1.5\",\n            transform: \"matrix(0.865865 0.500278 -0.871576 0.490261 69.7573 32.5945)\",\n            className: \"fill-ui-fg-muted\"\n          }\n        ),\n        /* @__PURE__ */ jsx(\n          \"rect\",\n          {\n            width: \"12\",\n            height: \"3\",\n            rx: \"1.5\",\n            transform: \"matrix(0.865865 0.500278 -0.871576 0.490261 65.3994 35.0459)\",\n            className: \"fill-ui-fg-muted\"\n          }\n        ),\n        /* @__PURE__ */ jsx(\"g\", { clipPath: \"url(#clip2_20787_38934)\", children: /* @__PURE__ */ jsx(\n          \"path\",\n          {\n            d: \"M64.5622 29.5929C63.1296 28.7652 60.7919 28.7517 59.3499 29.5628C57.9079 30.374 57.9002 31.7067 59.3327 32.5344C60.7653 33.3622 63.103 33.3756 64.5451 32.5645C65.9871 31.7534 65.9948 30.4206 64.5622 29.5929ZM63.8581 31.3974L60.8148 31.6267C60.6827 31.6368 60.5495 31.6135 60.4486 31.5632C60.4399 31.5587 60.4321 31.5547 60.4244 31.5502C60.3386 31.5006 60.2899 31.4337 60.2903 31.3639L60.2933 30.6203C60.2937 30.4754 60.5012 30.3587 60.7557 30.3602C61.0102 30.3616 61.2163 30.4802 61.2155 30.6258L61.2138 31.0671L63.7317 30.8771C63.9833 30.858 64.2168 30.9586 64.2512 31.1032C64.286 31.247 64.1101 31.379 63.8581 31.3978L63.8581 31.3974Z\",\n            className: \"fill-ui-fg-subtle\"\n          }\n        ) }),\n        /* @__PURE__ */ jsx(\"g\", { clipPath: \"url(#clip3_20787_38934)\", children: /* @__PURE__ */ jsx(\n          \"path\",\n          {\n            d: \"M93.106 54.3022L100.49 54.3448L100.514 50.135\",\n            className: \"stroke-ui-fg-subtle\",\n            strokeWidth: \"1.5\",\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\"\n          }\n        ) }),\n        /* @__PURE__ */ jsx(\"g\", { clipPath: \"url(#clip4_20787_38934)\", children: /* @__PURE__ */ jsx(\n          \"path\",\n          {\n            d: \"M103.496 60.3056L110.881 60.3482L110.905 56.1384\",\n            className: \"stroke-ui-fg-subtle\",\n            strokeWidth: \"1.5\",\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\"\n          }\n        ) }),\n        /* @__PURE__ */ jsx(\"g\", { clipPath: \"url(#clip5_20787_38934)\", children: /* @__PURE__ */ jsx(\n          \"path\",\n          {\n            d: \"M113.887 66.3088L121.271 66.3514L121.295 62.1416\",\n            className: \"stroke-ui-fg-subtle\",\n            strokeWidth: \"1.5\",\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\"\n          }\n        ) }),\n        /* @__PURE__ */ jsx(\"g\", { clipPath: \"url(#clip6_20787_38934)\", children: /* @__PURE__ */ jsx(\n          \"path\",\n          {\n            d: \"M86.1135 61.6911L78.7294 61.6486L78.7051 65.8583\",\n            className: \"stroke-ui-fg-subtle\",\n            strokeWidth: \"1.5\",\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\"\n          }\n        ) }),\n        /* @__PURE__ */ jsx(\"g\", { clipPath: \"url(#clip7_20787_38934)\", children: /* @__PURE__ */ jsx(\n          \"path\",\n          {\n            d: \"M96.5039 67.6945L89.1198 67.652L89.0955 71.8618\",\n            className: \"stroke-ui-fg-subtle\",\n            strokeWidth: \"1.5\",\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\"\n          }\n        ) }),\n        /* @__PURE__ */ jsx(\"g\", { clipPath: \"url(#clip8_20787_38934)\", children: /* @__PURE__ */ jsx(\n          \"path\",\n          {\n            d: \"M106.894 73.6977L99.5102 73.6551L99.4859 77.8649\",\n            className: \"stroke-ui-fg-subtle\",\n            strokeWidth: \"1.5\",\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\"\n          }\n        ) }),\n        /* @__PURE__ */ jsxs(\"defs\", { children: [\n          /* @__PURE__ */ jsx(\"clipPath\", { id: \"clip0_20787_38934\", children: /* @__PURE__ */ jsx(\n            \"rect\",\n            {\n              width: \"12\",\n              height: \"12\",\n              className: \"fill-ui-bg-base\",\n              transform: \"matrix(0.865865 0.500278 -0.871576 0.490261 136.401 76.0686)\"\n            }\n          ) }),\n          /* @__PURE__ */ jsx(\"clipPath\", { id: \"clip1_20787_38934\", children: /* @__PURE__ */ jsx(\n            \"rect\",\n            {\n              width: \"6\",\n              height: \"6\",\n              className: \"fill-ui-bg-base\",\n              transform: \"matrix(0.865865 0.500278 -0.871576 0.490261 49.7627 34.9556)\"\n            }\n          ) }),\n          /* @__PURE__ */ jsx(\"clipPath\", { id: \"clip2_20787_38934\", children: /* @__PURE__ */ jsx(\n            \"rect\",\n            {\n              width: \"6\",\n              height: \"6\",\n              className: \"fill-ui-bg-base\",\n              transform: \"matrix(0.865865 0.500278 -0.871576 0.490261 61.9646 28.092)\"\n            }\n          ) }),\n          /* @__PURE__ */ jsx(\"clipPath\", { id: \"clip3_20787_38934\", children: /* @__PURE__ */ jsx(\n            \"rect\",\n            {\n              width: \"12\",\n              height: \"12\",\n              className: \"fill-ui-bg-base\",\n              transform: \"matrix(0.865865 0.500278 -0.871576 0.490261 98.3596 47.1509)\"\n            }\n          ) }),\n          /* @__PURE__ */ jsx(\"clipPath\", { id: \"clip4_20787_38934\", children: /* @__PURE__ */ jsx(\n            \"rect\",\n            {\n              width: \"12\",\n              height: \"12\",\n              className: \"fill-ui-bg-base\",\n              transform: \"matrix(0.865865 0.500278 -0.871576 0.490261 108.75 53.1543)\"\n            }\n          ) }),\n          /* @__PURE__ */ jsx(\"clipPath\", { id: \"clip5_20787_38934\", children: /* @__PURE__ */ jsx(\n            \"rect\",\n            {\n              width: \"12\",\n              height: \"12\",\n              className: \"fill-ui-bg-base\",\n              transform: \"matrix(0.865865 0.500278 -0.871576 0.490261 119.14 59.1575)\"\n            }\n          ) }),\n          /* @__PURE__ */ jsx(\"clipPath\", { id: \"clip6_20787_38934\", children: /* @__PURE__ */ jsx(\n            \"rect\",\n            {\n              width: \"12\",\n              height: \"12\",\n              className: \"fill-ui-bg-base\",\n              transform: \"matrix(0.865865 0.500278 -0.871576 0.490261 80.9282 56.9561)\"\n            }\n          ) }),\n          /* @__PURE__ */ jsx(\"clipPath\", { id: \"clip7_20787_38934\", children: /* @__PURE__ */ jsx(\n            \"rect\",\n            {\n              width: \"12\",\n              height: \"12\",\n              className: \"fill-ui-bg-base\",\n              transform: \"matrix(0.865865 0.500278 -0.871576 0.490261 91.3186 62.9595)\"\n            }\n          ) }),\n          /* @__PURE__ */ jsx(\"clipPath\", { id: \"clip8_20787_38934\", children: /* @__PURE__ */ jsx(\n            \"rect\",\n            {\n              width: \"12\",\n              height: \"12\",\n              className: \"fill-ui-bg-base\",\n              transform: \"matrix(0.865865 0.500278 -0.871576 0.490261 101.709 68.9626)\"\n            }\n          ) })\n        ] })\n      ]\n    }\n  );\n}\n\n// src/routes/orders/order-request-transfer/components/create-order-transfer-form/create-order-transfer-form.tsx\nimport { jsx as jsx2, jsxs as jsxs2 } from \"react/jsx-runtime\";\nvar CreateOrderTransferSchema = zod.object({\n  customer_id: zod.string().min(1),\n  current_customer_details: zod.string().min(1)\n});\nfunction CreateOrderTransferForm({\n  order\n}) {\n  const { t } = useTranslation();\n  const { handleSuccess } = useRouteModal();\n  const form = useForm({\n    defaultValues: {\n      customer_id: \"\",\n      current_customer_details: order.customer?.first_name ? `${order.customer?.first_name} ${order.customer?.last_name} (${order.customer?.email}) ` : order.customer?.email\n    },\n    resolver: zodResolver(CreateOrderTransferSchema)\n  });\n  const customers = useComboboxData({\n    queryKey: [\"customers\"],\n    queryFn: (params) => sdk.admin.customer.list({ ...params, has_account: true }),\n    getOptions: (data) => data.customers.map((item) => ({\n      label: `${item.first_name || \"\"} ${item.last_name || \"\"} (${item.email})`,\n      value: item.id\n    }))\n  });\n  const { mutateAsync, isPending } = useRequestTransferOrder(order.id);\n  const handleSubmit = form.handleSubmit(async (data) => {\n    try {\n      await mutateAsync({\n        customer_id: data.customer_id\n      });\n      toast.success(t(\"orders.transfer.requestSuccess\", { email: order.email }));\n      handleSuccess();\n    } catch (error) {\n      toast.error(error.message);\n    }\n  });\n  return /* @__PURE__ */ jsx2(RouteDrawer.Form, { form, children: /* @__PURE__ */ jsxs2(\n    KeyboundForm,\n    {\n      onSubmit: handleSubmit,\n      className: \"flex size-full flex-col overflow-hidden\",\n      children: [\n        /* @__PURE__ */ jsx2(RouteDrawer.Body, { className: \"flex-1 overflow-auto\", children: /* @__PURE__ */ jsxs2(\"div\", { className: \"flex flex-col gap-y-8\", children: [\n          /* @__PURE__ */ jsx2(\"div\", { className: \"flex justify-center\", children: /* @__PURE__ */ jsx2(TransferHeader, {}) }),\n          /* @__PURE__ */ jsx2(\n            Form.Field,\n            {\n              control: form.control,\n              name: \"current_customer_details\",\n              render: ({ field }) => {\n                return /* @__PURE__ */ jsxs2(Form.Item, { children: [\n                  /* @__PURE__ */ jsx2(Form.Label, { children: t(\"orders.transfer.currentOwner\") }),\n                  /* @__PURE__ */ jsx2(\"span\", { className: \"txt-small text-ui-fg-muted\", children: t(\"orders.transfer.currentOwnerDescription\") }),\n                  /* @__PURE__ */ jsx2(Form.Control, { children: /* @__PURE__ */ jsx2(Input, { type: \"email\", ...field, disabled: true }) }),\n                  /* @__PURE__ */ jsx2(Form.ErrorMessage, {})\n                ] });\n              }\n            }\n          ),\n          /* @__PURE__ */ jsx2(\n            Form.Field,\n            {\n              control: form.control,\n              name: \"customer_id\",\n              render: ({ field }) => {\n                return /* @__PURE__ */ jsxs2(Form.Item, { children: [\n                  /* @__PURE__ */ jsx2(Form.Label, { children: t(\"orders.transfer.newOwner\") }),\n                  /* @__PURE__ */ jsx2(\"span\", { className: \"txt-small text-ui-fg-muted\", children: t(\"orders.transfer.newOwnerDescription\") }),\n                  /* @__PURE__ */ jsx2(Form.Control, { children: /* @__PURE__ */ jsx2(\n                    Combobox,\n                    {\n                      ...field,\n                      options: customers.options,\n                      searchValue: customers.searchValue,\n                      onSearchValueChange: customers.onSearchValueChange,\n                      fetchNextPage: customers.fetchNextPage,\n                      className: \"bg-ui-bg-field-component hover:bg-ui-bg-field-component-hover\",\n                      placeholder: t(\"actions.select\")\n                    }\n                  ) }),\n                  /* @__PURE__ */ jsx2(Form.ErrorMessage, {})\n                ] });\n              }\n            }\n          )\n        ] }) }),\n        /* @__PURE__ */ jsx2(RouteDrawer.Footer, { children: /* @__PURE__ */ jsxs2(\"div\", { className: \"flex items-center justify-end gap-x-2\", children: [\n          /* @__PURE__ */ jsx2(RouteDrawer.Close, { asChild: true, children: /* @__PURE__ */ jsx2(Button, { variant: \"secondary\", size: \"small\", children: t(\"actions.cancel\") }) }),\n          /* @__PURE__ */ jsx2(\n            Button,\n            {\n              isLoading: isPending,\n              type: \"submit\",\n              variant: \"primary\",\n              size: \"small\",\n              disabled: !!Object.keys(form.formState.errors || {}).length,\n              children: t(\"actions.save\")\n            }\n          )\n        ] }) })\n      ]\n    }\n  ) });\n}\n\n// src/routes/orders/order-request-transfer/order-request-transfer.tsx\nimport { jsx as jsx3, jsxs as jsxs3 } from \"react/jsx-runtime\";\nvar OrderRequestTransfer = () => {\n  const { t } = useTranslation2();\n  const params = useParams();\n  const orderId = params.order_id || params.id;\n  const { order, isPending, isError } = useOrder(orderId, {\n    fields: DEFAULT_FIELDS\n  });\n  if (!isPending && isError) {\n    throw new Error(\"Order not found\");\n  }\n  return /* @__PURE__ */ jsxs3(RouteDrawer, { children: [\n    /* @__PURE__ */ jsx3(RouteDrawer.Header, { children: /* @__PURE__ */ jsx3(Heading, { children: t(\"orders.transfer.title\") }) }),\n    order && /* @__PURE__ */ jsx3(CreateOrderTransferForm, { order })\n  ] });\n};\nexport {\n  OrderRequestTransfer as Component\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsEA,yBAA0B;AAuX1B,IAAAA,sBAA2C;AA2G3C,IAAAA,sBAA2C;AAje3C,SAAS,iBAAiB;AACxB,aAAuB;AAAA,IACrB;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,MAAM;AAAA,MACN,OAAO;AAAA,MACP,UAAU;AAAA,YACQ;AAAA,UACd;AAAA,UACA;AAAA,YACE,GAAG;AAAA,YACH,GAAG;AAAA,YACH,OAAO;AAAA,YACP,QAAQ;AAAA,YACR,IAAI;AAAA,YACJ,WAAW;AAAA,YACX,WAAW;AAAA,YACX,aAAa;AAAA,UACf;AAAA,QACF;AAAA,YACgB;AAAA,UACd;AAAA,UACA;AAAA,YACE,GAAG;AAAA,YACH,GAAG;AAAA,YACH,OAAO;AAAA,YACP,QAAQ;AAAA,YACR,IAAI;AAAA,YACJ,WAAW;AAAA,YACX,WAAW;AAAA,YACX,aAAa;AAAA,UACf;AAAA,QACF;AAAA,YACgB,yBAAK,KAAK,EAAE,UAAU,2BAA2B,UAAU;AAAA,cACzD;AAAA,YACd;AAAA,YACA;AAAA,cACE,GAAG;AAAA,cACH,WAAW;AAAA,cACX,aAAa;AAAA,cACb,eAAe;AAAA,cACf,gBAAgB;AAAA,YAClB;AAAA,UACF;AAAA,cACgB;AAAA,YACd;AAAA,YACA;AAAA,cACE,SAAS;AAAA,cACT,GAAG;AAAA,cACH,WAAW;AAAA,cACX,aAAa;AAAA,cACb,eAAe;AAAA,cACf,gBAAgB;AAAA,YAClB;AAAA,UACF;AAAA,cACgB;AAAA,YACd;AAAA,YACA;AAAA,cACE,SAAS;AAAA,cACT,GAAG;AAAA,cACH,WAAW;AAAA,cACX,aAAa;AAAA,cACb,eAAe;AAAA,cACf,gBAAgB;AAAA,YAClB;AAAA,UACF;AAAA,cACgB;AAAA,YACd;AAAA,YACA;AAAA,cACE,SAAS;AAAA,cACT,GAAG;AAAA,cACH,WAAW;AAAA,cACX,aAAa;AAAA,cACb,eAAe;AAAA,cACf,gBAAgB;AAAA,YAClB;AAAA,UACF;AAAA,cACgB;AAAA,YACd;AAAA,YACA;AAAA,cACE,SAAS;AAAA,cACT,GAAG;AAAA,cACH,WAAW;AAAA,cACX,aAAa;AAAA,cACb,eAAe;AAAA,cACf,gBAAgB;AAAA,YAClB;AAAA,UACF;AAAA,cACgB;AAAA,YACd;AAAA,YACA;AAAA,cACE,SAAS;AAAA,cACT,GAAG;AAAA,cACH,WAAW;AAAA,cACX,aAAa;AAAA,cACb,eAAe;AAAA,cACf,gBAAgB;AAAA,YAClB;AAAA,UACF;AAAA,cACgB;AAAA,YACd;AAAA,YACA;AAAA,cACE,SAAS;AAAA,cACT,GAAG;AAAA,cACH,WAAW;AAAA,cACX,aAAa;AAAA,cACb,eAAe;AAAA,cACf,gBAAgB;AAAA,YAClB;AAAA,UACF;AAAA,cACgB;AAAA,YACd;AAAA,YACA;AAAA,cACE,SAAS;AAAA,cACT,GAAG;AAAA,cACH,WAAW;AAAA,cACX,aAAa;AAAA,cACb,eAAe;AAAA,cACf,gBAAgB;AAAA,YAClB;AAAA,UACF;AAAA,QACF,EAAE,CAAC;AAAA,YACa;AAAA,UACd;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,QAAQ;AAAA,YACR,IAAI;AAAA,YACJ,WAAW;AAAA,YACX,WAAW;AAAA,UACb;AAAA,QACF;AAAA,YACgB;AAAA,UACd;AAAA,UACA;AAAA,YACE,GAAG;AAAA,YACH,GAAG;AAAA,YACH,OAAO;AAAA,YACP,QAAQ;AAAA,YACR,IAAI;AAAA,YACJ,WAAW;AAAA,YACX,WAAW;AAAA,YACX,aAAa;AAAA,UACf;AAAA,QACF;AAAA,YACgB;AAAA,UACd;AAAA,UACA;AAAA,YACE,GAAG;AAAA,YACH,GAAG;AAAA,YACH,OAAO;AAAA,YACP,QAAQ;AAAA,YACR,IAAI;AAAA,YACJ,WAAW;AAAA,YACX,WAAW;AAAA,YACX,aAAa;AAAA,UACf;AAAA,QACF;AAAA,YACgB;AAAA,UACd;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,QAAQ;AAAA,YACR,IAAI;AAAA,YACJ,WAAW;AAAA,YACX,WAAW;AAAA,UACb;AAAA,QACF;AAAA,YACgB;AAAA,UACd;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,QAAQ;AAAA,YACR,IAAI;AAAA,YACJ,WAAW;AAAA,YACX,WAAW;AAAA,UACb;AAAA,QACF;AAAA,YACgB;AAAA,UACd;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,QAAQ;AAAA,YACR,IAAI;AAAA,YACJ,WAAW;AAAA,YACX,WAAW;AAAA,UACb;AAAA,QACF;AAAA,YACgB,wBAAI,KAAK,EAAE,UAAU,2BAA2B,cAA0B;AAAA,UACxF;AAAA,UACA;AAAA,YACE,GAAG;AAAA,YACH,WAAW;AAAA,UACb;AAAA,QACF,EAAE,CAAC;AAAA,YACa;AAAA,UACd;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,QAAQ;AAAA,YACR,IAAI;AAAA,YACJ,WAAW;AAAA,YACX,WAAW;AAAA,UACb;AAAA,QACF;AAAA,YACgB;AAAA,UACd;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,QAAQ;AAAA,YACR,IAAI;AAAA,YACJ,WAAW;AAAA,YACX,WAAW;AAAA,UACb;AAAA,QACF;AAAA,YACgB,wBAAI,KAAK,EAAE,UAAU,2BAA2B,cAA0B;AAAA,UACxF;AAAA,UACA;AAAA,YACE,GAAG;AAAA,YACH,WAAW;AAAA,UACb;AAAA,QACF,EAAE,CAAC;AAAA,YACa,wBAAI,KAAK,EAAE,UAAU,2BAA2B,cAA0B;AAAA,UACxF;AAAA,UACA;AAAA,YACE,GAAG;AAAA,YACH,WAAW;AAAA,YACX,aAAa;AAAA,YACb,eAAe;AAAA,YACf,gBAAgB;AAAA,UAClB;AAAA,QACF,EAAE,CAAC;AAAA,YACa,wBAAI,KAAK,EAAE,UAAU,2BAA2B,cAA0B;AAAA,UACxF;AAAA,UACA;AAAA,YACE,GAAG;AAAA,YACH,WAAW;AAAA,YACX,aAAa;AAAA,YACb,eAAe;AAAA,YACf,gBAAgB;AAAA,UAClB;AAAA,QACF,EAAE,CAAC;AAAA,YACa,wBAAI,KAAK,EAAE,UAAU,2BAA2B,cAA0B;AAAA,UACxF;AAAA,UACA;AAAA,YACE,GAAG;AAAA,YACH,WAAW;AAAA,YACX,aAAa;AAAA,YACb,eAAe;AAAA,YACf,gBAAgB;AAAA,UAClB;AAAA,QACF,EAAE,CAAC;AAAA,YACa,wBAAI,KAAK,EAAE,UAAU,2BAA2B,cAA0B;AAAA,UACxF;AAAA,UACA;AAAA,YACE,GAAG;AAAA,YACH,WAAW;AAAA,YACX,aAAa;AAAA,YACb,eAAe;AAAA,YACf,gBAAgB;AAAA,UAClB;AAAA,QACF,EAAE,CAAC;AAAA,YACa,wBAAI,KAAK,EAAE,UAAU,2BAA2B,cAA0B;AAAA,UACxF;AAAA,UACA;AAAA,YACE,GAAG;AAAA,YACH,WAAW;AAAA,YACX,aAAa;AAAA,YACb,eAAe;AAAA,YACf,gBAAgB;AAAA,UAClB;AAAA,QACF,EAAE,CAAC;AAAA,YACa,wBAAI,KAAK,EAAE,UAAU,2BAA2B,cAA0B;AAAA,UACxF;AAAA,UACA;AAAA,YACE,GAAG;AAAA,YACH,WAAW;AAAA,YACX,aAAa;AAAA,YACb,eAAe;AAAA,YACf,gBAAgB;AAAA,UAClB;AAAA,QACF,EAAE,CAAC;AAAA,YACa,yBAAK,QAAQ,EAAE,UAAU;AAAA,cACvB,wBAAI,YAAY,EAAE,IAAI,qBAAqB,cAA0B;AAAA,YACnF;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,QAAQ;AAAA,cACR,WAAW;AAAA,cACX,WAAW;AAAA,YACb;AAAA,UACF,EAAE,CAAC;AAAA,cACa,wBAAI,YAAY,EAAE,IAAI,qBAAqB,cAA0B;AAAA,YACnF;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,QAAQ;AAAA,cACR,WAAW;AAAA,cACX,WAAW;AAAA,YACb;AAAA,UACF,EAAE,CAAC;AAAA,cACa,wBAAI,YAAY,EAAE,IAAI,qBAAqB,cAA0B;AAAA,YACnF;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,QAAQ;AAAA,cACR,WAAW;AAAA,cACX,WAAW;AAAA,YACb;AAAA,UACF,EAAE,CAAC;AAAA,cACa,wBAAI,YAAY,EAAE,IAAI,qBAAqB,cAA0B;AAAA,YACnF;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,QAAQ;AAAA,cACR,WAAW;AAAA,cACX,WAAW;AAAA,YACb;AAAA,UACF,EAAE,CAAC;AAAA,cACa,wBAAI,YAAY,EAAE,IAAI,qBAAqB,cAA0B;AAAA,YACnF;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,QAAQ;AAAA,cACR,WAAW;AAAA,cACX,WAAW;AAAA,YACb;AAAA,UACF,EAAE,CAAC;AAAA,cACa,wBAAI,YAAY,EAAE,IAAI,qBAAqB,cAA0B;AAAA,YACnF;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,QAAQ;AAAA,cACR,WAAW;AAAA,cACX,WAAW;AAAA,YACb;AAAA,UACF,EAAE,CAAC;AAAA,cACa,wBAAI,YAAY,EAAE,IAAI,qBAAqB,cAA0B;AAAA,YACnF;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,QAAQ;AAAA,cACR,WAAW;AAAA,cACX,WAAW;AAAA,YACb;AAAA,UACF,EAAE,CAAC;AAAA,cACa,wBAAI,YAAY,EAAE,IAAI,qBAAqB,cAA0B;AAAA,YACnF;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,QAAQ;AAAA,cACR,WAAW;AAAA,cACX,WAAW;AAAA,YACb;AAAA,UACF,EAAE,CAAC;AAAA,cACa,wBAAI,YAAY,EAAE,IAAI,qBAAqB,cAA0B;AAAA,YACnF;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,QAAQ;AAAA,cACR,WAAW;AAAA,cACX,WAAW;AAAA,YACb;AAAA,UACF,EAAE,CAAC;AAAA,QACL,EAAE,CAAC;AAAA,MACL;AAAA,IACF;AAAA,EACF;AACF;AAIA,IAAI,4BAAgC,WAAO;AAAA,EACzC,aAAiB,WAAO,EAAE,IAAI,CAAC;AAAA,EAC/B,0BAA8B,WAAO,EAAE,IAAI,CAAC;AAC9C,CAAC;AACD,SAAS,wBAAwB;AAAA,EAC/B;AACF,GAAG;AApcH;AAqcE,QAAM,EAAE,GAAAC,GAAE,IAAI,eAAe;AAC7B,QAAM,EAAE,cAAc,IAAI,cAAc;AACxC,QAAM,OAAO,QAAQ;AAAA,IACnB,eAAe;AAAA,MACb,aAAa;AAAA,MACb,4BAA0B,WAAM,aAAN,mBAAgB,cAAa,IAAG,WAAM,aAAN,mBAAgB,UAAU,KAAI,WAAM,aAAN,mBAAgB,SAAS,MAAK,WAAM,aAAN,mBAAgB,KAAK,QAAO,WAAM,aAAN,mBAAgB;AAAA,IACpK;AAAA,IACA,UAAU,EAAY,yBAAyB;AAAA,EACjD,CAAC;AACD,QAAM,YAAY,gBAAgB;AAAA,IAChC,UAAU,CAAC,WAAW;AAAA,IACtB,SAAS,CAAC,WAAW,IAAI,MAAM,SAAS,KAAK,EAAE,GAAG,QAAQ,aAAa,KAAK,CAAC;AAAA,IAC7E,YAAY,CAAC,SAAS,KAAK,UAAU,IAAI,CAAC,UAAU;AAAA,MAClD,OAAO,GAAG,KAAK,cAAc,EAAE,IAAI,KAAK,aAAa,EAAE,KAAK,KAAK,KAAK;AAAA,MACtE,OAAO,KAAK;AAAA,IACd,EAAE;AAAA,EACJ,CAAC;AACD,QAAM,EAAE,aAAa,UAAU,IAAI,wBAAwB,MAAM,EAAE;AACnE,QAAM,eAAe,KAAK,aAAa,OAAO,SAAS;AACrD,QAAI;AACF,YAAM,YAAY;AAAA,QAChB,aAAa,KAAK;AAAA,MACpB,CAAC;AACD,YAAM,QAAQA,GAAE,kCAAkC,EAAE,OAAO,MAAM,MAAM,CAAC,CAAC;AACzE,oBAAc;AAAA,IAChB,SAAS,OAAO;AACd,YAAM,MAAM,MAAM,OAAO;AAAA,IAC3B;AAAA,EACF,CAAC;AACD,aAAuB,oBAAAC,KAAK,YAAY,MAAM,EAAE,MAAM,cAA0B,oBAAAC;AAAA,IAC9E;AAAA,IACA;AAAA,MACE,UAAU;AAAA,MACV,WAAW;AAAA,MACX,UAAU;AAAA,YACQ,oBAAAD,KAAK,YAAY,MAAM,EAAE,WAAW,wBAAwB,cAA0B,oBAAAC,MAAM,OAAO,EAAE,WAAW,yBAAyB,UAAU;AAAA,cACjJ,oBAAAD,KAAK,OAAO,EAAE,WAAW,uBAAuB,cAA0B,oBAAAA,KAAK,gBAAgB,CAAC,CAAC,EAAE,CAAC;AAAA,cACpG,oBAAAA;AAAA,YACd,KAAK;AAAA,YACL;AAAA,cACE,SAAS,KAAK;AAAA,cACd,MAAM;AAAA,cACN,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,2BAAuB,oBAAAC,MAAM,KAAK,MAAM,EAAE,UAAU;AAAA,sBAClC,oBAAAD,KAAK,KAAK,OAAO,EAAE,UAAUD,GAAE,8BAA8B,EAAE,CAAC;AAAA,sBAChE,oBAAAC,KAAK,QAAQ,EAAE,WAAW,8BAA8B,UAAUD,GAAE,yCAAyC,EAAE,CAAC;AAAA,sBAChH,oBAAAC,KAAK,KAAK,SAAS,EAAE,cAA0B,oBAAAA,KAAK,OAAO,EAAE,MAAM,SAAS,GAAG,OAAO,UAAU,KAAK,CAAC,EAAE,CAAC;AAAA,sBACzG,oBAAAA,KAAK,KAAK,cAAc,CAAC,CAAC;AAAA,gBAC5C,EAAE,CAAC;AAAA,cACL;AAAA,YACF;AAAA,UACF;AAAA,cACgB,oBAAAA;AAAA,YACd,KAAK;AAAA,YACL;AAAA,cACE,SAAS,KAAK;AAAA,cACd,MAAM;AAAA,cACN,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,2BAAuB,oBAAAC,MAAM,KAAK,MAAM,EAAE,UAAU;AAAA,sBAClC,oBAAAD,KAAK,KAAK,OAAO,EAAE,UAAUD,GAAE,0BAA0B,EAAE,CAAC;AAAA,sBAC5D,oBAAAC,KAAK,QAAQ,EAAE,WAAW,8BAA8B,UAAUD,GAAE,qCAAqC,EAAE,CAAC;AAAA,sBAC5G,oBAAAC,KAAK,KAAK,SAAS,EAAE,cAA0B,oBAAAA;AAAA,oBAC7D;AAAA,oBACA;AAAA,sBACE,GAAG;AAAA,sBACH,SAAS,UAAU;AAAA,sBACnB,aAAa,UAAU;AAAA,sBACvB,qBAAqB,UAAU;AAAA,sBAC/B,eAAe,UAAU;AAAA,sBACzB,WAAW;AAAA,sBACX,aAAaD,GAAE,gBAAgB;AAAA,oBACjC;AAAA,kBACF,EAAE,CAAC;AAAA,sBACa,oBAAAC,KAAK,KAAK,cAAc,CAAC,CAAC;AAAA,gBAC5C,EAAE,CAAC;AAAA,cACL;AAAA,YACF;AAAA,UACF;AAAA,QACF,EAAE,CAAC,EAAE,CAAC;AAAA,YACU,oBAAAA,KAAK,YAAY,QAAQ,EAAE,cAA0B,oBAAAC,MAAM,OAAO,EAAE,WAAW,yCAAyC,UAAU;AAAA,cAChI,oBAAAD,KAAK,YAAY,OAAO,EAAE,SAAS,MAAM,cAA0B,oBAAAA,KAAK,QAAQ,EAAE,SAAS,aAAa,MAAM,SAAS,UAAUD,GAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;AAAA,cACzJ,oBAAAC;AAAA,YACd;AAAA,YACA;AAAA,cACE,WAAW;AAAA,cACX,MAAM;AAAA,cACN,SAAS;AAAA,cACT,MAAM;AAAA,cACN,UAAU,CAAC,CAAC,OAAO,KAAK,KAAK,UAAU,UAAU,CAAC,CAAC,EAAE;AAAA,cACrD,UAAUD,GAAE,cAAc;AAAA,YAC5B;AAAA,UACF;AAAA,QACF,EAAE,CAAC,EAAE,CAAC;AAAA,MACR;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;AAIA,IAAI,uBAAuB,MAAM;AAC/B,QAAM,EAAE,GAAAA,GAAE,IAAI,eAAgB;AAC9B,QAAM,SAAS,UAAU;AACzB,QAAM,UAAU,OAAO,YAAY,OAAO;AAC1C,QAAM,EAAE,OAAO,WAAW,QAAQ,IAAI,SAAS,SAAS;AAAA,IACtD,QAAQ;AAAA,EACV,CAAC;AACD,MAAI,CAAC,aAAa,SAAS;AACzB,UAAM,IAAI,MAAM,iBAAiB;AAAA,EACnC;AACA,aAAuB,oBAAAG,MAAM,aAAa,EAAE,UAAU;AAAA,QACpC,oBAAAC,KAAK,YAAY,QAAQ,EAAE,cAA0B,oBAAAA,KAAK,SAAS,EAAE,UAAUJ,GAAE,uBAAuB,EAAE,CAAC,EAAE,CAAC;AAAA,IAC9H,aAAyB,oBAAAI,KAAK,yBAAyB,EAAE,MAAM,CAAC;AAAA,EAClE,EAAE,CAAC;AACL;", "names": ["import_jsx_runtime", "t", "jsx2", "jsxs2", "jsxs3", "jsx3"]}