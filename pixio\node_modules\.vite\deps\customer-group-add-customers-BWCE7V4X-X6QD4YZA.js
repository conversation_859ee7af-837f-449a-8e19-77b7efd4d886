import {
  useCustomerTableColumns
} from "./chunk-5BDJR5GO.js";
import {
  useCustomerTableQuery
} from "./chunk-QG4LHCCG.js";
import "./chunk-ZJX5R5NM.js";
import "./chunk-OVCKROM5.js";
import "./chunk-EGRHWZRV.js";
import "./chunk-S5JEKJNE.js";
import {
  _DataTable,
  useDataTable
} from "./chunk-VCX3BVQR.js";
import "./chunk-VCBFQV64.js";
import "./chunk-QLJZR2JY.js";
import "./chunk-X3TOWPPJ.js";
import "./chunk-EGRHWZRV.js";
import {
  KeyboundForm
} from "./chunk-DP54EP6X.js";
import "./chunk-32T72GVU.js";
import {
  useCustomerTableFilters
} from "./chunk-EB3HY52D.js";
import "./chunk-MX43XOWY.js";
import "./chunk-QX6SXRUW.js";
import "./chunk-7ANVLPZR.js";
import "./chunk-2E2FUO6N.js";
import {
  RouteFocusModal,
  useRouteModal
} from "./chunk-MVVOBQIC.js";
import {
  t
} from "./chunk-WHQIBI5S.js";
import {
  arrayType,
  objectType,
  stringType
} from "./chunk-4XXECALA.js";
import "./chunk-YXT43UJF.js";
import "./chunk-DL4QDYPT.js";
import "./chunk-NV2N3EWM.js";
import "./chunk-BF7OBKIN.js";
import "./chunk-7UAYECTW.js";
import "./chunk-Y3NYV3NU.js";
import "./chunk-MPXR7HT5.js";
import {
  useForm
} from "./chunk-XXJU43CK.js";
import {
  useAddCustomersToGroup,
  useCustomers
} from "./chunk-2AWKOUCD.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import "./chunk-JFCIYOH4.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-5GF3RGIE.js";
import {
  useParams
} from "./chunk-T7YBVUWZ.js";
import {
  Button,
  Checkbox,
  Hint,
  Tooltip,
  createColumnHelper,
  toast
} from "./chunk-YP2LLXWB.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-ZSQ362B4.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/customer-group-add-customers-BWCE7V4X.mjs
var import_react = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var AddCustomersSchema = objectType({
  customer_ids: arrayType(stringType()).min(1)
});
var PAGE_SIZE = 10;
var AddCustomersForm = ({
  customerGroupId
}) => {
  const { t: t2 } = useTranslation();
  const { handleSuccess } = useRouteModal();
  const form = useForm({
    defaultValues: {
      customer_ids: []
    },
    resolver: t(AddCustomersSchema)
  });
  const { setValue } = form;
  const [rowSelection, setRowSelection] = (0, import_react.useState)({});
  (0, import_react.useEffect)(() => {
    setValue(
      "customer_ids",
      Object.keys(rowSelection).filter((k) => rowSelection[k]),
      {
        shouldDirty: true,
        shouldTouch: true
      }
    );
  }, [rowSelection, setValue]);
  const { searchParams, raw } = useCustomerTableQuery({ pageSize: PAGE_SIZE });
  const filters = useCustomerTableFilters();
  const { customers, count, isLoading, isError, error } = useCustomers({
    fields: "id,email,first_name,last_name,*groups",
    ...searchParams
  });
  const updater = (fn) => {
    const state = typeof fn === "function" ? fn(rowSelection) : fn;
    const ids = Object.keys(state);
    setValue("customer_ids", ids, {
      shouldDirty: true,
      shouldTouch: true
    });
    setRowSelection(state);
  };
  const columns = useColumns();
  const { table } = useDataTable({
    data: customers ?? [],
    columns,
    count,
    enablePagination: true,
    enableRowSelection: (row) => {
      var _a;
      return !((_a = row.original.groups) == null ? void 0 : _a.map((gc) => gc.id).includes(customerGroupId));
    },
    getRowId: (row) => row.id,
    pageSize: PAGE_SIZE,
    rowSelection: {
      state: rowSelection,
      updater
    }
  });
  const { mutateAsync, isPending } = useAddCustomersToGroup(customerGroupId);
  const handleSubmit = form.handleSubmit(async (data) => {
    await mutateAsync(data.customer_ids, {
      onSuccess: () => {
        toast.success(
          t2("customerGroups.customers.add.successToast", {
            count: data.customer_ids.length
          })
        );
        handleSuccess(`/customer-groups/${customerGroupId}`);
      },
      onError: (error2) => {
        toast.error(error2.message);
      }
    });
  });
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime.jsx)(RouteFocusModal.Form, { form, children: (0, import_jsx_runtime.jsxs)(
    KeyboundForm,
    {
      className: "flex h-full flex-col overflow-hidden",
      onSubmit: handleSubmit,
      children: [
        (0, import_jsx_runtime.jsx)(RouteFocusModal.Header, { children: (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center justify-end gap-x-2", children: [
          form.formState.errors.customer_ids && (0, import_jsx_runtime.jsx)(Hint, { variant: "error", children: form.formState.errors.customer_ids.message }),
          (0, import_jsx_runtime.jsx)(RouteFocusModal.Close, { asChild: true, children: (0, import_jsx_runtime.jsx)(Button, { variant: "secondary", size: "small", children: t2("actions.cancel") }) }),
          (0, import_jsx_runtime.jsx)(
            Button,
            {
              type: "submit",
              variant: "primary",
              size: "small",
              isLoading: isPending,
              children: t2("actions.save")
            }
          )
        ] }) }),
        (0, import_jsx_runtime.jsx)(RouteFocusModal.Body, { className: "size-full overflow-hidden", children: (0, import_jsx_runtime.jsx)(
          _DataTable,
          {
            table,
            columns,
            pageSize: PAGE_SIZE,
            count,
            filters,
            orderBy: [
              { key: "email", label: t2("fields.email") },
              { key: "first_name", label: t2("fields.firstName") },
              { key: "last_name", label: t2("fields.lastName") },
              { key: "has_account", label: t2("customers.hasAccount") },
              { key: "created_at", label: t2("fields.createdAt") },
              { key: "updated_at", label: t2("fields.updatedAt") }
            ],
            isLoading,
            layout: "fill",
            search: "autofocus",
            queryObject: raw,
            noRecords: {
              message: t2("customerGroups.customers.add.list.noRecordsMessage")
            }
          }
        ) })
      ]
    }
  ) });
};
var columnHelper = createColumnHelper();
var useColumns = () => {
  const { t: t2 } = useTranslation();
  const base = useCustomerTableColumns();
  const columns = (0, import_react.useMemo)(
    () => [
      columnHelper.display({
        id: "select",
        header: ({ table }) => {
          return (0, import_jsx_runtime.jsx)(
            Checkbox,
            {
              checked: table.getIsSomePageRowsSelected() ? "indeterminate" : table.getIsAllPageRowsSelected(),
              onCheckedChange: (value) => table.toggleAllPageRowsSelected(!!value)
            }
          );
        },
        cell: ({ row }) => {
          const isPreSelected = !row.getCanSelect();
          const isSelected = row.getIsSelected() || isPreSelected;
          const Component = (0, import_jsx_runtime.jsx)(
            Checkbox,
            {
              checked: isSelected,
              disabled: isPreSelected,
              onCheckedChange: (value) => row.toggleSelected(!!value),
              onClick: (e) => {
                e.stopPropagation();
              }
            }
          );
          if (isPreSelected) {
            return (0, import_jsx_runtime.jsx)(
              Tooltip,
              {
                content: t2("customerGroups.customers.alreadyAddedTooltip"),
                side: "right",
                children: Component
              }
            );
          }
          return Component;
        }
      }),
      ...base
    ],
    [t2, base]
  );
  return columns;
};
var CustomerGroupAddCustomers = () => {
  const { id } = useParams();
  return (0, import_jsx_runtime2.jsx)(RouteFocusModal, { children: (0, import_jsx_runtime2.jsx)(AddCustomersForm, { customerGroupId: id }) });
};
export {
  CustomerGroupAddCustomers as Component
};
//# sourceMappingURL=customer-group-add-customers-BWCE7V4X-X6QD4YZA.js.map
