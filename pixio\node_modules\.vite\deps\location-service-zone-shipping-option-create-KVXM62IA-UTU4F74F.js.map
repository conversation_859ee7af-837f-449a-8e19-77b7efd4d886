{"version": 3, "sources": ["../../@medusajs/dashboard/dist/location-service-zone-shipping-option-create-KVXM62IA.mjs"], "sourcesContent": ["import {\n  ConditionalPriceForm,\n  ConditionalPriceSchema,\n  ShippingOptionPriceProvider,\n  buildShippingOptionPriceRules,\n  useShippingOptionPriceColumns\n} from \"./chunk-VTDYXPA4.mjs\";\nimport {\n  CONDITIONAL_PRICES_STACKED_MODAL_ID,\n  ShippingOptionPriceType\n} from \"./chunk-PYIO3TDQ.mjs\";\nimport {\n  formatProvider\n} from \"./chunk-IR5DHEKS.mjs\";\nimport \"./chunk-PDWBYQOW.mjs\";\nimport {\n  useComboboxData\n} from \"./chunk-YIZSVS2R.mjs\";\nimport {\n  Combobox\n} from \"./chunk-GZBFGV7Y.mjs\";\nimport {\n  DataGrid\n} from \"./chunk-53RYGJCD.mjs\";\nimport \"./chunk-MWVM4TYO.mjs\";\nimport {\n  SwitchBox\n} from \"./chunk-D7H6ZNK4.mjs\";\nimport {\n  castNumber\n} from \"./chunk-6GU6IDUA.mjs\";\nimport \"./chunk-IUCDCPJU.mjs\";\nimport {\n  KeyboundForm\n} from \"./chunk-6HTZNHPT.mjs\";\nimport {\n  RouteFocusModal,\n  StackedFocusModal,\n  useRouteModal,\n  useStackedModal\n} from \"./chunk-4TC5YS65.mjs\";\nimport \"./chunk-LPEUYMRK.mjs\";\nimport \"./chunk-OC7BQLYI.mjs\";\nimport {\n  Form\n} from \"./chunk-OBQI23QM.mjs\";\nimport \"./chunk-Z5UDPQIH.mjs\";\nimport \"./chunk-KOSCMAIC.mjs\";\nimport \"./chunk-X6DSNTTX.mjs\";\nimport \"./chunk-I6E6CALJ.mjs\";\nimport \"./chunk-B4GODIOW.mjs\";\nimport \"./chunk-F6IJV2I2.mjs\";\nimport \"./chunk-QTCZFYFH.mjs\";\nimport \"./chunk-ENV6YVOM.mjs\";\nimport \"./chunk-PIR2H25N.mjs\";\nimport \"./chunk-RLY2SL5E.mjs\";\nimport \"./chunk-C5LYZZZ5.mjs\";\nimport \"./chunk-2ZKVRTBW.mjs\";\nimport \"./chunk-FO3VP56P.mjs\";\nimport \"./chunk-YS65UGPC.mjs\";\nimport \"./chunk-F6PXCY3N.mjs\";\nimport \"./chunk-3OHH43G6.mjs\";\nimport \"./chunk-G2H6MAK7.mjs\";\nimport {\n  useCreateShippingOptions\n} from \"./chunk-GRT22PE5.mjs\";\nimport {\n  useFulfillmentProviderOptions,\n  useStockLocation\n} from \"./chunk-32IQRUVY.mjs\";\nimport \"./chunk-FNYASI54.mjs\";\nimport \"./chunk-FVC7M755.mjs\";\nimport \"./chunk-ZJ3OFMHB.mjs\";\nimport \"./chunk-PNU5HPGY.mjs\";\nimport {\n  useStore\n} from \"./chunk-V2LANK5S.mjs\";\nimport {\n  useRegions\n} from \"./chunk-QZ6PT4QV.mjs\";\nimport {\n  usePricePreferences\n} from \"./chunk-QL4XKIVL.mjs\";\nimport \"./chunk-6I62UDJA.mjs\";\nimport \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport {\n  sdk\n} from \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-RPUOO7AV.mjs\";\n\n// src/routes/locations/location-service-zone-shipping-option-create/location-service-zone-shipping-option-create.tsx\nimport { json, useParams, useSearchParams } from \"react-router-dom\";\n\n// src/routes/locations/location-service-zone-shipping-option-create/components/create-shipping-options-form/create-shipping-options-form.tsx\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { Button, ProgressTabs, toast } from \"@medusajs/ui\";\nimport { useForm, useWatch as useWatch2 } from \"react-hook-form\";\nimport { useTranslation as useTranslation2 } from \"react-i18next\";\nimport { useState as useState2 } from \"react\";\n\n// src/routes/locations/location-service-zone-shipping-option-create/components/create-shipping-options-form/create-shipping-option-details-form.tsx\nimport { Divider, Heading, Input, RadioGroup, Select, Text } from \"@medusajs/ui\";\nimport { useTranslation } from \"react-i18next\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nimport { createElement } from \"react\";\nvar CreateShippingOptionDetailsForm = ({\n  form,\n  isReturn = false,\n  zone,\n  locationId,\n  fulfillmentProviderOptions,\n  selectedProviderId,\n  type\n}) => {\n  const { t } = useTranslation();\n  const isPickup = type === \"pickup\" /* Pickup */;\n  const shippingProfiles = useComboboxData({\n    queryFn: (params) => sdk.admin.shippingProfile.list(params),\n    queryKey: [\"shipping_profiles\"],\n    getOptions: (data) => data.shipping_profiles.map((profile) => ({\n      label: profile.name,\n      value: profile.id\n    }))\n  });\n  const fulfillmentProviders = useComboboxData({\n    queryFn: (params) => sdk.admin.fulfillmentProvider.list({\n      ...params,\n      stock_location_id: locationId\n    }),\n    queryKey: [\"fulfillment_providers\"],\n    getOptions: (data) => data.fulfillment_providers.map((provider) => ({\n      label: formatProvider(provider.id),\n      value: provider.id\n    }))\n  });\n  return /* @__PURE__ */ jsx(\"div\", { className: \"flex flex-1 flex-col items-center overflow-y-auto\", children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex w-full max-w-[720px] flex-col gap-y-8 px-6 py-16\", children: [\n    /* @__PURE__ */ jsxs(\"div\", { children: [\n      /* @__PURE__ */ jsx(Heading, { children: t(\n        `stockLocations.shippingOptions.create.${isPickup ? \"pickup\" : isReturn ? \"returns\" : \"shipping\"}.header`,\n        {\n          zone: zone.name\n        }\n      ) }),\n      /* @__PURE__ */ jsx(Text, { size: \"small\", className: \"text-ui-fg-subtle\", children: t(\n        `stockLocations.shippingOptions.create.${isReturn ? \"returns\" : isPickup ? \"pickup\" : \"shipping\"}.hint`\n      ) })\n    ] }),\n    !isPickup && /* @__PURE__ */ jsx(\n      Form.Field,\n      {\n        control: form.control,\n        name: \"price_type\",\n        render: ({ field }) => {\n          return /* @__PURE__ */ jsxs(Form.Item, { children: [\n            /* @__PURE__ */ jsx(Form.Label, { children: t(\"stockLocations.shippingOptions.fields.priceType.label\") }),\n            /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsxs(\n              RadioGroup,\n              {\n                className: \"grid grid-cols-1 gap-4 md:grid-cols-2\",\n                ...field,\n                onValueChange: field.onChange,\n                children: [\n                  /* @__PURE__ */ jsx(\n                    RadioGroup.ChoiceBox,\n                    {\n                      className: \"flex-1\",\n                      value: \"flat\" /* FlatRate */,\n                      label: t(\n                        \"stockLocations.shippingOptions.fields.priceType.options.fixed.label\"\n                      ),\n                      description: t(\n                        \"stockLocations.shippingOptions.fields.priceType.options.fixed.hint\"\n                      )\n                    }\n                  ),\n                  /* @__PURE__ */ jsx(\n                    RadioGroup.ChoiceBox,\n                    {\n                      className: \"flex-1\",\n                      value: \"calculated\" /* Calculated */,\n                      label: t(\n                        \"stockLocations.shippingOptions.fields.priceType.options.calculated.label\"\n                      ),\n                      description: t(\n                        \"stockLocations.shippingOptions.fields.priceType.options.calculated.hint\"\n                      )\n                    }\n                  )\n                ]\n              }\n            ) }),\n            /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n          ] });\n        }\n      }\n    ),\n    /* @__PURE__ */ jsxs(\"div\", { className: \"grid grid-cols-1 gap-4 md:grid-cols-2\", children: [\n      /* @__PURE__ */ jsx(\n        Form.Field,\n        {\n          control: form.control,\n          name: \"name\",\n          render: ({ field }) => {\n            return /* @__PURE__ */ jsxs(Form.Item, { children: [\n              /* @__PURE__ */ jsx(Form.Label, { children: t(\"fields.name\") }),\n              /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(Input, { ...field }) }),\n              /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n            ] });\n          }\n        }\n      ),\n      /* @__PURE__ */ jsx(\n        Form.Field,\n        {\n          control: form.control,\n          name: \"shipping_profile_id\",\n          render: ({ field }) => {\n            return /* @__PURE__ */ jsxs(Form.Item, { children: [\n              /* @__PURE__ */ jsx(Form.Label, { children: t(\"stockLocations.shippingOptions.fields.profile\") }),\n              /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(\n                Combobox,\n                {\n                  ...field,\n                  options: shippingProfiles.options,\n                  searchValue: shippingProfiles.searchValue,\n                  onSearchValueChange: shippingProfiles.onSearchValueChange,\n                  disabled: shippingProfiles.disabled\n                }\n              ) }),\n              /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n            ] });\n          }\n        }\n      )\n    ] }),\n    /* @__PURE__ */ jsxs(\"div\", { className: \"grid grid-cols-1 gap-4 md:grid-cols-2\", children: [\n      /* @__PURE__ */ jsx(\n        Form.Field,\n        {\n          control: form.control,\n          name: \"provider_id\",\n          render: ({ field }) => {\n            return /* @__PURE__ */ jsxs(Form.Item, { children: [\n              /* @__PURE__ */ jsx(\n                Form.Label,\n                {\n                  tooltip: t(\n                    \"stockLocations.fulfillmentProviders.shippingOptionsTooltip\"\n                  ),\n                  children: t(\"stockLocations.shippingOptions.fields.provider\")\n                }\n              ),\n              /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(\n                Combobox,\n                {\n                  ...field,\n                  onChange: (e) => {\n                    field.onChange(e);\n                    form.setValue(\"fulfillment_option_id\", \"\");\n                  },\n                  options: fulfillmentProviders.options,\n                  searchValue: fulfillmentProviders.searchValue,\n                  onSearchValueChange: fulfillmentProviders.onSearchValueChange,\n                  disabled: fulfillmentProviders.disabled\n                }\n              ) }),\n              /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n            ] });\n          }\n        }\n      ),\n      /* @__PURE__ */ jsx(\n        Form.Field,\n        {\n          control: form.control,\n          name: \"fulfillment_option_id\",\n          render: ({ field }) => {\n            return /* @__PURE__ */ jsxs(Form.Item, { children: [\n              /* @__PURE__ */ jsx(Form.Label, { children: t(\n                \"stockLocations.shippingOptions.fields.fulfillmentOption\"\n              ) }),\n              /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ createElement(\n                Select,\n                {\n                  ...field,\n                  onValueChange: field.onChange,\n                  disabled: !selectedProviderId,\n                  key: selectedProviderId\n                },\n                /* @__PURE__ */ jsx(Select.Trigger, { ref: field.ref, children: /* @__PURE__ */ jsx(Select.Value, {}) }),\n                /* @__PURE__ */ jsx(Select.Content, { children: fulfillmentProviderOptions?.filter((fo) => !!fo.is_return === isReturn).map((option) => /* @__PURE__ */ jsx(Select.Item, { value: option.id, children: option.name || option.id }, option.id)) })\n              ) }),\n              /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n            ] });\n          }\n        }\n      )\n    ] }),\n    /* @__PURE__ */ jsx(Divider, {}),\n    /* @__PURE__ */ jsx(\n      SwitchBox,\n      {\n        control: form.control,\n        name: \"enabled_in_store\",\n        label: t(\"stockLocations.shippingOptions.fields.enableInStore.label\"),\n        description: t(\n          \"stockLocations.shippingOptions.fields.enableInStore.hint\"\n        )\n      }\n    )\n  ] }) });\n};\n\n// src/routes/locations/location-service-zone-shipping-option-create/components/create-shipping-options-form/create-shipping-options-prices-form.tsx\nimport { useEffect, useMemo, useState } from \"react\";\nimport { useWatch } from \"react-hook-form\";\nimport { jsx as jsx2, jsxs as jsxs2 } from \"react/jsx-runtime\";\nvar CreateShippingOptionsPricesForm = ({\n  form,\n  type\n}) => {\n  const isPickup = type === \"pickup\" /* Pickup */;\n  const { getIsOpen, setIsOpen } = useStackedModal();\n  const [selectedPrice, setSelectedPrice] = useState(null);\n  const onOpenConditionalPricesModal = (info) => {\n    setIsOpen(CONDITIONAL_PRICES_STACKED_MODAL_ID, true);\n    setSelectedPrice(info);\n  };\n  const onCloseConditionalPricesModal = () => {\n    setIsOpen(CONDITIONAL_PRICES_STACKED_MODAL_ID, false);\n    setSelectedPrice(null);\n  };\n  const {\n    store,\n    isLoading: isStoreLoading,\n    isError: isStoreError,\n    error: storeError\n  } = useStore();\n  const currencies = useMemo(\n    () => store?.supported_currencies?.map((c) => c.currency_code) || [],\n    [store]\n  );\n  const {\n    regions,\n    isLoading: isRegionsLoading,\n    isError: isRegionsError,\n    error: regionsError\n  } = useRegions({\n    fields: \"id,name,currency_code\",\n    limit: 999\n  });\n  const { price_preferences: pricePreferences } = usePricePreferences({});\n  const { setCloseOnEscape } = useRouteModal();\n  const name = useWatch({ control: form.control, name: \"name\" });\n  const columns = useShippingOptionPriceColumns({\n    name,\n    currencies,\n    regions,\n    pricePreferences\n  });\n  const isLoading = isStoreLoading || !store || isRegionsLoading || !regions;\n  const data = useMemo(\n    () => [[...currencies || [], ...regions || []]],\n    [currencies, regions]\n  );\n  useEffect(() => {\n    if (!isLoading && isPickup) {\n      if (currencies.length > 0) {\n        currencies.forEach((currency) => {\n          form.setValue(`currency_prices.${currency}`, \"0\");\n        });\n      }\n      if (regions.length > 0) {\n        regions.forEach((region) => {\n          form.setValue(`region_prices.${region.id}`, \"0\");\n        });\n      }\n    }\n  }, [isLoading, isPickup]);\n  if (isStoreError) {\n    throw storeError;\n  }\n  if (isRegionsError) {\n    throw regionsError;\n  }\n  return /* @__PURE__ */ jsx2(\n    StackedFocusModal,\n    {\n      id: CONDITIONAL_PRICES_STACKED_MODAL_ID,\n      onOpenChangeCallback: (open) => {\n        if (!open) {\n          setSelectedPrice(null);\n        }\n      },\n      children: /* @__PURE__ */ jsx2(\n        ShippingOptionPriceProvider,\n        {\n          onOpenConditionalPricesModal,\n          onCloseConditionalPricesModal,\n          children: /* @__PURE__ */ jsxs2(\"div\", { className: \"flex size-full flex-col divide-y overflow-hidden\", children: [\n            /* @__PURE__ */ jsx2(\n              DataGrid,\n              {\n                isLoading,\n                data,\n                columns,\n                state: form,\n                onEditingChange: (editing) => setCloseOnEscape(!editing),\n                disableInteractions: getIsOpen(CONDITIONAL_PRICES_STACKED_MODAL_ID)\n              }\n            ),\n            selectedPrice && /* @__PURE__ */ jsx2(ConditionalPriceForm, { info: selectedPrice, variant: \"create\" })\n          ] })\n        }\n      )\n    }\n  );\n};\n\n// src/routes/locations/location-service-zone-shipping-option-create/components/create-shipping-options-form/schema.ts\nimport { z } from \"zod\";\nvar CreateShippingOptionDetailsSchema = z.object({\n  name: z.string().min(1),\n  price_type: z.nativeEnum(ShippingOptionPriceType),\n  enabled_in_store: z.boolean(),\n  shipping_profile_id: z.string().min(1),\n  provider_id: z.string().min(1),\n  fulfillment_option_id: z.string().min(1)\n});\nvar ShippingOptionConditionalPriceSchema = z.object({\n  conditional_region_prices: z.record(\n    z.string(),\n    z.array(ConditionalPriceSchema).optional()\n  ),\n  conditional_currency_prices: z.record(\n    z.string(),\n    z.array(ConditionalPriceSchema).optional()\n  )\n});\nvar CreateShippingOptionSchema = z.object({\n  region_prices: z.record(z.string(), z.string().optional()),\n  currency_prices: z.record(z.string(), z.string().optional())\n}).merge(CreateShippingOptionDetailsSchema).merge(ShippingOptionConditionalPriceSchema);\n\n// src/routes/locations/location-service-zone-shipping-option-create/components/create-shipping-options-form/create-shipping-options-form.tsx\nimport { jsx as jsx3, jsxs as jsxs3 } from \"react/jsx-runtime\";\nfunction CreateShippingOptionsForm({\n  zone,\n  isReturn,\n  locationId,\n  type\n}) {\n  const [activeTab, setActiveTab] = useState2(\"details\" /* DETAILS */);\n  const [validDetails, setValidDetails] = useState2(false);\n  const { t } = useTranslation2();\n  const { handleSuccess } = useRouteModal();\n  const form = useForm({\n    defaultValues: {\n      name: \"\",\n      price_type: \"flat\" /* FlatRate */,\n      enabled_in_store: true,\n      shipping_profile_id: \"\",\n      provider_id: \"\",\n      fulfillment_option_id: \"\",\n      region_prices: {},\n      currency_prices: {},\n      conditional_region_prices: {},\n      conditional_currency_prices: {}\n    },\n    resolver: zodResolver(CreateShippingOptionSchema)\n  });\n  const selectedProviderId = useWatch2({\n    control: form.control,\n    name: \"provider_id\"\n  });\n  const { fulfillment_options: fulfillmentProviderOptions } = useFulfillmentProviderOptions(selectedProviderId, {\n    enabled: !!selectedProviderId\n  });\n  const isCalculatedPriceType = form.watch(\"price_type\") === \"calculated\" /* Calculated */;\n  const { mutateAsync, isPending: isLoading } = useCreateShippingOptions();\n  const handleSubmit = form.handleSubmit(async (data) => {\n    const currencyPrices = Object.entries(data.currency_prices).map(([code, value]) => {\n      if (!value) {\n        return void 0;\n      }\n      return {\n        currency_code: code,\n        amount: castNumber(value)\n      };\n    }).filter((p) => !!p);\n    const regionPrices = Object.entries(data.region_prices).map(([region_id, value]) => {\n      if (!value) {\n        return void 0;\n      }\n      return {\n        region_id,\n        amount: castNumber(value)\n      };\n    }).filter((p) => !!p);\n    const conditionalRegionPrices = Object.entries(\n      data.conditional_region_prices\n    ).flatMap(([region_id, value]) => {\n      const prices = value?.map((rule) => ({\n        region_id,\n        amount: castNumber(rule.amount),\n        rules: buildShippingOptionPriceRules(rule)\n      })) || [];\n      return prices?.filter(Boolean);\n    });\n    const conditionalCurrencyPrices = Object.entries(\n      data.conditional_currency_prices\n    ).flatMap(([currency_code, value]) => {\n      const prices = value?.map((rule) => ({\n        currency_code,\n        amount: castNumber(rule.amount),\n        rules: buildShippingOptionPriceRules(rule)\n      })) || [];\n      return prices?.filter(Boolean);\n    });\n    const allPrices = [\n      ...currencyPrices,\n      ...conditionalCurrencyPrices,\n      ...regionPrices,\n      ...conditionalRegionPrices\n    ];\n    const fulfillmentOptionData = fulfillmentProviderOptions?.find(\n      (fo) => fo.id === data.fulfillment_option_id\n    );\n    await mutateAsync(\n      {\n        name: data.name,\n        price_type: data.price_type,\n        service_zone_id: zone.id,\n        shipping_profile_id: data.shipping_profile_id,\n        provider_id: data.provider_id,\n        prices: allPrices,\n        data: fulfillmentOptionData,\n        rules: [\n          {\n            // eslint-disable-next-line\n            value: isReturn ? \"true\" : \"false\",\n            attribute: \"is_return\",\n            operator: \"eq\"\n          },\n          {\n            // eslint-disable-next-line\n            value: data.enabled_in_store ? \"true\" : \"false\",\n            attribute: \"enabled_in_store\",\n            operator: \"eq\"\n          }\n        ],\n        type: {\n          // TODO: FETCH TYPES\n          label: \"Type label\",\n          description: \"Type description\",\n          code: \"type-code\"\n        }\n      },\n      {\n        onSuccess: ({ shipping_option }) => {\n          toast.success(\n            t(\n              `stockLocations.shippingOptions.create.${isReturn ? \"returns\" : \"shipping\"}.successToast`,\n              { name: shipping_option.name }\n            )\n          );\n          handleSuccess(`/settings/locations/${locationId}`);\n        },\n        onError: (e) => {\n          toast.error(e.message);\n        }\n      }\n    );\n  });\n  const onTabChange = (tab) => {\n    if (tab === \"pricing\" /* PRICING */) {\n      form.clearErrors();\n      const result = CreateShippingOptionDetailsSchema.safeParse({\n        ...form.getValues()\n      });\n      if (!result.success) {\n        const [firstError, ...rest] = result.error.errors;\n        for (const error of rest) {\n          const _path = error.path.join(\".\");\n          form.setError(_path, {\n            message: error.message,\n            type: error.code\n          });\n        }\n        form.setError(\n          firstError.path.join(\".\"),\n          {\n            message: firstError.message,\n            type: firstError.code\n          },\n          {\n            shouldFocus: true\n          }\n        );\n        setValidDetails(false);\n        return;\n      }\n      setValidDetails(true);\n    }\n    setActiveTab(tab);\n  };\n  const pricesStatus = form.getFieldState(\"currency_prices\")?.isDirty || form.getFieldState(\"region_prices\")?.isDirty || activeTab === \"pricing\" /* PRICING */ ? \"in-progress\" : \"not-started\";\n  const detailsStatus = validDetails ? \"completed\" : \"in-progress\";\n  return /* @__PURE__ */ jsx3(RouteFocusModal.Form, { form, children: /* @__PURE__ */ jsx3(\n    KeyboundForm,\n    {\n      className: \"flex h-full flex-col\",\n      onSubmit: handleSubmit,\n      onKeyDown: (e) => {\n        const isEnterKey = e.key === \"Enter\";\n        const isModifierPressed = e.metaKey || e.ctrlKey;\n        const shouldContinueToPricing = activeTab !== \"pricing\" /* PRICING */ && !isCalculatedPriceType;\n        if (!isEnterKey) {\n          return;\n        }\n        e.preventDefault();\n        if (!isModifierPressed) {\n          return;\n        }\n        if (shouldContinueToPricing) {\n          e.stopPropagation();\n          onTabChange(\"pricing\" /* PRICING */);\n          return;\n        }\n        handleSubmit();\n      },\n      children: /* @__PURE__ */ jsxs3(\n        ProgressTabs,\n        {\n          value: activeTab,\n          className: \"flex h-full flex-col overflow-hidden\",\n          onValueChange: (tab) => onTabChange(tab),\n          children: [\n            /* @__PURE__ */ jsx3(RouteFocusModal.Header, { children: /* @__PURE__ */ jsxs3(ProgressTabs.List, { className: \"border-ui-border-base -my-2 ml-2 min-w-0 flex-1 border-l\", children: [\n              /* @__PURE__ */ jsx3(\n                ProgressTabs.Trigger,\n                {\n                  value: \"details\" /* DETAILS */,\n                  status: detailsStatus,\n                  className: \"w-full max-w-[200px]\",\n                  children: /* @__PURE__ */ jsx3(\"span\", { className: \"w-full cursor-auto overflow-hidden text-ellipsis whitespace-nowrap\", children: t(\"stockLocations.shippingOptions.create.tabs.details\") })\n                }\n              ),\n              !isCalculatedPriceType && /* @__PURE__ */ jsx3(\n                ProgressTabs.Trigger,\n                {\n                  value: \"pricing\" /* PRICING */,\n                  status: pricesStatus,\n                  className: \"w-full max-w-[200px]\",\n                  children: /* @__PURE__ */ jsx3(\"span\", { className: \"w-full overflow-hidden text-ellipsis whitespace-nowrap\", children: t(\"stockLocations.shippingOptions.create.tabs.prices\") })\n                }\n              )\n            ] }) }),\n            /* @__PURE__ */ jsxs3(RouteFocusModal.Body, { className: \"size-full overflow-hidden\", children: [\n              /* @__PURE__ */ jsx3(\n                ProgressTabs.Content,\n                {\n                  value: \"details\" /* DETAILS */,\n                  className: \"size-full overflow-y-auto\",\n                  children: /* @__PURE__ */ jsx3(\n                    CreateShippingOptionDetailsForm,\n                    {\n                      form,\n                      zone,\n                      isReturn,\n                      type,\n                      locationId,\n                      fulfillmentProviderOptions: fulfillmentProviderOptions || [],\n                      selectedProviderId\n                    }\n                  )\n                }\n              ),\n              /* @__PURE__ */ jsx3(ProgressTabs.Content, { value: \"pricing\" /* PRICING */, className: \"size-full\", children: /* @__PURE__ */ jsx3(CreateShippingOptionsPricesForm, { form, type }) })\n            ] }),\n            /* @__PURE__ */ jsx3(RouteFocusModal.Footer, { children: /* @__PURE__ */ jsxs3(\"div\", { className: \"flex items-center justify-end gap-x-2\", children: [\n              /* @__PURE__ */ jsx3(RouteFocusModal.Close, { asChild: true, children: /* @__PURE__ */ jsx3(Button, { variant: \"secondary\", size: \"small\", children: t(\"actions.cancel\") }) }),\n              activeTab === \"pricing\" /* PRICING */ || isCalculatedPriceType ? /* @__PURE__ */ jsx3(\n                Button,\n                {\n                  size: \"small\",\n                  className: \"whitespace-nowrap\",\n                  isLoading,\n                  type: \"submit\",\n                  children: t(\"actions.save\")\n                },\n                \"submit-btn\"\n              ) : /* @__PURE__ */ jsx3(\n                Button,\n                {\n                  size: \"small\",\n                  className: \"whitespace-nowrap\",\n                  isLoading,\n                  onClick: () => onTabChange(\"pricing\" /* PRICING */),\n                  type: \"button\",\n                  children: t(\"actions.continue\")\n                },\n                \"continue-btn\"\n              )\n            ] }) })\n          ]\n        }\n      )\n    }\n  ) });\n}\n\n// src/routes/locations/location-service-zone-shipping-option-create/constants.ts\nvar LOC_CREATE_SHIPPING_OPTION_FIELDS = \"*fulfillment_sets,*fulfillment_sets.service_zones,*fulfillment_sets.service_zones.shipping_options\";\n\n// src/routes/locations/location-service-zone-shipping-option-create/location-service-zone-shipping-option-create.tsx\nimport { jsx as jsx4 } from \"react/jsx-runtime\";\nfunction LocationServiceZoneShippingOptionCreate() {\n  const { location_id, fset_id, zone_id } = useParams();\n  const [searchParams] = useSearchParams();\n  const isReturn = searchParams.has(\"is_return\");\n  const { stock_location, isPending, isFetching, isError, error } = useStockLocation(location_id, {\n    fields: LOC_CREATE_SHIPPING_OPTION_FIELDS\n  });\n  const fulfillmentSet = stock_location?.fulfillment_sets?.find(\n    (f) => f.id === fset_id\n  );\n  if (!isPending && !isFetching && !fulfillmentSet) {\n    throw json(\n      { message: `Fulfillment set with ID ${fset_id} was not found` },\n      404\n    );\n  }\n  const zone = fulfillmentSet?.service_zones?.find((z2) => z2.id === zone_id);\n  if (!isPending && !isFetching && !zone) {\n    throw json(\n      { message: `Service zone with ID ${zone_id} was not found` },\n      404\n    );\n  }\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsx4(RouteFocusModal, { prev: `/settings/locations/${location_id}`, children: zone && /* @__PURE__ */ jsx4(\n    CreateShippingOptionsForm,\n    {\n      zone,\n      isReturn,\n      locationId: location_id,\n      type: fulfillmentSet.type\n    }\n  ) });\n}\nexport {\n  LocationServiceZoneShippingOptionCreate as Component\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmGA,mBAAsC;AAKtC,yBAA0B;AAC1B,IAAAA,gBAA8B;AAkN9B,IAAAA,gBAA6C;AAE7C,IAAAC,sBAA2C;AAiI3C,IAAAC,sBAA2C;AA+Q3C,IAAAA,sBAA4B;AAnmB5B,IAAI,kCAAkC,CAAC;AAAA,EACrC;AAAA,EACA,WAAW;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACJ,QAAM,EAAE,GAAAC,GAAE,IAAI,eAAe;AAC7B,QAAM,WAAW,SAAS;AAC1B,QAAM,mBAAmB,gBAAgB;AAAA,IACvC,SAAS,CAAC,WAAW,IAAI,MAAM,gBAAgB,KAAK,MAAM;AAAA,IAC1D,UAAU,CAAC,mBAAmB;AAAA,IAC9B,YAAY,CAAC,SAAS,KAAK,kBAAkB,IAAI,CAAC,aAAa;AAAA,MAC7D,OAAO,QAAQ;AAAA,MACf,OAAO,QAAQ;AAAA,IACjB,EAAE;AAAA,EACJ,CAAC;AACD,QAAM,uBAAuB,gBAAgB;AAAA,IAC3C,SAAS,CAAC,WAAW,IAAI,MAAM,oBAAoB,KAAK;AAAA,MACtD,GAAG;AAAA,MACH,mBAAmB;AAAA,IACrB,CAAC;AAAA,IACD,UAAU,CAAC,uBAAuB;AAAA,IAClC,YAAY,CAAC,SAAS,KAAK,sBAAsB,IAAI,CAAC,cAAc;AAAA,MAClE,OAAO,eAAe,SAAS,EAAE;AAAA,MACjC,OAAO,SAAS;AAAA,IAClB,EAAE;AAAA,EACJ,CAAC;AACD,aAAuB,wBAAI,OAAO,EAAE,WAAW,qDAAqD,cAA0B,yBAAK,OAAO,EAAE,WAAW,yDAAyD,UAAU;AAAA,QACxM,yBAAK,OAAO,EAAE,UAAU;AAAA,UACtB,wBAAI,SAAS,EAAE,UAAUA;AAAA,QACvC,yCAAyC,WAAW,WAAW,WAAW,YAAY,UAAU;AAAA,QAChG;AAAA,UACE,MAAM,KAAK;AAAA,QACb;AAAA,MACF,EAAE,CAAC;AAAA,UACa,wBAAI,MAAM,EAAE,MAAM,SAAS,WAAW,qBAAqB,UAAUA;AAAA,QACnF,yCAAyC,WAAW,YAAY,WAAW,WAAW,UAAU;AAAA,MAClG,EAAE,CAAC;AAAA,IACL,EAAE,CAAC;AAAA,IACH,CAAC,gBAA4B;AAAA,MAC3B,KAAK;AAAA,MACL;AAAA,QACE,SAAS,KAAK;AAAA,QACd,MAAM;AAAA,QACN,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,qBAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,gBACjC,wBAAI,KAAK,OAAO,EAAE,UAAUA,GAAE,uDAAuD,EAAE,CAAC;AAAA,gBACxF,wBAAI,KAAK,SAAS,EAAE,cAA0B;AAAA,cAC5D;AAAA,cACA;AAAA,gBACE,WAAW;AAAA,gBACX,GAAG;AAAA,gBACH,eAAe,MAAM;AAAA,gBACrB,UAAU;AAAA,sBACQ;AAAA,oBACd,WAAW;AAAA,oBACX;AAAA,sBACE,WAAW;AAAA,sBACX,OAAO;AAAA,sBACP,OAAOA;AAAA,wBACL;AAAA,sBACF;AAAA,sBACA,aAAaA;AAAA,wBACX;AAAA,sBACF;AAAA,oBACF;AAAA,kBACF;AAAA,sBACgB;AAAA,oBACd,WAAW;AAAA,oBACX;AAAA,sBACE,WAAW;AAAA,sBACX,OAAO;AAAA,sBACP,OAAOA;AAAA,wBACL;AAAA,sBACF;AAAA,sBACA,aAAaA;AAAA,wBACX;AAAA,sBACF;AAAA,oBACF;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAAA,YACF,EAAE,CAAC;AAAA,gBACa,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,UAC3C,EAAE,CAAC;AAAA,QACL;AAAA,MACF;AAAA,IACF;AAAA,QACgB,yBAAK,OAAO,EAAE,WAAW,yCAAyC,UAAU;AAAA,UAC1E;AAAA,QACd,KAAK;AAAA,QACL;AAAA,UACE,SAAS,KAAK;AAAA,UACd,MAAM;AAAA,UACN,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,uBAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,kBACjC,wBAAI,KAAK,OAAO,EAAE,UAAUA,GAAE,aAAa,EAAE,CAAC;AAAA,kBAC9C,wBAAI,KAAK,SAAS,EAAE,cAA0B,wBAAI,OAAO,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC;AAAA,kBACxE,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,YAC3C,EAAE,CAAC;AAAA,UACL;AAAA,QACF;AAAA,MACF;AAAA,UACgB;AAAA,QACd,KAAK;AAAA,QACL;AAAA,UACE,SAAS,KAAK;AAAA,UACd,MAAM;AAAA,UACN,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,uBAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,kBACjC,wBAAI,KAAK,OAAO,EAAE,UAAUA,GAAE,+CAA+C,EAAE,CAAC;AAAA,kBAChF,wBAAI,KAAK,SAAS,EAAE,cAA0B;AAAA,gBAC5D;AAAA,gBACA;AAAA,kBACE,GAAG;AAAA,kBACH,SAAS,iBAAiB;AAAA,kBAC1B,aAAa,iBAAiB;AAAA,kBAC9B,qBAAqB,iBAAiB;AAAA,kBACtC,UAAU,iBAAiB;AAAA,gBAC7B;AAAA,cACF,EAAE,CAAC;AAAA,kBACa,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,YAC3C,EAAE,CAAC;AAAA,UACL;AAAA,QACF;AAAA,MACF;AAAA,IACF,EAAE,CAAC;AAAA,QACa,yBAAK,OAAO,EAAE,WAAW,yCAAyC,UAAU;AAAA,UAC1E;AAAA,QACd,KAAK;AAAA,QACL;AAAA,UACE,SAAS,KAAK;AAAA,UACd,MAAM;AAAA,UACN,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,uBAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,kBACjC;AAAA,gBACd,KAAK;AAAA,gBACL;AAAA,kBACE,SAASA;AAAA,oBACP;AAAA,kBACF;AAAA,kBACA,UAAUA,GAAE,gDAAgD;AAAA,gBAC9D;AAAA,cACF;AAAA,kBACgB,wBAAI,KAAK,SAAS,EAAE,cAA0B;AAAA,gBAC5D;AAAA,gBACA;AAAA,kBACE,GAAG;AAAA,kBACH,UAAU,CAAC,MAAM;AACf,0BAAM,SAAS,CAAC;AAChB,yBAAK,SAAS,yBAAyB,EAAE;AAAA,kBAC3C;AAAA,kBACA,SAAS,qBAAqB;AAAA,kBAC9B,aAAa,qBAAqB;AAAA,kBAClC,qBAAqB,qBAAqB;AAAA,kBAC1C,UAAU,qBAAqB;AAAA,gBACjC;AAAA,cACF,EAAE,CAAC;AAAA,kBACa,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,YAC3C,EAAE,CAAC;AAAA,UACL;AAAA,QACF;AAAA,MACF;AAAA,UACgB;AAAA,QACd,KAAK;AAAA,QACL;AAAA,UACE,SAAS,KAAK;AAAA,UACd,MAAM;AAAA,UACN,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,uBAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,kBACjC,wBAAI,KAAK,OAAO,EAAE,UAAUA;AAAA,gBAC1C;AAAA,cACF,EAAE,CAAC;AAAA,kBACa,wBAAI,KAAK,SAAS,EAAE,cAA0B;AAAA,gBAC5D;AAAA,gBACA;AAAA,kBACE,GAAG;AAAA,kBACH,eAAe,MAAM;AAAA,kBACrB,UAAU,CAAC;AAAA,kBACX,KAAK;AAAA,gBACP;AAAA,oBACgB,wBAAI,OAAO,SAAS,EAAE,KAAK,MAAM,KAAK,cAA0B,wBAAI,OAAO,OAAO,CAAC,CAAC,EAAE,CAAC;AAAA,oBACvF,wBAAI,OAAO,SAAS,EAAE,UAAU,yEAA4B,OAAO,CAAC,OAAO,CAAC,CAAC,GAAG,cAAc,UAAU,IAAI,CAAC,eAA2B,wBAAI,OAAO,MAAM,EAAE,OAAO,OAAO,IAAI,UAAU,OAAO,QAAQ,OAAO,GAAG,GAAG,OAAO,EAAE,GAAG,CAAC;AAAA,cAClP,EAAE,CAAC;AAAA,kBACa,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,YAC3C,EAAE,CAAC;AAAA,UACL;AAAA,QACF;AAAA,MACF;AAAA,IACF,EAAE,CAAC;AAAA,QACa,wBAAI,SAAS,CAAC,CAAC;AAAA,QACf;AAAA,MACd;AAAA,MACA;AAAA,QACE,SAAS,KAAK;AAAA,QACd,MAAM;AAAA,QACN,OAAOA,GAAE,2DAA2D;AAAA,QACpE,aAAaA;AAAA,UACX;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF,EAAE,CAAC,EAAE,CAAC;AACR;AAMA,IAAI,kCAAkC,CAAC;AAAA,EACrC;AAAA,EACA;AACF,MAAM;AACJ,QAAM,WAAW,SAAS;AAC1B,QAAM,EAAE,WAAW,UAAU,IAAI,gBAAgB;AACjD,QAAM,CAAC,eAAe,gBAAgB,QAAI,wBAAS,IAAI;AACvD,QAAM,+BAA+B,CAAC,SAAS;AAC7C,cAAU,qCAAqC,IAAI;AACnD,qBAAiB,IAAI;AAAA,EACvB;AACA,QAAM,gCAAgC,MAAM;AAC1C,cAAU,qCAAqC,KAAK;AACpD,qBAAiB,IAAI;AAAA,EACvB;AACA,QAAM;AAAA,IACJ;AAAA,IACA,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT,IAAI,SAAS;AACb,QAAM,iBAAa;AAAA,IACjB,MAAG;AApVP;AAoVU,mDAAO,yBAAP,mBAA6B,IAAI,CAAC,MAAM,EAAE,mBAAkB,CAAC;AAAA;AAAA,IACnE,CAAC,KAAK;AAAA,EACR;AACA,QAAM;AAAA,IACJ;AAAA,IACA,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT,IAAI,WAAW;AAAA,IACb,QAAQ;AAAA,IACR,OAAO;AAAA,EACT,CAAC;AACD,QAAM,EAAE,mBAAmB,iBAAiB,IAAI,oBAAoB,CAAC,CAAC;AACtE,QAAM,EAAE,iBAAiB,IAAI,cAAc;AAC3C,QAAM,OAAO,SAAS,EAAE,SAAS,KAAK,SAAS,MAAM,OAAO,CAAC;AAC7D,QAAM,UAAU,8BAA8B;AAAA,IAC5C;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,QAAM,YAAY,kBAAkB,CAAC,SAAS,oBAAoB,CAAC;AACnE,QAAM,WAAO;AAAA,IACX,MAAM,CAAC,CAAC,GAAG,cAAc,CAAC,GAAG,GAAG,WAAW,CAAC,CAAC,CAAC;AAAA,IAC9C,CAAC,YAAY,OAAO;AAAA,EACtB;AACA,+BAAU,MAAM;AACd,QAAI,CAAC,aAAa,UAAU;AAC1B,UAAI,WAAW,SAAS,GAAG;AACzB,mBAAW,QAAQ,CAAC,aAAa;AAC/B,eAAK,SAAS,mBAAmB,QAAQ,IAAI,GAAG;AAAA,QAClD,CAAC;AAAA,MACH;AACA,UAAI,QAAQ,SAAS,GAAG;AACtB,gBAAQ,QAAQ,CAAC,WAAW;AAC1B,eAAK,SAAS,iBAAiB,OAAO,EAAE,IAAI,GAAG;AAAA,QACjD,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF,GAAG,CAAC,WAAW,QAAQ,CAAC;AACxB,MAAI,cAAc;AAChB,UAAM;AAAA,EACR;AACA,MAAI,gBAAgB;AAClB,UAAM;AAAA,EACR;AACA,aAAuB,oBAAAC;AAAA,IACrB;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,sBAAsB,CAAC,SAAS;AAC9B,YAAI,CAAC,MAAM;AACT,2BAAiB,IAAI;AAAA,QACvB;AAAA,MACF;AAAA,MACA,cAA0B,oBAAAA;AAAA,QACxB;AAAA,QACA;AAAA,UACE;AAAA,UACA;AAAA,UACA,cAA0B,oBAAAC,MAAM,OAAO,EAAE,WAAW,oDAAoD,UAAU;AAAA,gBAChG,oBAAAD;AAAA,cACd;AAAA,cACA;AAAA,gBACE;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA,OAAO;AAAA,gBACP,iBAAiB,CAAC,YAAY,iBAAiB,CAAC,OAAO;AAAA,gBACvD,qBAAqB,UAAU,mCAAmC;AAAA,cACpE;AAAA,YACF;AAAA,YACA,qBAAiC,oBAAAA,KAAK,sBAAsB,EAAE,MAAM,eAAe,SAAS,SAAS,CAAC;AAAA,UACxG,EAAE,CAAC;AAAA,QACL;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAIA,IAAI,oCAAoC,EAAE,OAAO;AAAA,EAC/C,MAAM,EAAE,OAAO,EAAE,IAAI,CAAC;AAAA,EACtB,YAAY,EAAE,WAAW,uBAAuB;AAAA,EAChD,kBAAkB,EAAE,QAAQ;AAAA,EAC5B,qBAAqB,EAAE,OAAO,EAAE,IAAI,CAAC;AAAA,EACrC,aAAa,EAAE,OAAO,EAAE,IAAI,CAAC;AAAA,EAC7B,uBAAuB,EAAE,OAAO,EAAE,IAAI,CAAC;AACzC,CAAC;AACD,IAAI,uCAAuC,EAAE,OAAO;AAAA,EAClD,2BAA2B,EAAE;AAAA,IAC3B,EAAE,OAAO;AAAA,IACT,EAAE,MAAM,sBAAsB,EAAE,SAAS;AAAA,EAC3C;AAAA,EACA,6BAA6B,EAAE;AAAA,IAC7B,EAAE,OAAO;AAAA,IACT,EAAE,MAAM,sBAAsB,EAAE,SAAS;AAAA,EAC3C;AACF,CAAC;AACD,IAAI,6BAA6B,EAAE,OAAO;AAAA,EACxC,eAAe,EAAE,OAAO,EAAE,OAAO,GAAG,EAAE,OAAO,EAAE,SAAS,CAAC;AAAA,EACzD,iBAAiB,EAAE,OAAO,EAAE,OAAO,GAAG,EAAE,OAAO,EAAE,SAAS,CAAC;AAC7D,CAAC,EAAE,MAAM,iCAAiC,EAAE,MAAM,oCAAoC;AAItF,SAAS,0BAA0B;AAAA,EACjC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAAG;AApcH;AAqcE,QAAM,CAAC,WAAW,YAAY,QAAI,aAAAE;AAAA,IAAU;AAAA;AAAA,EAAuB;AACnE,QAAM,CAAC,cAAc,eAAe,QAAI,aAAAA,UAAU,KAAK;AACvD,QAAM,EAAE,GAAAH,GAAE,IAAI,eAAgB;AAC9B,QAAM,EAAE,cAAc,IAAI,cAAc;AACxC,QAAM,OAAO,QAAQ;AAAA,IACnB,eAAe;AAAA,MACb,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,kBAAkB;AAAA,MAClB,qBAAqB;AAAA,MACrB,aAAa;AAAA,MACb,uBAAuB;AAAA,MACvB,eAAe,CAAC;AAAA,MAChB,iBAAiB,CAAC;AAAA,MAClB,2BAA2B,CAAC;AAAA,MAC5B,6BAA6B,CAAC;AAAA,IAChC;AAAA,IACA,UAAU,EAAY,0BAA0B;AAAA,EAClD,CAAC;AACD,QAAM,qBAAqB,SAAU;AAAA,IACnC,SAAS,KAAK;AAAA,IACd,MAAM;AAAA,EACR,CAAC;AACD,QAAM,EAAE,qBAAqB,2BAA2B,IAAI,8BAA8B,oBAAoB;AAAA,IAC5G,SAAS,CAAC,CAAC;AAAA,EACb,CAAC;AACD,QAAM,wBAAwB,KAAK,MAAM,YAAY,MAAM;AAC3D,QAAM,EAAE,aAAa,WAAW,UAAU,IAAI,yBAAyB;AACvE,QAAM,eAAe,KAAK,aAAa,OAAO,SAAS;AACrD,UAAM,iBAAiB,OAAO,QAAQ,KAAK,eAAe,EAAE,IAAI,CAAC,CAAC,MAAM,KAAK,MAAM;AACjF,UAAI,CAAC,OAAO;AACV,eAAO;AAAA,MACT;AACA,aAAO;AAAA,QACL,eAAe;AAAA,QACf,QAAQ,WAAW,KAAK;AAAA,MAC1B;AAAA,IACF,CAAC,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;AACpB,UAAM,eAAe,OAAO,QAAQ,KAAK,aAAa,EAAE,IAAI,CAAC,CAAC,WAAW,KAAK,MAAM;AAClF,UAAI,CAAC,OAAO;AACV,eAAO;AAAA,MACT;AACA,aAAO;AAAA,QACL;AAAA,QACA,QAAQ,WAAW,KAAK;AAAA,MAC1B;AAAA,IACF,CAAC,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;AACpB,UAAM,0BAA0B,OAAO;AAAA,MACrC,KAAK;AAAA,IACP,EAAE,QAAQ,CAAC,CAAC,WAAW,KAAK,MAAM;AAChC,YAAM,UAAS,+BAAO,IAAI,CAAC,UAAU;AAAA,QACnC;AAAA,QACA,QAAQ,WAAW,KAAK,MAAM;AAAA,QAC9B,OAAO,8BAA8B,IAAI;AAAA,MAC3C,QAAO,CAAC;AACR,aAAO,iCAAQ,OAAO;AAAA,IACxB,CAAC;AACD,UAAM,4BAA4B,OAAO;AAAA,MACvC,KAAK;AAAA,IACP,EAAE,QAAQ,CAAC,CAAC,eAAe,KAAK,MAAM;AACpC,YAAM,UAAS,+BAAO,IAAI,CAAC,UAAU;AAAA,QACnC;AAAA,QACA,QAAQ,WAAW,KAAK,MAAM;AAAA,QAC9B,OAAO,8BAA8B,IAAI;AAAA,MAC3C,QAAO,CAAC;AACR,aAAO,iCAAQ,OAAO;AAAA,IACxB,CAAC;AACD,UAAM,YAAY;AAAA,MAChB,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,IACL;AACA,UAAM,wBAAwB,yEAA4B;AAAA,MACxD,CAAC,OAAO,GAAG,OAAO,KAAK;AAAA;AAEzB,UAAM;AAAA,MACJ;AAAA,QACE,MAAM,KAAK;AAAA,QACX,YAAY,KAAK;AAAA,QACjB,iBAAiB,KAAK;AAAA,QACtB,qBAAqB,KAAK;AAAA,QAC1B,aAAa,KAAK;AAAA,QAClB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,OAAO;AAAA,UACL;AAAA;AAAA,YAEE,OAAO,WAAW,SAAS;AAAA,YAC3B,WAAW;AAAA,YACX,UAAU;AAAA,UACZ;AAAA,UACA;AAAA;AAAA,YAEE,OAAO,KAAK,mBAAmB,SAAS;AAAA,YACxC,WAAW;AAAA,YACX,UAAU;AAAA,UACZ;AAAA,QACF;AAAA,QACA,MAAM;AAAA;AAAA,UAEJ,OAAO;AAAA,UACP,aAAa;AAAA,UACb,MAAM;AAAA,QACR;AAAA,MACF;AAAA,MACA;AAAA,QACE,WAAW,CAAC,EAAE,gBAAgB,MAAM;AAClC,gBAAM;AAAA,YACJA;AAAA,cACE,yCAAyC,WAAW,YAAY,UAAU;AAAA,cAC1E,EAAE,MAAM,gBAAgB,KAAK;AAAA,YAC/B;AAAA,UACF;AACA,wBAAc,uBAAuB,UAAU,EAAE;AAAA,QACnD;AAAA,QACA,SAAS,CAAC,MAAM;AACd,gBAAM,MAAM,EAAE,OAAO;AAAA,QACvB;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AACD,QAAM,cAAc,CAAC,QAAQ;AAC3B,QAAI,QAAQ,WAAyB;AACnC,WAAK,YAAY;AACjB,YAAM,SAAS,kCAAkC,UAAU;AAAA,QACzD,GAAG,KAAK,UAAU;AAAA,MACpB,CAAC;AACD,UAAI,CAAC,OAAO,SAAS;AACnB,cAAM,CAAC,YAAY,GAAG,IAAI,IAAI,OAAO,MAAM;AAC3C,mBAAW,SAAS,MAAM;AACxB,gBAAM,QAAQ,MAAM,KAAK,KAAK,GAAG;AACjC,eAAK,SAAS,OAAO;AAAA,YACnB,SAAS,MAAM;AAAA,YACf,MAAM,MAAM;AAAA,UACd,CAAC;AAAA,QACH;AACA,aAAK;AAAA,UACH,WAAW,KAAK,KAAK,GAAG;AAAA,UACxB;AAAA,YACE,SAAS,WAAW;AAAA,YACpB,MAAM,WAAW;AAAA,UACnB;AAAA,UACA;AAAA,YACE,aAAa;AAAA,UACf;AAAA,QACF;AACA,wBAAgB,KAAK;AACrB;AAAA,MACF;AACA,sBAAgB,IAAI;AAAA,IACtB;AACA,iBAAa,GAAG;AAAA,EAClB;AACA,QAAM,iBAAe,UAAK,cAAc,iBAAiB,MAApC,mBAAuC,cAAW,UAAK,cAAc,eAAe,MAAlC,mBAAqC,YAAW,cAAc,YAA0B,gBAAgB;AAC/K,QAAM,gBAAgB,eAAe,cAAc;AACnD,aAAuB,oBAAAI,KAAK,gBAAgB,MAAM,EAAE,MAAM,cAA0B,oBAAAA;AAAA,IAClF;AAAA,IACA;AAAA,MACE,WAAW;AAAA,MACX,UAAU;AAAA,MACV,WAAW,CAAC,MAAM;AAChB,cAAM,aAAa,EAAE,QAAQ;AAC7B,cAAM,oBAAoB,EAAE,WAAW,EAAE;AACzC,cAAM,0BAA0B,cAAc,aAA2B,CAAC;AAC1E,YAAI,CAAC,YAAY;AACf;AAAA,QACF;AACA,UAAE,eAAe;AACjB,YAAI,CAAC,mBAAmB;AACtB;AAAA,QACF;AACA,YAAI,yBAAyB;AAC3B,YAAE,gBAAgB;AAClB;AAAA,YAAY;AAAA;AAAA,UAAuB;AACnC;AAAA,QACF;AACA,qBAAa;AAAA,MACf;AAAA,MACA,cAA0B,oBAAAC;AAAA,QACxB;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,WAAW;AAAA,UACX,eAAe,CAAC,QAAQ,YAAY,GAAG;AAAA,UACvC,UAAU;AAAA,gBACQ,oBAAAD,KAAK,gBAAgB,QAAQ,EAAE,cAA0B,oBAAAC,MAAM,aAAa,MAAM,EAAE,WAAW,4DAA4D,UAAU;AAAA,kBACnK,oBAAAD;AAAA,gBACd,aAAa;AAAA,gBACb;AAAA,kBACE,OAAO;AAAA,kBACP,QAAQ;AAAA,kBACR,WAAW;AAAA,kBACX,cAA0B,oBAAAA,KAAK,QAAQ,EAAE,WAAW,sEAAsE,UAAUJ,GAAE,oDAAoD,EAAE,CAAC;AAAA,gBAC/L;AAAA,cACF;AAAA,cACA,CAAC,6BAAyC,oBAAAI;AAAA,gBACxC,aAAa;AAAA,gBACb;AAAA,kBACE,OAAO;AAAA,kBACP,QAAQ;AAAA,kBACR,WAAW;AAAA,kBACX,cAA0B,oBAAAA,KAAK,QAAQ,EAAE,WAAW,0DAA0D,UAAUJ,GAAE,mDAAmD,EAAE,CAAC;AAAA,gBAClL;AAAA,cACF;AAAA,YACF,EAAE,CAAC,EAAE,CAAC;AAAA,gBACU,oBAAAK,MAAM,gBAAgB,MAAM,EAAE,WAAW,6BAA6B,UAAU;AAAA,kBAC9E,oBAAAD;AAAA,gBACd,aAAa;AAAA,gBACb;AAAA,kBACE,OAAO;AAAA,kBACP,WAAW;AAAA,kBACX,cAA0B,oBAAAA;AAAA,oBACxB;AAAA,oBACA;AAAA,sBACE;AAAA,sBACA;AAAA,sBACA;AAAA,sBACA;AAAA,sBACA;AAAA,sBACA,4BAA4B,8BAA8B,CAAC;AAAA,sBAC3D;AAAA,oBACF;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAAA,kBACgB,oBAAAA,KAAK,aAAa,SAAS,EAAE,OAAO,WAAyB,WAAW,aAAa,cAA0B,oBAAAA,KAAK,iCAAiC,EAAE,MAAM,KAAK,CAAC,EAAE,CAAC;AAAA,YACxL,EAAE,CAAC;AAAA,gBACa,oBAAAA,KAAK,gBAAgB,QAAQ,EAAE,cAA0B,oBAAAC,MAAM,OAAO,EAAE,WAAW,yCAAyC,UAAU;AAAA,kBACpI,oBAAAD,KAAK,gBAAgB,OAAO,EAAE,SAAS,MAAM,cAA0B,oBAAAA,KAAK,QAAQ,EAAE,SAAS,aAAa,MAAM,SAAS,UAAUJ,GAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;AAAA,cAC7K,cAAc,aAA2B,4BAAwC,oBAAAI;AAAA,gBAC/E;AAAA,gBACA;AAAA,kBACE,MAAM;AAAA,kBACN,WAAW;AAAA,kBACX;AAAA,kBACA,MAAM;AAAA,kBACN,UAAUJ,GAAE,cAAc;AAAA,gBAC5B;AAAA,gBACA;AAAA,cACF,QAAoB,oBAAAI;AAAA,gBAClB;AAAA,gBACA;AAAA,kBACE,MAAM;AAAA,kBACN,WAAW;AAAA,kBACX;AAAA,kBACA,SAAS,MAAM;AAAA,oBAAY;AAAA;AAAA,kBAAuB;AAAA,kBAClD,MAAM;AAAA,kBACN,UAAUJ,GAAE,kBAAkB;AAAA,gBAChC;AAAA,gBACA;AAAA,cACF;AAAA,YACF,EAAE,CAAC,EAAE,CAAC;AAAA,UACR;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;AAGA,IAAI,oCAAoC;AAIxC,SAAS,0CAA0C;AA9sBnD;AA+sBE,QAAM,EAAE,aAAa,SAAS,QAAQ,IAAI,UAAU;AACpD,QAAM,CAAC,YAAY,IAAI,gBAAgB;AACvC,QAAM,WAAW,aAAa,IAAI,WAAW;AAC7C,QAAM,EAAE,gBAAgB,WAAW,YAAY,SAAS,MAAM,IAAI,iBAAiB,aAAa;AAAA,IAC9F,QAAQ;AAAA,EACV,CAAC;AACD,QAAM,kBAAiB,sDAAgB,qBAAhB,mBAAkC;AAAA,IACvD,CAAC,MAAM,EAAE,OAAO;AAAA;AAElB,MAAI,CAAC,aAAa,CAAC,cAAc,CAAC,gBAAgB;AAChD,UAAM;AAAA,MACJ,EAAE,SAAS,2BAA2B,OAAO,iBAAiB;AAAA,MAC9D;AAAA,IACF;AAAA,EACF;AACA,QAAM,QAAO,sDAAgB,kBAAhB,mBAA+B,KAAK,CAAC,OAAO,GAAG,OAAO;AACnE,MAAI,CAAC,aAAa,CAAC,cAAc,CAAC,MAAM;AACtC,UAAM;AAAA,MACJ,EAAE,SAAS,wBAAwB,OAAO,iBAAiB;AAAA,MAC3D;AAAA,IACF;AAAA,EACF;AACA,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,oBAAAM,KAAK,iBAAiB,EAAE,MAAM,uBAAuB,WAAW,IAAI,UAAU,YAAwB,oBAAAA;AAAA,IAC3H;AAAA,IACA;AAAA,MACE;AAAA,MACA;AAAA,MACA,YAAY;AAAA,MACZ,MAAM,eAAe;AAAA,IACvB;AAAA,EACF,EAAE,CAAC;AACL;", "names": ["import_react", "import_jsx_runtime", "import_jsx_runtime", "t", "jsx2", "jsxs2", "useState2", "jsx3", "jsxs3", "jsx4"]}