// node_modules/@medusajs/dashboard/dist/chunk-PYIO3TDQ.mjs
var ShippingOptionPriceType = ((ShippingOptionPriceType2) => {
  ShippingOptionPriceType2["FlatRate"] = "flat";
  ShippingOptionPriceType2["Calculated"] = "calculated";
  return ShippingOptionPriceType2;
})(ShippingOptionPriceType || {});
var GEO_ZONE_STACKED_MODAL_ID = "geo-zone";
var CONDITIONAL_PRICES_STACKED_MODAL_ID = "conditional-prices";
var ITEM_TOTAL_ATTRIBUTE = "item_total";
var REGION_ID_ATTRIBUTE = "region_id";

export {
  ShippingOptionPriceType,
  GEO_ZONE_STACKED_MODAL_ID,
  CONDITIONAL_PRICES_STACKED_MODAL_ID,
  ITEM_TOTAL_ATTRIBUTE,
  REGION_ID_ATTRIBUTE
};
//# sourceMappingURL=chunk-S4XCFSZC.js.map
