import {
  require_lodash,
  useSelectedParams
} from "./chunk-QX6SXRUW.js";
import {
  require_ListCache,
  require_Map,
  require_MapCache,
  require_eq,
  require_getNative,
  require_isArray,
  require_isFunction,
  require_isIndex,
  require_toSource
} from "./chunk-7ANVLPZR.js";
import {
  useDate
} from "./chunk-2E2FUO6N.js";
import {
  we
} from "./chunk-DL4QDYPT.js";
import {
  require_Symbol,
  require_baseGetTag,
  require_freeGlobal,
  require_isObjectLike,
  require_root
} from "./chunk-NV2N3EWM.js";
import {
  t
} from "./chunk-MPXR7HT5.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import {
  useSearchParams
} from "./chunk-T7YBVUWZ.js";
import {
  <PERSON><PERSON>,
  Check<PERSON>ini,
  DatePicker,
  EllipseMiniSolid,
  Input,
  Label,
  Text,
  XMarkMini,
  clx,
  dist_exports5 as dist_exports,
  dist_exports6 as dist_exports2
} from "./chunk-YP2LLXWB.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __commonJS,
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/lodash/_stackClear.js
var require_stackClear = __commonJS({
  "node_modules/lodash/_stackClear.js"(exports, module) {
    var ListCache = require_ListCache();
    function stackClear() {
      this.__data__ = new ListCache();
      this.size = 0;
    }
    module.exports = stackClear;
  }
});

// node_modules/lodash/_stackDelete.js
var require_stackDelete = __commonJS({
  "node_modules/lodash/_stackDelete.js"(exports, module) {
    function stackDelete(key) {
      var data = this.__data__, result = data["delete"](key);
      this.size = data.size;
      return result;
    }
    module.exports = stackDelete;
  }
});

// node_modules/lodash/_stackGet.js
var require_stackGet = __commonJS({
  "node_modules/lodash/_stackGet.js"(exports, module) {
    function stackGet(key) {
      return this.__data__.get(key);
    }
    module.exports = stackGet;
  }
});

// node_modules/lodash/_stackHas.js
var require_stackHas = __commonJS({
  "node_modules/lodash/_stackHas.js"(exports, module) {
    function stackHas(key) {
      return this.__data__.has(key);
    }
    module.exports = stackHas;
  }
});

// node_modules/lodash/_stackSet.js
var require_stackSet = __commonJS({
  "node_modules/lodash/_stackSet.js"(exports, module) {
    var ListCache = require_ListCache();
    var Map = require_Map();
    var MapCache = require_MapCache();
    var LARGE_ARRAY_SIZE = 200;
    function stackSet(key, value) {
      var data = this.__data__;
      if (data instanceof ListCache) {
        var pairs = data.__data__;
        if (!Map || pairs.length < LARGE_ARRAY_SIZE - 1) {
          pairs.push([key, value]);
          this.size = ++data.size;
          return this;
        }
        data = this.__data__ = new MapCache(pairs);
      }
      data.set(key, value);
      this.size = data.size;
      return this;
    }
    module.exports = stackSet;
  }
});

// node_modules/lodash/_Stack.js
var require_Stack = __commonJS({
  "node_modules/lodash/_Stack.js"(exports, module) {
    var ListCache = require_ListCache();
    var stackClear = require_stackClear();
    var stackDelete = require_stackDelete();
    var stackGet = require_stackGet();
    var stackHas = require_stackHas();
    var stackSet = require_stackSet();
    function Stack(entries) {
      var data = this.__data__ = new ListCache(entries);
      this.size = data.size;
    }
    Stack.prototype.clear = stackClear;
    Stack.prototype["delete"] = stackDelete;
    Stack.prototype.get = stackGet;
    Stack.prototype.has = stackHas;
    Stack.prototype.set = stackSet;
    module.exports = Stack;
  }
});

// node_modules/lodash/_setCacheAdd.js
var require_setCacheAdd = __commonJS({
  "node_modules/lodash/_setCacheAdd.js"(exports, module) {
    var HASH_UNDEFINED = "__lodash_hash_undefined__";
    function setCacheAdd(value) {
      this.__data__.set(value, HASH_UNDEFINED);
      return this;
    }
    module.exports = setCacheAdd;
  }
});

// node_modules/lodash/_setCacheHas.js
var require_setCacheHas = __commonJS({
  "node_modules/lodash/_setCacheHas.js"(exports, module) {
    function setCacheHas(value) {
      return this.__data__.has(value);
    }
    module.exports = setCacheHas;
  }
});

// node_modules/lodash/_SetCache.js
var require_SetCache = __commonJS({
  "node_modules/lodash/_SetCache.js"(exports, module) {
    var MapCache = require_MapCache();
    var setCacheAdd = require_setCacheAdd();
    var setCacheHas = require_setCacheHas();
    function SetCache(values) {
      var index = -1, length = values == null ? 0 : values.length;
      this.__data__ = new MapCache();
      while (++index < length) {
        this.add(values[index]);
      }
    }
    SetCache.prototype.add = SetCache.prototype.push = setCacheAdd;
    SetCache.prototype.has = setCacheHas;
    module.exports = SetCache;
  }
});

// node_modules/lodash/_arraySome.js
var require_arraySome = __commonJS({
  "node_modules/lodash/_arraySome.js"(exports, module) {
    function arraySome(array, predicate) {
      var index = -1, length = array == null ? 0 : array.length;
      while (++index < length) {
        if (predicate(array[index], index, array)) {
          return true;
        }
      }
      return false;
    }
    module.exports = arraySome;
  }
});

// node_modules/lodash/_cacheHas.js
var require_cacheHas = __commonJS({
  "node_modules/lodash/_cacheHas.js"(exports, module) {
    function cacheHas(cache, key) {
      return cache.has(key);
    }
    module.exports = cacheHas;
  }
});

// node_modules/lodash/_equalArrays.js
var require_equalArrays = __commonJS({
  "node_modules/lodash/_equalArrays.js"(exports, module) {
    var SetCache = require_SetCache();
    var arraySome = require_arraySome();
    var cacheHas = require_cacheHas();
    var COMPARE_PARTIAL_FLAG = 1;
    var COMPARE_UNORDERED_FLAG = 2;
    function equalArrays(array, other, bitmask, customizer, equalFunc, stack) {
      var isPartial = bitmask & COMPARE_PARTIAL_FLAG, arrLength = array.length, othLength = other.length;
      if (arrLength != othLength && !(isPartial && othLength > arrLength)) {
        return false;
      }
      var arrStacked = stack.get(array);
      var othStacked = stack.get(other);
      if (arrStacked && othStacked) {
        return arrStacked == other && othStacked == array;
      }
      var index = -1, result = true, seen = bitmask & COMPARE_UNORDERED_FLAG ? new SetCache() : void 0;
      stack.set(array, other);
      stack.set(other, array);
      while (++index < arrLength) {
        var arrValue = array[index], othValue = other[index];
        if (customizer) {
          var compared = isPartial ? customizer(othValue, arrValue, index, other, array, stack) : customizer(arrValue, othValue, index, array, other, stack);
        }
        if (compared !== void 0) {
          if (compared) {
            continue;
          }
          result = false;
          break;
        }
        if (seen) {
          if (!arraySome(other, function(othValue2, othIndex) {
            if (!cacheHas(seen, othIndex) && (arrValue === othValue2 || equalFunc(arrValue, othValue2, bitmask, customizer, stack))) {
              return seen.push(othIndex);
            }
          })) {
            result = false;
            break;
          }
        } else if (!(arrValue === othValue || equalFunc(arrValue, othValue, bitmask, customizer, stack))) {
          result = false;
          break;
        }
      }
      stack["delete"](array);
      stack["delete"](other);
      return result;
    }
    module.exports = equalArrays;
  }
});

// node_modules/lodash/_Uint8Array.js
var require_Uint8Array = __commonJS({
  "node_modules/lodash/_Uint8Array.js"(exports, module) {
    var root = require_root();
    var Uint8Array = root.Uint8Array;
    module.exports = Uint8Array;
  }
});

// node_modules/lodash/_mapToArray.js
var require_mapToArray = __commonJS({
  "node_modules/lodash/_mapToArray.js"(exports, module) {
    function mapToArray(map) {
      var index = -1, result = Array(map.size);
      map.forEach(function(value, key) {
        result[++index] = [key, value];
      });
      return result;
    }
    module.exports = mapToArray;
  }
});

// node_modules/lodash/_setToArray.js
var require_setToArray = __commonJS({
  "node_modules/lodash/_setToArray.js"(exports, module) {
    function setToArray(set) {
      var index = -1, result = Array(set.size);
      set.forEach(function(value) {
        result[++index] = value;
      });
      return result;
    }
    module.exports = setToArray;
  }
});

// node_modules/lodash/_equalByTag.js
var require_equalByTag = __commonJS({
  "node_modules/lodash/_equalByTag.js"(exports, module) {
    var Symbol = require_Symbol();
    var Uint8Array = require_Uint8Array();
    var eq = require_eq();
    var equalArrays = require_equalArrays();
    var mapToArray = require_mapToArray();
    var setToArray = require_setToArray();
    var COMPARE_PARTIAL_FLAG = 1;
    var COMPARE_UNORDERED_FLAG = 2;
    var boolTag = "[object Boolean]";
    var dateTag = "[object Date]";
    var errorTag = "[object Error]";
    var mapTag = "[object Map]";
    var numberTag = "[object Number]";
    var regexpTag = "[object RegExp]";
    var setTag = "[object Set]";
    var stringTag = "[object String]";
    var symbolTag = "[object Symbol]";
    var arrayBufferTag = "[object ArrayBuffer]";
    var dataViewTag = "[object DataView]";
    var symbolProto = Symbol ? Symbol.prototype : void 0;
    var symbolValueOf = symbolProto ? symbolProto.valueOf : void 0;
    function equalByTag(object, other, tag, bitmask, customizer, equalFunc, stack) {
      switch (tag) {
        case dataViewTag:
          if (object.byteLength != other.byteLength || object.byteOffset != other.byteOffset) {
            return false;
          }
          object = object.buffer;
          other = other.buffer;
        case arrayBufferTag:
          if (object.byteLength != other.byteLength || !equalFunc(new Uint8Array(object), new Uint8Array(other))) {
            return false;
          }
          return true;
        case boolTag:
        case dateTag:
        case numberTag:
          return eq(+object, +other);
        case errorTag:
          return object.name == other.name && object.message == other.message;
        case regexpTag:
        case stringTag:
          return object == other + "";
        case mapTag:
          var convert = mapToArray;
        case setTag:
          var isPartial = bitmask & COMPARE_PARTIAL_FLAG;
          convert || (convert = setToArray);
          if (object.size != other.size && !isPartial) {
            return false;
          }
          var stacked = stack.get(object);
          if (stacked) {
            return stacked == other;
          }
          bitmask |= COMPARE_UNORDERED_FLAG;
          stack.set(object, other);
          var result = equalArrays(convert(object), convert(other), bitmask, customizer, equalFunc, stack);
          stack["delete"](object);
          return result;
        case symbolTag:
          if (symbolValueOf) {
            return symbolValueOf.call(object) == symbolValueOf.call(other);
          }
      }
      return false;
    }
    module.exports = equalByTag;
  }
});

// node_modules/lodash/_arrayPush.js
var require_arrayPush = __commonJS({
  "node_modules/lodash/_arrayPush.js"(exports, module) {
    function arrayPush(array, values) {
      var index = -1, length = values.length, offset = array.length;
      while (++index < length) {
        array[offset + index] = values[index];
      }
      return array;
    }
    module.exports = arrayPush;
  }
});

// node_modules/lodash/_baseGetAllKeys.js
var require_baseGetAllKeys = __commonJS({
  "node_modules/lodash/_baseGetAllKeys.js"(exports, module) {
    var arrayPush = require_arrayPush();
    var isArray = require_isArray();
    function baseGetAllKeys(object, keysFunc, symbolsFunc) {
      var result = keysFunc(object);
      return isArray(object) ? result : arrayPush(result, symbolsFunc(object));
    }
    module.exports = baseGetAllKeys;
  }
});

// node_modules/lodash/_arrayFilter.js
var require_arrayFilter = __commonJS({
  "node_modules/lodash/_arrayFilter.js"(exports, module) {
    function arrayFilter(array, predicate) {
      var index = -1, length = array == null ? 0 : array.length, resIndex = 0, result = [];
      while (++index < length) {
        var value = array[index];
        if (predicate(value, index, array)) {
          result[resIndex++] = value;
        }
      }
      return result;
    }
    module.exports = arrayFilter;
  }
});

// node_modules/lodash/stubArray.js
var require_stubArray = __commonJS({
  "node_modules/lodash/stubArray.js"(exports, module) {
    function stubArray() {
      return [];
    }
    module.exports = stubArray;
  }
});

// node_modules/lodash/_getSymbols.js
var require_getSymbols = __commonJS({
  "node_modules/lodash/_getSymbols.js"(exports, module) {
    var arrayFilter = require_arrayFilter();
    var stubArray = require_stubArray();
    var objectProto = Object.prototype;
    var propertyIsEnumerable = objectProto.propertyIsEnumerable;
    var nativeGetSymbols = Object.getOwnPropertySymbols;
    var getSymbols = !nativeGetSymbols ? stubArray : function(object) {
      if (object == null) {
        return [];
      }
      object = Object(object);
      return arrayFilter(nativeGetSymbols(object), function(symbol) {
        return propertyIsEnumerable.call(object, symbol);
      });
    };
    module.exports = getSymbols;
  }
});

// node_modules/lodash/_baseTimes.js
var require_baseTimes = __commonJS({
  "node_modules/lodash/_baseTimes.js"(exports, module) {
    function baseTimes(n, iteratee) {
      var index = -1, result = Array(n);
      while (++index < n) {
        result[index] = iteratee(index);
      }
      return result;
    }
    module.exports = baseTimes;
  }
});

// node_modules/lodash/_baseIsArguments.js
var require_baseIsArguments = __commonJS({
  "node_modules/lodash/_baseIsArguments.js"(exports, module) {
    var baseGetTag = require_baseGetTag();
    var isObjectLike = require_isObjectLike();
    var argsTag = "[object Arguments]";
    function baseIsArguments(value) {
      return isObjectLike(value) && baseGetTag(value) == argsTag;
    }
    module.exports = baseIsArguments;
  }
});

// node_modules/lodash/isArguments.js
var require_isArguments = __commonJS({
  "node_modules/lodash/isArguments.js"(exports, module) {
    var baseIsArguments = require_baseIsArguments();
    var isObjectLike = require_isObjectLike();
    var objectProto = Object.prototype;
    var hasOwnProperty = objectProto.hasOwnProperty;
    var propertyIsEnumerable = objectProto.propertyIsEnumerable;
    var isArguments = baseIsArguments(/* @__PURE__ */ function() {
      return arguments;
    }()) ? baseIsArguments : function(value) {
      return isObjectLike(value) && hasOwnProperty.call(value, "callee") && !propertyIsEnumerable.call(value, "callee");
    };
    module.exports = isArguments;
  }
});

// node_modules/lodash/stubFalse.js
var require_stubFalse = __commonJS({
  "node_modules/lodash/stubFalse.js"(exports, module) {
    function stubFalse() {
      return false;
    }
    module.exports = stubFalse;
  }
});

// node_modules/lodash/isBuffer.js
var require_isBuffer = __commonJS({
  "node_modules/lodash/isBuffer.js"(exports, module) {
    var root = require_root();
    var stubFalse = require_stubFalse();
    var freeExports = typeof exports == "object" && exports && !exports.nodeType && exports;
    var freeModule = freeExports && typeof module == "object" && module && !module.nodeType && module;
    var moduleExports = freeModule && freeModule.exports === freeExports;
    var Buffer = moduleExports ? root.Buffer : void 0;
    var nativeIsBuffer = Buffer ? Buffer.isBuffer : void 0;
    var isBuffer = nativeIsBuffer || stubFalse;
    module.exports = isBuffer;
  }
});

// node_modules/lodash/isLength.js
var require_isLength = __commonJS({
  "node_modules/lodash/isLength.js"(exports, module) {
    var MAX_SAFE_INTEGER = 9007199254740991;
    function isLength(value) {
      return typeof value == "number" && value > -1 && value % 1 == 0 && value <= MAX_SAFE_INTEGER;
    }
    module.exports = isLength;
  }
});

// node_modules/lodash/_baseIsTypedArray.js
var require_baseIsTypedArray = __commonJS({
  "node_modules/lodash/_baseIsTypedArray.js"(exports, module) {
    var baseGetTag = require_baseGetTag();
    var isLength = require_isLength();
    var isObjectLike = require_isObjectLike();
    var argsTag = "[object Arguments]";
    var arrayTag = "[object Array]";
    var boolTag = "[object Boolean]";
    var dateTag = "[object Date]";
    var errorTag = "[object Error]";
    var funcTag = "[object Function]";
    var mapTag = "[object Map]";
    var numberTag = "[object Number]";
    var objectTag = "[object Object]";
    var regexpTag = "[object RegExp]";
    var setTag = "[object Set]";
    var stringTag = "[object String]";
    var weakMapTag = "[object WeakMap]";
    var arrayBufferTag = "[object ArrayBuffer]";
    var dataViewTag = "[object DataView]";
    var float32Tag = "[object Float32Array]";
    var float64Tag = "[object Float64Array]";
    var int8Tag = "[object Int8Array]";
    var int16Tag = "[object Int16Array]";
    var int32Tag = "[object Int32Array]";
    var uint8Tag = "[object Uint8Array]";
    var uint8ClampedTag = "[object Uint8ClampedArray]";
    var uint16Tag = "[object Uint16Array]";
    var uint32Tag = "[object Uint32Array]";
    var typedArrayTags = {};
    typedArrayTags[float32Tag] = typedArrayTags[float64Tag] = typedArrayTags[int8Tag] = typedArrayTags[int16Tag] = typedArrayTags[int32Tag] = typedArrayTags[uint8Tag] = typedArrayTags[uint8ClampedTag] = typedArrayTags[uint16Tag] = typedArrayTags[uint32Tag] = true;
    typedArrayTags[argsTag] = typedArrayTags[arrayTag] = typedArrayTags[arrayBufferTag] = typedArrayTags[boolTag] = typedArrayTags[dataViewTag] = typedArrayTags[dateTag] = typedArrayTags[errorTag] = typedArrayTags[funcTag] = typedArrayTags[mapTag] = typedArrayTags[numberTag] = typedArrayTags[objectTag] = typedArrayTags[regexpTag] = typedArrayTags[setTag] = typedArrayTags[stringTag] = typedArrayTags[weakMapTag] = false;
    function baseIsTypedArray(value) {
      return isObjectLike(value) && isLength(value.length) && !!typedArrayTags[baseGetTag(value)];
    }
    module.exports = baseIsTypedArray;
  }
});

// node_modules/lodash/_baseUnary.js
var require_baseUnary = __commonJS({
  "node_modules/lodash/_baseUnary.js"(exports, module) {
    function baseUnary(func) {
      return function(value) {
        return func(value);
      };
    }
    module.exports = baseUnary;
  }
});

// node_modules/lodash/_nodeUtil.js
var require_nodeUtil = __commonJS({
  "node_modules/lodash/_nodeUtil.js"(exports, module) {
    var freeGlobal = require_freeGlobal();
    var freeExports = typeof exports == "object" && exports && !exports.nodeType && exports;
    var freeModule = freeExports && typeof module == "object" && module && !module.nodeType && module;
    var moduleExports = freeModule && freeModule.exports === freeExports;
    var freeProcess = moduleExports && freeGlobal.process;
    var nodeUtil = function() {
      try {
        var types = freeModule && freeModule.require && freeModule.require("util").types;
        if (types) {
          return types;
        }
        return freeProcess && freeProcess.binding && freeProcess.binding("util");
      } catch (e) {
      }
    }();
    module.exports = nodeUtil;
  }
});

// node_modules/lodash/isTypedArray.js
var require_isTypedArray = __commonJS({
  "node_modules/lodash/isTypedArray.js"(exports, module) {
    var baseIsTypedArray = require_baseIsTypedArray();
    var baseUnary = require_baseUnary();
    var nodeUtil = require_nodeUtil();
    var nodeIsTypedArray = nodeUtil && nodeUtil.isTypedArray;
    var isTypedArray = nodeIsTypedArray ? baseUnary(nodeIsTypedArray) : baseIsTypedArray;
    module.exports = isTypedArray;
  }
});

// node_modules/lodash/_arrayLikeKeys.js
var require_arrayLikeKeys = __commonJS({
  "node_modules/lodash/_arrayLikeKeys.js"(exports, module) {
    var baseTimes = require_baseTimes();
    var isArguments = require_isArguments();
    var isArray = require_isArray();
    var isBuffer = require_isBuffer();
    var isIndex = require_isIndex();
    var isTypedArray = require_isTypedArray();
    var objectProto = Object.prototype;
    var hasOwnProperty = objectProto.hasOwnProperty;
    function arrayLikeKeys(value, inherited) {
      var isArr = isArray(value), isArg = !isArr && isArguments(value), isBuff = !isArr && !isArg && isBuffer(value), isType = !isArr && !isArg && !isBuff && isTypedArray(value), skipIndexes = isArr || isArg || isBuff || isType, result = skipIndexes ? baseTimes(value.length, String) : [], length = result.length;
      for (var key in value) {
        if ((inherited || hasOwnProperty.call(value, key)) && !(skipIndexes && // Safari 9 has enumerable `arguments.length` in strict mode.
        (key == "length" || // Node.js 0.10 has enumerable non-index properties on buffers.
        isBuff && (key == "offset" || key == "parent") || // PhantomJS 2 has enumerable non-index properties on typed arrays.
        isType && (key == "buffer" || key == "byteLength" || key == "byteOffset") || // Skip index properties.
        isIndex(key, length)))) {
          result.push(key);
        }
      }
      return result;
    }
    module.exports = arrayLikeKeys;
  }
});

// node_modules/lodash/_isPrototype.js
var require_isPrototype = __commonJS({
  "node_modules/lodash/_isPrototype.js"(exports, module) {
    var objectProto = Object.prototype;
    function isPrototype(value) {
      var Ctor = value && value.constructor, proto = typeof Ctor == "function" && Ctor.prototype || objectProto;
      return value === proto;
    }
    module.exports = isPrototype;
  }
});

// node_modules/lodash/_overArg.js
var require_overArg = __commonJS({
  "node_modules/lodash/_overArg.js"(exports, module) {
    function overArg(func, transform) {
      return function(arg) {
        return func(transform(arg));
      };
    }
    module.exports = overArg;
  }
});

// node_modules/lodash/_nativeKeys.js
var require_nativeKeys = __commonJS({
  "node_modules/lodash/_nativeKeys.js"(exports, module) {
    var overArg = require_overArg();
    var nativeKeys = overArg(Object.keys, Object);
    module.exports = nativeKeys;
  }
});

// node_modules/lodash/_baseKeys.js
var require_baseKeys = __commonJS({
  "node_modules/lodash/_baseKeys.js"(exports, module) {
    var isPrototype = require_isPrototype();
    var nativeKeys = require_nativeKeys();
    var objectProto = Object.prototype;
    var hasOwnProperty = objectProto.hasOwnProperty;
    function baseKeys(object) {
      if (!isPrototype(object)) {
        return nativeKeys(object);
      }
      var result = [];
      for (var key in Object(object)) {
        if (hasOwnProperty.call(object, key) && key != "constructor") {
          result.push(key);
        }
      }
      return result;
    }
    module.exports = baseKeys;
  }
});

// node_modules/lodash/isArrayLike.js
var require_isArrayLike = __commonJS({
  "node_modules/lodash/isArrayLike.js"(exports, module) {
    var isFunction = require_isFunction();
    var isLength = require_isLength();
    function isArrayLike(value) {
      return value != null && isLength(value.length) && !isFunction(value);
    }
    module.exports = isArrayLike;
  }
});

// node_modules/lodash/keys.js
var require_keys = __commonJS({
  "node_modules/lodash/keys.js"(exports, module) {
    var arrayLikeKeys = require_arrayLikeKeys();
    var baseKeys = require_baseKeys();
    var isArrayLike = require_isArrayLike();
    function keys(object) {
      return isArrayLike(object) ? arrayLikeKeys(object) : baseKeys(object);
    }
    module.exports = keys;
  }
});

// node_modules/lodash/_getAllKeys.js
var require_getAllKeys = __commonJS({
  "node_modules/lodash/_getAllKeys.js"(exports, module) {
    var baseGetAllKeys = require_baseGetAllKeys();
    var getSymbols = require_getSymbols();
    var keys = require_keys();
    function getAllKeys(object) {
      return baseGetAllKeys(object, keys, getSymbols);
    }
    module.exports = getAllKeys;
  }
});

// node_modules/lodash/_equalObjects.js
var require_equalObjects = __commonJS({
  "node_modules/lodash/_equalObjects.js"(exports, module) {
    var getAllKeys = require_getAllKeys();
    var COMPARE_PARTIAL_FLAG = 1;
    var objectProto = Object.prototype;
    var hasOwnProperty = objectProto.hasOwnProperty;
    function equalObjects(object, other, bitmask, customizer, equalFunc, stack) {
      var isPartial = bitmask & COMPARE_PARTIAL_FLAG, objProps = getAllKeys(object), objLength = objProps.length, othProps = getAllKeys(other), othLength = othProps.length;
      if (objLength != othLength && !isPartial) {
        return false;
      }
      var index = objLength;
      while (index--) {
        var key = objProps[index];
        if (!(isPartial ? key in other : hasOwnProperty.call(other, key))) {
          return false;
        }
      }
      var objStacked = stack.get(object);
      var othStacked = stack.get(other);
      if (objStacked && othStacked) {
        return objStacked == other && othStacked == object;
      }
      var result = true;
      stack.set(object, other);
      stack.set(other, object);
      var skipCtor = isPartial;
      while (++index < objLength) {
        key = objProps[index];
        var objValue = object[key], othValue = other[key];
        if (customizer) {
          var compared = isPartial ? customizer(othValue, objValue, key, other, object, stack) : customizer(objValue, othValue, key, object, other, stack);
        }
        if (!(compared === void 0 ? objValue === othValue || equalFunc(objValue, othValue, bitmask, customizer, stack) : compared)) {
          result = false;
          break;
        }
        skipCtor || (skipCtor = key == "constructor");
      }
      if (result && !skipCtor) {
        var objCtor = object.constructor, othCtor = other.constructor;
        if (objCtor != othCtor && ("constructor" in object && "constructor" in other) && !(typeof objCtor == "function" && objCtor instanceof objCtor && typeof othCtor == "function" && othCtor instanceof othCtor)) {
          result = false;
        }
      }
      stack["delete"](object);
      stack["delete"](other);
      return result;
    }
    module.exports = equalObjects;
  }
});

// node_modules/lodash/_DataView.js
var require_DataView = __commonJS({
  "node_modules/lodash/_DataView.js"(exports, module) {
    var getNative = require_getNative();
    var root = require_root();
    var DataView = getNative(root, "DataView");
    module.exports = DataView;
  }
});

// node_modules/lodash/_Promise.js
var require_Promise = __commonJS({
  "node_modules/lodash/_Promise.js"(exports, module) {
    var getNative = require_getNative();
    var root = require_root();
    var Promise2 = getNative(root, "Promise");
    module.exports = Promise2;
  }
});

// node_modules/lodash/_Set.js
var require_Set = __commonJS({
  "node_modules/lodash/_Set.js"(exports, module) {
    var getNative = require_getNative();
    var root = require_root();
    var Set = getNative(root, "Set");
    module.exports = Set;
  }
});

// node_modules/lodash/_WeakMap.js
var require_WeakMap = __commonJS({
  "node_modules/lodash/_WeakMap.js"(exports, module) {
    var getNative = require_getNative();
    var root = require_root();
    var WeakMap = getNative(root, "WeakMap");
    module.exports = WeakMap;
  }
});

// node_modules/lodash/_getTag.js
var require_getTag = __commonJS({
  "node_modules/lodash/_getTag.js"(exports, module) {
    var DataView = require_DataView();
    var Map = require_Map();
    var Promise2 = require_Promise();
    var Set = require_Set();
    var WeakMap = require_WeakMap();
    var baseGetTag = require_baseGetTag();
    var toSource = require_toSource();
    var mapTag = "[object Map]";
    var objectTag = "[object Object]";
    var promiseTag = "[object Promise]";
    var setTag = "[object Set]";
    var weakMapTag = "[object WeakMap]";
    var dataViewTag = "[object DataView]";
    var dataViewCtorString = toSource(DataView);
    var mapCtorString = toSource(Map);
    var promiseCtorString = toSource(Promise2);
    var setCtorString = toSource(Set);
    var weakMapCtorString = toSource(WeakMap);
    var getTag = baseGetTag;
    if (DataView && getTag(new DataView(new ArrayBuffer(1))) != dataViewTag || Map && getTag(new Map()) != mapTag || Promise2 && getTag(Promise2.resolve()) != promiseTag || Set && getTag(new Set()) != setTag || WeakMap && getTag(new WeakMap()) != weakMapTag) {
      getTag = function(value) {
        var result = baseGetTag(value), Ctor = result == objectTag ? value.constructor : void 0, ctorString = Ctor ? toSource(Ctor) : "";
        if (ctorString) {
          switch (ctorString) {
            case dataViewCtorString:
              return dataViewTag;
            case mapCtorString:
              return mapTag;
            case promiseCtorString:
              return promiseTag;
            case setCtorString:
              return setTag;
            case weakMapCtorString:
              return weakMapTag;
          }
        }
        return result;
      };
    }
    module.exports = getTag;
  }
});

// node_modules/lodash/_baseIsEqualDeep.js
var require_baseIsEqualDeep = __commonJS({
  "node_modules/lodash/_baseIsEqualDeep.js"(exports, module) {
    var Stack = require_Stack();
    var equalArrays = require_equalArrays();
    var equalByTag = require_equalByTag();
    var equalObjects = require_equalObjects();
    var getTag = require_getTag();
    var isArray = require_isArray();
    var isBuffer = require_isBuffer();
    var isTypedArray = require_isTypedArray();
    var COMPARE_PARTIAL_FLAG = 1;
    var argsTag = "[object Arguments]";
    var arrayTag = "[object Array]";
    var objectTag = "[object Object]";
    var objectProto = Object.prototype;
    var hasOwnProperty = objectProto.hasOwnProperty;
    function baseIsEqualDeep(object, other, bitmask, customizer, equalFunc, stack) {
      var objIsArr = isArray(object), othIsArr = isArray(other), objTag = objIsArr ? arrayTag : getTag(object), othTag = othIsArr ? arrayTag : getTag(other);
      objTag = objTag == argsTag ? objectTag : objTag;
      othTag = othTag == argsTag ? objectTag : othTag;
      var objIsObj = objTag == objectTag, othIsObj = othTag == objectTag, isSameTag = objTag == othTag;
      if (isSameTag && isBuffer(object)) {
        if (!isBuffer(other)) {
          return false;
        }
        objIsArr = true;
        objIsObj = false;
      }
      if (isSameTag && !objIsObj) {
        stack || (stack = new Stack());
        return objIsArr || isTypedArray(object) ? equalArrays(object, other, bitmask, customizer, equalFunc, stack) : equalByTag(object, other, objTag, bitmask, customizer, equalFunc, stack);
      }
      if (!(bitmask & COMPARE_PARTIAL_FLAG)) {
        var objIsWrapped = objIsObj && hasOwnProperty.call(object, "__wrapped__"), othIsWrapped = othIsObj && hasOwnProperty.call(other, "__wrapped__");
        if (objIsWrapped || othIsWrapped) {
          var objUnwrapped = objIsWrapped ? object.value() : object, othUnwrapped = othIsWrapped ? other.value() : other;
          stack || (stack = new Stack());
          return equalFunc(objUnwrapped, othUnwrapped, bitmask, customizer, stack);
        }
      }
      if (!isSameTag) {
        return false;
      }
      stack || (stack = new Stack());
      return equalObjects(object, other, bitmask, customizer, equalFunc, stack);
    }
    module.exports = baseIsEqualDeep;
  }
});

// node_modules/lodash/_baseIsEqual.js
var require_baseIsEqual = __commonJS({
  "node_modules/lodash/_baseIsEqual.js"(exports, module) {
    var baseIsEqualDeep = require_baseIsEqualDeep();
    var isObjectLike = require_isObjectLike();
    function baseIsEqual(value, other, bitmask, customizer, stack) {
      if (value === other) {
        return true;
      }
      if (value == null || other == null || !isObjectLike(value) && !isObjectLike(other)) {
        return value !== value && other !== other;
      }
      return baseIsEqualDeep(value, other, bitmask, customizer, baseIsEqual, stack);
    }
    module.exports = baseIsEqual;
  }
});

// node_modules/lodash/isEqual.js
var require_isEqual = __commonJS({
  "node_modules/lodash/isEqual.js"(exports, module) {
    var baseIsEqual = require_baseIsEqual();
    function isEqual2(value, other) {
      return baseIsEqual(value, other);
    }
    module.exports = isEqual2;
  }
});

// node_modules/@medusajs/dashboard/dist/chunk-SXYXTC2L.mjs
var import_react = __toESM(require_react(), 1);
var import_react2 = __toESM(require_react(), 1);
var import_isEqual = __toESM(require_isEqual(), 1);
var import_react3 = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var import_lodash = __toESM(require_lodash(), 1);
var import_react4 = __toESM(require_react(), 1);
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var import_react5 = __toESM(require_react(), 1);
var import_jsx_runtime4 = __toESM(require_jsx_runtime(), 1);
var import_lodash2 = __toESM(require_lodash(), 1);
var import_react6 = __toESM(require_react(), 1);
var import_jsx_runtime5 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime6 = __toESM(require_jsx_runtime(), 1);
var DataTableFilterContext = (0, import_react2.createContext)(null);
var useDataTableFilterContext = () => {
  const ctx = (0, import_react2.useContext)(DataTableFilterContext);
  if (!ctx) {
    throw new Error(
      "useDataTableFacetedFilterContext must be used within a DataTableFacetedFilter"
    );
  }
  return ctx;
};
var FilterChip = ({
  hadPreviousValue,
  label,
  value,
  readonly,
  hasOperator,
  onRemove
}) => {
  const { t: t2 } = useTranslation();
  const handleRemove = (e) => {
    e.stopPropagation();
    onRemove();
  };
  return (0, import_jsx_runtime.jsxs)("div", { className: "bg-ui-bg-field transition-fg shadow-borders-base text-ui-fg-subtle flex cursor-default select-none items-stretch overflow-hidden rounded-md", children: [
    !hadPreviousValue && (0, import_jsx_runtime.jsx)(dist_exports.Anchor, {}),
    (0, import_jsx_runtime.jsx)(
      "div",
      {
        className: clx(
          "flex items-center justify-center whitespace-nowrap px-2 py-1",
          {
            "border-r": !!(value || hadPreviousValue)
          }
        ),
        children: (0, import_jsx_runtime.jsx)(Text, { size: "small", weight: "plus", leading: "compact", children: label })
      }
    ),
    (0, import_jsx_runtime.jsxs)("div", { className: "flex w-full items-center overflow-hidden", children: [
      hasOperator && !!(value || hadPreviousValue) && (0, import_jsx_runtime.jsx)("div", { className: "border-r p-1 px-2", children: (0, import_jsx_runtime.jsx)(
        Text,
        {
          size: "small",
          weight: "plus",
          leading: "compact",
          className: "text-ui-fg-muted",
          children: t2("general.is")
        }
      ) }),
      !!(value || hadPreviousValue) && (0, import_jsx_runtime.jsx)(
        dist_exports.Trigger,
        {
          asChild: true,
          className: clx(
            "flex-1 cursor-pointer overflow-hidden border-r p-1 px-2",
            {
              "hover:bg-ui-bg-field-hover": !readonly,
              "data-[state=open]:bg-ui-bg-field-hover": !readonly
            }
          ),
          children: (0, import_jsx_runtime.jsx)(
            Text,
            {
              size: "small",
              leading: "compact",
              weight: "plus",
              className: "truncate text-nowrap",
              children: value || " "
            }
          )
        }
      )
    ] }),
    !readonly && !!(value || hadPreviousValue) && (0, import_jsx_runtime.jsx)(
      "button",
      {
        onClick: handleRemove,
        className: clx(
          "text-ui-fg-muted transition-fg flex items-center justify-center p-1",
          "hover:bg-ui-bg-subtle-hover",
          "active:bg-ui-bg-subtle-pressed active:text-ui-fg-base"
        ),
        children: (0, import_jsx_runtime.jsx)(XMarkMini, {})
      }
    )
  ] });
};
var filter_chip_default = FilterChip;
var DateFilter = ({
  filter,
  prefix,
  readonly,
  openOnMount
}) => {
  const [open, setOpen] = (0, import_react3.useState)(openOnMount);
  const [showCustom, setShowCustom] = (0, import_react3.useState)(false);
  const { getFullDate } = useDate();
  const { key, label } = filter;
  const { removeFilter } = useDataTableFilterContext();
  const selectedParams = useSelectedParams({ param: key, prefix });
  const presets = usePresets();
  const handleSelectPreset = (value) => {
    selectedParams.add(JSON.stringify(value));
    setShowCustom(false);
  };
  const handleSelectCustom = () => {
    selectedParams.delete();
    setShowCustom((prev) => !prev);
  };
  const currentValue = selectedParams.get();
  const currentDateComparison = parseDateComparison(currentValue);
  const customStartValue = getDateFromComparison(currentDateComparison, "$gte");
  const customEndValue = getDateFromComparison(currentDateComparison, "$lte");
  const handleCustomDateChange = (value, pos) => {
    const key2 = pos === "start" ? "$gte" : "$lte";
    const dateValue = value ? value.toISOString() : void 0;
    selectedParams.add(
      JSON.stringify({
        ...currentDateComparison || {},
        [key2]: dateValue
      })
    );
  };
  const getDisplayValueFromPresets = () => {
    const preset = presets.find((p) => (0, import_isEqual.default)(p.value, currentDateComparison));
    return preset == null ? void 0 : preset.label;
  };
  const formatCustomDate = (date) => {
    return date ? getFullDate({ date }) : void 0;
  };
  const getCustomDisplayValue = () => {
    const formattedDates = [customStartValue, customEndValue].map(
      formatCustomDate
    );
    return formattedDates.filter(Boolean).join(" - ");
  };
  const displayValue = getDisplayValueFromPresets() || getCustomDisplayValue();
  const [previousValue, setPreviousValue] = (0, import_react3.useState)(
    displayValue
  );
  const handleRemove = () => {
    selectedParams.delete();
    removeFilter(key);
  };
  let timeoutId = null;
  const handleOpenChange = (open2) => {
    setOpen(open2);
    setPreviousValue(displayValue);
    if (timeoutId) {
      clearTimeout(timeoutId);
    }
    if (!open2 && !currentValue.length) {
      timeoutId = setTimeout(() => {
        removeFilter(key);
      }, 200);
    }
  };
  return (0, import_jsx_runtime2.jsxs)(dist_exports.Root, { modal: true, open, onOpenChange: handleOpenChange, children: [
    (0, import_jsx_runtime2.jsx)(
      filter_chip_default,
      {
        hadPreviousValue: !!previousValue,
        label,
        value: displayValue,
        onRemove: handleRemove,
        readonly
      }
    ),
    !readonly && (0, import_jsx_runtime2.jsx)(dist_exports.Portal, { children: (0, import_jsx_runtime2.jsxs)(
      dist_exports.Content,
      {
        "data-name": "date_filter_content",
        align: "start",
        sideOffset: 8,
        collisionPadding: 24,
        className: clx(
          "bg-ui-bg-base text-ui-fg-base shadow-elevation-flyout h-full max-h-[var(--radix-popper-available-height)] w-[300px] overflow-auto rounded-lg"
        ),
        onInteractOutside: (e) => {
          var _a;
          if (e.target instanceof HTMLElement) {
            if (((_a = e.target.attributes.getNamedItem("data-name")) == null ? void 0 : _a.value) === "filters_menu_content") {
              e.preventDefault();
            }
          }
        },
        children: [
          (0, import_jsx_runtime2.jsxs)("ul", { className: "w-full p-1", children: [
            presets.map((preset) => {
              const isSelected = selectedParams.get().includes(JSON.stringify(preset.value));
              return (0, import_jsx_runtime2.jsx)("li", { children: (0, import_jsx_runtime2.jsxs)(
                "button",
                {
                  className: "bg-ui-bg-base hover:bg-ui-bg-base-hover focus-visible:bg-ui-bg-base-pressed text-ui-fg-base data-[disabled]:text-ui-fg-disabled txt-compact-small relative flex w-full cursor-pointer select-none items-center rounded-md px-2 py-1.5 outline-none transition-colors data-[disabled]:pointer-events-none",
                  type: "button",
                  onClick: () => {
                    handleSelectPreset(preset.value);
                  },
                  children: [
                    (0, import_jsx_runtime2.jsx)(
                      "div",
                      {
                        className: clx(
                          "transition-fg flex h-5 w-5 items-center justify-center",
                          {
                            "[&_svg]:invisible": !isSelected
                          }
                        ),
                        children: (0, import_jsx_runtime2.jsx)(EllipseMiniSolid, {})
                      }
                    ),
                    preset.label
                  ]
                }
              ) }, preset.label);
            }),
            (0, import_jsx_runtime2.jsx)("li", { children: (0, import_jsx_runtime2.jsxs)(
              "button",
              {
                className: "bg-ui-bg-base hover:bg-ui-bg-base-hover focus-visible:bg-ui-bg-base-pressed text-ui-fg-base data-[disabled]:text-ui-fg-disabled txt-compact-small relative flex w-full cursor-pointer select-none items-center rounded-md px-2 py-1.5 outline-none transition-colors data-[disabled]:pointer-events-none",
                type: "button",
                onClick: handleSelectCustom,
                children: [
                  (0, import_jsx_runtime2.jsx)(
                    "div",
                    {
                      className: clx(
                        "transition-fg flex h-5 w-5 items-center justify-center",
                        {
                          "[&_svg]:invisible": !showCustom
                        }
                      ),
                      children: (0, import_jsx_runtime2.jsx)(EllipseMiniSolid, {})
                    }
                  ),
                  t("filters.date.custom")
                ]
              }
            ) })
          ] }),
          showCustom && (0, import_jsx_runtime2.jsxs)("div", { className: "border-t px-1 pb-3 pt-1", children: [
            (0, import_jsx_runtime2.jsxs)("div", { children: [
              (0, import_jsx_runtime2.jsx)("div", { className: "px-2 py-1", children: (0, import_jsx_runtime2.jsx)(Text, { size: "xsmall", leading: "compact", weight: "plus", children: t("filters.date.from") }) }),
              (0, import_jsx_runtime2.jsx)("div", { className: "px-2 py-1", children: (0, import_jsx_runtime2.jsx)(
                DatePicker,
                {
                  modal: true,
                  maxValue: customEndValue,
                  value: customStartValue,
                  onChange: (d) => handleCustomDateChange(d, "start")
                }
              ) })
            ] }),
            (0, import_jsx_runtime2.jsxs)("div", { children: [
              (0, import_jsx_runtime2.jsx)("div", { className: "px-2 py-1", children: (0, import_jsx_runtime2.jsx)(Text, { size: "xsmall", leading: "compact", weight: "plus", children: t("filters.date.to") }) }),
              (0, import_jsx_runtime2.jsx)("div", { className: "px-2 py-1", children: (0, import_jsx_runtime2.jsx)(
                DatePicker,
                {
                  modal: true,
                  minValue: customStartValue,
                  value: customEndValue || void 0,
                  onChange: (d) => {
                    handleCustomDateChange(d, "end");
                  }
                }
              ) })
            ] })
          ] })
        ]
      }
    ) })
  ] });
};
var today = /* @__PURE__ */ new Date();
today.setHours(0, 0, 0, 0);
var usePresets = () => {
  const { t: t2 } = useTranslation();
  return (0, import_react3.useMemo)(
    () => [
      {
        label: t2("filters.date.today"),
        value: {
          $gte: today.toISOString()
        }
      },
      {
        label: t2("filters.date.lastSevenDays"),
        value: {
          $gte: new Date(
            today.getTime() - 7 * 24 * 60 * 60 * 1e3
          ).toISOString()
          // 7 days ago
        }
      },
      {
        label: t2("filters.date.lastThirtyDays"),
        value: {
          $gte: new Date(
            today.getTime() - 30 * 24 * 60 * 60 * 1e3
          ).toISOString()
          // 30 days ago
        }
      },
      {
        label: t2("filters.date.lastNinetyDays"),
        value: {
          $gte: new Date(
            today.getTime() - 90 * 24 * 60 * 60 * 1e3
          ).toISOString()
          // 90 days ago
        }
      },
      {
        label: t2("filters.date.lastTwelveMonths"),
        value: {
          $gte: new Date(
            today.getTime() - 365 * 24 * 60 * 60 * 1e3
          ).toISOString()
          // 365 days ago
        }
      }
    ],
    [t2]
  );
};
var parseDateComparison = (value) => {
  return (value == null ? void 0 : value.length) ? JSON.parse(value.join(",")) : null;
};
var getDateFromComparison = (comparison, key) => {
  return (comparison == null ? void 0 : comparison[key]) ? new Date(comparison[key]) : void 0;
};
var NumberFilter = ({
  filter,
  prefix,
  readonly,
  openOnMount
}) => {
  const { t: t2 } = useTranslation();
  const [open, setOpen] = (0, import_react4.useState)(openOnMount);
  const { key, label } = filter;
  const { removeFilter } = useDataTableFilterContext();
  const selectedParams = useSelectedParams({
    param: key,
    prefix,
    multiple: false
  });
  const currentValue = selectedParams.get();
  const [previousValue, setPreviousValue] = (0, import_react4.useState)(
    currentValue
  );
  const [operator, setOperator] = (0, import_react4.useState)(
    getOperator(currentValue)
  );
  const debouncedOnChange = (0, import_react4.useCallback)(
    (0, import_lodash.debounce)((e, operator2) => {
      const value = e.target.value;
      const curr = JSON.parse((currentValue == null ? void 0 : currentValue.join(",")) || "{}");
      const isCurrentNumber = !isNaN(Number(curr));
      const handleValue = (operator3) => {
        if (!value && isCurrentNumber) {
          selectedParams.delete();
          return;
        }
        if (curr && !value) {
          delete curr[operator3];
          selectedParams.add(JSON.stringify(curr));
          return;
        }
        if (!curr) {
          selectedParams.add(JSON.stringify({ [operator3]: value }));
          return;
        }
        selectedParams.add(JSON.stringify({ ...curr, [operator3]: value }));
      };
      switch (operator2) {
        case "eq":
          if (!value) {
            selectedParams.delete();
          } else {
            selectedParams.add(value);
          }
          break;
        case "lt":
        case "gt":
          handleValue(operator2);
          break;
      }
    }, 500),
    [selectedParams, currentValue]
  );
  (0, import_react4.useEffect)(() => {
    return () => {
      debouncedOnChange.cancel();
    };
  }, [debouncedOnChange]);
  let timeoutId = null;
  const handleOpenChange = (open2) => {
    setOpen(open2);
    setPreviousValue(currentValue);
    if (timeoutId) {
      clearTimeout(timeoutId);
    }
    if (!open2 && !currentValue.length) {
      timeoutId = setTimeout(() => {
        removeFilter(key);
      }, 200);
    }
  };
  const handleRemove = () => {
    selectedParams.delete();
    removeFilter(key);
  };
  const operators = [
    {
      operator: "exact",
      label: t2("filters.compare.exact")
    },
    {
      operator: "range",
      label: t2("filters.compare.range")
    }
  ];
  const GT_KEY = `${key}-gt`;
  const LT_KEY = `${key}-lt`;
  const EQ_KEY = key;
  const displayValue = parseDisplayValue(currentValue, t2);
  const previousDisplayValue = parseDisplayValue(previousValue, t2);
  return (0, import_jsx_runtime3.jsxs)(dist_exports.Root, { modal: true, open, onOpenChange: handleOpenChange, children: [
    (0, import_jsx_runtime3.jsx)(
      filter_chip_default,
      {
        hasOperator: true,
        hadPreviousValue: !!previousDisplayValue,
        label,
        value: displayValue,
        onRemove: handleRemove,
        readonly
      }
    ),
    !readonly && (0, import_jsx_runtime3.jsx)(dist_exports.Portal, { children: (0, import_jsx_runtime3.jsxs)(
      dist_exports.Content,
      {
        "data-name": "number_filter_content",
        align: "start",
        sideOffset: 8,
        collisionPadding: 24,
        className: clx(
          "bg-ui-bg-base text-ui-fg-base shadow-elevation-flyout max-h-[var(--radix-popper-available-height)] w-[300px] divide-y overflow-y-auto rounded-lg outline-none"
        ),
        onInteractOutside: (e) => {
          var _a;
          if (e.target instanceof HTMLElement) {
            if (((_a = e.target.attributes.getNamedItem("data-name")) == null ? void 0 : _a.value) === "filters_menu_content") {
              e.preventDefault();
            }
          }
        },
        children: [
          (0, import_jsx_runtime3.jsx)("div", { className: "p-1", children: (0, import_jsx_runtime3.jsx)(
            dist_exports2.Root,
            {
              value: operator,
              onValueChange: (val) => setOperator(val),
              className: "flex flex-col items-start",
              orientation: "vertical",
              autoFocus: true,
              children: operators.map((o) => (0, import_jsx_runtime3.jsxs)(
                dist_exports2.Item,
                {
                  value: o.operator,
                  className: "txt-compact-small hover:bg-ui-bg-base-hover focus-visible:bg-ui-bg-base-hover active:bg-ui-bg-base-pressed transition-fg grid w-full grid-cols-[20px_1fr] gap-2 rounded-[4px] px-2 py-1.5 text-left outline-none",
                  children: [
                    (0, import_jsx_runtime3.jsx)("div", { className: "size-5", children: (0, import_jsx_runtime3.jsx)(dist_exports2.Indicator, { children: (0, import_jsx_runtime3.jsx)(EllipseMiniSolid, {}) }) }),
                    (0, import_jsx_runtime3.jsx)("span", { className: "w-full", children: o.label })
                  ]
                },
                o.operator
              ))
            }
          ) }),
          (0, import_jsx_runtime3.jsx)("div", { children: operator === "range" ? (0, import_jsx_runtime3.jsxs)("div", { className: "px-1 pb-3 pt-1", children: [
            (0, import_jsx_runtime3.jsx)("div", { className: "px-2 py-1.5", children: (0, import_jsx_runtime3.jsx)(Label, { size: "xsmall", weight: "plus", htmlFor: GT_KEY, children: t2("filters.compare.greaterThan") }) }),
            (0, import_jsx_runtime3.jsx)("div", { className: "px-2 py-0.5", children: (0, import_jsx_runtime3.jsx)(
              Input,
              {
                name: GT_KEY,
                size: "small",
                type: "number",
                defaultValue: getValue(currentValue, "gt"),
                onChange: (e) => debouncedOnChange(e, "gt")
              }
            ) }),
            (0, import_jsx_runtime3.jsx)("div", { className: "px-2 py-1.5", children: (0, import_jsx_runtime3.jsx)(Label, { size: "xsmall", weight: "plus", htmlFor: LT_KEY, children: t2("filters.compare.lessThan") }) }),
            (0, import_jsx_runtime3.jsx)("div", { className: "px-2 py-0.5", children: (0, import_jsx_runtime3.jsx)(
              Input,
              {
                name: LT_KEY,
                size: "small",
                type: "number",
                defaultValue: getValue(currentValue, "lt"),
                onChange: (e) => debouncedOnChange(e, "lt")
              }
            ) })
          ] }, "range") : (0, import_jsx_runtime3.jsxs)("div", { className: "px-1 pb-3 pt-1", children: [
            (0, import_jsx_runtime3.jsx)("div", { className: "px-2 py-1.5", children: (0, import_jsx_runtime3.jsx)(Label, { size: "xsmall", weight: "plus", htmlFor: EQ_KEY, children: label }) }),
            (0, import_jsx_runtime3.jsx)("div", { className: "px-2 py-0.5", children: (0, import_jsx_runtime3.jsx)(
              Input,
              {
                name: EQ_KEY,
                size: "small",
                type: "number",
                defaultValue: getValue(currentValue, "eq"),
                onChange: (e) => debouncedOnChange(e, "eq")
              }
            ) })
          ] }, "exact") })
        ]
      }
    ) })
  ] });
};
var parseDisplayValue = (value, t2) => {
  const parsed = JSON.parse((value == null ? void 0 : value.join(",")) || "{}");
  let displayValue = "";
  if (typeof parsed === "object") {
    const parts = [];
    if (parsed.gt) {
      parts.push(t2("filters.compare.greaterThanLabel", { value: parsed.gt }));
    }
    if (parsed.lt) {
      parts.push(
        t2("filters.compare.lessThanLabel", {
          value: parsed.lt
        })
      );
    }
    displayValue = parts.join(` ${t2("filters.compare.andLabel")} `);
  }
  if (typeof parsed === "number") {
    displayValue = parsed.toString();
  }
  return displayValue;
};
var parseValue = (value) => {
  if (!value) {
    return void 0;
  }
  const val = value.join(",");
  if (!val) {
    return void 0;
  }
  return JSON.parse(val);
};
var getValue = (value, key) => {
  const parsed = parseValue(value);
  if (typeof parsed === "object") {
    return parsed[key];
  }
  if (typeof parsed === "number" && key === "eq") {
    return parsed;
  }
  return void 0;
};
var getOperator = (value) => {
  const parsed = parseValue(value);
  return typeof parsed === "object" ? "range" : "exact";
};
var SelectFilter = ({
  filter,
  prefix,
  readonly,
  multiple,
  searchable,
  options,
  openOnMount
}) => {
  const [open, setOpen] = (0, import_react5.useState)(openOnMount);
  const [search, setSearch] = (0, import_react5.useState)("");
  const [searchRef, setSearchRef] = (0, import_react5.useState)(null);
  const { t: t2 } = useTranslation();
  const { removeFilter } = useDataTableFilterContext();
  const { key, label } = filter;
  const selectedParams = useSelectedParams({ param: key, prefix, multiple });
  const currentValue = selectedParams.get();
  const labelValues = currentValue.map((v) => {
    var _a;
    return (_a = options.find((o) => o.value === v)) == null ? void 0 : _a.label;
  }).filter(Boolean);
  const [previousValue, setPreviousValue] = (0, import_react5.useState)(labelValues);
  const handleRemove = () => {
    selectedParams.delete();
    removeFilter(key);
  };
  let timeoutId = null;
  const handleOpenChange = (open2) => {
    setOpen(open2);
    setPreviousValue(labelValues);
    if (timeoutId) {
      clearTimeout(timeoutId);
    }
    if (!open2 && !currentValue.length) {
      timeoutId = setTimeout(() => {
        removeFilter(key);
      }, 200);
    }
  };
  const handleClearSearch = () => {
    setSearch("");
    if (searchRef) {
      searchRef.focus();
    }
  };
  const handleSelect = (value) => {
    const isSelected = selectedParams.get().includes(String(value));
    if (isSelected) {
      selectedParams.delete(String(value));
    } else {
      selectedParams.add(String(value));
    }
  };
  const normalizedValues = labelValues ? Array.isArray(labelValues) ? labelValues : [labelValues] : null;
  const normalizedPrev = previousValue ? Array.isArray(previousValue) ? previousValue : [previousValue] : null;
  return (0, import_jsx_runtime4.jsxs)(dist_exports.Root, { modal: true, open, onOpenChange: handleOpenChange, children: [
    (0, import_jsx_runtime4.jsx)(
      filter_chip_default,
      {
        hasOperator: true,
        hadPreviousValue: !!(normalizedPrev == null ? void 0 : normalizedPrev.length),
        readonly,
        label,
        value: normalizedValues == null ? void 0 : normalizedValues.join(", "),
        onRemove: handleRemove
      }
    ),
    !readonly && (0, import_jsx_runtime4.jsx)(dist_exports.Portal, { children: (0, import_jsx_runtime4.jsx)(
      dist_exports.Content,
      {
        hideWhenDetached: true,
        align: "start",
        sideOffset: 8,
        collisionPadding: 8,
        className: clx(
          "bg-ui-bg-base text-ui-fg-base shadow-elevation-flyout z-[1] h-full max-h-[200px] w-[300px] overflow-hidden rounded-lg outline-none"
        ),
        onInteractOutside: (e) => {
          var _a;
          if (e.target instanceof HTMLElement) {
            if (((_a = e.target.attributes.getNamedItem("data-name")) == null ? void 0 : _a.value) === "filters_menu_content") {
              e.preventDefault();
              e.stopPropagation();
            }
          }
        },
        children: (0, import_jsx_runtime4.jsxs)(we, { className: "h-full", children: [
          searchable && (0, import_jsx_runtime4.jsx)("div", { className: "border-b p-1", children: (0, import_jsx_runtime4.jsxs)("div", { className: "grid grid-cols-[1fr_20px] gap-x-2 rounded-md px-2 py-1", children: [
            (0, import_jsx_runtime4.jsx)(
              we.Input,
              {
                ref: setSearchRef,
                value: search,
                onValueChange: setSearch,
                className: "txt-compact-small placeholder:text-ui-fg-muted bg-transparent outline-none",
                placeholder: "Search"
              }
            ),
            (0, import_jsx_runtime4.jsx)("div", { className: "flex h-5 w-5 items-center justify-center", children: (0, import_jsx_runtime4.jsx)(
              "button",
              {
                disabled: !search,
                onClick: handleClearSearch,
                className: clx(
                  "transition-fg text-ui-fg-muted focus-visible:bg-ui-bg-base-pressed rounded-md outline-none",
                  {
                    invisible: !search
                  }
                ),
                children: (0, import_jsx_runtime4.jsx)(XMarkMini, {})
              }
            ) })
          ] }) }),
          (0, import_jsx_runtime4.jsx)(we.Empty, { className: "txt-compact-small flex items-center justify-center p-1", children: (0, import_jsx_runtime4.jsx)("span", { className: "w-full px-2 py-1 text-center", children: t2("general.noResultsTitle") }) }),
          (0, import_jsx_runtime4.jsx)(we.List, { className: "h-full max-h-[163px] min-h-[0] overflow-auto p-1 outline-none", children: options.map((option) => {
            const isSelected = selectedParams.get().includes(String(option.value));
            return (0, import_jsx_runtime4.jsxs)(
              we.Item,
              {
                className: "bg-ui-bg-base hover:bg-ui-bg-base-hover aria-selected:bg-ui-bg-base-pressed focus-visible:bg-ui-bg-base-pressed text-ui-fg-base data-[disabled]:text-ui-fg-disabled txt-compact-small relative flex cursor-pointer select-none items-center gap-x-2 rounded-md px-2 py-1.5 outline-none transition-colors data-[disabled]:pointer-events-none",
                value: option.label,
                onSelect: () => {
                  handleSelect(option.value);
                },
                children: [
                  (0, import_jsx_runtime4.jsx)(
                    "div",
                    {
                      className: clx(
                        "transition-fg flex h-5 w-5 items-center justify-center",
                        {
                          "[&_svg]:invisible": !isSelected
                        }
                      ),
                      children: multiple ? (0, import_jsx_runtime4.jsx)(CheckMini, {}) : (0, import_jsx_runtime4.jsx)(EllipseMiniSolid, {})
                    }
                  ),
                  option.label
                ]
              },
              String(option.value)
            );
          }) })
        ] })
      }
    ) })
  ] });
};
var StringFilter = ({
  filter,
  prefix,
  readonly,
  openOnMount
}) => {
  const [open, setOpen] = (0, import_react6.useState)(openOnMount);
  const { key, label } = filter;
  const { removeFilter } = useDataTableFilterContext();
  const selectedParams = useSelectedParams({ param: key, prefix });
  const query = selectedParams.get();
  const [previousValue, setPreviousValue] = (0, import_react6.useState)(
    query == null ? void 0 : query[0]
  );
  const debouncedOnChange = (0, import_react6.useCallback)(
    (0, import_lodash2.debounce)((e) => {
      const value = e.target.value;
      if (!value) {
        selectedParams.delete();
      } else {
        selectedParams.add(value);
      }
    }, 500),
    [selectedParams]
  );
  (0, import_react6.useEffect)(() => {
    return () => {
      debouncedOnChange.cancel();
    };
  }, [debouncedOnChange]);
  let timeoutId = null;
  const handleOpenChange = (open2) => {
    setOpen(open2);
    setPreviousValue(query == null ? void 0 : query[0]);
    if (timeoutId) {
      clearTimeout(timeoutId);
    }
    if (!open2 && !query.length) {
      timeoutId = setTimeout(() => {
        removeFilter(key);
      }, 200);
    }
  };
  const handleRemove = () => {
    selectedParams.delete();
    removeFilter(key);
  };
  return (0, import_jsx_runtime5.jsxs)(dist_exports.Root, { modal: true, open, onOpenChange: handleOpenChange, children: [
    (0, import_jsx_runtime5.jsx)(
      filter_chip_default,
      {
        hasOperator: true,
        hadPreviousValue: !!previousValue,
        label,
        value: query == null ? void 0 : query[0],
        onRemove: handleRemove,
        readonly
      }
    ),
    !readonly && (0, import_jsx_runtime5.jsx)(dist_exports.Portal, { children: (0, import_jsx_runtime5.jsx)(
      dist_exports.Content,
      {
        hideWhenDetached: true,
        align: "start",
        sideOffset: 8,
        collisionPadding: 8,
        className: clx(
          "bg-ui-bg-base text-ui-fg-base shadow-elevation-flyout z-[1] h-full max-h-[200px] w-[300px] overflow-hidden rounded-lg outline-none"
        ),
        onInteractOutside: (e) => {
          var _a;
          if (e.target instanceof HTMLElement) {
            if (((_a = e.target.attributes.getNamedItem("data-name")) == null ? void 0 : _a.value) === "filters_menu_content") {
              e.preventDefault();
              e.stopPropagation();
            }
          }
        },
        children: (0, import_jsx_runtime5.jsxs)("div", { className: "px-1 pb-3 pt-1", children: [
          (0, import_jsx_runtime5.jsx)("div", { className: "px-2 py-1.5", children: (0, import_jsx_runtime5.jsx)(Label, { size: "xsmall", weight: "plus", htmlFor: key, children: label }) }),
          (0, import_jsx_runtime5.jsx)("div", { className: "px-2 py-0.5", children: (0, import_jsx_runtime5.jsx)(
            Input,
            {
              name: key,
              size: "small",
              defaultValue: (query == null ? void 0 : query[0]) || void 0,
              onChange: debouncedOnChange
            }
          ) })
        ] })
      }
    ) })
  ] });
};
var DataTableFilter = ({
  filters,
  readonly,
  prefix
}) => {
  const { t: t2 } = useTranslation();
  const [searchParams] = useSearchParams();
  const [open, setOpen] = (0, import_react.useState)(false);
  const [activeFilters, setActiveFilters] = (0, import_react.useState)(
    getInitialFilters({ searchParams, filters, prefix })
  );
  const availableFilters = filters.filter(
    (f) => !activeFilters.find((af) => af.key === f.key)
  );
  const initialMount = (0, import_react.useRef)(true);
  (0, import_react.useEffect)(() => {
    if (initialMount.current) {
      const params = new URLSearchParams(searchParams);
      filters.forEach((filter) => {
        const key = prefix ? `${prefix}_${filter.key}` : filter.key;
        const value = params.get(key);
        if (value && !activeFilters.find((af) => af.key === filter.key)) {
          if (filter.type === "select") {
            setActiveFilters((prev) => [
              ...prev,
              {
                ...filter,
                multiple: filter.multiple,
                options: filter.options,
                openOnMount: false
              }
            ]);
          } else {
            setActiveFilters((prev) => [
              ...prev,
              { ...filter, openOnMount: false }
            ]);
          }
        }
      });
    }
    initialMount.current = false;
  }, [activeFilters, filters, prefix, searchParams]);
  const addFilter = (filter) => {
    setOpen(false);
    setActiveFilters((prev) => [...prev, { ...filter, openOnMount: true }]);
  };
  const removeFilter = (0, import_react.useCallback)((key) => {
    setActiveFilters((prev) => prev.filter((f) => f.key !== key));
  }, []);
  const removeAllFilters = (0, import_react.useCallback)(() => {
    setActiveFilters([]);
  }, []);
  return (0, import_jsx_runtime6.jsx)(
    DataTableFilterContext.Provider,
    {
      value: (0, import_react.useMemo)(
        () => ({
          removeFilter,
          removeAllFilters
        }),
        [removeAllFilters, removeFilter]
      ),
      children: (0, import_jsx_runtime6.jsxs)("div", { className: "max-w-2/3 flex flex-wrap items-center gap-2", children: [
        activeFilters.map((filter) => {
          switch (filter.type) {
            case "select":
              return (0, import_jsx_runtime6.jsx)(
                SelectFilter,
                {
                  filter,
                  prefix,
                  readonly,
                  options: filter.options,
                  multiple: filter.multiple,
                  searchable: filter.searchable,
                  openOnMount: filter.openOnMount
                },
                filter.key
              );
            case "date":
              return (0, import_jsx_runtime6.jsx)(
                DateFilter,
                {
                  filter,
                  prefix,
                  readonly,
                  openOnMount: filter.openOnMount
                },
                filter.key
              );
            case "string":
              return (0, import_jsx_runtime6.jsx)(
                StringFilter,
                {
                  filter,
                  prefix,
                  readonly,
                  openOnMount: filter.openOnMount
                },
                filter.key
              );
            case "number":
              return (0, import_jsx_runtime6.jsx)(
                NumberFilter,
                {
                  filter,
                  prefix,
                  readonly,
                  openOnMount: filter.openOnMount
                },
                filter.key
              );
            default:
              break;
          }
        }),
        !readonly && availableFilters.length > 0 && (0, import_jsx_runtime6.jsxs)(dist_exports.Root, { modal: true, open, onOpenChange: setOpen, children: [
          (0, import_jsx_runtime6.jsx)(dist_exports.Trigger, { asChild: true, id: "filters_menu_trigger", children: (0, import_jsx_runtime6.jsx)(Button, { size: "small", variant: "secondary", children: t2("filters.addFilter") }) }),
          (0, import_jsx_runtime6.jsx)(dist_exports.Portal, { children: (0, import_jsx_runtime6.jsx)(
            dist_exports.Content,
            {
              className: clx(
                "bg-ui-bg-base text-ui-fg-base shadow-elevation-flyout z-[1] h-full max-h-[200px] w-[300px] overflow-auto rounded-lg p-1 outline-none"
              ),
              "data-name": "filters_menu_content",
              align: "start",
              sideOffset: 8,
              collisionPadding: 8,
              onCloseAutoFocus: (e) => {
                const hasOpenFilter = activeFilters.find(
                  (filter) => filter.openOnMount
                );
                if (hasOpenFilter) {
                  e.preventDefault();
                }
              },
              children: availableFilters.map((filter) => {
                return (0, import_jsx_runtime6.jsx)(
                  "div",
                  {
                    className: "bg-ui-bg-base hover:bg-ui-bg-base-hover focus-visible:bg-ui-bg-base-pressed text-ui-fg-base data-[disabled]:text-ui-fg-disabled txt-compact-small relative flex cursor-pointer select-none items-center rounded-md px-2 py-1.5 outline-none transition-colors data-[disabled]:pointer-events-none",
                    role: "menuitem",
                    onClick: () => {
                      addFilter(filter);
                    },
                    children: filter.label
                  },
                  filter.key
                );
              })
            }
          ) })
        ] }),
        !readonly && activeFilters.length > 0 && (0, import_jsx_runtime6.jsx)(ClearAllFilters, { filters, prefix })
      ] })
    }
  );
};
var ClearAllFilters = ({ filters, prefix }) => {
  const { removeAllFilters } = useDataTableFilterContext();
  const [_, setSearchParams] = useSearchParams();
  const handleRemoveAll = () => {
    setSearchParams((prev) => {
      const newValues = new URLSearchParams(prev);
      filters.forEach((filter) => {
        newValues.delete(prefix ? `${prefix}_${filter.key}` : filter.key);
      });
      return newValues;
    });
    removeAllFilters();
  };
  return (0, import_jsx_runtime6.jsx)(
    "button",
    {
      type: "button",
      onClick: handleRemoveAll,
      className: clx(
        "text-ui-fg-muted transition-fg txt-compact-small-plus rounded-md px-2 py-1",
        "hover:text-ui-fg-subtle",
        "focus-visible:shadow-borders-focus"
      ),
      children: "Clear all"
    }
  );
};
var getInitialFilters = ({
  searchParams,
  filters,
  prefix
}) => {
  const params = new URLSearchParams(searchParams);
  const activeFilters = [];
  filters.forEach((filter) => {
    const key = prefix ? `${prefix}_${filter.key}` : filter.key;
    const value = params.get(key);
    if (value) {
      if (filter.type === "select") {
        activeFilters.push({
          ...filter,
          multiple: filter.multiple,
          options: filter.options,
          openOnMount: false
        });
      } else {
        activeFilters.push({ ...filter, openOnMount: false });
      }
    }
  });
  return activeFilters;
};

export {
  DataTableFilter
};
//# sourceMappingURL=chunk-MX43XOWY.js.map
