import {
  Form
} from "./chunk-XXJU43CK.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import {
  useBlocker,
  useLocation,
  useNavigate
} from "./chunk-T7YBVUWZ.js";
import {
  Drawer,
  FocusModal,
  Prompt,
  clx
} from "./chunk-YP2LLXWB.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/chunk-4TC5YS65.mjs
var import_react = __toESM(require_react(), 1);
var import_react2 = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_react3 = __toESM(require_react(), 1);
var import_react4 = __toESM(require_react(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var import_react5 = __toESM(require_react(), 1);
var import_react6 = __toESM(require_react(), 1);
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var import_react7 = __toESM(require_react(), 1);
var import_jsx_runtime4 = __toESM(require_jsx_runtime(), 1);
var import_react8 = __toESM(require_react(), 1);
var import_react9 = __toESM(require_react(), 1);
var import_jsx_runtime5 = __toESM(require_jsx_runtime(), 1);
var import_react10 = __toESM(require_react(), 1);
var import_jsx_runtime6 = __toESM(require_jsx_runtime(), 1);
var import_react11 = __toESM(require_react(), 1);
var import_jsx_runtime7 = __toESM(require_jsx_runtime(), 1);
var useStateAwareTo = (prev) => {
  const location = useLocation();
  const to = (0, import_react2.useMemo)(() => {
    var _a;
    const params = (_a = location.state) == null ? void 0 : _a.restore_params;
    if (!params) {
      return prev;
    }
    return `${prev}?${params.toString()}`;
  }, [location.state, prev]);
  return to;
};
var RouteModalForm = ({
  form,
  blockSearchParams: blockSearch = false,
  children,
  onClose
}) => {
  const { t } = useTranslation();
  const {
    formState: { isDirty }
  } = form;
  const blocker = useBlocker(({ currentLocation, nextLocation }) => {
    const { isSubmitSuccessful } = nextLocation.state || {};
    if (isSubmitSuccessful) {
      onClose == null ? void 0 : onClose(true);
      return false;
    }
    const isPathChanged = currentLocation.pathname !== nextLocation.pathname;
    const isSearchChanged = currentLocation.search !== nextLocation.search;
    if (blockSearch) {
      const shouldBlock2 = isDirty && (isPathChanged || isSearchChanged);
      if (isPathChanged) {
        onClose == null ? void 0 : onClose(isSubmitSuccessful);
      }
      return shouldBlock2;
    }
    const shouldBlock = isDirty && isPathChanged;
    if (isPathChanged) {
      onClose == null ? void 0 : onClose(isSubmitSuccessful);
    }
    return shouldBlock;
  });
  const handleCancel = () => {
    var _a;
    (_a = blocker == null ? void 0 : blocker.reset) == null ? void 0 : _a.call(blocker);
  };
  const handleContinue = () => {
    var _a;
    (_a = blocker == null ? void 0 : blocker.proceed) == null ? void 0 : _a.call(blocker);
    onClose == null ? void 0 : onClose(false);
  };
  return (0, import_jsx_runtime.jsxs)(Form, { ...form, children: [
    children,
    (0, import_jsx_runtime.jsx)(Prompt, { open: blocker.state === "blocked", variant: "confirmation", children: (0, import_jsx_runtime.jsxs)(Prompt.Content, { children: [
      (0, import_jsx_runtime.jsxs)(Prompt.Header, { children: [
        (0, import_jsx_runtime.jsx)(Prompt.Title, { children: t("general.unsavedChangesTitle") }),
        (0, import_jsx_runtime.jsx)(Prompt.Description, { children: t("general.unsavedChangesDescription") })
      ] }),
      (0, import_jsx_runtime.jsxs)(Prompt.Footer, { children: [
        (0, import_jsx_runtime.jsx)(Prompt.Cancel, { onClick: handleCancel, type: "button", children: t("actions.cancel") }),
        (0, import_jsx_runtime.jsx)(Prompt.Action, { onClick: handleContinue, type: "button", children: t("actions.continue") })
      ] })
    ] }) })
  ] });
};
var RouteModalProviderContext = (0, import_react4.createContext)(null);
var RouteModalProvider = ({
  prev,
  children
}) => {
  const navigate = useNavigate();
  const [closeOnEscape, setCloseOnEscape] = (0, import_react3.useState)(true);
  const handleSuccess = (0, import_react3.useCallback)(
    (path) => {
      const to = path || prev;
      navigate(to, { replace: true, state: { isSubmitSuccessful: true } });
    },
    [navigate, prev]
  );
  const value = (0, import_react3.useMemo)(
    () => ({
      handleSuccess,
      setCloseOnEscape,
      __internal: { closeOnEscape }
    }),
    [handleSuccess, setCloseOnEscape, closeOnEscape]
  );
  return (0, import_jsx_runtime2.jsx)(RouteModalProviderContext.Provider, { value, children });
};
var StackedModalContext = (0, import_react6.createContext)(null);
var StackedModalProvider = ({
  children,
  onOpenChange
}) => {
  const [state, setState] = (0, import_react5.useState)({});
  const getIsOpen = (id) => {
    return state[id] || false;
  };
  const setIsOpen = (id, open) => {
    setState((prevState) => ({
      ...prevState,
      [id]: open
    }));
    onOpenChange(open);
  };
  const register = (id) => {
    setState((prevState) => ({
      ...prevState,
      [id]: false
    }));
  };
  const unregister = (id) => {
    setState((prevState) => {
      const newState = { ...prevState };
      delete newState[id];
      return newState;
    });
  };
  return (0, import_jsx_runtime3.jsx)(
    StackedModalContext.Provider,
    {
      value: {
        getIsOpen,
        setIsOpen,
        register,
        unregister
      },
      children
    }
  );
};
var useStackedModal = () => {
  const context = (0, import_react7.useContext)(StackedModalContext);
  if (!context) {
    throw new Error(
      "useStackedModal must be used within a StackedModalProvider"
    );
  }
  return context;
};
var Root = ({ prev = "..", children }) => {
  const navigate = useNavigate();
  const [open, setOpen] = (0, import_react.useState)(false);
  const [stackedModalOpen, onStackedModalOpen] = (0, import_react.useState)(false);
  const to = useStateAwareTo(prev);
  (0, import_react.useEffect)(() => {
    setOpen(true);
    return () => {
      setOpen(false);
      onStackedModalOpen(false);
    };
  }, []);
  const handleOpenChange = (open2) => {
    if (!open2) {
      document.body.style.pointerEvents = "auto";
      navigate(to, { replace: true });
      return;
    }
    setOpen(open2);
  };
  return (0, import_jsx_runtime4.jsx)(Drawer, { open, onOpenChange: handleOpenChange, children: (0, import_jsx_runtime4.jsx)(RouteModalProvider, { prev: to, children: (0, import_jsx_runtime4.jsx)(StackedModalProvider, { onOpenChange: onStackedModalOpen, children: (0, import_jsx_runtime4.jsx)(
    Drawer.Content,
    {
      "aria-describedby": void 0,
      className: clx({
        "!bg-ui-bg-disabled !inset-y-5 !right-5": stackedModalOpen
      }),
      children
    }
  ) }) }) });
};
var Header = Drawer.Header;
var Title = Drawer.Title;
var Description = Drawer.Description;
var Body = Drawer.Body;
var Footer = Drawer.Footer;
var Close = Drawer.Close;
var Form2 = RouteModalForm;
var RouteDrawer = Object.assign(Root, {
  Header,
  Title,
  Body,
  Description,
  Footer,
  Close,
  Form: Form2
});
var useRouteModal = () => {
  const context = (0, import_react8.useContext)(RouteModalProviderContext);
  if (!context) {
    throw new Error("useRouteModal must be used within a RouteModalProvider");
  }
  return context;
};
var Root2 = ({ prev = "..", children }) => {
  const navigate = useNavigate();
  const [open, setOpen] = (0, import_react9.useState)(false);
  const [stackedModalOpen, onStackedModalOpen] = (0, import_react9.useState)(false);
  const to = useStateAwareTo(prev);
  (0, import_react9.useEffect)(() => {
    setOpen(true);
    return () => {
      setOpen(false);
      onStackedModalOpen(false);
    };
  }, []);
  const handleOpenChange = (open2) => {
    if (!open2) {
      document.body.style.pointerEvents = "auto";
      navigate(to, { replace: true });
      return;
    }
    setOpen(open2);
  };
  return (0, import_jsx_runtime5.jsx)(FocusModal, { open, onOpenChange: handleOpenChange, children: (0, import_jsx_runtime5.jsx)(RouteModalProvider, { prev: to, children: (0, import_jsx_runtime5.jsx)(StackedModalProvider, { onOpenChange: onStackedModalOpen, children: (0, import_jsx_runtime5.jsx)(Content, { stackedModalOpen, children }) }) }) });
};
var Content = ({ stackedModalOpen, children }) => {
  const { __internal } = useRouteModal();
  const shouldPreventClose = !__internal.closeOnEscape;
  return (0, import_jsx_runtime5.jsx)(
    FocusModal.Content,
    {
      onEscapeKeyDown: shouldPreventClose ? (e) => {
        e.preventDefault();
      } : void 0,
      className: clx({
        "!bg-ui-bg-disabled !inset-x-5 !inset-y-3": stackedModalOpen
      }),
      children
    }
  );
};
var Header2 = FocusModal.Header;
var Title2 = FocusModal.Title;
var Description2 = FocusModal.Description;
var Footer2 = FocusModal.Footer;
var Body2 = FocusModal.Body;
var Close2 = FocusModal.Close;
var Form3 = RouteModalForm;
var RouteFocusModal = Object.assign(Root2, {
  Header: Header2,
  Title: Title2,
  Body: Body2,
  Description: Description2,
  Footer: Footer2,
  Close: Close2,
  Form: Form3
});
var Root3 = ({ id, children }) => {
  const { register, unregister, getIsOpen, setIsOpen } = useStackedModal();
  (0, import_react10.useEffect)(() => {
    register(id);
    return () => unregister(id);
  }, []);
  return (0, import_jsx_runtime6.jsx)(Drawer, { open: getIsOpen(id), onOpenChange: (open) => setIsOpen(id, open), children });
};
var Close3 = Drawer.Close;
Close3.displayName = "StackedDrawer.Close";
var Header3 = Drawer.Header;
Header3.displayName = "StackedDrawer.Header";
var Body3 = Drawer.Body;
Body3.displayName = "StackedDrawer.Body";
var Trigger = Drawer.Trigger;
Trigger.displayName = "StackedDrawer.Trigger";
var Footer3 = Drawer.Footer;
Footer3.displayName = "StackedDrawer.Footer";
var Title3 = Drawer.Title;
Title3.displayName = "StackedDrawer.Title";
var Description3 = Drawer.Description;
Description3.displayName = "StackedDrawer.Description";
var Content2 = (0, import_react10.forwardRef)(({ className, ...props }, ref) => {
  return (0, import_jsx_runtime6.jsx)(
    Drawer.Content,
    {
      ref,
      className: clx(className),
      overlayProps: {
        className: "bg-transparent"
      },
      ...props
    }
  );
});
Content2.displayName = "StackedDrawer.Content";
var StackedDrawer = Object.assign(Root3, {
  Close: Close3,
  Header: Header3,
  Body: Body3,
  Content: Content2,
  Trigger,
  Footer: Footer3,
  Description: Description3,
  Title: Title3
});
var Root4 = ({
  id,
  onOpenChangeCallback,
  children
}) => {
  const { register, unregister, getIsOpen, setIsOpen } = useStackedModal();
  (0, import_react11.useEffect)(() => {
    register(id);
    return () => unregister(id);
  }, []);
  const handleOpenChange = (open) => {
    setIsOpen(id, open);
    onOpenChangeCallback == null ? void 0 : onOpenChangeCallback(open);
  };
  return (0, import_jsx_runtime7.jsx)(FocusModal, { open: getIsOpen(id), onOpenChange: handleOpenChange, children });
};
var Close4 = FocusModal.Close;
Close4.displayName = "StackedFocusModal.Close";
var Header4 = FocusModal.Header;
Header4.displayName = "StackedFocusModal.Header";
var Body4 = FocusModal.Body;
Body4.displayName = "StackedFocusModal.Body";
var Trigger2 = FocusModal.Trigger;
Trigger2.displayName = "StackedFocusModal.Trigger";
var Footer4 = FocusModal.Footer;
Footer4.displayName = "StackedFocusModal.Footer";
var Title4 = FocusModal.Title;
Title4.displayName = "StackedFocusModal.Title";
var Description4 = FocusModal.Description;
Description4.displayName = "StackedFocusModal.Description";
var Content3 = (0, import_react11.forwardRef)(({ className, ...props }, ref) => {
  return (0, import_jsx_runtime7.jsx)(
    FocusModal.Content,
    {
      ref,
      className: clx("!top-6", className),
      overlayProps: {
        className: "bg-transparent"
      },
      ...props
    }
  );
});
Content3.displayName = "StackedFocusModal.Content";
var StackedFocusModal = Object.assign(Root4, {
  Close: Close4,
  Header: Header4,
  Body: Body4,
  Content: Content3,
  Trigger: Trigger2,
  Footer: Footer4,
  Description: Description4,
  Title: Title4
});

export {
  useStackedModal,
  RouteDrawer,
  useRouteModal,
  RouteFocusModal,
  StackedDrawer,
  StackedFocusModal
};
//# sourceMappingURL=chunk-MVVOBQIC.js.map
