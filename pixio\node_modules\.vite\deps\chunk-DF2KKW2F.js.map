{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-LTC6LGS4.mjs"], "sourcesContent": ["import {\n  useDeletePriceList\n} from \"./chunk-YS65UGPC.mjs\";\n\n// src/routes/price-lists/common/hooks/use-delete-price-list-action.tsx\nimport { toast, usePrompt } from \"@medusajs/ui\";\nimport { useTranslation } from \"react-i18next\";\nimport { useNavigate } from \"react-router-dom\";\nvar useDeletePriceListAction = ({\n  priceList\n}) => {\n  const { t } = useTranslation();\n  const prompt = usePrompt();\n  const navigate = useNavigate();\n  const { mutateAsync } = useDeletePriceList(priceList.id);\n  const handleDelete = async () => {\n    const res = await prompt({\n      title: t(\"general.areYouSure\"),\n      description: t(\"priceLists.delete.confirmation\", {\n        title: priceList.title\n      }),\n      confirmText: t(\"actions.delete\"),\n      cancelText: t(\"actions.cancel\")\n    });\n    if (!res) {\n      return;\n    }\n    await mutateAsync(void 0, {\n      onSuccess: () => {\n        toast.success(\n          t(\"priceLists.delete.successToast\", {\n            title: priceList.title\n          })\n        );\n        navigate(\"/price-lists\");\n      },\n      onError: (e) => {\n        toast.error(e.message);\n      }\n    });\n  };\n  return handleDelete;\n};\n\nexport {\n  useDeletePriceListAction\n};\n"], "mappings": ";;;;;;;;;;;;;;;AAQA,IAAI,2BAA2B,CAAC;AAAA,EAC9B;AACF,MAAM;AACJ,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,QAAM,SAAS,UAAU;AACzB,QAAM,WAAW,YAAY;AAC7B,QAAM,EAAE,YAAY,IAAI,mBAAmB,UAAU,EAAE;AACvD,QAAM,eAAe,YAAY;AAC/B,UAAM,MAAM,MAAM,OAAO;AAAA,MACvB,OAAO,EAAE,oBAAoB;AAAA,MAC7B,aAAa,EAAE,kCAAkC;AAAA,QAC/C,OAAO,UAAU;AAAA,MACnB,CAAC;AAAA,MACD,aAAa,EAAE,gBAAgB;AAAA,MAC/B,YAAY,EAAE,gBAAgB;AAAA,IAChC,CAAC;AACD,QAAI,CAAC,KAAK;AACR;AAAA,IACF;AACA,UAAM,YAAY,QAAQ;AAAA,MACxB,WAAW,MAAM;AACf,cAAM;AAAA,UACJ,EAAE,kCAAkC;AAAA,YAClC,OAAO,UAAU;AAAA,UACnB,CAAC;AAAA,QACH;AACA,iBAAS,cAAc;AAAA,MACzB;AAAA,MACA,SAAS,CAAC,MAAM;AACd,cAAM,MAAM,EAAE,OAAO;AAAA,MACvB;AAAA,IACF,CAAC;AAAA,EACH;AACA,SAAO;AACT;", "names": []}