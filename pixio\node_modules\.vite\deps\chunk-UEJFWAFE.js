import {
  inventoryItemLevelsQueryKeys,
  inventoryItemsQueryKeys
} from "./chunk-AJYMIHLQ.js";
import {
  queryClient
} from "./chunk-VMLNCWLE.js";
import {
  queryKeysFactory
} from "./chunk-CUPZIPFX.js";
import {
  sdk
} from "./chunk-JFCIYOH4.js";
import {
  useMutation,
  useQuery
} from "./chunk-R35JBZ3G.js";

// node_modules/@medusajs/dashboard/dist/chunk-FVC7M755.mjs
var RESERVATION_ITEMS_QUERY_KEY = "reservation_items";
var reservationItemsQueryKeys = queryKeysFactory(
  RESERVATION_ITEMS_QUERY_KEY
);
var useReservationItem = (id, query, options) => {
  const { data, ...rest } = useQuery({
    queryKey: reservationItemsQueryKeys.detail(id),
    queryFn: async () => sdk.admin.reservation.retrieve(id, query),
    ...options
  });
  return { ...data, ...rest };
};
var useReservationItems = (query, options) => {
  const { data, ...rest } = useQuery({
    queryFn: () => sdk.admin.reservation.list(query),
    queryKey: reservationItemsQueryKeys.list(query),
    ...options
  });
  return { ...data, ...rest };
};
var useUpdateReservationItem = (id, options) => {
  return useMutation({
    mutationFn: (payload) => sdk.admin.reservation.update(id, payload),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({
        queryKey: reservationItemsQueryKeys.detail(id)
      });
      queryClient.invalidateQueries({
        queryKey: reservationItemsQueryKeys.lists()
      });
      queryClient.invalidateQueries({
        queryKey: inventoryItemsQueryKeys.details()
      });
      queryClient.invalidateQueries({
        queryKey: inventoryItemLevelsQueryKeys.details()
      });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useCreateReservationItem = (options) => {
  return useMutation({
    mutationFn: (payload) => sdk.admin.reservation.create(payload),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({
        queryKey: reservationItemsQueryKeys.lists()
      });
      queryClient.invalidateQueries({
        queryKey: inventoryItemsQueryKeys.details()
      });
      queryClient.invalidateQueries({
        queryKey: inventoryItemLevelsQueryKeys.details()
      });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useDeleteReservationItem = (id, options) => {
  return useMutation({
    mutationFn: () => sdk.admin.reservation.delete(id),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({
        queryKey: reservationItemsQueryKeys.lists()
      });
      queryClient.invalidateQueries({
        queryKey: reservationItemsQueryKeys.detail(id)
      });
      queryClient.invalidateQueries({
        queryKey: inventoryItemsQueryKeys.details()
      });
      queryClient.invalidateQueries({
        queryKey: inventoryItemLevelsQueryKeys.details()
      });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};

export {
  reservationItemsQueryKeys,
  useReservationItem,
  useReservationItems,
  useUpdateReservationItem,
  useCreateReservationItem,
  useDeleteReservationItem
};
//# sourceMappingURL=chunk-UEJFWAFE.js.map
