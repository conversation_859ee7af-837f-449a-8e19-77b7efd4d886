import "./chunk-EGRHWZRV.js";
import {
  KeyboundForm
} from "./chunk-DP54EP6X.js";
import {
  RouteDrawer,
  useRouteModal
} from "./chunk-MVVOBQIC.js";
import {
  objectType,
  stringType
} from "./chunk-4XXECALA.js";
import {
  Form,
  useForm
} from "./chunk-XXJU43CK.js";
import {
  useUpdateFulfillmentSetServiceZone
} from "./chunk-KI3HEITX.js";
import "./chunk-MSQ25CWB.js";
import {
  useStockLocation
} from "./chunk-ONYSAQ5Z.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import "./chunk-JFCIYOH4.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-5GF3RGIE.js";
import {
  json,
  useParams
} from "./chunk-T7YBVUWZ.js";
import {
  But<PERSON>,
  Heading,
  InlineTip,
  Input,
  toast
} from "./chunk-YP2LLXWB.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-ZSQ362B4.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/location-service-zone-edit-74Q2BX26.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var EditServiceZoneSchema = objectType({
  name: stringType().min(1)
});
var EditServiceZoneForm = ({
  zone,
  fulfillmentSetId,
  locationId
}) => {
  const { t } = useTranslation();
  const { handleSuccess } = useRouteModal();
  const form = useForm({
    defaultValues: {
      name: zone.name
    }
  });
  const { mutateAsync, isPending: isLoading } = useUpdateFulfillmentSetServiceZone(fulfillmentSetId, zone.id);
  const handleSubmit = form.handleSubmit(async (values) => {
    await mutateAsync(
      {
        name: values.name
      },
      {
        onSuccess: () => {
          toast.success(
            t("stockLocations.serviceZones.edit.successToast", {
              name: values.name
            })
          );
          handleSuccess(`/settings/locations/${locationId}`);
        },
        onError: (e) => {
          toast.error(e.message);
        }
      }
    );
  });
  return (0, import_jsx_runtime.jsx)(RouteDrawer.Form, { form, children: (0, import_jsx_runtime.jsxs)(
    KeyboundForm,
    {
      onSubmit: handleSubmit,
      className: "flex size-full flex-col overflow-hidden",
      children: [
        (0, import_jsx_runtime.jsx)(RouteDrawer.Body, { className: "flex-1 overflow-auto", children: (0, import_jsx_runtime.jsxs)("div", { className: "flex flex-col gap-y-8", children: [
          (0, import_jsx_runtime.jsx)("div", { className: "flex flex-col gap-y-4", children: (0, import_jsx_runtime.jsx)(
            Form.Field,
            {
              control: form.control,
              name: "name",
              render: ({ field }) => {
                return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                  (0, import_jsx_runtime.jsx)(Form.Label, { children: t("fields.name") }),
                  (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(Input, { ...field }) }),
                  (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                ] });
              }
            }
          ) }),
          (0, import_jsx_runtime.jsx)(InlineTip, { label: t("general.tip"), children: t("stockLocations.serviceZones.fields.tip") })
        ] }) }),
        (0, import_jsx_runtime.jsx)(RouteDrawer.Footer, { children: (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center gap-x-2", children: [
          (0, import_jsx_runtime.jsx)(RouteDrawer.Close, { asChild: true, children: (0, import_jsx_runtime.jsx)(Button, { size: "small", variant: "secondary", children: t("actions.cancel") }) }),
          (0, import_jsx_runtime.jsx)(Button, { size: "small", type: "submit", isLoading, children: t("actions.save") })
        ] }) })
      ]
    }
  ) });
};
var LocationServiceZoneEdit = () => {
  var _a, _b;
  const { t } = useTranslation();
  const { location_id, fset_id, zone_id } = useParams();
  const { stock_location, isPending, isFetching, isError, error } = useStockLocation(location_id, {
    fields: "*fulfillment_sets.service_zones"
  });
  const serviceZone = (_b = (_a = stock_location == null ? void 0 : stock_location.fulfillment_sets) == null ? void 0 : _a.find((f) => f.id === fset_id)) == null ? void 0 : _b.service_zones.find((z) => z.id === zone_id);
  if (!isPending && !isFetching && !serviceZone) {
    throw json(
      { message: `Service zone with ID ${zone_id} was not found` },
      404
    );
  }
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime2.jsxs)(RouteDrawer, { prev: `/settings/locations/${location_id}`, children: [
    (0, import_jsx_runtime2.jsx)(RouteDrawer.Header, { children: (0, import_jsx_runtime2.jsx)(Heading, { children: t("stockLocations.serviceZones.edit.header") }) }),
    serviceZone && (0, import_jsx_runtime2.jsx)(
      EditServiceZoneForm,
      {
        zone: serviceZone,
        fulfillmentSetId: fset_id,
        locationId: location_id
      }
    )
  ] });
};
export {
  LocationServiceZoneEdit as Component
};
//# sourceMappingURL=location-service-zone-edit-74Q2BX26-6D6T7ISO.js.map
