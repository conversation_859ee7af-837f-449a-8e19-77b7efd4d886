{"version": 3, "sources": ["../../@hookform/resolvers/src/validateFieldsNatively.ts", "../../@hookform/resolvers/src/toNestErrors.ts", "../../@hookform/resolvers/zod/src/zod.ts"], "sourcesContent": ["import {\n  get,\n  FieldError,\n  ResolverOptions,\n  Ref,\n  FieldErrors,\n  FieldValues,\n} from 'react-hook-form';\n\nconst setCustomValidity = (\n  ref: Ref,\n  fieldPath: string,\n  errors: FieldErrors,\n) => {\n  if (ref && 'reportValidity' in ref) {\n    const error = get(errors, fieldPath) as FieldError | undefined;\n    ref.setCustomValidity((error && error.message) || '');\n\n    ref.reportValidity();\n  }\n};\n\n// Native validation (web only)\nexport const validateFieldsNatively = <TFieldValues extends FieldValues>(\n  errors: FieldErrors,\n  options: ResolverOptions<TFieldValues>,\n): void => {\n  for (const fieldPath in options.fields) {\n    const field = options.fields[fieldPath];\n    if (field && field.ref && 'reportValidity' in field.ref) {\n      setCustomValidity(field.ref, fieldPath, errors);\n    } else if (field.refs) {\n      field.refs.forEach((ref: HTMLInputElement) =>\n        setCustomValidity(ref, fieldPath, errors),\n      );\n    }\n  }\n};\n", "import {\n  get,\n  FieldErrors,\n  Field,\n  ResolverOptions,\n  FieldValues,\n  InternalFieldName,\n} from 'react-hook-form';\nimport { validateFieldsNatively } from './validateFieldsNatively';\n\nexport const isDateObject = (value: unknown): value is Date => value instanceof Date;\n\nexport const isNullOrUndefined = (value: unknown): value is null | undefined => value == null;\n\nexport const isObjectType = (value: unknown): value is object =>\n  typeof value === 'object';\n\nexport const isObject = <T extends object>(value: unknown): value is T =>\n  !isNullOrUndefined(value) &&\n  !Array.isArray(value) &&\n  isObjectType(value) &&\n  !isDateObject(value);\n\nexport const isKey = (value: string) => /^\\w*$/.test(value);\n\nconst compact = <TValue>(value: TValue[]) =>\n  Array.isArray(value) ? value.filter(Boolean) : [];\n\nconst stringToPath = (input: string): string[] =>\n  compact(input.replace(/[\"|']|\\]/g, '').split(/\\.|\\[/));\n\nconst set = (object: FieldValues, path: string, value?: unknown) => {\n  let index = -1;\n  const tempPath = isKey(path) ? [path] : stringToPath(path);\n  const length = tempPath.length;\n  const lastIndex = length - 1;\n\n  while (++index < length) {\n    const key = tempPath[index];\n    let newValue = value;\n\n    if (index !== lastIndex) {\n      const objValue = object[key];\n      newValue =\n        isObject(objValue) || Array.isArray(objValue)\n          ? objValue\n          : !isNaN(+tempPath[index + 1])\n            ? []\n            : {};\n    }\n    object[key] = newValue;\n    object = object[key];\n  }\n  return object;\n};\n\n\nexport const toNestErrors = <TFieldValues extends FieldValues>(\n  errors: FieldErrors,\n  options: ResolverOptions<TFieldValues>,\n): FieldErrors<TFieldValues> => {\n  options.shouldUseNativeValidation && validateFieldsNatively(errors, options);\n\n  const fieldErrors = {} as FieldErrors<TFieldValues>;\n  for (const path in errors) {\n    const field = get(options.fields, path) as Field['_f'] | undefined;\n    const error = Object.assign(errors[path] || {}, {\n      ref: field && field.ref,\n    });\n\n    if (isNameInFieldArray(options.names || Object.keys(errors), path)) {\n      const fieldArrayErrors = Object.assign({}, get(fieldErrors, path));\n\n      set(fieldArrayErrors, 'root', error);\n      set(fieldErrors, path, fieldArrayErrors);\n    } else {\n      set(fieldErrors, path, error);\n    }\n  }\n\n  return fieldErrors;\n};\n\nconst isNameInFieldArray = (\n  names: InternalFieldName[],\n  name: InternalFieldName,\n) => names.some((n) => n.startsWith(name + '.'));\n", "import { appendErrors, FieldError, FieldErrors } from 'react-hook-form';\nimport { z, ZodError } from 'zod';\nimport { toNestErrors, validateFieldsNatively } from '@hookform/resolvers';\nimport type { Resolver } from './types';\n\nconst isZodError = (error: any): error is ZodError => error.errors != null;\n\nconst parseErrorSchema = (\n  zodErrors: z.ZodIssue[],\n  validateAllFieldCriteria: boolean,\n) => {\n  const errors: Record<string, FieldError> = {};\n  for (; zodErrors.length; ) {\n    const error = zodErrors[0];\n    const { code, message, path } = error;\n    const _path = path.join('.');\n\n    if (!errors[_path]) {\n      if ('unionErrors' in error) {\n        const unionError = error.unionErrors[0].errors[0];\n\n        errors[_path] = {\n          message: unionError.message,\n          type: unionError.code,\n        };\n      } else {\n        errors[_path] = { message, type: code };\n      }\n    }\n\n    if ('unionErrors' in error) {\n      error.unionErrors.forEach((unionError) =>\n        unionError.errors.forEach((e) => zodErrors.push(e)),\n      );\n    }\n\n    if (validateAllFieldCriteria) {\n      const types = errors[_path].types;\n      const messages = types && types[error.code];\n\n      errors[_path] = appendErrors(\n        _path,\n        validateAllFieldCriteria,\n        errors,\n        code,\n        messages\n          ? ([] as string[]).concat(messages as string[], error.message)\n          : error.message,\n      ) as FieldError;\n    }\n\n    zodErrors.shift();\n  }\n\n  return errors;\n};\n\nexport const zodResolver: Resolver =\n  (schema, schemaOptions, resolverOptions = {}) =>\n  async (values, _, options) => {\n    try {\n      const data = await schema[\n        resolverOptions.mode === 'sync' ? 'parse' : 'parseAsync'\n      ](values, schemaOptions);\n\n      options.shouldUseNativeValidation && validateFieldsNatively({}, options);\n\n      return {\n        errors: {} as FieldErrors,\n        values: resolverOptions.raw ? values : data,\n      };\n    } catch (error: any) {\n      if (isZodError(error)) {\n        return {\n          values: {},\n          errors: toNestErrors(\n            parseErrorSchema(\n              error.errors,\n              !options.shouldUseNativeValidation &&\n                options.criteriaMode === 'all',\n            ),\n            options,\n          ),\n        };\n      }\n\n      throw error;\n    }\n  };\n"], "mappings": ";;;;;;AASA,IAAMA,IAAoB,SACxBC,IACAC,IACAC,IAAAA;AAEA,MAAIF,MAAO,oBAAoBA,IAAK;AAClC,QAAMG,KAAQC,IAAIF,IAAQD,EAAAA;AAC1BD,IAAAA,GAAID,kBAAmBI,MAASA,GAAME,WAAY,EAAA,GAElDL,GAAIM,eAAAA;EACL;AACH;AAXA,IAcaC,IAAyB,SACpCL,GACAM,IAAAA;AACQ,MAAAC,KAAAA,SAAAR,IAAAA;AAEN,QAAMS,KAAQF,GAAQG,OAAOV,EAAAA;AACzBS,IAAAA,MAASA,GAAMV,OAAO,oBAAoBU,GAAMV,MAClDD,EAAkBW,GAAMV,KAAKC,IAAWC,CAAAA,IAC/BQ,GAAME,QACfF,GAAME,KAAKC,QAAQ,SAACb,IAAAA;AAAqB,aACvCD,EAAkBC,IAAKC,IAAWC,CAAAA;IAAO,CAAA;EAG9C;AATD,WAAWD,MAAaO,GAAQG,OAAMF,CAAAA,GAAAR,EAAAA;AAUxC;AA5BA,ICCaa,IAAe,SAACC,GAAAA;AAAkC,SAAAA,aAAiBC;AAAI;ADDpF,ICGaC,IAAoB,SAACF,GAAAA;AAA8C,SAAS,QAATA;AAAa;ADH7F,ICKaG,IAAe,SAACH,GAAAA;AAC3B,SAAiB,YAAA,OAAVA;AAAkB;ADN3B,ICQaI,IAAW,SAAmBJ,GAAAA;AACzC,SAAA,CAACE,EAAkBF,CAAAA,KAAAA,CAClBK,MAAMC,QAAQN,CAAAA,KACfG,EAAaH,CAAAA,KAAAA,CACZD,EAAaC,CAAAA;AAAM;ADZtB,ICcaO,IAAQ,SAACP,GAAAA;AAAa,SAAY,QAACQ,KAAKR,CAAAA;AAAM;ADd3D,ICsBMS,IAAM,SAACC,GAAqBC,IAAcX,IAAAA;AAM9C,WALIY,KAAAA,IACEC,KAAWN,EAAMI,EAAAA,IAAQ,CAACA,EAAAA,IALb,SAACG,IAAAA;AACpB,WAJuBd,KAIfc,GAAMC,QAAQ,aAAa,EAAA,EAAIC,MAAM,OAAA,GAH7CX,MAAMC,QAAQN,EAAAA,IAASA,GAAMiB,OAAOC,OAAAA,IAAW,CAAA;AADjC,QAASlB;EAI+B,EAIDW,EAAAA,GAC/CQ,KAASN,GAASM,QAClBC,KAAYD,KAAS,GAAA,EAElBP,KAAQO,MAAQ;AACvB,QAAME,KAAMR,GAASD,EAAAA,GACjBU,KAAWtB;AAEf,QAAIY,OAAUQ,IAAW;AACvB,UAAMG,IAAWb,EAAOW,EAAAA;AACxBC,MAAAA,KACElB,EAASmB,CAAAA,KAAalB,MAAMC,QAAQiB,CAAAA,IAChCA,IACCC,MAAAA,CAAOX,GAASD,KAAQ,CAAA,CAAA,IAEvB,CAAA,IADA,CAAA;IAET;AACDF,MAAOW,EAAAA,IAAOC,IACdZ,IAASA,EAAOW,EAAAA;EACjB;AACD,SAAOX;AACT;AD7CA,ICgDae,IAAe,SAC1BtC,IACAM,IAAAA;AAEAA,EAAAA,GAAQiC,6BAA6BlC,EAAuBL,IAAQM,EAAAA;AAEpE,MAAMkC,KAAc,CAAA;AACpB,WAAWhB,MAAQxB,IAAQ;AACzB,QAAMQ,KAAQN,IAAII,GAAQG,QAAQe,EAAAA,GAC5BvB,KAAQwC,OAAOC,OAAO1C,GAAOwB,EAAAA,KAAS,CAAE,GAAE,EAC9C1B,KAAKU,MAASA,GAAMV,IAAAA,CAAAA;AAGtB,QAAI6C,EAAmBrC,GAAQsC,SAASH,OAAOI,KAAK7C,EAAAA,GAASwB,EAAAA,GAAO;AAClE,UAAMsB,KAAmBL,OAAOC,OAAO,CAAA,GAAIxC,IAAIsC,IAAahB,EAAAA,CAAAA;AAE5DF,QAAIwB,IAAkB,QAAQ7C,EAAAA,GAC9BqB,EAAIkB,IAAahB,IAAMsB,EAAAA;IACxB,MACCxB,GAAIkB,IAAahB,IAAMvB,EAAAA;EAE1B;AAED,SAAOuC;AACT;ADxEA,IC0EMG,IAAqB,SACzBC,GACAG,IAAAA;AACG,SAAAH,EAAMI,KAAK,SAACC,IAAAA;AAAM,WAAAA,GAAEC,WAAWH,KAAO,GAAA;EAAI,CAAA;AAAC;;;ACjFhD,IAEMI,KAAmB,SACvBC,IACAC,IAAAA;AAGA,WADMC,KAAqC,CAAA,GACpCF,GAAUG,UAAU;AACzB,QAAMC,KAAQJ,GAAU,CAAA,GAChBK,KAAwBD,GAAxBC,MAAMC,KAAkBF,GAAlBE,SACRC,KAD0BH,GAATI,KACJC,KAAK,GAAA;AAExB,QAAA,CAAKP,GAAOK,EAAAA,EACV,KAAI,iBAAiBH,IAAO;AAC1B,UAAMM,KAAaN,GAAMO,YAAY,CAAA,EAAGT,OAAO,CAAA;AAE/CA,MAAAA,GAAOK,EAAAA,IAAS,EACdD,SAASI,GAAWJ,SACpBM,MAAMF,GAAWL,KAAAA;IAEpB,MACCH,CAAAA,GAAOK,EAAAA,IAAS,EAAED,SAAAA,IAASM,MAAMP,GAAAA;AAUrC,QANI,iBAAiBD,MACnBA,GAAMO,YAAYE,QAAQ,SAACH,GAAAA;AACzB,aAAAA,EAAWR,OAAOW,QAAQ,SAACC,IAAAA;AAAC,eAAKd,GAAUe,KAAKD,EAAAA;MAAE,CAAA;IAAC,CAAA,GAInDb,IAA0B;AAC5B,UAAMe,KAAQd,GAAOK,EAAAA,EAAOS,OACtBC,KAAWD,MAASA,GAAMZ,GAAMC,IAAAA;AAEtCH,MAAAA,GAAOK,EAAAA,IAASW,aACdX,IACAN,IACAC,IACAG,IACAY,KACK,CAAA,EAAgBE,OAAOF,IAAsBb,GAAME,OAAAA,IACpDF,GAAME,OAAAA;IAEb;AAEDN,IAAAA,GAAUoB,MAAAA;EACX;AAED,SAAOlB;AACT;AAlDA,IAoDamB,KACX,SAACC,GAAQC,IAAeC,IAAAA;AAAoB,SAAA,WAApBA,OAAAA,KAAkB,CAAA,IAAE,SACrCC,IAAQC,IAAGC,IAAAA;AAAO,QAAA;AAAIC,aAAAA,QAAAC,QAAAA,SAAAA,IAAAA,IAAAA;AAAAA,YAAAA;AAAAA,cAAAA,KACvBD,QAAAC,QACiBP,EACQ,WAAzBE,GAAgBM,OAAkB,UAAU,YAAA,EAC5CL,IAAQF,EAAAA,CAAAA,EAAcQ,KAAA,SAFlBC,IAAAA;AAMN,mBAFAL,GAAQM,6BAA6BC,EAAuB,CAAA,GAAIP,EAAAA,GAEzD,EACLzB,QAAQ,CAAA,GACRuB,QAAQD,GAAgBW,MAAMV,KAASO,GAAAA;UACvC,CAAA;QAAA,SAAAI,IAAA;AAAA,iBAAAC,GAAAD,EAAA;QAAA;AAAA,eAAAE,MAAAA,GAAA,OAAAA,GAAA,KAAA,QAAAD,EAAA,IAAAC;MAAA,EAXuB,GAYlBlC,SAAAA,IAAAA;AACP,YAnEa,SAACA,IAAAA;AAAU,iBAAwC,QAAhBA,GAAMF;QAAc,EAmErDE,EAAAA,EACb,QAAO,EACLqB,QAAQ,CAAA,GACRvB,QAAQqC,EACNxC,GACEK,GAAMF,QAAAA,CACLyB,GAAQM,6BACkB,UAAzBN,GAAQa,YAAAA,GAEZb,EAAAA,EAAAA;AAKN,cAAMvB;MACP,CAAA,CAAA;IACH,SAACU,IAAAA;AAAA,aAAAc,QAAAa,OAAA3B,EAAAA;IAAA;EAAA;AAAA;", "names": ["setCustomValidity", "ref", "fieldPath", "errors", "error", "get", "message", "reportValidity", "validateFieldsNatively", "options", "_loop", "field", "fields", "refs", "for<PERSON>ach", "isDateObject", "value", "Date", "isNullOrUndefined", "isObjectType", "isObject", "Array", "isArray", "is<PERSON>ey", "test", "set", "object", "path", "index", "temp<PERSON>ath", "input", "replace", "split", "filter", "Boolean", "length", "lastIndex", "key", "newValue", "objValue", "isNaN", "toNestErrors", "shouldUseNativeValidation", "fieldErrors", "Object", "assign", "isNameInFieldArray", "names", "keys", "fieldArrayErrors", "name", "some", "n", "startsWith", "parseErrorSchema", "zodErrors", "validateAllFieldCriteria", "errors", "length", "error", "code", "message", "_path", "path", "join", "unionError", "unionErrors", "type", "for<PERSON>ach", "e", "push", "types", "messages", "appendErrors", "concat", "shift", "zodResolver", "schema", "schemaOptions", "resolverOptions", "values", "_", "options", "Promise", "resolve", "mode", "then", "data", "shouldUseNativeValidation", "validateFieldsNatively", "raw", "r", "n", "a", "toNestErrors", "criteriaMode", "reject"]}