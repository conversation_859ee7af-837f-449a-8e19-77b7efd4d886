// node_modules/@medusajs/dashboard/dist/chunk-3WXBLS2P.mjs
var formatter = new Intl.NumberFormat([], {
  style: "percent",
  minimumFractionDigits: 2
});
var formatPercentage = (value, isPercentageValue = false) => {
  let val = value || 0;
  if (!isPercentageValue) {
    val = val / 100;
  }
  return formatter.format(val);
};

export {
  formatPercentage
};
//# sourceMappingURL=chunk-XYHEMHQ5.js.map
