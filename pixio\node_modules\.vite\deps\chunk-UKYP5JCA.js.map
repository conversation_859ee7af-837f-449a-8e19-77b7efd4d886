{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-7I5DQGWY.mjs"], "sourcesContent": ["// src/routes/orders/order-detail/constants.ts\nvar DEFAULT_PROPERTIES = [\n  \"id\",\n  \"status\",\n  \"created_at\",\n  \"canceled_at\",\n  \"email\",\n  \"display_id\",\n  \"currency_code\",\n  \"metadata\",\n  // --- TOTALS ---\n  \"total\",\n  \"item_total\",\n  \"shipping_subtotal\",\n  \"subtotal\",\n  \"discount_total\",\n  \"discount_subtotal\",\n  \"shipping_total\",\n  \"shipping_tax_total\",\n  \"tax_total\",\n  \"refundable_total\",\n  \"order_change\"\n];\nvar DEFAULT_RELATIONS = [\n  \"*customer\",\n  \"*items\",\n  // -> we get LineItem here with added `quantity` and `detail` which is actually an OrderItem (which is a parent object to LineItem in the DB)\n  \"*items.variant\",\n  \"*items.variant.product\",\n  \"*items.variant.options\",\n  \"+items.variant.manage_inventory\",\n  \"*items.variant.inventory_items.inventory\",\n  \"+items.variant.inventory_items.required_quantity\",\n  \"+summary\",\n  \"*shipping_address\",\n  \"*billing_address\",\n  \"*sales_channel\",\n  \"*promotion\",\n  \"*shipping_methods\",\n  \"*fulfillments\",\n  \"+fulfillments.shipping_option.service_zone.fulfillment_set.type\",\n  \"*fulfillments.items\",\n  \"*fulfillments.labels\",\n  \"*fulfillments.labels\",\n  \"*payment_collections\",\n  \"*payment_collections.payments\",\n  \"*payment_collections.payments.refunds\",\n  \"*payment_collections.payments.refunds.refund_reason\",\n  \"region.automatic_taxes\"\n];\nvar DEFAULT_FIELDS = `${DEFAULT_PROPERTIES.join(\n  \",\"\n)},${DEFAULT_RELATIONS.join(\",\")}`;\n\nexport {\n  DEFAULT_FIELDS\n};\n"], "mappings": ";AACA,IAAI,qBAAqB;AAAA,EACvB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA,EAEA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,IAAI,oBAAoB;AAAA,EACtB;AAAA,EACA;AAAA;AAAA,EAEA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,IAAI,iBAAiB,GAAG,mBAAmB;AAAA,EACzC;AACF,CAAC,IAAI,kBAAkB,KAAK,GAAG,CAAC;", "names": []}