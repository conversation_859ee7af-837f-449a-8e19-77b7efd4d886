{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-QMRGIWOP.mjs"], "sourcesContent": ["import {\n  ListSummary\n} from \"./chunk-I3VB6NM2.mjs\";\nimport {\n  formatProvider\n} from \"./chunk-IR5DHEKS.mjs\";\nimport {\n  PlaceholderCell\n} from \"./chunk-P3UUX2T6.mjs\";\nimport {\n  countries\n} from \"./chunk-DG7J63J2.mjs\";\n\n// src/hooks/table/columns/use-region-table-columns.tsx\nimport { createColumnHelper } from \"@tanstack/react-table\";\nimport { useMemo } from \"react\";\n\n// src/components/table/table-cells/region/countries-cell/countries-cell.tsx\nimport { useTranslation } from \"react-i18next\";\nimport { jsx } from \"react/jsx-runtime\";\nvar CountriesCell = ({ countries: countries2 }) => {\n  if (!countries2 || countries2.length === 0) {\n    return /* @__PURE__ */ jsx(PlaceholderCell, {});\n  }\n  const list = countries2.map(\n    (country) => countries.find((c) => c.iso_2 === country.iso_2)?.display_name\n  ).filter(Boolean);\n  return /* @__PURE__ */ jsx(\"div\", { className: \"flex size-full items-center overflow-hidden\", children: /* @__PURE__ */ jsx(ListSummary, { list }) });\n};\nvar CountriesHeader = () => {\n  const { t } = useTranslation();\n  return /* @__PURE__ */ jsx(\"div\", { className: \"flex size-full items-center\", children: /* @__PURE__ */ jsx(\"span\", { children: t(\"fields.countries\") }) });\n};\n\n// src/components/table/table-cells/region/payment-providers-cell/payment-providers-cell.tsx\nimport { useTranslation as useTranslation2 } from \"react-i18next\";\nimport { jsx as jsx2 } from \"react/jsx-runtime\";\nvar PaymentProvidersCell = ({\n  paymentProviders\n}) => {\n  if (!paymentProviders || paymentProviders.length === 0) {\n    return /* @__PURE__ */ jsx2(PlaceholderCell, {});\n  }\n  const displayValues = paymentProviders.map((p) => formatProvider(p.id));\n  return /* @__PURE__ */ jsx2(\"div\", { className: \"flex size-full items-center overflow-hidden\", children: /* @__PURE__ */ jsx2(ListSummary, { list: displayValues }) });\n};\nvar PaymentProvidersHeader = () => {\n  const { t } = useTranslation2();\n  return /* @__PURE__ */ jsx2(\"div\", { className: \"flex size-full items-center overflow-hidden\", children: /* @__PURE__ */ jsx2(\"span\", { className: \"truncate\", children: t(\"fields.paymentProviders\") }) });\n};\n\n// src/components/table/table-cells/region/region-cell/region-cell.tsx\nimport { useTranslation as useTranslation3 } from \"react-i18next\";\nimport { jsx as jsx3 } from \"react/jsx-runtime\";\nvar RegionCell = ({ name }) => {\n  return /* @__PURE__ */ jsx3(\"div\", { className: \"flex h-full w-full items-center overflow-hidden\", children: /* @__PURE__ */ jsx3(\"span\", { className: \"truncate\", children: name }) });\n};\nvar RegionHeader = () => {\n  const { t } = useTranslation3();\n  return /* @__PURE__ */ jsx3(\"div\", { className: \"flex h-full w-full items-center\", children: /* @__PURE__ */ jsx3(\"span\", { className: \"truncate\", children: t(\"fields.name\") }) });\n};\n\n// src/hooks/table/columns/use-region-table-columns.tsx\nimport { jsx as jsx4 } from \"react/jsx-runtime\";\nvar columnHelper = createColumnHelper();\nvar useRegionTableColumns = () => {\n  return useMemo(\n    () => [\n      columnHelper.accessor(\"name\", {\n        header: () => /* @__PURE__ */ jsx4(RegionHeader, {}),\n        cell: ({ getValue }) => /* @__PURE__ */ jsx4(RegionCell, { name: getValue() })\n      }),\n      columnHelper.accessor(\"countries\", {\n        header: () => /* @__PURE__ */ jsx4(CountriesHeader, {}),\n        cell: ({ getValue }) => /* @__PURE__ */ jsx4(CountriesCell, { countries: getValue() })\n      }),\n      columnHelper.accessor(\"payment_providers\", {\n        header: () => /* @__PURE__ */ jsx4(PaymentProvidersHeader, {}),\n        cell: ({ getValue }) => /* @__PURE__ */ jsx4(PaymentProvidersCell, { paymentProviders: getValue() })\n      })\n    ],\n    []\n  );\n};\n\nexport {\n  useRegionTableColumns\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAeA,mBAAwB;AAIxB,yBAAoB;AAiBpB,IAAAA,sBAA4B;AAiB5B,IAAAC,sBAA4B;AAU5B,IAAAA,sBAA4B;AA3C5B,IAAI,gBAAgB,CAAC,EAAE,WAAW,WAAW,MAAM;AACjD,MAAI,CAAC,cAAc,WAAW,WAAW,GAAG;AAC1C,eAAuB,wBAAI,iBAAiB,CAAC,CAAC;AAAA,EAChD;AACA,QAAM,OAAO,WAAW;AAAA,IACtB,CAAC,YAAS;AAzBd;AAyBiB,6BAAU,KAAK,CAAC,MAAM,EAAE,UAAU,QAAQ,KAAK,MAA/C,mBAAkD;AAAA;AAAA,EACjE,EAAE,OAAO,OAAO;AAChB,aAAuB,wBAAI,OAAO,EAAE,WAAW,+CAA+C,cAA0B,wBAAI,aAAa,EAAE,KAAK,CAAC,EAAE,CAAC;AACtJ;AACA,IAAI,kBAAkB,MAAM;AAC1B,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,aAAuB,wBAAI,OAAO,EAAE,WAAW,+BAA+B,cAA0B,wBAAI,QAAQ,EAAE,UAAU,EAAE,kBAAkB,EAAE,CAAC,EAAE,CAAC;AAC5J;AAKA,IAAI,uBAAuB,CAAC;AAAA,EAC1B;AACF,MAAM;AACJ,MAAI,CAAC,oBAAoB,iBAAiB,WAAW,GAAG;AACtD,eAAuB,oBAAAC,KAAK,iBAAiB,CAAC,CAAC;AAAA,EACjD;AACA,QAAM,gBAAgB,iBAAiB,IAAI,CAAC,MAAM,eAAe,EAAE,EAAE,CAAC;AACtE,aAAuB,oBAAAA,KAAK,OAAO,EAAE,WAAW,+CAA+C,cAA0B,oBAAAA,KAAK,aAAa,EAAE,MAAM,cAAc,CAAC,EAAE,CAAC;AACvK;AACA,IAAI,yBAAyB,MAAM;AACjC,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,aAAuB,oBAAAA,KAAK,OAAO,EAAE,WAAW,+CAA+C,cAA0B,oBAAAA,KAAK,QAAQ,EAAE,WAAW,YAAY,UAAU,EAAE,yBAAyB,EAAE,CAAC,EAAE,CAAC;AAC5M;AAKA,IAAI,aAAa,CAAC,EAAE,KAAK,MAAM;AAC7B,aAAuB,oBAAAC,KAAK,OAAO,EAAE,WAAW,mDAAmD,cAA0B,oBAAAA,KAAK,QAAQ,EAAE,WAAW,YAAY,UAAU,KAAK,CAAC,EAAE,CAAC;AACxL;AACA,IAAI,eAAe,MAAM;AACvB,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,aAAuB,oBAAAA,KAAK,OAAO,EAAE,WAAW,mCAAmC,cAA0B,oBAAAA,KAAK,QAAQ,EAAE,WAAW,YAAY,UAAU,EAAE,aAAa,EAAE,CAAC,EAAE,CAAC;AACpL;AAIA,IAAI,eAAe,mBAAmB;AACtC,IAAI,wBAAwB,MAAM;AAChC,aAAO;AAAA,IACL,MAAM;AAAA,MACJ,aAAa,SAAS,QAAQ;AAAA,QAC5B,QAAQ,UAAsB,oBAAAC,KAAK,cAAc,CAAC,CAAC;AAAA,QACnD,MAAM,CAAC,EAAE,SAAS,UAAsB,oBAAAA,KAAK,YAAY,EAAE,MAAM,SAAS,EAAE,CAAC;AAAA,MAC/E,CAAC;AAAA,MACD,aAAa,SAAS,aAAa;AAAA,QACjC,QAAQ,UAAsB,oBAAAA,KAAK,iBAAiB,CAAC,CAAC;AAAA,QACtD,MAAM,CAAC,EAAE,SAAS,UAAsB,oBAAAA,KAAK,eAAe,EAAE,WAAW,SAAS,EAAE,CAAC;AAAA,MACvF,CAAC;AAAA,MACD,aAAa,SAAS,qBAAqB;AAAA,QACzC,QAAQ,UAAsB,oBAAAA,KAAK,wBAAwB,CAAC,CAAC;AAAA,QAC7D,MAAM,CAAC,EAAE,SAAS,UAAsB,oBAAAA,KAAK,sBAAsB,EAAE,kBAAkB,SAAS,EAAE,CAAC;AAAA,MACrG,CAAC;AAAA,IACH;AAAA,IACA,CAAC;AAAA,EACH;AACF;", "names": ["import_jsx_runtime", "import_jsx_runtime", "jsx2", "jsx3", "jsx4"]}