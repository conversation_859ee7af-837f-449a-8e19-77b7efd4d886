import {
  FileUpload
} from "./chunk-WDXTIEQI.js";
import {
  RouteDrawer,
  useRouteModal
} from "./chunk-MVVOBQIC.js";
import {
  FilePreview
} from "./chunk-EIZ27OVL.js";
import "./chunk-CFRQOB2M.js";
import "./chunk-66DVUN72.js";
import "./chunk-XXJU43CK.js";
import "./chunk-WISME5HP.js";
import "./chunk-WAUSOHFQ.js";
import "./chunk-3ORK3QZ4.js";
import "./chunk-3A5TVVNI.js";
import "./chunk-EZ62MM7J.js";
import "./chunk-3R6LYIXM.js";
import "./chunk-2P2O4TMQ.js";
import "./chunk-KI3HEITX.js";
import "./chunk-2VUCAO7L.js";
import "./chunk-PTIULTKD.js";
import "./chunk-KISXY2LD.js";
import "./chunk-L7C3XPHJ.js";
import "./chunk-T7MG2EIJ.js";
import "./chunk-MER4VYPR.js";
import "./chunk-2AWKOUCD.js";
import "./chunk-YL3TYBFR.js";
import "./chunk-TZWW72YW.js";
import "./chunk-MSQ25CWB.js";
import "./chunk-ONYSAQ5Z.js";
import "./chunk-NIH7SAXN.js";
import "./chunk-UEJFWAFE.js";
import "./chunk-GR55MFKT.js";
import "./chunk-AAFHKNJG.js";
import "./chunk-7XDBFDTZ.js";
import "./chunk-3JKGO5XL.js";
import "./chunk-OISUTS7G.js";
import {
  useConfirmImportProducts,
  useImportProducts
} from "./chunk-AJYMIHLQ.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import "./chunk-JFCIYOH4.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-5GF3RGIE.js";
import "./chunk-T7YBVUWZ.js";
import {
  Button,
  Divider,
  Heading,
  Hint,
  Text,
  Trash,
  toast
} from "./chunk-YP2LLXWB.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-ZSQ362B4.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/product-import-43YYSHGQ.mjs
var import_react = __toESM(require_react(), 1);
var import_react2 = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var SUPPORTED_FORMATS = ["text/csv"];
var SUPPORTED_FORMATS_FILE_EXTENSIONS = [".csv"];
var UploadImport = ({
  onUploaded
}) => {
  const { t } = useTranslation();
  const [error, setError] = (0, import_react2.useState)();
  const hasInvalidFiles = (fileList) => {
    const invalidFile = fileList.find(
      (f) => !SUPPORTED_FORMATS.includes(f.file.type)
    );
    if (invalidFile) {
      setError(
        t("products.media.invalidFileType", {
          name: invalidFile.file.name,
          types: SUPPORTED_FORMATS_FILE_EXTENSIONS.join(", ")
        })
      );
      return true;
    }
    return false;
  };
  return (0, import_jsx_runtime.jsxs)("div", { className: "flex flex-col gap-y-4", children: [
    (0, import_jsx_runtime.jsx)(
      FileUpload,
      {
        label: t("products.import.uploadLabel"),
        hint: t("products.import.uploadHint"),
        multiple: false,
        hasError: !!error,
        formats: SUPPORTED_FORMATS,
        onUploaded: (files) => {
          setError(void 0);
          if (hasInvalidFiles(files)) {
            return;
          }
          onUploaded(files[0].file);
        }
      }
    ),
    error && (0, import_jsx_runtime.jsx)("div", { children: (0, import_jsx_runtime.jsx)(Hint, { variant: "error", children: error }) })
  ] });
};
var ImportSummary = ({
  summary
}) => {
  const { t } = useTranslation();
  return (0, import_jsx_runtime2.jsxs)("div", { className: "shadow-elevation-card-rest bg-ui-bg-component transition-fg flex flex-row rounded-md px-3 py-2", children: [
    (0, import_jsx_runtime2.jsx)(
      Stat,
      {
        title: summary.toCreate.toLocaleString(),
        description: t("products.import.upload.productsToCreate")
      }
    ),
    (0, import_jsx_runtime2.jsx)(Divider, { orientation: "vertical", className: "h-10 px-3" }),
    (0, import_jsx_runtime2.jsx)(
      Stat,
      {
        title: summary.toUpdate.toLocaleString(),
        description: t("products.import.upload.productsToUpdate")
      }
    )
  ] });
};
var Stat = ({
  title,
  description
}) => {
  return (0, import_jsx_runtime2.jsxs)("div", { className: "flex flex-1 flex-col justify-center", children: [
    (0, import_jsx_runtime2.jsx)(Text, { size: "xlarge", className: "font-sans font-medium", children: title }),
    (0, import_jsx_runtime2.jsx)(
      Text,
      {
        leading: "compact",
        size: "xsmall",
        weight: "plus",
        className: "text-ui-fg-subtle",
        children: description
      }
    )
  ] });
};
var ProductImportCSV = `data:text/csv;charset=utf-8,Product Id;Product Handle;Product Title;Product Subtitle;Product Description;Product Status;Product Thumbnail;Product Weight;Product Length;Product Width;Product Height;Product HS Code;Product Origin Country;Product MID Code;Product Material;Product Collection Title;Product Collection Handle;Product Type;Product Tags;Product Discountable;Product External Id;Product Profile Name;Product Profile Type;Variant Id;Variant Title;Variant SKU;Variant Barcode;Variant Inventory Quantity;Variant Allow Backorder;Variant Manage Inventory;Variant Weight;Variant Length;Variant Width;Variant Height;Variant HS Code;Variant Origin Country;Variant MID Code;Variant Material;Price EUR;Price USD;Option 1 Name;Option 1 Value;Image 1 Url;Image 2 Url
;coffee-mug-v2;Medusa Coffee Mug;;Every programmer's best friend.;published;https://medusa-public-images.s3.eu-west-1.amazonaws.com/coffee-mug.png;400;;;;;;;;;;;;true;;;;;One Size;;;100;false;true;;;;;;;;;1000;1200;Size;One Size;https://medusa-public-images.s3.eu-west-1.amazonaws.com/coffee-mug.png;
;sweatpants-v2;Medusa Sweatpants;;Reimagine the feeling of classic sweatpants. With our cotton sweatpants, everyday essentials no longer have to be ordinary.;published;https://medusa-public-images.s3.eu-west-1.amazonaws.com/sweatpants-gray-front.png;400;;;;;;;;;;;;true;;;;;S;;;100;false;true;;;;;;;;;2950;3350;Size;S;https://medusa-public-images.s3.eu-west-1.amazonaws.com/sweatpants-gray-front.png;https://medusa-public-images.s3.eu-west-1.amazonaws.com/sweatpants-gray-back.png
;sweatpants-v2;Medusa Sweatpants;;Reimagine the feeling of classic sweatpants. With our cotton sweatpants, everyday essentials no longer have to be ordinary.;published;https://medusa-public-images.s3.eu-west-1.amazonaws.com/sweatpants-gray-front.png;400;;;;;;;;;;;;true;;;;;M;;;100;false;true;;;;;;;;;2950;3350;Size;M;https://medusa-public-images.s3.eu-west-1.amazonaws.com/sweatpants-gray-front.png;https://medusa-public-images.s3.eu-west-1.amazonaws.com/sweatpants-gray-back.png
;sweatpants-v2;Medusa Sweatpants;;Reimagine the feeling of classic sweatpants. With our cotton sweatpants, everyday essentials no longer have to be ordinary.;published;https://medusa-public-images.s3.eu-west-1.amazonaws.com/sweatpants-gray-front.png;400;;;;;;;;;;;;true;;;;;L;;;100;false;true;;;;;;;;;2950;3350;Size;L;https://medusa-public-images.s3.eu-west-1.amazonaws.com/sweatpants-gray-front.png;https://medusa-public-images.s3.eu-west-1.amazonaws.com/sweatpants-gray-back.png
;sweatpants-v2;Medusa Sweatpants;;Reimagine the feeling of classic sweatpants. With our cotton sweatpants, everyday essentials no longer have to be ordinary.;published;https://medusa-public-images.s3.eu-west-1.amazonaws.com/sweatpants-gray-front.png;400;;;;;;;;;;;;true;;;;;XL;;;100;false;true;;;;;;;;;2950;3350;Size;XL;https://medusa-public-images.s3.eu-west-1.amazonaws.com/sweatpants-gray-front.png;https://medusa-public-images.s3.eu-west-1.amazonaws.com/sweatpants-gray-back.png
`;
var getProductImportCsvTemplate = () => {
  return encodeURI(ProductImportCSV);
};
var ProductImport = () => {
  const { t } = useTranslation();
  return (0, import_jsx_runtime3.jsxs)(RouteDrawer, { children: [
    (0, import_jsx_runtime3.jsxs)(RouteDrawer.Header, { children: [
      (0, import_jsx_runtime3.jsx)(RouteDrawer.Title, { asChild: true, children: (0, import_jsx_runtime3.jsx)(Heading, { children: t("products.import.header") }) }),
      (0, import_jsx_runtime3.jsx)(RouteDrawer.Description, { className: "sr-only", children: t("products.import.description") })
    ] }),
    (0, import_jsx_runtime3.jsx)(ProductImportContent, {})
  ] });
};
var ProductImportContent = () => {
  const { t } = useTranslation();
  const [filename, setFilename] = (0, import_react.useState)();
  const { mutateAsync: importProducts, isPending, data } = useImportProducts();
  const { mutateAsync: confirm } = useConfirmImportProducts();
  const { handleSuccess } = useRouteModal();
  const productImportTemplateContent = (0, import_react.useMemo)(() => {
    return getProductImportCsvTemplate();
  }, []);
  const handleUploaded = async (file) => {
    setFilename(file.name);
    await importProducts(
      { file },
      {
        onError: (err) => {
          toast.error(err.message);
          setFilename(void 0);
        }
      }
    );
  };
  const handleConfirm = async () => {
    if (!(data == null ? void 0 : data.transaction_id)) {
      return;
    }
    await confirm(data.transaction_id, {
      onSuccess: () => {
        toast.info(t("products.import.success.title"), {
          description: t("products.import.success.description")
        });
        handleSuccess();
      },
      onError: (err) => {
        toast.error(err.message);
      }
    });
  };
  const uploadedFileActions = [
    {
      actions: [
        {
          label: t("actions.delete"),
          icon: (0, import_jsx_runtime3.jsx)(Trash, {}),
          onClick: () => setFilename(void 0)
        }
      ]
    }
  ];
  return (0, import_jsx_runtime3.jsxs)(import_jsx_runtime3.Fragment, { children: [
    (0, import_jsx_runtime3.jsxs)(RouteDrawer.Body, { children: [
      (0, import_jsx_runtime3.jsx)(Heading, { level: "h2", children: t("products.import.upload.title") }),
      (0, import_jsx_runtime3.jsx)(Text, { size: "small", className: "text-ui-fg-subtle", children: t("products.import.upload.description") }),
      (0, import_jsx_runtime3.jsx)("div", { className: "mt-4", children: filename ? (0, import_jsx_runtime3.jsx)(
        FilePreview,
        {
          filename,
          loading: isPending,
          activity: t("products.import.upload.preprocessing"),
          actions: uploadedFileActions
        }
      ) : (0, import_jsx_runtime3.jsx)(UploadImport, { onUploaded: handleUploaded }) }),
      (data == null ? void 0 : data.summary) && !!filename && (0, import_jsx_runtime3.jsx)("div", { className: "mt-4", children: (0, import_jsx_runtime3.jsx)(ImportSummary, { summary: data == null ? void 0 : data.summary }) }),
      (0, import_jsx_runtime3.jsx)(Heading, { className: "mt-6", level: "h2", children: t("products.import.template.title") }),
      (0, import_jsx_runtime3.jsx)(Text, { size: "small", className: "text-ui-fg-subtle", children: t("products.import.template.description") }),
      (0, import_jsx_runtime3.jsx)("div", { className: "mt-4", children: (0, import_jsx_runtime3.jsx)(
        FilePreview,
        {
          filename: "product-import-template.csv",
          url: productImportTemplateContent
        }
      ) })
    ] }),
    (0, import_jsx_runtime3.jsx)(RouteDrawer.Footer, { children: (0, import_jsx_runtime3.jsxs)("div", { className: "flex items-center gap-x-2", children: [
      (0, import_jsx_runtime3.jsx)(RouteDrawer.Close, { asChild: true, children: (0, import_jsx_runtime3.jsx)(Button, { size: "small", variant: "secondary", children: t("actions.cancel") }) }),
      (0, import_jsx_runtime3.jsx)(
        Button,
        {
          onClick: handleConfirm,
          size: "small",
          disabled: !(data == null ? void 0 : data.transaction_id) || !filename,
          children: t("actions.import")
        }
      )
    ] }) })
  ] });
};
export {
  ProductImport as Component
};
//# sourceMappingURL=product-import-43YYSHGQ-OU4PY4H6.js.map
