{"version": 3, "sources": ["../../@medusajs/dashboard/dist/customers-add-customer-group-IPTB4EZQ.mjs"], "sourcesContent": ["import {\n  useCustomerGroupTableColumns\n} from \"./chunk-ZJRFL6ZN.mjs\";\nimport {\n  useCustomerGroupTableQuery\n} from \"./chunk-MOSRJHJ3.mjs\";\nimport \"./chunk-MSDRGCRR.mjs\";\nimport \"./chunk-LQTHYS2Z.mjs\";\nimport \"./chunk-P3UUX2T6.mjs\";\nimport {\n  _DataTable,\n  useDataTable\n} from \"./chunk-UE6PO4FK.mjs\";\nimport \"./chunk-YEDAFXMB.mjs\";\nimport \"./chunk-AOFGTNG6.mjs\";\nimport \"./chunk-EMIHDNB7.mjs\";\nimport {\n  useCustomerGroupTableFilters\n} from \"./chunk-DLZWPHHO.mjs\";\nimport \"./chunk-SXYXTC2L.mjs\";\nimport \"./chunk-M3VFKDXJ.mjs\";\nimport \"./chunk-C76H5USB.mjs\";\nimport \"./chunk-KSV3NQOT.mjs\";\nimport \"./chunk-IUCDCPJU.mjs\";\nimport {\n  KeyboundForm\n} from \"./chunk-6HTZNHPT.mjs\";\nimport {\n  RouteFocusModal,\n  useRouteModal\n} from \"./chunk-4TC5YS65.mjs\";\nimport \"./chunk-LPEUYMRK.mjs\";\nimport \"./chunk-OC7BQLYI.mjs\";\nimport \"./chunk-NYXYHZE6.mjs\";\nimport \"./chunk-OBQI23QM.mjs\";\nimport \"./chunk-Z5UDPQIH.mjs\";\nimport \"./chunk-KOSCMAIC.mjs\";\nimport \"./chunk-X6DSNTTX.mjs\";\nimport \"./chunk-I6E6CALJ.mjs\";\nimport \"./chunk-B4GODIOW.mjs\";\nimport \"./chunk-F6IJV2I2.mjs\";\nimport \"./chunk-QTCZFYFH.mjs\";\nimport \"./chunk-ENV6YVOM.mjs\";\nimport \"./chunk-PIR2H25N.mjs\";\nimport \"./chunk-RLY2SL5E.mjs\";\nimport \"./chunk-C5LYZZZ5.mjs\";\nimport \"./chunk-2ZKVRTBW.mjs\";\nimport \"./chunk-FO3VP56P.mjs\";\nimport \"./chunk-YS65UGPC.mjs\";\nimport {\n  useBatchCustomerCustomerGroups,\n  useCustomerGroups\n} from \"./chunk-F6PXCY3N.mjs\";\nimport \"./chunk-3OHH43G6.mjs\";\nimport \"./chunk-G2H6MAK7.mjs\";\nimport \"./chunk-GRT22PE5.mjs\";\nimport \"./chunk-32IQRUVY.mjs\";\nimport \"./chunk-FNYASI54.mjs\";\nimport \"./chunk-FVC7M755.mjs\";\nimport \"./chunk-ZJ3OFMHB.mjs\";\nimport \"./chunk-PNU5HPGY.mjs\";\nimport \"./chunk-V2LANK5S.mjs\";\nimport \"./chunk-QZ6PT4QV.mjs\";\nimport \"./chunk-QL4XKIVL.mjs\";\nimport \"./chunk-6I62UDJA.mjs\";\nimport \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-RPUOO7AV.mjs\";\n\n// src/routes/customers/customers-add-customer-group/customer-add-customer-groups.tsx\nimport { useParams } from \"react-router-dom\";\n\n// src/routes/customers/customers-add-customer-group/components/add-customers-form/add-customer-groups-form.tsx\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { Button, Checkbox, Hint, Tooltip, toast } from \"@medusajs/ui\";\nimport {\n  createColumnHelper\n} from \"@tanstack/react-table\";\nimport { useEffect, useMemo, useState } from \"react\";\nimport { useForm } from \"react-hook-form\";\nimport { useTranslation } from \"react-i18next\";\nimport * as zod from \"zod\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar AddCustomerGroupsSchema = zod.object({\n  customer_group_ids: zod.array(zod.string()).min(1)\n});\nvar PAGE_SIZE = 10;\nvar AddCustomerGroupsForm = ({\n  customerId\n}) => {\n  const { t } = useTranslation();\n  const { handleSuccess } = useRouteModal();\n  const [isPending, setIsPending] = useState(false);\n  const { mutateAsync: batchCustomerCustomerGroups } = useBatchCustomerCustomerGroups(customerId);\n  const form = useForm({\n    defaultValues: {\n      customer_group_ids: []\n    },\n    resolver: zodResolver(AddCustomerGroupsSchema)\n  });\n  const { setValue } = form;\n  const [rowSelection, setRowSelection] = useState({});\n  useEffect(() => {\n    setValue(\n      \"customer_group_ids\",\n      Object.keys(rowSelection).filter((k) => rowSelection[k]),\n      {\n        shouldDirty: true,\n        shouldTouch: true\n      }\n    );\n  }, [rowSelection, setValue]);\n  const { searchParams, raw } = useCustomerGroupTableQuery({\n    pageSize: PAGE_SIZE\n  });\n  const filters = useCustomerGroupTableFilters();\n  const {\n    customer_groups,\n    count,\n    isPending: isLoading,\n    isError,\n    error\n  } = useCustomerGroups({\n    fields: \"*customers\",\n    ...searchParams\n  });\n  const updater = (fn) => {\n    const state = typeof fn === \"function\" ? fn(rowSelection) : fn;\n    const ids = Object.keys(state);\n    setValue(\"customer_group_ids\", ids, {\n      shouldDirty: true,\n      shouldTouch: true\n    });\n    setRowSelection(state);\n  };\n  const columns = useColumns();\n  const { table } = useDataTable({\n    data: customer_groups ?? [],\n    columns,\n    count,\n    enablePagination: true,\n    enableRowSelection: (row) => {\n      return !row.original.customers?.map((c) => c.id).includes(customerId);\n    },\n    getRowId: (row) => row.id,\n    pageSize: PAGE_SIZE,\n    rowSelection: {\n      state: rowSelection,\n      updater\n    }\n  });\n  const handleSubmit = form.handleSubmit(async (data) => {\n    setIsPending(true);\n    try {\n      await batchCustomerCustomerGroups({ add: data.customer_group_ids });\n      toast.success(\n        t(\"customers.groups.add.success\", {\n          groups: data.customer_group_ids.map((id) => customer_groups?.find((g) => g.id === id)).filter(Boolean).map((cg) => cg?.name)\n        })\n      );\n      handleSuccess(`/customers/${customerId}`);\n    } catch (e) {\n      toast.error(e.message);\n    } finally {\n      setIsPending(false);\n    }\n  });\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsx(RouteFocusModal.Form, { form, children: /* @__PURE__ */ jsxs(\n    KeyboundForm,\n    {\n      className: \"flex h-full flex-col overflow-hidden\",\n      onSubmit: handleSubmit,\n      children: [\n        /* @__PURE__ */ jsx(RouteFocusModal.Header, { children: /* @__PURE__ */ jsx(\"div\", { className: \"flex items-center justify-end gap-x-2\", children: form.formState.errors.customer_group_ids && /* @__PURE__ */ jsx(Hint, { variant: \"error\", children: form.formState.errors.customer_group_ids.message }) }) }),\n        /* @__PURE__ */ jsx(RouteFocusModal.Body, { className: \"size-full overflow-hidden\", children: /* @__PURE__ */ jsx(\n          _DataTable,\n          {\n            table,\n            columns,\n            pageSize: PAGE_SIZE,\n            count,\n            filters,\n            orderBy: [\n              { key: \"name\", label: t(\"fields.name\") },\n              { key: \"created_at\", label: t(\"fields.createdAt\") },\n              { key: \"updated_at\", label: t(\"fields.updatedAt\") }\n            ],\n            isLoading,\n            layout: \"fill\",\n            search: \"autofocus\",\n            queryObject: raw,\n            noRecords: {\n              message: t(\"customers.groups.add.list.noRecordsMessage\")\n            }\n          }\n        ) }),\n        /* @__PURE__ */ jsxs(RouteFocusModal.Footer, { children: [\n          /* @__PURE__ */ jsx(RouteFocusModal.Close, { asChild: true, children: /* @__PURE__ */ jsx(Button, { variant: \"secondary\", size: \"small\", children: t(\"actions.cancel\") }) }),\n          /* @__PURE__ */ jsx(\n            Button,\n            {\n              type: \"submit\",\n              variant: \"primary\",\n              size: \"small\",\n              isLoading: isPending,\n              children: t(\"actions.save\")\n            }\n          )\n        ] })\n      ]\n    }\n  ) });\n};\nvar columnHelper = createColumnHelper();\nvar useColumns = () => {\n  const { t } = useTranslation();\n  const base = useCustomerGroupTableColumns();\n  const columns = useMemo(\n    () => [\n      columnHelper.display({\n        id: \"select\",\n        header: ({ table }) => {\n          return /* @__PURE__ */ jsx(\n            Checkbox,\n            {\n              checked: table.getIsSomePageRowsSelected() ? \"indeterminate\" : table.getIsAllPageRowsSelected(),\n              onCheckedChange: (value) => table.toggleAllPageRowsSelected(!!value)\n            }\n          );\n        },\n        cell: ({ row }) => {\n          const isPreSelected = !row.getCanSelect();\n          const isSelected = row.getIsSelected() || isPreSelected;\n          const Component = /* @__PURE__ */ jsx(\n            Checkbox,\n            {\n              checked: isSelected,\n              disabled: isPreSelected,\n              onCheckedChange: (value) => row.toggleSelected(!!value),\n              onClick: (e) => {\n                e.stopPropagation();\n              }\n            }\n          );\n          if (isPreSelected) {\n            return /* @__PURE__ */ jsx(\n              Tooltip,\n              {\n                content: t(\"customers.groups.alreadyAddedTooltip\"),\n                side: \"right\",\n                children: Component\n              }\n            );\n          }\n          return Component;\n        }\n      }),\n      ...base\n    ],\n    [t, base]\n  );\n  return columns;\n};\n\n// src/routes/customers/customers-add-customer-group/customer-add-customer-groups.tsx\nimport { jsx as jsx2 } from \"react/jsx-runtime\";\nvar CustomerAddCustomerGroups = () => {\n  const { id } = useParams();\n  return /* @__PURE__ */ jsx2(RouteFocusModal, { children: /* @__PURE__ */ jsx2(AddCustomerGroupsForm, { customerId: id }) });\n};\nexport {\n  CustomerAddCustomerGroups as Component\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+EA,mBAA6C;AAI7C,yBAA0B;AA0L1B,IAAAA,sBAA4B;AAzL5B,IAAI,0BAA8B,WAAO;AAAA,EACvC,oBAAwB,UAAU,WAAO,CAAC,EAAE,IAAI,CAAC;AACnD,CAAC;AACD,IAAI,YAAY;AAChB,IAAI,wBAAwB,CAAC;AAAA,EAC3B;AACF,MAAM;AACJ,QAAM,EAAE,GAAAC,GAAE,IAAI,eAAe;AAC7B,QAAM,EAAE,cAAc,IAAI,cAAc;AACxC,QAAM,CAAC,WAAW,YAAY,QAAI,uBAAS,KAAK;AAChD,QAAM,EAAE,aAAa,4BAA4B,IAAI,+BAA+B,UAAU;AAC9F,QAAM,OAAO,QAAQ;AAAA,IACnB,eAAe;AAAA,MACb,oBAAoB,CAAC;AAAA,IACvB;AAAA,IACA,UAAU,EAAY,uBAAuB;AAAA,EAC/C,CAAC;AACD,QAAM,EAAE,SAAS,IAAI;AACrB,QAAM,CAAC,cAAc,eAAe,QAAI,uBAAS,CAAC,CAAC;AACnD,8BAAU,MAAM;AACd;AAAA,MACE;AAAA,MACA,OAAO,KAAK,YAAY,EAAE,OAAO,CAAC,MAAM,aAAa,CAAC,CAAC;AAAA,MACvD;AAAA,QACE,aAAa;AAAA,QACb,aAAa;AAAA,MACf;AAAA,IACF;AAAA,EACF,GAAG,CAAC,cAAc,QAAQ,CAAC;AAC3B,QAAM,EAAE,cAAc,IAAI,IAAI,2BAA2B;AAAA,IACvD,UAAU;AAAA,EACZ,CAAC;AACD,QAAM,UAAU,6BAA6B;AAC7C,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA,WAAW;AAAA,IACX;AAAA,IACA;AAAA,EACF,IAAI,kBAAkB;AAAA,IACpB,QAAQ;AAAA,IACR,GAAG;AAAA,EACL,CAAC;AACD,QAAM,UAAU,CAAC,OAAO;AACtB,UAAM,QAAQ,OAAO,OAAO,aAAa,GAAG,YAAY,IAAI;AAC5D,UAAM,MAAM,OAAO,KAAK,KAAK;AAC7B,aAAS,sBAAsB,KAAK;AAAA,MAClC,aAAa;AAAA,MACb,aAAa;AAAA,IACf,CAAC;AACD,oBAAgB,KAAK;AAAA,EACvB;AACA,QAAM,UAAU,WAAW;AAC3B,QAAM,EAAE,MAAM,IAAI,aAAa;AAAA,IAC7B,MAAM,mBAAmB,CAAC;AAAA,IAC1B;AAAA,IACA;AAAA,IACA,kBAAkB;AAAA,IAClB,oBAAoB,CAAC,QAAQ;AA9IjC;AA+IM,aAAO,GAAC,SAAI,SAAS,cAAb,mBAAwB,IAAI,CAAC,MAAM,EAAE,IAAI,SAAS;AAAA,IAC5D;AAAA,IACA,UAAU,CAAC,QAAQ,IAAI;AAAA,IACvB,UAAU;AAAA,IACV,cAAc;AAAA,MACZ,OAAO;AAAA,MACP;AAAA,IACF;AAAA,EACF,CAAC;AACD,QAAM,eAAe,KAAK,aAAa,OAAO,SAAS;AACrD,iBAAa,IAAI;AACjB,QAAI;AACF,YAAM,4BAA4B,EAAE,KAAK,KAAK,mBAAmB,CAAC;AAClE,YAAM;AAAA,QACJA,GAAE,gCAAgC;AAAA,UAChC,QAAQ,KAAK,mBAAmB,IAAI,CAAC,OAAO,mDAAiB,KAAK,CAAC,MAAM,EAAE,OAAO,GAAG,EAAE,OAAO,OAAO,EAAE,IAAI,CAAC,OAAO,yBAAI,IAAI;AAAA,QAC7H,CAAC;AAAA,MACH;AACA,oBAAc,cAAc,UAAU,EAAE;AAAA,IAC1C,SAAS,GAAG;AACV,YAAM,MAAM,EAAE,OAAO;AAAA,IACvB,UAAE;AACA,mBAAa,KAAK;AAAA,IACpB;AAAA,EACF,CAAC;AACD,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,wBAAI,gBAAgB,MAAM,EAAE,MAAM,cAA0B;AAAA,IACjF;AAAA,IACA;AAAA,MACE,WAAW;AAAA,MACX,UAAU;AAAA,MACV,UAAU;AAAA,YACQ,wBAAI,gBAAgB,QAAQ,EAAE,cAA0B,wBAAI,OAAO,EAAE,WAAW,yCAAyC,UAAU,KAAK,UAAU,OAAO,0BAAsC,wBAAI,MAAM,EAAE,SAAS,SAAS,UAAU,KAAK,UAAU,OAAO,mBAAmB,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC;AAAA,YAC/R,wBAAI,gBAAgB,MAAM,EAAE,WAAW,6BAA6B,cAA0B;AAAA,UAC5G;AAAA,UACA;AAAA,YACE;AAAA,YACA;AAAA,YACA,UAAU;AAAA,YACV;AAAA,YACA;AAAA,YACA,SAAS;AAAA,cACP,EAAE,KAAK,QAAQ,OAAOA,GAAE,aAAa,EAAE;AAAA,cACvC,EAAE,KAAK,cAAc,OAAOA,GAAE,kBAAkB,EAAE;AAAA,cAClD,EAAE,KAAK,cAAc,OAAOA,GAAE,kBAAkB,EAAE;AAAA,YACpD;AAAA,YACA;AAAA,YACA,QAAQ;AAAA,YACR,QAAQ;AAAA,YACR,aAAa;AAAA,YACb,WAAW;AAAA,cACT,SAASA,GAAE,4CAA4C;AAAA,YACzD;AAAA,UACF;AAAA,QACF,EAAE,CAAC;AAAA,YACa,yBAAK,gBAAgB,QAAQ,EAAE,UAAU;AAAA,cACvC,wBAAI,gBAAgB,OAAO,EAAE,SAAS,MAAM,cAA0B,wBAAI,QAAQ,EAAE,SAAS,aAAa,MAAM,SAAS,UAAUA,GAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;AAAA,cAC3J;AAAA,YACd;AAAA,YACA;AAAA,cACE,MAAM;AAAA,cACN,SAAS;AAAA,cACT,MAAM;AAAA,cACN,WAAW;AAAA,cACX,UAAUA,GAAE,cAAc;AAAA,YAC5B;AAAA,UACF;AAAA,QACF,EAAE,CAAC;AAAA,MACL;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;AACA,IAAI,eAAe,mBAAmB;AACtC,IAAI,aAAa,MAAM;AACrB,QAAM,EAAE,GAAAA,GAAE,IAAI,eAAe;AAC7B,QAAM,OAAO,6BAA6B;AAC1C,QAAM,cAAU;AAAA,IACd,MAAM;AAAA,MACJ,aAAa,QAAQ;AAAA,QACnB,IAAI;AAAA,QACJ,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,qBAAuB;AAAA,YACrB;AAAA,YACA;AAAA,cACE,SAAS,MAAM,0BAA0B,IAAI,kBAAkB,MAAM,yBAAyB;AAAA,cAC9F,iBAAiB,CAAC,UAAU,MAAM,0BAA0B,CAAC,CAAC,KAAK;AAAA,YACrE;AAAA,UACF;AAAA,QACF;AAAA,QACA,MAAM,CAAC,EAAE,IAAI,MAAM;AACjB,gBAAM,gBAAgB,CAAC,IAAI,aAAa;AACxC,gBAAM,aAAa,IAAI,cAAc,KAAK;AAC1C,gBAAM,gBAA4B;AAAA,YAChC;AAAA,YACA;AAAA,cACE,SAAS;AAAA,cACT,UAAU;AAAA,cACV,iBAAiB,CAAC,UAAU,IAAI,eAAe,CAAC,CAAC,KAAK;AAAA,cACtD,SAAS,CAAC,MAAM;AACd,kBAAE,gBAAgB;AAAA,cACpB;AAAA,YACF;AAAA,UACF;AACA,cAAI,eAAe;AACjB,uBAAuB;AAAA,cACrB;AAAA,cACA;AAAA,gBACE,SAASA,GAAE,sCAAsC;AAAA,gBACjD,MAAM;AAAA,gBACN,UAAU;AAAA,cACZ;AAAA,YACF;AAAA,UACF;AACA,iBAAO;AAAA,QACT;AAAA,MACF,CAAC;AAAA,MACD,GAAG;AAAA,IACL;AAAA,IACA,CAACA,IAAG,IAAI;AAAA,EACV;AACA,SAAO;AACT;AAIA,IAAI,4BAA4B,MAAM;AACpC,QAAM,EAAE,GAAG,IAAI,UAAU;AACzB,aAAuB,oBAAAC,KAAK,iBAAiB,EAAE,cAA0B,oBAAAA,KAAK,uBAAuB,EAAE,YAAY,GAAG,CAAC,EAAE,CAAC;AAC5H;", "names": ["import_jsx_runtime", "t", "jsx2"]}