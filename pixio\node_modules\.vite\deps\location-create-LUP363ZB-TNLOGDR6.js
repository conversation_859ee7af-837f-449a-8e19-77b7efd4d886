import {
  CountrySelect
} from "./chunk-LULCCYRV.js";
import "./chunk-EGRHWZRV.js";
import {
  KeyboundForm
} from "./chunk-DP54EP6X.js";
import {
  RouteFocusModal,
  useRouteModal
} from "./chunk-MVVOBQIC.js";
import "./chunk-HPGXK5DQ.js";
import {
  t
} from "./chunk-WHQIBI5S.js";
import {
  objectType,
  stringType
} from "./chunk-4XXECALA.js";
import {
  Form,
  useForm
} from "./chunk-XXJU43CK.js";
import {
  useCreateStockLocation
} from "./chunk-ONYSAQ5Z.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import "./chunk-JFCIYOH4.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-5GF3RGIE.js";
import "./chunk-T7YBVUWZ.js";
import {
  Button,
  Heading,
  Input,
  Text,
  toast
} from "./chunk-YP2LLXWB.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-ZSQ362B4.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/location-create-LUP363ZB.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var CreateLocationSchema = objectType({
  name: stringType().min(1),
  address: objectType({
    address_1: stringType().min(1),
    address_2: stringType().optional(),
    country_code: stringType().min(2).max(2),
    city: stringType().optional(),
    postal_code: stringType().optional(),
    province: stringType().optional(),
    company: stringType().optional(),
    phone: stringType().optional()
  })
});
var CreateLocationForm = () => {
  const { t: t2 } = useTranslation();
  const { handleSuccess } = useRouteModal();
  const form = useForm({
    defaultValues: {
      name: "",
      address: {
        address_1: "",
        address_2: "",
        city: "",
        company: "",
        country_code: "",
        phone: "",
        postal_code: "",
        province: ""
      }
    },
    resolver: t(CreateLocationSchema)
  });
  const { mutateAsync, isPending } = useCreateStockLocation();
  const handleSubmit = form.handleSubmit(async (values) => {
    await mutateAsync(
      {
        name: values.name,
        address: values.address
      },
      {
        onSuccess: ({ stock_location }) => {
          toast.success(t2("locations.toast.create"));
          handleSuccess(`/settings/locations/${stock_location.id}`);
        },
        onError: (e) => {
          toast.error(e.message);
        }
      }
    );
  });
  return (0, import_jsx_runtime.jsx)(RouteFocusModal.Form, { form, children: (0, import_jsx_runtime.jsxs)(
    KeyboundForm,
    {
      onSubmit: handleSubmit,
      className: "flex h-full flex-col overflow-hidden",
      children: [
        (0, import_jsx_runtime.jsx)(RouteFocusModal.Header, {}),
        (0, import_jsx_runtime.jsx)(RouteFocusModal.Body, { className: "flex flex-1 flex-col overflow-hidden", children: (0, import_jsx_runtime.jsx)("div", { className: "flex flex-1 flex-col items-center overflow-y-auto", children: (0, import_jsx_runtime.jsxs)("div", { className: "flex w-full max-w-[720px] flex-col gap-y-8 px-2 py-16", children: [
          (0, import_jsx_runtime.jsxs)("div", { children: [
            (0, import_jsx_runtime.jsx)(Heading, { className: "capitalize", children: t2("stockLocations.create.header") }),
            (0, import_jsx_runtime.jsx)(Text, { size: "small", className: "text-ui-fg-subtle", children: t2("stockLocations.create.hint") })
          ] }),
          (0, import_jsx_runtime.jsx)("div", { className: "grid grid-cols-2 gap-4", children: (0, import_jsx_runtime.jsx)(
            Form.Field,
            {
              control: form.control,
              name: "name",
              render: ({ field }) => {
                return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                  (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("fields.name") }),
                  (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(Input, { size: "small", ...field }) }),
                  (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                ] });
              }
            }
          ) }),
          (0, import_jsx_runtime.jsxs)("div", { className: "grid grid-cols-2 gap-4", children: [
            (0, import_jsx_runtime.jsx)(
              Form.Field,
              {
                control: form.control,
                name: "address.address_1",
                render: ({ field }) => {
                  return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                    (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("fields.address") }),
                    (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(Input, { size: "small", ...field }) }),
                    (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                  ] });
                }
              }
            ),
            (0, import_jsx_runtime.jsx)(
              Form.Field,
              {
                control: form.control,
                name: "address.address_2",
                render: ({ field }) => {
                  return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                    (0, import_jsx_runtime.jsx)(Form.Label, { optional: true, children: t2("fields.address2") }),
                    (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(Input, { size: "small", ...field }) }),
                    (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                  ] });
                }
              }
            ),
            (0, import_jsx_runtime.jsx)(
              Form.Field,
              {
                control: form.control,
                name: "address.postal_code",
                render: ({ field }) => {
                  return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                    (0, import_jsx_runtime.jsx)(Form.Label, { optional: true, children: t2("fields.postalCode") }),
                    (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(Input, { size: "small", ...field }) }),
                    (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                  ] });
                }
              }
            ),
            (0, import_jsx_runtime.jsx)(
              Form.Field,
              {
                control: form.control,
                name: "address.city",
                render: ({ field }) => {
                  return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                    (0, import_jsx_runtime.jsx)(Form.Label, { optional: true, children: t2("fields.city") }),
                    (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(Input, { size: "small", ...field }) }),
                    (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                  ] });
                }
              }
            ),
            (0, import_jsx_runtime.jsx)(
              Form.Field,
              {
                control: form.control,
                name: "address.country_code",
                render: ({ field }) => {
                  return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                    (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("fields.country") }),
                    (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(CountrySelect, { ...field }) }),
                    (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                  ] });
                }
              }
            ),
            (0, import_jsx_runtime.jsx)(
              Form.Field,
              {
                control: form.control,
                name: "address.province",
                render: ({ field }) => {
                  return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                    (0, import_jsx_runtime.jsx)(Form.Label, { optional: true, children: t2("fields.state") }),
                    (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(Input, { size: "small", ...field }) }),
                    (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                  ] });
                }
              }
            ),
            (0, import_jsx_runtime.jsx)(
              Form.Field,
              {
                control: form.control,
                name: "address.company",
                render: ({ field }) => {
                  return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                    (0, import_jsx_runtime.jsx)(Form.Label, { optional: true, children: t2("fields.company") }),
                    (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(Input, { size: "small", ...field }) }),
                    (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                  ] });
                }
              }
            ),
            (0, import_jsx_runtime.jsx)(
              Form.Field,
              {
                control: form.control,
                name: "address.phone",
                render: ({ field }) => {
                  return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                    (0, import_jsx_runtime.jsx)(Form.Label, { optional: true, children: t2("fields.phone") }),
                    (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(Input, { size: "small", ...field }) }),
                    (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                  ] });
                }
              }
            )
          ] })
        ] }) }) }),
        (0, import_jsx_runtime.jsx)(RouteFocusModal.Footer, { children: (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center justify-end gap-x-2", children: [
          (0, import_jsx_runtime.jsx)(RouteFocusModal.Close, { asChild: true, children: (0, import_jsx_runtime.jsx)(Button, { size: "small", variant: "secondary", children: t2("actions.cancel") }) }),
          (0, import_jsx_runtime.jsx)(Button, { type: "submit", size: "small", isLoading: isPending, children: t2("actions.save") })
        ] }) })
      ]
    }
  ) });
};
var LocationCreate = () => {
  return (0, import_jsx_runtime2.jsx)(RouteFocusModal, { children: (0, import_jsx_runtime2.jsx)(CreateLocationForm, {}) });
};
export {
  LocationCreate as Component
};
//# sourceMappingURL=location-create-LUP363ZB-TNLOGDR6.js.map
