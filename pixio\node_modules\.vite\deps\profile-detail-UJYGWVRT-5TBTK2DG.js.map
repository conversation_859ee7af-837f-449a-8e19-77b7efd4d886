{"version": 3, "sources": ["../../@medusajs/dashboard/dist/profile-detail-UJYGWVRT.mjs"], "sourcesContent": ["import {\n  SingleColumnPage\n} from \"./chunk-2RQLKDBF.mjs\";\nimport {\n  useExtension\n} from \"./chunk-C5P5PL3E.mjs\";\nimport {\n  SingleColumnPageSkeleton\n} from \"./chunk-LPEUYMRK.mjs\";\nimport {\n  ActionMenu\n} from \"./chunk-3NJTXRIY.mjs\";\nimport \"./chunk-OC7BQLYI.mjs\";\nimport {\n  languages\n} from \"./chunk-NYXYHZE6.mjs\";\nimport {\n  useMe\n} from \"./chunk-2ZKVRTBW.mjs\";\nimport \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-RPUOO7AV.mjs\";\n\n// src/routes/profile/profile-detail/components/profile-general-section/profile-general-section.tsx\nimport { PencilSquare } from \"@medusajs/icons\";\nimport { Container, Heading, Text } from \"@medusajs/ui\";\nimport { useTranslation } from \"react-i18next\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar ProfileGeneralSection = ({ user }) => {\n  const { i18n, t } = useTranslation();\n  const name = [user.first_name, user.last_name].filter(Boolean).join(\" \");\n  return /* @__PURE__ */ jsxs(Container, { className: \"divide-y p-0\", children: [\n    /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center justify-between px-6 py-4\", children: [\n      /* @__PURE__ */ jsxs(\"div\", { children: [\n        /* @__PURE__ */ jsx(Heading, { children: t(\"profile.domain\") }),\n        /* @__PURE__ */ jsx(Text, { className: \"text-ui-fg-subtle\", size: \"small\", children: t(\"profile.manageYourProfileDetails\") })\n      ] }),\n      /* @__PURE__ */ jsx(\n        ActionMenu,\n        {\n          groups: [\n            {\n              actions: [\n                {\n                  label: t(\"actions.edit\"),\n                  to: \"edit\",\n                  icon: /* @__PURE__ */ jsx(PencilSquare, {})\n                }\n              ]\n            }\n          ]\n        }\n      )\n    ] }),\n    /* @__PURE__ */ jsxs(\"div\", { className: \"text-ui-fg-subtle grid grid-cols-2 items-center px-6 py-4\", children: [\n      /* @__PURE__ */ jsx(Text, { size: \"small\", leading: \"compact\", weight: \"plus\", children: t(\"fields.name\") }),\n      /* @__PURE__ */ jsx(Text, { size: \"small\", leading: \"compact\", children: name || \"-\" })\n    ] }),\n    /* @__PURE__ */ jsxs(\"div\", { className: \"grid grid-cols-2 items-center px-6 py-4\", children: [\n      /* @__PURE__ */ jsx(Text, { size: \"small\", leading: \"compact\", weight: \"plus\", children: t(\"fields.email\") }),\n      /* @__PURE__ */ jsx(Text, { size: \"small\", leading: \"compact\", children: user.email })\n    ] }),\n    /* @__PURE__ */ jsxs(\"div\", { className: \"grid grid-cols-2 items-center px-6 py-4\", children: [\n      /* @__PURE__ */ jsx(Text, { size: \"small\", leading: \"compact\", weight: \"plus\", children: t(\"profile.fields.languageLabel\") }),\n      /* @__PURE__ */ jsx(Text, { size: \"small\", leading: \"compact\", children: languages.find((lang) => lang.code === i18n.language)?.display_name || \"-\" })\n    ] })\n  ] });\n};\n\n// src/routes/profile/profile-detail/profile-detail.tsx\nimport { jsx as jsx2 } from \"react/jsx-runtime\";\nvar ProfileDetail = () => {\n  const { user, isPending: isLoading, isError, error } = useMe();\n  const { getWidgets } = useExtension();\n  if (isLoading || !user) {\n    return /* @__PURE__ */ jsx2(SingleColumnPageSkeleton, { sections: 1 });\n  }\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsx2(\n    SingleColumnPage,\n    {\n      widgets: {\n        after: getWidgets(\"profile.details.after\"),\n        before: getWidgets(\"profile.details.before\")\n      },\n      children: /* @__PURE__ */ jsx2(ProfileGeneralSection, { user })\n    }\n  );\n};\nexport {\n  ProfileDetail as Component\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4BA,yBAA0B;AA2C1B,IAAAA,sBAA4B;AA1C5B,IAAI,wBAAwB,CAAC,EAAE,KAAK,MAAM;AA7B1C;AA8BE,QAAM,EAAE,MAAM,EAAE,IAAI,eAAe;AACnC,QAAM,OAAO,CAAC,KAAK,YAAY,KAAK,SAAS,EAAE,OAAO,OAAO,EAAE,KAAK,GAAG;AACvE,aAAuB,yBAAK,WAAW,EAAE,WAAW,gBAAgB,UAAU;AAAA,QAC5D,yBAAK,OAAO,EAAE,WAAW,+CAA+C,UAAU;AAAA,UAChF,yBAAK,OAAO,EAAE,UAAU;AAAA,YACtB,wBAAI,SAAS,EAAE,UAAU,EAAE,gBAAgB,EAAE,CAAC;AAAA,YAC9C,wBAAI,MAAM,EAAE,WAAW,qBAAqB,MAAM,SAAS,UAAU,EAAE,kCAAkC,EAAE,CAAC;AAAA,MAC9H,EAAE,CAAC;AAAA,UACa;AAAA,QACd;AAAA,QACA;AAAA,UACE,QAAQ;AAAA,YACN;AAAA,cACE,SAAS;AAAA,gBACP;AAAA,kBACE,OAAO,EAAE,cAAc;AAAA,kBACvB,IAAI;AAAA,kBACJ,UAAsB,wBAAI,cAAc,CAAC,CAAC;AAAA,gBAC5C;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF,EAAE,CAAC;AAAA,QACa,yBAAK,OAAO,EAAE,WAAW,6DAA6D,UAAU;AAAA,UAC9F,wBAAI,MAAM,EAAE,MAAM,SAAS,SAAS,WAAW,QAAQ,QAAQ,UAAU,EAAE,aAAa,EAAE,CAAC;AAAA,UAC3F,wBAAI,MAAM,EAAE,MAAM,SAAS,SAAS,WAAW,UAAU,QAAQ,IAAI,CAAC;AAAA,IACxF,EAAE,CAAC;AAAA,QACa,yBAAK,OAAO,EAAE,WAAW,2CAA2C,UAAU;AAAA,UAC5E,wBAAI,MAAM,EAAE,MAAM,SAAS,SAAS,WAAW,QAAQ,QAAQ,UAAU,EAAE,cAAc,EAAE,CAAC;AAAA,UAC5F,wBAAI,MAAM,EAAE,MAAM,SAAS,SAAS,WAAW,UAAU,KAAK,MAAM,CAAC;AAAA,IACvF,EAAE,CAAC;AAAA,QACa,yBAAK,OAAO,EAAE,WAAW,2CAA2C,UAAU;AAAA,UAC5E,wBAAI,MAAM,EAAE,MAAM,SAAS,SAAS,WAAW,QAAQ,QAAQ,UAAU,EAAE,8BAA8B,EAAE,CAAC;AAAA,UAC5G,wBAAI,MAAM,EAAE,MAAM,SAAS,SAAS,WAAW,YAAU,eAAU,KAAK,CAAC,SAAS,KAAK,SAAS,KAAK,QAAQ,MAApD,mBAAuD,iBAAgB,IAAI,CAAC;AAAA,IACvJ,EAAE,CAAC;AAAA,EACL,EAAE,CAAC;AACL;AAIA,IAAI,gBAAgB,MAAM;AACxB,QAAM,EAAE,MAAM,WAAW,WAAW,SAAS,MAAM,IAAI,MAAM;AAC7D,QAAM,EAAE,WAAW,IAAI,aAAa;AACpC,MAAI,aAAa,CAAC,MAAM;AACtB,eAAuB,oBAAAC,KAAK,0BAA0B,EAAE,UAAU,EAAE,CAAC;AAAA,EACvE;AACA,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,oBAAAA;AAAA,IACrB;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP,OAAO,WAAW,uBAAuB;AAAA,QACzC,QAAQ,WAAW,wBAAwB;AAAA,MAC7C;AAAA,MACA,cAA0B,oBAAAA,KAAK,uBAAuB,EAAE,KAAK,CAAC;AAAA,IAChE;AAAA,EACF;AACF;", "names": ["import_jsx_runtime", "jsx2"]}