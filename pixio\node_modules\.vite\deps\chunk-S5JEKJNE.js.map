{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-P3UUX2T6.mjs"], "sourcesContent": ["// src/components/table/table-cells/common/placeholder-cell/placeholder-cell.tsx\nimport { jsx } from \"react/jsx-runtime\";\nvar PlaceholderCell = () => {\n  return /* @__PURE__ */ jsx(\"div\", { className: \"flex h-full w-full items-center\", children: /* @__PURE__ */ jsx(\"span\", { className: \"text-ui-fg-muted\", children: \"-\" }) });\n};\n\nexport {\n  PlaceholderCell\n};\n"], "mappings": ";;;;;;;;AACA,yBAAoB;AACpB,IAAI,kBAAkB,MAAM;AAC1B,aAAuB,wBAAI,OAAO,EAAE,WAAW,mCAAmC,cAA0B,wBAAI,QAAQ,EAAE,WAAW,oBAAoB,UAAU,IAAI,CAAC,EAAE,CAAC;AAC7K;", "names": []}