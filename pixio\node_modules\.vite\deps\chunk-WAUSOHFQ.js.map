{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-KOSCMAIC.mjs"], "sourcesContent": ["import {\n  sdk\n} from \"./chunk-DEQUVHHE.mjs\";\n\n// src/hooks/api/auth.tsx\nimport { useMutation } from \"@tanstack/react-query\";\nvar useSignInWithEmailPass = (options) => {\n  return useMutation({\n    mutationFn: (payload) => sdk.auth.login(\"user\", \"emailpass\", payload),\n    onSuccess: async (data, variables, context) => {\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\nvar useSignUpWithEmailPass = (options) => {\n  return useMutation({\n    mutationFn: (payload) => sdk.auth.register(\"user\", \"emailpass\", payload),\n    onSuccess: async (data, variables, context) => {\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\nvar useResetPasswordForEmailPass = (options) => {\n  return useMutation({\n    mutationFn: (payload) => sdk.auth.resetPassword(\"user\", \"emailpass\", {\n      identifier: payload.email\n    }),\n    onSuccess: async (data, variables, context) => {\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\nvar useLogout = (options) => {\n  return useMutation({\n    mutationFn: () => sdk.auth.logout(),\n    ...options\n  });\n};\nvar useUpdateProviderForEmailPass = (token, options) => {\n  return useMutation({\n    mutationFn: (payload) => sdk.auth.updateProvider(\"user\", \"emailpass\", payload, token),\n    onSuccess: async (data, variables, context) => {\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\n\nexport {\n  useSignInWithEmailPass,\n  useSignUpWithEmailPass,\n  useResetPasswordForEmailPass,\n  useLogout,\n  useUpdateProviderForEmailPass\n};\n"], "mappings": ";;;;;;;;AAMA,IAAI,yBAAyB,CAAC,YAAY;AACxC,SAAO,YAAY;AAAA,IACjB,YAAY,CAAC,YAAY,IAAI,KAAK,MAAM,QAAQ,aAAa,OAAO;AAAA,IACpE,WAAW,OAAO,MAAM,WAAW,YAAY;AATnD;AAUM,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;AACA,IAAI,yBAAyB,CAAC,YAAY;AACxC,SAAO,YAAY;AAAA,IACjB,YAAY,CAAC,YAAY,IAAI,KAAK,SAAS,QAAQ,aAAa,OAAO;AAAA,IACvE,WAAW,OAAO,MAAM,WAAW,YAAY;AAlBnD;AAmBM,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;AACA,IAAI,+BAA+B,CAAC,YAAY;AAC9C,SAAO,YAAY;AAAA,IACjB,YAAY,CAAC,YAAY,IAAI,KAAK,cAAc,QAAQ,aAAa;AAAA,MACnE,YAAY,QAAQ;AAAA,IACtB,CAAC;AAAA,IACD,WAAW,OAAO,MAAM,WAAW,YAAY;AA7BnD;AA8BM,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;AACA,IAAI,YAAY,CAAC,YAAY;AAC3B,SAAO,YAAY;AAAA,IACjB,YAAY,MAAM,IAAI,KAAK,OAAO;AAAA,IAClC,GAAG;AAAA,EACL,CAAC;AACH;AACA,IAAI,gCAAgC,CAAC,OAAO,YAAY;AACtD,SAAO,YAAY;AAAA,IACjB,YAAY,CAAC,YAAY,IAAI,KAAK,eAAe,QAAQ,aAAa,SAAS,KAAK;AAAA,IACpF,WAAW,OAAO,MAAM,WAAW,YAAY;AA5CnD;AA6CM,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;", "names": []}