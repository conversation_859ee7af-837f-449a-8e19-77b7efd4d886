{"version": 3, "sources": ["../../@medusajs/dashboard/dist/collection-edit-6OEMTTR4.mjs"], "sourcesContent": ["import \"./chunk-IUCDCPJU.mjs\";\nimport {\n  KeyboundForm\n} from \"./chunk-6HTZNHPT.mjs\";\nimport {\n  RouteDrawer,\n  useRouteModal\n} from \"./chunk-4TC5YS65.mjs\";\nimport {\n  Form\n} from \"./chunk-OBQI23QM.mjs\";\nimport {\n  useCollection,\n  useUpdateCollection\n} from \"./chunk-3OHH43G6.mjs\";\nimport \"./chunk-6I62UDJA.mjs\";\nimport \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-RPUOO7AV.mjs\";\n\n// src/routes/collections/collection-edit/collection-edit.tsx\nimport { Heading } from \"@medusajs/ui\";\nimport { useTranslation as useTranslation2 } from \"react-i18next\";\nimport { useParams } from \"react-router-dom\";\n\n// src/routes/collections/collection-edit/components/edit-collection-form/edit-collection-form.tsx\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { Button, Input, Text } from \"@medusajs/ui\";\nimport { useForm } from \"react-hook-form\";\nimport { useTranslation } from \"react-i18next\";\nimport * as zod from \"zod\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar EditCollectionSchema = zod.object({\n  title: zod.string().min(1),\n  handle: zod.string().min(1)\n});\nvar EditCollectionForm = ({ collection }) => {\n  const { t } = useTranslation();\n  const { handleSuccess } = useRouteModal();\n  const form = useForm({\n    defaultValues: {\n      title: collection.title,\n      handle: collection.handle\n    },\n    resolver: zodResolver(EditCollectionSchema)\n  });\n  const { mutateAsync, isPending } = useUpdateCollection(collection.id);\n  const handleSubmit = form.handleSubmit(async (data) => {\n    await mutateAsync(data, {\n      onSuccess: () => {\n        handleSuccess();\n      }\n    });\n  });\n  return /* @__PURE__ */ jsx(RouteDrawer.Form, { form, children: /* @__PURE__ */ jsxs(KeyboundForm, { onSubmit: handleSubmit, className: \"flex flex-1 flex-col\", children: [\n    /* @__PURE__ */ jsx(RouteDrawer.Body, { children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex flex-col gap-y-4\", children: [\n      /* @__PURE__ */ jsx(\n        Form.Field,\n        {\n          control: form.control,\n          name: \"title\",\n          render: ({ field }) => {\n            return /* @__PURE__ */ jsxs(Form.Item, { children: [\n              /* @__PURE__ */ jsx(Form.Label, { children: t(\"fields.title\") }),\n              /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(Input, { ...field }) }),\n              /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n            ] });\n          }\n        }\n      ),\n      /* @__PURE__ */ jsx(\n        Form.Field,\n        {\n          control: form.control,\n          name: \"handle\",\n          render: ({ field }) => {\n            return /* @__PURE__ */ jsxs(Form.Item, { children: [\n              /* @__PURE__ */ jsx(Form.Label, { tooltip: t(\"collections.handleTooltip\"), children: t(\"fields.handle\") }),\n              /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsxs(\"div\", { className: \"relative\", children: [\n                /* @__PURE__ */ jsx(\"div\", { className: \"absolute inset-y-0 left-0 z-10 flex w-8 items-center justify-center border-r\", children: /* @__PURE__ */ jsx(\n                  Text,\n                  {\n                    className: \"text-ui-fg-muted\",\n                    size: \"small\",\n                    leading: \"compact\",\n                    weight: \"plus\",\n                    children: \"/\"\n                  }\n                ) }),\n                /* @__PURE__ */ jsx(Input, { ...field, className: \"pl-10\" })\n              ] }) }),\n              /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n            ] });\n          }\n        }\n      )\n    ] }) }),\n    /* @__PURE__ */ jsx(RouteDrawer.Footer, { children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center gap-x-2\", children: [\n      /* @__PURE__ */ jsx(RouteDrawer.Close, { asChild: true, children: /* @__PURE__ */ jsx(Button, { size: \"small\", variant: \"secondary\", children: t(\"actions.cancel\") }) }),\n      /* @__PURE__ */ jsx(Button, { size: \"small\", type: \"submit\", isLoading: isPending, children: t(\"actions.save\") })\n    ] }) })\n  ] }) });\n};\n\n// src/routes/collections/collection-edit/collection-edit.tsx\nimport { jsx as jsx2, jsxs as jsxs2 } from \"react/jsx-runtime\";\nvar CollectionEdit = () => {\n  const { id } = useParams();\n  const { t } = useTranslation2();\n  const { collection, isLoading, isError, error } = useCollection(id);\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsxs2(RouteDrawer, { children: [\n    /* @__PURE__ */ jsx2(RouteDrawer.Header, { children: /* @__PURE__ */ jsx2(Heading, { children: t(\"collections.editCollection\") }) }),\n    !isLoading && collection && /* @__PURE__ */ jsx2(EditCollectionForm, { collection })\n  ] });\n};\nexport {\n  CollectionEdit as Component\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgCA,yBAA0B;AA0E1B,IAAAA,sBAA2C;AAzE3C,IAAI,uBAA2B,WAAO;AAAA,EACpC,OAAW,WAAO,EAAE,IAAI,CAAC;AAAA,EACzB,QAAY,WAAO,EAAE,IAAI,CAAC;AAC5B,CAAC;AACD,IAAI,qBAAqB,CAAC,EAAE,WAAW,MAAM;AAC3C,QAAM,EAAE,GAAAC,GAAE,IAAI,eAAe;AAC7B,QAAM,EAAE,cAAc,IAAI,cAAc;AACxC,QAAM,OAAO,QAAQ;AAAA,IACnB,eAAe;AAAA,MACb,OAAO,WAAW;AAAA,MAClB,QAAQ,WAAW;AAAA,IACrB;AAAA,IACA,UAAU,EAAY,oBAAoB;AAAA,EAC5C,CAAC;AACD,QAAM,EAAE,aAAa,UAAU,IAAI,oBAAoB,WAAW,EAAE;AACpE,QAAM,eAAe,KAAK,aAAa,OAAO,SAAS;AACrD,UAAM,YAAY,MAAM;AAAA,MACtB,WAAW,MAAM;AACf,sBAAc;AAAA,MAChB;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACD,aAAuB,wBAAI,YAAY,MAAM,EAAE,MAAM,cAA0B,yBAAK,cAAc,EAAE,UAAU,cAAc,WAAW,wBAAwB,UAAU;AAAA,QACvJ,wBAAI,YAAY,MAAM,EAAE,cAA0B,yBAAK,OAAO,EAAE,WAAW,yBAAyB,UAAU;AAAA,UAC5G;AAAA,QACd,KAAK;AAAA,QACL;AAAA,UACE,SAAS,KAAK;AAAA,UACd,MAAM;AAAA,UACN,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,uBAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,kBACjC,wBAAI,KAAK,OAAO,EAAE,UAAUA,GAAE,cAAc,EAAE,CAAC;AAAA,kBAC/C,wBAAI,KAAK,SAAS,EAAE,cAA0B,wBAAI,OAAO,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC;AAAA,kBACxE,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,YAC3C,EAAE,CAAC;AAAA,UACL;AAAA,QACF;AAAA,MACF;AAAA,UACgB;AAAA,QACd,KAAK;AAAA,QACL;AAAA,UACE,SAAS,KAAK;AAAA,UACd,MAAM;AAAA,UACN,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,uBAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,kBACjC,wBAAI,KAAK,OAAO,EAAE,SAASA,GAAE,2BAA2B,GAAG,UAAUA,GAAE,eAAe,EAAE,CAAC;AAAA,kBACzF,wBAAI,KAAK,SAAS,EAAE,cAA0B,yBAAK,OAAO,EAAE,WAAW,YAAY,UAAU;AAAA,oBAC3F,wBAAI,OAAO,EAAE,WAAW,gFAAgF,cAA0B;AAAA,kBAChJ;AAAA,kBACA;AAAA,oBACE,WAAW;AAAA,oBACX,MAAM;AAAA,oBACN,SAAS;AAAA,oBACT,QAAQ;AAAA,oBACR,UAAU;AAAA,kBACZ;AAAA,gBACF,EAAE,CAAC;AAAA,oBACa,wBAAI,OAAO,EAAE,GAAG,OAAO,WAAW,QAAQ,CAAC;AAAA,cAC7D,EAAE,CAAC,EAAE,CAAC;AAAA,kBACU,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,YAC3C,EAAE,CAAC;AAAA,UACL;AAAA,QACF;AAAA,MACF;AAAA,IACF,EAAE,CAAC,EAAE,CAAC;AAAA,QACU,wBAAI,YAAY,QAAQ,EAAE,cAA0B,yBAAK,OAAO,EAAE,WAAW,6BAA6B,UAAU;AAAA,UAClH,wBAAI,YAAY,OAAO,EAAE,SAAS,MAAM,cAA0B,wBAAI,QAAQ,EAAE,MAAM,SAAS,SAAS,aAAa,UAAUA,GAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;AAAA,UACvJ,wBAAI,QAAQ,EAAE,MAAM,SAAS,MAAM,UAAU,WAAW,WAAW,UAAUA,GAAE,cAAc,EAAE,CAAC;AAAA,IAClH,EAAE,CAAC,EAAE,CAAC;AAAA,EACR,EAAE,CAAC,EAAE,CAAC;AACR;AAIA,IAAI,iBAAiB,MAAM;AACzB,QAAM,EAAE,GAAG,IAAI,UAAU;AACzB,QAAM,EAAE,GAAAA,GAAE,IAAI,eAAgB;AAC9B,QAAM,EAAE,YAAY,WAAW,SAAS,MAAM,IAAI,cAAc,EAAE;AAClE,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,oBAAAC,MAAM,aAAa,EAAE,UAAU;AAAA,QACpC,oBAAAC,KAAK,YAAY,QAAQ,EAAE,cAA0B,oBAAAA,KAAK,SAAS,EAAE,UAAUF,GAAE,4BAA4B,EAAE,CAAC,EAAE,CAAC;AAAA,IACnI,CAAC,aAAa,kBAA8B,oBAAAE,KAAK,oBAAoB,EAAE,WAAW,CAAC;AAAA,EACrF,EAAE,CAAC;AACL;", "names": ["import_jsx_runtime", "t", "jsxs2", "jsx2"]}