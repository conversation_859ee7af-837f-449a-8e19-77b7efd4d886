{"version": 3, "sources": ["../../@medusajs/dashboard/dist/order-create-refund-V5YDN6HL.mjs"], "sourcesContent": ["import {\n  getPaymentsFromOrder\n} from \"./chunk-EA4G7XL6.mjs\";\nimport {\n  formatProvider\n} from \"./chunk-IR5DHEKS.mjs\";\nimport {\n  formatCurrency\n} from \"./chunk-OV5NMSY6.mjs\";\nimport {\n  DEFAULT_FIELDS\n} from \"./chunk-7I5DQGWY.mjs\";\nimport \"./chunk-7DXVXBSA.mjs\";\nimport {\n  getLocaleAmount\n} from \"./chunk-PDWBYQOW.mjs\";\nimport {\n  currencies\n} from \"./chunk-MWVM4TYO.mjs\";\nimport \"./chunk-IUCDCPJU.mjs\";\nimport {\n  KeyboundForm\n} from \"./chunk-6HTZNHPT.mjs\";\nimport {\n  RouteDrawer,\n  useRouteModal\n} from \"./chunk-4TC5YS65.mjs\";\nimport \"./chunk-3NJTXRIY.mjs\";\nimport \"./chunk-OC7BQLYI.mjs\";\nimport {\n  Form\n} from \"./chunk-OBQI23QM.mjs\";\nimport {\n  useRefundReasons\n} from \"./chunk-Z5UDPQIH.mjs\";\nimport \"./chunk-KOSCMAIC.mjs\";\nimport \"./chunk-X6DSNTTX.mjs\";\nimport \"./chunk-I6E6CALJ.mjs\";\nimport \"./chunk-B4GODIOW.mjs\";\nimport \"./chunk-F6IJV2I2.mjs\";\nimport \"./chunk-QTCZFYFH.mjs\";\nimport \"./chunk-ENV6YVOM.mjs\";\nimport \"./chunk-PIR2H25N.mjs\";\nimport \"./chunk-RLY2SL5E.mjs\";\nimport \"./chunk-C5LYZZZ5.mjs\";\nimport \"./chunk-2ZKVRTBW.mjs\";\nimport {\n  useRefundPayment\n} from \"./chunk-FO3VP56P.mjs\";\nimport \"./chunk-YS65UGPC.mjs\";\nimport \"./chunk-F6PXCY3N.mjs\";\nimport \"./chunk-3OHH43G6.mjs\";\nimport \"./chunk-G2H6MAK7.mjs\";\nimport \"./chunk-GRT22PE5.mjs\";\nimport \"./chunk-32IQRUVY.mjs\";\nimport {\n  useOrder\n} from \"./chunk-FNYASI54.mjs\";\nimport \"./chunk-FVC7M755.mjs\";\nimport \"./chunk-ZJ3OFMHB.mjs\";\nimport \"./chunk-PNU5HPGY.mjs\";\nimport \"./chunk-V2LANK5S.mjs\";\nimport \"./chunk-QZ6PT4QV.mjs\";\nimport \"./chunk-QL4XKIVL.mjs\";\nimport \"./chunk-6I62UDJA.mjs\";\nimport \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-RPUOO7AV.mjs\";\n\n// src/routes/orders/order-create-refund/order-create-refund.tsx\nimport { Heading } from \"@medusajs/ui\";\nimport { useTranslation as useTranslation2 } from \"react-i18next\";\nimport { useParams } from \"react-router-dom\";\n\n// src/routes/orders/order-create-refund/components/create-refund-form/create-refund-form.tsx\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport {\n  Button,\n  CurrencyInput,\n  Label,\n  Select,\n  Textarea,\n  toast\n} from \"@medusajs/ui\";\nimport { useEffect, useMemo } from \"react\";\nimport { useForm } from \"react-hook-form\";\nimport { useTranslation } from \"react-i18next\";\nimport { useNavigate, useSearchParams } from \"react-router-dom\";\nimport * as zod from \"zod\";\nimport { formatValue } from \"react-currency-input-field\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar CreateRefundSchema = zod.object({\n  amount: zod.string().or(zod.number()),\n  refund_reason_id: zod.string().nullish(),\n  note: zod.string().optional()\n});\nvar CreateRefundForm = ({\n  order,\n  refundReasons\n}) => {\n  const { t } = useTranslation();\n  const { handleSuccess } = useRouteModal();\n  const navigate = useNavigate();\n  const [searchParams] = useSearchParams();\n  const paymentId = searchParams.get(\"paymentId\");\n  const payments = getPaymentsFromOrder(order);\n  const payment = payments.find((p) => p.id === paymentId);\n  const paymentAmount = payment?.amount || 0;\n  const currency = useMemo(\n    () => currencies[order.currency_code.toUpperCase()],\n    [order.currency_code]\n  );\n  const form = useForm({\n    defaultValues: {\n      amount: paymentAmount,\n      note: \"\"\n    },\n    resolver: zodResolver(CreateRefundSchema)\n  });\n  useEffect(() => {\n    const pendingDifference = order.summary.pending_difference;\n    const paymentAmount2 = payment?.amount || 0;\n    const pendingAmount = pendingDifference < 0 ? Math.min(pendingDifference, paymentAmount2) : paymentAmount2;\n    const normalizedAmount = pendingAmount < 0 ? pendingAmount * -1 : pendingAmount;\n    form.setValue(\"amount\", normalizedAmount);\n  }, [payment]);\n  const { mutateAsync, isPending } = useRefundPayment(order.id, payment?.id);\n  const handleSubmit = form.handleSubmit(async (data) => {\n    await mutateAsync(\n      {\n        amount: parseFloat(data.amount),\n        refund_reason_id: data.refund_reason_id,\n        note: data.note\n      },\n      {\n        onSuccess: () => {\n          toast.success(\n            t(\"orders.payment.refundPaymentSuccess\", {\n              amount: formatCurrency(data.amount, payment?.currency_code)\n            })\n          );\n          handleSuccess();\n        },\n        onError: (error) => {\n          toast.error(error.message);\n        }\n      }\n    );\n  });\n  return /* @__PURE__ */ jsx(RouteDrawer.Form, { form, children: /* @__PURE__ */ jsxs(\n    KeyboundForm,\n    {\n      onSubmit: handleSubmit,\n      className: \"flex size-full flex-col overflow-hidden\",\n      children: [\n        /* @__PURE__ */ jsx(RouteDrawer.Body, { className: \"flex-1 overflow-auto\", children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex flex-col gap-y-4\", children: [\n          /* @__PURE__ */ jsxs(\n            Select,\n            {\n              value: payment?.id,\n              onValueChange: (value) => {\n                navigate(`/orders/${order.id}/refund?paymentId=${value}`, {\n                  replace: true\n                });\n              },\n              children: [\n                /* @__PURE__ */ jsx(Label, { className: \"txt-compact-small mb-[-6px] font-sans font-medium\", children: t(\"orders.payment.selectPaymentToRefund\") }),\n                /* @__PURE__ */ jsx(Select.Trigger, { children: /* @__PURE__ */ jsx(\n                  Select.Value,\n                  {\n                    placeholder: t(\"orders.payment.selectPaymentToRefund\")\n                  }\n                ) }),\n                /* @__PURE__ */ jsx(Select.Content, { children: payments.map((payment2) => {\n                  const totalRefunded = payment2.refunds.reduce(\n                    (acc, next) => next.amount + acc,\n                    0\n                  );\n                  return /* @__PURE__ */ jsxs(\n                    Select.Item,\n                    {\n                      value: payment2.id,\n                      disabled: !!payment2.canceled_at || totalRefunded >= payment2.amount,\n                      className: \"flex items-center justify-center\",\n                      children: [\n                        /* @__PURE__ */ jsxs(\"span\", { children: [\n                          getLocaleAmount(\n                            payment2.amount,\n                            payment2.currency_code\n                          ),\n                          \" - \"\n                        ] }),\n                        /* @__PURE__ */ jsx(\"span\", { children: formatProvider(payment2.provider_id) }),\n                        /* @__PURE__ */ jsxs(\"span\", { children: [\n                          \" - (#\",\n                          payment2.id.substring(23),\n                          \")\"\n                        ] })\n                      ]\n                    },\n                    payment2.id\n                  );\n                }) })\n              ]\n            }\n          ),\n          /* @__PURE__ */ jsx(\n            Form.Field,\n            {\n              control: form.control,\n              name: \"amount\",\n              rules: {\n                required: true,\n                min: 0,\n                max: paymentAmount\n              },\n              render: ({ field: { onChange, ...field } }) => {\n                return /* @__PURE__ */ jsxs(Form.Item, { children: [\n                  /* @__PURE__ */ jsx(Form.Label, { children: t(\"fields.amount\") }),\n                  /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(\n                    CurrencyInput,\n                    {\n                      ...field,\n                      min: 0,\n                      placeholder: formatValue({\n                        value: \"0\",\n                        decimalScale: currency.decimal_digits\n                      }),\n                      decimalScale: currency.decimal_digits,\n                      symbol: currency.symbol_native,\n                      code: currency.code,\n                      value: field.value,\n                      onValueChange: (_value, _name, values) => onChange(values?.value ? values?.value : \"\"),\n                      autoFocus: true\n                    }\n                  ) }),\n                  /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n                ] });\n              }\n            }\n          ),\n          /* @__PURE__ */ jsx(\n            Form.Field,\n            {\n              control: form.control,\n              name: `note`,\n              render: ({ field }) => {\n                return /* @__PURE__ */ jsxs(Form.Item, { children: [\n                  /* @__PURE__ */ jsx(Form.Label, { children: t(\"fields.note\") }),\n                  /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(Textarea, { ...field }) }),\n                  /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n                ] });\n              }\n            }\n          )\n        ] }) }),\n        /* @__PURE__ */ jsx(RouteDrawer.Footer, { children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center justify-end gap-x-2\", children: [\n          /* @__PURE__ */ jsx(RouteDrawer.Close, { asChild: true, children: /* @__PURE__ */ jsx(Button, { variant: \"secondary\", size: \"small\", children: t(\"actions.cancel\") }) }),\n          /* @__PURE__ */ jsx(\n            Button,\n            {\n              isLoading: isPending,\n              type: \"submit\",\n              variant: \"primary\",\n              size: \"small\",\n              disabled: !!Object.keys(form.formState.errors || {}).length,\n              children: t(\"actions.save\")\n            }\n          )\n        ] }) })\n      ]\n    }\n  ) });\n};\n\n// src/routes/orders/order-create-refund/order-create-refund.tsx\nimport { jsx as jsx2, jsxs as jsxs2 } from \"react/jsx-runtime\";\nvar OrderCreateRefund = () => {\n  const { t } = useTranslation2();\n  const params = useParams();\n  const { order } = useOrder(params.id, {\n    fields: DEFAULT_FIELDS\n  });\n  const {\n    refund_reasons: refundReasons,\n    isLoading: isRefundReasonsLoading,\n    isError: isRefundReasonsError,\n    error: refundReasonsError\n  } = useRefundReasons();\n  if (isRefundReasonsError) {\n    throw refundReasonsError;\n  }\n  return /* @__PURE__ */ jsxs2(RouteDrawer, { children: [\n    /* @__PURE__ */ jsx2(RouteDrawer.Header, { children: /* @__PURE__ */ jsx2(Heading, { children: t(\"orders.payment.createRefund\") }) }),\n    order && !isRefundReasonsLoading && refundReasons && /* @__PURE__ */ jsx2(CreateRefundForm, { order, refundReasons })\n  ] });\n};\nexport {\n  OrderCreateRefund as Component\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqFA,mBAAmC;AAMnC,yBAA0B;AA0L1B,IAAAA,sBAA2C;AAzL3C,IAAI,qBAAyB,WAAO;AAAA,EAClC,QAAY,WAAO,EAAE,GAAO,WAAO,CAAC;AAAA,EACpC,kBAAsB,WAAO,EAAE,QAAQ;AAAA,EACvC,MAAU,WAAO,EAAE,SAAS;AAC9B,CAAC;AACD,IAAI,mBAAmB,CAAC;AAAA,EACtB;AAAA,EACA;AACF,MAAM;AACJ,QAAM,EAAE,GAAAC,GAAE,IAAI,eAAe;AAC7B,QAAM,EAAE,cAAc,IAAI,cAAc;AACxC,QAAM,WAAW,YAAY;AAC7B,QAAM,CAAC,YAAY,IAAI,gBAAgB;AACvC,QAAM,YAAY,aAAa,IAAI,WAAW;AAC9C,QAAM,WAAW,qBAAqB,KAAK;AAC3C,QAAM,UAAU,SAAS,KAAK,CAAC,MAAM,EAAE,OAAO,SAAS;AACvD,QAAM,iBAAgB,mCAAS,WAAU;AACzC,QAAM,eAAW;AAAA,IACf,MAAM,WAAW,MAAM,cAAc,YAAY,CAAC;AAAA,IAClD,CAAC,MAAM,aAAa;AAAA,EACtB;AACA,QAAM,OAAO,QAAQ;AAAA,IACnB,eAAe;AAAA,MACb,QAAQ;AAAA,MACR,MAAM;AAAA,IACR;AAAA,IACA,UAAU,EAAY,kBAAkB;AAAA,EAC1C,CAAC;AACD,8BAAU,MAAM;AACd,UAAM,oBAAoB,MAAM,QAAQ;AACxC,UAAM,kBAAiB,mCAAS,WAAU;AAC1C,UAAM,gBAAgB,oBAAoB,IAAI,KAAK,IAAI,mBAAmB,cAAc,IAAI;AAC5F,UAAM,mBAAmB,gBAAgB,IAAI,gBAAgB,KAAK;AAClE,SAAK,SAAS,UAAU,gBAAgB;AAAA,EAC1C,GAAG,CAAC,OAAO,CAAC;AACZ,QAAM,EAAE,aAAa,UAAU,IAAI,iBAAiB,MAAM,IAAI,mCAAS,EAAE;AACzE,QAAM,eAAe,KAAK,aAAa,OAAO,SAAS;AACrD,UAAM;AAAA,MACJ;AAAA,QACE,QAAQ,WAAW,KAAK,MAAM;AAAA,QAC9B,kBAAkB,KAAK;AAAA,QACvB,MAAM,KAAK;AAAA,MACb;AAAA,MACA;AAAA,QACE,WAAW,MAAM;AACf,gBAAM;AAAA,YACJA,GAAE,uCAAuC;AAAA,cACvC,QAAQ,eAAe,KAAK,QAAQ,mCAAS,aAAa;AAAA,YAC5D,CAAC;AAAA,UACH;AACA,wBAAc;AAAA,QAChB;AAAA,QACA,SAAS,CAAC,UAAU;AAClB,gBAAM,MAAM,MAAM,OAAO;AAAA,QAC3B;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AACD,aAAuB,wBAAI,YAAY,MAAM,EAAE,MAAM,cAA0B;AAAA,IAC7E;AAAA,IACA;AAAA,MACE,UAAU;AAAA,MACV,WAAW;AAAA,MACX,UAAU;AAAA,YACQ,wBAAI,YAAY,MAAM,EAAE,WAAW,wBAAwB,cAA0B,yBAAK,OAAO,EAAE,WAAW,yBAAyB,UAAU;AAAA,cAC/I;AAAA,YACd;AAAA,YACA;AAAA,cACE,OAAO,mCAAS;AAAA,cAChB,eAAe,CAAC,UAAU;AACxB,yBAAS,WAAW,MAAM,EAAE,qBAAqB,KAAK,IAAI;AAAA,kBACxD,SAAS;AAAA,gBACX,CAAC;AAAA,cACH;AAAA,cACA,UAAU;AAAA,oBACQ,wBAAI,OAAO,EAAE,WAAW,qDAAqD,UAAUA,GAAE,sCAAsC,EAAE,CAAC;AAAA,oBAClI,wBAAI,OAAO,SAAS,EAAE,cAA0B;AAAA,kBAC9D,OAAO;AAAA,kBACP;AAAA,oBACE,aAAaA,GAAE,sCAAsC;AAAA,kBACvD;AAAA,gBACF,EAAE,CAAC;AAAA,oBACa,wBAAI,OAAO,SAAS,EAAE,UAAU,SAAS,IAAI,CAAC,aAAa;AACzE,wBAAM,gBAAgB,SAAS,QAAQ;AAAA,oBACrC,CAAC,KAAK,SAAS,KAAK,SAAS;AAAA,oBAC7B;AAAA,kBACF;AACA,6BAAuB;AAAA,oBACrB,OAAO;AAAA,oBACP;AAAA,sBACE,OAAO,SAAS;AAAA,sBAChB,UAAU,CAAC,CAAC,SAAS,eAAe,iBAAiB,SAAS;AAAA,sBAC9D,WAAW;AAAA,sBACX,UAAU;AAAA,4BACQ,yBAAK,QAAQ,EAAE,UAAU;AAAA,0BACvC;AAAA,4BACE,SAAS;AAAA,4BACT,SAAS;AAAA,0BACX;AAAA,0BACA;AAAA,wBACF,EAAE,CAAC;AAAA,4BACa,wBAAI,QAAQ,EAAE,UAAU,eAAe,SAAS,WAAW,EAAE,CAAC;AAAA,4BAC9D,yBAAK,QAAQ,EAAE,UAAU;AAAA,0BACvC;AAAA,0BACA,SAAS,GAAG,UAAU,EAAE;AAAA,0BACxB;AAAA,wBACF,EAAE,CAAC;AAAA,sBACL;AAAA,oBACF;AAAA,oBACA,SAAS;AAAA,kBACX;AAAA,gBACF,CAAC,EAAE,CAAC;AAAA,cACN;AAAA,YACF;AAAA,UACF;AAAA,cACgB;AAAA,YACd,KAAK;AAAA,YACL;AAAA,cACE,SAAS,KAAK;AAAA,cACd,MAAM;AAAA,cACN,OAAO;AAAA,gBACL,UAAU;AAAA,gBACV,KAAK;AAAA,gBACL,KAAK;AAAA,cACP;AAAA,cACA,QAAQ,CAAC,EAAE,OAAO,EAAE,UAAU,GAAG,MAAM,EAAE,MAAM;AAC7C,2BAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,sBACjC,wBAAI,KAAK,OAAO,EAAE,UAAUA,GAAE,eAAe,EAAE,CAAC;AAAA,sBAChD,wBAAI,KAAK,SAAS,EAAE,cAA0B;AAAA,oBAC5D;AAAA,oBACA;AAAA,sBACE,GAAG;AAAA,sBACH,KAAK;AAAA,sBACL,aAAa,YAAY;AAAA,wBACvB,OAAO;AAAA,wBACP,cAAc,SAAS;AAAA,sBACzB,CAAC;AAAA,sBACD,cAAc,SAAS;AAAA,sBACvB,QAAQ,SAAS;AAAA,sBACjB,MAAM,SAAS;AAAA,sBACf,OAAO,MAAM;AAAA,sBACb,eAAe,CAAC,QAAQ,OAAO,WAAW,UAAS,iCAAQ,SAAQ,iCAAQ,QAAQ,EAAE;AAAA,sBACrF,WAAW;AAAA,oBACb;AAAA,kBACF,EAAE,CAAC;AAAA,sBACa,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,gBAC3C,EAAE,CAAC;AAAA,cACL;AAAA,YACF;AAAA,UACF;AAAA,cACgB;AAAA,YACd,KAAK;AAAA,YACL;AAAA,cACE,SAAS,KAAK;AAAA,cACd,MAAM;AAAA,cACN,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,2BAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,sBACjC,wBAAI,KAAK,OAAO,EAAE,UAAUA,GAAE,aAAa,EAAE,CAAC;AAAA,sBAC9C,wBAAI,KAAK,SAAS,EAAE,cAA0B,wBAAI,UAAU,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC;AAAA,sBAC3E,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,gBAC3C,EAAE,CAAC;AAAA,cACL;AAAA,YACF;AAAA,UACF;AAAA,QACF,EAAE,CAAC,EAAE,CAAC;AAAA,YACU,wBAAI,YAAY,QAAQ,EAAE,cAA0B,yBAAK,OAAO,EAAE,WAAW,yCAAyC,UAAU;AAAA,cAC9H,wBAAI,YAAY,OAAO,EAAE,SAAS,MAAM,cAA0B,wBAAI,QAAQ,EAAE,SAAS,aAAa,MAAM,SAAS,UAAUA,GAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;AAAA,cACvJ;AAAA,YACd;AAAA,YACA;AAAA,cACE,WAAW;AAAA,cACX,MAAM;AAAA,cACN,SAAS;AAAA,cACT,MAAM;AAAA,cACN,UAAU,CAAC,CAAC,OAAO,KAAK,KAAK,UAAU,UAAU,CAAC,CAAC,EAAE;AAAA,cACrD,UAAUA,GAAE,cAAc;AAAA,YAC5B;AAAA,UACF;AAAA,QACF,EAAE,CAAC,EAAE,CAAC;AAAA,MACR;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;AAIA,IAAI,oBAAoB,MAAM;AAC5B,QAAM,EAAE,GAAAA,GAAE,IAAI,eAAgB;AAC9B,QAAM,SAAS,UAAU;AACzB,QAAM,EAAE,MAAM,IAAI,SAAS,OAAO,IAAI;AAAA,IACpC,QAAQ;AAAA,EACV,CAAC;AACD,QAAM;AAAA,IACJ,gBAAgB;AAAA,IAChB,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT,IAAI,iBAAiB;AACrB,MAAI,sBAAsB;AACxB,UAAM;AAAA,EACR;AACA,aAAuB,oBAAAC,MAAM,aAAa,EAAE,UAAU;AAAA,QACpC,oBAAAC,KAAK,YAAY,QAAQ,EAAE,cAA0B,oBAAAA,KAAK,SAAS,EAAE,UAAUF,GAAE,6BAA6B,EAAE,CAAC,EAAE,CAAC;AAAA,IACpI,SAAS,CAAC,0BAA0B,qBAAiC,oBAAAE,KAAK,kBAAkB,EAAE,OAAO,cAAc,CAAC;AAAA,EACtH,EAAE,CAAC;AACL;", "names": ["import_jsx_runtime", "t", "jsxs2", "jsx2"]}