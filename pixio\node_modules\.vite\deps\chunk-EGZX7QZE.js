import {
  DateCell
} from "./chunk-ZJX5R5NM.js";
import {
  TextCell,
  TextHeader
} from "./chunk-C43B7AQX.js";
import {
  PlaceholderCell
} from "./chunk-S5JEKJNE.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import {
  createColumnHelper
} from "./chunk-YP2LLXWB.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/chunk-BFAYZKJV.mjs
var import_react = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var DescriptionCell = ({ description }) => {
  if (!description) {
    return (0, import_jsx_runtime.jsx)(PlaceholderCell, {});
  }
  return (0, import_jsx_runtime.jsx)("div", { className: "flex h-full w-full items-center overflow-hidden", children: (0, import_jsx_runtime.jsx)("span", { className: "truncate", children: description }) });
};
var DescriptionHeader = () => {
  const { t } = useTranslation();
  return (0, import_jsx_runtime.jsx)("div", { className: "flex h-full w-full items-center", children: (0, import_jsx_runtime.jsx)("span", { className: "truncate", children: t("fields.description") }) });
};
var NameCell = ({ name }) => {
  if (!name) {
    return (0, import_jsx_runtime2.jsx)(PlaceholderCell, {});
  }
  return (0, import_jsx_runtime2.jsx)("div", { className: "flex h-full w-full items-center overflow-hidden", children: (0, import_jsx_runtime2.jsx)("span", { className: "truncate", children: name }) });
};
var NameHeader = () => {
  const { t } = useTranslation();
  return (0, import_jsx_runtime2.jsx)("div", { className: "flex h-full w-full items-center", children: (0, import_jsx_runtime2.jsx)("span", { className: "truncate", children: t("fields.name") }) });
};
var columnHelper = createColumnHelper();
var useCampaignTableColumns = () => {
  const { t } = useTranslation();
  return (0, import_react.useMemo)(
    () => [
      columnHelper.accessor("name", {
        header: () => (0, import_jsx_runtime3.jsx)(NameHeader, {}),
        cell: ({ getValue }) => (0, import_jsx_runtime3.jsx)(NameCell, { name: getValue() })
      }),
      columnHelper.accessor("description", {
        header: () => (0, import_jsx_runtime3.jsx)(DescriptionHeader, {}),
        cell: ({ getValue }) => (0, import_jsx_runtime3.jsx)(DescriptionCell, { description: getValue() })
      }),
      columnHelper.accessor("campaign_identifier", {
        header: () => (0, import_jsx_runtime3.jsx)(TextHeader, { text: t("campaigns.fields.identifier") }),
        cell: ({ getValue }) => {
          const value = getValue();
          return (0, import_jsx_runtime3.jsx)(TextCell, { text: value });
        }
      }),
      columnHelper.accessor("starts_at", {
        header: () => (0, import_jsx_runtime3.jsx)(TextHeader, { text: t("campaigns.fields.start_date") }),
        cell: ({ getValue }) => {
          const value = getValue();
          if (!value) {
            return;
          }
          const date = new Date(value);
          return (0, import_jsx_runtime3.jsx)(DateCell, { date });
        }
      }),
      columnHelper.accessor("ends_at", {
        header: () => (0, import_jsx_runtime3.jsx)(TextHeader, { text: t("campaigns.fields.end_date") }),
        cell: ({ getValue }) => {
          const value = getValue();
          if (!value) {
            return;
          }
          const date = new Date(value);
          return (0, import_jsx_runtime3.jsx)(DateCell, { date });
        }
      })
    ],
    [t]
  );
};

export {
  useCampaignTableColumns
};
//# sourceMappingURL=chunk-EGZX7QZE.js.map
