{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-BKJC5BGQ.mjs"], "sourcesContent": ["// src/components/common/badge-list-summary/badge-list-summary.tsx\nimport { Badge, Tooltip, clx } from \"@medusajs/ui\";\nimport { useTranslation } from \"react-i18next\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar BadgeListSummary = ({\n  list,\n  className,\n  inline,\n  rounded = false,\n  n = 2\n}) => {\n  const { t } = useTranslation();\n  const title = t(\"general.plusCount\", {\n    count: list.length - n\n  });\n  return /* @__PURE__ */ jsxs(\n    \"div\",\n    {\n      className: clx(\n        \"text-ui-fg-subtle txt-compact-small gap-x-2 overflow-hidden\",\n        {\n          \"inline-flex\": inline,\n          flex: !inline\n        },\n        className\n      ),\n      children: [\n        list.slice(0, n).map((item) => {\n          return /* @__PURE__ */ jsx(Badge, { rounded: rounded ? \"full\" : \"base\", size: \"2xsmall\", children: item }, item);\n        }),\n        list.length > n && /* @__PURE__ */ jsx(\"div\", { className: \"whitespace-nowrap\", children: /* @__PURE__ */ jsx(\n          Tooltip,\n          {\n            content: /* @__PURE__ */ jsx(\"ul\", { children: list.slice(n).map((c) => /* @__PURE__ */ jsx(\"li\", { children: c }, c)) }),\n            children: /* @__PURE__ */ jsx(\n              Badge,\n              {\n                rounded: rounded ? \"full\" : \"base\",\n                size: \"2xsmall\",\n                className: \"cursor-default whitespace-nowrap\",\n                children: title\n              }\n            )\n          }\n        ) })\n      ]\n    }\n  );\n};\n\nexport {\n  BadgeListSummary\n};\n"], "mappings": ";;;;;;;;;;;;;;;;AAGA,yBAA0B;AAC1B,IAAI,mBAAmB,CAAC;AAAA,EACtB;AAAA,EACA;AAAA,EACA;AAAA,EACA,UAAU;AAAA,EACV,IAAI;AACN,MAAM;AACJ,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,QAAM,QAAQ,EAAE,qBAAqB;AAAA,IACnC,OAAO,KAAK,SAAS;AAAA,EACvB,CAAC;AACD,aAAuB;AAAA,IACrB;AAAA,IACA;AAAA,MACE,WAAW;AAAA,QACT;AAAA,QACA;AAAA,UACE,eAAe;AAAA,UACf,MAAM,CAAC;AAAA,QACT;AAAA,QACA;AAAA,MACF;AAAA,MACA,UAAU;AAAA,QACR,KAAK,MAAM,GAAG,CAAC,EAAE,IAAI,CAAC,SAAS;AAC7B,qBAAuB,wBAAI,OAAO,EAAE,SAAS,UAAU,SAAS,QAAQ,MAAM,WAAW,UAAU,KAAK,GAAG,IAAI;AAAA,QACjH,CAAC;AAAA,QACD,KAAK,SAAS,SAAqB,wBAAI,OAAO,EAAE,WAAW,qBAAqB,cAA0B;AAAA,UACxG;AAAA,UACA;AAAA,YACE,aAAyB,wBAAI,MAAM,EAAE,UAAU,KAAK,MAAM,CAAC,EAAE,IAAI,CAAC,UAAsB,wBAAI,MAAM,EAAE,UAAU,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC;AAAA,YACxH,cAA0B;AAAA,cACxB;AAAA,cACA;AAAA,gBACE,SAAS,UAAU,SAAS;AAAA,gBAC5B,MAAM;AAAA,gBACN,WAAW;AAAA,gBACX,UAAU;AAAA,cACZ;AAAA,YACF;AAAA,UACF;AAAA,QACF,EAAE,CAAC;AAAA,MACL;AAAA,IACF;AAAA,EACF;AACF;", "names": []}