{"version": 3, "sources": ["../../@medusajs/dashboard/dist/product-type-metadata-EEB4KPWS.mjs"], "sourcesContent": ["import {\n  MetadataForm\n} from \"./chunk-S2UEWRDA.mjs\";\nimport \"./chunk-IUCDCPJU.mjs\";\nimport \"./chunk-6HTZNHPT.mjs\";\nimport \"./chunk-4TC5YS65.mjs\";\nimport \"./chunk-LPEUYMRK.mjs\";\nimport \"./chunk-OC7BQLYI.mjs\";\nimport \"./chunk-OBQI23QM.mjs\";\nimport \"./chunk-Z5UDPQIH.mjs\";\nimport \"./chunk-KOSCMAIC.mjs\";\nimport \"./chunk-X6DSNTTX.mjs\";\nimport \"./chunk-I6E6CALJ.mjs\";\nimport {\n  useProductType,\n  useUpdateProductType\n} from \"./chunk-B4GODIOW.mjs\";\nimport \"./chunk-F6IJV2I2.mjs\";\nimport \"./chunk-QTCZFYFH.mjs\";\nimport \"./chunk-ENV6YVOM.mjs\";\nimport \"./chunk-PIR2H25N.mjs\";\nimport \"./chunk-RLY2SL5E.mjs\";\nimport \"./chunk-C5LYZZZ5.mjs\";\nimport \"./chunk-2ZKVRTBW.mjs\";\nimport \"./chunk-FO3VP56P.mjs\";\nimport \"./chunk-YS65UGPC.mjs\";\nimport \"./chunk-F6PXCY3N.mjs\";\nimport \"./chunk-3OHH43G6.mjs\";\nimport \"./chunk-G2H6MAK7.mjs\";\nimport \"./chunk-GRT22PE5.mjs\";\nimport \"./chunk-32IQRUVY.mjs\";\nimport \"./chunk-FNYASI54.mjs\";\nimport \"./chunk-FVC7M755.mjs\";\nimport \"./chunk-ZJ3OFMHB.mjs\";\nimport \"./chunk-PNU5HPGY.mjs\";\nimport \"./chunk-V2LANK5S.mjs\";\nimport \"./chunk-QZ6PT4QV.mjs\";\nimport \"./chunk-QL4XKIVL.mjs\";\nimport \"./chunk-6I62UDJA.mjs\";\nimport \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-RPUOO7AV.mjs\";\n\n// src/routes/product-types/product-type-metadata/product-type-metadata.tsx\nimport { useParams } from \"react-router-dom\";\nimport { jsx } from \"react/jsx-runtime\";\nvar ProductTypeMetadata = () => {\n  const { id } = useParams();\n  const { product_type, isPending, isError, error } = useProductType(id);\n  const { mutateAsync, isPending: isMutating } = useUpdateProductType(\n    product_type?.id\n  );\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsx(\n    MetadataForm,\n    {\n      metadata: product_type?.metadata,\n      hook: mutateAsync,\n      isPending,\n      isMutating\n    }\n  );\n};\nexport {\n  ProductTypeMetadata as Component\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8CA,yBAAoB;AACpB,IAAI,sBAAsB,MAAM;AAC9B,QAAM,EAAE,GAAG,IAAI,UAAU;AACzB,QAAM,EAAE,cAAc,WAAW,SAAS,MAAM,IAAI,eAAe,EAAE;AACrE,QAAM,EAAE,aAAa,WAAW,WAAW,IAAI;AAAA,IAC7C,6CAAc;AAAA,EAChB;AACA,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB;AAAA,IACrB;AAAA,IACA;AAAA,MACE,UAAU,6CAAc;AAAA,MACxB,MAAM;AAAA,MACN;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACF;", "names": []}