import {
  DataGrid,
  createD<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  createDataGridPriceColumns
} from "./chunk-ZOK7LZDT.js";
import "./chunk-H3DTEG3J.js";
import {
  useComboboxData
} from "./chunk-CFEMRZCK.js";
import {
  Combobox
} from "./chunk-RC2DY4WG.js";
import {
  optionalInt,
  partialFormValidation
} from "./chunk-7LOZU53L.js";
import {
  castNumber
} from "./chunk-EZLR4STK.js";
import "./chunk-EGRHWZRV.js";
import {
  KeyboundForm
} from "./chunk-DP54EP6X.js";
import "./chunk-7ANVLPZR.js";
import {
  RouteDrawer,
  RouteFocusModal,
  useRouteModal
} from "./chunk-MVVOBQIC.js";
import "./chunk-RQF55WOK.js";
import "./chunk-5QX4V4M4.js";
import "./chunk-IA4ROPJA.js";
import {
  t
} from "./chunk-WHQIBI5S.js";
import {
  numberType,
  recordType,
  stringType,
  z
} from "./chunk-4XXECALA.js";
import "./chunk-YXT43UJF.js";
import "./chunk-66DVUN72.js";
import "./chunk-NV2N3EWM.js";
import "./chunk-MPXR7HT5.js";
import {
  Form,
  useFieldArray,
  useForm,
  useWatch
} from "./chunk-XXJU43CK.js";
import "./chunk-WISME5HP.js";
import "./chunk-WAUSOHFQ.js";
import "./chunk-3ORK3QZ4.js";
import "./chunk-3A5TVVNI.js";
import "./chunk-EZ62MM7J.js";
import "./chunk-3R6LYIXM.js";
import "./chunk-2P2O4TMQ.js";
import "./chunk-KI3HEITX.js";
import "./chunk-2VUCAO7L.js";
import "./chunk-PTIULTKD.js";
import "./chunk-KISXY2LD.js";
import "./chunk-L7C3XPHJ.js";
import "./chunk-T7MG2EIJ.js";
import "./chunk-MER4VYPR.js";
import "./chunk-2AWKOUCD.js";
import "./chunk-YL3TYBFR.js";
import "./chunk-TZWW72YW.js";
import "./chunk-MSQ25CWB.js";
import "./chunk-ONYSAQ5Z.js";
import "./chunk-NIH7SAXN.js";
import "./chunk-UEJFWAFE.js";
import "./chunk-GR55MFKT.js";
import {
  useStore
} from "./chunk-AAFHKNJG.js";
import {
  useRegions
} from "./chunk-7XDBFDTZ.js";
import {
  usePricePreferences
} from "./chunk-3JKGO5XL.js";
import "./chunk-OISUTS7G.js";
import {
  useCreateProductVariant,
  useProduct
} from "./chunk-AJYMIHLQ.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import {
  sdk
} from "./chunk-JFCIYOH4.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-5GF3RGIE.js";
import {
  useParams
} from "./chunk-T7YBVUWZ.js";
import {
  Button,
  Heading,
  IconButton,
  Input,
  Label,
  ProgressTabs,
  Switch,
  XMarkMini,
  toast
} from "./chunk-YP2LLXWB.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-ZSQ362B4.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/product-create-variant-HIJNEOSH.mjs
var import_react = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var import_react2 = __toESM(require_react(), 1);
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime4 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime5 = __toESM(require_jsx_runtime(), 1);
var CreateProductVariantSchema = z.object({
  title: z.string().min(1),
  sku: z.string().optional(),
  manage_inventory: z.boolean().optional(),
  allow_backorder: z.boolean().optional(),
  inventory_kit: z.boolean().optional(),
  options: z.record(z.string()),
  prices: recordType(stringType(), stringType().or(numberType()).optional()).optional(),
  inventory: z.array(
    z.object({
      inventory_item_id: z.string(),
      required_quantity: optionalInt
    })
  ).optional()
});
var CreateVariantDetailsSchema = CreateProductVariantSchema.pick({
  title: true,
  sku: true,
  manage_inventory: true,
  allow_backorder: true,
  inventory_kit: true,
  options: true
});
var CreateVariantDetailsFields = Object.keys(
  CreateVariantDetailsSchema.shape
);
var CreateVariantPriceSchema = CreateProductVariantSchema.pick({
  prices: true
});
var CreateVariantPriceFields = Object.keys(
  CreateVariantPriceSchema.shape
);
function DetailsTab({ form, product }) {
  const { t: t2 } = useTranslation();
  const manageInventoryEnabled = useWatch({
    control: form.control,
    name: "manage_inventory"
  });
  return (0, import_jsx_runtime.jsx)("div", { className: "flex flex-1 flex-col items-center overflow-y-auto", children: (0, import_jsx_runtime.jsxs)("div", { className: "flex w-full max-w-[720px] flex-col gap-y-8 px-8 py-16", children: [
    (0, import_jsx_runtime.jsx)(Heading, { level: "h1", children: t2("products.variant.create.header") }),
    (0, import_jsx_runtime.jsxs)("div", { className: "my-8 grid grid-cols-1 gap-4 md:grid-cols-2", children: [
      (0, import_jsx_runtime.jsx)(
        Form.Field,
        {
          control: form.control,
          name: "title",
          render: ({ field }) => {
            return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
              (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("fields.title") }),
              (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(Input, { ...field }) }),
              (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
            ] });
          }
        }
      ),
      (0, import_jsx_runtime.jsx)(
        Form.Field,
        {
          control: form.control,
          name: "sku",
          render: ({ field }) => {
            return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
              (0, import_jsx_runtime.jsx)(Form.Label, { optional: true, children: t2("fields.sku") }),
              (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(Input, { ...field }) }),
              (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
            ] });
          }
        }
      ),
      product.options.map((option) => (0, import_jsx_runtime.jsx)(
        Form.Field,
        {
          control: form.control,
          name: `options.${option.title}`,
          render: ({ field: { value, onChange, ...field } }) => {
            return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
              (0, import_jsx_runtime.jsx)(Form.Label, { children: option.title }),
              (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(
                Combobox,
                {
                  value,
                  onChange: (v) => {
                    onChange(v);
                  },
                  ...field,
                  options: option.values.map((v) => ({
                    label: v.value,
                    value: v.value
                  }))
                }
              ) })
            ] });
          }
        },
        option.id
      ))
    ] }),
    (0, import_jsx_runtime.jsxs)("div", { className: "flex flex-col gap-y-4", children: [
      (0, import_jsx_runtime.jsx)(
        Form.Field,
        {
          control: form.control,
          name: "manage_inventory",
          render: ({ field: { value, onChange, ...field } }) => {
            return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
              (0, import_jsx_runtime.jsxs)("div", { className: "bg-ui-bg-component shadow-elevation-card-rest flex gap-x-3 rounded-lg p-4", children: [
                (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(
                  Switch,
                  {
                    className: "mt-[2px]",
                    checked: value,
                    onCheckedChange: (checked) => onChange(!!checked),
                    ...field
                  }
                ) }),
                (0, import_jsx_runtime.jsxs)("div", { className: "flex flex-col", children: [
                  (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("products.variant.inventory.manageInventoryLabel") }),
                  (0, import_jsx_runtime.jsx)(Form.Hint, { children: t2("products.variant.inventory.manageInventoryHint") })
                ] })
              ] }),
              (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
            ] });
          }
        }
      ),
      (0, import_jsx_runtime.jsx)(
        Form.Field,
        {
          control: form.control,
          name: "allow_backorder",
          disabled: !manageInventoryEnabled,
          render: ({ field: { value, onChange, ...field } }) => {
            return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
              (0, import_jsx_runtime.jsxs)("div", { className: "bg-ui-bg-component shadow-elevation-card-rest flex gap-x-3 rounded-lg p-4", children: [
                (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(
                  Switch,
                  {
                    checked: value,
                    onCheckedChange: (checked) => onChange(!!checked),
                    ...field,
                    disabled: !manageInventoryEnabled
                  }
                ) }),
                (0, import_jsx_runtime.jsxs)("div", { className: "flex flex-col", children: [
                  (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("products.variant.inventory.allowBackordersLabel") }),
                  (0, import_jsx_runtime.jsx)(Form.Hint, { children: t2("products.variant.inventory.allowBackordersHint") })
                ] })
              ] }),
              (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
            ] });
          }
        }
      ),
      (0, import_jsx_runtime.jsx)(
        Form.Field,
        {
          control: form.control,
          name: "inventory_kit",
          render: ({ field: { value, onChange, ...field } }) => {
            return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
              (0, import_jsx_runtime.jsxs)("div", { className: "bg-ui-bg-component shadow-elevation-card-rest flex gap-x-3 rounded-lg p-4", children: [
                (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(
                  Switch,
                  {
                    checked: value,
                    onCheckedChange: (checked) => onChange(!!checked),
                    ...field,
                    disabled: !manageInventoryEnabled
                  }
                ) }),
                (0, import_jsx_runtime.jsxs)("div", { className: "flex flex-col", children: [
                  (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("products.variant.inventory.inventoryKit") }),
                  (0, import_jsx_runtime.jsx)(Form.Hint, { children: t2("products.variant.inventory.inventoryKitHint") })
                ] })
              ] }),
              (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
            ] });
          }
        }
      )
    ] })
  ] }) });
}
var details_tab_default = DetailsTab;
function InventoryKitTab({ form }) {
  const { t: t2 } = useTranslation();
  const inventory = useFieldArray({
    control: form.control,
    name: `inventory`
  });
  const inventoryFormData = inventory.fields;
  const items = useComboboxData({
    queryKey: ["inventory_items"],
    queryFn: (params) => sdk.admin.inventoryItem.list(params),
    getOptions: (data) => data.inventory_items.map((item) => ({
      label: item.title,
      value: item.id
    }))
  });
  const isItemOptionDisabled = (option, inventoryIndex) => {
    return inventoryFormData == null ? void 0 : inventoryFormData.some(
      (i, index) => index != inventoryIndex && i.inventory_item_id === option.value
    );
  };
  return (0, import_jsx_runtime2.jsx)("div", { className: "flex flex-col items-center p-16", children: (0, import_jsx_runtime2.jsx)("div", { className: "flex w-full max-w-[720px] flex-col gap-y-8", children: (0, import_jsx_runtime2.jsxs)("div", { id: "organize", className: "flex flex-col gap-y-8", children: [
    (0, import_jsx_runtime2.jsx)(Heading, { children: t2("products.create.inventory.heading") }),
    (0, import_jsx_runtime2.jsxs)("div", { className: "grid gap-y-4", children: [
      (0, import_jsx_runtime2.jsxs)("div", { className: "flex items-start justify-between gap-x-4", children: [
        (0, import_jsx_runtime2.jsxs)("div", { className: "flex flex-col", children: [
          (0, import_jsx_runtime2.jsx)(Form.Label, { children: form.getValues("title") }),
          (0, import_jsx_runtime2.jsx)(Form.Hint, { children: t2("products.create.inventory.label") })
        ] }),
        (0, import_jsx_runtime2.jsx)(
          Button,
          {
            size: "small",
            variant: "secondary",
            type: "button",
            onClick: () => {
              inventory.append({
                inventory_item_id: "",
                required_quantity: ""
              });
            },
            children: t2("actions.add")
          }
        )
      ] }),
      inventory.fields.map((inventoryItem, inventoryIndex) => (0, import_jsx_runtime2.jsxs)(
        "li",
        {
          className: "bg-ui-bg-component shadow-elevation-card-rest grid grid-cols-[1fr_28px] items-center gap-1.5 rounded-xl p-1.5",
          children: [
            (0, import_jsx_runtime2.jsxs)("div", { className: "grid grid-cols-[min-content,1fr] items-center gap-1.5", children: [
              (0, import_jsx_runtime2.jsx)("div", { className: "flex items-center px-2 py-1.5", children: (0, import_jsx_runtime2.jsx)(
                Label,
                {
                  size: "xsmall",
                  weight: "plus",
                  className: "text-ui-fg-subtle",
                  htmlFor: `inventory.${inventoryIndex}.inventory_item_id`,
                  children: t2("fields.item")
                }
              ) }),
              (0, import_jsx_runtime2.jsx)(
                Form.Field,
                {
                  control: form.control,
                  name: `inventory.${inventoryIndex}.inventory_item_id`,
                  render: ({ field }) => {
                    return (0, import_jsx_runtime2.jsx)(Form.Item, { children: (0, import_jsx_runtime2.jsx)(Form.Control, { children: (0, import_jsx_runtime2.jsx)(
                      Combobox,
                      {
                        ...field,
                        options: items.options.map((o) => ({
                          ...o,
                          disabled: isItemOptionDisabled(
                            o,
                            inventoryIndex
                          )
                        })),
                        searchValue: items.searchValue,
                        onSearchValueChange: items.onSearchValueChange,
                        fetchNextPage: items.fetchNextPage,
                        className: "bg-ui-bg-field-component hover:bg-ui-bg-field-component-hover",
                        placeholder: t2(
                          "products.create.inventory.itemPlaceholder"
                        )
                      }
                    ) }) });
                  }
                }
              ),
              (0, import_jsx_runtime2.jsx)("div", { className: "flex items-center px-2 py-1.5", children: (0, import_jsx_runtime2.jsx)(
                Label,
                {
                  size: "xsmall",
                  weight: "plus",
                  className: "text-ui-fg-subtle",
                  htmlFor: `inventory.${inventoryIndex}.required_quantity`,
                  children: t2("fields.quantity")
                }
              ) }),
              (0, import_jsx_runtime2.jsx)(
                Form.Field,
                {
                  control: form.control,
                  name: `inventory.${inventoryIndex}.required_quantity`,
                  render: ({ field: { onChange, value, ...field } }) => {
                    return (0, import_jsx_runtime2.jsxs)(Form.Item, { children: [
                      (0, import_jsx_runtime2.jsx)(Form.Control, { children: (0, import_jsx_runtime2.jsx)(
                        Input,
                        {
                          type: "number",
                          className: "bg-ui-bg-field-component",
                          min: 0,
                          value,
                          onChange: (e) => {
                            const value2 = e.target.value;
                            if (value2 === "") {
                              onChange(null);
                            } else {
                              onChange(Number(value2));
                            }
                          },
                          ...field,
                          placeholder: t2(
                            "products.create.inventory.quantityPlaceholder"
                          )
                        }
                      ) }),
                      (0, import_jsx_runtime2.jsx)(Form.ErrorMessage, {})
                    ] });
                  }
                }
              )
            ] }),
            (0, import_jsx_runtime2.jsx)(
              IconButton,
              {
                type: "button",
                size: "small",
                variant: "transparent",
                className: "text-ui-fg-muted",
                onClick: () => inventory.remove(inventoryIndex),
                children: (0, import_jsx_runtime2.jsx)(XMarkMini, {})
              }
            )
          ]
        },
        inventoryItem.id
      ))
    ] })
  ] }) }) });
}
var inventory_kit_tab_default = InventoryKitTab;
function PricingTab({ form }) {
  const { store } = useStore();
  const { regions } = useRegions({ limit: 9999 });
  const { price_preferences: pricePreferences } = usePricePreferences({});
  const { setCloseOnEscape } = useRouteModal();
  const columns = useVariantPriceGridColumns({
    currencies: store == null ? void 0 : store.supported_currencies,
    regions,
    pricePreferences
  });
  const variant = useWatch({
    control: form.control
  });
  return (0, import_jsx_runtime3.jsx)(
    DataGrid,
    {
      columns,
      data: [variant],
      state: form,
      onEditingChange: (editing) => setCloseOnEscape(!editing)
    }
  );
}
var columnHelper = createDataGridHelper();
var useVariantPriceGridColumns = ({
  currencies = [],
  regions = [],
  pricePreferences = []
}) => {
  const { t: t2 } = useTranslation();
  return (0, import_react2.useMemo)(() => {
    return [
      columnHelper.column({
        id: t2("fields.title"),
        header: t2("fields.title"),
        cell: (context) => {
          const entity = context.row.original;
          return (0, import_jsx_runtime3.jsx)(DataGrid.ReadonlyCell, { context, children: (0, import_jsx_runtime3.jsx)("div", { className: "flex h-full w-full items-center gap-x-2 overflow-hidden", children: (0, import_jsx_runtime3.jsx)("span", { className: "truncate", children: entity.title }) }) });
        },
        disableHiding: true
      }),
      ...createDataGridPriceColumns({
        currencies: currencies.map((c) => c.currency_code),
        regions,
        pricePreferences,
        getFieldName: (context, value) => {
          var _a;
          if ((_a = context.column.id) == null ? void 0 : _a.startsWith("currency_prices")) {
            return `prices.${value}`;
          }
          return `prices.${value}`;
        },
        t: t2
      })
    ];
  }, [t2, currencies, regions, pricePreferences]);
};
var pricing_tab_default = PricingTab;
var initialTabState = {
  [
    "detail"
    /* DETAIL */
  ]: "in-progress",
  [
    "price"
    /* PRICE */
  ]: "not-started",
  [
    "inventory"
    /* INVENTORY */
  ]: "not-started"
};
var CreateProductVariantForm = ({
  product
}) => {
  const { t: t2 } = useTranslation();
  const { handleSuccess } = useRouteModal();
  const [tab, setTab] = (0, import_react.useState)(
    "detail"
    /* DETAIL */
  );
  const [tabState, setTabState] = (0, import_react.useState)(initialTabState);
  const form = useForm({
    defaultValues: {
      sku: "",
      title: "",
      manage_inventory: false,
      allow_backorder: false,
      inventory_kit: false,
      options: {}
    },
    resolver: t(CreateProductVariantSchema)
  });
  const { mutateAsync, isPending } = useCreateProductVariant(product.id);
  const { regions } = useRegions({ limit: 9999 });
  const regionsCurrencyMap = (0, import_react.useMemo)(() => {
    if (!(regions == null ? void 0 : regions.length)) {
      return {};
    }
    return regions.reduce((acc, reg) => {
      acc[reg.id] = reg.currency_code;
      return acc;
    }, {});
  }, [regions]);
  const isManageInventoryEnabled = useWatch({
    control: form.control,
    name: "manage_inventory"
  });
  const isInventoryKitEnabled = useWatch({
    control: form.control,
    name: "inventory_kit"
  });
  const inventoryField = useFieldArray({
    control: form.control,
    name: `inventory`
  });
  const inventoryTabEnabled = isManageInventoryEnabled && isInventoryKitEnabled;
  const tabOrder = (0, import_react.useMemo)(() => {
    if (inventoryTabEnabled) {
      return [
        "detail",
        "price",
        "inventory"
        /* INVENTORY */
      ];
    }
    return [
      "detail",
      "price"
      /* PRICE */
    ];
  }, [inventoryTabEnabled]);
  (0, import_react.useEffect)(() => {
    if (isInventoryKitEnabled && inventoryField.fields.length === 0) {
      inventoryField.append({
        inventory_item_id: "",
        required_quantity: void 0
      });
    }
  }, [isInventoryKitEnabled]);
  const handleChangeTab = (update) => {
    if (tab === update) {
      return;
    }
    if (tabOrder.indexOf(update) < tabOrder.indexOf(tab)) {
      const isCurrentTabDirty = false;
      setTabState((prev) => ({
        ...prev,
        [tab]: isCurrentTabDirty ? prev[tab] : "not-started",
        [update]: "in-progress"
      }));
      setTab(update);
      return;
    }
    const tabs = tabOrder.slice(0, tabOrder.indexOf(update));
    for (const tab2 of tabs) {
      if (tab2 === "detail") {
        if (!partialFormValidation(
          form,
          CreateVariantDetailsFields,
          CreateVariantDetailsSchema
        )) {
          setTabState((prev) => ({
            ...prev,
            [tab2]: "in-progress"
          }));
          setTab(tab2);
          return;
        }
        setTabState((prev) => ({
          ...prev,
          [tab2]: "completed"
        }));
      } else if (tab2 === "price") {
        if (!partialFormValidation(
          form,
          CreateVariantPriceFields,
          CreateVariantPriceSchema
        )) {
          setTabState((prev) => ({
            ...prev,
            [tab2]: "in-progress"
          }));
          setTab(tab2);
          return;
        }
        setTabState((prev) => ({
          ...prev,
          [tab2]: "completed"
        }));
      }
    }
    setTabState((prev) => ({
      ...prev,
      [tab]: "completed",
      [update]: "in-progress"
    }));
    setTab(update);
  };
  const handleNextTab = (tab2) => {
    if (tabOrder.indexOf(tab2) + 1 >= tabOrder.length) {
      return;
    }
    const nextTab = tabOrder[tabOrder.indexOf(tab2) + 1];
    handleChangeTab(nextTab);
  };
  const handleSubmit = form.handleSubmit(async (data) => {
    const { allow_backorder, manage_inventory, sku, title } = data;
    await mutateAsync(
      {
        title,
        sku: sku || void 0,
        allow_backorder,
        manage_inventory,
        options: data.options,
        prices: Object.entries(data.prices ?? {}).map(([currencyOrRegion, value]) => {
          if (value === "" || value === void 0) {
            return void 0;
          }
          const ret = {};
          const amount = castNumber(value);
          if (currencyOrRegion.startsWith("reg_")) {
            ret.rules = { region_id: currencyOrRegion };
            ret.currency_code = regionsCurrencyMap[currencyOrRegion];
          } else {
            ret.currency_code = currencyOrRegion;
          }
          ret.amount = amount;
          return ret;
        }).filter(Boolean),
        inventory_items: (data.inventory || []).map((i) => {
          if (!i.required_quantity || !i.inventory_item_id) {
            return false;
          }
          return {
            ...i,
            required_quantity: castNumber(i.required_quantity)
          };
        }).filter(Boolean)
      },
      {
        onSuccess: () => {
          handleSuccess();
        },
        onError: (error) => {
          toast.error(error.message);
        }
      }
    );
  });
  return (0, import_jsx_runtime4.jsx)(RouteFocusModal.Form, { form, children: (0, import_jsx_runtime4.jsx)(
    ProgressTabs,
    {
      value: tab,
      onValueChange: (tab2) => handleChangeTab(tab2),
      className: "flex h-full flex-col overflow-hidden",
      children: (0, import_jsx_runtime4.jsxs)(
        KeyboundForm,
        {
          onSubmit: handleSubmit,
          className: "flex h-full flex-col overflow-hidden",
          children: [
            (0, import_jsx_runtime4.jsx)(RouteFocusModal.Header, { children: (0, import_jsx_runtime4.jsx)("div", { className: "flex w-full items-center justify-between gap-x-4", children: (0, import_jsx_runtime4.jsx)("div", { className: "-my-2 w-full max-w-[600px] border-l", children: (0, import_jsx_runtime4.jsxs)(ProgressTabs.List, { className: "grid w-full grid-cols-3", children: [
              (0, import_jsx_runtime4.jsx)(
                ProgressTabs.Trigger,
                {
                  status: tabState.detail,
                  value: "detail",
                  children: t2("priceLists.create.tabs.details")
                }
              ),
              (0, import_jsx_runtime4.jsx)(
                ProgressTabs.Trigger,
                {
                  status: tabState.price,
                  value: "price",
                  children: t2("priceLists.create.tabs.prices")
                }
              ),
              !!inventoryTabEnabled && (0, import_jsx_runtime4.jsx)(
                ProgressTabs.Trigger,
                {
                  status: tabState.inventory,
                  value: "inventory",
                  children: t2("products.create.tabs.inventory")
                }
              )
            ] }) }) }) }),
            (0, import_jsx_runtime4.jsxs)(RouteFocusModal.Body, { className: "size-full overflow-hidden", children: [
              (0, import_jsx_runtime4.jsx)(
                ProgressTabs.Content,
                {
                  className: "size-full overflow-y-auto",
                  value: "detail",
                  children: (0, import_jsx_runtime4.jsx)(details_tab_default, { form, product })
                }
              ),
              (0, import_jsx_runtime4.jsx)(
                ProgressTabs.Content,
                {
                  className: "size-full overflow-y-auto",
                  value: "price",
                  children: (0, import_jsx_runtime4.jsx)(pricing_tab_default, { form })
                }
              ),
              !!inventoryTabEnabled && (0, import_jsx_runtime4.jsx)(
                ProgressTabs.Content,
                {
                  className: "size-full overflow-hidden",
                  value: "inventory",
                  children: (0, import_jsx_runtime4.jsx)(inventory_kit_tab_default, { form })
                }
              )
            ] }),
            (0, import_jsx_runtime4.jsx)(RouteFocusModal.Footer, { children: (0, import_jsx_runtime4.jsxs)("div", { className: "flex items-center justify-end gap-x-2", children: [
              (0, import_jsx_runtime4.jsx)(RouteDrawer.Close, { asChild: true, children: (0, import_jsx_runtime4.jsx)(Button, { variant: "secondary", size: "small", children: t2("actions.cancel") }) }),
              (0, import_jsx_runtime4.jsx)(
                PrimaryButton,
                {
                  tab,
                  next: handleNextTab,
                  isLoading: isPending,
                  inventoryTabEnabled: !!inventoryTabEnabled
                }
              )
            ] }) })
          ]
        }
      )
    }
  ) });
};
var PrimaryButton = ({
  tab,
  next,
  isLoading,
  inventoryTabEnabled
}) => {
  const { t: t2 } = useTranslation();
  if (inventoryTabEnabled && tab === "inventory" || !inventoryTabEnabled && tab === "price") {
    return (0, import_jsx_runtime4.jsx)(
      Button,
      {
        type: "submit",
        variant: "primary",
        size: "small",
        isLoading,
        children: t2("actions.save")
      },
      "submit-button"
    );
  }
  return (0, import_jsx_runtime4.jsx)(
    Button,
    {
      type: "button",
      variant: "primary",
      size: "small",
      onClick: () => next(tab),
      children: t2("actions.continue")
    },
    "next-button"
  );
};
var ProductCreateVariant = () => {
  const { id } = useParams();
  const { product, isLoading, isError, error } = useProduct(id);
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime5.jsx)(RouteFocusModal, { children: !isLoading && product && (0, import_jsx_runtime5.jsx)(CreateProductVariantForm, { product }) });
};
export {
  ProductCreateVariant as Component
};
//# sourceMappingURL=product-create-variant-HIJNEOSH-QJT7GSZN.js.map
