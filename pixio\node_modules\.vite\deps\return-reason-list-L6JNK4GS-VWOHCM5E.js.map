{"version": 3, "sources": ["../../@medusajs/dashboard/dist/return-reason-list-L6JNK4GS.mjs"], "sourcesContent": ["import {\n  useReturnReasonTableColumns\n} from \"./chunk-R2HUTVC3.mjs\";\nimport \"./chunk-QBVCETMI.mjs\";\nimport \"./chunk-QMRGIWOP.mjs\";\nimport \"./chunk-44JF4VLS.mjs\";\nimport \"./chunk-I3VB6NM2.mjs\";\nimport \"./chunk-ZJRFL6ZN.mjs\";\nimport {\n  useReturnReasonTableQuery\n} from \"./chunk-DFA6WGYO.mjs\";\nimport \"./chunk-I5HYE2RW.mjs\";\nimport \"./chunk-RIV7FKGN.mjs\";\nimport \"./chunk-TDK3JDOB.mjs\";\nimport \"./chunk-FHSC5X62.mjs\";\nimport \"./chunk-XR4GEMGR.mjs\";\nimport \"./chunk-WRSGHGAT.mjs\";\nimport \"./chunk-MOSRJHJ3.mjs\";\nimport \"./chunk-BFAYZKJV.mjs\";\nimport \"./chunk-OMC5JCQH.mjs\";\nimport \"./chunk-RORIX3PU.mjs\";\nimport \"./chunk-XMAWMECC.mjs\";\nimport \"./chunk-5HNRTDDS.mjs\";\nimport \"./chunk-NNBHHXXN.mjs\";\nimport \"./chunk-IR5DHEKS.mjs\";\nimport \"./chunk-7DXVXBSA.mjs\";\nimport \"./chunk-PDWBYQOW.mjs\";\nimport \"./chunk-MSDRGCRR.mjs\";\nimport \"./chunk-G3QXMPRB.mjs\";\nimport \"./chunk-PCFUZKDS.mjs\";\nimport \"./chunk-IQBAUTU5.mjs\";\nimport \"./chunk-ADOCJB6L.mjs\";\nimport \"./chunk-LQTHYS2Z.mjs\";\nimport \"./chunk-P3UUX2T6.mjs\";\nimport {\n  _DataTable,\n  useDataTable\n} from \"./chunk-UE6PO4FK.mjs\";\nimport \"./chunk-YEDAFXMB.mjs\";\nimport \"./chunk-AOFGTNG6.mjs\";\nimport \"./chunk-EMIHDNB7.mjs\";\nimport \"./chunk-MWVM4TYO.mjs\";\nimport \"./chunk-SXYXTC2L.mjs\";\nimport \"./chunk-M3VFKDXJ.mjs\";\nimport {\n  SingleColumnPage\n} from \"./chunk-2RQLKDBF.mjs\";\nimport \"./chunk-C76H5USB.mjs\";\nimport \"./chunk-KSV3NQOT.mjs\";\nimport {\n  returnReasonsQueryKeys,\n  useDeleteReturnReason,\n  useReturnReasons\n} from \"./chunk-2VTICXJR.mjs\";\nimport \"./chunk-DG7J63J2.mjs\";\nimport \"./chunk-MNXC6Q4F.mjs\";\nimport {\n  useExtension\n} from \"./chunk-C5P5PL3E.mjs\";\nimport \"./chunk-LPEUYMRK.mjs\";\nimport {\n  ActionMenu\n} from \"./chunk-3NJTXRIY.mjs\";\nimport \"./chunk-OC7BQLYI.mjs\";\nimport \"./chunk-NYXYHZE6.mjs\";\nimport {\n  queryClient\n} from \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport {\n  sdk\n} from \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-RPUOO7AV.mjs\";\n\n// src/routes/return-reasons/return-reason-list/loader.ts\nvar returnReasonListQuery = (query) => ({\n  queryKey: returnReasonsQueryKeys.list(query),\n  queryFn: async () => sdk.admin.returnReason.list(query)\n});\nvar returnReasonListLoader = async () => {\n  const query = returnReasonListQuery();\n  return queryClient.getQueryData(query.queryKey) ?? await queryClient.fetchQuery(query);\n};\n\n// src/routes/return-reasons/return-reason-list/components/return-reason-list-table/return-reason-list-table.tsx\nimport { PencilSquare, Trash } from \"@medusajs/icons\";\nimport { Button, Container, Heading, Text } from \"@medusajs/ui\";\nimport { keepPreviousData } from \"@tanstack/react-query\";\nimport { createColumnHelper } from \"@tanstack/react-table\";\nimport { useMemo } from \"react\";\nimport { useTranslation as useTranslation2 } from \"react-i18next\";\nimport { Link } from \"react-router-dom\";\n\n// src/routes/return-reasons/common/hooks/use-delete-return-reason-action.tsx\nimport { toast, usePrompt } from \"@medusajs/ui\";\nimport { useTranslation } from \"react-i18next\";\nvar useDeleteReturnReasonAction = ({\n  id,\n  label\n}) => {\n  const { t } = useTranslation();\n  const prompt = usePrompt();\n  const { mutateAsync } = useDeleteReturnReason(id);\n  const handleDelete = async () => {\n    const result = await prompt({\n      title: t(\"general.areYouSure\"),\n      description: t(\"returnReasons.delete.confirmation\", {\n        label\n      }),\n      confirmText: t(\"actions.delete\"),\n      cancelText: t(\"actions.cancel\")\n    });\n    if (!result) {\n      return;\n    }\n    await mutateAsync(void 0, {\n      onSuccess: () => {\n        toast.success(t(\"returnReasons.delete.successToast\", { label }));\n      },\n      onError: (e) => {\n        toast.error(e.message);\n      }\n    });\n  };\n  return handleDelete;\n};\n\n// src/routes/return-reasons/return-reason-list/components/return-reason-list-table/return-reason-list-table.tsx\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar PAGE_SIZE = 20;\nvar ReturnReasonListTable = () => {\n  const { t } = useTranslation2();\n  const { searchParams, raw } = useReturnReasonTableQuery({\n    pageSize: PAGE_SIZE\n  });\n  const { return_reasons, count, isPending, isError, error } = useReturnReasons(\n    searchParams,\n    {\n      placeholderData: keepPreviousData\n    }\n  );\n  const columns = useColumns();\n  const { table } = useDataTable({\n    data: return_reasons,\n    columns,\n    count,\n    getRowId: (row) => row.id,\n    pageSize: PAGE_SIZE\n  });\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsxs(Container, { className: \"divide-y px-0 py-0\", children: [\n    /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center justify-between px-6 py-4\", children: [\n      /* @__PURE__ */ jsxs(\"div\", { children: [\n        /* @__PURE__ */ jsx(Heading, { children: t(\"returnReasons.domain\") }),\n        /* @__PURE__ */ jsx(Text, { className: \"text-ui-fg-subtle\", size: \"small\", children: t(\"returnReasons.subtitle\") })\n      ] }),\n      /* @__PURE__ */ jsx(Button, { variant: \"secondary\", size: \"small\", asChild: true, children: /* @__PURE__ */ jsx(Link, { to: \"create\", children: t(\"actions.create\") }) })\n    ] }),\n    /* @__PURE__ */ jsx(\n      _DataTable,\n      {\n        table,\n        queryObject: raw,\n        count,\n        isLoading: isPending,\n        columns,\n        pageSize: PAGE_SIZE,\n        noHeader: true,\n        pagination: true,\n        search: true\n      }\n    )\n  ] });\n};\nvar ReturnReasonRowActions = ({\n  returnReason\n}) => {\n  const { t } = useTranslation2();\n  const handleDelete = useDeleteReturnReasonAction(returnReason);\n  return /* @__PURE__ */ jsx(\n    ActionMenu,\n    {\n      groups: [\n        {\n          actions: [\n            {\n              icon: /* @__PURE__ */ jsx(PencilSquare, {}),\n              label: t(\"actions.edit\"),\n              to: `${returnReason.id}/edit`\n            }\n          ]\n        },\n        {\n          actions: [\n            {\n              icon: /* @__PURE__ */ jsx(Trash, {}),\n              label: t(\"actions.delete\"),\n              onClick: handleDelete\n            }\n          ]\n        }\n      ]\n    }\n  );\n};\nvar columnHelper = createColumnHelper();\nvar useColumns = () => {\n  const base = useReturnReasonTableColumns();\n  return useMemo(\n    () => [\n      ...base,\n      columnHelper.display({\n        id: \"actions\",\n        cell: ({ row }) => /* @__PURE__ */ jsx(ReturnReasonRowActions, { returnReason: row.original })\n      })\n    ],\n    [base]\n  );\n};\n\n// src/routes/return-reasons/return-reason-list/return-reason-list.tsx\nimport { jsx as jsx2 } from \"react/jsx-runtime\";\nvar ReturnReasonList = () => {\n  const { getWidgets } = useExtension();\n  return /* @__PURE__ */ jsx2(\n    SingleColumnPage,\n    {\n      showMetadata: false,\n      showJSON: false,\n      hasOutlet: true,\n      widgets: {\n        after: getWidgets(\"return_reason.list.after\"),\n        before: getWidgets(\"return_reason.list.before\")\n      },\n      children: /* @__PURE__ */ jsx2(ReturnReasonListTable, {})\n    }\n  );\n};\nexport {\n  ReturnReasonList as Component,\n  returnReasonListLoader as loader\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyFA,mBAAwB;AAuCxB,yBAA0B;AA+F1B,IAAAA,sBAA4B;AApJ5B,IAAI,wBAAwB,CAAC,WAAW;AAAA,EACtC,UAAU,uBAAuB,KAAK,KAAK;AAAA,EAC3C,SAAS,YAAY,IAAI,MAAM,aAAa,KAAK,KAAK;AACxD;AACA,IAAI,yBAAyB,YAAY;AACvC,QAAM,QAAQ,sBAAsB;AACpC,SAAO,YAAY,aAAa,MAAM,QAAQ,KAAK,MAAM,YAAY,WAAW,KAAK;AACvF;AAcA,IAAI,8BAA8B,CAAC;AAAA,EACjC;AAAA,EACA;AACF,MAAM;AACJ,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,QAAM,SAAS,UAAU;AACzB,QAAM,EAAE,YAAY,IAAI,sBAAsB,EAAE;AAChD,QAAM,eAAe,YAAY;AAC/B,UAAM,SAAS,MAAM,OAAO;AAAA,MAC1B,OAAO,EAAE,oBAAoB;AAAA,MAC7B,aAAa,EAAE,qCAAqC;AAAA,QAClD;AAAA,MACF,CAAC;AAAA,MACD,aAAa,EAAE,gBAAgB;AAAA,MAC/B,YAAY,EAAE,gBAAgB;AAAA,IAChC,CAAC;AACD,QAAI,CAAC,QAAQ;AACX;AAAA,IACF;AACA,UAAM,YAAY,QAAQ;AAAA,MACxB,WAAW,MAAM;AACf,cAAM,QAAQ,EAAE,qCAAqC,EAAE,MAAM,CAAC,CAAC;AAAA,MACjE;AAAA,MACA,SAAS,CAAC,MAAM;AACd,cAAM,MAAM,EAAE,OAAO;AAAA,MACvB;AAAA,IACF,CAAC;AAAA,EACH;AACA,SAAO;AACT;AAIA,IAAI,YAAY;AAChB,IAAI,wBAAwB,MAAM;AAChC,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,QAAM,EAAE,cAAc,IAAI,IAAI,0BAA0B;AAAA,IACtD,UAAU;AAAA,EACZ,CAAC;AACD,QAAM,EAAE,gBAAgB,OAAO,WAAW,SAAS,MAAM,IAAI;AAAA,IAC3D;AAAA,IACA;AAAA,MACE,iBAAiB;AAAA,IACnB;AAAA,EACF;AACA,QAAM,UAAU,WAAW;AAC3B,QAAM,EAAE,MAAM,IAAI,aAAa;AAAA,IAC7B,MAAM;AAAA,IACN;AAAA,IACA;AAAA,IACA,UAAU,CAAC,QAAQ,IAAI;AAAA,IACvB,UAAU;AAAA,EACZ,CAAC;AACD,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,yBAAK,WAAW,EAAE,WAAW,sBAAsB,UAAU;AAAA,QAClE,yBAAK,OAAO,EAAE,WAAW,+CAA+C,UAAU;AAAA,UAChF,yBAAK,OAAO,EAAE,UAAU;AAAA,YACtB,wBAAI,SAAS,EAAE,UAAU,EAAE,sBAAsB,EAAE,CAAC;AAAA,YACpD,wBAAI,MAAM,EAAE,WAAW,qBAAqB,MAAM,SAAS,UAAU,EAAE,wBAAwB,EAAE,CAAC;AAAA,MACpH,EAAE,CAAC;AAAA,UACa,wBAAI,QAAQ,EAAE,SAAS,aAAa,MAAM,SAAS,SAAS,MAAM,cAA0B,wBAAI,MAAM,EAAE,IAAI,UAAU,UAAU,EAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;AAAA,IAC1K,EAAE,CAAC;AAAA,QACa;AAAA,MACd;AAAA,MACA;AAAA,QACE;AAAA,QACA,aAAa;AAAA,QACb;AAAA,QACA,WAAW;AAAA,QACX;AAAA,QACA,UAAU;AAAA,QACV,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,QAAQ;AAAA,MACV;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;AACA,IAAI,yBAAyB,CAAC;AAAA,EAC5B;AACF,MAAM;AACJ,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,QAAM,eAAe,4BAA4B,YAAY;AAC7D,aAAuB;AAAA,IACrB;AAAA,IACA;AAAA,MACE,QAAQ;AAAA,QACN;AAAA,UACE,SAAS;AAAA,YACP;AAAA,cACE,UAAsB,wBAAI,cAAc,CAAC,CAAC;AAAA,cAC1C,OAAO,EAAE,cAAc;AAAA,cACvB,IAAI,GAAG,aAAa,EAAE;AAAA,YACxB;AAAA,UACF;AAAA,QACF;AAAA,QACA;AAAA,UACE,SAAS;AAAA,YACP;AAAA,cACE,UAAsB,wBAAI,OAAO,CAAC,CAAC;AAAA,cACnC,OAAO,EAAE,gBAAgB;AAAA,cACzB,SAAS;AAAA,YACX;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAI,eAAe,mBAAmB;AACtC,IAAI,aAAa,MAAM;AACrB,QAAM,OAAO,4BAA4B;AACzC,aAAO;AAAA,IACL,MAAM;AAAA,MACJ,GAAG;AAAA,MACH,aAAa,QAAQ;AAAA,QACnB,IAAI;AAAA,QACJ,MAAM,CAAC,EAAE,IAAI,UAAsB,wBAAI,wBAAwB,EAAE,cAAc,IAAI,SAAS,CAAC;AAAA,MAC/F,CAAC;AAAA,IACH;AAAA,IACA,CAAC,IAAI;AAAA,EACP;AACF;AAIA,IAAI,mBAAmB,MAAM;AAC3B,QAAM,EAAE,WAAW,IAAI,aAAa;AACpC,aAAuB,oBAAAC;AAAA,IACrB;AAAA,IACA;AAAA,MACE,cAAc;AAAA,MACd,UAAU;AAAA,MACV,WAAW;AAAA,MACX,SAAS;AAAA,QACP,OAAO,WAAW,0BAA0B;AAAA,QAC5C,QAAQ,WAAW,2BAA2B;AAAA,MAChD;AAAA,MACA,cAA0B,oBAAAA,KAAK,uBAAuB,CAAC,CAAC;AAAA,IAC1D;AAAA,EACF;AACF;", "names": ["import_jsx_runtime", "jsx2"]}