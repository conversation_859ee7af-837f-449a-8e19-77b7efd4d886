import {
  ChipInput
} from "./chunk-EFXWVIHH.js";
import "./chunk-EGRHWZRV.js";
import {
  KeyboundForm
} from "./chunk-DP54EP6X.js";
import {
  RouteDrawer,
  useRouteModal
} from "./chunk-MVVOBQIC.js";
import "./chunk-7M4ICL3D.js";
import {
  t
} from "./chunk-WHQIBI5S.js";
import {
  z
} from "./chunk-4XXECALA.js";
import {
  Form,
  useForm
} from "./chunk-XXJU43CK.js";
import {
  useCreateProductOption,
  useProduct
} from "./chunk-AJYMIHLQ.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import "./chunk-JFCIYOH4.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-5GF3RGIE.js";
import {
  useParams
} from "./chunk-T7YBVUWZ.js";
import {
  Button,
  Heading,
  Input,
  toast
} from "./chunk-YP2LLXWB.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-ZSQ362B4.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/product-create-option-HI2ZVX2S.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var CreateProductOptionSchema = z.object({
  title: z.string().min(1),
  values: z.array(z.string()).optional()
});
var CreateProductOptionForm = ({
  product
}) => {
  const { t: t2 } = useTranslation();
  const { handleSuccess } = useRouteModal();
  const form = useForm({
    defaultValues: {
      title: "",
      values: []
    },
    resolver: t(CreateProductOptionSchema)
  });
  const { mutateAsync, isPending } = useCreateProductOption(product.id);
  const handleSubmit = form.handleSubmit(async (values) => {
    mutateAsync(values, {
      onSuccess: () => {
        toast.success(
          t2("products.options.create.successToast", {
            title: values.title
          })
        );
        handleSuccess();
      },
      onError: async (err) => {
        toast.error(err.message);
      }
    });
  });
  return (0, import_jsx_runtime.jsx)(RouteDrawer.Form, { form, children: (0, import_jsx_runtime.jsxs)(
    KeyboundForm,
    {
      onSubmit: handleSubmit,
      className: "flex flex-1 flex-col overflow-hidden",
      children: [
        (0, import_jsx_runtime.jsxs)(RouteDrawer.Body, { className: "flex flex-1 flex-col gap-y-4 overflow-auto", children: [
          (0, import_jsx_runtime.jsx)(
            Form.Field,
            {
              control: form.control,
              name: "title",
              render: ({ field }) => {
                return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                  (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("products.fields.options.optionTitle") }),
                  (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(
                    Input,
                    {
                      ...field,
                      placeholder: t2(
                        "products.fields.options.optionTitlePlaceholder"
                      )
                    }
                  ) }),
                  (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                ] });
              }
            }
          ),
          (0, import_jsx_runtime.jsx)(
            Form.Field,
            {
              control: form.control,
              name: "values",
              render: ({ field }) => {
                return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                  (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("products.fields.options.variations") }),
                  (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(
                    ChipInput,
                    {
                      ...field,
                      placeholder: t2(
                        "products.fields.options.variantionsPlaceholder"
                      )
                    }
                  ) }),
                  (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                ] });
              }
            }
          )
        ] }),
        (0, import_jsx_runtime.jsx)(RouteDrawer.Footer, { children: (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center justify-end gap-x-2", children: [
          (0, import_jsx_runtime.jsx)(RouteDrawer.Close, { asChild: true, children: (0, import_jsx_runtime.jsx)(Button, { variant: "secondary", size: "small", children: t2("actions.cancel") }) }),
          (0, import_jsx_runtime.jsx)(Button, { type: "submit", size: "small", isLoading: isPending, children: t2("actions.save") })
        ] }) })
      ]
    }
  ) });
};
var ProductCreateOption = () => {
  const { id } = useParams();
  const { t: t2 } = useTranslation();
  const { product, isLoading, isError, error } = useProduct(id);
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime2.jsxs)(RouteDrawer, { children: [
    (0, import_jsx_runtime2.jsx)(RouteDrawer.Header, { children: (0, import_jsx_runtime2.jsx)(Heading, { children: t2("products.options.create.header") }) }),
    !isLoading && product && (0, import_jsx_runtime2.jsx)(CreateProductOptionForm, { product })
  ] });
};
export {
  ProductCreateOption as Component
};
//# sourceMappingURL=product-create-option-HI2ZVX2S-45PUP3JZ.js.map
