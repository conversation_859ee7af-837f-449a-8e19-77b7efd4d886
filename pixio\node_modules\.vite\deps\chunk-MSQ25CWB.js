import {
  stockLocationsQueryKeys
} from "./chunk-ONYSAQ5Z.js";
import {
  queryClient
} from "./chunk-VMLNCWLE.js";
import {
  queryKeysFactory
} from "./chunk-CUPZIPFX.js";
import {
  sdk
} from "./chunk-JFCIYOH4.js";
import {
  useMutation,
  useQuery
} from "./chunk-R35JBZ3G.js";

// node_modules/@medusajs/dashboard/dist/chunk-GRT22PE5.mjs
var SHIPPING_OPTIONS_QUERY_KEY = "shipping_options";
var shippingOptionsQueryKeys = queryKeysFactory(
  SHIPPING_OPTIONS_QUERY_KEY
);
var useShippingOption = (id, query, options) => {
  const { data, ...rest } = useQuery({
    queryFn: () => sdk.admin.shippingOption.retrieve(id, query),
    queryKey: shippingOptionsQueryKeys.detail(id),
    ...options
  });
  return { ...data, ...rest };
};
var useShippingOptions = (query, options) => {
  const { data, ...rest } = useQuery({
    queryFn: () => sdk.admin.shippingOption.list(query),
    queryKey: shippingOptionsQueryKeys.list(query),
    ...options
  });
  return { ...data, ...rest };
};
var useCreateShippingOptions = (options) => {
  return useMutation({
    mutationFn: (payload) => sdk.admin.shippingOption.create(payload),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({
        queryKey: stockLocationsQueryKeys.all
      });
      queryClient.invalidateQueries({
        queryKey: shippingOptionsQueryKeys.all
      });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useUpdateShippingOptions = (id, options) => {
  return useMutation({
    mutationFn: (payload) => sdk.admin.shippingOption.update(id, payload),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({
        queryKey: stockLocationsQueryKeys.all
      });
      queryClient.invalidateQueries({
        queryKey: shippingOptionsQueryKeys.all
      });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useDeleteShippingOption = (optionId, options) => {
  return useMutation({
    mutationFn: () => sdk.admin.shippingOption.delete(optionId),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({
        queryKey: stockLocationsQueryKeys.all
      });
      queryClient.invalidateQueries({
        queryKey: shippingOptionsQueryKeys.all
      });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};

export {
  shippingOptionsQueryKeys,
  useShippingOption,
  useShippingOptions,
  useCreateShippingOptions,
  useUpdateShippingOptions,
  useDeleteShippingOption
};
//# sourceMappingURL=chunk-MSQ25CWB.js.map
