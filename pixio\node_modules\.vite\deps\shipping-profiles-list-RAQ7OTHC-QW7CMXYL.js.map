{"version": 3, "sources": ["../../@medusajs/dashboard/dist/shipping-profiles-list-RAQ7OTHC.mjs"], "sourcesContent": ["import \"./chunk-LQTHYS2Z.mjs\";\nimport {\n  _DataTable,\n  useDataTable\n} from \"./chunk-UE6PO4FK.mjs\";\nimport \"./chunk-YEDAFXMB.mjs\";\nimport \"./chunk-AOFGTNG6.mjs\";\nimport \"./chunk-EMIHDNB7.mjs\";\nimport \"./chunk-SXYXTC2L.mjs\";\nimport \"./chunk-M3VFKDXJ.mjs\";\nimport {\n  SingleColumnPage\n} from \"./chunk-2RQLKDBF.mjs\";\nimport {\n  useQueryParams\n} from \"./chunk-C76H5USB.mjs\";\nimport \"./chunk-KSV3NQOT.mjs\";\nimport {\n  useExtension\n} from \"./chunk-C5P5PL3E.mjs\";\nimport \"./chunk-LPEUYMRK.mjs\";\nimport {\n  ActionMenu\n} from \"./chunk-3NJTXRIY.mjs\";\nimport \"./chunk-OC7BQLYI.mjs\";\nimport \"./chunk-NYXYHZE6.mjs\";\nimport {\n  useDeleteShippingProfile,\n  useShippingProfiles\n} from \"./chunk-PIR2H25N.mjs\";\nimport \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-RPUOO7AV.mjs\";\n\n// src/routes/shipping-profiles/shipping-profiles-list/components/shipping-profile-list-table/shipping-profile-list-table.tsx\nimport { Button, Container, Heading, Text } from \"@medusajs/ui\";\nimport { Link } from \"react-router-dom\";\nimport { keepPreviousData } from \"@tanstack/react-query\";\nimport { useTranslation as useTranslation4 } from \"react-i18next\";\n\n// src/routes/shipping-profiles/shipping-profiles-list/components/shipping-profile-list-table/use-shipping-profile-table-columns.tsx\nimport { createColumnHelper } from \"@tanstack/react-table\";\nimport { useMemo } from \"react\";\nimport { useTranslation as useTranslation2 } from \"react-i18next\";\n\n// src/routes/shipping-profiles/shipping-profiles-list/components/shipping-profile-list-table/shipping-options-row-actions.tsx\nimport { Trash } from \"@medusajs/icons\";\nimport { toast, usePrompt } from \"@medusajs/ui\";\nimport { useTranslation } from \"react-i18next\";\nimport { jsx } from \"react/jsx-runtime\";\nvar ShippingOptionsRowActions = ({\n  profile\n}) => {\n  const { t } = useTranslation();\n  const prompt = usePrompt();\n  const { mutateAsync } = useDeleteShippingProfile(profile.id);\n  const handleDelete = async () => {\n    const res = await prompt({\n      title: t(\"shippingProfile.delete.title\"),\n      description: t(\"shippingProfile.delete.description\", {\n        name: profile.name\n      }),\n      verificationText: profile.name,\n      verificationInstruction: t(\"general.typeToConfirm\"),\n      confirmText: t(\"actions.delete\"),\n      cancelText: t(\"actions.cancel\")\n    });\n    if (!res) {\n      return;\n    }\n    await mutateAsync(void 0, {\n      onSuccess: () => {\n        toast.success(\n          t(\"shippingProfile.delete.successToast\", {\n            name: profile.name\n          })\n        );\n      },\n      onError: (error) => {\n        toast.error(error.message);\n      }\n    });\n  };\n  return /* @__PURE__ */ jsx(\n    ActionMenu,\n    {\n      groups: [\n        {\n          actions: [\n            {\n              icon: /* @__PURE__ */ jsx(Trash, {}),\n              label: t(\"actions.delete\"),\n              onClick: handleDelete\n            }\n          ]\n        }\n      ]\n    }\n  );\n};\n\n// src/routes/shipping-profiles/shipping-profiles-list/components/shipping-profile-list-table/use-shipping-profile-table-columns.tsx\nimport { jsx as jsx2 } from \"react/jsx-runtime\";\nvar columnHelper = createColumnHelper();\nvar useShippingProfileTableColumns = () => {\n  const { t } = useTranslation2();\n  return useMemo(\n    () => [\n      columnHelper.accessor(\"name\", {\n        header: t(\"fields.name\"),\n        cell: (cell) => cell.getValue()\n      }),\n      columnHelper.accessor(\"type\", {\n        header: t(\"fields.type\"),\n        cell: (cell) => cell.getValue()\n      }),\n      columnHelper.display({\n        id: \"actions\",\n        cell: ({ row }) => /* @__PURE__ */ jsx2(ShippingOptionsRowActions, { profile: row.original })\n      })\n    ],\n    [t]\n  );\n};\n\n// src/routes/shipping-profiles/shipping-profiles-list/components/shipping-profile-list-table/use-shipping-profile-table-filters.tsx\nimport { useTranslation as useTranslation3 } from \"react-i18next\";\nvar useShippingProfileTableFilters = () => {\n  const { t } = useTranslation3();\n  let filters = [];\n  filters.push({\n    key: \"name\",\n    label: t(\"fields.name\"),\n    type: \"string\"\n  });\n  filters.push({\n    key: \"type\",\n    label: t(\"fields.type\"),\n    type: \"string\"\n  });\n  const dateFilters = [\n    { label: t(\"fields.createdAt\"), key: \"created_at\" },\n    { label: t(\"fields.updatedAt\"), key: \"updated_at\" }\n  ].map((f) => ({\n    key: f.key,\n    label: f.label,\n    type: \"date\"\n  }));\n  filters = [...filters, ...dateFilters];\n  return filters;\n};\n\n// src/routes/shipping-profiles/shipping-profiles-list/components/shipping-profile-list-table/use-shipping-profile-table-query.tsx\nvar useShippingProfileTableQuery = ({\n  pageSize = 20,\n  prefix\n}) => {\n  const raw = useQueryParams(\n    [\"offset\", \"q\", \"order\", \"created_at\", \"updated_at\", \"name\", \"type\"],\n    prefix\n  );\n  const searchParams = {\n    limit: pageSize,\n    offset: raw.offset ? parseInt(raw.offset) : 0,\n    q: raw.q,\n    order: raw.order,\n    created_at: raw.created_at ? JSON.parse(raw.created_at) : void 0,\n    updated_at: raw.updated_at ? JSON.parse(raw.updated_at) : void 0,\n    name: raw.name,\n    type: raw.type\n  };\n  return {\n    searchParams,\n    raw\n  };\n};\n\n// src/routes/shipping-profiles/shipping-profiles-list/components/shipping-profile-list-table/shipping-profile-list-table.tsx\nimport { jsx as jsx3, jsxs } from \"react/jsx-runtime\";\nvar PAGE_SIZE = 20;\nvar ShippingProfileListTable = () => {\n  const { t } = useTranslation4();\n  const { raw, searchParams } = useShippingProfileTableQuery({\n    pageSize: PAGE_SIZE\n  });\n  const { shipping_profiles, count, isLoading, isError, error } = useShippingProfiles(searchParams, {\n    placeholderData: keepPreviousData\n  });\n  const columns = useShippingProfileTableColumns();\n  const filters = useShippingProfileTableFilters();\n  const { table } = useDataTable({\n    data: shipping_profiles,\n    columns,\n    count,\n    enablePagination: true,\n    getRowId: (row) => row.id,\n    pageSize: PAGE_SIZE\n  });\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsxs(Container, { className: \"divide-y p-0\", children: [\n    /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center justify-between px-6 py-4\", children: [\n      /* @__PURE__ */ jsxs(\"div\", { children: [\n        /* @__PURE__ */ jsx3(Heading, { children: t(\"shippingProfile.domain\") }),\n        /* @__PURE__ */ jsx3(Text, { className: \"text-ui-fg-subtle\", size: \"small\", children: t(\"shippingProfile.subtitle\") })\n      ] }),\n      /* @__PURE__ */ jsx3(\"div\", { children: /* @__PURE__ */ jsx3(Button, { size: \"small\", variant: \"secondary\", asChild: true, children: /* @__PURE__ */ jsx3(Link, { to: \"create\", children: t(\"actions.create\") }) }) })\n    ] }),\n    /* @__PURE__ */ jsx3(\n      _DataTable,\n      {\n        table,\n        pageSize: PAGE_SIZE,\n        count,\n        columns,\n        filters,\n        orderBy: [\n          { key: \"name\", label: t(\"fields.name\") },\n          { key: \"type\", label: t(\"fields.type\") },\n          { key: \"created_at\", label: t(\"fields.createdAt\") },\n          { key: \"updated_at\", label: t(\"fields.updatedAt\") }\n        ],\n        isLoading,\n        navigateTo: (row) => row.id,\n        queryObject: raw,\n        search: true,\n        pagination: true\n      }\n    )\n  ] });\n};\n\n// src/routes/shipping-profiles/shipping-profiles-list/shipping-profile-list.tsx\nimport { jsx as jsx4 } from \"react/jsx-runtime\";\nvar ShippingProfileList = () => {\n  const { getWidgets } = useExtension();\n  return /* @__PURE__ */ jsx4(\n    SingleColumnPage,\n    {\n      widgets: {\n        before: getWidgets(\"shipping_profile.list.before\"),\n        after: getWidgets(\"shipping_profile.list.after\")\n      },\n      children: /* @__PURE__ */ jsx4(ShippingProfileListTable, {})\n    }\n  );\n};\nexport {\n  ShippingProfileList as Component\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2CA,mBAAwB;AAOxB,yBAAoB;AAqDpB,IAAAA,sBAA4B;AA4E5B,IAAAC,sBAAkC;AAwDlC,IAAAA,sBAA4B;AAxL5B,IAAI,4BAA4B,CAAC;AAAA,EAC/B;AACF,MAAM;AACJ,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,QAAM,SAAS,UAAU;AACzB,QAAM,EAAE,YAAY,IAAI,yBAAyB,QAAQ,EAAE;AAC3D,QAAM,eAAe,YAAY;AAC/B,UAAM,MAAM,MAAM,OAAO;AAAA,MACvB,OAAO,EAAE,8BAA8B;AAAA,MACvC,aAAa,EAAE,sCAAsC;AAAA,QACnD,MAAM,QAAQ;AAAA,MAChB,CAAC;AAAA,MACD,kBAAkB,QAAQ;AAAA,MAC1B,yBAAyB,EAAE,uBAAuB;AAAA,MAClD,aAAa,EAAE,gBAAgB;AAAA,MAC/B,YAAY,EAAE,gBAAgB;AAAA,IAChC,CAAC;AACD,QAAI,CAAC,KAAK;AACR;AAAA,IACF;AACA,UAAM,YAAY,QAAQ;AAAA,MACxB,WAAW,MAAM;AACf,cAAM;AAAA,UACJ,EAAE,uCAAuC;AAAA,YACvC,MAAM,QAAQ;AAAA,UAChB,CAAC;AAAA,QACH;AAAA,MACF;AAAA,MACA,SAAS,CAAC,UAAU;AAClB,cAAM,MAAM,MAAM,OAAO;AAAA,MAC3B;AAAA,IACF,CAAC;AAAA,EACH;AACA,aAAuB;AAAA,IACrB;AAAA,IACA;AAAA,MACE,QAAQ;AAAA,QACN;AAAA,UACE,SAAS;AAAA,YACP;AAAA,cACE,UAAsB,wBAAI,OAAO,CAAC,CAAC;AAAA,cACnC,OAAO,EAAE,gBAAgB;AAAA,cACzB,SAAS;AAAA,YACX;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAIA,IAAI,eAAe,mBAAmB;AACtC,IAAI,iCAAiC,MAAM;AACzC,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,aAAO;AAAA,IACL,MAAM;AAAA,MACJ,aAAa,SAAS,QAAQ;AAAA,QAC5B,QAAQ,EAAE,aAAa;AAAA,QACvB,MAAM,CAAC,SAAS,KAAK,SAAS;AAAA,MAChC,CAAC;AAAA,MACD,aAAa,SAAS,QAAQ;AAAA,QAC5B,QAAQ,EAAE,aAAa;AAAA,QACvB,MAAM,CAAC,SAAS,KAAK,SAAS;AAAA,MAChC,CAAC;AAAA,MACD,aAAa,QAAQ;AAAA,QACnB,IAAI;AAAA,QACJ,MAAM,CAAC,EAAE,IAAI,UAAsB,oBAAAC,KAAK,2BAA2B,EAAE,SAAS,IAAI,SAAS,CAAC;AAAA,MAC9F,CAAC;AAAA,IACH;AAAA,IACA,CAAC,CAAC;AAAA,EACJ;AACF;AAIA,IAAI,iCAAiC,MAAM;AACzC,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,MAAI,UAAU,CAAC;AACf,UAAQ,KAAK;AAAA,IACX,KAAK;AAAA,IACL,OAAO,EAAE,aAAa;AAAA,IACtB,MAAM;AAAA,EACR,CAAC;AACD,UAAQ,KAAK;AAAA,IACX,KAAK;AAAA,IACL,OAAO,EAAE,aAAa;AAAA,IACtB,MAAM;AAAA,EACR,CAAC;AACD,QAAM,cAAc;AAAA,IAClB,EAAE,OAAO,EAAE,kBAAkB,GAAG,KAAK,aAAa;AAAA,IAClD,EAAE,OAAO,EAAE,kBAAkB,GAAG,KAAK,aAAa;AAAA,EACpD,EAAE,IAAI,CAAC,OAAO;AAAA,IACZ,KAAK,EAAE;AAAA,IACP,OAAO,EAAE;AAAA,IACT,MAAM;AAAA,EACR,EAAE;AACF,YAAU,CAAC,GAAG,SAAS,GAAG,WAAW;AACrC,SAAO;AACT;AAGA,IAAI,+BAA+B,CAAC;AAAA,EAClC,WAAW;AAAA,EACX;AACF,MAAM;AACJ,QAAM,MAAM;AAAA,IACV,CAAC,UAAU,KAAK,SAAS,cAAc,cAAc,QAAQ,MAAM;AAAA,IACnE;AAAA,EACF;AACA,QAAM,eAAe;AAAA,IACnB,OAAO;AAAA,IACP,QAAQ,IAAI,SAAS,SAAS,IAAI,MAAM,IAAI;AAAA,IAC5C,GAAG,IAAI;AAAA,IACP,OAAO,IAAI;AAAA,IACX,YAAY,IAAI,aAAa,KAAK,MAAM,IAAI,UAAU,IAAI;AAAA,IAC1D,YAAY,IAAI,aAAa,KAAK,MAAM,IAAI,UAAU,IAAI;AAAA,IAC1D,MAAM,IAAI;AAAA,IACV,MAAM,IAAI;AAAA,EACZ;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAIA,IAAI,YAAY;AAChB,IAAI,2BAA2B,MAAM;AACnC,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,QAAM,EAAE,KAAK,aAAa,IAAI,6BAA6B;AAAA,IACzD,UAAU;AAAA,EACZ,CAAC;AACD,QAAM,EAAE,mBAAmB,OAAO,WAAW,SAAS,MAAM,IAAI,oBAAoB,cAAc;AAAA,IAChG,iBAAiB;AAAA,EACnB,CAAC;AACD,QAAM,UAAU,+BAA+B;AAC/C,QAAM,UAAU,+BAA+B;AAC/C,QAAM,EAAE,MAAM,IAAI,aAAa;AAAA,IAC7B,MAAM;AAAA,IACN;AAAA,IACA;AAAA,IACA,kBAAkB;AAAA,IAClB,UAAU,CAAC,QAAQ,IAAI;AAAA,IACvB,UAAU;AAAA,EACZ,CAAC;AACD,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,0BAAK,WAAW,EAAE,WAAW,gBAAgB,UAAU;AAAA,QAC5D,0BAAK,OAAO,EAAE,WAAW,+CAA+C,UAAU;AAAA,UAChF,0BAAK,OAAO,EAAE,UAAU;AAAA,YACtB,oBAAAC,KAAK,SAAS,EAAE,UAAU,EAAE,wBAAwB,EAAE,CAAC;AAAA,YACvD,oBAAAA,KAAK,MAAM,EAAE,WAAW,qBAAqB,MAAM,SAAS,UAAU,EAAE,0BAA0B,EAAE,CAAC;AAAA,MACvH,EAAE,CAAC;AAAA,UACa,oBAAAA,KAAK,OAAO,EAAE,cAA0B,oBAAAA,KAAK,QAAQ,EAAE,MAAM,SAAS,SAAS,aAAa,SAAS,MAAM,cAA0B,oBAAAA,KAAK,MAAM,EAAE,IAAI,UAAU,UAAU,EAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;AAAA,IACvN,EAAE,CAAC;AAAA,QACa,oBAAAA;AAAA,MACd;AAAA,MACA;AAAA,QACE;AAAA,QACA,UAAU;AAAA,QACV;AAAA,QACA;AAAA,QACA;AAAA,QACA,SAAS;AAAA,UACP,EAAE,KAAK,QAAQ,OAAO,EAAE,aAAa,EAAE;AAAA,UACvC,EAAE,KAAK,QAAQ,OAAO,EAAE,aAAa,EAAE;AAAA,UACvC,EAAE,KAAK,cAAc,OAAO,EAAE,kBAAkB,EAAE;AAAA,UAClD,EAAE,KAAK,cAAc,OAAO,EAAE,kBAAkB,EAAE;AAAA,QACpD;AAAA,QACA;AAAA,QACA,YAAY,CAAC,QAAQ,IAAI;AAAA,QACzB,aAAa;AAAA,QACb,QAAQ;AAAA,QACR,YAAY;AAAA,MACd;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;AAIA,IAAI,sBAAsB,MAAM;AAC9B,QAAM,EAAE,WAAW,IAAI,aAAa;AACpC,aAAuB,oBAAAC;AAAA,IACrB;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP,QAAQ,WAAW,8BAA8B;AAAA,QACjD,OAAO,WAAW,6BAA6B;AAAA,MACjD;AAAA,MACA,cAA0B,oBAAAA,KAAK,0BAA0B,CAAC,CAAC;AAAA,IAC7D;AAAA,EACF;AACF;", "names": ["import_jsx_runtime", "import_jsx_runtime", "jsx2", "jsx3", "jsx4"]}