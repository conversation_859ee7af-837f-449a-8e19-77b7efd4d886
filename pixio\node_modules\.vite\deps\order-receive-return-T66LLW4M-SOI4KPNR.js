import {
  useAddDismissItems,
  useAddReceiveItems,
  useCancelReceiveReturn,
  useConfirmReturnReceive,
  useInitiateReceiveReturn,
  useRemoveDismissItem,
  useRemoveReceiveItems,
  useReturn,
  useUpdateDismissItem,
  useUpdateReceiveItem
} from "./chunk-YJDYMRQS.js";
import {
  getStylizedAmount
} from "./chunk-UDMOPZAP.js";
import "./chunk-H3DTEG3J.js";
import "./chunk-EGRHWZRV.js";
import {
  KeyboundForm
} from "./chunk-DP54EP6X.js";
import {
  RouteDrawer,
  useRouteModal
} from "./chunk-MVVOBQIC.js";
import {
  t
} from "./chunk-WHQIBI5S.js";
import {
  z
} from "./chunk-4XXECALA.js";
import {
  Thumbnail
} from "./chunk-6GQUHAET.js";
import {
  Form,
  useForm
} from "./chunk-XXJU43CK.js";
import "./chunk-WISME5HP.js";
import "./chunk-WAUSOHFQ.js";
import "./chunk-3ORK3QZ4.js";
import "./chunk-3A5TVVNI.js";
import "./chunk-EZ62MM7J.js";
import "./chunk-3R6LYIXM.js";
import "./chunk-2P2O4TMQ.js";
import "./chunk-KI3HEITX.js";
import "./chunk-2VUCAO7L.js";
import "./chunk-PTIULTKD.js";
import "./chunk-KISXY2LD.js";
import "./chunk-L7C3XPHJ.js";
import "./chunk-T7MG2EIJ.js";
import "./chunk-MER4VYPR.js";
import "./chunk-2AWKOUCD.js";
import "./chunk-YL3TYBFR.js";
import "./chunk-TZWW72YW.js";
import "./chunk-MSQ25CWB.js";
import {
  useStockLocation
} from "./chunk-ONYSAQ5Z.js";
import {
  useOrder,
  useOrderPreview
} from "./chunk-NIH7SAXN.js";
import "./chunk-UEJFWAFE.js";
import "./chunk-GR55MFKT.js";
import "./chunk-AAFHKNJG.js";
import "./chunk-7XDBFDTZ.js";
import "./chunk-3JKGO5XL.js";
import "./chunk-OISUTS7G.js";
import "./chunk-AJYMIHLQ.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import "./chunk-JFCIYOH4.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-5GF3RGIE.js";
import {
  useNavigate,
  useParams
} from "./chunk-T7YBVUWZ.js";
import {
  Alert,
  ArrowRight,
  Button,
  Heading,
  HeartBroken,
  Input,
  Popover,
  Switch,
  Text,
  toast
} from "./chunk-YP2LLXWB.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-ZSQ362B4.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/order-receive-return-T66LLW4M.mjs
var import_react = __toESM(require_react(), 1);
var import_react2 = __toESM(require_react(), 1);
var import_react3 = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var ReceiveReturnSchema = z.object({
  items: z.array(
    z.object({
      quantity: z.number().nullish(),
      dismissed_quantity: z.number().nullish(),
      item_id: z.string()
    })
  ),
  send_notification: z.boolean().optional()
});
function DismissedQuantity({
  form,
  item,
  index,
  returnId,
  orderId
}) {
  const { t: t2 } = useTranslation();
  const [isOpen, setIsOpen] = (0, import_react3.useState)(false);
  const { mutateAsync: addDismissedItems } = useAddDismissItems(
    returnId,
    orderId
  );
  const { mutateAsync: updateDismissedItems } = useUpdateDismissItem(
    returnId,
    orderId
  );
  const { mutateAsync: removeDismissedItems } = useRemoveDismissItem(
    returnId,
    orderId
  );
  const [receivedQuantity, dismissedQuantity] = (0, import_react3.useMemo)(() => {
    var _a, _b;
    const receivedAction = (_a = item.actions) == null ? void 0 : _a.find(
      (a) => a.action === "RECEIVE_RETURN_ITEM"
    );
    const dismissedAction = (_b = item.actions) == null ? void 0 : _b.find(
      (a) => a.action === "RECEIVE_DAMAGED_RETURN_ITEM"
    );
    return [receivedAction == null ? void 0 : receivedAction.details.quantity, dismissedAction == null ? void 0 : dismissedAction.details.quantity];
  }, [item]);
  const onDismissedQuantityChanged = async (value) => {
    var _a;
    const action = (_a = item.actions) == null ? void 0 : _a.find(
      (a) => a.action === "RECEIVE_DAMAGED_RETURN_ITEM"
    );
    if (typeof value === "number" && value < 0) {
      form.setValue(`items.${index}.dismissed_quantity`, dismissedQuantity, {
        shouldTouch: true,
        shouldDirty: true
      });
      toast.error(t2("orders.returns.receive.toast.errorNegativeValue"));
      return;
    }
    if (typeof value === "number" && value > item.quantity - item.detail.return_received_quantity) {
      form.setValue(`items.${index}.dismissed_quantity`, dismissedQuantity, {
        shouldTouch: true,
        shouldDirty: true
      });
      toast.error(t2("orders.returns.receive.toast.errorLargeDamagedValue"));
      return;
    }
    try {
      if (value) {
        if (!action) {
          await addDismissedItems({
            items: [{ id: item.id, quantity: value }]
          });
        } else {
          await updateDismissedItems({ actionId: action.id, quantity: value });
        }
      } else {
        if (action) {
          await removeDismissedItems(action.id);
        }
      }
    } catch (e) {
      toast.error(e.message);
    }
  };
  return (0, import_jsx_runtime.jsxs)(Popover, { open: isOpen, onOpenChange: setIsOpen, children: [
    (0, import_jsx_runtime.jsx)(Popover.Trigger, { asChild: true, children: (0, import_jsx_runtime.jsxs)(Button, { className: "flex gap-2 px-2", variant: "secondary", type: "button", children: [
      (0, import_jsx_runtime.jsx)("div", { children: (0, import_jsx_runtime.jsx)(HeartBroken, {}) }),
      !!dismissedQuantity && (0, import_jsx_runtime.jsx)("span", { children: dismissedQuantity })
    ] }) }),
    (0, import_jsx_runtime.jsx)(Popover.Content, { align: "center", children: (0, import_jsx_runtime.jsxs)("div", { className: "flex flex-col p-2", children: [
      (0, import_jsx_runtime.jsx)("span", { className: "txt-small text-ui-fg-subtle mb-2 font-medium", children: t2("orders.returns.receive.writeOffInputLabel") }),
      (0, import_jsx_runtime.jsx)(
        Form.Field,
        {
          control: form.control,
          name: `items.${index}.dismissed_quantity`,
          render: ({ field: { onChange, value, ...field } }) => {
            return (0, import_jsx_runtime.jsx)(Form.Item, { className: "w-full", children: (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(
              Input,
              {
                min: 0,
                max: item.quantity,
                type: "number",
                value,
                className: "bg-ui-bg-field-component text-right [appearance:textfield] [&::-webkit-inner-spin-button]:appearance-none [&::-webkit-outer-spin-button]:appearance-none",
                onChange: (e) => {
                  const value2 = e.target.value === "" ? null : parseFloat(e.target.value);
                  onChange(value2);
                },
                ...field,
                onBlur: () => {
                  field.onBlur();
                  onDismissedQuantityChanged(value);
                }
              }
            ) }) });
          }
        }
      )
    ] }) })
  ] });
}
var dismissed_quantity_default = DismissedQuantity;
function OrderReceiveReturnForm({
  order,
  preview,
  orderReturn
}) {
  const { t: t2 } = useTranslation();
  const { handleSuccess } = useRouteModal();
  const previewItems = (0, import_react2.useMemo)(() => {
    const idsMap = {};
    orderReturn.items.forEach((i) => idsMap[i.item_id] = true);
    return preview.items.filter((i) => idsMap[i.id]);
  }, [preview.items, orderReturn]);
  const { mutateAsync: confirmReturnReceive } = useConfirmReturnReceive(
    orderReturn.id,
    order.id
  );
  const { mutateAsync: cancelReceiveReturn } = useCancelReceiveReturn(
    orderReturn.id,
    order.id
  );
  const { mutateAsync: addReceiveItems } = useAddReceiveItems(
    orderReturn.id,
    order.id
  );
  const { mutateAsync: updateReceiveItem } = useUpdateReceiveItem(
    orderReturn.id,
    order.id
  );
  const { mutateAsync: removeReceiveItem } = useRemoveReceiveItems(
    orderReturn.id,
    order.id
  );
  const { stock_location } = useStockLocation(
    orderReturn.location_id,
    void 0,
    {
      enabled: !!orderReturn.location_id
    }
  );
  const itemsMap = (0, import_react2.useMemo)(() => {
    const ret = {};
    order.items.forEach((i) => ret[i.id] = i);
    return ret;
  }, [order.items]);
  const form = useForm({
    defaultValues: {
      items: previewItems == null ? void 0 : previewItems.sort((i1, i2) => i1.id.localeCompare(i2.id)).map((i) => ({
        item_id: i.id
      })),
      send_notification: false
    },
    resolver: t(ReceiveReturnSchema)
  });
  (0, import_react2.useEffect)(() => {
    previewItems == null ? void 0 : previewItems.sort((i1, i2) => i1.id.localeCompare(i2.id)).forEach((item, index) => {
      var _a, _b;
      const receivedAction = (_a = item.actions) == null ? void 0 : _a.find(
        (a) => a.action === "RECEIVE_RETURN_ITEM"
      );
      const dismissedAction = (_b = item.actions) == null ? void 0 : _b.find(
        (a) => a.action === "RECEIVE_DAMAGED_RETURN_ITEM"
      );
      form.setValue(
        `items.${index}.quantity`,
        receivedAction == null ? void 0 : receivedAction.details.quantity,
        { shouldTouch: true, shouldDirty: true }
      );
      form.setValue(
        `items.${index}.dismissed_quantity`,
        dismissedAction == null ? void 0 : dismissedAction.details.quantity,
        { shouldTouch: true, shouldDirty: true }
      );
    });
  }, [previewItems]);
  const handleSubmit = form.handleSubmit(async (data) => {
    try {
      await confirmReturnReceive({ no_notification: !data.send_notification });
      handleSuccess(`/orders/${order.id}`);
      toast.success(t2("general.success"), {
        description: t2("orders.returns.receive.toast.success"),
        dismissLabel: t2("actions.close")
      });
    } catch (e) {
      toast.error(t2("general.error"), {
        description: e.message,
        dismissLabel: t2("actions.close")
      });
    }
  });
  const handleQuantityChange = async (itemId, value, index) => {
    var _a;
    const item = previewItems == null ? void 0 : previewItems.find((i) => i.id === itemId);
    const action = (_a = item == null ? void 0 : item.actions) == null ? void 0 : _a.find(
      (a) => a.action === "RECEIVE_RETURN_ITEM"
    );
    if (typeof value === "number" && value < 0) {
      form.setValue(
        `items.${index}.quantity`,
        item.detail.return_received_quantity,
        { shouldTouch: true, shouldDirty: true }
      );
      toast.error(t2("orders.returns.receive.toast.errorNegativeValue"));
      return;
    }
    if (typeof value === "number" && value > item.quantity) {
      form.setValue(
        `items.${index}.quantity`,
        item.detail.return_received_quantity,
        { shouldTouch: true, shouldDirty: true }
      );
      toast.error(t2("orders.returns.receive.toast.errorLargeValue"));
      return;
    }
    try {
      if (action) {
        if (value === null || value === 0) {
          await removeReceiveItem(action.id);
          return;
        }
        await updateReceiveItem({ actionId: action.id, quantity: value });
      } else {
        if (typeof value === "number" && value > 0 && value <= item.quantity) {
          await addReceiveItems({ items: [{ id: item.id, quantity: value }] });
        }
      }
    } catch (e) {
      toast.error(e.message);
    }
  };
  const onFormClose = async (isSubmitSuccessful) => {
    try {
      if (!isSubmitSuccessful) {
        await cancelReceiveReturn();
      }
    } catch (e) {
      toast.error(e.message);
    }
  };
  return (0, import_jsx_runtime2.jsx)(RouteDrawer.Form, { form, onClose: onFormClose, children: (0, import_jsx_runtime2.jsxs)(
    KeyboundForm,
    {
      onSubmit: handleSubmit,
      className: "flex size-full flex-col overflow-hidden",
      children: [
        (0, import_jsx_runtime2.jsxs)(RouteDrawer.Body, { className: "flex size-full flex-col overflow-auto", children: [
          (0, import_jsx_runtime2.jsxs)("div", { className: "flex justify-between", children: [
            (0, import_jsx_runtime2.jsx)("div", { children: stock_location && (0, import_jsx_runtime2.jsxs)("div", { className: "flex items-center gap-2", children: [
              (0, import_jsx_runtime2.jsx)(ArrowRight, { className: "text-ui-fg-subtle" }),
              " ",
              (0, import_jsx_runtime2.jsx)("span", { className: "text-ui-fg-base txt-small font-medium", children: stock_location.name })
            ] }) }),
            (0, import_jsx_runtime2.jsx)("span", { className: "text-ui-fg-muted txt-small text-right", children: t2("orders.returns.receive.itemsLabel") })
          ] }),
          previewItems.map((item, ind) => {
            const originalItem = itemsMap[item.id];
            return (0, import_jsx_runtime2.jsx)(
              "div",
              {
                className: "bg-ui-bg-subtle shadow-elevation-card-rest mt-2 rounded-xl",
                children: (0, import_jsx_runtime2.jsxs)("div", { className: "flex flex-col items-center gap-x-2 gap-y-2 p-3 text-sm md:flex-row", children: [
                  (0, import_jsx_runtime2.jsxs)("div", { className: "flex flex-1 items-center gap-x-3", children: [
                    (0, import_jsx_runtime2.jsxs)(Text, { size: "small", className: "text-ui-fg-subtle", children: [
                      item.quantity,
                      "x"
                    ] }),
                    (0, import_jsx_runtime2.jsx)(Thumbnail, { src: item.thumbnail }),
                    (0, import_jsx_runtime2.jsxs)("div", { className: "flex flex-col", children: [
                      (0, import_jsx_runtime2.jsxs)("div", { children: [
                        (0, import_jsx_runtime2.jsxs)(Text, { className: "txt-small", as: "span", weight: "plus", children: [
                          item.title,
                          " "
                        ] }),
                        originalItem.variant_sku && (0, import_jsx_runtime2.jsxs)("span", { children: [
                          "(",
                          originalItem.variant_sku,
                          ")"
                        ] })
                      ] }),
                      (0, import_jsx_runtime2.jsx)(Text, { as: "div", className: "text-ui-fg-subtle txt-small", children: originalItem.product_title })
                    ] })
                  ] }),
                  (0, import_jsx_runtime2.jsxs)("div", { className: "flex flex-1 flex-row items-center gap-2", children: [
                    (0, import_jsx_runtime2.jsx)(
                      dismissed_quantity_default,
                      {
                        form,
                        item,
                        index: ind,
                        returnId: orderReturn.id,
                        orderId: order.id
                      }
                    ),
                    (0, import_jsx_runtime2.jsx)(
                      Form.Field,
                      {
                        control: form.control,
                        name: `items.${ind}.quantity`,
                        render: ({ field: { onChange, value, ...field } }) => {
                          return (0, import_jsx_runtime2.jsx)(Form.Item, { className: "w-full", children: (0, import_jsx_runtime2.jsx)(Form.Control, { children: (0, import_jsx_runtime2.jsx)(
                            Input,
                            {
                              min: 0,
                              max: item.quantity,
                              type: "number",
                              value,
                              className: "bg-ui-bg-field-component text-right [appearance:textfield] [&::-webkit-inner-spin-button]:appearance-none [&::-webkit-outer-spin-button]:appearance-none",
                              onChange: (e) => {
                                const value2 = e.target.value === "" ? null : parseFloat(e.target.value);
                                onChange(value2);
                              },
                              ...field,
                              onBlur: () => {
                                field.onBlur();
                                handleQuantityChange(item.id, value, ind);
                              }
                            }
                          ) }) });
                        }
                      }
                    )
                  ] })
                ] })
              },
              item.id
            );
          }),
          (0, import_jsx_runtime2.jsxs)("div", { className: "my-6 border-b border-t border-dashed py-4", children: [
            (0, import_jsx_runtime2.jsxs)("div", { className: "mb-2 flex items-center justify-between", children: [
              (0, import_jsx_runtime2.jsx)("span", { className: "txt-small text-ui-fg-subtle", children: t2("fields.total") }),
              (0, import_jsx_runtime2.jsx)("span", { className: "txt-small text-ui-fg-subtle", children: getStylizedAmount(preview.total, order.currency_code) })
            ] }),
            (0, import_jsx_runtime2.jsxs)("div", { className: "mt-4 flex items-center justify-between border-t border-dotted pt-4", children: [
              (0, import_jsx_runtime2.jsx)("span", { className: "txt-small font-medium", children: t2("orders.returns.outstandingAmount") }),
              (0, import_jsx_runtime2.jsx)("span", { className: "txt-small font-medium", children: getStylizedAmount(
                preview.summary.pending_difference || 0,
                order.currency_code
              ) })
            ] })
          ] }),
          (0, import_jsx_runtime2.jsx)(Alert, { className: "rounded-xl", variant: "warning", children: t2("orders.returns.receive.inventoryWarning") }),
          (0, import_jsx_runtime2.jsx)("div", { className: "bg-ui-bg-subtle shadow-elevation-card-rest my-2 rounded-xl p-3", children: (0, import_jsx_runtime2.jsx)(
            Form.Field,
            {
              control: form.control,
              name: "send_notification",
              render: ({ field: { onChange, value, ...field } }) => {
                return (0, import_jsx_runtime2.jsxs)(Form.Item, { children: [
                  (0, import_jsx_runtime2.jsxs)("div", { className: "flex items-center gap-3", children: [
                    (0, import_jsx_runtime2.jsx)(Form.Control, { children: (0, import_jsx_runtime2.jsx)(
                      Switch,
                      {
                        className: "mt-1 self-start",
                        checked: !!value,
                        onCheckedChange: onChange,
                        ...field
                      }
                    ) }),
                    (0, import_jsx_runtime2.jsxs)("div", { className: "flex flex-col", children: [
                      (0, import_jsx_runtime2.jsx)(Form.Label, { children: t2("orders.returns.sendNotification") }),
                      (0, import_jsx_runtime2.jsx)(Form.Hint, { className: "!mt-1", children: t2("orders.returns.receive.sendNotificationHint") })
                    ] })
                  ] }),
                  (0, import_jsx_runtime2.jsx)(Form.ErrorMessage, {})
                ] });
              }
            }
          ) })
        ] }),
        (0, import_jsx_runtime2.jsx)(RouteDrawer.Footer, { className: "overflow-hidden", children: (0, import_jsx_runtime2.jsxs)("div", { className: "flex items-center gap-x-2", children: [
          (0, import_jsx_runtime2.jsx)(RouteDrawer.Close, { asChild: true, children: (0, import_jsx_runtime2.jsx)(Button, { size: "small", variant: "secondary", children: t2("actions.cancel") }) }),
          (0, import_jsx_runtime2.jsx)(Button, { size: "small", type: "submit", isLoading: false, children: t2("actions.save") })
        ] }) })
      ]
    }
  ) });
}
var IS_REQUEST_RUNNING = false;
function OrderReceiveReturn() {
  const { id, return_id } = useParams();
  const { t: t2 } = useTranslation();
  const navigate = useNavigate();
  const { order } = useOrder(id, { fields: "+currency_code,*items" });
  const { order: preview } = useOrderPreview(id);
  const { return: orderReturn } = useReturn(return_id, {
    fields: "*items.item,*items.item.variant,*items.item.variant.product"
  });
  const { mutateAsync: initiateReceiveReturn } = useInitiateReceiveReturn(
    return_id,
    id
  );
  const { mutateAsync: addReceiveItems } = useAddReceiveItems(return_id, id);
  (0, import_react.useEffect)(() => {
    ;
    (async function() {
      if (IS_REQUEST_RUNNING || !preview) {
        return;
      }
      if (preview.order_change) {
        if (preview.order_change.change_type !== "return_receive") {
          navigate(`/orders/${id}`, { replace: true });
          toast.error(t2("orders.returns.activeChangeError"));
        }
        return;
      }
      IS_REQUEST_RUNNING = true;
      try {
        const { return: _return } = await initiateReceiveReturn({});
        await addReceiveItems({
          items: _return.items.map((i) => ({
            id: i.item_id,
            quantity: i.quantity
          }))
        });
      } catch (e) {
        toast.error(e.message);
      } finally {
        IS_REQUEST_RUNNING = false;
      }
    })();
  }, [preview]);
  const ready = order && orderReturn && preview;
  return (0, import_jsx_runtime3.jsxs)(RouteDrawer, { children: [
    (0, import_jsx_runtime3.jsx)(RouteDrawer.Header, { children: (0, import_jsx_runtime3.jsx)(Heading, { children: t2("orders.returns.receive.title", {
      returnId: return_id == null ? void 0 : return_id.slice(-7)
    }) }) }),
    ready && (0, import_jsx_runtime3.jsx)(
      OrderReceiveReturnForm,
      {
        order,
        orderReturn,
        preview
      }
    )
  ] });
}
export {
  OrderReceiveReturn as Component
};
//# sourceMappingURL=order-receive-return-T66LLW4M-SOI4KPNR.js.map
