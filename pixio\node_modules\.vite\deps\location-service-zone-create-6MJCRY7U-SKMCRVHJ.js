import {
  GeoZoneForm
} from "./chunk-5SUCCVVS.js";
import "./chunk-RXJJ2XKA.js";
import {
  GEO_ZONE_STACKED_MODAL_ID
} from "./chunk-S4XCFSZC.js";
import "./chunk-EGRHWZRV.js";
import "./chunk-VCX3BVQR.js";
import "./chunk-VCBFQV64.js";
import "./chunk-QLJZR2JY.js";
import "./chunk-X3TOWPPJ.js";
import "./chunk-V2W2XNHI.js";
import "./chunk-EGRHWZRV.js";
import {
  KeyboundForm
} from "./chunk-DP54EP6X.js";
import "./chunk-32T72GVU.js";
import "./chunk-MX43XOWY.js";
import "./chunk-QX6SXRUW.js";
import "./chunk-7ANVLPZR.js";
import "./chunk-2E2FUO6N.js";
import {
  RouteFocusModal,
  StackedFocusModal,
  useRouteModal
} from "./chunk-MVVOBQIC.js";
import "./chunk-HPGXK5DQ.js";
import {
  t
} from "./chunk-WHQIBI5S.js";
import {
  z
} from "./chunk-4XXECALA.js";
import "./chunk-YXT43UJF.js";
import "./chunk-DL4QDYPT.js";
import "./chunk-NV2N3EWM.js";
import "./chunk-BF7OBKIN.js";
import "./chunk-7UAYECTW.js";
import "./chunk-Y3NYV3NU.js";
import "./chunk-MPXR7HT5.js";
import {
  Form,
  useForm
} from "./chunk-XXJU43CK.js";
import {
  useCreateFulfillmentSetServiceZone
} from "./chunk-KI3HEITX.js";
import "./chunk-MSQ25CWB.js";
import {
  useStockLocation
} from "./chunk-ONYSAQ5Z.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import "./chunk-JFCIYOH4.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-5GF3RGIE.js";
import {
  json,
  useParams
} from "./chunk-T7YBVUWZ.js";
import {
  Button,
  Heading,
  InlineTip,
  Input,
  toast
} from "./chunk-YP2LLXWB.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-ZSQ362B4.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/location-service-zone-create-6MJCRY7U.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var CreateServiceZoneSchema = z.object({
  name: z.string().min(1),
  countries: z.array(z.object({ iso_2: z.string().min(2), display_name: z.string() })).min(1)
});
function CreateServiceZoneForm({
  fulfillmentSet,
  type,
  location
}) {
  const { t: t2 } = useTranslation();
  const { handleSuccess } = useRouteModal();
  const form = useForm({
    defaultValues: {
      name: "",
      countries: []
    },
    resolver: t(CreateServiceZoneSchema)
  });
  const { mutateAsync, isPending } = useCreateFulfillmentSetServiceZone(
    fulfillmentSet.id
  );
  const handleSubmit = form.handleSubmit(async (data) => {
    await mutateAsync(
      {
        name: data.name,
        geo_zones: data.countries.map(({ iso_2 }) => ({
          country_code: iso_2,
          type: "country"
        }))
      },
      {
        onSuccess: () => {
          toast.success(
            t2("stockLocations.serviceZones.create.successToast", {
              name: data.name
            })
          );
          handleSuccess(`/settings/locations/${location.id}`);
        },
        onError: (e) => {
          toast.error(e.message);
        }
      }
    );
  });
  return (0, import_jsx_runtime.jsx)(RouteFocusModal.Form, { form, children: (0, import_jsx_runtime.jsxs)(
    KeyboundForm,
    {
      className: "flex h-full flex-col overflow-hidden",
      onSubmit: handleSubmit,
      children: [
        (0, import_jsx_runtime.jsx)(RouteFocusModal.Header, {}),
        (0, import_jsx_runtime.jsx)(RouteFocusModal.Body, { className: "flex flex-1 flex-col items-center overflow-auto", children: (0, import_jsx_runtime.jsxs)(StackedFocusModal, { id: GEO_ZONE_STACKED_MODAL_ID, children: [
          (0, import_jsx_runtime.jsx)("div", { className: "flex flex-1 flex-col items-center", children: (0, import_jsx_runtime.jsxs)("div", { className: "flex w-full max-w-[720px] flex-col gap-y-8 px-2 py-16", children: [
            (0, import_jsx_runtime.jsx)(Heading, { children: type === "pickup" ? t2("stockLocations.serviceZones.create.headerPickup", {
              location: location.name
            }) : t2("stockLocations.serviceZones.create.headerShipping", {
              location: location.name
            }) }),
            (0, import_jsx_runtime.jsx)("div", { className: "grid grid-cols-1 gap-4 md:grid-cols-2", children: (0, import_jsx_runtime.jsx)(
              Form.Field,
              {
                control: form.control,
                name: "name",
                render: ({ field }) => {
                  return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                    (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("fields.name") }),
                    (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(Input, { ...field }) }),
                    (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                  ] });
                }
              }
            ) }),
            (0, import_jsx_runtime.jsx)(InlineTip, { label: t2("general.tip"), children: t2("stockLocations.serviceZones.fields.tip") }),
            (0, import_jsx_runtime.jsx)(GeoZoneForm, { form })
          ] }) }),
          (0, import_jsx_runtime.jsx)(GeoZoneForm.AreaDrawer, { form })
        ] }) }),
        (0, import_jsx_runtime.jsx)(RouteFocusModal.Footer, { children: (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center justify-end gap-x-2", children: [
          (0, import_jsx_runtime.jsx)(RouteFocusModal.Close, { asChild: true, children: (0, import_jsx_runtime.jsx)(Button, { variant: "secondary", size: "small", children: t2("actions.cancel") }) }),
          (0, import_jsx_runtime.jsx)(Button, { type: "submit", size: "small", isLoading: isPending, children: t2("actions.save") })
        ] }) })
      ]
    }
  ) });
}
function LocationCreateServiceZone() {
  var _a;
  const { fset_id, location_id } = useParams();
  const { stock_location, isPending, isFetching, isError, error } = useStockLocation(location_id, {
    fields: "*fulfillment_sets"
  });
  const fulfillmentSet = (_a = stock_location == null ? void 0 : stock_location.fulfillment_sets) == null ? void 0 : _a.find(
    (f) => f.id === fset_id
  );
  const type = (fulfillmentSet == null ? void 0 : fulfillmentSet.type) === "pickup" ? "pickup" : "shipping";
  if (!isPending && !isFetching && !fulfillmentSet) {
    throw json(
      { message: `Fulfillment set with ID: ${fset_id} was not found.` },
      404
    );
  }
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime2.jsx)(RouteFocusModal, { prev: `/settings/locations/${location_id}`, children: fulfillmentSet && (0, import_jsx_runtime2.jsx)(
    CreateServiceZoneForm,
    {
      fulfillmentSet,
      location: stock_location,
      type
    }
  ) });
}
export {
  LocationCreateServiceZone as Component
};
//# sourceMappingURL=location-service-zone-create-6MJCRY7U-SKMCRVHJ.js.map
