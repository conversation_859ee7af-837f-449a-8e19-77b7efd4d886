import {
  CSS,
  DndContext,
  DragOverlay,
  KeyboardCode,
  KeyboardSensor,
  MeasuringStrategy,
  PointerSensor,
  SortableContext,
  arrayMove,
  closestCenter,
  closestCorners,
  defaultDropAnimationConfiguration,
  getFirstCollision,
  useSensor,
  useSensors,
  useSortable,
  verticalListSortingStrategy
} from "./chunk-RVJLUPRX.js";
import {
  Badge,
  DotsSix,
  FolderIllustration,
  FolderOpenIllustration,
  IconButton,
  TagIllustration,
  TriangleRightMini,
  clx
} from "./chunk-YP2LLXWB.js";
import {
  require_react_dom
} from "./chunk-RPCDYKBN.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/chunk-XDCNT5GQ.mjs
var import_react = __toESM(require_react(), 1);
var import_react_dom = __toESM(require_react_dom(), 1);
var import_react2 = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime4 = __toESM(require_jsx_runtime(), 1);
var iOS = /iPad|iPhone|iPod/.test(navigator.platform);
function getDragDepth(offset, indentationWidth) {
  return Math.round(offset / indentationWidth);
}
function getProjection(items, activeId, overId, dragOffset, indentationWidth) {
  const overItemIndex = items.findIndex(({ id }) => id === overId);
  const activeItemIndex = items.findIndex(({ id }) => id === activeId);
  const activeItem = items[activeItemIndex];
  const newItems = arrayMove(items, activeItemIndex, overItemIndex);
  const previousItem = newItems[overItemIndex - 1];
  const nextItem = newItems[overItemIndex + 1];
  const dragDepth = getDragDepth(dragOffset, indentationWidth);
  const projectedDepth = activeItem.depth + dragDepth;
  const maxDepth = getMaxDepth({
    previousItem
  });
  const minDepth = getMinDepth({ nextItem });
  let depth = projectedDepth;
  if (projectedDepth >= maxDepth) {
    depth = maxDepth;
  } else if (projectedDepth < minDepth) {
    depth = minDepth;
  }
  return { depth, maxDepth, minDepth, parentId: getParentId() };
  function getParentId() {
    var _a;
    if (depth === 0 || !previousItem) {
      return null;
    }
    if (depth === previousItem.depth) {
      return previousItem.parentId;
    }
    if (depth > previousItem.depth) {
      return previousItem.id;
    }
    const newParent = (_a = newItems.slice(0, overItemIndex).reverse().find((item) => item.depth === depth)) == null ? void 0 : _a.parentId;
    return newParent ?? null;
  }
}
function getMaxDepth({ previousItem }) {
  if (previousItem) {
    return previousItem.depth + 1;
  }
  return 0;
}
function getMinDepth({ nextItem }) {
  if (nextItem) {
    return nextItem.depth;
  }
  return 0;
}
function flatten(items, parentId = null, depth = 0, childrenProp) {
  return items.reduce((acc, item, index) => {
    const children = item[childrenProp] || [];
    return [
      ...acc,
      { ...item, parentId, depth, index },
      ...flatten(children, item.id, depth + 1, childrenProp)
    ];
  }, []);
}
function flattenTree(items, childrenProp) {
  return flatten(items, void 0, void 0, childrenProp);
}
function buildTree(flattenedItems, newIndex, childrenProp) {
  const root = { id: "root", [childrenProp]: [] };
  const nodes = { [root.id]: root };
  const items = flattenedItems.map((item) => ({ ...item, [childrenProp]: [] }));
  let update = {
    id: null,
    parentId: null,
    index: 0
  };
  items.forEach((item, index) => {
    const {
      id,
      index: _index,
      depth: _depth,
      parentId: _parentId,
      ...rest
    } = item;
    const children = item[childrenProp] || [];
    const parentId = _parentId ?? root.id;
    const parent = nodes[parentId] ?? findItem(items, parentId);
    nodes[id] = { id, [childrenProp]: children };
    parent[childrenProp].push({
      id,
      ...rest,
      [childrenProp]: children
    });
    if (index === newIndex) {
      const parentChildren = parent[childrenProp];
      update = {
        id: item.id,
        parentId: parent.id === "root" ? null : parent.id,
        index: parentChildren.length - 1
      };
    }
  });
  if (!update.id) {
    throw new Error("Could not find item");
  }
  return {
    items: root[childrenProp],
    update
  };
}
function findItem(items, itemId) {
  return items.find(({ id }) => id === itemId);
}
function findItemDeep(items, itemId, childrenProp) {
  for (const item of items) {
    const { id } = item;
    const children = item[childrenProp] || [];
    if (id === itemId) {
      return item;
    }
    if (children.length) {
      const child = findItemDeep(children, itemId, childrenProp);
      if (child) {
        return child;
      }
    }
  }
  return void 0;
}
function countChildren(items, count = 0, childrenProp) {
  return items.reduce((acc, item) => {
    const children = item[childrenProp] || [];
    if (children.length) {
      return countChildren(children, acc + 1, childrenProp);
    }
    return acc + 1;
  }, count);
}
function getChildCount(items, id, childrenProp) {
  const item = findItemDeep(items, id, childrenProp);
  const children = (item == null ? void 0 : item[childrenProp]) || [];
  return item ? countChildren(children, 0, childrenProp) : 0;
}
function removeChildrenOf(items, ids, childrenProp) {
  const excludeParentIds = [...ids];
  return items.filter((item) => {
    if (item.parentId && excludeParentIds.includes(item.parentId)) {
      const children = item[childrenProp] || [];
      if (children.length) {
        excludeParentIds.push(item.id);
      }
      return false;
    }
    return true;
  });
}
var directions = [
  KeyboardCode.Down,
  KeyboardCode.Right,
  KeyboardCode.Up,
  KeyboardCode.Left
];
var horizontal = [KeyboardCode.Left, KeyboardCode.Right];
var sortableTreeKeyboardCoordinates = (context, indentationWidth) => (event, {
  currentCoordinates,
  context: {
    active,
    over,
    collisionRect,
    droppableRects,
    droppableContainers
  }
}) => {
  if (directions.includes(event.code)) {
    if (!active || !collisionRect) {
      return;
    }
    event.preventDefault();
    const {
      current: { items, offset }
    } = context;
    if (horizontal.includes(event.code) && (over == null ? void 0 : over.id)) {
      const { depth, maxDepth, minDepth } = getProjection(
        items,
        active.id,
        over.id,
        offset,
        indentationWidth
      );
      switch (event.code) {
        case KeyboardCode.Left:
          if (depth > minDepth) {
            return {
              ...currentCoordinates,
              x: currentCoordinates.x - indentationWidth
            };
          }
          break;
        case KeyboardCode.Right:
          if (depth < maxDepth) {
            return {
              ...currentCoordinates,
              x: currentCoordinates.x + indentationWidth
            };
          }
          break;
      }
      return void 0;
    }
    const containers = [];
    droppableContainers.forEach((container) => {
      if ((container == null ? void 0 : container.disabled) || container.id === (over == null ? void 0 : over.id)) {
        return;
      }
      const rect = droppableRects.get(container.id);
      if (!rect) {
        return;
      }
      switch (event.code) {
        case KeyboardCode.Down:
          if (collisionRect.top < rect.top) {
            containers.push(container);
          }
          break;
        case KeyboardCode.Up:
          if (collisionRect.top > rect.top) {
            containers.push(container);
          }
          break;
      }
    });
    const collisions = closestCorners({
      active,
      collisionRect,
      pointerCoordinates: null,
      droppableRects,
      droppableContainers: containers
    });
    let closestId = getFirstCollision(collisions, "id");
    if (closestId === (over == null ? void 0 : over.id) && collisions.length > 1) {
      closestId = collisions[1].id;
    }
    if (closestId && (over == null ? void 0 : over.id)) {
      const activeRect = droppableRects.get(active.id);
      const newRect = droppableRects.get(closestId);
      const newDroppable = droppableContainers.get(closestId);
      if (activeRect && newRect && newDroppable) {
        const newIndex = items.findIndex(({ id }) => id === closestId);
        const newItem = items[newIndex];
        const activeIndex = items.findIndex(({ id }) => id === active.id);
        const activeItem = items[activeIndex];
        if (newItem && activeItem) {
          const { depth } = getProjection(
            items,
            active.id,
            closestId,
            (newItem.depth - activeItem.depth) * indentationWidth,
            indentationWidth
          );
          const isBelow = newIndex > activeIndex;
          const modifier = isBelow ? 1 : -1;
          const offset2 = 0;
          const newCoordinates = {
            x: newRect.left + depth * indentationWidth,
            y: newRect.top + modifier * offset2
          };
          return newCoordinates;
        }
      }
    }
  }
  return void 0;
};
var TreeItem = (0, import_react2.forwardRef)(
  ({
    childCount,
    clone,
    depth,
    disableSelection,
    disableInteraction,
    ghost,
    handleProps,
    indentationWidth,
    collapsed,
    onCollapse,
    style,
    value,
    disabled,
    wrapperRef,
    ...props
  }, ref) => {
    return (0, import_jsx_runtime.jsx)(
      "li",
      {
        ref: wrapperRef,
        style: {
          paddingLeft: `${indentationWidth * depth}px`
        },
        className: clx("-mb-px list-none", {
          "pointer-events-none": disableInteraction,
          "select-none": disableSelection,
          "[&:first-of-type>div]:border-t-0": !clone
        }),
        ...props,
        children: (0, import_jsx_runtime.jsxs)(
          "div",
          {
            ref,
            style,
            className: clx(
              "bg-ui-bg-base transition-fg relative flex items-center gap-x-3 border-y px-6 py-2.5",
              {
                "border-l": depth > 0,
                "shadow-elevation-flyout bg-ui-bg-base w-fit rounded-lg border-none pr-6 opacity-80": clone,
                "bg-ui-bg-base-hover z-[1] opacity-50": ghost,
                "bg-ui-bg-disabled": disabled
              }
            ),
            children: [
              (0, import_jsx_runtime.jsx)(Handle, { ...handleProps, disabled }),
              (0, import_jsx_runtime.jsx)(
                Collapse,
                {
                  collapsed,
                  onCollapse,
                  clone
                }
              ),
              (0, import_jsx_runtime.jsx)(
                Icon,
                {
                  childrenCount: childCount,
                  collapsed,
                  clone
                }
              ),
              (0, import_jsx_runtime.jsx)(Value, { value }),
              (0, import_jsx_runtime.jsx)(ChildrenCount, { clone, childrenCount: childCount })
            ]
          }
        )
      }
    );
  }
);
TreeItem.displayName = "TreeItem";
var Handle = ({
  listeners,
  attributes,
  disabled
}) => {
  return (0, import_jsx_runtime.jsx)(
    IconButton,
    {
      size: "small",
      variant: "transparent",
      type: "button",
      className: clx("cursor-grab", { "cursor-not-allowed": disabled }),
      disabled,
      ...attributes,
      ...listeners,
      children: (0, import_jsx_runtime.jsx)(DotsSix, {})
    }
  );
};
var Icon = ({ childrenCount, collapsed, clone }) => {
  const isBranch = clone ? childrenCount && childrenCount > 1 : childrenCount;
  const isOpen = clone ? false : !collapsed;
  return (0, import_jsx_runtime.jsx)("div", { className: "flex size-7 items-center justify-center", children: isBranch ? isOpen ? (0, import_jsx_runtime.jsx)(FolderOpenIllustration, {}) : (0, import_jsx_runtime.jsx)(FolderIllustration, {}) : (0, import_jsx_runtime.jsx)(TagIllustration, {}) });
};
var Collapse = ({ collapsed, onCollapse, clone }) => {
  if (clone) {
    return null;
  }
  if (!onCollapse) {
    return (0, import_jsx_runtime.jsx)("div", { className: "size-7", role: "presentation" });
  }
  return (0, import_jsx_runtime.jsx)(
    IconButton,
    {
      size: "small",
      variant: "transparent",
      onClick: onCollapse,
      type: "button",
      children: (0, import_jsx_runtime.jsx)(
        TriangleRightMini,
        {
          className: clx("text-ui-fg-subtle transition-transform", {
            "rotate-90": !collapsed
          })
        }
      )
    }
  );
};
var Value = ({ value }) => {
  return (0, import_jsx_runtime.jsx)("div", { className: "txt-compact-small text-ui-fg-subtle flex-grow truncate", children: value });
};
var ChildrenCount = ({ clone, childrenCount }) => {
  if (!clone || !childrenCount) {
    return null;
  }
  if (clone && childrenCount <= 1) {
    return null;
  }
  return (0, import_jsx_runtime.jsx)(Badge, { size: "2xsmall", color: "blue", className: "absolute -right-2 -top-2", children: childrenCount });
};
var animateLayoutChanges = ({
  isSorting,
  wasDragging
}) => {
  return isSorting || wasDragging ? false : true;
};
function SortableTreeItem({
  id,
  depth,
  disabled,
  ...props
}) {
  const {
    attributes,
    isDragging,
    isSorting,
    listeners,
    setDraggableNodeRef,
    setDroppableNodeRef,
    transform,
    transition
  } = useSortable({
    id,
    animateLayoutChanges,
    disabled
  });
  const style = {
    transform: CSS.Translate.toString(transform),
    transition
  };
  return (0, import_jsx_runtime2.jsx)(
    TreeItem,
    {
      ref: setDraggableNodeRef,
      wrapperRef: setDroppableNodeRef,
      style,
      depth,
      ghost: isDragging,
      disableSelection: iOS,
      disableInteraction: isSorting,
      disabled,
      handleProps: {
        listeners,
        attributes
      },
      ...props
    }
  );
}
var measuring = {
  droppable: {
    strategy: MeasuringStrategy.Always
  }
};
var dropAnimationConfig = {
  keyframes({ transform }) {
    return [
      { opacity: 1, transform: CSS.Transform.toString(transform.initial) },
      {
        opacity: 0,
        transform: CSS.Transform.toString({
          ...transform.final,
          x: transform.final.x + 5,
          y: transform.final.y + 5
        })
      }
    ];
  },
  easing: "ease-out",
  sideEffects({ active }) {
    active.node.animate([{ opacity: 0 }, { opacity: 1 }], {
      duration: defaultDropAnimationConfiguration.duration,
      easing: defaultDropAnimationConfiguration.easing
    });
  }
};
function SortableTree({
  collapsible = true,
  childrenProp = "children",
  // "children" is the default children prop name
  enableDrag = true,
  items = [],
  indentationWidth = 40,
  onChange,
  renderValue
}) {
  const [collapsedState, setCollapsedState] = (0, import_react.useState)({});
  const [activeId, setActiveId] = (0, import_react.useState)(null);
  const [overId, setOverId] = (0, import_react.useState)(null);
  const [offsetLeft, setOffsetLeft] = (0, import_react.useState)(0);
  const [currentPosition, setCurrentPosition] = (0, import_react.useState)(null);
  const flattenedItems = (0, import_react.useMemo)(() => {
    const flattenedTree = flattenTree(items, childrenProp);
    const collapsedItems = flattenedTree.reduce(
      (acc, item) => {
        const { id } = item;
        const children = item[childrenProp] || [];
        const collapsed = collapsedState[id];
        return collapsed && children.length ? [...acc, id] : acc;
      },
      []
    );
    return removeChildrenOf(
      flattenedTree,
      activeId ? [activeId, ...collapsedItems] : collapsedItems,
      childrenProp
    );
  }, [activeId, items, childrenProp, collapsedState]);
  const projected = activeId && overId ? getProjection(
    flattenedItems,
    activeId,
    overId,
    offsetLeft,
    indentationWidth
  ) : null;
  const sensorContext = (0, import_react.useRef)({
    items: flattenedItems,
    offset: offsetLeft
  });
  const [coordinateGetter] = (0, import_react.useState)(
    () => sortableTreeKeyboardCoordinates(sensorContext, indentationWidth)
  );
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter
    })
  );
  const sortedIds = (0, import_react.useMemo)(
    () => flattenedItems.map(({ id }) => id),
    [flattenedItems]
  );
  const activeItem = activeId ? flattenedItems.find(({ id }) => id === activeId) : null;
  (0, import_react.useEffect)(() => {
    sensorContext.current = {
      items: flattenedItems,
      offset: offsetLeft
    };
  }, [flattenedItems, offsetLeft]);
  function handleDragStart({ active: { id: activeId2 } }) {
    setActiveId(activeId2);
    setOverId(activeId2);
    const activeItem2 = flattenedItems.find(({ id }) => id === activeId2);
    if (activeItem2) {
      setCurrentPosition({
        parentId: activeItem2.parentId,
        overId: activeId2
      });
    }
    document.body.style.setProperty("cursor", "grabbing");
  }
  function handleDragMove({ delta }) {
    setOffsetLeft(delta.x);
  }
  function handleDragOver({ over }) {
    setOverId((over == null ? void 0 : over.id) ?? null);
  }
  function handleDragEnd({ active, over }) {
    resetState();
    if (projected && over) {
      const { depth, parentId } = projected;
      const clonedItems = JSON.parse(
        JSON.stringify(flattenTree(items, childrenProp))
      );
      const overIndex = clonedItems.findIndex(({ id }) => id === over.id);
      const activeIndex = clonedItems.findIndex(({ id }) => id === active.id);
      const activeTreeItem = clonedItems[activeIndex];
      clonedItems[activeIndex] = { ...activeTreeItem, depth, parentId };
      const sortedItems = arrayMove(clonedItems, activeIndex, overIndex);
      const { items: newItems, update } = buildTree(
        sortedItems,
        overIndex,
        childrenProp
      );
      onChange(update, newItems);
    }
  }
  function handleDragCancel() {
    resetState();
  }
  function resetState() {
    setOverId(null);
    setActiveId(null);
    setOffsetLeft(0);
    setCurrentPosition(null);
    document.body.style.setProperty("cursor", "");
  }
  function handleCollapse(id) {
    setCollapsedState((state) => ({
      ...state,
      [id]: state[id] ? false : true
    }));
  }
  function getMovementAnnouncement(eventName, activeId2, overId2) {
    if (overId2 && projected) {
      if (eventName !== "onDragEnd") {
        if (currentPosition && projected.parentId === currentPosition.parentId && overId2 === currentPosition.overId) {
          return;
        } else {
          setCurrentPosition({
            parentId: projected.parentId,
            overId: overId2
          });
        }
      }
      const clonedItems = JSON.parse(
        JSON.stringify(flattenTree(items, childrenProp))
      );
      const overIndex = clonedItems.findIndex(({ id }) => id === overId2);
      const activeIndex = clonedItems.findIndex(({ id }) => id === activeId2);
      const sortedItems = arrayMove(clonedItems, activeIndex, overIndex);
      const previousItem = sortedItems[overIndex - 1];
      let announcement;
      const movedVerb = eventName === "onDragEnd" ? "dropped" : "moved";
      const nestedVerb = eventName === "onDragEnd" ? "dropped" : "nested";
      if (!previousItem) {
        const nextItem = sortedItems[overIndex + 1];
        announcement = `${activeId2} was ${movedVerb} before ${nextItem.id}.`;
      } else {
        if (projected.depth > previousItem.depth) {
          announcement = `${activeId2} was ${nestedVerb} under ${previousItem.id}.`;
        } else {
          let previousSibling = previousItem;
          while (previousSibling && projected.depth < previousSibling.depth) {
            const parentId = previousSibling.parentId;
            previousSibling = sortedItems.find(({ id }) => id === parentId);
          }
          if (previousSibling) {
            announcement = `${activeId2} was ${movedVerb} after ${previousSibling.id}.`;
          }
        }
      }
      return announcement;
    }
    return;
  }
  const announcements = {
    onDragStart({ active }) {
      return `Picked up ${active.id}.`;
    },
    onDragMove({ active, over }) {
      return getMovementAnnouncement("onDragMove", active.id, over == null ? void 0 : over.id);
    },
    onDragOver({ active, over }) {
      return getMovementAnnouncement("onDragOver", active.id, over == null ? void 0 : over.id);
    },
    onDragEnd({ active, over }) {
      return getMovementAnnouncement("onDragEnd", active.id, over == null ? void 0 : over.id);
    },
    onDragCancel({ active }) {
      return `Moving was cancelled. ${active.id} was dropped in its original position.`;
    }
  };
  return (0, import_jsx_runtime3.jsx)(
    DndContext,
    {
      accessibility: { announcements },
      sensors,
      collisionDetection: closestCenter,
      measuring,
      onDragStart: handleDragStart,
      onDragMove: handleDragMove,
      onDragOver: handleDragOver,
      onDragEnd: handleDragEnd,
      onDragCancel: handleDragCancel,
      children: (0, import_jsx_runtime3.jsxs)(SortableContext, { items: sortedIds, strategy: verticalListSortingStrategy, children: [
        flattenedItems.map((item) => {
          const { id, depth } = item;
          const children = item[childrenProp] || [];
          const disabled = typeof enableDrag === "function" ? !enableDrag(item) : !enableDrag;
          return (0, import_jsx_runtime3.jsx)(
            SortableTreeItem,
            {
              id,
              value: renderValue(item),
              disabled,
              depth: id === activeId && projected ? projected.depth : depth,
              indentationWidth,
              collapsed: Boolean(collapsedState[id] && children.length),
              childCount: children.length,
              onCollapse: collapsible && children.length ? () => handleCollapse(id) : void 0
            },
            id
          );
        }),
        (0, import_react_dom.createPortal)(
          (0, import_jsx_runtime3.jsx)(DragOverlay, { dropAnimation: dropAnimationConfig, children: activeId && activeItem ? (0, import_jsx_runtime3.jsx)(
            SortableTreeItem,
            {
              id: activeId,
              depth: activeItem.depth,
              clone: true,
              childCount: getChildCount(items, activeId, childrenProp) + 1,
              value: renderValue(activeItem),
              indentationWidth: 0
            }
          ) : null }),
          document.body
        )
      ] })
    }
  );
}
var CategoryTree = ({
  value,
  onChange,
  renderValue,
  enableDrag = true,
  isLoading = false
}) => {
  if (isLoading) {
    return (0, import_jsx_runtime4.jsx)("div", { className: "txt-compact-small relative flex-1 overflow-y-auto", children: Array.from({ length: 10 }).map((_, i) => (0, import_jsx_runtime4.jsx)(CategoryLeafPlaceholder, {}, i)) });
  }
  return (0, import_jsx_runtime4.jsx)(
    SortableTree,
    {
      items: value,
      childrenProp: "category_children",
      collapsible: true,
      enableDrag,
      onChange,
      renderValue
    }
  );
};
var CategoryLeafPlaceholder = () => {
  return (0, import_jsx_runtime4.jsx)("div", { className: "bg-ui-bg-base -mb-px flex h-12 animate-pulse items-center border-y px-6 py-2.5" });
};

export {
  CategoryTree
};
//# sourceMappingURL=chunk-C7GWWUEO.js.map
