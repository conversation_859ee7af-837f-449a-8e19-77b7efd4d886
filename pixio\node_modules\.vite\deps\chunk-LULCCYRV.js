import {
  countries
} from "./chunk-HPGXK5DQ.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import {
  TrianglesMini,
  clx
} from "./chunk-YP2LLXWB.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/chunk-SCBXRJPV.mjs
var import_react = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var CountrySelect = (0, import_react.forwardRef)(
  ({ className, disabled, placeholder, value, defaultValue, ...props }, ref) => {
    var _a;
    const { t } = useTranslation();
    const innerRef = (0, import_react.useRef)(null);
    (0, import_react.useImperativeHandle)(ref, () => innerRef.current);
    const isPlaceholder = ((_a = innerRef.current) == null ? void 0 : _a.value) === "";
    return (0, import_jsx_runtime.jsxs)("div", { className: "relative", children: [
      (0, import_jsx_runtime.jsx)(
        TrianglesMini,
        {
          className: clx(
            "text-ui-fg-muted transition-fg pointer-events-none absolute right-2 top-1/2 -translate-y-1/2",
            {
              "text-ui-fg-disabled": disabled
            }
          )
        }
      ),
      (0, import_jsx_runtime.jsxs)(
        "select",
        {
          value: value !== void 0 ? value.toLowerCase() : void 0,
          defaultValue: defaultValue ? defaultValue.toLowerCase() : void 0,
          disabled,
          className: clx(
            "bg-ui-bg-field shadow-buttons-neutral transition-fg txt-compact-small flex w-full select-none appearance-none items-center justify-between rounded-md px-2 py-1.5 outline-none",
            "placeholder:text-ui-fg-muted text-ui-fg-base",
            "hover:bg-ui-bg-field-hover",
            "focus-visible:shadow-borders-interactive-with-active data-[state=open]:!shadow-borders-interactive-with-active",
            "aria-[invalid=true]:border-ui-border-error aria-[invalid=true]:shadow-borders-error",
            "invalid::border-ui-border-error invalid:shadow-borders-error",
            "disabled:!bg-ui-bg-disabled disabled:!text-ui-fg-disabled",
            {
              "text-ui-fg-muted": isPlaceholder
            },
            className
          ),
          ...props,
          ref: innerRef,
          children: [
            (0, import_jsx_runtime.jsx)("option", { value: "", disabled: true, className: "text-ui-fg-muted", children: placeholder || t("fields.selectCountry") }),
            countries.map((country) => {
              return (0, import_jsx_runtime.jsx)("option", { value: country.iso_2.toLowerCase(), children: country.display_name }, country.iso_2);
            })
          ]
        }
      )
    ] });
  }
);
CountrySelect.displayName = "CountrySelect";

export {
  CountrySelect
};
//# sourceMappingURL=chunk-LULCCYRV.js.map
