{"version": 3, "sources": ["../../@medusajs/dashboard/dist/region-list-SIDHBM6S.mjs"], "sourcesContent": ["import {\n  useRegionTableColumns\n} from \"./chunk-QMRGIWOP.mjs\";\nimport \"./chunk-I3VB6NM2.mjs\";\nimport {\n  useRegionTableQuery\n} from \"./chunk-XR4GEMGR.mjs\";\nimport \"./chunk-IR5DHEKS.mjs\";\nimport \"./chunk-LQTHYS2Z.mjs\";\nimport \"./chunk-P3UUX2T6.mjs\";\nimport {\n  _DataTable,\n  useDataTable\n} from \"./chunk-UE6PO4FK.mjs\";\nimport \"./chunk-YEDAFXMB.mjs\";\nimport \"./chunk-AOFGTNG6.mjs\";\nimport \"./chunk-EMIHDNB7.mjs\";\nimport {\n  useRegionTableFilters\n} from \"./chunk-LT4MVCA7.mjs\";\nimport \"./chunk-SXYXTC2L.mjs\";\nimport \"./chunk-M3VFKDXJ.mjs\";\nimport {\n  SingleColumnPage\n} from \"./chunk-2RQLKDBF.mjs\";\nimport \"./chunk-C76H5USB.mjs\";\nimport \"./chunk-KSV3NQOT.mjs\";\nimport \"./chunk-DG7J63J2.mjs\";\nimport {\n  useExtension\n} from \"./chunk-C5P5PL3E.mjs\";\nimport \"./chunk-LPEUYMRK.mjs\";\nimport {\n  ActionMenu\n} from \"./chunk-3NJTXRIY.mjs\";\nimport \"./chunk-OC7BQLYI.mjs\";\nimport \"./chunk-NYXYHZE6.mjs\";\nimport {\n  useDeleteRegion,\n  useRegions\n} from \"./chunk-QZ6PT4QV.mjs\";\nimport \"./chunk-QL4XKIVL.mjs\";\nimport \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-RPUOO7AV.mjs\";\n\n// src/routes/regions/region-list/components/region-list-table/region-list-table.tsx\nimport { PencilSquare, Trash } from \"@medusajs/icons\";\nimport {\n  Button,\n  Container,\n  Heading,\n  Text,\n  toast,\n  usePrompt\n} from \"@medusajs/ui\";\nimport { keepPreviousData } from \"@tanstack/react-query\";\nimport { createColumnHelper } from \"@tanstack/react-table\";\nimport { useMemo } from \"react\";\nimport { useTranslation } from \"react-i18next\";\nimport { Link } from \"react-router-dom\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar PAGE_SIZE = 20;\nvar RegionListTable = () => {\n  const { t } = useTranslation();\n  const { searchParams, raw } = useRegionTableQuery({ pageSize: PAGE_SIZE });\n  const {\n    regions,\n    count,\n    isPending: isLoading,\n    isError,\n    error\n  } = useRegions(\n    {\n      ...searchParams,\n      fields: \"*payment_providers\"\n    },\n    {\n      placeholderData: keepPreviousData\n    }\n  );\n  const filters = useRegionTableFilters();\n  const columns = useColumns();\n  const { table } = useDataTable({\n    data: regions ?? [],\n    columns,\n    count,\n    enablePagination: true,\n    getRowId: (row) => row.id,\n    pageSize: PAGE_SIZE\n  });\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsxs(Container, { className: \"divide-y p-0\", children: [\n    /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center justify-between px-6 py-4\", children: [\n      /* @__PURE__ */ jsxs(\"div\", { children: [\n        /* @__PURE__ */ jsx(Heading, { children: t(\"regions.domain\") }),\n        /* @__PURE__ */ jsx(Text, { className: \"text-ui-fg-subtle\", size: \"small\", children: t(\"regions.subtitle\") })\n      ] }),\n      /* @__PURE__ */ jsx(Link, { to: \"/settings/regions/create\", children: /* @__PURE__ */ jsx(Button, { size: \"small\", variant: \"secondary\", children: t(\"actions.create\") }) })\n    ] }),\n    /* @__PURE__ */ jsx(\n      _DataTable,\n      {\n        table,\n        columns,\n        count,\n        pageSize: PAGE_SIZE,\n        isLoading,\n        filters,\n        orderBy: [\n          { key: \"name\", label: t(\"fields.name\") },\n          { key: \"created_at\", label: t(\"fields.createdAt\") },\n          { key: \"updated_at\", label: t(\"fields.updatedAt\") }\n        ],\n        navigateTo: (row) => `${row.original.id}`,\n        pagination: true,\n        search: true,\n        queryObject: raw,\n        noRecords: {\n          message: t(\"regions.list.noRecordsMessage\")\n        }\n      }\n    )\n  ] });\n};\nvar RegionActions = ({ region }) => {\n  const { t } = useTranslation();\n  const prompt = usePrompt();\n  const { mutateAsync } = useDeleteRegion(region.id);\n  const handleDelete = async () => {\n    const res = await prompt({\n      title: t(\"general.areYouSure\"),\n      description: t(\"regions.deleteRegionWarning\", {\n        name: region.name\n      }),\n      verificationText: region.name,\n      verificationInstruction: t(\"general.typeToConfirm\"),\n      confirmText: t(\"actions.delete\"),\n      cancelText: t(\"actions.cancel\")\n    });\n    if (!res) {\n      return;\n    }\n    await mutateAsync(void 0, {\n      onSuccess: () => {\n        toast.success(t(\"regions.toast.delete\"));\n      },\n      onError: (e) => {\n        toast.error(e.message);\n      }\n    });\n  };\n  return /* @__PURE__ */ jsx(\n    ActionMenu,\n    {\n      groups: [\n        {\n          actions: [\n            {\n              label: t(\"actions.edit\"),\n              to: `/settings/regions/${region.id}/edit`,\n              icon: /* @__PURE__ */ jsx(PencilSquare, {})\n            }\n          ]\n        },\n        {\n          actions: [\n            {\n              label: t(\"actions.delete\"),\n              onClick: handleDelete,\n              icon: /* @__PURE__ */ jsx(Trash, {})\n            }\n          ]\n        }\n      ]\n    }\n  );\n};\nvar columnHelper = createColumnHelper();\nvar useColumns = () => {\n  const base = useRegionTableColumns();\n  return useMemo(\n    () => [\n      ...base,\n      columnHelper.display({\n        id: \"actions\",\n        cell: ({ row }) => {\n          return /* @__PURE__ */ jsx(RegionActions, { region: row.original });\n        }\n      })\n    ],\n    [base]\n  );\n};\n\n// src/routes/regions/region-list/region-list.tsx\nimport { jsx as jsx2 } from \"react/jsx-runtime\";\nvar RegionList = () => {\n  const { getWidgets } = useExtension();\n  return /* @__PURE__ */ jsx2(\n    SingleColumnPage,\n    {\n      widgets: {\n        before: getWidgets(\"region.list.before\"),\n        after: getWidgets(\"region.list.after\")\n      },\n      children: /* @__PURE__ */ jsx2(RegionListTable, {})\n    }\n  );\n};\nexport {\n  RegionList as Component\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2DA,mBAAwB;AAGxB,yBAA0B;AAyI1B,IAAAA,sBAA4B;AAxI5B,IAAI,YAAY;AAChB,IAAI,kBAAkB,MAAM;AAC1B,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,QAAM,EAAE,cAAc,IAAI,IAAI,oBAAoB,EAAE,UAAU,UAAU,CAAC;AACzE,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA,WAAW;AAAA,IACX;AAAA,IACA;AAAA,EACF,IAAI;AAAA,IACF;AAAA,MACE,GAAG;AAAA,MACH,QAAQ;AAAA,IACV;AAAA,IACA;AAAA,MACE,iBAAiB;AAAA,IACnB;AAAA,EACF;AACA,QAAM,UAAU,sBAAsB;AACtC,QAAM,UAAU,WAAW;AAC3B,QAAM,EAAE,MAAM,IAAI,aAAa;AAAA,IAC7B,MAAM,WAAW,CAAC;AAAA,IAClB;AAAA,IACA;AAAA,IACA,kBAAkB;AAAA,IAClB,UAAU,CAAC,QAAQ,IAAI;AAAA,IACvB,UAAU;AAAA,EACZ,CAAC;AACD,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,yBAAK,WAAW,EAAE,WAAW,gBAAgB,UAAU;AAAA,QAC5D,yBAAK,OAAO,EAAE,WAAW,+CAA+C,UAAU;AAAA,UAChF,yBAAK,OAAO,EAAE,UAAU;AAAA,YACtB,wBAAI,SAAS,EAAE,UAAU,EAAE,gBAAgB,EAAE,CAAC;AAAA,YAC9C,wBAAI,MAAM,EAAE,WAAW,qBAAqB,MAAM,SAAS,UAAU,EAAE,kBAAkB,EAAE,CAAC;AAAA,MAC9G,EAAE,CAAC;AAAA,UACa,wBAAI,MAAM,EAAE,IAAI,4BAA4B,cAA0B,wBAAI,QAAQ,EAAE,MAAM,SAAS,SAAS,aAAa,UAAU,EAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;AAAA,IAC7K,EAAE,CAAC;AAAA,QACa;AAAA,MACd;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,QACA;AAAA,QACA,UAAU;AAAA,QACV;AAAA,QACA;AAAA,QACA,SAAS;AAAA,UACP,EAAE,KAAK,QAAQ,OAAO,EAAE,aAAa,EAAE;AAAA,UACvC,EAAE,KAAK,cAAc,OAAO,EAAE,kBAAkB,EAAE;AAAA,UAClD,EAAE,KAAK,cAAc,OAAO,EAAE,kBAAkB,EAAE;AAAA,QACpD;AAAA,QACA,YAAY,CAAC,QAAQ,GAAG,IAAI,SAAS,EAAE;AAAA,QACvC,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,aAAa;AAAA,QACb,WAAW;AAAA,UACT,SAAS,EAAE,+BAA+B;AAAA,QAC5C;AAAA,MACF;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;AACA,IAAI,gBAAgB,CAAC,EAAE,OAAO,MAAM;AAClC,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,QAAM,SAAS,UAAU;AACzB,QAAM,EAAE,YAAY,IAAI,gBAAgB,OAAO,EAAE;AACjD,QAAM,eAAe,YAAY;AAC/B,UAAM,MAAM,MAAM,OAAO;AAAA,MACvB,OAAO,EAAE,oBAAoB;AAAA,MAC7B,aAAa,EAAE,+BAA+B;AAAA,QAC5C,MAAM,OAAO;AAAA,MACf,CAAC;AAAA,MACD,kBAAkB,OAAO;AAAA,MACzB,yBAAyB,EAAE,uBAAuB;AAAA,MAClD,aAAa,EAAE,gBAAgB;AAAA,MAC/B,YAAY,EAAE,gBAAgB;AAAA,IAChC,CAAC;AACD,QAAI,CAAC,KAAK;AACR;AAAA,IACF;AACA,UAAM,YAAY,QAAQ;AAAA,MACxB,WAAW,MAAM;AACf,cAAM,QAAQ,EAAE,sBAAsB,CAAC;AAAA,MACzC;AAAA,MACA,SAAS,CAAC,MAAM;AACd,cAAM,MAAM,EAAE,OAAO;AAAA,MACvB;AAAA,IACF,CAAC;AAAA,EACH;AACA,aAAuB;AAAA,IACrB;AAAA,IACA;AAAA,MACE,QAAQ;AAAA,QACN;AAAA,UACE,SAAS;AAAA,YACP;AAAA,cACE,OAAO,EAAE,cAAc;AAAA,cACvB,IAAI,qBAAqB,OAAO,EAAE;AAAA,cAClC,UAAsB,wBAAI,cAAc,CAAC,CAAC;AAAA,YAC5C;AAAA,UACF;AAAA,QACF;AAAA,QACA;AAAA,UACE,SAAS;AAAA,YACP;AAAA,cACE,OAAO,EAAE,gBAAgB;AAAA,cACzB,SAAS;AAAA,cACT,UAAsB,wBAAI,OAAO,CAAC,CAAC;AAAA,YACrC;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAI,eAAe,mBAAmB;AACtC,IAAI,aAAa,MAAM;AACrB,QAAM,OAAO,sBAAsB;AACnC,aAAO;AAAA,IACL,MAAM;AAAA,MACJ,GAAG;AAAA,MACH,aAAa,QAAQ;AAAA,QACnB,IAAI;AAAA,QACJ,MAAM,CAAC,EAAE,IAAI,MAAM;AACjB,qBAAuB,wBAAI,eAAe,EAAE,QAAQ,IAAI,SAAS,CAAC;AAAA,QACpE;AAAA,MACF,CAAC;AAAA,IACH;AAAA,IACA,CAAC,IAAI;AAAA,EACP;AACF;AAIA,IAAI,aAAa,MAAM;AACrB,QAAM,EAAE,WAAW,IAAI,aAAa;AACpC,aAAuB,oBAAAC;AAAA,IACrB;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP,QAAQ,WAAW,oBAAoB;AAAA,QACvC,OAAO,WAAW,mBAAmB;AAAA,MACvC;AAAA,MACA,cAA0B,oBAAAA,KAAK,iBAAiB,CAAC,CAAC;AAAA,IACpD;AAAA,EACF;AACF;", "names": ["import_jsx_runtime", "jsx2"]}