import {
  productsQueryKeys
} from "./chunk-AJYMIHLQ.js";
import {
  queryClient
} from "./chunk-VMLNCWLE.js";
import {
  queryKeysFactory
} from "./chunk-CUPZIPFX.js";
import {
  sdk
} from "./chunk-JFCIYOH4.js";
import {
  useMutation,
  useQuery
} from "./chunk-R35JBZ3G.js";

// node_modules/@medusajs/dashboard/dist/chunk-3OHH43G6.mjs
var COLLECTION_QUERY_KEY = "collections";
var collectionsQueryKeys = queryKeysFactory(COLLECTION_QUERY_KEY);
var useCollection = (id, options) => {
  const { data, ...rest } = useQuery({
    queryKey: collectionsQueryKeys.detail(id),
    queryFn: async () => sdk.admin.productCollection.retrieve(id),
    ...options
  });
  return { ...data, ...rest };
};
var useCollections = (query, options) => {
  const { data, ...rest } = useQuery({
    queryKey: collectionsQueryKeys.list(query),
    queryFn: async () => sdk.admin.productCollection.list(query),
    ...options
  });
  return { ...data, ...rest };
};
var useUpdateCollection = (id, options) => {
  return useMutation({
    mutationFn: (payload) => sdk.admin.productCollection.update(id, payload),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({ queryKey: collectionsQueryKeys.lists() });
      queryClient.invalidateQueries({
        queryKey: collectionsQueryKeys.detail(id)
      });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useUpdateCollectionProducts = (id, options) => {
  return useMutation({
    mutationFn: (payload) => sdk.admin.productCollection.updateProducts(id, payload),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({ queryKey: collectionsQueryKeys.lists() });
      queryClient.invalidateQueries({
        queryKey: collectionsQueryKeys.detail(id)
      });
      queryClient.invalidateQueries({
        queryKey: productsQueryKeys.lists()
      });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useCreateCollection = (options) => {
  return useMutation({
    mutationFn: (payload) => sdk.admin.productCollection.create(payload),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({ queryKey: collectionsQueryKeys.lists() });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useDeleteCollection = (id, options) => {
  return useMutation({
    mutationFn: () => sdk.admin.productCollection.delete(id),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({ queryKey: collectionsQueryKeys.lists() });
      queryClient.invalidateQueries({
        queryKey: collectionsQueryKeys.detail(id)
      });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};

export {
  collectionsQueryKeys,
  useCollection,
  useCollections,
  useUpdateCollection,
  useUpdateCollectionProducts,
  useCreateCollection,
  useDeleteCollection
};
//# sourceMappingURL=chunk-YL3TYBFR.js.map
