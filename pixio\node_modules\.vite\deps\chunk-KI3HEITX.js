import {
  shippingOptionsQueryKeys
} from "./chunk-MSQ25CWB.js";
import {
  stockLocationsQueryKeys
} from "./chunk-ONYSAQ5Z.js";
import {
  queryClient
} from "./chunk-VMLNCWLE.js";
import {
  queryKeysFactory
} from "./chunk-CUPZIPFX.js";
import {
  sdk
} from "./chunk-JFCIYOH4.js";
import {
  useMutation
} from "./chunk-R35JBZ3G.js";

// node_modules/@medusajs/dashboard/dist/chunk-ENV6YVOM.mjs
var FULFILLMENT_SETS_QUERY_KEY = "fulfillment_sets";
var fulfillmentSetsQueryKeys = queryKeysFactory(
  FULFILLMENT_SETS_QUERY_KEY
);
var useDeleteFulfillmentSet = (id, options) => {
  return useMutation({
    mutationFn: () => sdk.admin.fulfillmentSet.delete(id),
    onSuccess: async (data, variables, context) => {
      var _a;
      await queryClient.invalidateQueries({
        queryKey: fulfillmentSetsQueryKeys.detail(id)
      });
      await queryClient.invalidateQueries({
        queryKey: fulfillmentSetsQueryKeys.lists()
      });
      await queryClient.invalidateQueries({
        queryKey: stockLocationsQueryKeys.all
      });
      await queryClient.invalidateQueries({
        queryKey: shippingOptionsQueryKeys.all
      });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useCreateFulfillmentSetServiceZone = (fulfillmentSetId, options) => {
  return useMutation({
    mutationFn: (payload) => sdk.admin.fulfillmentSet.createServiceZone(fulfillmentSetId, payload),
    onSuccess: async (data, variables, context) => {
      var _a;
      await queryClient.invalidateQueries({
        queryKey: fulfillmentSetsQueryKeys.lists()
      });
      await queryClient.invalidateQueries({
        queryKey: stockLocationsQueryKeys.all
      });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useUpdateFulfillmentSetServiceZone = (fulfillmentSetId, serviceZoneId, options) => {
  return useMutation({
    mutationFn: (payload) => sdk.admin.fulfillmentSet.updateServiceZone(
      fulfillmentSetId,
      serviceZoneId,
      payload
    ),
    onSuccess: async (data, variables, context) => {
      var _a;
      await queryClient.invalidateQueries({
        queryKey: fulfillmentSetsQueryKeys.lists()
      });
      await queryClient.invalidateQueries({
        queryKey: stockLocationsQueryKeys.all
      });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useDeleteFulfillmentServiceZone = (fulfillmentSetId, serviceZoneId, options) => {
  return useMutation({
    mutationFn: () => sdk.admin.fulfillmentSet.deleteServiceZone(
      fulfillmentSetId,
      serviceZoneId
    ),
    onSuccess: async (data, variables, context) => {
      var _a;
      await queryClient.invalidateQueries({
        queryKey: fulfillmentSetsQueryKeys.lists()
      });
      await queryClient.invalidateQueries({
        queryKey: shippingOptionsQueryKeys.lists()
      });
      await queryClient.invalidateQueries({
        queryKey: stockLocationsQueryKeys.all
      });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};

export {
  useDeleteFulfillmentSet,
  useCreateFulfillmentSetServiceZone,
  useUpdateFulfillmentSetServiceZone,
  useDeleteFulfillmentServiceZone
};
//# sourceMappingURL=chunk-KI3HEITX.js.map
