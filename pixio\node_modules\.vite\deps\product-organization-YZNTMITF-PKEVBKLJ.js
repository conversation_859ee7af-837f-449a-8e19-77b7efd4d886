import {
  PRODUCT_DETAIL_FIELDS
} from "./chunk-7NJ535R3.js";
import {
  CategoryCombobox
} from "./chunk-UF5DOWAO.js";
import {
  useComboboxData
} from "./chunk-CFEMRZCK.js";
import {
  Combobox
} from "./chunk-RC2DY4WG.js";
import "./chunk-EGRHWZRV.js";
import {
  KeyboundForm
} from "./chunk-DP54EP6X.js";
import {
  RouteDrawer,
  useRouteModal
} from "./chunk-MVVOBQIC.js";
import {
  FormExtensionZone,
  useExtendableForm
} from "./chunk-BS6QEWFI.js";
import "./chunk-MM7T76RN.js";
import "./chunk-WTOMBGNF.js";
import "./chunk-EGRHWZRV.js";
import "./chunk-EPRCCFRP.js";
import "./chunk-YM3FRBGU.js";
import "./chunk-7M4ICL3D.js";
import "./chunk-RQF55WOK.js";
import "./chunk-5QX4V4M4.js";
import "./chunk-HPGXK5DQ.js";
import "./chunk-IA4ROPJA.js";
import "./chunk-SP3VUFZN.js";
import "./chunk-WHQIBI5S.js";
import {
  arrayType,
  objectType,
  stringType
} from "./chunk-4XXECALA.js";
import "./chunk-6GQUHAET.js";
import {
  useExtension
} from "./chunk-FTD3ZWHZ.js";
import "./chunk-YXT43UJF.js";
import "./chunk-EIZ27OVL.js";
import "./chunk-CFRQOB2M.js";
import "./chunk-66DVUN72.js";
import "./chunk-DL4QDYPT.js";
import "./chunk-NV2N3EWM.js";
import "./chunk-BF7OBKIN.js";
import "./chunk-7UAYECTW.js";
import "./chunk-Y3NYV3NU.js";
import "./chunk-MPXR7HT5.js";
import {
  Form
} from "./chunk-XXJU43CK.js";
import "./chunk-WISME5HP.js";
import "./chunk-WAUSOHFQ.js";
import "./chunk-3ORK3QZ4.js";
import "./chunk-3A5TVVNI.js";
import "./chunk-EZ62MM7J.js";
import "./chunk-3R6LYIXM.js";
import "./chunk-2P2O4TMQ.js";
import "./chunk-KI3HEITX.js";
import "./chunk-2VUCAO7L.js";
import "./chunk-PTIULTKD.js";
import "./chunk-KISXY2LD.js";
import "./chunk-L7C3XPHJ.js";
import "./chunk-T7MG2EIJ.js";
import "./chunk-MER4VYPR.js";
import "./chunk-2AWKOUCD.js";
import "./chunk-YL3TYBFR.js";
import "./chunk-TZWW72YW.js";
import "./chunk-MSQ25CWB.js";
import "./chunk-ONYSAQ5Z.js";
import "./chunk-NIH7SAXN.js";
import "./chunk-UEJFWAFE.js";
import "./chunk-GR55MFKT.js";
import "./chunk-AAFHKNJG.js";
import "./chunk-7XDBFDTZ.js";
import "./chunk-3JKGO5XL.js";
import "./chunk-OISUTS7G.js";
import {
  useProduct,
  useUpdateProduct
} from "./chunk-AJYMIHLQ.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import {
  sdk
} from "./chunk-JFCIYOH4.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-5GF3RGIE.js";
import {
  useParams
} from "./chunk-T7YBVUWZ.js";
import {
  Button,
  Heading,
  toast
} from "./chunk-YP2LLXWB.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-ZSQ362B4.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/product-organization-YZNTMITF.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var ProductOrganizationSchema = objectType({
  type_id: stringType().nullable(),
  collection_id: stringType().nullable(),
  category_ids: arrayType(stringType()),
  tag_ids: arrayType(stringType())
});
var ProductOrganizationForm = ({
  product
}) => {
  var _a, _b;
  const { t } = useTranslation();
  const { handleSuccess } = useRouteModal();
  const { getFormConfigs, getFormFields } = useExtension();
  const configs = getFormConfigs("product", "organize");
  const fields = getFormFields("product", "organize");
  const collections = useComboboxData({
    queryKey: ["product_collections"],
    queryFn: (params) => sdk.admin.productCollection.list(params),
    getOptions: (data) => data.collections.map((collection) => ({
      label: collection.title,
      value: collection.id
    }))
  });
  const types = useComboboxData({
    queryKey: ["product_types"],
    queryFn: (params) => sdk.admin.productType.list(params),
    getOptions: (data) => data.product_types.map((type) => ({
      label: type.value,
      value: type.id
    }))
  });
  const tags = useComboboxData({
    queryKey: ["product_tags"],
    queryFn: (params) => sdk.admin.productTag.list(params),
    getOptions: (data) => data.product_tags.map((tag) => ({
      label: tag.value,
      value: tag.id
    }))
  });
  const form = useExtendableForm({
    defaultValues: {
      type_id: product.type_id ?? "",
      collection_id: product.collection_id ?? "",
      category_ids: ((_a = product.categories) == null ? void 0 : _a.map((c) => c.id)) || [],
      tag_ids: ((_b = product.tags) == null ? void 0 : _b.map((t2) => t2.id)) || []
    },
    schema: ProductOrganizationSchema,
    configs,
    data: product
  });
  const { mutateAsync, isPending } = useUpdateProduct(product.id);
  const handleSubmit = form.handleSubmit(async (data) => {
    var _a2;
    await mutateAsync(
      {
        type_id: data.type_id || null,
        collection_id: data.collection_id || null,
        categories: data.category_ids.map((c) => ({ id: c })),
        tags: (_a2 = data.tag_ids) == null ? void 0 : _a2.map((t2) => ({ id: t2 }))
      },
      {
        onSuccess: ({ product: product2 }) => {
          toast.success(
            t("products.organization.edit.toasts.success", {
              title: product2.title
            })
          );
          handleSuccess();
        },
        onError: (error) => {
          toast.error(error.message);
        }
      }
    );
  });
  return (0, import_jsx_runtime.jsx)(RouteDrawer.Form, { form, children: (0, import_jsx_runtime.jsxs)(KeyboundForm, { onSubmit: handleSubmit, className: "flex h-full flex-col", children: [
    (0, import_jsx_runtime.jsx)(RouteDrawer.Body, { children: (0, import_jsx_runtime.jsxs)("div", { className: "flex h-full flex-col gap-y-4", children: [
      (0, import_jsx_runtime.jsx)(
        Form.Field,
        {
          control: form.control,
          name: "type_id",
          render: ({ field }) => {
            return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
              (0, import_jsx_runtime.jsx)(Form.Label, { optional: true, children: t("products.fields.type.label") }),
              (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(
                Combobox,
                {
                  ...field,
                  options: types.options,
                  searchValue: types.searchValue,
                  onSearchValueChange: types.onSearchValueChange,
                  fetchNextPage: types.fetchNextPage
                }
              ) }),
              (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
            ] });
          }
        }
      ),
      (0, import_jsx_runtime.jsx)(
        Form.Field,
        {
          control: form.control,
          name: "collection_id",
          render: ({ field }) => {
            return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
              (0, import_jsx_runtime.jsx)(Form.Label, { optional: true, children: t("products.fields.collection.label") }),
              (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(
                Combobox,
                {
                  ...field,
                  multiple: false,
                  options: collections.options,
                  onSearchValueChange: collections.onSearchValueChange,
                  searchValue: collections.searchValue
                }
              ) }),
              (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
            ] });
          }
        }
      ),
      (0, import_jsx_runtime.jsx)(
        Form.Field,
        {
          control: form.control,
          name: "category_ids",
          render: ({ field }) => {
            return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
              (0, import_jsx_runtime.jsx)(Form.Label, { optional: true, children: t("products.fields.categories.label") }),
              (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(CategoryCombobox, { ...field }) }),
              (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
            ] });
          }
        }
      ),
      (0, import_jsx_runtime.jsx)(
        Form.Field,
        {
          control: form.control,
          name: "tag_ids",
          render: ({ field }) => {
            return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
              (0, import_jsx_runtime.jsx)(Form.Label, { optional: true, children: t("products.fields.tags.label") }),
              (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(
                Combobox,
                {
                  ...field,
                  multiple: true,
                  options: tags.options,
                  onSearchValueChange: tags.onSearchValueChange,
                  searchValue: tags.searchValue
                }
              ) }),
              (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
            ] });
          }
        }
      ),
      (0, import_jsx_runtime.jsx)(FormExtensionZone, { fields, form })
    ] }) }),
    (0, import_jsx_runtime.jsx)(RouteDrawer.Footer, { children: (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center justify-end gap-x-2", children: [
      (0, import_jsx_runtime.jsx)(RouteDrawer.Close, { asChild: true, children: (0, import_jsx_runtime.jsx)(Button, { size: "small", variant: "secondary", children: t("actions.cancel") }) }),
      (0, import_jsx_runtime.jsx)(Button, { size: "small", type: "submit", isLoading: isPending, children: t("actions.save") })
    ] }) })
  ] }) });
};
var ProductOrganization = () => {
  const { id } = useParams();
  const { t } = useTranslation();
  const { product, isLoading, isError, error } = useProduct(id, {
    fields: PRODUCT_DETAIL_FIELDS
  });
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime2.jsxs)(RouteDrawer, { children: [
    (0, import_jsx_runtime2.jsx)(RouteDrawer.Header, { children: (0, import_jsx_runtime2.jsx)(RouteDrawer.Title, { asChild: true, children: (0, import_jsx_runtime2.jsx)(Heading, { children: t("products.organization.edit.header") }) }) }),
    !isLoading && product && (0, import_jsx_runtime2.jsx)(ProductOrganizationForm, { product })
  ] });
};
export {
  ProductOrganization as Component
};
//# sourceMappingURL=product-organization-YZNTMITF-PKEVBKLJ.js.map
