{"version": 3, "sources": ["../../@medusajs/dashboard/dist/customer-group-create-7X4TZR54.mjs"], "sourcesContent": ["import \"./chunk-IUCDCPJU.mjs\";\nimport {\n  KeyboundForm\n} from \"./chunk-6HTZNHPT.mjs\";\nimport {\n  RouteFocusModal,\n  useRouteModal\n} from \"./chunk-4TC5YS65.mjs\";\nimport {\n  Form\n} from \"./chunk-OBQI23QM.mjs\";\nimport {\n  useCreateCustomerGroup\n} from \"./chunk-F6PXCY3N.mjs\";\nimport \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-RPUOO7AV.mjs\";\n\n// src/routes/customer-groups/customer-group-create/components/create-customer-group-form/create-customer-group-form.tsx\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { Button, Heading, Input, Text, toast } from \"@medusajs/ui\";\nimport { useForm } from \"react-hook-form\";\nimport { useTranslation } from \"react-i18next\";\nimport * as zod from \"zod\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar CreateCustomerGroupSchema = zod.object({\n  name: zod.string().min(1)\n});\nvar CreateCustomerGroupForm = () => {\n  const { t } = useTranslation();\n  const { handleSuccess } = useRouteModal();\n  const form = useForm({\n    defaultValues: {\n      name: \"\"\n    },\n    resolver: zodResolver(CreateCustomerGroupSchema)\n  });\n  const { mutateAsync, isPending } = useCreateCustomerGroup();\n  const handleSubmit = form.handleSubmit(async (data) => {\n    await mutateAsync(\n      {\n        name: data.name\n      },\n      {\n        onSuccess: ({ customer_group }) => {\n          toast.success(\n            t(\"customerGroups.create.successToast\", {\n              name: customer_group.name\n            })\n          );\n          handleSuccess(`/customer-groups/${customer_group.id}`);\n        },\n        onError: (error) => {\n          toast.error(error.message);\n        }\n      }\n    );\n  });\n  return /* @__PURE__ */ jsx(RouteFocusModal.Form, { form, children: /* @__PURE__ */ jsxs(\n    KeyboundForm,\n    {\n      className: \"flex h-full flex-col overflow-hidden\",\n      onSubmit: handleSubmit,\n      children: [\n        /* @__PURE__ */ jsx(RouteFocusModal.Header, {}),\n        /* @__PURE__ */ jsx(RouteFocusModal.Body, { className: \"flex flex-col items-center pt-[72px]\", children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex size-full max-w-[720px] flex-col gap-y-8\", children: [\n          /* @__PURE__ */ jsxs(\"div\", { children: [\n            /* @__PURE__ */ jsx(RouteFocusModal.Title, { asChild: true, children: /* @__PURE__ */ jsx(Heading, { children: t(\"customerGroups.create.header\") }) }),\n            /* @__PURE__ */ jsx(RouteFocusModal.Description, { asChild: true, children: /* @__PURE__ */ jsx(Text, { size: \"small\", className: \"text-ui-fg-subtle\", children: t(\"customerGroups.create.hint\") }) })\n          ] }),\n          /* @__PURE__ */ jsx(\"div\", { className: \"grid grid-cols-2 gap-4\", children: /* @__PURE__ */ jsx(\n            Form.Field,\n            {\n              control: form.control,\n              name: \"name\",\n              render: ({ field }) => {\n                return /* @__PURE__ */ jsxs(Form.Item, { children: [\n                  /* @__PURE__ */ jsx(Form.Label, { children: t(\"fields.name\") }),\n                  /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(Input, { ...field }) }),\n                  /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n                ] });\n              }\n            }\n          ) })\n        ] }) }),\n        /* @__PURE__ */ jsxs(RouteFocusModal.Footer, { children: [\n          /* @__PURE__ */ jsx(RouteFocusModal.Close, { asChild: true, children: /* @__PURE__ */ jsx(Button, { variant: \"secondary\", size: \"small\", children: t(\"actions.cancel\") }) }),\n          /* @__PURE__ */ jsx(\n            Button,\n            {\n              type: \"submit\",\n              variant: \"primary\",\n              size: \"small\",\n              isLoading: isPending,\n              children: t(\"actions.create\")\n            }\n          )\n        ] })\n      ]\n    }\n  ) });\n};\n\n// src/routes/customer-groups/customer-group-create/customer-group-create.tsx\nimport { jsx as jsx2 } from \"react/jsx-runtime\";\nvar CustomerGroupCreate = () => {\n  return /* @__PURE__ */ jsx2(RouteFocusModal, { children: /* @__PURE__ */ jsx2(CreateCustomerGroupForm, {}) });\n};\nexport {\n  CustomerGroupCreate as Component\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyBA,yBAA0B;AAgF1B,IAAAA,sBAA4B;AA/E5B,IAAI,4BAAgC,WAAO;AAAA,EACzC,MAAU,WAAO,EAAE,IAAI,CAAC;AAC1B,CAAC;AACD,IAAI,0BAA0B,MAAM;AAClC,QAAM,EAAE,GAAAC,GAAE,IAAI,eAAe;AAC7B,QAAM,EAAE,cAAc,IAAI,cAAc;AACxC,QAAM,OAAO,QAAQ;AAAA,IACnB,eAAe;AAAA,MACb,MAAM;AAAA,IACR;AAAA,IACA,UAAU,EAAY,yBAAyB;AAAA,EACjD,CAAC;AACD,QAAM,EAAE,aAAa,UAAU,IAAI,uBAAuB;AAC1D,QAAM,eAAe,KAAK,aAAa,OAAO,SAAS;AACrD,UAAM;AAAA,MACJ;AAAA,QACE,MAAM,KAAK;AAAA,MACb;AAAA,MACA;AAAA,QACE,WAAW,CAAC,EAAE,eAAe,MAAM;AACjC,gBAAM;AAAA,YACJA,GAAE,sCAAsC;AAAA,cACtC,MAAM,eAAe;AAAA,YACvB,CAAC;AAAA,UACH;AACA,wBAAc,oBAAoB,eAAe,EAAE,EAAE;AAAA,QACvD;AAAA,QACA,SAAS,CAAC,UAAU;AAClB,gBAAM,MAAM,MAAM,OAAO;AAAA,QAC3B;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AACD,aAAuB,wBAAI,gBAAgB,MAAM,EAAE,MAAM,cAA0B;AAAA,IACjF;AAAA,IACA;AAAA,MACE,WAAW;AAAA,MACX,UAAU;AAAA,MACV,UAAU;AAAA,YACQ,wBAAI,gBAAgB,QAAQ,CAAC,CAAC;AAAA,YAC9B,wBAAI,gBAAgB,MAAM,EAAE,WAAW,wCAAwC,cAA0B,yBAAK,OAAO,EAAE,WAAW,iDAAiD,UAAU;AAAA,cAC3L,yBAAK,OAAO,EAAE,UAAU;AAAA,gBACtB,wBAAI,gBAAgB,OAAO,EAAE,SAAS,MAAM,cAA0B,wBAAI,SAAS,EAAE,UAAUA,GAAE,8BAA8B,EAAE,CAAC,EAAE,CAAC;AAAA,gBACrI,wBAAI,gBAAgB,aAAa,EAAE,SAAS,MAAM,cAA0B,wBAAI,MAAM,EAAE,MAAM,SAAS,WAAW,qBAAqB,UAAUA,GAAE,4BAA4B,EAAE,CAAC,EAAE,CAAC;AAAA,UACvM,EAAE,CAAC;AAAA,cACa,wBAAI,OAAO,EAAE,WAAW,0BAA0B,cAA0B;AAAA,YAC1F,KAAK;AAAA,YACL;AAAA,cACE,SAAS,KAAK;AAAA,cACd,MAAM;AAAA,cACN,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,2BAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,sBACjC,wBAAI,KAAK,OAAO,EAAE,UAAUA,GAAE,aAAa,EAAE,CAAC;AAAA,sBAC9C,wBAAI,KAAK,SAAS,EAAE,cAA0B,wBAAI,OAAO,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC;AAAA,sBACxE,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,gBAC3C,EAAE,CAAC;AAAA,cACL;AAAA,YACF;AAAA,UACF,EAAE,CAAC;AAAA,QACL,EAAE,CAAC,EAAE,CAAC;AAAA,YACU,yBAAK,gBAAgB,QAAQ,EAAE,UAAU;AAAA,cACvC,wBAAI,gBAAgB,OAAO,EAAE,SAAS,MAAM,cAA0B,wBAAI,QAAQ,EAAE,SAAS,aAAa,MAAM,SAAS,UAAUA,GAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;AAAA,cAC3J;AAAA,YACd;AAAA,YACA;AAAA,cACE,MAAM;AAAA,cACN,SAAS;AAAA,cACT,MAAM;AAAA,cACN,WAAW;AAAA,cACX,UAAUA,GAAE,gBAAgB;AAAA,YAC9B;AAAA,UACF;AAAA,QACF,EAAE,CAAC;AAAA,MACL;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;AAIA,IAAI,sBAAsB,MAAM;AAC9B,aAAuB,oBAAAC,KAAK,iBAAiB,EAAE,cAA0B,oBAAAA,KAAK,yBAAyB,CAAC,CAAC,EAAE,CAAC;AAC9G;", "names": ["import_jsx_runtime", "t", "jsx2"]}