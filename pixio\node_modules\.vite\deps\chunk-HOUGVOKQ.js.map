{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-IM74HYR5.mjs"], "sourcesContent": ["import {\n  DataGridCellContainer,\n  useCombinedRefs,\n  useDataGridCell,\n  useDataGridCellError\n} from \"./chunk-53RYGJCD.mjs\";\nimport {\n  ConditionalTooltip\n} from \"./chunk-OC7BQLYI.mjs\";\n\n// src/components/data-grid/components/data-grid-toggleable-number-cell.tsx\nimport { Switch } from \"@medusajs/ui\";\nimport { useEffect, useRef, useState } from \"react\";\nimport CurrencyInput from \"react-currency-input-field\";\nimport { Controller } from \"react-hook-form\";\nimport { jsx } from \"react/jsx-runtime\";\nvar DataGridTogglableNumberCell = ({\n  context,\n  disabledToggleTooltip,\n  ...rest\n}) => {\n  const { field, control, renderProps } = useDataGridCell({\n    context\n  });\n  const errorProps = useDataGridCellError({ context });\n  const { container, input } = renderProps;\n  return /* @__PURE__ */ jsx(\n    Controller,\n    {\n      control,\n      name: field,\n      render: ({ field: field2 }) => {\n        return /* @__PURE__ */ jsx(\n          DataGridCellContainer,\n          {\n            ...container,\n            ...errorProps,\n            outerComponent: /* @__PURE__ */ jsx(\n              OuterComponent,\n              {\n                field: field2,\n                inputProps: input,\n                isAnchor: container.isAnchor,\n                tooltip: disabledToggleTooltip\n              }\n            ),\n            children: /* @__PURE__ */ jsx(Inner, { field: field2, inputProps: input, ...rest })\n          }\n        );\n      }\n    }\n  );\n};\nvar OuterComponent = ({\n  field,\n  inputProps,\n  isAnchor,\n  tooltip\n}) => {\n  const buttonRef = useRef(null);\n  const { value } = field;\n  const { onChange } = inputProps;\n  const [localValue, setLocalValue] = useState(value);\n  useEffect(() => {\n    setLocalValue(value);\n  }, [value]);\n  const handleCheckedChange = (update) => {\n    const newValue = { ...localValue, checked: update };\n    if (!update && !newValue.disabledToggle) {\n      newValue.quantity = \"\";\n    }\n    if (update && newValue.quantity === \"\") {\n      newValue.quantity = 0;\n    }\n    setLocalValue(newValue);\n    onChange(newValue, value);\n  };\n  useEffect(() => {\n    const handleKeyDown = (e) => {\n      if (isAnchor && e.key.toLowerCase() === \"x\") {\n        e.preventDefault();\n        buttonRef.current?.click();\n      }\n    };\n    document.addEventListener(\"keydown\", handleKeyDown);\n    return () => document.removeEventListener(\"keydown\", handleKeyDown);\n  }, [isAnchor]);\n  return /* @__PURE__ */ jsx(\n    ConditionalTooltip,\n    {\n      showTooltip: localValue.disabledToggle && tooltip,\n      content: tooltip,\n      children: /* @__PURE__ */ jsx(\"div\", { className: \"absolute inset-y-0 left-4 z-[3] flex w-fit items-center justify-center\", children: /* @__PURE__ */ jsx(\n        Switch,\n        {\n          ref: buttonRef,\n          size: \"small\",\n          className: \"shrink-0\",\n          checked: localValue.checked,\n          disabled: localValue.disabledToggle,\n          onCheckedChange: handleCheckedChange\n        }\n      ) })\n    }\n  );\n};\nvar Inner = ({\n  field,\n  inputProps,\n  placeholder,\n  ...props\n}) => {\n  const { ref, value, onChange: _, onBlur, ...fieldProps } = field;\n  const {\n    ref: inputRef,\n    onChange,\n    onBlur: onInputBlur,\n    onFocus,\n    ...attributes\n  } = inputProps;\n  const [localValue, setLocalValue] = useState(value);\n  useEffect(() => {\n    setLocalValue(value);\n  }, [value]);\n  const combinedRefs = useCombinedRefs(inputRef, ref);\n  const handleInputChange = (updatedValue, _name, _values) => {\n    const ensuredValue = updatedValue !== void 0 ? updatedValue : \"\";\n    const newValue = { ...localValue, quantity: ensuredValue };\n    if (ensuredValue !== \"\") {\n      newValue.checked = true;\n    } else if (newValue.checked && newValue.disabledToggle === false) {\n      newValue.checked = false;\n    }\n    setLocalValue(newValue);\n  };\n  const handleOnChange = () => {\n    if (localValue.disabledToggle && localValue.quantity === \"\") {\n      localValue.quantity = 0;\n    }\n    onChange(localValue, value);\n  };\n  return /* @__PURE__ */ jsx(\"div\", { className: \"flex size-full items-center gap-x-2\", children: /* @__PURE__ */ jsx(\n    CurrencyInput,\n    {\n      ...fieldProps,\n      ...attributes,\n      ...props,\n      ref: combinedRefs,\n      className: \"txt-compact-small w-full flex-1 cursor-default appearance-none bg-transparent pl-8 text-right outline-none\",\n      value: localValue?.quantity,\n      onValueChange: handleInputChange,\n      formatValueOnBlur: true,\n      onBlur: () => {\n        onBlur();\n        onInputBlur();\n        handleOnChange();\n      },\n      onFocus,\n      decimalsLimit: 0,\n      autoComplete: \"off\",\n      tabIndex: -1,\n      placeholder: !localValue.checked ? placeholder : void 0\n    }\n  ) });\n};\n\nexport {\n  DataGridTogglableNumberCell\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAYA,mBAA4C;AAG5C,yBAAoB;AACpB,IAAI,8BAA8B,CAAC;AAAA,EACjC;AAAA,EACA;AAAA,EACA,GAAG;AACL,MAAM;AACJ,QAAM,EAAE,OAAO,SAAS,YAAY,IAAI,gBAAgB;AAAA,IACtD;AAAA,EACF,CAAC;AACD,QAAM,aAAa,qBAAqB,EAAE,QAAQ,CAAC;AACnD,QAAM,EAAE,WAAW,MAAM,IAAI;AAC7B,aAAuB;AAAA,IACrB;AAAA,IACA;AAAA,MACE;AAAA,MACA,MAAM;AAAA,MACN,QAAQ,CAAC,EAAE,OAAO,OAAO,MAAM;AAC7B,mBAAuB;AAAA,UACrB;AAAA,UACA;AAAA,YACE,GAAG;AAAA,YACH,GAAG;AAAA,YACH,oBAAgC;AAAA,cAC9B;AAAA,cACA;AAAA,gBACE,OAAO;AAAA,gBACP,YAAY;AAAA,gBACZ,UAAU,UAAU;AAAA,gBACpB,SAAS;AAAA,cACX;AAAA,YACF;AAAA,YACA,cAA0B,wBAAI,OAAO,EAAE,OAAO,QAAQ,YAAY,OAAO,GAAG,KAAK,CAAC;AAAA,UACpF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAI,iBAAiB,CAAC;AAAA,EACpB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACJ,QAAM,gBAAY,qBAAO,IAAI;AAC7B,QAAM,EAAE,MAAM,IAAI;AAClB,QAAM,EAAE,SAAS,IAAI;AACrB,QAAM,CAAC,YAAY,aAAa,QAAI,uBAAS,KAAK;AAClD,8BAAU,MAAM;AACd,kBAAc,KAAK;AAAA,EACrB,GAAG,CAAC,KAAK,CAAC;AACV,QAAM,sBAAsB,CAAC,WAAW;AACtC,UAAM,WAAW,EAAE,GAAG,YAAY,SAAS,OAAO;AAClD,QAAI,CAAC,UAAU,CAAC,SAAS,gBAAgB;AACvC,eAAS,WAAW;AAAA,IACtB;AACA,QAAI,UAAU,SAAS,aAAa,IAAI;AACtC,eAAS,WAAW;AAAA,IACtB;AACA,kBAAc,QAAQ;AACtB,aAAS,UAAU,KAAK;AAAA,EAC1B;AACA,8BAAU,MAAM;AACd,UAAM,gBAAgB,CAAC,MAAM;AA9EjC;AA+EM,UAAI,YAAY,EAAE,IAAI,YAAY,MAAM,KAAK;AAC3C,UAAE,eAAe;AACjB,wBAAU,YAAV,mBAAmB;AAAA,MACrB;AAAA,IACF;AACA,aAAS,iBAAiB,WAAW,aAAa;AAClD,WAAO,MAAM,SAAS,oBAAoB,WAAW,aAAa;AAAA,EACpE,GAAG,CAAC,QAAQ,CAAC;AACb,aAAuB;AAAA,IACrB;AAAA,IACA;AAAA,MACE,aAAa,WAAW,kBAAkB;AAAA,MAC1C,SAAS;AAAA,MACT,cAA0B,wBAAI,OAAO,EAAE,WAAW,0EAA0E,cAA0B;AAAA,QACpJ;AAAA,QACA;AAAA,UACE,KAAK;AAAA,UACL,MAAM;AAAA,UACN,WAAW;AAAA,UACX,SAAS,WAAW;AAAA,UACpB,UAAU,WAAW;AAAA,UACrB,iBAAiB;AAAA,QACnB;AAAA,MACF,EAAE,CAAC;AAAA,IACL;AAAA,EACF;AACF;AACA,IAAI,QAAQ,CAAC;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA,GAAG;AACL,MAAM;AACJ,QAAM,EAAE,KAAK,OAAO,UAAU,GAAG,QAAQ,GAAG,WAAW,IAAI;AAC3D,QAAM;AAAA,IACJ,KAAK;AAAA,IACL;AAAA,IACA,QAAQ;AAAA,IACR;AAAA,IACA,GAAG;AAAA,EACL,IAAI;AACJ,QAAM,CAAC,YAAY,aAAa,QAAI,uBAAS,KAAK;AAClD,8BAAU,MAAM;AACd,kBAAc,KAAK;AAAA,EACrB,GAAG,CAAC,KAAK,CAAC;AACV,QAAM,eAAe,gBAAgB,UAAU,GAAG;AAClD,QAAM,oBAAoB,CAAC,cAAc,OAAO,YAAY;AAC1D,UAAM,eAAe,iBAAiB,SAAS,eAAe;AAC9D,UAAM,WAAW,EAAE,GAAG,YAAY,UAAU,aAAa;AACzD,QAAI,iBAAiB,IAAI;AACvB,eAAS,UAAU;AAAA,IACrB,WAAW,SAAS,WAAW,SAAS,mBAAmB,OAAO;AAChE,eAAS,UAAU;AAAA,IACrB;AACA,kBAAc,QAAQ;AAAA,EACxB;AACA,QAAM,iBAAiB,MAAM;AAC3B,QAAI,WAAW,kBAAkB,WAAW,aAAa,IAAI;AAC3D,iBAAW,WAAW;AAAA,IACxB;AACA,aAAS,YAAY,KAAK;AAAA,EAC5B;AACA,aAAuB,wBAAI,OAAO,EAAE,WAAW,uCAAuC,cAA0B;AAAA,IAC9G;AAAA,IACA;AAAA,MACE,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,MACH,KAAK;AAAA,MACL,WAAW;AAAA,MACX,OAAO,yCAAY;AAAA,MACnB,eAAe;AAAA,MACf,mBAAmB;AAAA,MACnB,QAAQ,MAAM;AACZ,eAAO;AACP,oBAAY;AACZ,uBAAe;AAAA,MACjB;AAAA,MACA;AAAA,MACA,eAAe;AAAA,MACf,cAAc;AAAA,MACd,UAAU;AAAA,MACV,aAAa,CAAC,WAAW,UAAU,cAAc;AAAA,IACnD;AAAA,EACF,EAAE,CAAC;AACL;", "names": []}