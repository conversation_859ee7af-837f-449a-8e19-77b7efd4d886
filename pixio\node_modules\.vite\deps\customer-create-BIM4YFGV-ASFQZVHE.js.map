{"version": 3, "sources": ["../../@medusajs/dashboard/dist/customer-create-BIM4YFGV.mjs"], "sourcesContent": ["import \"./chunk-IUCDCPJU.mjs\";\nimport {\n  KeyboundForm\n} from \"./chunk-6HTZNHPT.mjs\";\nimport {\n  RouteFocusModal,\n  useRouteModal\n} from \"./chunk-4TC5YS65.mjs\";\nimport {\n  Form\n} from \"./chunk-OBQI23QM.mjs\";\nimport {\n  useCreateCustomer\n} from \"./chunk-F6PXCY3N.mjs\";\nimport \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-RPUOO7AV.mjs\";\n\n// src/routes/customers/customer-create/components/create-customer-form/create-customer-form.tsx\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { Button, Heading, Input, Text, toast } from \"@medusajs/ui\";\nimport { useForm } from \"react-hook-form\";\nimport { useTranslation } from \"react-i18next\";\nimport * as zod from \"zod\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar CreateCustomerSchema = zod.object({\n  email: zod.string().email(),\n  first_name: zod.string().optional(),\n  last_name: zod.string().optional(),\n  company_name: zod.string().optional(),\n  phone: zod.string().optional()\n});\nvar CreateCustomerForm = () => {\n  const { t } = useTranslation();\n  const { handleSuccess } = useRouteModal();\n  const { mutateAsync, isPending } = useCreateCustomer();\n  const form = useForm({\n    defaultValues: {\n      email: \"\",\n      first_name: \"\",\n      last_name: \"\",\n      phone: \"\",\n      company_name: \"\"\n    },\n    resolver: zodResolver(CreateCustomerSchema)\n  });\n  const handleSubmit = form.handleSubmit(async (data) => {\n    await mutateAsync(\n      {\n        email: data.email,\n        first_name: data.first_name || void 0,\n        last_name: data.last_name || void 0,\n        company_name: data.company_name || void 0,\n        phone: data.phone || void 0\n      },\n      {\n        onSuccess: ({ customer }) => {\n          toast.success(\n            t(\"customers.create.successToast\", {\n              email: customer.email\n            })\n          );\n          handleSuccess(`/customers/${customer.id}`);\n        },\n        onError: (error) => {\n          toast.error(error.message);\n        }\n      }\n    );\n  });\n  return /* @__PURE__ */ jsx(RouteFocusModal.Form, { form, children: /* @__PURE__ */ jsxs(\n    KeyboundForm,\n    {\n      onSubmit: handleSubmit,\n      className: \"flex flex-1 flex-col overflow-hidden\",\n      children: [\n        /* @__PURE__ */ jsx(RouteFocusModal.Header, {}),\n        /* @__PURE__ */ jsx(RouteFocusModal.Body, { className: \"flex flex-1 flex-col items-center overflow-y-auto py-16\", children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex w-full max-w-[720px] flex-col gap-y-8\", children: [\n          /* @__PURE__ */ jsxs(\"div\", { children: [\n            /* @__PURE__ */ jsx(Heading, { children: t(\"customers.create.header\") }),\n            /* @__PURE__ */ jsx(Text, { size: \"small\", className: \"text-ui-fg-subtle\", children: t(\"customers.create.hint\") })\n          ] }),\n          /* @__PURE__ */ jsxs(\"div\", { className: \"grid grid-cols-1 gap-4 md:grid-cols-2\", children: [\n            /* @__PURE__ */ jsx(\n              Form.Field,\n              {\n                control: form.control,\n                name: \"first_name\",\n                render: ({ field }) => {\n                  return /* @__PURE__ */ jsxs(Form.Item, { children: [\n                    /* @__PURE__ */ jsx(Form.Label, { optional: true, children: t(\"fields.firstName\") }),\n                    /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(Input, { autoComplete: \"off\", ...field }) }),\n                    /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n                  ] });\n                }\n              }\n            ),\n            /* @__PURE__ */ jsx(\n              Form.Field,\n              {\n                control: form.control,\n                name: \"last_name\",\n                render: ({ field }) => {\n                  return /* @__PURE__ */ jsxs(Form.Item, { children: [\n                    /* @__PURE__ */ jsx(Form.Label, { optional: true, children: t(\"fields.lastName\") }),\n                    /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(Input, { autoComplete: \"off\", ...field }) }),\n                    /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n                  ] });\n                }\n              }\n            ),\n            /* @__PURE__ */ jsx(\n              Form.Field,\n              {\n                control: form.control,\n                name: \"email\",\n                render: ({ field }) => {\n                  return /* @__PURE__ */ jsxs(Form.Item, { children: [\n                    /* @__PURE__ */ jsx(Form.Label, { children: t(\"fields.email\") }),\n                    /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(Input, { autoComplete: \"off\", ...field }) }),\n                    /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n                  ] });\n                }\n              }\n            ),\n            /* @__PURE__ */ jsx(\n              Form.Field,\n              {\n                control: form.control,\n                name: \"company_name\",\n                render: ({ field }) => {\n                  return /* @__PURE__ */ jsxs(Form.Item, { children: [\n                    /* @__PURE__ */ jsx(Form.Label, { optional: true, children: t(\"fields.company\") }),\n                    /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(Input, { autoComplete: \"off\", ...field }) }),\n                    /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n                  ] });\n                }\n              }\n            ),\n            /* @__PURE__ */ jsx(\n              Form.Field,\n              {\n                control: form.control,\n                name: \"phone\",\n                render: ({ field }) => {\n                  return /* @__PURE__ */ jsxs(Form.Item, { children: [\n                    /* @__PURE__ */ jsx(Form.Label, { optional: true, children: t(\"fields.phone\") }),\n                    /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(Input, { autoComplete: \"off\", ...field }) }),\n                    /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n                  ] });\n                }\n              }\n            )\n          ] })\n        ] }) }),\n        /* @__PURE__ */ jsx(RouteFocusModal.Footer, { children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center justify-end gap-x-2\", children: [\n          /* @__PURE__ */ jsx(RouteFocusModal.Close, { asChild: true, children: /* @__PURE__ */ jsx(Button, { size: \"small\", variant: \"secondary\", children: t(\"actions.cancel\") }) }),\n          /* @__PURE__ */ jsx(\n            Button,\n            {\n              size: \"small\",\n              variant: \"primary\",\n              type: \"submit\",\n              isLoading: isPending,\n              children: t(\"actions.create\")\n            }\n          )\n        ] }) })\n      ]\n    }\n  ) });\n};\n\n// src/routes/customers/customer-create/customer-create.tsx\nimport { jsx as jsx2 } from \"react/jsx-runtime\";\nvar CustomerCreate = () => {\n  return /* @__PURE__ */ jsx2(RouteFocusModal, { children: /* @__PURE__ */ jsx2(CreateCustomerForm, {}) });\n};\nexport {\n  CustomerCreate as Component\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyBA,yBAA0B;AAsJ1B,IAAAA,sBAA4B;AArJ5B,IAAI,uBAA2B,WAAO;AAAA,EACpC,OAAW,WAAO,EAAE,MAAM;AAAA,EAC1B,YAAgB,WAAO,EAAE,SAAS;AAAA,EAClC,WAAe,WAAO,EAAE,SAAS;AAAA,EACjC,cAAkB,WAAO,EAAE,SAAS;AAAA,EACpC,OAAW,WAAO,EAAE,SAAS;AAC/B,CAAC;AACD,IAAI,qBAAqB,MAAM;AAC7B,QAAM,EAAE,GAAAC,GAAE,IAAI,eAAe;AAC7B,QAAM,EAAE,cAAc,IAAI,cAAc;AACxC,QAAM,EAAE,aAAa,UAAU,IAAI,kBAAkB;AACrD,QAAM,OAAO,QAAQ;AAAA,IACnB,eAAe;AAAA,MACb,OAAO;AAAA,MACP,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,OAAO;AAAA,MACP,cAAc;AAAA,IAChB;AAAA,IACA,UAAU,EAAY,oBAAoB;AAAA,EAC5C,CAAC;AACD,QAAM,eAAe,KAAK,aAAa,OAAO,SAAS;AACrD,UAAM;AAAA,MACJ;AAAA,QACE,OAAO,KAAK;AAAA,QACZ,YAAY,KAAK,cAAc;AAAA,QAC/B,WAAW,KAAK,aAAa;AAAA,QAC7B,cAAc,KAAK,gBAAgB;AAAA,QACnC,OAAO,KAAK,SAAS;AAAA,MACvB;AAAA,MACA;AAAA,QACE,WAAW,CAAC,EAAE,SAAS,MAAM;AAC3B,gBAAM;AAAA,YACJA,GAAE,iCAAiC;AAAA,cACjC,OAAO,SAAS;AAAA,YAClB,CAAC;AAAA,UACH;AACA,wBAAc,cAAc,SAAS,EAAE,EAAE;AAAA,QAC3C;AAAA,QACA,SAAS,CAAC,UAAU;AAClB,gBAAM,MAAM,MAAM,OAAO;AAAA,QAC3B;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AACD,aAAuB,wBAAI,gBAAgB,MAAM,EAAE,MAAM,cAA0B;AAAA,IACjF;AAAA,IACA;AAAA,MACE,UAAU;AAAA,MACV,WAAW;AAAA,MACX,UAAU;AAAA,YACQ,wBAAI,gBAAgB,QAAQ,CAAC,CAAC;AAAA,YAC9B,wBAAI,gBAAgB,MAAM,EAAE,WAAW,2DAA2D,cAA0B,yBAAK,OAAO,EAAE,WAAW,8CAA8C,UAAU;AAAA,cAC3M,yBAAK,OAAO,EAAE,UAAU;AAAA,gBACtB,wBAAI,SAAS,EAAE,UAAUA,GAAE,yBAAyB,EAAE,CAAC;AAAA,gBACvD,wBAAI,MAAM,EAAE,MAAM,SAAS,WAAW,qBAAqB,UAAUA,GAAE,uBAAuB,EAAE,CAAC;AAAA,UACnH,EAAE,CAAC;AAAA,cACa,yBAAK,OAAO,EAAE,WAAW,yCAAyC,UAAU;AAAA,gBAC1E;AAAA,cACd,KAAK;AAAA,cACL;AAAA,gBACE,SAAS,KAAK;AAAA,gBACd,MAAM;AAAA,gBACN,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,6BAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,wBACjC,wBAAI,KAAK,OAAO,EAAE,UAAU,MAAM,UAAUA,GAAE,kBAAkB,EAAE,CAAC;AAAA,wBACnE,wBAAI,KAAK,SAAS,EAAE,cAA0B,wBAAI,OAAO,EAAE,cAAc,OAAO,GAAG,MAAM,CAAC,EAAE,CAAC;AAAA,wBAC7F,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,kBAC3C,EAAE,CAAC;AAAA,gBACL;AAAA,cACF;AAAA,YACF;AAAA,gBACgB;AAAA,cACd,KAAK;AAAA,cACL;AAAA,gBACE,SAAS,KAAK;AAAA,gBACd,MAAM;AAAA,gBACN,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,6BAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,wBACjC,wBAAI,KAAK,OAAO,EAAE,UAAU,MAAM,UAAUA,GAAE,iBAAiB,EAAE,CAAC;AAAA,wBAClE,wBAAI,KAAK,SAAS,EAAE,cAA0B,wBAAI,OAAO,EAAE,cAAc,OAAO,GAAG,MAAM,CAAC,EAAE,CAAC;AAAA,wBAC7F,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,kBAC3C,EAAE,CAAC;AAAA,gBACL;AAAA,cACF;AAAA,YACF;AAAA,gBACgB;AAAA,cACd,KAAK;AAAA,cACL;AAAA,gBACE,SAAS,KAAK;AAAA,gBACd,MAAM;AAAA,gBACN,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,6BAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,wBACjC,wBAAI,KAAK,OAAO,EAAE,UAAUA,GAAE,cAAc,EAAE,CAAC;AAAA,wBAC/C,wBAAI,KAAK,SAAS,EAAE,cAA0B,wBAAI,OAAO,EAAE,cAAc,OAAO,GAAG,MAAM,CAAC,EAAE,CAAC;AAAA,wBAC7F,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,kBAC3C,EAAE,CAAC;AAAA,gBACL;AAAA,cACF;AAAA,YACF;AAAA,gBACgB;AAAA,cACd,KAAK;AAAA,cACL;AAAA,gBACE,SAAS,KAAK;AAAA,gBACd,MAAM;AAAA,gBACN,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,6BAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,wBACjC,wBAAI,KAAK,OAAO,EAAE,UAAU,MAAM,UAAUA,GAAE,gBAAgB,EAAE,CAAC;AAAA,wBACjE,wBAAI,KAAK,SAAS,EAAE,cAA0B,wBAAI,OAAO,EAAE,cAAc,OAAO,GAAG,MAAM,CAAC,EAAE,CAAC;AAAA,wBAC7F,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,kBAC3C,EAAE,CAAC;AAAA,gBACL;AAAA,cACF;AAAA,YACF;AAAA,gBACgB;AAAA,cACd,KAAK;AAAA,cACL;AAAA,gBACE,SAAS,KAAK;AAAA,gBACd,MAAM;AAAA,gBACN,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,6BAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,wBACjC,wBAAI,KAAK,OAAO,EAAE,UAAU,MAAM,UAAUA,GAAE,cAAc,EAAE,CAAC;AAAA,wBAC/D,wBAAI,KAAK,SAAS,EAAE,cAA0B,wBAAI,OAAO,EAAE,cAAc,OAAO,GAAG,MAAM,CAAC,EAAE,CAAC;AAAA,wBAC7F,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,kBAC3C,EAAE,CAAC;AAAA,gBACL;AAAA,cACF;AAAA,YACF;AAAA,UACF,EAAE,CAAC;AAAA,QACL,EAAE,CAAC,EAAE,CAAC;AAAA,YACU,wBAAI,gBAAgB,QAAQ,EAAE,cAA0B,yBAAK,OAAO,EAAE,WAAW,yCAAyC,UAAU;AAAA,cAClI,wBAAI,gBAAgB,OAAO,EAAE,SAAS,MAAM,cAA0B,wBAAI,QAAQ,EAAE,MAAM,SAAS,SAAS,aAAa,UAAUA,GAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;AAAA,cAC3J;AAAA,YACd;AAAA,YACA;AAAA,cACE,MAAM;AAAA,cACN,SAAS;AAAA,cACT,MAAM;AAAA,cACN,WAAW;AAAA,cACX,UAAUA,GAAE,gBAAgB;AAAA,YAC9B;AAAA,UACF;AAAA,QACF,EAAE,CAAC,EAAE,CAAC;AAAA,MACR;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;AAIA,IAAI,iBAAiB,MAAM;AACzB,aAAuB,oBAAAC,KAAK,iBAAiB,EAAE,cAA0B,oBAAAA,KAAK,oBAAoB,CAAC,CAAC,EAAE,CAAC;AACzG;", "names": ["import_jsx_runtime", "t", "jsx2"]}