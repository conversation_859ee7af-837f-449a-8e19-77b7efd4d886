{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-XMAWMECC.mjs"], "sourcesContent": ["import {\n  useQueryParams\n} from \"./chunk-C76H5USB.mjs\";\n\n// src/hooks/table/query/use-order-table-query.tsx\nvar useOrderTableQuery = ({\n  prefix,\n  pageSize = 20\n}) => {\n  const queryObject = useQueryParams(\n    [\n      \"offset\",\n      \"q\",\n      \"created_at\",\n      \"updated_at\",\n      \"region_id\",\n      \"sales_channel_id\",\n      \"payment_status\",\n      \"fulfillment_status\",\n      \"order\"\n    ],\n    prefix\n  );\n  const {\n    offset,\n    sales_channel_id,\n    created_at,\n    updated_at,\n    fulfillment_status,\n    payment_status,\n    region_id,\n    q,\n    order\n  } = queryObject;\n  const searchParams = {\n    limit: pageSize,\n    offset: offset ? Number(offset) : 0,\n    sales_channel_id: sales_channel_id?.split(\",\"),\n    fulfillment_status: fulfillment_status?.split(\",\"),\n    payment_status: payment_status?.split(\",\"),\n    created_at: created_at ? JSON.parse(created_at) : void 0,\n    updated_at: updated_at ? JSON.parse(updated_at) : void 0,\n    region_id: region_id?.split(\",\"),\n    order: order ? order : \"-display_id\",\n    q\n  };\n  return {\n    searchParams,\n    raw: queryObject\n  };\n};\n\nexport {\n  useOrderTableQuery\n};\n"], "mappings": ";;;;;AAKA,IAAI,qBAAqB,CAAC;AAAA,EACxB;AAAA,EACA,WAAW;AACb,MAAM;AACJ,QAAM,cAAc;AAAA,IAClB;AAAA,MACE;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,IACA;AAAA,EACF;AACA,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,eAAe;AAAA,IACnB,OAAO;AAAA,IACP,QAAQ,SAAS,OAAO,MAAM,IAAI;AAAA,IAClC,kBAAkB,qDAAkB,MAAM;AAAA,IAC1C,oBAAoB,yDAAoB,MAAM;AAAA,IAC9C,gBAAgB,iDAAgB,MAAM;AAAA,IACtC,YAAY,aAAa,KAAK,MAAM,UAAU,IAAI;AAAA,IAClD,YAAY,aAAa,KAAK,MAAM,UAAU,IAAI;AAAA,IAClD,WAAW,uCAAW,MAAM;AAAA,IAC5B,OAAO,QAAQ,QAAQ;AAAA,IACvB;AAAA,EACF;AACA,SAAO;AAAA,IACL;AAAA,IACA,KAAK;AAAA,EACP;AACF;", "names": []}