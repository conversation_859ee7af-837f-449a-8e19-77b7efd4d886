{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-6WKBBTKM.mjs"], "sourcesContent": ["// src/components/common/link-button/link-button.tsx\nimport { clx } from \"@medusajs/ui\";\nimport { <PERSON> } from \"react-router-dom\";\nimport { jsx } from \"react/jsx-runtime\";\nvar LinkButton = ({\n  className,\n  variant = \"interactive\",\n  ...props\n}) => {\n  return /* @__PURE__ */ jsx(\n    Link,\n    {\n      className: clx(\n        \"transition-fg txt-compact-small-plus rounded-[4px] outline-none\",\n        \"focus-visible:shadow-borders-focus\",\n        {\n          \"text-ui-fg-interactive hover:text-ui-fg-interactive-hover\": variant === \"interactive\",\n          \"text-ui-fg-base hover:text-ui-fg-subtle\": variant === \"primary\"\n        },\n        className\n      ),\n      ...props\n    }\n  );\n};\n\nexport {\n  LinkButton\n};\n"], "mappings": ";;;;;;;;;;;;;;AAGA,yBAAoB;AACpB,IAAI,aAAa,CAAC;AAAA,EAChB;AAAA,EACA,UAAU;AAAA,EACV,GAAG;AACL,MAAM;AACJ,aAAuB;AAAA,IACrB;AAAA,IACA;AAAA,MACE,WAAW;AAAA,QACT;AAAA,QACA;AAAA,QACA;AAAA,UACE,6DAA6D,YAAY;AAAA,UACzE,2CAA2C,YAAY;AAAA,QACzD;AAAA,QACA;AAAA,MACF;AAAA,MACA,GAAG;AAAA,IACL;AAAA,EACF;AACF;", "names": []}