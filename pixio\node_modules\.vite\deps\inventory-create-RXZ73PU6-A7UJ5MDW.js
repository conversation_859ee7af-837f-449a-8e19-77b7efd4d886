import {
  transformNullableFormData,
  transformNullableFormNumber,
  transformNullableFormNumbers
} from "./chunk-6JFXN7BR.js";
import {
  CountrySelect
} from "./chunk-LULCCYRV.js";
import {
  DataGrid,
  createDataGridHelper
} from "./chunk-ZOK7LZDT.js";
import "./chunk-H3DTEG3J.js";
import {
  SwitchBox
} from "./chunk-4LGSZLHH.js";
import {
  optionalInt
} from "./chunk-7LOZU53L.js";
import "./chunk-EZLR4STK.js";
import "./chunk-EGRHWZRV.js";
import {
  KeyboundForm
} from "./chunk-DP54EP6X.js";
import "./chunk-7ANVLPZR.js";
import {
  RouteFocusModal,
  useRouteModal
} from "./chunk-MVVOBQIC.js";
import "./chunk-5QX4V4M4.js";
import "./chunk-HPGXK5DQ.js";
import {
  t
} from "./chunk-WHQIBI5S.js";
import {
  z
} from "./chunk-4XXECALA.js";
import "./chunk-YXT43UJF.js";
import "./chunk-66DVUN72.js";
import "./chunk-NV2N3EWM.js";
import "./chunk-MPXR7HT5.js";
import {
  Form,
  useForm
} from "./chunk-XXJU43CK.js";
import "./chunk-WISME5HP.js";
import "./chunk-WAUSOHFQ.js";
import "./chunk-3ORK3QZ4.js";
import "./chunk-3A5TVVNI.js";
import "./chunk-EZ62MM7J.js";
import "./chunk-3R6LYIXM.js";
import "./chunk-2P2O4TMQ.js";
import "./chunk-KI3HEITX.js";
import "./chunk-2VUCAO7L.js";
import "./chunk-PTIULTKD.js";
import "./chunk-KISXY2LD.js";
import "./chunk-L7C3XPHJ.js";
import "./chunk-T7MG2EIJ.js";
import "./chunk-MER4VYPR.js";
import "./chunk-2AWKOUCD.js";
import "./chunk-YL3TYBFR.js";
import "./chunk-TZWW72YW.js";
import "./chunk-MSQ25CWB.js";
import {
  useStockLocations
} from "./chunk-ONYSAQ5Z.js";
import "./chunk-NIH7SAXN.js";
import "./chunk-UEJFWAFE.js";
import "./chunk-GR55MFKT.js";
import "./chunk-AAFHKNJG.js";
import "./chunk-7XDBFDTZ.js";
import "./chunk-3JKGO5XL.js";
import "./chunk-OISUTS7G.js";
import {
  inventoryItemsQueryKeys,
  useCreateInventoryItem
} from "./chunk-AJYMIHLQ.js";
import {
  queryClient
} from "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import {
  sdk
} from "./chunk-JFCIYOH4.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-5GF3RGIE.js";
import "./chunk-T7YBVUWZ.js";
import {
  Button,
  Divider,
  Heading,
  Input,
  ProgressTabs,
  Textarea,
  clx,
  toast
} from "./chunk-YP2LLXWB.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-ZSQ362B4.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/inventory-create-RXZ73PU6.mjs
var import_react = __toESM(require_react(), 1);
var import_react2 = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var InventoryAvailabilityForm = ({
  form,
  locations
}) => {
  const { setCloseOnEscape } = useRouteModal();
  const columns = useColumns();
  return (0, import_jsx_runtime.jsx)("div", { className: "size-full", children: (0, import_jsx_runtime.jsx)(
    DataGrid,
    {
      columns,
      data: locations,
      state: form,
      onEditingChange: (editing) => setCloseOnEscape(!editing)
    }
  ) });
};
var columnHelper = createDataGridHelper();
var useColumns = () => {
  const { t: t2 } = useTranslation();
  return (0, import_react2.useMemo)(
    () => [
      columnHelper.column({
        id: "location",
        header: () => (0, import_jsx_runtime.jsx)("div", { className: "flex size-full items-center overflow-hidden", children: (0, import_jsx_runtime.jsx)("span", { className: "truncate", children: t2("locations.domain") }) }),
        cell: (context) => {
          return (0, import_jsx_runtime.jsx)(DataGrid.ReadonlyCell, { context, children: context.row.original.name });
        },
        disableHiding: true
      }),
      columnHelper.column({
        id: "in-stock",
        name: t2("fields.inStock"),
        header: t2("fields.inStock"),
        field: (context) => `locations.${context.row.original.id}`,
        type: "number",
        cell: (context) => {
          return (0, import_jsx_runtime.jsx)(DataGrid.NumberCell, { placeholder: "0", context });
        },
        disableHiding: true
      })
    ],
    [t2]
  );
};
var CreateInventoryItemSchema = z.object({
  title: z.string().min(1),
  description: z.string().optional(),
  sku: z.string().optional(),
  hs_code: z.string().optional(),
  weight: optionalInt,
  length: optionalInt,
  height: optionalInt,
  width: optionalInt,
  origin_country: z.string().optional(),
  mid_code: z.string().optional(),
  material: z.string().optional(),
  requires_shipping: z.boolean().optional(),
  thumbnail: z.string().optional(),
  locations: z.record(z.string(), optionalInt).optional()
});
function InventoryCreateForm({ locations }) {
  const { t: t2 } = useTranslation();
  const { handleSuccess } = useRouteModal();
  const [tab, setTab] = (0, import_react.useState)(
    "details"
    /* DETAILS */
  );
  const form = useForm({
    defaultValues: {
      title: "",
      sku: "",
      hs_code: "",
      weight: "",
      length: "",
      height: "",
      width: "",
      origin_country: "",
      mid_code: "",
      material: "",
      description: "",
      requires_shipping: true,
      thumbnail: "",
      locations: Object.fromEntries(
        locations.map((location) => [location.id, ""])
      )
    },
    resolver: t(CreateInventoryItemSchema)
  });
  const {
    trigger,
    formState: { isDirty }
  } = form;
  const { mutateAsync: createInventoryItem, isPending: isLoading } = useCreateInventoryItem();
  const handleSubmit = form.handleSubmit(async (data) => {
    const { locations: locations2, weight, length, height, width, ...payload } = data;
    const cleanData = transformNullableFormData(payload, false);
    const cleanNumbers = transformNullableFormNumbers(
      {
        weight,
        length,
        height,
        width
      },
      false
    );
    const { inventory_item } = await createInventoryItem(
      {
        ...cleanData,
        ...cleanNumbers
      },
      {
        onError: (e) => {
          toast.error(e.message);
          return;
        }
      }
    );
    await sdk.admin.inventoryItem.batchUpdateLevels(inventory_item.id, {
      create: Object.entries(locations2 ?? {}).filter(([_, quantiy]) => !!quantiy).map(([location_id, stocked_quantity]) => ({
        location_id,
        stocked_quantity: transformNullableFormNumber(
          stocked_quantity,
          false
        )
      }))
    }).then(async () => {
      await queryClient.invalidateQueries({
        queryKey: inventoryItemsQueryKeys.lists()
      });
    }).catch((e) => {
      toast.error(e.message);
    }).finally(() => {
      handleSuccess();
      toast.success(t2("inventory.create.successToast"));
    });
  });
  const [status, setStatus] = (0, import_react.useState)({
    [
      "availability"
      /* AVAILABILITY */
    ]: "not-started",
    [
      "details"
      /* DETAILS */
    ]: "not-started"
  });
  const onTabChange = (0, import_react.useCallback)(
    async (value) => {
      const result = await trigger();
      if (!result) {
        return;
      }
      setTab(value);
    },
    [trigger]
  );
  const onNext = (0, import_react.useCallback)(async () => {
    const result = await trigger();
    if (!result) {
      return;
    }
    switch (tab) {
      case "details": {
        setTab(
          "availability"
          /* AVAILABILITY */
        );
        break;
      }
      case "availability":
        break;
    }
  }, [tab, trigger]);
  (0, import_react.useEffect)(() => {
    if (isDirty) {
      setStatus((prev) => ({ ...prev, [
        "details"
        /* DETAILS */
      ]: "in-progress" }));
    } else {
      setStatus((prev) => ({ ...prev, [
        "details"
        /* DETAILS */
      ]: "not-started" }));
    }
  }, [isDirty]);
  (0, import_react.useEffect)(() => {
    if (tab === "details" && isDirty) {
      setStatus((prev) => ({ ...prev, [
        "details"
        /* DETAILS */
      ]: "in-progress" }));
    }
    if (tab === "availability") {
      setStatus((prev) => ({
        ...prev,
        [
          "details"
          /* DETAILS */
        ]: "completed",
        [
          "availability"
          /* AVAILABILITY */
        ]: "in-progress"
      }));
    }
  }, [tab, isDirty]);
  return (0, import_jsx_runtime2.jsx)(RouteFocusModal.Form, { form, children: (0, import_jsx_runtime2.jsx)(
    ProgressTabs,
    {
      value: tab,
      className: "h-full",
      onValueChange: (tab2) => onTabChange(tab2),
      children: (0, import_jsx_runtime2.jsxs)(
        KeyboundForm,
        {
          className: "flex h-full flex-col overflow-hidden",
          onSubmit: handleSubmit,
          children: [
            (0, import_jsx_runtime2.jsx)(RouteFocusModal.Header, { children: (0, import_jsx_runtime2.jsxs)(ProgressTabs.List, { className: "border-ui-border-base -my-2 ml-2 min-w-0 flex-1 border-l", children: [
              (0, import_jsx_runtime2.jsx)(
                ProgressTabs.Trigger,
                {
                  value: "details",
                  status: status[
                    "details"
                    /* DETAILS */
                  ],
                  className: "w-full max-w-[200px]",
                  children: (0, import_jsx_runtime2.jsx)("span", { className: "w-full cursor-auto overflow-hidden text-ellipsis whitespace-nowrap", children: t2("inventory.create.details") })
                }
              ),
              (0, import_jsx_runtime2.jsx)(
                ProgressTabs.Trigger,
                {
                  value: "availability",
                  className: "w-full max-w-[200px]",
                  status: status[
                    "availability"
                    /* AVAILABILITY */
                  ],
                  children: (0, import_jsx_runtime2.jsx)("span", { className: "w-full overflow-hidden text-ellipsis whitespace-nowrap", children: t2("inventory.create.availability") })
                }
              )
            ] }) }),
            (0, import_jsx_runtime2.jsxs)(
              RouteFocusModal.Body,
              {
                className: clx(
                  "flex h-full w-full flex-col items-center divide-y overflow-hidden",
                  {
                    "mx-auto": tab === "details"
                    /* DETAILS */
                  }
                ),
                children: [
                  (0, import_jsx_runtime2.jsx)(
                    ProgressTabs.Content,
                    {
                      value: "details",
                      className: "h-full w-full overflow-auto px-3",
                      children: (0, import_jsx_runtime2.jsxs)("div", { className: "mx-auto flex w-full max-w-[720px] flex-col gap-y-8 px-px py-16", children: [
                        (0, import_jsx_runtime2.jsxs)("div", { className: "flex flex-col gap-y-8", children: [
                          (0, import_jsx_runtime2.jsx)(Heading, { children: t2("inventory.create.title") }),
                          (0, import_jsx_runtime2.jsxs)("div", { className: "flex flex-col gap-y-6", children: [
                            (0, import_jsx_runtime2.jsxs)("div", { className: "grid grid-cols-1 gap-4 lg:grid-cols-2", children: [
                              (0, import_jsx_runtime2.jsx)(
                                Form.Field,
                                {
                                  control: form.control,
                                  name: "title",
                                  render: ({ field }) => {
                                    return (0, import_jsx_runtime2.jsxs)(Form.Item, { children: [
                                      (0, import_jsx_runtime2.jsx)(Form.Label, { children: t2("fields.title") }),
                                      (0, import_jsx_runtime2.jsx)(Form.Control, { children: (0, import_jsx_runtime2.jsx)(
                                        Input,
                                        {
                                          ...field,
                                          placeholder: t2("fields.title")
                                        }
                                      ) }),
                                      (0, import_jsx_runtime2.jsx)(Form.ErrorMessage, {})
                                    ] });
                                  }
                                }
                              ),
                              (0, import_jsx_runtime2.jsx)(
                                Form.Field,
                                {
                                  control: form.control,
                                  name: "sku",
                                  render: ({ field }) => {
                                    return (0, import_jsx_runtime2.jsxs)(Form.Item, { children: [
                                      (0, import_jsx_runtime2.jsx)(Form.Label, { children: t2("fields.sku") }),
                                      (0, import_jsx_runtime2.jsx)(Form.Control, { children: (0, import_jsx_runtime2.jsx)(Input, { ...field, placeholder: "sku-123" }) }),
                                      (0, import_jsx_runtime2.jsx)(Form.ErrorMessage, {})
                                    ] });
                                  }
                                }
                              )
                            ] }),
                            (0, import_jsx_runtime2.jsx)(
                              Form.Field,
                              {
                                control: form.control,
                                name: "description",
                                render: ({ field }) => {
                                  return (0, import_jsx_runtime2.jsxs)(Form.Item, { children: [
                                    (0, import_jsx_runtime2.jsx)(Form.Label, { optional: true, children: t2("products.fields.description.label") }),
                                    (0, import_jsx_runtime2.jsx)(Form.Control, { children: (0, import_jsx_runtime2.jsx)(
                                      Textarea,
                                      {
                                        ...field,
                                        placeholder: "The item description"
                                      }
                                    ) })
                                  ] });
                                }
                              }
                            )
                          ] }),
                          (0, import_jsx_runtime2.jsx)(
                            SwitchBox,
                            {
                              control: form.control,
                              name: "requires_shipping",
                              label: t2("inventory.create.requiresShipping"),
                              description: t2("inventory.create.requiresShippingHint")
                            }
                          )
                        ] }),
                        (0, import_jsx_runtime2.jsx)(Divider, {}),
                        (0, import_jsx_runtime2.jsxs)("div", { className: "flex flex-col gap-y-6", children: [
                          (0, import_jsx_runtime2.jsx)(Heading, { level: "h2", children: t2("inventory.create.attributes") }),
                          (0, import_jsx_runtime2.jsxs)("div", { className: "grid grid-cols-1 gap-x-4 gap-y-4 lg:grid-cols-2 lg:gap-y-8", children: [
                            (0, import_jsx_runtime2.jsx)(
                              Form.Field,
                              {
                                control: form.control,
                                name: "width",
                                render: ({ field }) => {
                                  return (0, import_jsx_runtime2.jsxs)(Form.Item, { children: [
                                    (0, import_jsx_runtime2.jsx)(Form.Label, { optional: true, children: t2("products.fields.width.label") }),
                                    (0, import_jsx_runtime2.jsx)(Form.Control, { children: (0, import_jsx_runtime2.jsx)(
                                      Input,
                                      {
                                        ...field,
                                        type: "number",
                                        min: 0,
                                        placeholder: "100"
                                      }
                                    ) })
                                  ] });
                                }
                              }
                            ),
                            (0, import_jsx_runtime2.jsx)(
                              Form.Field,
                              {
                                control: form.control,
                                name: "length",
                                render: ({ field }) => {
                                  return (0, import_jsx_runtime2.jsxs)(Form.Item, { children: [
                                    (0, import_jsx_runtime2.jsx)(Form.Label, { optional: true, children: t2("products.fields.length.label") }),
                                    (0, import_jsx_runtime2.jsx)(Form.Control, { children: (0, import_jsx_runtime2.jsx)(
                                      Input,
                                      {
                                        ...field,
                                        type: "number",
                                        min: 0,
                                        placeholder: "100"
                                      }
                                    ) })
                                  ] });
                                }
                              }
                            ),
                            (0, import_jsx_runtime2.jsx)(
                              Form.Field,
                              {
                                control: form.control,
                                name: "height",
                                render: ({ field }) => {
                                  return (0, import_jsx_runtime2.jsxs)(Form.Item, { children: [
                                    (0, import_jsx_runtime2.jsx)(Form.Label, { optional: true, children: t2("products.fields.height.label") }),
                                    (0, import_jsx_runtime2.jsx)(Form.Control, { children: (0, import_jsx_runtime2.jsx)(
                                      Input,
                                      {
                                        ...field,
                                        type: "number",
                                        min: 0,
                                        placeholder: "100"
                                      }
                                    ) })
                                  ] });
                                }
                              }
                            ),
                            (0, import_jsx_runtime2.jsx)(
                              Form.Field,
                              {
                                control: form.control,
                                name: "weight",
                                render: ({ field }) => {
                                  return (0, import_jsx_runtime2.jsxs)(Form.Item, { children: [
                                    (0, import_jsx_runtime2.jsx)(Form.Label, { optional: true, children: t2("products.fields.weight.label") }),
                                    (0, import_jsx_runtime2.jsx)(Form.Control, { children: (0, import_jsx_runtime2.jsx)(
                                      Input,
                                      {
                                        ...field,
                                        type: "number",
                                        min: 0,
                                        placeholder: "100"
                                      }
                                    ) })
                                  ] });
                                }
                              }
                            ),
                            (0, import_jsx_runtime2.jsx)(
                              Form.Field,
                              {
                                control: form.control,
                                name: "mid_code",
                                render: ({ field }) => {
                                  return (0, import_jsx_runtime2.jsxs)(Form.Item, { children: [
                                    (0, import_jsx_runtime2.jsx)(Form.Label, { optional: true, children: t2("products.fields.mid_code.label") }),
                                    (0, import_jsx_runtime2.jsx)(Form.Control, { children: (0, import_jsx_runtime2.jsx)(Input, { ...field }) })
                                  ] });
                                }
                              }
                            ),
                            (0, import_jsx_runtime2.jsx)(
                              Form.Field,
                              {
                                control: form.control,
                                name: "hs_code",
                                render: ({ field }) => {
                                  return (0, import_jsx_runtime2.jsxs)(Form.Item, { children: [
                                    (0, import_jsx_runtime2.jsx)(Form.Label, { optional: true, children: t2("products.fields.hs_code.label") }),
                                    (0, import_jsx_runtime2.jsx)(Form.Control, { children: (0, import_jsx_runtime2.jsx)(Input, { ...field }) })
                                  ] });
                                }
                              }
                            ),
                            (0, import_jsx_runtime2.jsx)(
                              Form.Field,
                              {
                                control: form.control,
                                name: "origin_country",
                                render: ({ field }) => {
                                  return (0, import_jsx_runtime2.jsxs)(Form.Item, { children: [
                                    (0, import_jsx_runtime2.jsx)(Form.Label, { optional: true, children: t2("products.fields.countryOrigin.label") }),
                                    (0, import_jsx_runtime2.jsx)(Form.Control, { children: (0, import_jsx_runtime2.jsx)(CountrySelect, { ...field }) })
                                  ] });
                                }
                              }
                            ),
                            (0, import_jsx_runtime2.jsx)(
                              Form.Field,
                              {
                                control: form.control,
                                name: "material",
                                render: ({ field }) => {
                                  return (0, import_jsx_runtime2.jsxs)(Form.Item, { children: [
                                    (0, import_jsx_runtime2.jsx)(Form.Label, { optional: true, children: t2("products.fields.material.label") }),
                                    (0, import_jsx_runtime2.jsx)(Form.Control, { children: (0, import_jsx_runtime2.jsx)(Input, { ...field }) })
                                  ] });
                                }
                              }
                            )
                          ] })
                        ] })
                      ] })
                    }
                  ),
                  (0, import_jsx_runtime2.jsx)(
                    ProgressTabs.Content,
                    {
                      value: "availability",
                      className: "size-full",
                      children: (0, import_jsx_runtime2.jsx)(InventoryAvailabilityForm, { form, locations })
                    }
                  )
                ]
              }
            ),
            (0, import_jsx_runtime2.jsx)(RouteFocusModal.Footer, { children: (0, import_jsx_runtime2.jsxs)("div", { className: "flex items-center justify-end gap-x-2", children: [
              (0, import_jsx_runtime2.jsx)(RouteFocusModal.Close, { asChild: true, children: (0, import_jsx_runtime2.jsx)(Button, { variant: "secondary", size: "small", children: t2("actions.cancel") }) }),
              (0, import_jsx_runtime2.jsx)(
                Button,
                {
                  size: "small",
                  className: "whitespace-nowrap",
                  isLoading,
                  onClick: tab !== "availability" ? onNext : void 0,
                  type: tab === "availability" ? "submit" : "button",
                  children: tab === "availability" ? t2("actions.save") : t2("general.next")
                },
                tab === "availability" ? "details" : "pricing"
              )
            ] }) })
          ]
        }
      )
    }
  ) });
}
function InventoryCreate() {
  const { isPending, stock_locations, isError, error } = useStockLocations({
    limit: 9999,
    fields: "id,name"
  });
  const ready = !isPending && !!stock_locations;
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime3.jsx)(RouteFocusModal, { children: ready && (0, import_jsx_runtime3.jsx)(InventoryCreateForm, { locations: stock_locations }) });
}
export {
  InventoryCreate as Component
};
//# sourceMappingURL=inventory-create-RXZ73PU6-A7UJ5MDW.js.map
