{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-TP7N4YBP.mjs"], "sourcesContent": ["import {\n  useDeleteProductCategory\n} from \"./chunk-ZJ3OFMHB.mjs\";\n\n// src/routes/categories/common/hooks/use-delete-product-category-action.tsx\nimport { toast, usePrompt } from \"@medusajs/ui\";\nimport { useTranslation } from \"react-i18next\";\nimport { useNavigate } from \"react-router-dom\";\nvar useDeleteProductCategoryAction = (category) => {\n  const { t } = useTranslation();\n  const navigate = useNavigate();\n  const prompt = usePrompt();\n  const { mutateAsync } = useDeleteProductCategory(category.id);\n  const handleDelete = async () => {\n    const res = await prompt({\n      title: t(\"general.areYouSure\"),\n      description: t(\"categories.delete.confirmation\", {\n        name: category.name\n      }),\n      confirmText: t(\"actions.delete\"),\n      cancelText: t(\"actions.cancel\")\n    });\n    if (!res) {\n      return;\n    }\n    await mutateAsync(void 0, {\n      onSuccess: () => {\n        toast.success(\n          t(\"categories.delete.successToast\", {\n            name: category.name\n          })\n        );\n        navigate(\"/categories\", {\n          replace: true\n        });\n      },\n      onError: (e) => {\n        toast.error(e.message);\n      }\n    });\n  };\n  return handleDelete;\n};\n\nexport {\n  useDeleteProductCategoryAction\n};\n"], "mappings": ";;;;;;;;;;;;;;;AAQA,IAAI,iCAAiC,CAAC,aAAa;AACjD,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,QAAM,WAAW,YAAY;AAC7B,QAAM,SAAS,UAAU;AACzB,QAAM,EAAE,YAAY,IAAI,yBAAyB,SAAS,EAAE;AAC5D,QAAM,eAAe,YAAY;AAC/B,UAAM,MAAM,MAAM,OAAO;AAAA,MACvB,OAAO,EAAE,oBAAoB;AAAA,MAC7B,aAAa,EAAE,kCAAkC;AAAA,QAC/C,MAAM,SAAS;AAAA,MACjB,CAAC;AAAA,MACD,aAAa,EAAE,gBAAgB;AAAA,MAC/B,YAAY,EAAE,gBAAgB;AAAA,IAChC,CAAC;AACD,QAAI,CAAC,KAAK;AACR;AAAA,IACF;AACA,UAAM,YAAY,QAAQ;AAAA,MACxB,WAAW,MAAM;AACf,cAAM;AAAA,UACJ,EAAE,kCAAkC;AAAA,YAClC,MAAM,SAAS;AAAA,UACjB,CAAC;AAAA,QACH;AACA,iBAAS,eAAe;AAAA,UACtB,SAAS;AAAA,QACX,CAAC;AAAA,MACH;AAAA,MACA,SAAS,CAAC,MAAM;AACd,cAAM,MAAM,EAAE,OAAO;AAAA,MACvB;AAAA,IACF,CAAC;AAAA,EACH;AACA,SAAO;AACT;", "names": []}