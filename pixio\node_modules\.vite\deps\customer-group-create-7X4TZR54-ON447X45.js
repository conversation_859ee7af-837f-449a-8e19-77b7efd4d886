import "./chunk-EGRHWZRV.js";
import {
  KeyboundForm
} from "./chunk-DP54EP6X.js";
import {
  RouteFocusModal,
  useRouteModal
} from "./chunk-MVVOBQIC.js";
import {
  t
} from "./chunk-WHQIBI5S.js";
import {
  objectType,
  stringType
} from "./chunk-4XXECALA.js";
import {
  Form,
  useForm
} from "./chunk-XXJU43CK.js";
import {
  useCreateCustomerGroup
} from "./chunk-2AWKOUCD.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import "./chunk-JFCIYOH4.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-5GF3RGIE.js";
import "./chunk-T7YBVUWZ.js";
import {
  Button,
  Heading,
  Input,
  Text,
  toast
} from "./chunk-YP2LLXWB.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-ZSQ362B4.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/customer-group-create-7X4TZR54.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var CreateCustomerGroupSchema = objectType({
  name: stringType().min(1)
});
var CreateCustomerGroupForm = () => {
  const { t: t2 } = useTranslation();
  const { handleSuccess } = useRouteModal();
  const form = useForm({
    defaultValues: {
      name: ""
    },
    resolver: t(CreateCustomerGroupSchema)
  });
  const { mutateAsync, isPending } = useCreateCustomerGroup();
  const handleSubmit = form.handleSubmit(async (data) => {
    await mutateAsync(
      {
        name: data.name
      },
      {
        onSuccess: ({ customer_group }) => {
          toast.success(
            t2("customerGroups.create.successToast", {
              name: customer_group.name
            })
          );
          handleSuccess(`/customer-groups/${customer_group.id}`);
        },
        onError: (error) => {
          toast.error(error.message);
        }
      }
    );
  });
  return (0, import_jsx_runtime.jsx)(RouteFocusModal.Form, { form, children: (0, import_jsx_runtime.jsxs)(
    KeyboundForm,
    {
      className: "flex h-full flex-col overflow-hidden",
      onSubmit: handleSubmit,
      children: [
        (0, import_jsx_runtime.jsx)(RouteFocusModal.Header, {}),
        (0, import_jsx_runtime.jsx)(RouteFocusModal.Body, { className: "flex flex-col items-center pt-[72px]", children: (0, import_jsx_runtime.jsxs)("div", { className: "flex size-full max-w-[720px] flex-col gap-y-8", children: [
          (0, import_jsx_runtime.jsxs)("div", { children: [
            (0, import_jsx_runtime.jsx)(RouteFocusModal.Title, { asChild: true, children: (0, import_jsx_runtime.jsx)(Heading, { children: t2("customerGroups.create.header") }) }),
            (0, import_jsx_runtime.jsx)(RouteFocusModal.Description, { asChild: true, children: (0, import_jsx_runtime.jsx)(Text, { size: "small", className: "text-ui-fg-subtle", children: t2("customerGroups.create.hint") }) })
          ] }),
          (0, import_jsx_runtime.jsx)("div", { className: "grid grid-cols-2 gap-4", children: (0, import_jsx_runtime.jsx)(
            Form.Field,
            {
              control: form.control,
              name: "name",
              render: ({ field }) => {
                return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                  (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("fields.name") }),
                  (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(Input, { ...field }) }),
                  (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                ] });
              }
            }
          ) })
        ] }) }),
        (0, import_jsx_runtime.jsxs)(RouteFocusModal.Footer, { children: [
          (0, import_jsx_runtime.jsx)(RouteFocusModal.Close, { asChild: true, children: (0, import_jsx_runtime.jsx)(Button, { variant: "secondary", size: "small", children: t2("actions.cancel") }) }),
          (0, import_jsx_runtime.jsx)(
            Button,
            {
              type: "submit",
              variant: "primary",
              size: "small",
              isLoading: isPending,
              children: t2("actions.create")
            }
          )
        ] })
      ]
    }
  ) });
};
var CustomerGroupCreate = () => {
  return (0, import_jsx_runtime2.jsx)(RouteFocusModal, { children: (0, import_jsx_runtime2.jsx)(CreateCustomerGroupForm, {}) });
};
export {
  CustomerGroupCreate as Component
};
//# sourceMappingURL=customer-group-create-7X4TZR54-ON447X45.js.map
