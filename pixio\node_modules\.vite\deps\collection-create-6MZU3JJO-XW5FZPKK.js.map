{"version": 3, "sources": ["../../@medusajs/dashboard/dist/collection-create-6MZU3JJO.mjs"], "sourcesContent": ["import {\n  HandleInput\n} from \"./chunk-7OYLCEKK.mjs\";\nimport \"./chunk-IUCDCPJU.mjs\";\nimport {\n  KeyboundForm\n} from \"./chunk-6HTZNHPT.mjs\";\nimport {\n  RouteFocusModal,\n  useRouteModal\n} from \"./chunk-4TC5YS65.mjs\";\nimport {\n  Form\n} from \"./chunk-OBQI23QM.mjs\";\nimport {\n  useCreateCollection\n} from \"./chunk-3OHH43G6.mjs\";\nimport \"./chunk-6I62UDJA.mjs\";\nimport \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-RPUOO7AV.mjs\";\n\n// src/routes/collections/collection-create/components/create-collection-form/create-collection-form.tsx\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { But<PERSON>, Heading, Input, Text, toast } from \"@medusajs/ui\";\nimport { useForm } from \"react-hook-form\";\nimport { useTranslation } from \"react-i18next\";\nimport * as zod from \"zod\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar CreateCollectionSchema = zod.object({\n  title: zod.string().min(1),\n  handle: zod.string().optional()\n});\nvar CreateCollectionForm = () => {\n  const { t } = useTranslation();\n  const { handleSuccess } = useRouteModal();\n  const form = useForm({\n    defaultValues: {\n      title: \"\",\n      handle: \"\"\n    },\n    resolver: zodResolver(CreateCollectionSchema)\n  });\n  const { mutateAsync, isPending } = useCreateCollection();\n  const handleSubmit = form.handleSubmit(async (data) => {\n    await mutateAsync(data, {\n      onSuccess: ({ collection }) => {\n        handleSuccess(`/collections/${collection.id}`);\n        toast.success(t(\"collections.createSuccess\"));\n      },\n      onError: (error) => {\n        toast.error(error.message);\n      }\n    });\n  });\n  return /* @__PURE__ */ jsx(RouteFocusModal.Form, { form, children: /* @__PURE__ */ jsxs(KeyboundForm, { onSubmit: handleSubmit, children: [\n    /* @__PURE__ */ jsx(RouteFocusModal.Header, { children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center justify-end gap-x-2\", children: [\n      /* @__PURE__ */ jsx(RouteFocusModal.Close, { asChild: true, children: /* @__PURE__ */ jsx(Button, { size: \"small\", variant: \"secondary\", children: t(\"actions.cancel\") }) }),\n      /* @__PURE__ */ jsx(\n        Button,\n        {\n          size: \"small\",\n          variant: \"primary\",\n          type: \"submit\",\n          isLoading: isPending,\n          children: t(\"actions.create\")\n        }\n      )\n    ] }) }),\n    /* @__PURE__ */ jsx(RouteFocusModal.Body, { className: \"flex flex-col items-center p-16\", children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex w-full max-w-[720px] flex-col gap-y-8\", children: [\n      /* @__PURE__ */ jsxs(\"div\", { children: [\n        /* @__PURE__ */ jsx(Heading, { children: t(\"collections.createCollection\") }),\n        /* @__PURE__ */ jsx(Text, { size: \"small\", className: \"text-ui-fg-subtle\", children: t(\"collections.createCollectionHint\") })\n      ] }),\n      /* @__PURE__ */ jsxs(\"div\", { className: \"grid grid-cols-1 gap-4 md:grid-cols-2\", children: [\n        /* @__PURE__ */ jsx(\n          Form.Field,\n          {\n            control: form.control,\n            name: \"title\",\n            render: ({ field }) => {\n              return /* @__PURE__ */ jsxs(Form.Item, { children: [\n                /* @__PURE__ */ jsx(Form.Label, { children: t(\"fields.title\") }),\n                /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(Input, { autoComplete: \"off\", ...field }) }),\n                /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n              ] });\n            }\n          }\n        ),\n        /* @__PURE__ */ jsx(\n          Form.Field,\n          {\n            control: form.control,\n            name: \"handle\",\n            render: ({ field }) => {\n              return /* @__PURE__ */ jsxs(Form.Item, { children: [\n                /* @__PURE__ */ jsx(\n                  Form.Label,\n                  {\n                    optional: true,\n                    tooltip: t(\"collections.handleTooltip\"),\n                    children: t(\"fields.handle\")\n                  }\n                ),\n                /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(HandleInput, { ...field }) }),\n                /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n              ] });\n            }\n          }\n        )\n      ] })\n    ] }) })\n  ] }) });\n};\n\n// src/routes/collections/collection-create/collection-create.tsx\nimport { jsx as jsx2 } from \"react/jsx-runtime\";\nvar CollectionCreate = () => {\n  return /* @__PURE__ */ jsx2(RouteFocusModal, { children: /* @__PURE__ */ jsx2(CreateCollectionForm, {}) });\n};\nexport {\n  CollectionCreate as Component\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6BA,yBAA0B;AAwF1B,IAAAA,sBAA4B;AAvF5B,IAAI,yBAA6B,WAAO;AAAA,EACtC,OAAW,WAAO,EAAE,IAAI,CAAC;AAAA,EACzB,QAAY,WAAO,EAAE,SAAS;AAChC,CAAC;AACD,IAAI,uBAAuB,MAAM;AAC/B,QAAM,EAAE,GAAAC,GAAE,IAAI,eAAe;AAC7B,QAAM,EAAE,cAAc,IAAI,cAAc;AACxC,QAAM,OAAO,QAAQ;AAAA,IACnB,eAAe;AAAA,MACb,OAAO;AAAA,MACP,QAAQ;AAAA,IACV;AAAA,IACA,UAAU,EAAY,sBAAsB;AAAA,EAC9C,CAAC;AACD,QAAM,EAAE,aAAa,UAAU,IAAI,oBAAoB;AACvD,QAAM,eAAe,KAAK,aAAa,OAAO,SAAS;AACrD,UAAM,YAAY,MAAM;AAAA,MACtB,WAAW,CAAC,EAAE,WAAW,MAAM;AAC7B,sBAAc,gBAAgB,WAAW,EAAE,EAAE;AAC7C,cAAM,QAAQA,GAAE,2BAA2B,CAAC;AAAA,MAC9C;AAAA,MACA,SAAS,CAAC,UAAU;AAClB,cAAM,MAAM,MAAM,OAAO;AAAA,MAC3B;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACD,aAAuB,wBAAI,gBAAgB,MAAM,EAAE,MAAM,cAA0B,yBAAK,cAAc,EAAE,UAAU,cAAc,UAAU;AAAA,QACxH,wBAAI,gBAAgB,QAAQ,EAAE,cAA0B,yBAAK,OAAO,EAAE,WAAW,yCAAyC,UAAU;AAAA,UAClI,wBAAI,gBAAgB,OAAO,EAAE,SAAS,MAAM,cAA0B,wBAAI,QAAQ,EAAE,MAAM,SAAS,SAAS,aAAa,UAAUA,GAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;AAAA,UAC3J;AAAA,QACd;AAAA,QACA;AAAA,UACE,MAAM;AAAA,UACN,SAAS;AAAA,UACT,MAAM;AAAA,UACN,WAAW;AAAA,UACX,UAAUA,GAAE,gBAAgB;AAAA,QAC9B;AAAA,MACF;AAAA,IACF,EAAE,CAAC,EAAE,CAAC;AAAA,QACU,wBAAI,gBAAgB,MAAM,EAAE,WAAW,mCAAmC,cAA0B,yBAAK,OAAO,EAAE,WAAW,8CAA8C,UAAU;AAAA,UACnL,yBAAK,OAAO,EAAE,UAAU;AAAA,YACtB,wBAAI,SAAS,EAAE,UAAUA,GAAE,8BAA8B,EAAE,CAAC;AAAA,YAC5D,wBAAI,MAAM,EAAE,MAAM,SAAS,WAAW,qBAAqB,UAAUA,GAAE,kCAAkC,EAAE,CAAC;AAAA,MAC9H,EAAE,CAAC;AAAA,UACa,yBAAK,OAAO,EAAE,WAAW,yCAAyC,UAAU;AAAA,YAC1E;AAAA,UACd,KAAK;AAAA,UACL;AAAA,YACE,SAAS,KAAK;AAAA,YACd,MAAM;AAAA,YACN,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,yBAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,oBACjC,wBAAI,KAAK,OAAO,EAAE,UAAUA,GAAE,cAAc,EAAE,CAAC;AAAA,oBAC/C,wBAAI,KAAK,SAAS,EAAE,cAA0B,wBAAI,OAAO,EAAE,cAAc,OAAO,GAAG,MAAM,CAAC,EAAE,CAAC;AAAA,oBAC7F,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,cAC3C,EAAE,CAAC;AAAA,YACL;AAAA,UACF;AAAA,QACF;AAAA,YACgB;AAAA,UACd,KAAK;AAAA,UACL;AAAA,YACE,SAAS,KAAK;AAAA,YACd,MAAM;AAAA,YACN,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,yBAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,oBACjC;AAAA,kBACd,KAAK;AAAA,kBACL;AAAA,oBACE,UAAU;AAAA,oBACV,SAASA,GAAE,2BAA2B;AAAA,oBACtC,UAAUA,GAAE,eAAe;AAAA,kBAC7B;AAAA,gBACF;AAAA,oBACgB,wBAAI,KAAK,SAAS,EAAE,cAA0B,wBAAI,aAAa,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC;AAAA,oBAC9E,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,cAC3C,EAAE,CAAC;AAAA,YACL;AAAA,UACF;AAAA,QACF;AAAA,MACF,EAAE,CAAC;AAAA,IACL,EAAE,CAAC,EAAE,CAAC;AAAA,EACR,EAAE,CAAC,EAAE,CAAC;AACR;AAIA,IAAI,mBAAmB,MAAM;AAC3B,aAAuB,oBAAAC,KAAK,iBAAiB,EAAE,cAA0B,oBAAAA,KAAK,sBAAsB,CAAC,CAAC,EAAE,CAAC;AAC3G;", "names": ["import_jsx_runtime", "t", "jsx2"]}