import {
  DEFAULT_FIELDS
} from "./chunk-UKYP5JCA.js";
import {
  useComboboxData
} from "./chunk-CFEMRZCK.js";
import {
  Combobox
} from "./chunk-RC2DY4WG.js";
import "./chunk-EGRHWZRV.js";
import {
  KeyboundForm
} from "./chunk-DP54EP6X.js";
import {
  RouteDrawer,
  useRouteModal
} from "./chunk-MVVOBQIC.js";
import "./chunk-RQF55WOK.js";
import "./chunk-5QX4V4M4.js";
import "./chunk-IA4ROPJA.js";
import {
  t
} from "./chunk-WHQIBI5S.js";
import {
  objectType,
  stringType
} from "./chunk-4XXECALA.js";
import "./chunk-NV2N3EWM.js";
import {
  Form,
  useForm
} from "./chunk-XXJU43CK.js";
import "./chunk-WISME5HP.js";
import "./chunk-WAUSOHFQ.js";
import "./chunk-3ORK3QZ4.js";
import "./chunk-3A5TVVNI.js";
import "./chunk-EZ62MM7J.js";
import "./chunk-3R6LYIXM.js";
import "./chunk-2P2O4TMQ.js";
import "./chunk-KI3HEITX.js";
import "./chunk-2VUCAO7L.js";
import "./chunk-PTIULTKD.js";
import "./chunk-KISXY2LD.js";
import "./chunk-L7C3XPHJ.js";
import "./chunk-T7MG2EIJ.js";
import "./chunk-MER4VYPR.js";
import "./chunk-2AWKOUCD.js";
import "./chunk-YL3TYBFR.js";
import "./chunk-TZWW72YW.js";
import "./chunk-MSQ25CWB.js";
import "./chunk-ONYSAQ5Z.js";
import {
  useOrder,
  useRequestTransferOrder
} from "./chunk-NIH7SAXN.js";
import "./chunk-UEJFWAFE.js";
import "./chunk-GR55MFKT.js";
import "./chunk-AAFHKNJG.js";
import "./chunk-7XDBFDTZ.js";
import "./chunk-3JKGO5XL.js";
import "./chunk-OISUTS7G.js";
import "./chunk-AJYMIHLQ.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import {
  sdk
} from "./chunk-JFCIYOH4.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-5GF3RGIE.js";
import {
  useParams
} from "./chunk-T7YBVUWZ.js";
import {
  Button,
  Heading,
  Input,
  toast
} from "./chunk-YP2LLXWB.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-ZSQ362B4.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/order-request-transfer-BESJQ4LK.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
function TransferHeader() {
  return (0, import_jsx_runtime.jsxs)(
    "svg",
    {
      width: "200",
      height: "128",
      viewBox: "0 0 200 128",
      fill: "none",
      xmlns: "http://www.w3.org/2000/svg",
      children: [
        (0, import_jsx_runtime.jsx)(
          "rect",
          {
            x: "0.00428286",
            y: "-0.742904",
            width: "33.5",
            height: "65.5",
            rx: "6.75",
            transform: "matrix(0.865865 0.500278 -0.871576 0.490261 149.756 60.938)",
            className: "stroke-ui-fg-subtle fill-ui-bg-base",
            strokeWidth: "1.5"
          }
        ),
        (0, import_jsx_runtime.jsx)(
          "rect",
          {
            x: "0.00428286",
            y: "-0.742904",
            width: "33.5",
            height: "65.5",
            rx: "6.75",
            transform: "matrix(0.865865 0.500278 -0.871576 0.490261 149.756 57.9383)",
            className: "stroke-ui-fg-subtle fill-ui-bg-base",
            strokeWidth: "1.5"
          }
        ),
        (0, import_jsx_runtime.jsxs)("g", { clipPath: "url(#clip0_20787_38934)", children: [
          (0, import_jsx_runtime.jsx)(
            "path",
            {
              d: "M140.579 79.6421L139.126 80.4592",
              className: "stroke-ui-fg-subtle",
              strokeWidth: "1.5",
              strokeLinecap: "round",
              strokeLinejoin: "round"
            }
          ),
          (0, import_jsx_runtime.jsx)(
            "path",
            {
              opacity: "0.88",
              d: "M142.305 82.046L140.257 82.0342",
              className: "stroke-ui-fg-subtle",
              strokeWidth: "1.5",
              strokeLinecap: "round",
              strokeLinejoin: "round"
            }
          ),
          (0, import_jsx_runtime.jsx)(
            "path",
            {
              opacity: "0.75",
              d: "M140.552 84.4297L139.108 83.5959",
              className: "stroke-ui-fg-subtle",
              strokeWidth: "1.5",
              strokeLinecap: "round",
              strokeLinejoin: "round"
            }
          ),
          (0, import_jsx_runtime.jsx)(
            "path",
            {
              opacity: "0.63",
              d: "M136.347 85.3975L136.354 84.23",
              className: "stroke-ui-fg-subtle",
              strokeWidth: "1.5",
              strokeLinecap: "round",
              strokeLinejoin: "round"
            }
          ),
          (0, import_jsx_runtime.jsx)(
            "path",
            {
              opacity: "0.5",
              d: "M132.154 84.3813L133.606 83.5642",
              className: "stroke-ui-fg-subtle",
              strokeWidth: "1.5",
              strokeLinecap: "round",
              strokeLinejoin: "round"
            }
          ),
          (0, import_jsx_runtime.jsx)(
            "path",
            {
              opacity: "0.38",
              d: "M130.428 81.9775L132.476 81.9893",
              className: "stroke-ui-fg-subtle",
              strokeWidth: "1.5",
              strokeLinecap: "round",
              strokeLinejoin: "round"
            }
          ),
          (0, import_jsx_runtime.jsx)(
            "path",
            {
              opacity: "0.25",
              d: "M132.181 79.5938L133.625 80.4275",
              className: "stroke-ui-fg-subtle",
              strokeWidth: "1.5",
              strokeLinecap: "round",
              strokeLinejoin: "round"
            }
          ),
          (0, import_jsx_runtime.jsx)(
            "path",
            {
              opacity: "0.13",
              d: "M136.386 78.626L136.379 79.7935",
              className: "stroke-ui-fg-subtle",
              strokeWidth: "1.5",
              strokeLinecap: "round",
              strokeLinejoin: "round"
            }
          )
        ] }),
        (0, import_jsx_runtime.jsx)(
          "rect",
          {
            width: "12",
            height: "3",
            rx: "1.5",
            transform: "matrix(0.865865 0.500278 -0.871576 0.490261 156.447 64.7927)",
            className: "fill-ui-fg-muted"
          }
        ),
        (0, import_jsx_runtime.jsx)(
          "rect",
          {
            x: "0.00428286",
            y: "-0.742904",
            width: "33.5",
            height: "65.5",
            rx: "6.75",
            transform: "matrix(0.865865 0.500278 -0.871576 0.490261 77.0232 18.9148)",
            className: "stroke-ui-fg-subtle fill-ui-fg-muted",
            strokeWidth: "1.5"
          }
        ),
        (0, import_jsx_runtime.jsx)(
          "rect",
          {
            x: "0.00428286",
            y: "-0.742904",
            width: "33.5",
            height: "65.5",
            rx: "6.75",
            transform: "matrix(0.865865 0.500278 -0.871576 0.490261 77.0232 15.9148)",
            className: "stroke-ui-fg-subtle fill-ui-bg-base",
            strokeWidth: "1.5"
          }
        ),
        (0, import_jsx_runtime.jsx)(
          "rect",
          {
            width: "12",
            height: "3",
            rx: "1.5",
            transform: "matrix(0.865865 0.500278 -0.871576 0.490261 83.7141 22.7693)",
            className: "fill-ui-fg-muted"
          }
        ),
        (0, import_jsx_runtime.jsx)(
          "rect",
          {
            width: "17",
            height: "3",
            rx: "1.5",
            transform: "matrix(0.865865 0.500278 -0.871576 0.490261 57.5554 39.458)",
            className: "fill-ui-fg-muted"
          }
        ),
        (0, import_jsx_runtime.jsx)(
          "rect",
          {
            width: "12",
            height: "3",
            rx: "1.5",
            transform: "matrix(0.865865 0.500278 -0.871576 0.490261 53.1975 41.9094)",
            className: "fill-ui-fg-muted"
          }
        ),
        (0, import_jsx_runtime.jsx)("g", { clipPath: "url(#clip1_20787_38934)", children: (0, import_jsx_runtime.jsx)(
          "path",
          {
            d: "M52.3603 36.4564C50.9277 35.6287 48.59 35.6152 47.148 36.4264C45.7059 37.2375 45.6983 38.5703 47.1308 39.398C48.5634 40.2257 50.9011 40.2392 52.3432 39.428C53.7852 38.6169 53.7929 37.2841 52.3603 36.4564ZM48.4382 38.6626C47.7221 38.2488 47.726 37.5822 48.4468 37.1768C49.1676 36.7713 50.3369 36.7781 51.0529 37.1918C51.769 37.6055 51.7652 38.2722 51.0444 38.6776C50.3236 39.083 49.1543 39.0763 48.4382 38.6626Z",
            className: "fill-ui-fg-subtle"
          }
        ) }),
        (0, import_jsx_runtime.jsx)(
          "rect",
          {
            width: "17",
            height: "3",
            rx: "1.5",
            transform: "matrix(0.865865 0.500278 -0.871576 0.490261 69.7573 32.5945)",
            className: "fill-ui-fg-muted"
          }
        ),
        (0, import_jsx_runtime.jsx)(
          "rect",
          {
            width: "12",
            height: "3",
            rx: "1.5",
            transform: "matrix(0.865865 0.500278 -0.871576 0.490261 65.3994 35.0459)",
            className: "fill-ui-fg-muted"
          }
        ),
        (0, import_jsx_runtime.jsx)("g", { clipPath: "url(#clip2_20787_38934)", children: (0, import_jsx_runtime.jsx)(
          "path",
          {
            d: "M64.5622 29.5929C63.1296 28.7652 60.7919 28.7517 59.3499 29.5628C57.9079 30.374 57.9002 31.7067 59.3327 32.5344C60.7653 33.3622 63.103 33.3756 64.5451 32.5645C65.9871 31.7534 65.9948 30.4206 64.5622 29.5929ZM63.8581 31.3974L60.8148 31.6267C60.6827 31.6368 60.5495 31.6135 60.4486 31.5632C60.4399 31.5587 60.4321 31.5547 60.4244 31.5502C60.3386 31.5006 60.2899 31.4337 60.2903 31.3639L60.2933 30.6203C60.2937 30.4754 60.5012 30.3587 60.7557 30.3602C61.0102 30.3616 61.2163 30.4802 61.2155 30.6258L61.2138 31.0671L63.7317 30.8771C63.9833 30.858 64.2168 30.9586 64.2512 31.1032C64.286 31.247 64.1101 31.379 63.8581 31.3978L63.8581 31.3974Z",
            className: "fill-ui-fg-subtle"
          }
        ) }),
        (0, import_jsx_runtime.jsx)("g", { clipPath: "url(#clip3_20787_38934)", children: (0, import_jsx_runtime.jsx)(
          "path",
          {
            d: "M93.106 54.3022L100.49 54.3448L100.514 50.135",
            className: "stroke-ui-fg-subtle",
            strokeWidth: "1.5",
            strokeLinecap: "round",
            strokeLinejoin: "round"
          }
        ) }),
        (0, import_jsx_runtime.jsx)("g", { clipPath: "url(#clip4_20787_38934)", children: (0, import_jsx_runtime.jsx)(
          "path",
          {
            d: "M103.496 60.3056L110.881 60.3482L110.905 56.1384",
            className: "stroke-ui-fg-subtle",
            strokeWidth: "1.5",
            strokeLinecap: "round",
            strokeLinejoin: "round"
          }
        ) }),
        (0, import_jsx_runtime.jsx)("g", { clipPath: "url(#clip5_20787_38934)", children: (0, import_jsx_runtime.jsx)(
          "path",
          {
            d: "M113.887 66.3088L121.271 66.3514L121.295 62.1416",
            className: "stroke-ui-fg-subtle",
            strokeWidth: "1.5",
            strokeLinecap: "round",
            strokeLinejoin: "round"
          }
        ) }),
        (0, import_jsx_runtime.jsx)("g", { clipPath: "url(#clip6_20787_38934)", children: (0, import_jsx_runtime.jsx)(
          "path",
          {
            d: "M86.1135 61.6911L78.7294 61.6486L78.7051 65.8583",
            className: "stroke-ui-fg-subtle",
            strokeWidth: "1.5",
            strokeLinecap: "round",
            strokeLinejoin: "round"
          }
        ) }),
        (0, import_jsx_runtime.jsx)("g", { clipPath: "url(#clip7_20787_38934)", children: (0, import_jsx_runtime.jsx)(
          "path",
          {
            d: "M96.5039 67.6945L89.1198 67.652L89.0955 71.8618",
            className: "stroke-ui-fg-subtle",
            strokeWidth: "1.5",
            strokeLinecap: "round",
            strokeLinejoin: "round"
          }
        ) }),
        (0, import_jsx_runtime.jsx)("g", { clipPath: "url(#clip8_20787_38934)", children: (0, import_jsx_runtime.jsx)(
          "path",
          {
            d: "M106.894 73.6977L99.5102 73.6551L99.4859 77.8649",
            className: "stroke-ui-fg-subtle",
            strokeWidth: "1.5",
            strokeLinecap: "round",
            strokeLinejoin: "round"
          }
        ) }),
        (0, import_jsx_runtime.jsxs)("defs", { children: [
          (0, import_jsx_runtime.jsx)("clipPath", { id: "clip0_20787_38934", children: (0, import_jsx_runtime.jsx)(
            "rect",
            {
              width: "12",
              height: "12",
              className: "fill-ui-bg-base",
              transform: "matrix(0.865865 0.500278 -0.871576 0.490261 136.401 76.0686)"
            }
          ) }),
          (0, import_jsx_runtime.jsx)("clipPath", { id: "clip1_20787_38934", children: (0, import_jsx_runtime.jsx)(
            "rect",
            {
              width: "6",
              height: "6",
              className: "fill-ui-bg-base",
              transform: "matrix(0.865865 0.500278 -0.871576 0.490261 49.7627 34.9556)"
            }
          ) }),
          (0, import_jsx_runtime.jsx)("clipPath", { id: "clip2_20787_38934", children: (0, import_jsx_runtime.jsx)(
            "rect",
            {
              width: "6",
              height: "6",
              className: "fill-ui-bg-base",
              transform: "matrix(0.865865 0.500278 -0.871576 0.490261 61.9646 28.092)"
            }
          ) }),
          (0, import_jsx_runtime.jsx)("clipPath", { id: "clip3_20787_38934", children: (0, import_jsx_runtime.jsx)(
            "rect",
            {
              width: "12",
              height: "12",
              className: "fill-ui-bg-base",
              transform: "matrix(0.865865 0.500278 -0.871576 0.490261 98.3596 47.1509)"
            }
          ) }),
          (0, import_jsx_runtime.jsx)("clipPath", { id: "clip4_20787_38934", children: (0, import_jsx_runtime.jsx)(
            "rect",
            {
              width: "12",
              height: "12",
              className: "fill-ui-bg-base",
              transform: "matrix(0.865865 0.500278 -0.871576 0.490261 108.75 53.1543)"
            }
          ) }),
          (0, import_jsx_runtime.jsx)("clipPath", { id: "clip5_20787_38934", children: (0, import_jsx_runtime.jsx)(
            "rect",
            {
              width: "12",
              height: "12",
              className: "fill-ui-bg-base",
              transform: "matrix(0.865865 0.500278 -0.871576 0.490261 119.14 59.1575)"
            }
          ) }),
          (0, import_jsx_runtime.jsx)("clipPath", { id: "clip6_20787_38934", children: (0, import_jsx_runtime.jsx)(
            "rect",
            {
              width: "12",
              height: "12",
              className: "fill-ui-bg-base",
              transform: "matrix(0.865865 0.500278 -0.871576 0.490261 80.9282 56.9561)"
            }
          ) }),
          (0, import_jsx_runtime.jsx)("clipPath", { id: "clip7_20787_38934", children: (0, import_jsx_runtime.jsx)(
            "rect",
            {
              width: "12",
              height: "12",
              className: "fill-ui-bg-base",
              transform: "matrix(0.865865 0.500278 -0.871576 0.490261 91.3186 62.9595)"
            }
          ) }),
          (0, import_jsx_runtime.jsx)("clipPath", { id: "clip8_20787_38934", children: (0, import_jsx_runtime.jsx)(
            "rect",
            {
              width: "12",
              height: "12",
              className: "fill-ui-bg-base",
              transform: "matrix(0.865865 0.500278 -0.871576 0.490261 101.709 68.9626)"
            }
          ) })
        ] })
      ]
    }
  );
}
var CreateOrderTransferSchema = objectType({
  customer_id: stringType().min(1),
  current_customer_details: stringType().min(1)
});
function CreateOrderTransferForm({
  order
}) {
  var _a, _b, _c, _d, _e;
  const { t: t2 } = useTranslation();
  const { handleSuccess } = useRouteModal();
  const form = useForm({
    defaultValues: {
      customer_id: "",
      current_customer_details: ((_a = order.customer) == null ? void 0 : _a.first_name) ? `${(_b = order.customer) == null ? void 0 : _b.first_name} ${(_c = order.customer) == null ? void 0 : _c.last_name} (${(_d = order.customer) == null ? void 0 : _d.email}) ` : (_e = order.customer) == null ? void 0 : _e.email
    },
    resolver: t(CreateOrderTransferSchema)
  });
  const customers = useComboboxData({
    queryKey: ["customers"],
    queryFn: (params) => sdk.admin.customer.list({ ...params, has_account: true }),
    getOptions: (data) => data.customers.map((item) => ({
      label: `${item.first_name || ""} ${item.last_name || ""} (${item.email})`,
      value: item.id
    }))
  });
  const { mutateAsync, isPending } = useRequestTransferOrder(order.id);
  const handleSubmit = form.handleSubmit(async (data) => {
    try {
      await mutateAsync({
        customer_id: data.customer_id
      });
      toast.success(t2("orders.transfer.requestSuccess", { email: order.email }));
      handleSuccess();
    } catch (error) {
      toast.error(error.message);
    }
  });
  return (0, import_jsx_runtime2.jsx)(RouteDrawer.Form, { form, children: (0, import_jsx_runtime2.jsxs)(
    KeyboundForm,
    {
      onSubmit: handleSubmit,
      className: "flex size-full flex-col overflow-hidden",
      children: [
        (0, import_jsx_runtime2.jsx)(RouteDrawer.Body, { className: "flex-1 overflow-auto", children: (0, import_jsx_runtime2.jsxs)("div", { className: "flex flex-col gap-y-8", children: [
          (0, import_jsx_runtime2.jsx)("div", { className: "flex justify-center", children: (0, import_jsx_runtime2.jsx)(TransferHeader, {}) }),
          (0, import_jsx_runtime2.jsx)(
            Form.Field,
            {
              control: form.control,
              name: "current_customer_details",
              render: ({ field }) => {
                return (0, import_jsx_runtime2.jsxs)(Form.Item, { children: [
                  (0, import_jsx_runtime2.jsx)(Form.Label, { children: t2("orders.transfer.currentOwner") }),
                  (0, import_jsx_runtime2.jsx)("span", { className: "txt-small text-ui-fg-muted", children: t2("orders.transfer.currentOwnerDescription") }),
                  (0, import_jsx_runtime2.jsx)(Form.Control, { children: (0, import_jsx_runtime2.jsx)(Input, { type: "email", ...field, disabled: true }) }),
                  (0, import_jsx_runtime2.jsx)(Form.ErrorMessage, {})
                ] });
              }
            }
          ),
          (0, import_jsx_runtime2.jsx)(
            Form.Field,
            {
              control: form.control,
              name: "customer_id",
              render: ({ field }) => {
                return (0, import_jsx_runtime2.jsxs)(Form.Item, { children: [
                  (0, import_jsx_runtime2.jsx)(Form.Label, { children: t2("orders.transfer.newOwner") }),
                  (0, import_jsx_runtime2.jsx)("span", { className: "txt-small text-ui-fg-muted", children: t2("orders.transfer.newOwnerDescription") }),
                  (0, import_jsx_runtime2.jsx)(Form.Control, { children: (0, import_jsx_runtime2.jsx)(
                    Combobox,
                    {
                      ...field,
                      options: customers.options,
                      searchValue: customers.searchValue,
                      onSearchValueChange: customers.onSearchValueChange,
                      fetchNextPage: customers.fetchNextPage,
                      className: "bg-ui-bg-field-component hover:bg-ui-bg-field-component-hover",
                      placeholder: t2("actions.select")
                    }
                  ) }),
                  (0, import_jsx_runtime2.jsx)(Form.ErrorMessage, {})
                ] });
              }
            }
          )
        ] }) }),
        (0, import_jsx_runtime2.jsx)(RouteDrawer.Footer, { children: (0, import_jsx_runtime2.jsxs)("div", { className: "flex items-center justify-end gap-x-2", children: [
          (0, import_jsx_runtime2.jsx)(RouteDrawer.Close, { asChild: true, children: (0, import_jsx_runtime2.jsx)(Button, { variant: "secondary", size: "small", children: t2("actions.cancel") }) }),
          (0, import_jsx_runtime2.jsx)(
            Button,
            {
              isLoading: isPending,
              type: "submit",
              variant: "primary",
              size: "small",
              disabled: !!Object.keys(form.formState.errors || {}).length,
              children: t2("actions.save")
            }
          )
        ] }) })
      ]
    }
  ) });
}
var OrderRequestTransfer = () => {
  const { t: t2 } = useTranslation();
  const params = useParams();
  const orderId = params.order_id || params.id;
  const { order, isPending, isError } = useOrder(orderId, {
    fields: DEFAULT_FIELDS
  });
  if (!isPending && isError) {
    throw new Error("Order not found");
  }
  return (0, import_jsx_runtime3.jsxs)(RouteDrawer, { children: [
    (0, import_jsx_runtime3.jsx)(RouteDrawer.Header, { children: (0, import_jsx_runtime3.jsx)(Heading, { children: t2("orders.transfer.title") }) }),
    order && (0, import_jsx_runtime3.jsx)(CreateOrderTransferForm, { order })
  ] });
};
export {
  OrderRequestTransfer as Component
};
//# sourceMappingURL=order-request-transfer-BESJQ4LK-HI3SXQ72.js.map
