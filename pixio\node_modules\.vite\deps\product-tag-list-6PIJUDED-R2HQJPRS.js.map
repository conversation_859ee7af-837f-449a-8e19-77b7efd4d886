{"version": 3, "sources": ["../../@medusajs/dashboard/dist/product-tag-list-6PIJUDED.mjs"], "sourcesContent": ["import {\n  useDeleteProductTagAction\n} from \"./chunk-SMK3VXN6.mjs\";\nimport {\n  useProductTagTableColumns\n} from \"./chunk-R2HUTVC3.mjs\";\nimport \"./chunk-QBVCETMI.mjs\";\nimport \"./chunk-QMRGIWOP.mjs\";\nimport \"./chunk-44JF4VLS.mjs\";\nimport \"./chunk-I3VB6NM2.mjs\";\nimport \"./chunk-ZJRFL6ZN.mjs\";\nimport {\n  useProductTagTableQuery\n} from \"./chunk-DFA6WGYO.mjs\";\nimport \"./chunk-I5HYE2RW.mjs\";\nimport \"./chunk-RIV7FKGN.mjs\";\nimport \"./chunk-TDK3JDOB.mjs\";\nimport \"./chunk-FHSC5X62.mjs\";\nimport \"./chunk-XR4GEMGR.mjs\";\nimport \"./chunk-WRSGHGAT.mjs\";\nimport \"./chunk-MOSRJHJ3.mjs\";\nimport \"./chunk-BFAYZKJV.mjs\";\nimport \"./chunk-OMC5JCQH.mjs\";\nimport \"./chunk-RORIX3PU.mjs\";\nimport \"./chunk-XMAWMECC.mjs\";\nimport \"./chunk-5HNRTDDS.mjs\";\nimport \"./chunk-NNBHHXXN.mjs\";\nimport \"./chunk-IR5DHEKS.mjs\";\nimport \"./chunk-7DXVXBSA.mjs\";\nimport \"./chunk-PDWBYQOW.mjs\";\nimport \"./chunk-MSDRGCRR.mjs\";\nimport \"./chunk-G3QXMPRB.mjs\";\nimport \"./chunk-PCFUZKDS.mjs\";\nimport \"./chunk-IQBAUTU5.mjs\";\nimport \"./chunk-ADOCJB6L.mjs\";\nimport \"./chunk-LQTHYS2Z.mjs\";\nimport \"./chunk-P3UUX2T6.mjs\";\nimport {\n  _DataTable,\n  useDataTable\n} from \"./chunk-UE6PO4FK.mjs\";\nimport \"./chunk-YEDAFXMB.mjs\";\nimport \"./chunk-AOFGTNG6.mjs\";\nimport \"./chunk-EMIHDNB7.mjs\";\nimport \"./chunk-MWVM4TYO.mjs\";\nimport {\n  useProductTagTableFilters\n} from \"./chunk-GW6TVOAA.mjs\";\nimport \"./chunk-CBSCX7RE.mjs\";\nimport \"./chunk-LT4MVCA7.mjs\";\nimport \"./chunk-A2UMBW3V.mjs\";\nimport \"./chunk-W7625H47.mjs\";\nimport \"./chunk-DLZWPHHO.mjs\";\nimport \"./chunk-LSEYENCI.mjs\";\nimport \"./chunk-FVK4ZYYM.mjs\";\nimport \"./chunk-FZRIVT5D.mjs\";\nimport \"./chunk-SXYXTC2L.mjs\";\nimport \"./chunk-M3VFKDXJ.mjs\";\nimport {\n  SingleColumnPage\n} from \"./chunk-2RQLKDBF.mjs\";\nimport \"./chunk-C76H5USB.mjs\";\nimport \"./chunk-KSV3NQOT.mjs\";\nimport \"./chunk-DG7J63J2.mjs\";\nimport \"./chunk-MNXC6Q4F.mjs\";\nimport {\n  useExtension\n} from \"./chunk-C5P5PL3E.mjs\";\nimport \"./chunk-LPEUYMRK.mjs\";\nimport {\n  ActionMenu\n} from \"./chunk-3NJTXRIY.mjs\";\nimport \"./chunk-OC7BQLYI.mjs\";\nimport \"./chunk-NYXYHZE6.mjs\";\nimport {\n  productTagsQueryKeys,\n  useProductTags\n} from \"./chunk-Z5UDPQIH.mjs\";\nimport \"./chunk-KOSCMAIC.mjs\";\nimport \"./chunk-X6DSNTTX.mjs\";\nimport \"./chunk-I6E6CALJ.mjs\";\nimport \"./chunk-B4GODIOW.mjs\";\nimport \"./chunk-F6IJV2I2.mjs\";\nimport \"./chunk-QTCZFYFH.mjs\";\nimport \"./chunk-ENV6YVOM.mjs\";\nimport \"./chunk-PIR2H25N.mjs\";\nimport \"./chunk-RLY2SL5E.mjs\";\nimport \"./chunk-C5LYZZZ5.mjs\";\nimport \"./chunk-2ZKVRTBW.mjs\";\nimport \"./chunk-FO3VP56P.mjs\";\nimport \"./chunk-YS65UGPC.mjs\";\nimport \"./chunk-F6PXCY3N.mjs\";\nimport \"./chunk-3OHH43G6.mjs\";\nimport \"./chunk-G2H6MAK7.mjs\";\nimport \"./chunk-GRT22PE5.mjs\";\nimport \"./chunk-32IQRUVY.mjs\";\nimport \"./chunk-FNYASI54.mjs\";\nimport \"./chunk-FVC7M755.mjs\";\nimport \"./chunk-ZJ3OFMHB.mjs\";\nimport \"./chunk-PNU5HPGY.mjs\";\nimport \"./chunk-V2LANK5S.mjs\";\nimport \"./chunk-QZ6PT4QV.mjs\";\nimport \"./chunk-QL4XKIVL.mjs\";\nimport \"./chunk-6I62UDJA.mjs\";\nimport {\n  queryClient\n} from \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport {\n  sdk\n} from \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-RPUOO7AV.mjs\";\n\n// src/routes/product-tags/product-tag-list/loader.ts\nvar productTagListQuery = (query) => ({\n  queryKey: productTagsQueryKeys.list(query),\n  queryFn: async () => sdk.admin.productTag.list(query)\n});\nvar productTagListLoader = async ({ request }) => {\n  const searchParams = new URL(request.url).searchParams;\n  const queryObject = {};\n  searchParams.forEach((value, key) => {\n    try {\n      queryObject[key] = JSON.parse(value);\n    } catch (_e) {\n      queryObject[key] = value;\n    }\n  });\n  const query = productTagListQuery(\n    queryObject\n  );\n  return queryClient.getQueryData(query.queryKey) ?? await queryClient.fetchQuery(query);\n};\n\n// src/routes/product-tags/product-tag-list/components/product-tag-list-table/product-tag-list-table.tsx\nimport { PencilSquare, Trash } from \"@medusajs/icons\";\nimport { Button, Container, Heading } from \"@medusajs/ui\";\nimport { keepPreviousData } from \"@tanstack/react-query\";\nimport { createColumnHelper } from \"@tanstack/react-table\";\nimport { useMemo } from \"react\";\nimport { useTranslation } from \"react-i18next\";\nimport { Link, useLoaderData } from \"react-router-dom\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar PAGE_SIZE = 20;\nvar ProductTagListTable = () => {\n  const { t } = useTranslation();\n  const { searchParams, raw } = useProductTagTableQuery({\n    pageSize: PAGE_SIZE\n  });\n  const initialData = useLoaderData();\n  const { product_tags, count, isPending, isError, error } = useProductTags(\n    searchParams,\n    {\n      initialData,\n      placeholderData: keepPreviousData\n    }\n  );\n  const columns = useColumns();\n  const filters = useProductTagTableFilters();\n  const { table } = useDataTable({\n    data: product_tags,\n    count,\n    columns,\n    getRowId: (row) => row.id,\n    pageSize: PAGE_SIZE\n  });\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsxs(Container, { className: \"divide-y px-0 py-0\", children: [\n    /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center justify-between px-6 py-4\", children: [\n      /* @__PURE__ */ jsx(Heading, { children: t(\"productTags.domain\") }),\n      /* @__PURE__ */ jsx(Button, { variant: \"secondary\", size: \"small\", asChild: true, children: /* @__PURE__ */ jsx(Link, { to: \"create\", children: t(\"actions.create\") }) })\n    ] }),\n    /* @__PURE__ */ jsx(\n      _DataTable,\n      {\n        table,\n        filters,\n        queryObject: raw,\n        isLoading: isPending,\n        columns,\n        pageSize: PAGE_SIZE,\n        count,\n        navigateTo: (row) => row.original.id,\n        search: true,\n        pagination: true,\n        orderBy: [\n          { key: \"value\", label: t(\"fields.value\") },\n          { key: \"created_at\", label: t(\"fields.createdAt\") },\n          { key: \"updated_at\", label: t(\"fields.updatedAt\") }\n        ]\n      }\n    )\n  ] });\n};\nvar ProductTagRowActions = ({\n  productTag\n}) => {\n  const { t } = useTranslation();\n  const handleDelete = useDeleteProductTagAction({ productTag });\n  return /* @__PURE__ */ jsx(\n    ActionMenu,\n    {\n      groups: [\n        {\n          actions: [\n            {\n              icon: /* @__PURE__ */ jsx(PencilSquare, {}),\n              label: t(\"actions.edit\"),\n              to: `${productTag.id}/edit`\n            }\n          ]\n        },\n        {\n          actions: [\n            {\n              icon: /* @__PURE__ */ jsx(Trash, {}),\n              label: t(\"actions.delete\"),\n              onClick: handleDelete\n            }\n          ]\n        }\n      ]\n    }\n  );\n};\nvar columnHelper = createColumnHelper();\nvar useColumns = () => {\n  const base = useProductTagTableColumns();\n  return useMemo(\n    () => [\n      ...base,\n      columnHelper.display({\n        id: \"actions\",\n        cell: ({ row }) => /* @__PURE__ */ jsx(ProductTagRowActions, { productTag: row.original })\n      })\n    ],\n    [base]\n  );\n};\n\n// src/routes/product-tags/product-tag-list/product-tag-list.tsx\nimport { jsx as jsx2 } from \"react/jsx-runtime\";\nvar ProductTagList = () => {\n  const { getWidgets } = useExtension();\n  return /* @__PURE__ */ jsx2(\n    SingleColumnPage,\n    {\n      showMetadata: false,\n      showJSON: false,\n      hasOutlet: true,\n      widgets: {\n        after: getWidgets(\"product_tag.list.after\"),\n        before: getWidgets(\"product_tag.list.before\")\n      },\n      children: /* @__PURE__ */ jsx2(ProductTagListTable, {})\n    }\n  );\n};\nexport {\n  ProductTagList as Component,\n  productTagListLoader as loader\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2IA,mBAAwB;AAGxB,yBAA0B;AAqG1B,IAAAA,sBAA4B;AAjI5B,IAAI,sBAAsB,CAAC,WAAW;AAAA,EACpC,UAAU,qBAAqB,KAAK,KAAK;AAAA,EACzC,SAAS,YAAY,IAAI,MAAM,WAAW,KAAK,KAAK;AACtD;AACA,IAAI,uBAAuB,OAAO,EAAE,QAAQ,MAAM;AAChD,QAAM,eAAe,IAAI,IAAI,QAAQ,GAAG,EAAE;AAC1C,QAAM,cAAc,CAAC;AACrB,eAAa,QAAQ,CAAC,OAAO,QAAQ;AACnC,QAAI;AACF,kBAAY,GAAG,IAAI,KAAK,MAAM,KAAK;AAAA,IACrC,SAAS,IAAI;AACX,kBAAY,GAAG,IAAI;AAAA,IACrB;AAAA,EACF,CAAC;AACD,QAAM,QAAQ;AAAA,IACZ;AAAA,EACF;AACA,SAAO,YAAY,aAAa,MAAM,QAAQ,KAAK,MAAM,YAAY,WAAW,KAAK;AACvF;AAWA,IAAI,YAAY;AAChB,IAAI,sBAAsB,MAAM;AAC9B,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,QAAM,EAAE,cAAc,IAAI,IAAI,wBAAwB;AAAA,IACpD,UAAU;AAAA,EACZ,CAAC;AACD,QAAM,cAAc,cAAc;AAClC,QAAM,EAAE,cAAc,OAAO,WAAW,SAAS,MAAM,IAAI;AAAA,IACzD;AAAA,IACA;AAAA,MACE;AAAA,MACA,iBAAiB;AAAA,IACnB;AAAA,EACF;AACA,QAAM,UAAU,WAAW;AAC3B,QAAM,UAAU,0BAA0B;AAC1C,QAAM,EAAE,MAAM,IAAI,aAAa;AAAA,IAC7B,MAAM;AAAA,IACN;AAAA,IACA;AAAA,IACA,UAAU,CAAC,QAAQ,IAAI;AAAA,IACvB,UAAU;AAAA,EACZ,CAAC;AACD,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,yBAAK,WAAW,EAAE,WAAW,sBAAsB,UAAU;AAAA,QAClE,yBAAK,OAAO,EAAE,WAAW,+CAA+C,UAAU;AAAA,UAChF,wBAAI,SAAS,EAAE,UAAU,EAAE,oBAAoB,EAAE,CAAC;AAAA,UAClD,wBAAI,QAAQ,EAAE,SAAS,aAAa,MAAM,SAAS,SAAS,MAAM,cAA0B,wBAAI,MAAM,EAAE,IAAI,UAAU,UAAU,EAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;AAAA,IAC1K,EAAE,CAAC;AAAA,QACa;AAAA,MACd;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,QACA,aAAa;AAAA,QACb,WAAW;AAAA,QACX;AAAA,QACA,UAAU;AAAA,QACV;AAAA,QACA,YAAY,CAAC,QAAQ,IAAI,SAAS;AAAA,QAClC,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,SAAS;AAAA,UACP,EAAE,KAAK,SAAS,OAAO,EAAE,cAAc,EAAE;AAAA,UACzC,EAAE,KAAK,cAAc,OAAO,EAAE,kBAAkB,EAAE;AAAA,UAClD,EAAE,KAAK,cAAc,OAAO,EAAE,kBAAkB,EAAE;AAAA,QACpD;AAAA,MACF;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;AACA,IAAI,uBAAuB,CAAC;AAAA,EAC1B;AACF,MAAM;AACJ,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,QAAM,eAAe,0BAA0B,EAAE,WAAW,CAAC;AAC7D,aAAuB;AAAA,IACrB;AAAA,IACA;AAAA,MACE,QAAQ;AAAA,QACN;AAAA,UACE,SAAS;AAAA,YACP;AAAA,cACE,UAAsB,wBAAI,cAAc,CAAC,CAAC;AAAA,cAC1C,OAAO,EAAE,cAAc;AAAA,cACvB,IAAI,GAAG,WAAW,EAAE;AAAA,YACtB;AAAA,UACF;AAAA,QACF;AAAA,QACA;AAAA,UACE,SAAS;AAAA,YACP;AAAA,cACE,UAAsB,wBAAI,OAAO,CAAC,CAAC;AAAA,cACnC,OAAO,EAAE,gBAAgB;AAAA,cACzB,SAAS;AAAA,YACX;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAI,eAAe,mBAAmB;AACtC,IAAI,aAAa,MAAM;AACrB,QAAM,OAAO,0BAA0B;AACvC,aAAO;AAAA,IACL,MAAM;AAAA,MACJ,GAAG;AAAA,MACH,aAAa,QAAQ;AAAA,QACnB,IAAI;AAAA,QACJ,MAAM,CAAC,EAAE,IAAI,UAAsB,wBAAI,sBAAsB,EAAE,YAAY,IAAI,SAAS,CAAC;AAAA,MAC3F,CAAC;AAAA,IACH;AAAA,IACA,CAAC,IAAI;AAAA,EACP;AACF;AAIA,IAAI,iBAAiB,MAAM;AACzB,QAAM,EAAE,WAAW,IAAI,aAAa;AACpC,aAAuB,oBAAAC;AAAA,IACrB;AAAA,IACA;AAAA,MACE,cAAc;AAAA,MACd,UAAU;AAAA,MACV,WAAW;AAAA,MACX,SAAS;AAAA,QACP,OAAO,WAAW,wBAAwB;AAAA,QAC1C,QAAQ,WAAW,yBAAyB;AAAA,MAC9C;AAAA,MACA,cAA0B,oBAAAA,KAAK,qBAAqB,CAAC,CAAC;AAAA,IACxD;AAAA,EACF;AACF;", "names": ["import_jsx_runtime", "jsx2"]}