import {
  useDeleteProductTagAction
} from "./chunk-KGZNQ46H.js";
import "./chunk-U3YCCVIX.js";
import "./chunk-J7NC22T4.js";
import "./chunk-UEYKZWIS.js";
import "./chunk-5BDJR5GO.js";
import "./chunk-IP6YNP6I.js";
import "./chunk-EHZUZSWH.js";
import "./chunk-TPBCRBDT.js";
import "./chunk-GDXEFZZY.js";
import "./chunk-GEC36FCE.js";
import "./chunk-ITWRYKT3.js";
import "./chunk-AYBSQXJR.js";
import "./chunk-ASBI7JIX.js";
import "./chunk-QG4LHCCG.js";
import "./chunk-I2ZOQM4X.js";
import "./chunk-EGZX7QZE.js";
import "./chunk-I45JH6GR.js";
import "./chunk-4FV466FW.js";
import "./chunk-QF476XOZ.js";
import "./chunk-MKZD3R7Z.js";
import "./chunk-ZJX5R5NM.js";
import "./chunk-LVAKEKGS.js";
import "./chunk-5ZQBU3TD.js";
import "./chunk-5AXVXNEZ.js";
import "./chunk-C43B7AQX.js";
import "./chunk-UDMOPZAP.js";
import {
  useProductTableColumns
} from "./chunk-7WCGWU4N.js";
import {
  useProductTableQuery
} from "./chunk-3M3PHA2D.js";
import "./chunk-NVCSASGM.js";
import "./chunk-OVCKROM5.js";
import "./chunk-EGRHWZRV.js";
import "./chunk-S5JEKJNE.js";
import {
  _DataTable,
  useDataTable
} from "./chunk-VCX3BVQR.js";
import "./chunk-VCBFQV64.js";
import "./chunk-QLJZR2JY.js";
import "./chunk-X3TOWPPJ.js";
import {
  SingleColumnPage
} from "./chunk-3LNIL4XX.js";
import "./chunk-H3DTEG3J.js";
import "./chunk-32T72GVU.js";
import "./chunk-3LRISSP5.js";
import "./chunk-P5T2IZP5.js";
import "./chunk-I7242KR3.js";
import "./chunk-EB3HY52D.js";
import "./chunk-IONS3C54.js";
import "./chunk-XNFM7P3M.js";
import "./chunk-ZIXJCBL3.js";
import "./chunk-2TO4KOWC.js";
import {
  useProductTableFilters
} from "./chunk-4VJTA5FM.js";
import "./chunk-MX43XOWY.js";
import "./chunk-QX6SXRUW.js";
import "./chunk-7ANVLPZR.js";
import "./chunk-2E2FUO6N.js";
import "./chunk-HPGXK5DQ.js";
import "./chunk-6GQUHAET.js";
import {
  useExtension
} from "./chunk-FTD3ZWHZ.js";
import {
  SingleColumnPageSkeleton
} from "./chunk-YXT43UJF.js";
import {
  ActionMenu
} from "./chunk-CFRQOB2M.js";
import "./chunk-66DVUN72.js";
import "./chunk-DL4QDYPT.js";
import "./chunk-NV2N3EWM.js";
import "./chunk-BF7OBKIN.js";
import "./chunk-7UAYECTW.js";
import "./chunk-Y3NYV3NU.js";
import "./chunk-MPXR7HT5.js";
import {
  productTagsQueryKeys,
  useProductTag
} from "./chunk-WISME5HP.js";
import "./chunk-WAUSOHFQ.js";
import "./chunk-3ORK3QZ4.js";
import "./chunk-3A5TVVNI.js";
import "./chunk-EZ62MM7J.js";
import "./chunk-3R6LYIXM.js";
import "./chunk-2P2O4TMQ.js";
import "./chunk-KI3HEITX.js";
import "./chunk-2VUCAO7L.js";
import "./chunk-PTIULTKD.js";
import "./chunk-KISXY2LD.js";
import "./chunk-L7C3XPHJ.js";
import "./chunk-T7MG2EIJ.js";
import "./chunk-MER4VYPR.js";
import "./chunk-2AWKOUCD.js";
import "./chunk-YL3TYBFR.js";
import "./chunk-TZWW72YW.js";
import "./chunk-MSQ25CWB.js";
import "./chunk-ONYSAQ5Z.js";
import "./chunk-NIH7SAXN.js";
import "./chunk-UEJFWAFE.js";
import "./chunk-GR55MFKT.js";
import "./chunk-AAFHKNJG.js";
import "./chunk-7XDBFDTZ.js";
import "./chunk-3JKGO5XL.js";
import "./chunk-OISUTS7G.js";
import {
  useProducts
} from "./chunk-AJYMIHLQ.js";
import {
  queryClient
} from "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import {
  sdk
} from "./chunk-JFCIYOH4.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-5GF3RGIE.js";
import {
  useLoaderData,
  useParams
} from "./chunk-T7YBVUWZ.js";
import {
  Container,
  Heading,
  PencilSquare,
  Trash
} from "./chunk-YP2LLXWB.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-ZSQ362B4.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/product-tag-detail-L5U4A5AW.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime4 = __toESM(require_jsx_runtime(), 1);
var ProductTagDetailBreadcrumb = (props) => {
  const { id } = props.params || {};
  const { product_tag } = useProductTag(id, void 0, {
    initialData: props.data,
    enabled: Boolean(id)
  });
  if (!product_tag) {
    return null;
  }
  return (0, import_jsx_runtime.jsx)("span", { children: product_tag.value });
};
var productTagDetailQuery = (id) => ({
  queryKey: productTagsQueryKeys.detail(id),
  queryFn: async () => sdk.admin.productTag.retrieve(id)
});
var productTagLoader = async ({ params }) => {
  const id = params.id;
  const query = productTagDetailQuery(id);
  return queryClient.ensureQueryData(query);
};
var ProductTagGeneralSection = ({
  productTag
}) => {
  const { t } = useTranslation();
  const handleDelete = useDeleteProductTagAction({ productTag });
  return (0, import_jsx_runtime2.jsxs)(Container, { className: "flex items-center justify-between", children: [
    (0, import_jsx_runtime2.jsxs)("div", { className: "flex items-center gap-x-1.5", children: [
      (0, import_jsx_runtime2.jsx)("span", { className: "text-ui-fg-muted h1-core", children: "#" }),
      (0, import_jsx_runtime2.jsx)(Heading, { children: productTag.value })
    ] }),
    (0, import_jsx_runtime2.jsx)(
      ActionMenu,
      {
        groups: [
          {
            actions: [
              {
                icon: (0, import_jsx_runtime2.jsx)(PencilSquare, {}),
                label: t("actions.edit"),
                to: "edit"
              }
            ]
          },
          {
            actions: [
              {
                icon: (0, import_jsx_runtime2.jsx)(Trash, {}),
                label: t("actions.delete"),
                onClick: handleDelete
              }
            ]
          }
        ]
      }
    )
  ] });
};
var PAGE_SIZE = 10;
var PREFIX = "pt";
var ProductTagProductSection = ({
  productTag
}) => {
  const { t } = useTranslation();
  const { searchParams, raw } = useProductTableQuery({
    pageSize: PAGE_SIZE,
    prefix: PREFIX
  });
  const { products, count, isPending, isError, error } = useProducts({
    ...searchParams,
    tag_id: productTag.id
  });
  const filters = useProductTableFilters(["product_tags"]);
  const columns = useProductTableColumns();
  const { table } = useDataTable({
    data: products,
    count,
    columns,
    getRowId: (row) => row.id,
    pageSize: PAGE_SIZE,
    prefix: PREFIX
  });
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime3.jsxs)(Container, { className: "divide-y px-0 py-0", children: [
    (0, import_jsx_runtime3.jsx)("div", { className: "px-6 py-4", children: (0, import_jsx_runtime3.jsx)(Heading, { level: "h2", children: t("products.domain") }) }),
    (0, import_jsx_runtime3.jsx)(
      _DataTable,
      {
        table,
        filters,
        queryObject: raw,
        isLoading: isPending,
        columns,
        pageSize: PAGE_SIZE,
        count,
        navigateTo: (row) => `/products/${row.original.id}`,
        search: true,
        pagination: true,
        orderBy: [
          { key: "title", label: t("fields.title") },
          { key: "status", label: t("fields.status") },
          { key: "created_at", label: t("fields.createdAt") },
          { key: "updated_at", label: t("fields.updatedAt") }
        ]
      }
    )
  ] });
};
var ProductTagDetail = () => {
  const { id } = useParams();
  const initialData = useLoaderData();
  const { getWidgets } = useExtension();
  const { product_tag, isPending, isError, error } = useProductTag(
    id,
    void 0,
    {
      initialData
    }
  );
  if (isPending || !product_tag) {
    return (0, import_jsx_runtime4.jsx)(SingleColumnPageSkeleton, { showJSON: true, sections: 2 });
  }
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime4.jsxs)(
    SingleColumnPage,
    {
      widgets: {
        after: getWidgets("product_tag.details.after"),
        before: getWidgets("product_tag.details.before")
      },
      showJSON: true,
      data: product_tag,
      children: [
        (0, import_jsx_runtime4.jsx)(ProductTagGeneralSection, { productTag: product_tag }),
        (0, import_jsx_runtime4.jsx)(ProductTagProductSection, { productTag: product_tag })
      ]
    }
  );
};
export {
  ProductTagDetailBreadcrumb as Breadcrumb,
  ProductTagDetail as Component,
  productTagLoader as loader
};
//# sourceMappingURL=product-tag-detail-L5U4A5AW-Q5XA63KN.js.map
