import {
  get<PERSON><PERSON><PERSON><PERSON><PERSON>
} from "./chunk-UDMOPZAP.js";
import {
  LinkButton
} from "./chunk-MOY5ZEOS.js";
import {
  VARIANT_DETAIL_FIELDS
} from "./chunk-VPOHTWLW.js";
import "./chunk-EGRHWZRV.js";
import {
  SectionRow
} from "./chunk-A5MCDQUY.js";
import "./chunk-EGRHWZRV.js";
import {
  PlaceholderCell
} from "./chunk-S5JEKJNE.js";
import {
  _DataTable,
  useDataTable
} from "./chunk-VCX3BVQR.js";
import "./chunk-VCBFQV64.js";
import "./chunk-QLJZR2JY.js";
import {
  NoRecords
} from "./chunk-X3TOWPPJ.js";
import {
  TwoColumnPage
} from "./chunk-3LNIL4XX.js";
import "./chunk-H3DTEG3J.js";
import "./chunk-MX43XOWY.js";
import "./chunk-QX6SXRUW.js";
import "./chunk-7ANVLPZR.js";
import "./chunk-2E2FUO6N.js";
import {
  useExtension
} from "./chunk-FTD3ZWHZ.js";
import {
  TwoColumnPageSkeleton
} from "./chunk-YXT43UJF.js";
import {
  ActionMenu
} from "./chunk-CFRQOB2M.js";
import "./chunk-66DVUN72.js";
import "./chunk-DL4QDYPT.js";
import "./chunk-NV2N3EWM.js";
import "./chunk-BF7OBKIN.js";
import "./chunk-7UAYECTW.js";
import "./chunk-Y3NYV3NU.js";
import "./chunk-MPXR7HT5.js";
import "./chunk-WISME5HP.js";
import "./chunk-WAUSOHFQ.js";
import "./chunk-3ORK3QZ4.js";
import "./chunk-3A5TVVNI.js";
import "./chunk-EZ62MM7J.js";
import "./chunk-3R6LYIXM.js";
import "./chunk-2P2O4TMQ.js";
import "./chunk-KI3HEITX.js";
import "./chunk-2VUCAO7L.js";
import "./chunk-PTIULTKD.js";
import "./chunk-KISXY2LD.js";
import "./chunk-L7C3XPHJ.js";
import "./chunk-T7MG2EIJ.js";
import "./chunk-MER4VYPR.js";
import "./chunk-2AWKOUCD.js";
import "./chunk-YL3TYBFR.js";
import "./chunk-TZWW72YW.js";
import "./chunk-MSQ25CWB.js";
import "./chunk-ONYSAQ5Z.js";
import "./chunk-NIH7SAXN.js";
import "./chunk-UEJFWAFE.js";
import "./chunk-GR55MFKT.js";
import "./chunk-AAFHKNJG.js";
import "./chunk-7XDBFDTZ.js";
import "./chunk-3JKGO5XL.js";
import "./chunk-OISUTS7G.js";
import {
  useDeleteVariant,
  useProductVariant,
  variantsQueryKeys
} from "./chunk-AJYMIHLQ.js";
import {
  queryClient
} from "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import {
  sdk
} from "./chunk-JFCIYOH4.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-5GF3RGIE.js";
import {
  useLoaderData,
  useNavigate,
  useParams
} from "./chunk-T7YBVUWZ.js";
import {
  Badge,
  Buildings,
  Button,
  Component,
  Container,
  CurrencyDollar,
  Heading,
  PencilSquare,
  Trash,
  createColumnHelper,
  usePrompt
} from "./chunk-YP2LLXWB.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-ZSQ362B4.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/product-variant-detail-JMGSGVUK.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var import_react = __toESM(require_react(), 1);
var import_jsx_runtime4 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime5 = __toESM(require_jsx_runtime(), 1);
var import_react2 = __toESM(require_react(), 1);
var import_jsx_runtime6 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime7 = __toESM(require_jsx_runtime(), 1);
var ProductVariantDetailBreadcrumb = (props) => {
  const { id, variant_id } = props.params || {};
  const { variant } = useProductVariant(
    id,
    variant_id,
    {
      fields: VARIANT_DETAIL_FIELDS
    },
    {
      initialData: props.data,
      enabled: Boolean(id) && Boolean(variant_id)
    }
  );
  if (!variant) {
    return null;
  }
  return (0, import_jsx_runtime.jsx)("span", { children: variant.title });
};
var variantDetailQuery = (productId, variantId) => ({
  queryKey: variantsQueryKeys.detail(variantId, {
    fields: VARIANT_DETAIL_FIELDS
  }),
  queryFn: async () => sdk.admin.product.retrieveVariant(productId, variantId, {
    fields: VARIANT_DETAIL_FIELDS
  })
});
var variantLoader = async ({ params }) => {
  const productId = params.id;
  const variantId = params.variant_id;
  const query = variantDetailQuery(productId, variantId);
  return queryClient.ensureQueryData(query);
};
function VariantGeneralSection({ variant }) {
  var _a, _b;
  const { t } = useTranslation();
  const prompt = usePrompt();
  const navigate = useNavigate();
  const hasInventoryKit = ((_a = variant.inventory) == null ? void 0 : _a.length) > 1;
  const { mutateAsync } = useDeleteVariant(variant.product_id, variant.id);
  const handleDelete = async () => {
    const res = await prompt({
      title: t("general.areYouSure"),
      description: t("products.variant.deleteWarning", {
        title: variant.title
      }),
      confirmText: t("actions.delete"),
      cancelText: t("actions.cancel")
    });
    if (!res) {
      return;
    }
    await mutateAsync(void 0, {
      onSuccess: () => {
        navigate("..", { replace: true });
      }
    });
  };
  return (0, import_jsx_runtime2.jsxs)(Container, { className: "divide-y p-0", children: [
    (0, import_jsx_runtime2.jsxs)("div", { className: "flex items-center justify-between px-6 py-4", children: [
      (0, import_jsx_runtime2.jsxs)("div", { children: [
        (0, import_jsx_runtime2.jsxs)("div", { className: "flex items-center gap-2", children: [
          (0, import_jsx_runtime2.jsx)(Heading, { children: variant.title }),
          hasInventoryKit && (0, import_jsx_runtime2.jsx)("span", { className: "text-ui-fg-muted font-normal", children: (0, import_jsx_runtime2.jsx)(Component, {}) })
        ] }),
        (0, import_jsx_runtime2.jsx)("span", { className: "text-ui-fg-subtle txt-small mt-2", children: t("labels.productVariant") })
      ] }),
      (0, import_jsx_runtime2.jsx)("div", { className: "flex items-center gap-x-4", children: (0, import_jsx_runtime2.jsx)(
        ActionMenu,
        {
          groups: [
            {
              actions: [
                {
                  label: t("actions.edit"),
                  to: "edit",
                  icon: (0, import_jsx_runtime2.jsx)(PencilSquare, {})
                }
              ]
            },
            {
              actions: [
                {
                  label: t("actions.delete"),
                  onClick: handleDelete,
                  icon: (0, import_jsx_runtime2.jsx)(Trash, {})
                }
              ]
            }
          ]
        }
      ) })
    ] }),
    (0, import_jsx_runtime2.jsx)(SectionRow, { title: t("fields.sku"), value: variant.sku }),
    (_b = variant.options) == null ? void 0 : _b.map((o) => {
      var _a2;
      return (0, import_jsx_runtime2.jsx)(
        SectionRow,
        {
          title: (_a2 = o.option) == null ? void 0 : _a2.title,
          value: (0, import_jsx_runtime2.jsx)(Badge, { size: "2xsmall", children: o.value })
        },
        o.id
      );
    })
  ] });
}
var InventoryActions = ({ item }) => {
  const { t } = useTranslation();
  return (0, import_jsx_runtime3.jsx)(
    ActionMenu,
    {
      groups: [
        {
          actions: [
            {
              icon: (0, import_jsx_runtime3.jsx)(Buildings, {}),
              label: t("products.variant.inventory.navigateToItem"),
              to: `/inventory/${item.id}`
            }
          ]
        }
      ]
    }
  );
};
var columnHelper = createColumnHelper();
var useInventoryTableColumns = () => {
  const { t } = useTranslation();
  return (0, import_react.useMemo)(
    () => [
      columnHelper.accessor("title", {
        header: t("fields.title"),
        cell: ({ getValue }) => {
          const title = getValue();
          if (!title) {
            return (0, import_jsx_runtime4.jsx)(PlaceholderCell, {});
          }
          return (0, import_jsx_runtime4.jsx)("div", { className: "flex size-full items-center overflow-hidden", children: (0, import_jsx_runtime4.jsx)("span", { className: "truncate", children: title }) });
        }
      }),
      columnHelper.accessor("sku", {
        header: t("fields.sku"),
        cell: ({ getValue }) => {
          const sku = getValue();
          if (!sku) {
            return (0, import_jsx_runtime4.jsx)(PlaceholderCell, {});
          }
          return (0, import_jsx_runtime4.jsx)("div", { className: "flex size-full items-center overflow-hidden", children: (0, import_jsx_runtime4.jsx)("span", { className: "truncate", children: sku }) });
        }
      }),
      columnHelper.accessor("required_quantity", {
        header: t("fields.requiredQuantity"),
        cell: ({ getValue }) => {
          const quantity = getValue();
          if (Number.isNaN(quantity)) {
            return (0, import_jsx_runtime4.jsx)(PlaceholderCell, {});
          }
          return (0, import_jsx_runtime4.jsx)("div", { className: "flex size-full items-center overflow-hidden", children: (0, import_jsx_runtime4.jsx)("span", { className: "truncate", children: quantity }) });
        }
      }),
      columnHelper.display({
        id: "inventory_quantity",
        header: t("fields.inventory"),
        cell: ({ getValue, row: { original: inventory } }) => {
          var _a;
          if (!((_a = inventory.location_levels) == null ? void 0 : _a.length)) {
            return (0, import_jsx_runtime4.jsx)(PlaceholderCell, {});
          }
          let quantity = 0;
          let locations = 0;
          inventory.location_levels.forEach((level) => {
            quantity += level.available_quantity;
            locations += 1;
          });
          return (0, import_jsx_runtime4.jsx)("div", { className: "flex size-full items-center overflow-hidden", children: (0, import_jsx_runtime4.jsx)("span", { className: "truncate", children: t("products.variant.tableItem", {
            availableCount: quantity,
            locationCount: locations,
            count: locations
          }) }) });
        }
      }),
      columnHelper.display({
        id: "actions",
        cell: ({ row }) => (0, import_jsx_runtime4.jsx)(InventoryActions, { item: row.original })
      })
    ],
    [t]
  );
};
var PAGE_SIZE = 20;
function VariantInventorySection({
  inventoryItems
}) {
  const { t } = useTranslation();
  const columns = useInventoryTableColumns();
  const { table } = useDataTable({
    data: inventoryItems ?? [],
    columns,
    count: inventoryItems.length,
    enablePagination: true,
    getRowId: (row) => row.id,
    pageSize: PAGE_SIZE
  });
  const hasKit = inventoryItems.length > 1;
  return (0, import_jsx_runtime5.jsxs)(Container, { className: "divide-y p-0", children: [
    (0, import_jsx_runtime5.jsxs)("div", { className: "flex items-center justify-between px-6 py-4", children: [
      (0, import_jsx_runtime5.jsx)("div", { className: "flex items-center gap-2", children: (0, import_jsx_runtime5.jsx)(Heading, { level: "h2", children: t("fields.inventoryItems") }) }),
      (0, import_jsx_runtime5.jsx)("div", { className: "flex items-center gap-x-4", children: (0, import_jsx_runtime5.jsx)(
        ActionMenu,
        {
          groups: [
            {
              actions: [
                {
                  label: t(
                    hasKit ? "products.variant.inventory.manageKit" : "products.variant.inventory.manageItems"
                  ),
                  to: "manage-items",
                  icon: hasKit ? (0, import_jsx_runtime5.jsx)(Component, {}) : (0, import_jsx_runtime5.jsx)(Buildings, {})
                }
              ]
            }
          ]
        }
      ) })
    ] }),
    (0, import_jsx_runtime5.jsx)(
      _DataTable,
      {
        table,
        columns,
        pageSize: PAGE_SIZE,
        count: inventoryItems.length,
        navigateTo: (row) => `/inventory/${row.id}`
      }
    )
  ] });
}
function InventorySectionPlaceholder() {
  const { t } = useTranslation();
  return (0, import_jsx_runtime5.jsx)(Container, { className: "divide-y p-0", children: (0, import_jsx_runtime5.jsxs)("div", { className: "flex items-center justify-between px-6 py-4", children: [
    (0, import_jsx_runtime5.jsxs)("div", { className: "flex flex-col gap-1", children: [
      (0, import_jsx_runtime5.jsx)(Heading, { level: "h2", children: t("fields.inventoryItems") }),
      (0, import_jsx_runtime5.jsx)("span", { className: "txt-small text-ui-fg-subtle", children: t("products.variant.inventory.notManagedDesc") })
    ] }),
    (0, import_jsx_runtime5.jsx)("div", { className: "flex items-center gap-x-4", children: (0, import_jsx_runtime5.jsx)(LinkButton, { to: "edit", children: t("products.variant.edit.header") }) })
  ] }) });
}
function VariantPricesSection({ variant }) {
  var _a;
  const { t } = useTranslation();
  const prices = (_a = variant.prices) == null ? void 0 : _a.filter((p) => !Object.keys(p.rules || {}).length).sort((p1, p2) => {
    var _a2;
    return (_a2 = p1.currency_code) == null ? void 0 : _a2.localeCompare(p2.currency_code);
  });
  const hasPrices = !!(prices == null ? void 0 : prices.length);
  const [pageSize, setPageSize] = (0, import_react2.useState)(3);
  const displayPrices = prices == null ? void 0 : prices.slice(0, pageSize);
  const onShowMore = () => {
    setPageSize(pageSize + 3);
  };
  return (0, import_jsx_runtime6.jsxs)(Container, { className: "flex flex-col divide-y p-0", children: [
    (0, import_jsx_runtime6.jsxs)("div", { className: "flex items-center justify-between px-6 py-4", children: [
      (0, import_jsx_runtime6.jsx)(Heading, { level: "h2", children: t("labels.prices") }),
      (0, import_jsx_runtime6.jsx)(
        ActionMenu,
        {
          groups: [
            {
              actions: [
                {
                  label: t("actions.edit"),
                  to: `/products/${variant.product_id}/variants/${variant.id}/prices`,
                  icon: (0, import_jsx_runtime6.jsx)(CurrencyDollar, {})
                }
              ]
            }
          ]
        }
      )
    ] }),
    !hasPrices && (0, import_jsx_runtime6.jsx)(NoRecords, { className: "h-60" }),
    displayPrices == null ? void 0 : displayPrices.map((price) => {
      return (0, import_jsx_runtime6.jsxs)(
        "div",
        {
          className: "txt-small text-ui-fg-subtle flex justify-between px-6 py-4",
          children: [
            (0, import_jsx_runtime6.jsx)("span", { className: "font-medium", children: price.currency_code.toUpperCase() }),
            (0, import_jsx_runtime6.jsx)("span", { children: getLocaleAmount(price.amount, price.currency_code) })
          ]
        },
        price.id
      );
    }),
    hasPrices && (0, import_jsx_runtime6.jsxs)("div", { className: "txt-small text-ui-fg-subtle flex items-center justify-between px-6 py-4", children: [
      (0, import_jsx_runtime6.jsx)("span", { className: "font-medium", children: t("products.variant.pricesPagination", {
        total: prices.length,
        current: Math.min(pageSize, prices.length)
      }) }),
      (0, import_jsx_runtime6.jsx)(
        Button,
        {
          onClick: onShowMore,
          disabled: pageSize >= prices.length,
          className: "-mr-3 text-blue-500",
          variant: "transparent",
          children: t("actions.showMore")
        }
      )
    ] })
  ] });
}
var ProductVariantDetail = () => {
  const initialData = useLoaderData();
  const { id, variant_id } = useParams();
  const { variant, isLoading, isError, error } = useProductVariant(
    id,
    variant_id,
    { fields: VARIANT_DETAIL_FIELDS },
    {
      initialData
    }
  );
  const { getWidgets } = useExtension();
  if (isLoading || !variant) {
    return (0, import_jsx_runtime7.jsx)(
      TwoColumnPageSkeleton,
      {
        mainSections: 2,
        sidebarSections: 1,
        showJSON: true,
        showMetadata: true
      }
    );
  }
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime7.jsxs)(
    TwoColumnPage,
    {
      data: variant,
      hasOutlet: true,
      showJSON: true,
      showMetadata: true,
      widgets: {
        after: getWidgets("product_variant.details.after"),
        before: getWidgets("product_variant.details.before"),
        sideAfter: getWidgets("product_variant.details.side.after"),
        sideBefore: getWidgets("product_variant.details.side.before")
      },
      children: [
        (0, import_jsx_runtime7.jsxs)(TwoColumnPage.Main, { children: [
          (0, import_jsx_runtime7.jsx)(VariantGeneralSection, { variant }),
          !variant.manage_inventory ? (0, import_jsx_runtime7.jsx)(InventorySectionPlaceholder, {}) : (0, import_jsx_runtime7.jsx)(
            VariantInventorySection,
            {
              inventoryItems: variant.inventory_items.map((i) => {
                return {
                  ...i.inventory,
                  required_quantity: i.required_quantity,
                  variant
                };
              })
            }
          )
        ] }),
        (0, import_jsx_runtime7.jsx)(TwoColumnPage.Sidebar, { children: (0, import_jsx_runtime7.jsx)(VariantPricesSection, { variant }) })
      ]
    }
  );
};
export {
  ProductVariantDetailBreadcrumb as Breadcrumb,
  ProductVariantDetail as Component,
  variantLoader as loader
};
//# sourceMappingURL=product-variant-detail-JMGSGVUK-57MN5V5B.js.map
