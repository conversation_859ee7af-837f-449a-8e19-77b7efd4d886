import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import {
  Button,
  XMarkMini,
  clx
} from "./chunk-YP2LLXWB.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/chunk-X5VECN6S.mjs
var import_react = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var GroupContext = (0, import_react.createContext)(null);
var useGroupContext = () => {
  const context = (0, import_react.useContext)(GroupContext);
  if (!context) {
    throw new Error("useGroupContext must be used within a ChipGroup component");
  }
  return context;
};
var Group = ({
  onClearAll,
  onRemove,
  variant = "component",
  className,
  children
}) => {
  const { t } = useTranslation();
  const showClearAll = !!onClearAll && import_react.Children.count(children) > 0;
  return (0, import_jsx_runtime.jsx)(GroupContext.Provider, { value: { onRemove, variant }, children: (0, import_jsx_runtime.jsxs)(
    "ul",
    {
      role: "application",
      className: clx("flex flex-wrap items-center gap-2", className),
      children: [
        children,
        showClearAll && (0, import_jsx_runtime.jsx)("li", { children: (0, import_jsx_runtime.jsx)(
          Button,
          {
            size: "small",
            variant: "transparent",
            type: "button",
            onClick: onClearAll,
            className: "text-ui-fg-muted active:text-ui-fg-subtle",
            children: t("actions.clearAll")
          }
        ) })
      ]
    }
  ) });
};
var Chip = ({ index, className, children }) => {
  const { onRemove, variant } = useGroupContext();
  return (0, import_jsx_runtime.jsxs)(
    "li",
    {
      className: clx(
        "bg-ui-bg-component shadow-borders-base flex items-stretch divide-x overflow-hidden rounded-md",
        {
          "bg-ui-bg-component": variant === "component",
          "bg-ui-bg-base-": variant === "base"
        },
        className
      ),
      children: [
        (0, import_jsx_runtime.jsx)("span", { className: "txt-compact-small-plus text-ui-fg-subtle flex items-center justify-center px-2 py-1", children }),
        !!onRemove && (0, import_jsx_runtime.jsx)(
          "button",
          {
            onClick: () => onRemove(index),
            type: "button",
            className: clx(
              "text-ui-fg-muted active:text-ui-fg-subtle transition-fg flex items-center justify-center p-1",
              {
                "hover:bg-ui-bg-component-hover active:bg-ui-bg-component-pressed": variant === "component",
                "hover:bg-ui-bg-base-hover active:bg-ui-bg-base-pressed": variant === "base"
              }
            ),
            children: (0, import_jsx_runtime.jsx)(XMarkMini, {})
          }
        )
      ]
    }
  );
};
var ChipGroup = Object.assign(Group, { Chip });

export {
  ChipGroup
};
//# sourceMappingURL=chunk-V2W2XNHI.js.map
