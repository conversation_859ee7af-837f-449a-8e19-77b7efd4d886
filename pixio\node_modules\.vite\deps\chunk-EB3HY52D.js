import {
  useCustomerGroups
} from "./chunk-2AWKOUCD.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";

// node_modules/@medusajs/dashboard/dist/chunk-A2UMBW3V.mjs
var useCustomerTableFilters = (exclude) => {
  const { t } = useTranslation();
  const isGroupsExcluded = exclude == null ? void 0 : exclude.includes("groups");
  const { customer_groups } = useCustomerGroups(
    {
      limit: 1e3
    },
    {
      enabled: !isGroupsExcluded
    }
  );
  let filters = [];
  if (customer_groups && !isGroupsExcluded) {
    const customerGroupFilter = {
      key: "groups",
      label: t("customers.groups.label"),
      type: "select",
      multiple: true,
      options: customer_groups.map((s) => ({
        label: s.name,
        value: s.id
      }))
    };
    filters = [...filters, customerGroupFilter];
  }
  const hasAccountFilter = {
    key: "has_account",
    label: t("fields.account"),
    type: "select",
    options: [
      {
        label: t("customers.registered"),
        value: "true"
      },
      {
        label: t("customers.guest"),
        value: "false"
      }
    ]
  };
  const dateFilters = [
    { label: t("fields.createdAt"), key: "created_at" },
    { label: t("fields.updatedAt"), key: "updated_at" }
  ].map((f) => ({
    key: f.key,
    label: f.label,
    type: "date"
  }));
  filters = [...filters, hasAccountFilter, ...dateFilters];
  return filters;
};

export {
  useCustomerTableFilters
};
//# sourceMappingURL=chunk-EB3HY52D.js.map
