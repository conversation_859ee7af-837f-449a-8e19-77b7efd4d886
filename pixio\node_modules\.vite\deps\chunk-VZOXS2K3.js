import {
  useUser
} from "./chunk-T7MG2EIJ.js";
import {
  Link
} from "./chunk-T7YBVUWZ.js";
import {
  Avatar,
  Text
} from "./chunk-YP2LLXWB.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/chunk-GXXQ33F7.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var UserLink = ({
  id,
  first_name,
  last_name,
  email,
  type = "user"
}) => {
  const name = [first_name, last_name].filter(Boolean).join(" ");
  const fallback = name ? name.slice(0, 1) : email.slice(0, 1);
  const link = type === "user" ? `/settings/users/${id}` : `/customers/${id}`;
  return (0, import_jsx_runtime.jsxs)(
    Link,
    {
      to: link,
      className: "flex items-center gap-x-2 w-fit transition-fg hover:text-ui-fg-subtle outline-none focus-visible:shadow-borders-focus rounded-md",
      children: [
        (0, import_jsx_runtime.jsx)(Avatar, { size: "2xsmall", fallback: fallback.toUpperCase() }),
        (0, import_jsx_runtime.jsx)(Text, { size: "small", leading: "compact", weight: "regular", children: name || email })
      ]
    }
  );
};
var By = ({ id }) => {
  const { user } = useUser(id);
  if (!user) {
    return null;
  }
  return (0, import_jsx_runtime.jsx)(UserLink, { ...user });
};

export {
  UserLink,
  By
};
//# sourceMappingURL=chunk-VZOXS2K3.js.map
