import "./chunk-EGRHWZRV.js";
import {
  MetadataForm
} from "./chunk-KXO32QO3.js";
import "./chunk-EGRHWZRV.js";
import "./chunk-DP54EP6X.js";
import "./chunk-MVVOBQIC.js";
import "./chunk-WHQIBI5S.js";
import "./chunk-4XXECALA.js";
import "./chunk-YXT43UJF.js";
import "./chunk-66DVUN72.js";
import "./chunk-XXJU43CK.js";
import {
  useCustomerGroup,
  useUpdateCustomerGroup
} from "./chunk-2AWKOUCD.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import "./chunk-JFCIYOH4.js";
import "./chunk-7HWTQOXJ.js";
import "./chunk-5GF3RGIE.js";
import {
  useParams
} from "./chunk-T7YBVUWZ.js";
import "./chunk-YP2LLXWB.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-ZSQ362B4.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/customer-group-metadata-UPAPULMF.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var CustomerGroupMetadata = () => {
  const { id } = useParams();
  const { customer_group, isPending, isError, error } = useCustomerGroup(id);
  const { mutateAsync, isPending: isMutating } = useUpdateCustomerGroup(id);
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime.jsx)(
    MetadataForm,
    {
      metadata: customer_group == null ? void 0 : customer_group.metadata,
      hook: mutateAsync,
      isPending,
      isMutating
    }
  );
};
export {
  CustomerGroupMetadata as Component
};
//# sourceMappingURL=customer-group-metadata-UPAPULMF-D7CUCZ46.js.map
