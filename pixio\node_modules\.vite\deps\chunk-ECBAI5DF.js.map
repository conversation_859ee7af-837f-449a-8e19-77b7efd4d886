{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-NEZX6265.mjs"], "sourcesContent": ["import {\n  TextCell,\n  TextHeader\n} from \"./chunk-MSDRGCRR.mjs\";\nimport {\n  useQueryParams\n} from \"./chunk-C76H5USB.mjs\";\n\n// src/routes/store/common/hooks/use-currencies-table-columns.tsx\nimport { createColumnHelper } from \"@tanstack/react-table\";\nimport { useMemo } from \"react\";\nimport { useTranslation } from \"react-i18next\";\nimport { jsx } from \"react/jsx-runtime\";\nvar columnHelper = createColumnHelper();\nvar useCurrenciesTableColumns = () => {\n  const { t } = useTranslation();\n  return useMemo(\n    () => [\n      columnHelper.accessor(\"code\", {\n        header: () => /* @__PURE__ */ jsx(TextHeader, { text: t(\"fields.code\") }),\n        cell: ({ getValue }) => /* @__PURE__ */ jsx(TextCell, { text: getValue().toUpperCase() })\n      }),\n      columnHelper.accessor(\"name\", {\n        header: () => /* @__PURE__ */ jsx(TextHeader, { text: t(\"fields.name\") }),\n        cell: ({ getValue }) => /* @__PURE__ */ jsx(TextCell, { text: getValue() })\n      })\n    ],\n    [t]\n  );\n};\n\n// src/routes/store/common/hooks/use-currencies-table-query.tsx\nvar useCurrenciesTableQuery = ({\n  pageSize = 10,\n  prefix\n}) => {\n  const raw = useQueryParams([\"order\", \"q\", \"offset\"], prefix);\n  const { offset, ...rest } = raw;\n  const searchParams = {\n    limit: pageSize,\n    offset: offset ? parseInt(offset) : 0,\n    ...rest\n  };\n  return { searchParams, raw };\n};\n\nexport {\n  useCurrenciesTableColumns,\n  useCurrenciesTableQuery\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAUA,mBAAwB;AAExB,yBAAoB;AACpB,IAAI,eAAe,mBAAmB;AACtC,IAAI,4BAA4B,MAAM;AACpC,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,aAAO;AAAA,IACL,MAAM;AAAA,MACJ,aAAa,SAAS,QAAQ;AAAA,QAC5B,QAAQ,UAAsB,wBAAI,YAAY,EAAE,MAAM,EAAE,aAAa,EAAE,CAAC;AAAA,QACxE,MAAM,CAAC,EAAE,SAAS,UAAsB,wBAAI,UAAU,EAAE,MAAM,SAAS,EAAE,YAAY,EAAE,CAAC;AAAA,MAC1F,CAAC;AAAA,MACD,aAAa,SAAS,QAAQ;AAAA,QAC5B,QAAQ,UAAsB,wBAAI,YAAY,EAAE,MAAM,EAAE,aAAa,EAAE,CAAC;AAAA,QACxE,MAAM,CAAC,EAAE,SAAS,UAAsB,wBAAI,UAAU,EAAE,MAAM,SAAS,EAAE,CAAC;AAAA,MAC5E,CAAC;AAAA,IACH;AAAA,IACA,CAAC,CAAC;AAAA,EACJ;AACF;AAGA,IAAI,0BAA0B,CAAC;AAAA,EAC7B,WAAW;AAAA,EACX;AACF,MAAM;AACJ,QAAM,MAAM,eAAe,CAAC,SAAS,KAAK,QAAQ,GAAG,MAAM;AAC3D,QAAM,EAAE,QAAQ,GAAG,KAAK,IAAI;AAC5B,QAAM,eAAe;AAAA,IACnB,OAAO;AAAA,IACP,QAAQ,SAAS,SAAS,MAAM,IAAI;AAAA,IACpC,GAAG;AAAA,EACL;AACA,SAAO,EAAE,cAAc,IAAI;AAC7B;", "names": []}