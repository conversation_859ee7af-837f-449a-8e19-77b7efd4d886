{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-FXYH54JP.mjs"], "sourcesContent": ["// src/lib/query-client.ts\nimport { QueryClient } from \"@tanstack/react-query\";\nvar MEDUSA_BACKEND_URL = __BACKEND_URL__ ?? \"/\";\nvar queryClient = new QueryClient({\n  defaultOptions: {\n    queries: {\n      refetchOnWindowFocus: false,\n      staleTime: 9e4,\n      retry: 1\n    }\n  }\n});\n\nexport {\n  queryClient\n};\n"], "mappings": ";;;;;AAEA,IAAI,qBAAqB,mBAAmB;AAC5C,IAAI,cAAc,IAAI,YAAY;AAAA,EAChC,gBAAgB;AAAA,IACd,SAAS;AAAA,MACP,sBAAsB;AAAA,MACtB,WAAW;AAAA,MACX,OAAO;AAAA,IACT;AAAA,EACF;AACF,CAAC;", "names": []}