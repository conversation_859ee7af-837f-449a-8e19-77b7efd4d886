import {
  transformNullableFormData
} from "./chunk-6JFXN7BR.js";
import {
  PRODUCT_DETAIL_FIELDS
} from "./chunk-7NJ535R3.js";
import {
  SwitchBox
} from "./chunk-4LGSZLHH.js";
import "./chunk-EZLR4STK.js";
import "./chunk-EGRHWZRV.js";
import {
  KeyboundForm
} from "./chunk-DP54EP6X.js";
import {
  RouteDrawer,
  useRouteModal
} from "./chunk-MVVOBQIC.js";
import {
  FormExtensionZone,
  useExtendableForm
} from "./chunk-BS6QEWFI.js";
import "./chunk-MM7T76RN.js";
import "./chunk-WTOMBGNF.js";
import "./chunk-EGRHWZRV.js";
import "./chunk-EPRCCFRP.js";
import "./chunk-YM3FRBGU.js";
import "./chunk-7M4ICL3D.js";
import "./chunk-RQF55WOK.js";
import "./chunk-5QX4V4M4.js";
import "./chunk-HPGXK5DQ.js";
import "./chunk-IA4ROPJA.js";
import "./chunk-SP3VUFZN.js";
import "./chunk-WHQIBI5S.js";
import {
  booleanType,
  enumType,
  objectType,
  stringType
} from "./chunk-4XXECALA.js";
import "./chunk-6GQUHAET.js";
import {
  useExtension
} from "./chunk-FTD3ZWHZ.js";
import "./chunk-YXT43UJF.js";
import "./chunk-EIZ27OVL.js";
import "./chunk-CFRQOB2M.js";
import "./chunk-66DVUN72.js";
import "./chunk-DL4QDYPT.js";
import "./chunk-NV2N3EWM.js";
import "./chunk-BF7OBKIN.js";
import "./chunk-7UAYECTW.js";
import "./chunk-Y3NYV3NU.js";
import "./chunk-MPXR7HT5.js";
import {
  Form
} from "./chunk-XXJU43CK.js";
import "./chunk-WISME5HP.js";
import "./chunk-WAUSOHFQ.js";
import "./chunk-3ORK3QZ4.js";
import "./chunk-3A5TVVNI.js";
import "./chunk-EZ62MM7J.js";
import "./chunk-3R6LYIXM.js";
import "./chunk-2P2O4TMQ.js";
import "./chunk-KI3HEITX.js";
import "./chunk-2VUCAO7L.js";
import "./chunk-PTIULTKD.js";
import "./chunk-KISXY2LD.js";
import "./chunk-L7C3XPHJ.js";
import "./chunk-T7MG2EIJ.js";
import "./chunk-MER4VYPR.js";
import "./chunk-2AWKOUCD.js";
import "./chunk-YL3TYBFR.js";
import "./chunk-TZWW72YW.js";
import "./chunk-MSQ25CWB.js";
import "./chunk-ONYSAQ5Z.js";
import "./chunk-NIH7SAXN.js";
import "./chunk-UEJFWAFE.js";
import "./chunk-GR55MFKT.js";
import "./chunk-AAFHKNJG.js";
import "./chunk-7XDBFDTZ.js";
import "./chunk-3JKGO5XL.js";
import "./chunk-OISUTS7G.js";
import {
  useProduct,
  useUpdateProduct
} from "./chunk-AJYMIHLQ.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import "./chunk-JFCIYOH4.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-5GF3RGIE.js";
import {
  useParams
} from "./chunk-T7YBVUWZ.js";
import {
  Button,
  Heading,
  Input,
  Select,
  Text,
  Textarea,
  toast
} from "./chunk-YP2LLXWB.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-ZSQ362B4.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/product-edit-OF6Y3Z4A.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var EditProductSchema = objectType({
  status: enumType(["draft", "published", "proposed", "rejected"]),
  title: stringType().min(1),
  subtitle: stringType().optional(),
  handle: stringType().min(1),
  material: stringType().optional(),
  description: stringType().optional(),
  discountable: booleanType()
});
var EditProductForm = ({ product }) => {
  const { t } = useTranslation();
  const { handleSuccess } = useRouteModal();
  const { getFormFields, getFormConfigs } = useExtension();
  const fields = getFormFields("product", "edit");
  const configs = getFormConfigs("product", "edit");
  const form = useExtendableForm({
    defaultValues: {
      status: product.status,
      title: product.title,
      material: product.material || "",
      subtitle: product.subtitle || "",
      handle: product.handle || "",
      description: product.description || "",
      discountable: product.discountable
    },
    schema: EditProductSchema,
    configs,
    data: product
  });
  const { mutateAsync, isPending } = useUpdateProduct(product.id);
  const handleSubmit = form.handleSubmit(async (data) => {
    const { title, discountable, handle, status, ...optional } = data;
    const nullableData = transformNullableFormData(optional);
    await mutateAsync(
      {
        title,
        discountable,
        handle,
        status,
        ...nullableData
      },
      {
        onSuccess: ({ product: product2 }) => {
          toast.success(
            t("products.edit.successToast", { title: product2.title })
          );
          handleSuccess();
        },
        onError: (e) => {
          toast.error(e.message);
        }
      }
    );
  });
  return (0, import_jsx_runtime.jsx)(RouteDrawer.Form, { form, children: (0, import_jsx_runtime.jsxs)(
    KeyboundForm,
    {
      onSubmit: handleSubmit,
      className: "flex flex-1 flex-col overflow-hidden",
      children: [
        (0, import_jsx_runtime.jsx)(RouteDrawer.Body, { className: "flex flex-1 flex-col gap-y-8 overflow-y-auto", children: (0, import_jsx_runtime.jsxs)("div", { className: "flex flex-col gap-y-8", children: [
          (0, import_jsx_runtime.jsxs)("div", { className: "flex flex-col gap-y-4", children: [
            (0, import_jsx_runtime.jsx)(
              Form.Field,
              {
                control: form.control,
                name: "status",
                render: ({ field: { onChange, ref, ...field } }) => {
                  return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                    (0, import_jsx_runtime.jsx)(Form.Label, { children: t("fields.status") }),
                    (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsxs)(Select, { ...field, onValueChange: onChange, children: [
                      (0, import_jsx_runtime.jsx)(Select.Trigger, { ref, children: (0, import_jsx_runtime.jsx)(Select.Value, {}) }),
                      (0, import_jsx_runtime.jsx)(Select.Content, { children: [
                        "draft",
                        "published",
                        "proposed",
                        "rejected"
                      ].map((status) => {
                        return (0, import_jsx_runtime.jsx)(Select.Item, { value: status, children: t(`products.productStatus.${status}`) }, status);
                      }) })
                    ] }) }),
                    (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                  ] });
                }
              }
            ),
            (0, import_jsx_runtime.jsx)(
              Form.Field,
              {
                control: form.control,
                name: "title",
                render: ({ field }) => {
                  return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                    (0, import_jsx_runtime.jsx)(Form.Label, { children: t("fields.title") }),
                    (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(Input, { ...field }) }),
                    (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                  ] });
                }
              }
            ),
            (0, import_jsx_runtime.jsx)(
              Form.Field,
              {
                control: form.control,
                name: "subtitle",
                render: ({ field }) => {
                  return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                    (0, import_jsx_runtime.jsx)(Form.Label, { optional: true, children: t("fields.subtitle") }),
                    (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(Input, { ...field }) }),
                    (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                  ] });
                }
              }
            ),
            (0, import_jsx_runtime.jsx)(
              Form.Field,
              {
                control: form.control,
                name: "handle",
                render: ({ field }) => {
                  return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                    (0, import_jsx_runtime.jsx)(Form.Label, { children: t("fields.handle") }),
                    (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsxs)("div", { className: "relative", children: [
                      (0, import_jsx_runtime.jsx)("div", { className: "absolute inset-y-0 left-0 z-10 flex w-8 items-center justify-center border-r", children: (0, import_jsx_runtime.jsx)(
                        Text,
                        {
                          className: "text-ui-fg-muted",
                          size: "small",
                          leading: "compact",
                          weight: "plus",
                          children: "/"
                        }
                      ) }),
                      (0, import_jsx_runtime.jsx)(Input, { ...field, className: "pl-10" })
                    ] }) }),
                    (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                  ] });
                }
              }
            ),
            (0, import_jsx_runtime.jsx)(
              Form.Field,
              {
                control: form.control,
                name: "material",
                render: ({ field }) => {
                  return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                    (0, import_jsx_runtime.jsx)(Form.Label, { optional: true, children: t("fields.material") }),
                    (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(Input, { ...field }) }),
                    (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                  ] });
                }
              }
            ),
            (0, import_jsx_runtime.jsx)(
              Form.Field,
              {
                control: form.control,
                name: "description",
                render: ({ field }) => {
                  return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                    (0, import_jsx_runtime.jsx)(Form.Label, { optional: true, children: t("fields.description") }),
                    (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(Textarea, { ...field }) }),
                    (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                  ] });
                }
              }
            )
          ] }),
          (0, import_jsx_runtime.jsx)(
            SwitchBox,
            {
              control: form.control,
              name: "discountable",
              label: t("fields.discountable"),
              description: t("products.discountableHint")
            }
          ),
          (0, import_jsx_runtime.jsx)(FormExtensionZone, { fields, form })
        ] }) }),
        (0, import_jsx_runtime.jsx)(RouteDrawer.Footer, { children: (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center justify-end gap-x-2", children: [
          (0, import_jsx_runtime.jsx)(RouteDrawer.Close, { asChild: true, children: (0, import_jsx_runtime.jsx)(Button, { size: "small", variant: "secondary", children: t("actions.cancel") }) }),
          (0, import_jsx_runtime.jsx)(Button, { size: "small", type: "submit", isLoading: isPending, children: t("actions.save") })
        ] }) })
      ]
    }
  ) });
};
var ProductEdit = () => {
  const { id } = useParams();
  const { t } = useTranslation();
  const { product, isLoading, isError, error } = useProduct(id, {
    fields: PRODUCT_DETAIL_FIELDS
  });
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime2.jsxs)(RouteDrawer, { children: [
    (0, import_jsx_runtime2.jsxs)(RouteDrawer.Header, { children: [
      (0, import_jsx_runtime2.jsx)(RouteDrawer.Title, { asChild: true, children: (0, import_jsx_runtime2.jsx)(Heading, { children: t("products.edit.header") }) }),
      (0, import_jsx_runtime2.jsx)(RouteDrawer.Description, { className: "sr-only", children: t("products.edit.description") })
    ] }),
    !isLoading && product && (0, import_jsx_runtime2.jsx)(EditProductForm, { product })
  ] });
};
export {
  ProductEdit as Component
};
//# sourceMappingURL=product-edit-OF6Y3Z4A-SF7USYBC.js.map
