import {
  AnimatePresence,
  motion
} from "./chunk-7M4ICL3D.js";
import {
  Badge,
  XMarkMini,
  clx
} from "./chunk-YP2LLXWB.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/chunk-XDJ7OMBR.mjs
var import_react2 = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var ChipInput = (0, import_react2.forwardRef)(
  ({
    value,
    onChange,
    onBlur,
    disabled,
    name,
    showRemove = true,
    variant = "base",
    allowDuplicates = false,
    placeholder,
    className
  }, ref) => {
    const innerRef = (0, import_react2.useRef)(null);
    const isControlledRef = (0, import_react2.useRef)(typeof value !== "undefined");
    const isControlled = isControlledRef.current;
    const [uncontrolledValue, setUncontrolledValue] = (0, import_react2.useState)([]);
    (0, import_react2.useImperativeHandle)(
      ref,
      () => innerRef.current
    );
    const [duplicateIndex, setDuplicateIndex] = (0, import_react2.useState)(null);
    const chips = isControlled ? value : uncontrolledValue;
    const handleAddChip = (chip) => {
      const cleanValue = chip.trim();
      if (!cleanValue) {
        return;
      }
      if (!allowDuplicates && chips.includes(cleanValue)) {
        setDuplicateIndex(chips.indexOf(cleanValue));
        setTimeout(() => {
          setDuplicateIndex(null);
        }, 300);
        return;
      }
      onChange == null ? void 0 : onChange([...chips, cleanValue]);
      if (!isControlled) {
        setUncontrolledValue([...chips, cleanValue]);
      }
    };
    const handleRemoveChip = (chip) => {
      onChange == null ? void 0 : onChange(chips.filter((v) => v !== chip));
      if (!isControlled) {
        setUncontrolledValue(chips.filter((v) => v !== chip));
      }
    };
    const handleBlur = (e) => {
      onBlur == null ? void 0 : onBlur();
      if (e.target.value) {
        handleAddChip(e.target.value);
        e.target.value = "";
      }
    };
    const handleKeyDown = (e) => {
      var _a, _b, _c, _d;
      if (e.key === "Enter" || e.key === ",") {
        e.preventDefault();
        if (!((_a = innerRef.current) == null ? void 0 : _a.value)) {
          return;
        }
        handleAddChip(((_b = innerRef.current) == null ? void 0 : _b.value) ?? "");
        innerRef.current.value = "";
        (_c = innerRef.current) == null ? void 0 : _c.focus();
      }
      if (e.key === "Backspace" && ((_d = innerRef.current) == null ? void 0 : _d.value) === "") {
        handleRemoveChip(chips[chips.length - 1]);
      }
    };
    const shake = {
      x: [0, -2, 2, -2, 2, 0],
      transition: { duration: 0.3 }
    };
    return (0, import_jsx_runtime.jsxs)(
      "div",
      {
        className: clx(
          "shadow-borders-base flex min-h-8 flex-wrap items-center gap-1 rounded-md px-2 py-1.5",
          "transition-fg focus-within:shadow-borders-interactive-with-active",
          "has-[input:disabled]:bg-ui-bg-disabled has-[input:disabled]:text-ui-fg-disabled has-[input:disabled]:cursor-not-allowed",
          {
            "bg-ui-bg-field-component hover:bg-ui-bg-field-component-hover": variant === "contrast",
            "bg-ui-bg-field hover:bg-ui-bg-field-hover": variant === "base"
          },
          className
        ),
        tabIndex: -1,
        onClick: () => {
          var _a;
          return (_a = innerRef.current) == null ? void 0 : _a.focus();
        },
        children: [
          chips.map((v, index) => {
            return (0, import_jsx_runtime.jsx)(AnimatePresence, { children: (0, import_jsx_runtime.jsx)(
              Badge,
              {
                size: "2xsmall",
                className: clx("gap-x-0.5 pl-1.5 pr-1.5", {
                  "transition-fg pr-1": showRemove,
                  "shadow-borders-focus": index === duplicateIndex
                }),
                asChild: true,
                children: (0, import_jsx_runtime.jsxs)(
                  motion.div,
                  {
                    animate: index === duplicateIndex ? shake : void 0,
                    children: [
                      v,
                      showRemove && (0, import_jsx_runtime.jsx)(
                        "button",
                        {
                          tabIndex: -1,
                          type: "button",
                          onClick: () => handleRemoveChip(v),
                          className: clx(
                            "text-ui-fg-subtle transition-fg outline-none"
                          ),
                          children: (0, import_jsx_runtime.jsx)(XMarkMini, {})
                        }
                      )
                    ]
                  }
                )
              }
            ) }, `${v}-${index}`);
          }),
          (0, import_jsx_runtime.jsx)(
            "input",
            {
              className: clx(
                "caret-ui-fg-base text-ui-fg-base txt-compact-small flex-1 appearance-none bg-transparent",
                "disabled:text-ui-fg-disabled disabled:cursor-not-allowed",
                "focus:outline-none",
                "placeholder:text-ui-fg-muted"
              ),
              onKeyDown: handleKeyDown,
              onBlur: handleBlur,
              disabled,
              name,
              ref: innerRef,
              placeholder: chips.length === 0 ? placeholder : void 0,
              autoComplete: "off"
            }
          )
        ]
      }
    );
  }
);
ChipInput.displayName = "ChipInput";

export {
  ChipInput
};
//# sourceMappingURL=chunk-EFXWVIHH.js.map
