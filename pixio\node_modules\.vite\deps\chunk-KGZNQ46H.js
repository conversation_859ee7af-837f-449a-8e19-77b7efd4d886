import {
  useDeleteProductTag
} from "./chunk-WISME5HP.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import {
  useNavigate
} from "./chunk-T7YBVUWZ.js";
import {
  toast,
  usePrompt
} from "./chunk-YP2LLXWB.js";

// node_modules/@medusajs/dashboard/dist/chunk-SMK3VXN6.mjs
var useDeleteProductTagAction = ({
  productTag
}) => {
  const { t } = useTranslation();
  const prompt = usePrompt();
  const navigate = useNavigate();
  const { mutateAsync } = useDeleteProductTag(productTag.id);
  const handleDelete = async () => {
    const confirmed = await prompt({
      title: t("general.areYouSure"),
      description: t("productTags.delete.confirmation", {
        value: productTag.value
      }),
      confirmText: t("actions.delete"),
      cancelText: t("actions.cancel")
    });
    if (!confirmed) {
      return;
    }
    await mutateAsync(void 0, {
      onSuccess: () => {
        toast.success(
          t("productTags.delete.successToast", {
            value: productTag.value
          })
        );
        navigate("/settings/product-tags", {
          replace: true
        });
      },
      onError: (error) => {
        toast.error(error.message);
      }
    });
  };
  return handleDelete;
};

export {
  useDeleteProductTagAction
};
//# sourceMappingURL=chunk-KGZNQ46H.js.map
