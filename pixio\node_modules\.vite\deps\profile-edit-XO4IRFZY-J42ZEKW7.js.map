{"version": 3, "sources": ["../../@medusajs/dashboard/dist/profile-edit-XO4IRFZY.mjs"], "sourcesContent": ["import \"./chunk-IUCDCPJU.mjs\";\nimport {\n  KeyboundForm\n} from \"./chunk-6HTZNHPT.mjs\";\nimport {\n  RouteDrawer,\n  useRouteModal\n} from \"./chunk-4TC5YS65.mjs\";\nimport {\n  languages\n} from \"./chunk-NYXYHZE6.mjs\";\nimport {\n  Form\n} from \"./chunk-OBQI23QM.mjs\";\nimport {\n  useMe,\n  useUpdateUser\n} from \"./chunk-2ZKVRTBW.mjs\";\nimport \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-RPUOO7AV.mjs\";\n\n// src/routes/profile/profile-edit/profile-edit.tsx\nimport { Heading } from \"@medusajs/ui\";\nimport { useTranslation as useTranslation2 } from \"react-i18next\";\n\n// src/routes/profile/profile-edit/components/edit-profile-form/edit-profile-form.tsx\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { Button, Input, Select, toast } from \"@medusajs/ui\";\nimport { useForm } from \"react-hook-form\";\nimport { useTranslation } from \"react-i18next\";\nimport * as zod from \"zod\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar EditProfileSchema = zod.object({\n  first_name: zod.string().optional(),\n  last_name: zod.string().optional(),\n  language: zod.string()\n  // usage_insights: zod.boolean(),\n});\nvar EditProfileForm = ({ user }) => {\n  const { t, i18n } = useTranslation();\n  const { handleSuccess } = useRouteModal();\n  const form = useForm({\n    defaultValues: {\n      first_name: user.first_name ?? \"\",\n      last_name: user.last_name ?? \"\",\n      language: i18n.language\n      // usage_insights: usageInsights,\n    },\n    resolver: zodResolver(EditProfileSchema)\n  });\n  const changeLanguage = async (code) => {\n    await i18n.changeLanguage(code);\n  };\n  const sortedLanguages = languages.sort(\n    (a, b) => a.display_name.localeCompare(b.display_name)\n  );\n  const { mutateAsync, isPending } = useUpdateUser(user.id);\n  const handleSubmit = form.handleSubmit(async (values) => {\n    await mutateAsync(\n      {\n        first_name: values.first_name,\n        last_name: values.last_name\n      },\n      {\n        onError: (error) => {\n          toast.error(error.message);\n          return;\n        }\n      }\n    );\n    await changeLanguage(values.language);\n    toast.success(t(\"profile.toast.edit\"));\n    handleSuccess();\n  });\n  return /* @__PURE__ */ jsx(RouteDrawer.Form, { form, children: /* @__PURE__ */ jsxs(KeyboundForm, { onSubmit: handleSubmit, className: \"flex flex-1 flex-col\", children: [\n    /* @__PURE__ */ jsx(RouteDrawer.Body, { children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex flex-col gap-y-8\", children: [\n      /* @__PURE__ */ jsxs(\"div\", { className: \"grid grid-cols-2 gap-4\", children: [\n        /* @__PURE__ */ jsx(\n          Form.Field,\n          {\n            control: form.control,\n            name: \"first_name\",\n            render: ({ field }) => /* @__PURE__ */ jsxs(Form.Item, { children: [\n              /* @__PURE__ */ jsx(Form.Label, { children: t(\"fields.firstName\") }),\n              /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(Input, { ...field }) }),\n              /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n            ] })\n          }\n        ),\n        /* @__PURE__ */ jsx(\n          Form.Field,\n          {\n            control: form.control,\n            name: \"last_name\",\n            render: ({ field }) => /* @__PURE__ */ jsxs(Form.Item, { children: [\n              /* @__PURE__ */ jsx(Form.Label, { children: t(\"fields.lastName\") }),\n              /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(Input, { ...field }) }),\n              /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n            ] })\n          }\n        )\n      ] }),\n      /* @__PURE__ */ jsx(\n        Form.Field,\n        {\n          control: form.control,\n          name: \"language\",\n          render: ({ field: { ref, ...field } }) => /* @__PURE__ */ jsxs(Form.Item, { className: \"gap-y-4\", children: [\n            /* @__PURE__ */ jsxs(\"div\", { children: [\n              /* @__PURE__ */ jsx(Form.Label, { children: t(\"profile.fields.languageLabel\") }),\n              /* @__PURE__ */ jsx(Form.Hint, { children: t(\"profile.edit.languageHint\") })\n            ] }),\n            /* @__PURE__ */ jsxs(\"div\", { children: [\n              /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsxs(Select, { ...field, onValueChange: field.onChange, children: [\n                /* @__PURE__ */ jsx(Select.Trigger, { ref, className: \"py-1 text-[13px]\", children: /* @__PURE__ */ jsx(\n                  Select.Value,\n                  {\n                    placeholder: t(\"profile.edit.languagePlaceholder\"),\n                    children: sortedLanguages.find(\n                      (language) => language.code === field.value\n                    )?.display_name\n                  }\n                ) }),\n                /* @__PURE__ */ jsx(Select.Content, { children: languages.map((language) => /* @__PURE__ */ jsx(\n                  Select.Item,\n                  {\n                    value: language.code,\n                    children: language.display_name\n                  },\n                  language.code\n                )) })\n              ] }) }),\n              /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n            ] })\n          ] })\n        }\n      )\n    ] }) }),\n    /* @__PURE__ */ jsx(RouteDrawer.Footer, { children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center gap-x-2\", children: [\n      /* @__PURE__ */ jsx(RouteDrawer.Close, { asChild: true, children: /* @__PURE__ */ jsx(Button, { size: \"small\", variant: \"secondary\", children: t(\"actions.cancel\") }) }),\n      /* @__PURE__ */ jsx(Button, { size: \"small\", type: \"submit\", isLoading: isPending, children: t(\"actions.save\") })\n    ] }) })\n  ] }) });\n};\n\n// src/routes/profile/profile-edit/profile-edit.tsx\nimport { jsx as jsx2, jsxs as jsxs2 } from \"react/jsx-runtime\";\nvar ProfileEdit = () => {\n  const { user, isPending: isLoading, isError, error } = useMe();\n  const { t } = useTranslation2();\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsxs2(RouteDrawer, { children: [\n    /* @__PURE__ */ jsx2(RouteDrawer.Header, { className: \"capitalize\", children: /* @__PURE__ */ jsx2(RouteDrawer.Title, { asChild: true, children: /* @__PURE__ */ jsx2(Heading, { children: t(\"profile.edit.header\") }) }) }),\n    !isLoading && user && /* @__PURE__ */ jsx2(EditProfileForm, { user })\n  ] });\n};\nexport {\n  ProfileEdit as Component\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiCA,yBAA0B;AAmH1B,IAAAA,sBAA2C;AAlH3C,IAAI,oBAAwB,WAAO;AAAA,EACjC,YAAgB,WAAO,EAAE,SAAS;AAAA,EAClC,WAAe,WAAO,EAAE,SAAS;AAAA,EACjC,UAAc,WAAO;AAAA;AAEvB,CAAC;AACD,IAAI,kBAAkB,CAAC,EAAE,KAAK,MAAM;AAClC,QAAM,EAAE,GAAAC,IAAG,KAAK,IAAI,eAAe;AACnC,QAAM,EAAE,cAAc,IAAI,cAAc;AACxC,QAAM,OAAO,QAAQ;AAAA,IACnB,eAAe;AAAA,MACb,YAAY,KAAK,cAAc;AAAA,MAC/B,WAAW,KAAK,aAAa;AAAA,MAC7B,UAAU,KAAK;AAAA;AAAA,IAEjB;AAAA,IACA,UAAU,EAAY,iBAAiB;AAAA,EACzC,CAAC;AACD,QAAM,iBAAiB,OAAO,SAAS;AACrC,UAAM,KAAK,eAAe,IAAI;AAAA,EAChC;AACA,QAAM,kBAAkB,UAAU;AAAA,IAChC,CAAC,GAAG,MAAM,EAAE,aAAa,cAAc,EAAE,YAAY;AAAA,EACvD;AACA,QAAM,EAAE,aAAa,UAAU,IAAI,cAAc,KAAK,EAAE;AACxD,QAAM,eAAe,KAAK,aAAa,OAAO,WAAW;AACvD,UAAM;AAAA,MACJ;AAAA,QACE,YAAY,OAAO;AAAA,QACnB,WAAW,OAAO;AAAA,MACpB;AAAA,MACA;AAAA,QACE,SAAS,CAAC,UAAU;AAClB,gBAAM,MAAM,MAAM,OAAO;AACzB;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,UAAM,eAAe,OAAO,QAAQ;AACpC,UAAM,QAAQA,GAAE,oBAAoB,CAAC;AACrC,kBAAc;AAAA,EAChB,CAAC;AACD,aAAuB,wBAAI,YAAY,MAAM,EAAE,MAAM,cAA0B,yBAAK,cAAc,EAAE,UAAU,cAAc,WAAW,wBAAwB,UAAU;AAAA,QACvJ,wBAAI,YAAY,MAAM,EAAE,cAA0B,yBAAK,OAAO,EAAE,WAAW,yBAAyB,UAAU;AAAA,UAC5G,yBAAK,OAAO,EAAE,WAAW,0BAA0B,UAAU;AAAA,YAC3D;AAAA,UACd,KAAK;AAAA,UACL;AAAA,YACE,SAAS,KAAK;AAAA,YACd,MAAM;AAAA,YACN,QAAQ,CAAC,EAAE,MAAM,UAAsB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,kBACjD,wBAAI,KAAK,OAAO,EAAE,UAAUA,GAAE,kBAAkB,EAAE,CAAC;AAAA,kBACnD,wBAAI,KAAK,SAAS,EAAE,cAA0B,wBAAI,OAAO,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC;AAAA,kBACxE,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,YAC3C,EAAE,CAAC;AAAA,UACL;AAAA,QACF;AAAA,YACgB;AAAA,UACd,KAAK;AAAA,UACL;AAAA,YACE,SAAS,KAAK;AAAA,YACd,MAAM;AAAA,YACN,QAAQ,CAAC,EAAE,MAAM,UAAsB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,kBACjD,wBAAI,KAAK,OAAO,EAAE,UAAUA,GAAE,iBAAiB,EAAE,CAAC;AAAA,kBAClD,wBAAI,KAAK,SAAS,EAAE,cAA0B,wBAAI,OAAO,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC;AAAA,kBACxE,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,YAC3C,EAAE,CAAC;AAAA,UACL;AAAA,QACF;AAAA,MACF,EAAE,CAAC;AAAA,UACa;AAAA,QACd,KAAK;AAAA,QACL;AAAA,UACE,SAAS,KAAK;AAAA,UACd,MAAM;AAAA,UACN,QAAQ,CAAC,EAAE,OAAO,EAAE,KAAK,GAAG,MAAM,EAAE,MAAG;AA7GjD;AA6GoE,gDAAK,KAAK,MAAM,EAAE,WAAW,WAAW,UAAU;AAAA,kBAC1F,yBAAK,OAAO,EAAE,UAAU;AAAA,oBACtB,wBAAI,KAAK,OAAO,EAAE,UAAUA,GAAE,8BAA8B,EAAE,CAAC;AAAA,oBAC/D,wBAAI,KAAK,MAAM,EAAE,UAAUA,GAAE,2BAA2B,EAAE,CAAC;AAAA,cAC7E,EAAE,CAAC;AAAA,kBACa,yBAAK,OAAO,EAAE,UAAU;AAAA,oBACtB,wBAAI,KAAK,SAAS,EAAE,cAA0B,yBAAK,QAAQ,EAAE,GAAG,OAAO,eAAe,MAAM,UAAU,UAAU;AAAA,sBAC9G,wBAAI,OAAO,SAAS,EAAE,KAAK,WAAW,oBAAoB,cAA0B;AAAA,oBAClG,OAAO;AAAA,oBACP;AAAA,sBACE,aAAaA,GAAE,kCAAkC;AAAA,sBACjD,WAAU,qBAAgB;AAAA,wBACxB,CAAC,aAAa,SAAS,SAAS,MAAM;AAAA,sBACxC,MAFU,mBAEP;AAAA,oBACL;AAAA,kBACF,EAAE,CAAC;AAAA,sBACa,wBAAI,OAAO,SAAS,EAAE,UAAU,UAAU,IAAI,CAAC,iBAA6B;AAAA,oBAC1F,OAAO;AAAA,oBACP;AAAA,sBACE,OAAO,SAAS;AAAA,sBAChB,UAAU,SAAS;AAAA,oBACrB;AAAA,oBACA,SAAS;AAAA,kBACX,CAAC,EAAE,CAAC;AAAA,gBACN,EAAE,CAAC,EAAE,CAAC;AAAA,oBACU,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,cAC3C,EAAE,CAAC;AAAA,YACL,EAAE,CAAC;AAAA;AAAA,QACL;AAAA,MACF;AAAA,IACF,EAAE,CAAC,EAAE,CAAC;AAAA,QACU,wBAAI,YAAY,QAAQ,EAAE,cAA0B,yBAAK,OAAO,EAAE,WAAW,6BAA6B,UAAU;AAAA,UAClH,wBAAI,YAAY,OAAO,EAAE,SAAS,MAAM,cAA0B,wBAAI,QAAQ,EAAE,MAAM,SAAS,SAAS,aAAa,UAAUA,GAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;AAAA,UACvJ,wBAAI,QAAQ,EAAE,MAAM,SAAS,MAAM,UAAU,WAAW,WAAW,UAAUA,GAAE,cAAc,EAAE,CAAC;AAAA,IAClH,EAAE,CAAC,EAAE,CAAC;AAAA,EACR,EAAE,CAAC,EAAE,CAAC;AACR;AAIA,IAAI,cAAc,MAAM;AACtB,QAAM,EAAE,MAAM,WAAW,WAAW,SAAS,MAAM,IAAI,MAAM;AAC7D,QAAM,EAAE,GAAAA,GAAE,IAAI,eAAgB;AAC9B,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,oBAAAC,MAAM,aAAa,EAAE,UAAU;AAAA,QACpC,oBAAAC,KAAK,YAAY,QAAQ,EAAE,WAAW,cAAc,cAA0B,oBAAAA,KAAK,YAAY,OAAO,EAAE,SAAS,MAAM,cAA0B,oBAAAA,KAAK,SAAS,EAAE,UAAUF,GAAE,qBAAqB,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;AAAA,IAC3N,CAAC,aAAa,YAAwB,oBAAAE,KAAK,iBAAiB,EAAE,KAAK,CAAC;AAAA,EACtE,EAAE,CAAC;AACL;", "names": ["import_jsx_runtime", "t", "jsxs2", "jsx2"]}