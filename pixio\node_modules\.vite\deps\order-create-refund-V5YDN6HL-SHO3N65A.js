import {
  getPaymentsFromOrder
} from "./chunk-ZS4C5DA2.js";
import {
  formatProvider
} from "./chunk-LVAKEKGS.js";
import {
  formatCurrency
} from "./chunk-SCWUY6XB.js";
import "./chunk-5ZQBU3TD.js";
import {
  DEFAULT_FIELDS
} from "./chunk-UKYP5JCA.js";
import {
  getLocaleAmount
} from "./chunk-UDMOPZAP.js";
import {
  currencies
} from "./chunk-H3DTEG3J.js";
import "./chunk-EGRHWZRV.js";
import {
  KeyboundForm
} from "./chunk-DP54EP6X.js";
import {
  RouteDrawer,
  useRouteModal
} from "./chunk-MVVOBQIC.js";
import {
  t
} from "./chunk-WHQIBI5S.js";
import {
  numberType,
  objectType,
  stringType
} from "./chunk-4XXECALA.js";
import "./chunk-CFRQOB2M.js";
import "./chunk-66DVUN72.js";
import "./chunk-7UAYECTW.js";
import "./chunk-Y3NYV3NU.js";
import {
  Form,
  useForm
} from "./chunk-XXJU43CK.js";
import {
  useRefundReasons
} from "./chunk-WISME5HP.js";
import "./chunk-WAUSOHFQ.js";
import "./chunk-3ORK3QZ4.js";
import "./chunk-3A5TVVNI.js";
import "./chunk-EZ62MM7J.js";
import "./chunk-3R6LYIXM.js";
import "./chunk-2P2O4TMQ.js";
import "./chunk-KI3HEITX.js";
import "./chunk-2VUCAO7L.js";
import "./chunk-PTIULTKD.js";
import "./chunk-KISXY2LD.js";
import {
  useRefundPayment
} from "./chunk-L7C3XPHJ.js";
import "./chunk-T7MG2EIJ.js";
import "./chunk-MER4VYPR.js";
import "./chunk-2AWKOUCD.js";
import "./chunk-YL3TYBFR.js";
import "./chunk-TZWW72YW.js";
import "./chunk-MSQ25CWB.js";
import "./chunk-ONYSAQ5Z.js";
import {
  useOrder
} from "./chunk-NIH7SAXN.js";
import "./chunk-UEJFWAFE.js";
import "./chunk-GR55MFKT.js";
import "./chunk-AAFHKNJG.js";
import "./chunk-7XDBFDTZ.js";
import "./chunk-3JKGO5XL.js";
import "./chunk-OISUTS7G.js";
import "./chunk-AJYMIHLQ.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import "./chunk-JFCIYOH4.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-5GF3RGIE.js";
import {
  useNavigate,
  useParams,
  useSearchParams
} from "./chunk-T7YBVUWZ.js";
import {
  Button,
  CurrencyInput2 as CurrencyInput,
  Heading,
  Label,
  Select,
  Textarea,
  formatValue,
  toast
} from "./chunk-YP2LLXWB.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-ZSQ362B4.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/order-create-refund-V5YDN6HL.mjs
var import_react = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var CreateRefundSchema = objectType({
  amount: stringType().or(numberType()),
  refund_reason_id: stringType().nullish(),
  note: stringType().optional()
});
var CreateRefundForm = ({
  order,
  refundReasons
}) => {
  const { t: t2 } = useTranslation();
  const { handleSuccess } = useRouteModal();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const paymentId = searchParams.get("paymentId");
  const payments = getPaymentsFromOrder(order);
  const payment = payments.find((p) => p.id === paymentId);
  const paymentAmount = (payment == null ? void 0 : payment.amount) || 0;
  const currency = (0, import_react.useMemo)(
    () => currencies[order.currency_code.toUpperCase()],
    [order.currency_code]
  );
  const form = useForm({
    defaultValues: {
      amount: paymentAmount,
      note: ""
    },
    resolver: t(CreateRefundSchema)
  });
  (0, import_react.useEffect)(() => {
    const pendingDifference = order.summary.pending_difference;
    const paymentAmount2 = (payment == null ? void 0 : payment.amount) || 0;
    const pendingAmount = pendingDifference < 0 ? Math.min(pendingDifference, paymentAmount2) : paymentAmount2;
    const normalizedAmount = pendingAmount < 0 ? pendingAmount * -1 : pendingAmount;
    form.setValue("amount", normalizedAmount);
  }, [payment]);
  const { mutateAsync, isPending } = useRefundPayment(order.id, payment == null ? void 0 : payment.id);
  const handleSubmit = form.handleSubmit(async (data) => {
    await mutateAsync(
      {
        amount: parseFloat(data.amount),
        refund_reason_id: data.refund_reason_id,
        note: data.note
      },
      {
        onSuccess: () => {
          toast.success(
            t2("orders.payment.refundPaymentSuccess", {
              amount: formatCurrency(data.amount, payment == null ? void 0 : payment.currency_code)
            })
          );
          handleSuccess();
        },
        onError: (error) => {
          toast.error(error.message);
        }
      }
    );
  });
  return (0, import_jsx_runtime.jsx)(RouteDrawer.Form, { form, children: (0, import_jsx_runtime.jsxs)(
    KeyboundForm,
    {
      onSubmit: handleSubmit,
      className: "flex size-full flex-col overflow-hidden",
      children: [
        (0, import_jsx_runtime.jsx)(RouteDrawer.Body, { className: "flex-1 overflow-auto", children: (0, import_jsx_runtime.jsxs)("div", { className: "flex flex-col gap-y-4", children: [
          (0, import_jsx_runtime.jsxs)(
            Select,
            {
              value: payment == null ? void 0 : payment.id,
              onValueChange: (value) => {
                navigate(`/orders/${order.id}/refund?paymentId=${value}`, {
                  replace: true
                });
              },
              children: [
                (0, import_jsx_runtime.jsx)(Label, { className: "txt-compact-small mb-[-6px] font-sans font-medium", children: t2("orders.payment.selectPaymentToRefund") }),
                (0, import_jsx_runtime.jsx)(Select.Trigger, { children: (0, import_jsx_runtime.jsx)(
                  Select.Value,
                  {
                    placeholder: t2("orders.payment.selectPaymentToRefund")
                  }
                ) }),
                (0, import_jsx_runtime.jsx)(Select.Content, { children: payments.map((payment2) => {
                  const totalRefunded = payment2.refunds.reduce(
                    (acc, next) => next.amount + acc,
                    0
                  );
                  return (0, import_jsx_runtime.jsxs)(
                    Select.Item,
                    {
                      value: payment2.id,
                      disabled: !!payment2.canceled_at || totalRefunded >= payment2.amount,
                      className: "flex items-center justify-center",
                      children: [
                        (0, import_jsx_runtime.jsxs)("span", { children: [
                          getLocaleAmount(
                            payment2.amount,
                            payment2.currency_code
                          ),
                          " - "
                        ] }),
                        (0, import_jsx_runtime.jsx)("span", { children: formatProvider(payment2.provider_id) }),
                        (0, import_jsx_runtime.jsxs)("span", { children: [
                          " - (#",
                          payment2.id.substring(23),
                          ")"
                        ] })
                      ]
                    },
                    payment2.id
                  );
                }) })
              ]
            }
          ),
          (0, import_jsx_runtime.jsx)(
            Form.Field,
            {
              control: form.control,
              name: "amount",
              rules: {
                required: true,
                min: 0,
                max: paymentAmount
              },
              render: ({ field: { onChange, ...field } }) => {
                return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                  (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("fields.amount") }),
                  (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(
                    CurrencyInput,
                    {
                      ...field,
                      min: 0,
                      placeholder: formatValue({
                        value: "0",
                        decimalScale: currency.decimal_digits
                      }),
                      decimalScale: currency.decimal_digits,
                      symbol: currency.symbol_native,
                      code: currency.code,
                      value: field.value,
                      onValueChange: (_value, _name, values) => onChange((values == null ? void 0 : values.value) ? values == null ? void 0 : values.value : ""),
                      autoFocus: true
                    }
                  ) }),
                  (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                ] });
              }
            }
          ),
          (0, import_jsx_runtime.jsx)(
            Form.Field,
            {
              control: form.control,
              name: `note`,
              render: ({ field }) => {
                return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                  (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("fields.note") }),
                  (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(Textarea, { ...field }) }),
                  (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                ] });
              }
            }
          )
        ] }) }),
        (0, import_jsx_runtime.jsx)(RouteDrawer.Footer, { children: (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center justify-end gap-x-2", children: [
          (0, import_jsx_runtime.jsx)(RouteDrawer.Close, { asChild: true, children: (0, import_jsx_runtime.jsx)(Button, { variant: "secondary", size: "small", children: t2("actions.cancel") }) }),
          (0, import_jsx_runtime.jsx)(
            Button,
            {
              isLoading: isPending,
              type: "submit",
              variant: "primary",
              size: "small",
              disabled: !!Object.keys(form.formState.errors || {}).length,
              children: t2("actions.save")
            }
          )
        ] }) })
      ]
    }
  ) });
};
var OrderCreateRefund = () => {
  const { t: t2 } = useTranslation();
  const params = useParams();
  const { order } = useOrder(params.id, {
    fields: DEFAULT_FIELDS
  });
  const {
    refund_reasons: refundReasons,
    isLoading: isRefundReasonsLoading,
    isError: isRefundReasonsError,
    error: refundReasonsError
  } = useRefundReasons();
  if (isRefundReasonsError) {
    throw refundReasonsError;
  }
  return (0, import_jsx_runtime2.jsxs)(RouteDrawer, { children: [
    (0, import_jsx_runtime2.jsx)(RouteDrawer.Header, { children: (0, import_jsx_runtime2.jsx)(Heading, { children: t2("orders.payment.createRefund") }) }),
    order && !isRefundReasonsLoading && refundReasons && (0, import_jsx_runtime2.jsx)(CreateRefundForm, { order, refundReasons })
  ] });
};
export {
  OrderCreateRefund as Component
};
//# sourceMappingURL=order-create-refund-V5YDN6HL-SHO3N65A.js.map
