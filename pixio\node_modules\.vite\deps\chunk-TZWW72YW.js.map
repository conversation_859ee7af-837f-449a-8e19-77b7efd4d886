{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-G2H6MAK7.mjs"], "sourcesContent": ["import {\n  queryClient\n} from \"./chunk-FXYH54JP.mjs\";\nimport {\n  queryKeysFactory\n} from \"./chunk-774WSTCC.mjs\";\nimport {\n  sdk\n} from \"./chunk-DEQUVHHE.mjs\";\n\n// src/hooks/api/promotions.tsx\nimport {\n  useMutation as useMutation2,\n  useQuery as useQuery2\n} from \"@tanstack/react-query\";\n\n// src/hooks/api/campaigns.tsx\nimport {\n  useMutation,\n  useQuery\n} from \"@tanstack/react-query\";\nvar REGIONS_QUERY_KEY = \"campaigns\";\nvar campaignsQueryKeys = queryKeysFactory(REGIONS_QUERY_KEY);\nvar useCampaign = (id, query, options) => {\n  const { data, ...rest } = useQuery({\n    queryKey: campaignsQueryKeys.detail(id),\n    queryFn: async () => sdk.admin.campaign.retrieve(id, query),\n    ...options\n  });\n  return { ...data, ...rest };\n};\nvar useCampaigns = (query, options) => {\n  const { data, ...rest } = useQuery({\n    queryFn: () => sdk.admin.campaign.list(query),\n    queryKey: campaignsQueryKeys.list(query),\n    ...options\n  });\n  return { ...data, ...rest };\n};\nvar useCreateCampaign = (options) => {\n  return useMutation({\n    mutationFn: (payload) => sdk.admin.campaign.create(payload),\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries({ queryKey: campaignsQueryKeys.lists() });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\nvar useUpdateCampaign = (id, options) => {\n  return useMutation({\n    mutationFn: (payload) => sdk.admin.campaign.update(id, payload),\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries({ queryKey: campaignsQueryKeys.lists() });\n      queryClient.invalidateQueries({ queryKey: campaignsQueryKeys.details() });\n      queryClient.invalidateQueries({ queryKey: promotionsQueryKeys.lists() });\n      queryClient.invalidateQueries({ queryKey: promotionsQueryKeys.details() });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\nvar useDeleteCampaign = (id, options) => {\n  return useMutation({\n    mutationFn: () => sdk.admin.campaign.delete(id),\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries({ queryKey: campaignsQueryKeys.lists() });\n      queryClient.invalidateQueries({ queryKey: campaignsQueryKeys.details() });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\nvar useAddOrRemoveCampaignPromotions = (id, options) => {\n  return useMutation({\n    mutationFn: (payload) => sdk.admin.campaign.batchPromotions(id, payload),\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries({ queryKey: campaignsQueryKeys.details() });\n      queryClient.invalidateQueries({ queryKey: promotionsQueryKeys.lists() });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\n\n// src/hooks/api/promotions.tsx\nvar PROMOTIONS_QUERY_KEY = \"promotions\";\nvar promotionsQueryKeys = {\n  ...queryKeysFactory(PROMOTIONS_QUERY_KEY),\n  // TODO: handle invalidations properly\n  listRules: (id, ruleType, query) => [PROMOTIONS_QUERY_KEY, id, ruleType, query],\n  listRuleAttributes: (ruleType, promotionType) => [\n    PROMOTIONS_QUERY_KEY,\n    ruleType,\n    promotionType\n  ],\n  listRuleValues: (ruleType, ruleValue, query) => [PROMOTIONS_QUERY_KEY, ruleType, ruleValue, query]\n};\nvar usePromotion = (id, options) => {\n  const { data, ...rest } = useQuery2({\n    queryKey: promotionsQueryKeys.detail(id),\n    queryFn: async () => sdk.admin.promotion.retrieve(id),\n    ...options\n  });\n  return { ...data, ...rest };\n};\nvar usePromotionRules = (id, ruleType, query, options) => {\n  const { data, ...rest } = useQuery2({\n    queryKey: promotionsQueryKeys.listRules(id, ruleType, query),\n    queryFn: async () => sdk.admin.promotion.listRules(id, ruleType, query),\n    ...options\n  });\n  return { ...data, ...rest };\n};\nvar usePromotions = (query, options) => {\n  const { data, ...rest } = useQuery2({\n    queryKey: promotionsQueryKeys.list(query),\n    queryFn: async () => sdk.admin.promotion.list(query),\n    ...options\n  });\n  return { ...data, ...rest };\n};\nvar usePromotionRuleAttributes = (ruleType, promotionType, options) => {\n  const { data, ...rest } = useQuery2({\n    queryKey: promotionsQueryKeys.listRuleAttributes(ruleType, promotionType),\n    queryFn: async () => sdk.admin.promotion.listRuleAttributes(ruleType, promotionType),\n    ...options\n  });\n  return { ...data, ...rest };\n};\nvar useDeletePromotion = (id, options) => {\n  return useMutation2({\n    mutationFn: () => sdk.admin.promotion.delete(id),\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries({ queryKey: promotionsQueryKeys.lists() });\n      queryClient.invalidateQueries({\n        queryKey: promotionsQueryKeys.detail(id)\n      });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\nvar useCreatePromotion = (options) => {\n  return useMutation2({\n    mutationFn: (payload) => sdk.admin.promotion.create(payload),\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries({ queryKey: promotionsQueryKeys.lists() });\n      queryClient.invalidateQueries({ queryKey: campaignsQueryKeys.lists() });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\nvar useUpdatePromotion = (id, options) => {\n  return useMutation2({\n    mutationFn: (payload) => sdk.admin.promotion.update(id, payload),\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries({ queryKey: promotionsQueryKeys.all });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\nvar usePromotionAddRules = (id, ruleType, options) => {\n  return useMutation2({\n    mutationFn: (payload) => sdk.admin.promotion.addRules(id, ruleType, payload),\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries({ queryKey: promotionsQueryKeys.all });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\nvar usePromotionRemoveRules = (id, ruleType, options) => {\n  return useMutation2({\n    mutationFn: (payload) => sdk.admin.promotion.removeRules(id, ruleType, payload),\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries({ queryKey: promotionsQueryKeys.all });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\nvar usePromotionUpdateRules = (id, ruleType, options) => {\n  return useMutation2({\n    mutationFn: (payload) => sdk.admin.promotion.updateRules(id, ruleType, payload),\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries({ queryKey: promotionsQueryKeys.all });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\n\nexport {\n  promotionsQueryKeys,\n  usePromotion,\n  usePromotionRules,\n  usePromotions,\n  usePromotionRuleAttributes,\n  useDeletePromotion,\n  useCreatePromotion,\n  useUpdatePromotion,\n  usePromotionAddRules,\n  usePromotionRemoveRules,\n  usePromotionUpdateRules,\n  campaignsQueryKeys,\n  useCampaign,\n  useCampaigns,\n  useCreateCampaign,\n  useUpdateCampaign,\n  useDeleteCampaign,\n  useAddOrRemoveCampaignPromotions\n};\n"], "mappings": ";;;;;;;;;;;;;;;AAqBA,IAAI,oBAAoB;AACxB,IAAI,qBAAqB,iBAAiB,iBAAiB;AAC3D,IAAI,cAAc,CAAC,IAAI,OAAO,YAAY;AACxC,QAAM,EAAE,MAAM,GAAG,KAAK,IAAI,SAAS;AAAA,IACjC,UAAU,mBAAmB,OAAO,EAAE;AAAA,IACtC,SAAS,YAAY,IAAI,MAAM,SAAS,SAAS,IAAI,KAAK;AAAA,IAC1D,GAAG;AAAA,EACL,CAAC;AACD,SAAO,EAAE,GAAG,MAAM,GAAG,KAAK;AAC5B;AACA,IAAI,eAAe,CAAC,OAAO,YAAY;AACrC,QAAM,EAAE,MAAM,GAAG,KAAK,IAAI,SAAS;AAAA,IACjC,SAAS,MAAM,IAAI,MAAM,SAAS,KAAK,KAAK;AAAA,IAC5C,UAAU,mBAAmB,KAAK,KAAK;AAAA,IACvC,GAAG;AAAA,EACL,CAAC;AACD,SAAO,EAAE,GAAG,MAAM,GAAG,KAAK;AAC5B;AACA,IAAI,oBAAoB,CAAC,YAAY;AACnC,SAAO,YAAY;AAAA,IACjB,YAAY,CAAC,YAAY,IAAI,MAAM,SAAS,OAAO,OAAO;AAAA,IAC1D,WAAW,CAAC,MAAM,WAAW,YAAY;AA1C7C;AA2CM,kBAAY,kBAAkB,EAAE,UAAU,mBAAmB,MAAM,EAAE,CAAC;AACtE,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;AACA,IAAI,oBAAoB,CAAC,IAAI,YAAY;AACvC,SAAO,YAAY;AAAA,IACjB,YAAY,CAAC,YAAY,IAAI,MAAM,SAAS,OAAO,IAAI,OAAO;AAAA,IAC9D,WAAW,CAAC,MAAM,WAAW,YAAY;AApD7C;AAqDM,kBAAY,kBAAkB,EAAE,UAAU,mBAAmB,MAAM,EAAE,CAAC;AACtE,kBAAY,kBAAkB,EAAE,UAAU,mBAAmB,QAAQ,EAAE,CAAC;AACxE,kBAAY,kBAAkB,EAAE,UAAU,oBAAoB,MAAM,EAAE,CAAC;AACvE,kBAAY,kBAAkB,EAAE,UAAU,oBAAoB,QAAQ,EAAE,CAAC;AACzE,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;AACA,IAAI,oBAAoB,CAAC,IAAI,YAAY;AACvC,SAAO,YAAY;AAAA,IACjB,YAAY,MAAM,IAAI,MAAM,SAAS,OAAO,EAAE;AAAA,IAC9C,WAAW,CAAC,MAAM,WAAW,YAAY;AAjE7C;AAkEM,kBAAY,kBAAkB,EAAE,UAAU,mBAAmB,MAAM,EAAE,CAAC;AACtE,kBAAY,kBAAkB,EAAE,UAAU,mBAAmB,QAAQ,EAAE,CAAC;AACxE,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;AACA,IAAI,mCAAmC,CAAC,IAAI,YAAY;AACtD,SAAO,YAAY;AAAA,IACjB,YAAY,CAAC,YAAY,IAAI,MAAM,SAAS,gBAAgB,IAAI,OAAO;AAAA,IACvE,WAAW,CAAC,MAAM,WAAW,YAAY;AA5E7C;AA6EM,kBAAY,kBAAkB,EAAE,UAAU,mBAAmB,QAAQ,EAAE,CAAC;AACxE,kBAAY,kBAAkB,EAAE,UAAU,oBAAoB,MAAM,EAAE,CAAC;AACvE,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;AAGA,IAAI,uBAAuB;AAC3B,IAAI,sBAAsB;AAAA,EACxB,GAAG,iBAAiB,oBAAoB;AAAA;AAAA,EAExC,WAAW,CAAC,IAAI,UAAU,UAAU,CAAC,sBAAsB,IAAI,UAAU,KAAK;AAAA,EAC9E,oBAAoB,CAAC,UAAU,kBAAkB;AAAA,IAC/C;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,gBAAgB,CAAC,UAAU,WAAW,UAAU,CAAC,sBAAsB,UAAU,WAAW,KAAK;AACnG;AACA,IAAI,eAAe,CAAC,IAAI,YAAY;AAClC,QAAM,EAAE,MAAM,GAAG,KAAK,IAAI,SAAU;AAAA,IAClC,UAAU,oBAAoB,OAAO,EAAE;AAAA,IACvC,SAAS,YAAY,IAAI,MAAM,UAAU,SAAS,EAAE;AAAA,IACpD,GAAG;AAAA,EACL,CAAC;AACD,SAAO,EAAE,GAAG,MAAM,GAAG,KAAK;AAC5B;AACA,IAAI,oBAAoB,CAAC,IAAI,UAAU,OAAO,YAAY;AACxD,QAAM,EAAE,MAAM,GAAG,KAAK,IAAI,SAAU;AAAA,IAClC,UAAU,oBAAoB,UAAU,IAAI,UAAU,KAAK;AAAA,IAC3D,SAAS,YAAY,IAAI,MAAM,UAAU,UAAU,IAAI,UAAU,KAAK;AAAA,IACtE,GAAG;AAAA,EACL,CAAC;AACD,SAAO,EAAE,GAAG,MAAM,GAAG,KAAK;AAC5B;AACA,IAAI,gBAAgB,CAAC,OAAO,YAAY;AACtC,QAAM,EAAE,MAAM,GAAG,KAAK,IAAI,SAAU;AAAA,IAClC,UAAU,oBAAoB,KAAK,KAAK;AAAA,IACxC,SAAS,YAAY,IAAI,MAAM,UAAU,KAAK,KAAK;AAAA,IACnD,GAAG;AAAA,EACL,CAAC;AACD,SAAO,EAAE,GAAG,MAAM,GAAG,KAAK;AAC5B;AACA,IAAI,6BAA6B,CAAC,UAAU,eAAe,YAAY;AACrE,QAAM,EAAE,MAAM,GAAG,KAAK,IAAI,SAAU;AAAA,IAClC,UAAU,oBAAoB,mBAAmB,UAAU,aAAa;AAAA,IACxE,SAAS,YAAY,IAAI,MAAM,UAAU,mBAAmB,UAAU,aAAa;AAAA,IACnF,GAAG;AAAA,EACL,CAAC;AACD,SAAO,EAAE,GAAG,MAAM,GAAG,KAAK;AAC5B;AACA,IAAI,qBAAqB,CAAC,IAAI,YAAY;AACxC,SAAO,YAAa;AAAA,IAClB,YAAY,MAAM,IAAI,MAAM,UAAU,OAAO,EAAE;AAAA,IAC/C,WAAW,CAAC,MAAM,WAAW,YAAY;AArI7C;AAsIM,kBAAY,kBAAkB,EAAE,UAAU,oBAAoB,MAAM,EAAE,CAAC;AACvE,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,oBAAoB,OAAO,EAAE;AAAA,MACzC,CAAC;AACD,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;AACA,IAAI,qBAAqB,CAAC,YAAY;AACpC,SAAO,YAAa;AAAA,IAClB,YAAY,CAAC,YAAY,IAAI,MAAM,UAAU,OAAO,OAAO;AAAA,IAC3D,WAAW,CAAC,MAAM,WAAW,YAAY;AAlJ7C;AAmJM,kBAAY,kBAAkB,EAAE,UAAU,oBAAoB,MAAM,EAAE,CAAC;AACvE,kBAAY,kBAAkB,EAAE,UAAU,mBAAmB,MAAM,EAAE,CAAC;AACtE,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;AACA,IAAI,qBAAqB,CAAC,IAAI,YAAY;AACxC,SAAO,YAAa;AAAA,IAClB,YAAY,CAAC,YAAY,IAAI,MAAM,UAAU,OAAO,IAAI,OAAO;AAAA,IAC/D,WAAW,CAAC,MAAM,WAAW,YAAY;AA7J7C;AA8JM,kBAAY,kBAAkB,EAAE,UAAU,oBAAoB,IAAI,CAAC;AACnE,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;AACA,IAAI,uBAAuB,CAAC,IAAI,UAAU,YAAY;AACpD,SAAO,YAAa;AAAA,IAClB,YAAY,CAAC,YAAY,IAAI,MAAM,UAAU,SAAS,IAAI,UAAU,OAAO;AAAA,IAC3E,WAAW,CAAC,MAAM,WAAW,YAAY;AAvK7C;AAwKM,kBAAY,kBAAkB,EAAE,UAAU,oBAAoB,IAAI,CAAC;AACnE,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;AACA,IAAI,0BAA0B,CAAC,IAAI,UAAU,YAAY;AACvD,SAAO,YAAa;AAAA,IAClB,YAAY,CAAC,YAAY,IAAI,MAAM,UAAU,YAAY,IAAI,UAAU,OAAO;AAAA,IAC9E,WAAW,CAAC,MAAM,WAAW,YAAY;AAjL7C;AAkLM,kBAAY,kBAAkB,EAAE,UAAU,oBAAoB,IAAI,CAAC;AACnE,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;AACA,IAAI,0BAA0B,CAAC,IAAI,UAAU,YAAY;AACvD,SAAO,YAAa;AAAA,IAClB,YAAY,CAAC,YAAY,IAAI,MAAM,UAAU,YAAY,IAAI,UAAU,OAAO;AAAA,IAC9E,WAAW,CAAC,MAAM,WAAW,YAAY;AA3L7C;AA4LM,kBAAY,kBAAkB,EAAE,UAAU,oBAAoB,IAAI,CAAC;AACnE,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;", "names": []}