import {
  SingleColumnPage
} from "./chunk-3LNIL4XX.js";
import {
  DataTable,
  useDataTableDateFilters
} from "./chunk-GNGA6ZKR.js";
import {
  useQueryParams
} from "./chunk-32T72GVU.js";
import {
  useDate
} from "./chunk-2E2FUO6N.js";
import {
  useExtension
} from "./chunk-FTD3ZWHZ.js";
import "./chunk-CFRQOB2M.js";
import "./chunk-66DVUN72.js";
import "./chunk-BF7OBKIN.js";
import "./chunk-7UAYECTW.js";
import "./chunk-Y3NYV3NU.js";
import "./chunk-WISME5HP.js";
import "./chunk-WAUSOHFQ.js";
import "./chunk-3ORK3QZ4.js";
import "./chunk-3A5TVVNI.js";
import "./chunk-EZ62MM7J.js";
import "./chunk-3R6LYIXM.js";
import "./chunk-2P2O4TMQ.js";
import "./chunk-KI3HEITX.js";
import "./chunk-2VUCAO7L.js";
import "./chunk-PTIULTKD.js";
import "./chunk-KISXY2LD.js";
import "./chunk-L7C3XPHJ.js";
import "./chunk-T7MG2EIJ.js";
import "./chunk-MER4VYPR.js";
import {
  useCustomerGroups,
  useDeleteCustomerGroupLazy
} from "./chunk-2AWKOUCD.js";
import "./chunk-YL3TYBFR.js";
import "./chunk-TZWW72YW.js";
import "./chunk-MSQ25CWB.js";
import "./chunk-ONYSAQ5Z.js";
import "./chunk-NIH7SAXN.js";
import "./chunk-UEJFWAFE.js";
import "./chunk-GR55MFKT.js";
import "./chunk-AAFHKNJG.js";
import "./chunk-7XDBFDTZ.js";
import "./chunk-3JKGO5XL.js";
import "./chunk-OISUTS7G.js";
import "./chunk-AJYMIHLQ.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import "./chunk-JFCIYOH4.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-5GF3RGIE.js";
import {
  useNavigate
} from "./chunk-T7YBVUWZ.js";
import {
  Container,
  PencilSquare,
  Trash,
  createDataTableColumnHelper,
  toast,
  usePrompt
} from "./chunk-YP2LLXWB.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-ZSQ362B4.js";
import {
  keepPreviousData
} from "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/customer-group-list-LTSITGYF.mjs
var import_react = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var PAGE_SIZE = 10;
var CustomerGroupListTable = () => {
  const { t } = useTranslation();
  const { getWidgets } = useExtension();
  const { q, order, offset, created_at, updated_at } = useQueryParams([
    "q",
    "order",
    "offset",
    "created_at",
    "updated_at"
  ]);
  const columns = useColumns();
  const filters = useFilters();
  const { customer_groups, count, isPending, isError, error } = useCustomerGroups(
    {
      q,
      order,
      offset: offset ? parseInt(offset) : void 0,
      limit: PAGE_SIZE,
      created_at: created_at ? JSON.parse(created_at) : void 0,
      updated_at: updated_at ? JSON.parse(updated_at) : void 0,
      fields: "id,name,created_at,updated_at,customers.id"
    },
    {
      placeholderData: keepPreviousData
    }
  );
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime.jsx)(
    SingleColumnPage,
    {
      widgets: {
        before: getWidgets("customer_group.list.before"),
        after: getWidgets("customer_group.list.after")
      },
      children: (0, import_jsx_runtime.jsx)(Container, { className: "overflow-hidden p-0", children: (0, import_jsx_runtime.jsx)(
        DataTable,
        {
          data: customer_groups,
          columns,
          filters,
          heading: t("customerGroups.domain"),
          rowCount: count,
          getRowId: (row) => row.id,
          rowHref: (row) => `/customer-groups/${row.id}`,
          action: {
            label: t("actions.create"),
            to: "/customer-groups/create"
          },
          emptyState: {
            empty: {
              heading: t("customerGroups.list.empty.heading"),
              description: t("customerGroups.list.empty.description")
            },
            filtered: {
              heading: t("customerGroups.list.filtered.heading"),
              description: t("customerGroups.list.filtered.description")
            }
          },
          pageSize: PAGE_SIZE,
          isLoading: isPending
        }
      ) })
    }
  );
};
var columnHelper = createDataTableColumnHelper();
var useColumns = () => {
  const { t } = useTranslation();
  const { getFullDate } = useDate();
  const navigate = useNavigate();
  const prompt = usePrompt();
  const { mutateAsync: deleteCustomerGroup } = useDeleteCustomerGroupLazy();
  const handleDeleteCustomerGroup = (0, import_react.useCallback)(
    async ({ id, name }) => {
      const res = await prompt({
        title: t("customerGroups.delete.title"),
        description: t("customerGroups.delete.description", {
          name
        }),
        verificationText: name,
        verificationInstruction: t("general.typeToConfirm"),
        confirmText: t("actions.delete"),
        cancelText: t("actions.cancel")
      });
      if (!res) {
        return;
      }
      await deleteCustomerGroup(
        { id },
        {
          onSuccess: () => {
            toast.success(t("customerGroups.delete.successToast", { name }));
          },
          onError: (e) => {
            toast.error(e.message);
          }
        }
      );
    },
    [t, prompt, deleteCustomerGroup]
  );
  return (0, import_react.useMemo)(() => {
    return [
      columnHelper.accessor("name", {
        header: t("fields.name"),
        enableSorting: true,
        sortAscLabel: t("filters.sorting.alphabeticallyAsc"),
        sortDescLabel: t("filters.sorting.alphabeticallyDesc")
      }),
      columnHelper.accessor("customers", {
        header: t("customers.domain"),
        cell: ({ row }) => {
          var _a;
          return (0, import_jsx_runtime.jsx)("span", { children: ((_a = row.original.customers) == null ? void 0 : _a.length) ?? 0 });
        }
      }),
      columnHelper.accessor("created_at", {
        header: t("fields.createdAt"),
        cell: ({ row }) => {
          return (0, import_jsx_runtime.jsx)("span", { children: getFullDate({
            date: row.original.created_at,
            includeTime: true
          }) });
        },
        enableSorting: true,
        sortAscLabel: t("filters.sorting.dateAsc"),
        sortDescLabel: t("filters.sorting.dateDesc")
      }),
      columnHelper.accessor("updated_at", {
        header: t("fields.updatedAt"),
        cell: ({ row }) => {
          return (0, import_jsx_runtime.jsx)("span", { children: getFullDate({
            date: row.original.updated_at,
            includeTime: true
          }) });
        },
        enableSorting: true,
        sortAscLabel: t("filters.sorting.dateAsc"),
        sortDescLabel: t("filters.sorting.dateDesc")
      }),
      columnHelper.action({
        actions: [
          [
            {
              icon: (0, import_jsx_runtime.jsx)(PencilSquare, {}),
              label: t("actions.edit"),
              onClick: (row) => {
                navigate(`/customer-groups/${row.row.original.id}/edit`);
              }
            }
          ],
          [
            {
              icon: (0, import_jsx_runtime.jsx)(Trash, {}),
              label: t("actions.delete"),
              onClick: (row) => {
                handleDeleteCustomerGroup({
                  id: row.row.original.id,
                  name: row.row.original.name ?? ""
                });
              }
            }
          ]
        ]
      })
    ];
  }, [t, navigate, getFullDate, handleDeleteCustomerGroup]);
};
var useFilters = () => {
  const dateFilters = useDataTableDateFilters();
  return (0, import_react.useMemo)(() => {
    return dateFilters;
  }, [dateFilters]);
};
var CustomerGroupsList = () => {
  const { getWidgets } = useExtension();
  return (0, import_jsx_runtime2.jsx)(
    SingleColumnPage,
    {
      widgets: {
        after: getWidgets("customer_group.list.after"),
        before: getWidgets("customer_group.list.before")
      },
      children: (0, import_jsx_runtime2.jsx)(CustomerGroupListTable, {})
    }
  );
};
export {
  CustomerGroupsList as Component
};
//# sourceMappingURL=customer-group-list-LTSITGYF-SHPBF6XD.js.map
