import {
  isOptionEnabledInStore
} from "./chunk-6IU4OEGQ.js";
import {
  ShippingOptionPriceType
} from "./chunk-S4XCFSZC.js";
import {
  SwitchBox
} from "./chunk-4LGSZLHH.js";
import {
  useComboboxData
} from "./chunk-CFEMRZCK.js";
import {
  Combobox
} from "./chunk-RC2DY4WG.js";
import "./chunk-EGRHWZRV.js";
import {
  KeyboundForm
} from "./chunk-DP54EP6X.js";
import {
  RouteDrawer,
  useRouteModal
} from "./chunk-MVVOBQIC.js";
import "./chunk-RQF55WOK.js";
import "./chunk-5QX4V4M4.js";
import "./chunk-IA4ROPJA.js";
import {
  t
} from "./chunk-WHQIBI5S.js";
import {
  booleanType,
  nativeEnumType,
  objectType,
  stringType
} from "./chunk-4XXECALA.js";
import "./chunk-NV2N3EWM.js";
import {
  Form,
  useForm
} from "./chunk-XXJU43CK.js";
import {
  useShippingOptions,
  useUpdateShippingOptions
} from "./chunk-MSQ25CWB.js";
import "./chunk-ONYSAQ5Z.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import {
  sdk
} from "./chunk-JFCIYOH4.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-5GF3RGIE.js";
import {
  json,
  useParams
} from "./chunk-T7YBVUWZ.js";
import {
  Button,
  Divider,
  Heading,
  Input,
  RadioGroup,
  toast
} from "./chunk-YP2LLXWB.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-ZSQ362B4.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/location-service-zone-shipping-option-edit-QGVO2QGV.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
function pick(obj, keys) {
  const ret = {};
  keys.forEach((k) => {
    if (k in obj) {
      ret[k] = obj[k];
    }
  });
  return ret;
}
var EditShippingOptionSchema = objectType({
  name: stringType().min(1),
  price_type: nativeEnumType(ShippingOptionPriceType),
  enabled_in_store: booleanType().optional(),
  shipping_profile_id: stringType()
});
var EditShippingOptionForm = ({
  locationId,
  shippingOption,
  type
}) => {
  const { t: t2 } = useTranslation();
  const { handleSuccess } = useRouteModal();
  const isPickup = type === "pickup";
  const shippingProfiles = useComboboxData({
    queryFn: (params) => sdk.admin.shippingProfile.list(params),
    queryKey: ["shipping_profiles"],
    getOptions: (data) => data.shipping_profiles.map((profile) => ({
      label: profile.name,
      value: profile.id
    })),
    defaultValue: shippingOption.shipping_profile_id
  });
  const form = useForm({
    defaultValues: {
      name: shippingOption.name,
      price_type: shippingOption.price_type,
      enabled_in_store: isOptionEnabledInStore(shippingOption),
      shipping_profile_id: shippingOption.shipping_profile_id
    },
    resolver: t(EditShippingOptionSchema)
  });
  const { mutateAsync, isPending: isLoading } = useUpdateShippingOptions(
    shippingOption.id
  );
  const handleSubmit = form.handleSubmit(async (values) => {
    const rules = shippingOption.rules.map((r) => ({
      ...pick(r, ["id", "attribute", "operator", "value"])
    }));
    const storeRule = rules.find((r) => r.attribute === "enabled_in_store");
    if (!storeRule) {
      rules.push({
        value: values.enabled_in_store ? "true" : "false",
        attribute: "enabled_in_store",
        operator: "eq"
      });
    } else {
      storeRule.value = values.enabled_in_store ? "true" : "false";
    }
    await mutateAsync(
      {
        name: values.name,
        price_type: values.price_type,
        shipping_profile_id: values.shipping_profile_id,
        rules
      },
      {
        onSuccess: ({ shipping_option }) => {
          toast.success(
            t2("stockLocations.shippingOptions.edit.successToast", {
              name: shipping_option.name
            })
          );
          handleSuccess(`/settings/locations/${locationId}`);
        },
        onError: (e) => {
          toast.error(e.message);
        }
      }
    );
  });
  return (0, import_jsx_runtime.jsx)(RouteDrawer.Form, { form, children: (0, import_jsx_runtime.jsxs)(KeyboundForm, { onSubmit: handleSubmit, className: "flex flex-1 flex-col", children: [
    (0, import_jsx_runtime.jsx)(RouteDrawer.Body, { children: (0, import_jsx_runtime.jsx)("div", { className: "flex flex-col gap-y-8", children: (0, import_jsx_runtime.jsxs)("div", { className: "flex flex-col gap-y-8", children: [
      !isPickup && (0, import_jsx_runtime.jsx)(
        Form.Field,
        {
          control: form.control,
          name: "price_type",
          render: ({ field }) => {
            return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
              (0, import_jsx_runtime.jsx)(Form.Label, { children: t2(
                "stockLocations.shippingOptions.fields.priceType.label"
              ) }),
              (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsxs)(RadioGroup, { ...field, onValueChange: field.onChange, children: [
                (0, import_jsx_runtime.jsx)(
                  RadioGroup.ChoiceBox,
                  {
                    className: "flex-1",
                    value: "flat",
                    label: t2(
                      "stockLocations.shippingOptions.fields.priceType.options.fixed.label"
                    ),
                    description: t2(
                      "stockLocations.shippingOptions.fields.priceType.options.fixed.hint"
                    )
                  }
                ),
                (0, import_jsx_runtime.jsx)(
                  RadioGroup.ChoiceBox,
                  {
                    className: "flex-1",
                    value: "calculated",
                    label: t2(
                      "stockLocations.shippingOptions.fields.priceType.options.calculated.label"
                    ),
                    description: t2(
                      "stockLocations.shippingOptions.fields.priceType.options.calculated.hint"
                    )
                  }
                )
              ] }) }),
              (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
            ] });
          }
        }
      ),
      (0, import_jsx_runtime.jsxs)("div", { className: "grid gap-y-4", children: [
        (0, import_jsx_runtime.jsx)(
          Form.Field,
          {
            control: form.control,
            name: "name",
            render: ({ field }) => {
              return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("fields.name") }),
                (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(Input, { ...field }) }),
                (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
              ] });
            }
          }
        ),
        (0, import_jsx_runtime.jsx)(
          Form.Field,
          {
            control: form.control,
            name: "shipping_profile_id",
            render: ({ field }) => {
              return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("stockLocations.shippingOptions.fields.profile") }),
                (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(
                  Combobox,
                  {
                    ...field,
                    options: shippingProfiles.options,
                    searchValue: shippingProfiles.searchValue,
                    onSearchValueChange: shippingProfiles.onSearchValueChange,
                    disabled: shippingProfiles.disabled
                  }
                ) }),
                (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
              ] });
            }
          }
        )
      ] }),
      (0, import_jsx_runtime.jsx)(Divider, {}),
      (0, import_jsx_runtime.jsx)(
        SwitchBox,
        {
          control: form.control,
          name: "enabled_in_store",
          label: t2(
            "stockLocations.shippingOptions.fields.enableInStore.label"
          ),
          description: t2(
            "stockLocations.shippingOptions.fields.enableInStore.hint"
          )
        }
      )
    ] }) }) }),
    (0, import_jsx_runtime.jsx)(RouteDrawer.Footer, { children: (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center gap-x-2", children: [
      (0, import_jsx_runtime.jsx)(RouteDrawer.Close, { asChild: true, children: (0, import_jsx_runtime.jsx)(Button, { size: "small", variant: "secondary", children: t2("actions.cancel") }) }),
      (0, import_jsx_runtime.jsx)(Button, { size: "small", type: "submit", isLoading, children: t2("actions.save") })
    ] }) })
  ] }) });
};
var LocationServiceZoneShippingOptionEdit = () => {
  const { t: t2 } = useTranslation();
  const { location_id, so_id } = useParams();
  const { shipping_options, isPending, isFetching, isError, error } = useShippingOptions({
    id: so_id,
    fields: "+service_zone.fulfillment_set.type"
  });
  const shippingOption = shipping_options == null ? void 0 : shipping_options.find((so) => so.id === so_id);
  if (!isPending && !isFetching && !shippingOption) {
    throw json(
      { message: `Shipping option with ID ${so_id} was not found` },
      404
    );
  }
  if (isError) {
    throw error;
  }
  const isPickup = (shippingOption == null ? void 0 : shippingOption.service_zone.fulfillment_set.type) === "pickup";
  return (0, import_jsx_runtime2.jsxs)(RouteDrawer, { children: [
    (0, import_jsx_runtime2.jsx)(RouteDrawer.Header, { children: (0, import_jsx_runtime2.jsx)(Heading, { children: t2(
      `stockLocations.${isPickup ? "pickupOptions" : "shippingOptions"}.edit.header`
    ) }) }),
    shippingOption && (0, import_jsx_runtime2.jsx)(
      EditShippingOptionForm,
      {
        shippingOption,
        locationId: location_id,
        type: shippingOption.service_zone.fulfillment_set.type
      }
    )
  ] });
};
export {
  LocationServiceZoneShippingOptionEdit as Component
};
//# sourceMappingURL=location-service-zone-shipping-option-edit-QGVO2QGV-H4NYADP4.js.map
