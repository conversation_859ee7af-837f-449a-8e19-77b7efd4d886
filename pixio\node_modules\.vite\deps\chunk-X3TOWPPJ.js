import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import {
  Link
} from "./chunk-T7YBVUWZ.js";
import {
  But<PERSON>,
  ExclamationCircle,
  MagnifyingGlass,
  PlusMini,
  Text,
  clx
} from "./chunk-YP2LLXWB.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/chunk-EMIHDNB7.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var NoResults = ({ title, message, className }) => {
  const { t } = useTranslation();
  return (0, import_jsx_runtime.jsx)(
    "div",
    {
      className: clx(
        "flex h-[400px] w-full items-center justify-center",
        className
      ),
      children: (0, import_jsx_runtime.jsxs)("div", { className: "flex flex-col items-center gap-y-2", children: [
        (0, import_jsx_runtime.jsx)(MagnifyingGlass, {}),
        (0, import_jsx_runtime.jsx)(Text, { size: "small", leading: "compact", weight: "plus", children: title ?? t("general.noResultsTitle") }),
        (0, import_jsx_runtime.jsx)(Text, { size: "small", className: "text-ui-fg-subtle", children: message ?? t("general.noResultsMessage") })
      ] })
    }
  );
};
var DefaultButton = ({ action }) => action && (0, import_jsx_runtime.jsx)(Link, { to: action.to, children: (0, import_jsx_runtime.jsx)(Button, { variant: "secondary", size: "small", children: action.label }) });
var TransparentIconLeftButton = ({ action }) => action && (0, import_jsx_runtime.jsx)(Link, { to: action.to, children: (0, import_jsx_runtime.jsxs)(Button, { variant: "transparent", className: "text-ui-fg-interactive", children: [
  (0, import_jsx_runtime.jsx)(PlusMini, {}),
  " ",
  action.label
] }) });
var NoRecords = ({
  title,
  message,
  action,
  className,
  buttonVariant = "default",
  icon = (0, import_jsx_runtime.jsx)(ExclamationCircle, { className: "text-ui-fg-subtle" })
}) => {
  const { t } = useTranslation();
  return (0, import_jsx_runtime.jsxs)(
    "div",
    {
      className: clx(
        "flex h-[150px] w-full flex-col items-center justify-center gap-y-4",
        className
      ),
      children: [
        (0, import_jsx_runtime.jsxs)("div", { className: "flex flex-col items-center gap-y-3", children: [
          icon,
          (0, import_jsx_runtime.jsxs)("div", { className: "flex flex-col items-center gap-y-1", children: [
            (0, import_jsx_runtime.jsx)(Text, { size: "small", leading: "compact", weight: "plus", children: title ?? t("general.noRecordsTitle") }),
            (0, import_jsx_runtime.jsx)(Text, { size: "small", className: "text-ui-fg-muted", children: message ?? t("general.noRecordsMessage") })
          ] })
        ] }),
        buttonVariant === "default" && (0, import_jsx_runtime.jsx)(DefaultButton, { action }),
        buttonVariant === "transparentIconLeft" && (0, import_jsx_runtime.jsx)(TransparentIconLeftButton, { action })
      ]
    }
  );
};

export {
  NoResults,
  NoRecords
};
//# sourceMappingURL=chunk-X3TOWPPJ.js.map
