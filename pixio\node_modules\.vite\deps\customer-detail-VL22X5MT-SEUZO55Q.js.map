{"version": 3, "sources": ["../../@medusajs/dashboard/dist/customer-detail-VL22X5MT.mjs"], "sourcesContent": ["import {\n  useCustomerGroupTableColumns\n} from \"./chunk-ZJRFL6ZN.mjs\";\nimport {\n  useCustomerGroupTableQuery\n} from \"./chunk-MOSRJHJ3.mjs\";\nimport {\n  useOrderTableColumns\n} from \"./chunk-RORIX3PU.mjs\";\nimport {\n  useOrderTableQuery\n} from \"./chunk-XMAWMECC.mjs\";\nimport \"./chunk-5HNRTDDS.mjs\";\nimport \"./chunk-NNBHHXXN.mjs\";\nimport \"./chunk-7DXVXBSA.mjs\";\nimport \"./chunk-PDWBYQOW.mjs\";\nimport \"./chunk-MSDRGCRR.mjs\";\nimport \"./chunk-ADOCJB6L.mjs\";\nimport \"./chunk-LQTHYS2Z.mjs\";\nimport \"./chunk-P3UUX2T6.mjs\";\nimport {\n  _DataTable,\n  useDataTable\n} from \"./chunk-UE6PO4FK.mjs\";\nimport \"./chunk-YEDAFXMB.mjs\";\nimport \"./chunk-AOFGTNG6.mjs\";\nimport {\n  NoRecords\n} from \"./chunk-EMIHDNB7.mjs\";\nimport \"./chunk-MWVM4TYO.mjs\";\nimport {\n  useCustomerGroupTableFilters\n} from \"./chunk-DLZWPHHO.mjs\";\nimport {\n  useOrderTableFilters\n} from \"./chunk-FVK4ZYYM.mjs\";\nimport \"./chunk-SXYXTC2L.mjs\";\nimport \"./chunk-M3VFKDXJ.mjs\";\nimport {\n  TwoColumnPage\n} from \"./chunk-2RQLKDBF.mjs\";\nimport \"./chunk-C76H5USB.mjs\";\nimport \"./chunk-KSV3NQOT.mjs\";\nimport {\n  useExtension\n} from \"./chunk-C5P5PL3E.mjs\";\nimport {\n  SingleColumnPageSkeleton\n} from \"./chunk-LPEUYMRK.mjs\";\nimport {\n  ActionMenu\n} from \"./chunk-3NJTXRIY.mjs\";\nimport \"./chunk-OC7BQLYI.mjs\";\nimport \"./chunk-NYXYHZE6.mjs\";\nimport \"./chunk-Z5UDPQIH.mjs\";\nimport \"./chunk-KOSCMAIC.mjs\";\nimport \"./chunk-X6DSNTTX.mjs\";\nimport \"./chunk-I6E6CALJ.mjs\";\nimport \"./chunk-B4GODIOW.mjs\";\nimport \"./chunk-F6IJV2I2.mjs\";\nimport \"./chunk-QTCZFYFH.mjs\";\nimport \"./chunk-ENV6YVOM.mjs\";\nimport \"./chunk-PIR2H25N.mjs\";\nimport \"./chunk-RLY2SL5E.mjs\";\nimport \"./chunk-C5LYZZZ5.mjs\";\nimport \"./chunk-2ZKVRTBW.mjs\";\nimport \"./chunk-FO3VP56P.mjs\";\nimport \"./chunk-YS65UGPC.mjs\";\nimport {\n  useBatchCustomerCustomerGroups,\n  useCustomer,\n  useCustomerGroups,\n  useDeleteCustomer,\n  useDeleteCustomerAddress,\n  useRemoveCustomersFromGroup\n} from \"./chunk-F6PXCY3N.mjs\";\nimport \"./chunk-3OHH43G6.mjs\";\nimport \"./chunk-G2H6MAK7.mjs\";\nimport \"./chunk-GRT22PE5.mjs\";\nimport \"./chunk-32IQRUVY.mjs\";\nimport {\n  useOrders\n} from \"./chunk-FNYASI54.mjs\";\nimport \"./chunk-FVC7M755.mjs\";\nimport \"./chunk-ZJ3OFMHB.mjs\";\nimport \"./chunk-PNU5HPGY.mjs\";\nimport \"./chunk-V2LANK5S.mjs\";\nimport \"./chunk-QZ6PT4QV.mjs\";\nimport \"./chunk-QL4XKIVL.mjs\";\nimport {\n  productsQueryKeys\n} from \"./chunk-6I62UDJA.mjs\";\nimport {\n  queryClient\n} from \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport {\n  sdk\n} from \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-RPUOO7AV.mjs\";\n\n// src/routes/customers/customer-detail/breadcrumb.tsx\nimport { jsx } from \"react/jsx-runtime\";\nvar CustomerDetailBreadcrumb = (props) => {\n  const { id } = props.params || {};\n  const { customer } = useCustomer(id, void 0, {\n    initialData: props.data,\n    enabled: Boolean(id)\n  });\n  if (!customer) {\n    return null;\n  }\n  const name = [customer.first_name, customer.last_name].filter(Boolean).join(\" \");\n  const display = name || customer.email;\n  return /* @__PURE__ */ jsx(\"span\", { children: display });\n};\n\n// src/routes/customers/customer-detail/customer-detail.tsx\nimport { useLoaderData, useParams } from \"react-router-dom\";\n\n// src/routes/customers/customer-detail/components/customer-address-section/customer-address-section.tsx\nimport { clx, Container, Heading, toast, usePrompt } from \"@medusajs/ui\";\nimport { useTranslation } from \"react-i18next\";\nimport { Trash } from \"@medusajs/icons\";\nimport { Link, useNavigate } from \"react-router-dom\";\n\n// src/components/common/listicle/listicle.tsx\nimport { Text } from \"@medusajs/ui\";\nimport { jsx as jsx2, jsxs } from \"react/jsx-runtime\";\nvar Listicle = ({\n  labelKey,\n  descriptionKey,\n  children\n}) => {\n  return /* @__PURE__ */ jsx2(\"div\", { className: \"flex flex-col gap-2 px-2 pb-2\", children: /* @__PURE__ */ jsx2(\"div\", { className: \"shadow-elevation-card-rest bg-ui-bg-component transition-fg hover:bg-ui-bg-component-hover active:bg-ui-bg-component-pressed group-focus-visible:shadow-borders-interactive-with-active rounded-md px-4 py-2\", children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center gap-4\", children: [\n    /* @__PURE__ */ jsxs(\"div\", { className: \"flex flex-1 flex-col\", children: [\n      /* @__PURE__ */ jsx2(Text, { size: \"small\", leading: \"compact\", weight: \"plus\", children: labelKey }),\n      /* @__PURE__ */ jsx2(Text, { size: \"small\", leading: \"compact\", className: \"text-ui-fg-subtle\", children: descriptionKey })\n    ] }),\n    /* @__PURE__ */ jsx2(\"div\", { className: \"flex size-7 items-center justify-center\", children })\n  ] }) }) });\n};\n\n// src/routes/customers/customer-detail/components/customer-address-section/customer-address-section.tsx\nimport { jsx as jsx3, jsxs as jsxs2 } from \"react/jsx-runtime\";\nvar CustomerAddressSection = ({\n  customer\n}) => {\n  const { t: t2 } = useTranslation();\n  const prompt = usePrompt();\n  const navigate = useNavigate();\n  const { mutateAsync: deleteAddress } = useDeleteCustomerAddress(customer.id);\n  const addresses = customer.addresses ?? [];\n  const handleDelete = async (address) => {\n    const confirm = await prompt({\n      title: t2(\"general.areYouSure\"),\n      description: t2(\"general.areYouSureDescription\", {\n        entity: t2(\"fields.address\"),\n        title: address.address_name ?? \"n/a\"\n      }),\n      verificationInstruction: t2(\"general.typeToConfirm\"),\n      verificationText: address.address_name ?? \"address\",\n      confirmText: t2(\"actions.delete\"),\n      cancelText: t2(\"actions.cancel\")\n    });\n    if (!confirm) {\n      return;\n    }\n    await deleteAddress(address.id, {\n      onSuccess: () => {\n        toast.success(\n          t2(\"general.success\", { name: address.address_name ?? \"address\" })\n        );\n        navigate(`/customers/${customer.id}`, { replace: true });\n      },\n      onError: (e) => {\n        toast.error(e.message);\n      }\n    });\n  };\n  return /* @__PURE__ */ jsxs2(Container, { className: \"p-0\", children: [\n    /* @__PURE__ */ jsxs2(\"div\", { className: \"flex items-center justify-between px-6 py-4\", children: [\n      /* @__PURE__ */ jsx3(Heading, { level: \"h2\", children: t2(\"addresses.title\") }),\n      /* @__PURE__ */ jsx3(Link, { to: `create-address`, className: \"text-ui-fg-muted text-xs\", children: \"Add\" })\n    ] }),\n    addresses.length === 0 && /* @__PURE__ */ jsx3(\n      NoRecords,\n      {\n        className: clx({\n          \"flex h-full flex-col overflow-hidden border-t p-6\": true\n        }),\n        icon: null,\n        title: t2(\"general.noRecordsTitle\"),\n        message: t2(\"general.noRecordsMessage\")\n      }\n    ),\n    addresses.map((address) => {\n      return /* @__PURE__ */ jsx3(\n        Listicle,\n        {\n          labelKey: address.address_name ?? \"n/a\",\n          descriptionKey: [address.address_1, address.address_2].join(\" \"),\n          children: /* @__PURE__ */ jsx3(\n            ActionMenu,\n            {\n              groups: [\n                {\n                  actions: [\n                    {\n                      icon: /* @__PURE__ */ jsx3(Trash, {}),\n                      label: t2(\"actions.delete\"),\n                      onClick: async () => {\n                        await handleDelete(address);\n                      }\n                    }\n                  ]\n                }\n              ]\n            }\n          )\n        },\n        address.id\n      );\n    })\n  ] });\n};\n\n// src/routes/customers/customer-detail/components/customer-general-section/customer-general-section.tsx\nimport { PencilSquare, Trash as Trash2 } from \"@medusajs/icons\";\nimport {\n  Container as Container2,\n  Heading as Heading2,\n  StatusBadge,\n  Text as Text2,\n  toast as toast2,\n  usePrompt as usePrompt2\n} from \"@medusajs/ui\";\nimport { useTranslation as useTranslation2 } from \"react-i18next\";\nimport { useNavigate as useNavigate2 } from \"react-router-dom\";\nimport { jsx as jsx4, jsxs as jsxs3 } from \"react/jsx-runtime\";\nvar CustomerGeneralSection = ({\n  customer\n}) => {\n  const { t: t2 } = useTranslation2();\n  const prompt = usePrompt2();\n  const navigate = useNavigate2();\n  const { mutateAsync } = useDeleteCustomer(customer.id);\n  const name = [customer.first_name, customer.last_name].filter(Boolean).join(\" \");\n  const statusColor = customer.has_account ? \"green\" : \"orange\";\n  const statusText = customer.has_account ? t2(\"customers.fields.registered\") : t2(\"customers.fields.guest\");\n  const handleDelete = async () => {\n    const res = await prompt({\n      title: t2(\"customers.delete.title\"),\n      description: t2(\"customers.delete.description\", {\n        email: customer.email\n      }),\n      verificationInstruction: t2(\"general.typeToConfirm\"),\n      verificationText: customer.email,\n      confirmText: t2(\"actions.delete\"),\n      cancelText: t2(\"actions.cancel\")\n    });\n    if (!res) {\n      return;\n    }\n    await mutateAsync(void 0, {\n      onSuccess: () => {\n        toast2.success(\n          t2(\"customers.delete.successToast\", {\n            email: customer.email\n          })\n        );\n        navigate(\"/customers\", { replace: true });\n      },\n      onError: (error) => {\n        toast2.error(error.message);\n      }\n    });\n  };\n  return /* @__PURE__ */ jsxs3(Container2, { className: \"divide-y p-0\", children: [\n    /* @__PURE__ */ jsxs3(\"div\", { className: \"flex items-center justify-between px-6 py-4\", children: [\n      /* @__PURE__ */ jsx4(Heading2, { children: customer.email }),\n      /* @__PURE__ */ jsxs3(\"div\", { className: \"flex items-center gap-x-2\", children: [\n        /* @__PURE__ */ jsx4(StatusBadge, { color: statusColor, children: statusText }),\n        /* @__PURE__ */ jsx4(\n          ActionMenu,\n          {\n            groups: [\n              {\n                actions: [\n                  {\n                    label: t2(\"actions.edit\"),\n                    icon: /* @__PURE__ */ jsx4(PencilSquare, {}),\n                    to: \"edit\"\n                  }\n                ]\n              },\n              {\n                actions: [\n                  {\n                    label: t2(\"actions.delete\"),\n                    icon: /* @__PURE__ */ jsx4(Trash2, {}),\n                    onClick: handleDelete\n                  }\n                ]\n              }\n            ]\n          }\n        )\n      ] })\n    ] }),\n    /* @__PURE__ */ jsxs3(\"div\", { className: \"text-ui-fg-subtle grid grid-cols-2 items-center px-6 py-4\", children: [\n      /* @__PURE__ */ jsx4(Text2, { size: \"small\", leading: \"compact\", weight: \"plus\", children: t2(\"fields.name\") }),\n      /* @__PURE__ */ jsx4(Text2, { size: \"small\", leading: \"compact\", children: name || \"-\" })\n    ] }),\n    /* @__PURE__ */ jsxs3(\"div\", { className: \"text-ui-fg-subtle grid grid-cols-2 items-center px-6 py-4\", children: [\n      /* @__PURE__ */ jsx4(Text2, { size: \"small\", leading: \"compact\", weight: \"plus\", children: t2(\"fields.company\") }),\n      /* @__PURE__ */ jsx4(Text2, { size: \"small\", leading: \"compact\", children: customer.company_name || \"-\" })\n    ] }),\n    /* @__PURE__ */ jsxs3(\"div\", { className: \"text-ui-fg-subtle grid grid-cols-2 items-center px-6 py-4\", children: [\n      /* @__PURE__ */ jsx4(Text2, { size: \"small\", leading: \"compact\", weight: \"plus\", children: t2(\"fields.phone\") }),\n      /* @__PURE__ */ jsx4(Text2, { size: \"small\", leading: \"compact\", children: customer.phone || \"-\" })\n    ] })\n  ] });\n};\n\n// src/routes/customers/customer-detail/components/customer-group-section/customer-group-section.tsx\nimport {\n  Button,\n  Checkbox,\n  Container as Container3,\n  Heading as Heading3,\n  toast as toast3,\n  usePrompt as usePrompt3\n} from \"@medusajs/ui\";\nimport { createColumnHelper } from \"@tanstack/react-table\";\nimport { t } from \"i18next\";\nimport { useMemo, useState } from \"react\";\nimport { PencilSquare as PencilSquare2, Trash as Trash3 } from \"@medusajs/icons\";\nimport { keepPreviousData } from \"@tanstack/react-query\";\nimport { useTranslation as useTranslation3 } from \"react-i18next\";\nimport { Link as Link2 } from \"react-router-dom\";\nimport { jsx as jsx5, jsxs as jsxs4 } from \"react/jsx-runtime\";\nvar PAGE_SIZE = 10;\nvar PREFIX = \"cusgr\";\nvar CustomerGroupSection = ({\n  customer\n}) => {\n  const prompt = usePrompt3();\n  const [rowSelection, setRowSelection] = useState({});\n  const { raw, searchParams } = useCustomerGroupTableQuery({\n    pageSize: PAGE_SIZE,\n    prefix: PREFIX\n  });\n  const { customer_groups, count, isLoading, isError, error } = useCustomerGroups(\n    {\n      ...searchParams,\n      fields: \"+customers.id\",\n      customers: { id: customer.id }\n    },\n    {\n      placeholderData: keepPreviousData\n    }\n  );\n  const { mutateAsync: batchCustomerCustomerGroups } = useBatchCustomerCustomerGroups(customer.id);\n  const filters = useCustomerGroupTableFilters();\n  const columns = useColumns(customer.id);\n  const { table } = useDataTable({\n    data: customer_groups ?? [],\n    columns,\n    count,\n    getRowId: (row) => row.id,\n    enablePagination: true,\n    enableRowSelection: true,\n    pageSize: PAGE_SIZE,\n    prefix: PREFIX,\n    rowSelection: {\n      state: rowSelection,\n      updater: setRowSelection\n    }\n  });\n  const handleRemove = async () => {\n    const customerGroupIds = Object.keys(rowSelection);\n    const res = await prompt({\n      title: t(\"general.areYouSure\"),\n      description: t(\"customers.groups.removeMany\", {\n        groups: customer_groups?.filter((g) => customerGroupIds.includes(g.id)).map((g) => g.name).join(\",\")\n      }),\n      confirmText: t(\"actions.remove\"),\n      cancelText: t(\"actions.cancel\")\n    });\n    if (!res) {\n      return;\n    }\n    await batchCustomerCustomerGroups(\n      { remove: customerGroupIds },\n      {\n        onSuccess: () => {\n          toast3.success(\n            t(\"customers.groups.removed.success\", {\n              groups: customer_groups.filter((cg) => customerGroupIds.includes(cg.id)).map((cg) => cg?.name)\n            })\n          );\n        },\n        onError: (error2) => {\n          toast3.error(error2.message);\n        }\n      }\n    );\n  };\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsxs4(Container3, { className: \"divide-y p-0\", children: [\n    /* @__PURE__ */ jsxs4(\"div\", { className: \"flex items-center justify-between px-6 py-4\", children: [\n      /* @__PURE__ */ jsx5(Heading3, { level: \"h2\", children: t(\"customerGroups.domain\") }),\n      /* @__PURE__ */ jsx5(Link2, { to: `/customers/${customer.id}/add-customer-groups`, children: /* @__PURE__ */ jsx5(Button, { variant: \"secondary\", size: \"small\", children: t(\"general.add\") }) })\n    ] }),\n    /* @__PURE__ */ jsx5(\n      _DataTable,\n      {\n        table,\n        columns,\n        pageSize: PAGE_SIZE,\n        isLoading,\n        count,\n        prefix: PREFIX,\n        navigateTo: (row) => `/customer-groups/${row.id}`,\n        filters,\n        search: true,\n        pagination: true,\n        orderBy: [\n          { key: \"name\", label: t(\"fields.name\") },\n          { key: \"created_at\", label: t(\"fields.createdAt\") },\n          { key: \"updated_at\", label: t(\"fields.updatedAt\") }\n        ],\n        commands: [\n          {\n            action: handleRemove,\n            label: t(\"actions.remove\"),\n            shortcut: \"r\"\n          }\n        ],\n        queryObject: raw,\n        noRecords: {\n          message: t(\"customers.groups.list.noRecordsMessage\")\n        }\n      }\n    )\n  ] });\n};\nvar CustomerGroupRowActions = ({\n  group,\n  customerId\n}) => {\n  const prompt = usePrompt3();\n  const { t: t2 } = useTranslation3();\n  const { mutateAsync } = useRemoveCustomersFromGroup(group.id);\n  const onRemove = async () => {\n    const res = await prompt({\n      title: t2(\"general.areYouSure\"),\n      description: t2(\"customers.groups.remove\", {\n        name: group.name\n      }),\n      confirmText: t2(\"actions.remove\"),\n      cancelText: t2(\"actions.cancel\")\n    });\n    if (!res) {\n      return;\n    }\n    await mutateAsync([customerId], {\n      onError: (error) => {\n        toast3.error(error.message);\n      }\n    });\n  };\n  return /* @__PURE__ */ jsx5(\n    ActionMenu,\n    {\n      groups: [\n        {\n          actions: [\n            {\n              label: t2(\"actions.edit\"),\n              icon: /* @__PURE__ */ jsx5(PencilSquare2, {}),\n              to: `/customer-groups/${group.id}/edit`\n            },\n            {\n              label: t2(\"actions.remove\"),\n              onClick: onRemove,\n              icon: /* @__PURE__ */ jsx5(Trash3, {})\n            }\n          ]\n        }\n      ]\n    }\n  );\n};\nvar columnHelper = createColumnHelper();\nvar useColumns = (customerId) => {\n  const columns = useCustomerGroupTableColumns();\n  return useMemo(\n    () => [\n      columnHelper.display({\n        id: \"select\",\n        header: ({ table }) => {\n          return /* @__PURE__ */ jsx5(\n            Checkbox,\n            {\n              checked: table.getIsSomePageRowsSelected() ? \"indeterminate\" : table.getIsAllPageRowsSelected(),\n              onCheckedChange: (value) => table.toggleAllPageRowsSelected(!!value)\n            }\n          );\n        },\n        cell: ({ row }) => {\n          return /* @__PURE__ */ jsx5(\n            Checkbox,\n            {\n              checked: row.getIsSelected(),\n              onCheckedChange: (value) => row.toggleSelected(!!value),\n              onClick: (e) => {\n                e.stopPropagation();\n              }\n            }\n          );\n        }\n      }),\n      ...columns,\n      columnHelper.display({\n        id: \"actions\",\n        cell: ({ row }) => /* @__PURE__ */ jsx5(\n          CustomerGroupRowActions,\n          {\n            group: row.original,\n            customerId\n          }\n        )\n      })\n    ],\n    [columns, customerId]\n  );\n};\n\n// src/routes/customers/customer-detail/components/customer-order-section/customer-order-section.tsx\nimport { ArrowPath } from \"@medusajs/icons\";\nimport { Container as Container4, Heading as Heading4 } from \"@medusajs/ui\";\nimport { keepPreviousData as keepPreviousData2 } from \"@tanstack/react-query\";\nimport { createColumnHelper as createColumnHelper2 } from \"@tanstack/react-table\";\nimport { useMemo as useMemo2 } from \"react\";\nimport { useTranslation as useTranslation4 } from \"react-i18next\";\nimport { jsx as jsx6, jsxs as jsxs5 } from \"react/jsx-runtime\";\nvar PREFIX2 = \"cusord\";\nvar PAGE_SIZE2 = 10;\nvar DEFAULT_RELATIONS = \"*customer,*items,*sales_channel\";\nvar DEFAULT_FIELDS = \"id,status,display_id,created_at,email,fulfillment_status,payment_status,total,currency_code\";\nvar CustomerOrderSection = ({\n  customer\n}) => {\n  const { t: t2 } = useTranslation4();\n  const { searchParams, raw } = useOrderTableQuery({\n    pageSize: PAGE_SIZE2,\n    prefix: PREFIX2\n  });\n  const { orders, count, isLoading, isError, error } = useOrders(\n    {\n      customer_id: customer.id,\n      fields: DEFAULT_FIELDS + \",\" + DEFAULT_RELATIONS,\n      ...searchParams\n    },\n    {\n      placeholderData: keepPreviousData2\n    }\n  );\n  const columns = useColumns2();\n  const filters = useOrderTableFilters();\n  const { table } = useDataTable({\n    data: orders ?? [],\n    columns,\n    enablePagination: true,\n    count,\n    pageSize: PAGE_SIZE2,\n    prefix: PREFIX2\n  });\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsxs5(Container4, { className: \"divide-y p-0\", children: [\n    /* @__PURE__ */ jsx6(\"div\", { className: \"flex items-center justify-between px-6 py-4\", children: /* @__PURE__ */ jsx6(Heading4, { level: \"h2\", children: t2(\"orders.domain\") }) }),\n    /* @__PURE__ */ jsx6(\n      _DataTable,\n      {\n        columns,\n        table,\n        pagination: true,\n        navigateTo: (row) => `/orders/${row.original.id}`,\n        filters,\n        count,\n        isLoading,\n        pageSize: PAGE_SIZE2,\n        orderBy: [\n          { key: \"display_id\", label: t2(\"orders.fields.displayId\") },\n          { key: \"created_at\", label: t2(\"fields.createdAt\") },\n          { key: \"updated_at\", label: t2(\"fields.updatedAt\") }\n        ],\n        search: true,\n        queryObject: raw,\n        prefix: PREFIX2\n      }\n    )\n  ] });\n};\nvar CustomerOrderActions = ({ order }) => {\n  const { t: t2 } = useTranslation4();\n  return /* @__PURE__ */ jsx6(\n    ActionMenu,\n    {\n      groups: [\n        {\n          actions: [\n            {\n              label: t2(\"transferOwnership.label\"),\n              to: `${order.id}/transfer`,\n              icon: /* @__PURE__ */ jsx6(ArrowPath, {})\n            }\n          ]\n        }\n      ]\n    }\n  );\n};\nvar columnHelper2 = createColumnHelper2();\nvar useColumns2 = () => {\n  const base = useOrderTableColumns({ exclude: [\"customer\"] });\n  return useMemo2(\n    () => [\n      ...base,\n      columnHelper2.display({\n        id: \"actions\",\n        cell: ({ row }) => /* @__PURE__ */ jsx6(CustomerOrderActions, { order: row.original })\n      })\n    ],\n    [base]\n  );\n};\n\n// src/routes/customers/customer-detail/customer-detail.tsx\nimport { jsx as jsx7, jsxs as jsxs6 } from \"react/jsx-runtime\";\nvar CustomerDetail = () => {\n  const { id } = useParams();\n  const initialData = useLoaderData();\n  const { customer, isLoading, isError, error } = useCustomer(\n    id,\n    { fields: \"+*addresses\" },\n    { initialData }\n  );\n  const { getWidgets } = useExtension();\n  if (isLoading || !customer) {\n    return /* @__PURE__ */ jsx7(SingleColumnPageSkeleton, { sections: 2, showJSON: true, showMetadata: true });\n  }\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsxs6(\n    TwoColumnPage,\n    {\n      widgets: {\n        before: getWidgets(\"customer.details.before\"),\n        after: getWidgets(\"customer.details.after\"),\n        sideAfter: getWidgets(\"customer.details.side.after\"),\n        sideBefore: getWidgets(\"customer.details.side.before\")\n      },\n      data: customer,\n      hasOutlet: true,\n      showJSON: true,\n      showMetadata: true,\n      children: [\n        /* @__PURE__ */ jsxs6(TwoColumnPage.Main, { children: [\n          /* @__PURE__ */ jsx7(CustomerGeneralSection, { customer }),\n          /* @__PURE__ */ jsx7(CustomerOrderSection, { customer }),\n          /* @__PURE__ */ jsx7(CustomerGroupSection, { customer })\n        ] }),\n        /* @__PURE__ */ jsx7(TwoColumnPage.Sidebar, { children: /* @__PURE__ */ jsx7(CustomerAddressSection, { customer }) })\n      ]\n    }\n  );\n};\n\n// src/routes/customers/customer-detail/loader.ts\nvar customerDetailQuery = (id) => ({\n  queryKey: productsQueryKeys.detail(id),\n  queryFn: async () => sdk.admin.customer.retrieve(id, {\n    fields: \"+*addresses\"\n  })\n});\nvar customerLoader = async ({ params }) => {\n  const id = params.id;\n  const query = customerDetailQuery(id);\n  return queryClient.ensureQueryData(query);\n};\nexport {\n  CustomerDetailBreadcrumb as Breadcrumb,\n  CustomerDetail as Component,\n  customerLoader as loader\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsGA,yBAAoB;AA0BpB,IAAAA,sBAAkC;AAgBlC,IAAAA,sBAA2C;AA+F3C,IAAAC,sBAA2C;AAiG3C,mBAAkC;AAKlC,IAAAC,sBAA2C;AA8M3C,IAAAC,gBAAoC;AAEpC,IAAAC,sBAA2C;AAgG3C,IAAAA,sBAA2C;AA9hB3C,IAAI,2BAA2B,CAAC,UAAU;AACxC,QAAM,EAAE,GAAG,IAAI,MAAM,UAAU,CAAC;AAChC,QAAM,EAAE,SAAS,IAAI,YAAY,IAAI,QAAQ;AAAA,IAC3C,aAAa,MAAM;AAAA,IACnB,SAAS,QAAQ,EAAE;AAAA,EACrB,CAAC;AACD,MAAI,CAAC,UAAU;AACb,WAAO;AAAA,EACT;AACA,QAAM,OAAO,CAAC,SAAS,YAAY,SAAS,SAAS,EAAE,OAAO,OAAO,EAAE,KAAK,GAAG;AAC/E,QAAM,UAAU,QAAQ,SAAS;AACjC,aAAuB,wBAAI,QAAQ,EAAE,UAAU,QAAQ,CAAC;AAC1D;AAcA,IAAI,WAAW,CAAC;AAAA,EACd;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACJ,aAAuB,oBAAAC,KAAK,OAAO,EAAE,WAAW,iCAAiC,cAA0B,oBAAAA,KAAK,OAAO,EAAE,WAAW,gNAAgN,cAA0B,0BAAK,OAAO,EAAE,WAAW,2BAA2B,UAAU;AAAA,QAC1Z,0BAAK,OAAO,EAAE,WAAW,wBAAwB,UAAU;AAAA,UACzD,oBAAAA,KAAK,MAAM,EAAE,MAAM,SAAS,SAAS,WAAW,QAAQ,QAAQ,UAAU,SAAS,CAAC;AAAA,UACpF,oBAAAA,KAAK,MAAM,EAAE,MAAM,SAAS,SAAS,WAAW,WAAW,qBAAqB,UAAU,eAAe,CAAC;AAAA,IAC5H,EAAE,CAAC;AAAA,QACa,oBAAAA,KAAK,OAAO,EAAE,WAAW,2CAA2C,SAAS,CAAC;AAAA,EAChG,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;AACX;AAIA,IAAI,yBAAyB,CAAC;AAAA,EAC5B;AACF,MAAM;AACJ,QAAM,EAAE,GAAG,GAAG,IAAI,eAAe;AACjC,QAAM,SAAS,UAAU;AACzB,QAAM,WAAW,YAAY;AAC7B,QAAM,EAAE,aAAa,cAAc,IAAI,yBAAyB,SAAS,EAAE;AAC3E,QAAM,YAAY,SAAS,aAAa,CAAC;AACzC,QAAM,eAAe,OAAO,YAAY;AACtC,UAAM,UAAU,MAAM,OAAO;AAAA,MAC3B,OAAO,GAAG,oBAAoB;AAAA,MAC9B,aAAa,GAAG,iCAAiC;AAAA,QAC/C,QAAQ,GAAG,gBAAgB;AAAA,QAC3B,OAAO,QAAQ,gBAAgB;AAAA,MACjC,CAAC;AAAA,MACD,yBAAyB,GAAG,uBAAuB;AAAA,MACnD,kBAAkB,QAAQ,gBAAgB;AAAA,MAC1C,aAAa,GAAG,gBAAgB;AAAA,MAChC,YAAY,GAAG,gBAAgB;AAAA,IACjC,CAAC;AACD,QAAI,CAAC,SAAS;AACZ;AAAA,IACF;AACA,UAAM,cAAc,QAAQ,IAAI;AAAA,MAC9B,WAAW,MAAM;AACf,cAAM;AAAA,UACJ,GAAG,mBAAmB,EAAE,MAAM,QAAQ,gBAAgB,UAAU,CAAC;AAAA,QACnE;AACA,iBAAS,cAAc,SAAS,EAAE,IAAI,EAAE,SAAS,KAAK,CAAC;AAAA,MACzD;AAAA,MACA,SAAS,CAAC,MAAM;AACd,cAAM,MAAM,EAAE,OAAO;AAAA,MACvB;AAAA,IACF,CAAC;AAAA,EACH;AACA,aAAuB,oBAAAC,MAAM,WAAW,EAAE,WAAW,OAAO,UAAU;AAAA,QACpD,oBAAAA,MAAM,OAAO,EAAE,WAAW,+CAA+C,UAAU;AAAA,UACjF,oBAAAC,KAAK,SAAS,EAAE,OAAO,MAAM,UAAU,GAAG,iBAAiB,EAAE,CAAC;AAAA,UAC9D,oBAAAA,KAAK,MAAM,EAAE,IAAI,kBAAkB,WAAW,4BAA4B,UAAU,MAAM,CAAC;AAAA,IAC7G,EAAE,CAAC;AAAA,IACH,UAAU,WAAW,SAAqB,oBAAAA;AAAA,MACxC;AAAA,MACA;AAAA,QACE,WAAW,IAAI;AAAA,UACb,qDAAqD;AAAA,QACvD,CAAC;AAAA,QACD,MAAM;AAAA,QACN,OAAO,GAAG,wBAAwB;AAAA,QAClC,SAAS,GAAG,0BAA0B;AAAA,MACxC;AAAA,IACF;AAAA,IACA,UAAU,IAAI,CAAC,YAAY;AACzB,iBAAuB,oBAAAA;AAAA,QACrB;AAAA,QACA;AAAA,UACE,UAAU,QAAQ,gBAAgB;AAAA,UAClC,gBAAgB,CAAC,QAAQ,WAAW,QAAQ,SAAS,EAAE,KAAK,GAAG;AAAA,UAC/D,cAA0B,oBAAAA;AAAA,YACxB;AAAA,YACA;AAAA,cACE,QAAQ;AAAA,gBACN;AAAA,kBACE,SAAS;AAAA,oBACP;AAAA,sBACE,UAAsB,oBAAAA,KAAK,OAAO,CAAC,CAAC;AAAA,sBACpC,OAAO,GAAG,gBAAgB;AAAA,sBAC1B,SAAS,YAAY;AACnB,8BAAM,aAAa,OAAO;AAAA,sBAC5B;AAAA,oBACF;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,QACA,QAAQ;AAAA,MACV;AAAA,IACF,CAAC;AAAA,EACH,EAAE,CAAC;AACL;AAeA,IAAI,yBAAyB,CAAC;AAAA,EAC5B;AACF,MAAM;AACJ,QAAM,EAAE,GAAG,GAAG,IAAI,eAAgB;AAClC,QAAM,SAAS,UAAW;AAC1B,QAAM,WAAW,YAAa;AAC9B,QAAM,EAAE,YAAY,IAAI,kBAAkB,SAAS,EAAE;AACrD,QAAM,OAAO,CAAC,SAAS,YAAY,SAAS,SAAS,EAAE,OAAO,OAAO,EAAE,KAAK,GAAG;AAC/E,QAAM,cAAc,SAAS,cAAc,UAAU;AACrD,QAAM,aAAa,SAAS,cAAc,GAAG,6BAA6B,IAAI,GAAG,wBAAwB;AACzG,QAAM,eAAe,YAAY;AAC/B,UAAM,MAAM,MAAM,OAAO;AAAA,MACvB,OAAO,GAAG,wBAAwB;AAAA,MAClC,aAAa,GAAG,gCAAgC;AAAA,QAC9C,OAAO,SAAS;AAAA,MAClB,CAAC;AAAA,MACD,yBAAyB,GAAG,uBAAuB;AAAA,MACnD,kBAAkB,SAAS;AAAA,MAC3B,aAAa,GAAG,gBAAgB;AAAA,MAChC,YAAY,GAAG,gBAAgB;AAAA,IACjC,CAAC;AACD,QAAI,CAAC,KAAK;AACR;AAAA,IACF;AACA,UAAM,YAAY,QAAQ;AAAA,MACxB,WAAW,MAAM;AACf,cAAO;AAAA,UACL,GAAG,iCAAiC;AAAA,YAClC,OAAO,SAAS;AAAA,UAClB,CAAC;AAAA,QACH;AACA,iBAAS,cAAc,EAAE,SAAS,KAAK,CAAC;AAAA,MAC1C;AAAA,MACA,SAAS,CAAC,UAAU;AAClB,cAAO,MAAM,MAAM,OAAO;AAAA,MAC5B;AAAA,IACF,CAAC;AAAA,EACH;AACA,aAAuB,oBAAAC,MAAM,WAAY,EAAE,WAAW,gBAAgB,UAAU;AAAA,QAC9D,oBAAAA,MAAM,OAAO,EAAE,WAAW,+CAA+C,UAAU;AAAA,UACjF,oBAAAC,KAAK,SAAU,EAAE,UAAU,SAAS,MAAM,CAAC;AAAA,UAC3C,oBAAAD,MAAM,OAAO,EAAE,WAAW,6BAA6B,UAAU;AAAA,YAC/D,oBAAAC,KAAK,aAAa,EAAE,OAAO,aAAa,UAAU,WAAW,CAAC;AAAA,YAC9D,oBAAAA;AAAA,UACd;AAAA,UACA;AAAA,YACE,QAAQ;AAAA,cACN;AAAA,gBACE,SAAS;AAAA,kBACP;AAAA,oBACE,OAAO,GAAG,cAAc;AAAA,oBACxB,UAAsB,oBAAAA,KAAK,cAAc,CAAC,CAAC;AAAA,oBAC3C,IAAI;AAAA,kBACN;AAAA,gBACF;AAAA,cACF;AAAA,cACA;AAAA,gBACE,SAAS;AAAA,kBACP;AAAA,oBACE,OAAO,GAAG,gBAAgB;AAAA,oBAC1B,UAAsB,oBAAAA,KAAK,OAAQ,CAAC,CAAC;AAAA,oBACrC,SAAS;AAAA,kBACX;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF,EAAE,CAAC;AAAA,IACL,EAAE,CAAC;AAAA,QACa,oBAAAD,MAAM,OAAO,EAAE,WAAW,6DAA6D,UAAU;AAAA,UAC/F,oBAAAC,KAAK,MAAO,EAAE,MAAM,SAAS,SAAS,WAAW,QAAQ,QAAQ,UAAU,GAAG,aAAa,EAAE,CAAC;AAAA,UAC9F,oBAAAA,KAAK,MAAO,EAAE,MAAM,SAAS,SAAS,WAAW,UAAU,QAAQ,IAAI,CAAC;AAAA,IAC1F,EAAE,CAAC;AAAA,QACa,oBAAAD,MAAM,OAAO,EAAE,WAAW,6DAA6D,UAAU;AAAA,UAC/F,oBAAAC,KAAK,MAAO,EAAE,MAAM,SAAS,SAAS,WAAW,QAAQ,QAAQ,UAAU,GAAG,gBAAgB,EAAE,CAAC;AAAA,UACjG,oBAAAA,KAAK,MAAO,EAAE,MAAM,SAAS,SAAS,WAAW,UAAU,SAAS,gBAAgB,IAAI,CAAC;AAAA,IAC3G,EAAE,CAAC;AAAA,QACa,oBAAAD,MAAM,OAAO,EAAE,WAAW,6DAA6D,UAAU;AAAA,UAC/F,oBAAAC,KAAK,MAAO,EAAE,MAAM,SAAS,SAAS,WAAW,QAAQ,QAAQ,UAAU,GAAG,cAAc,EAAE,CAAC;AAAA,UAC/F,oBAAAA,KAAK,MAAO,EAAE,MAAM,SAAS,SAAS,WAAW,UAAU,SAAS,SAAS,IAAI,CAAC;AAAA,IACpG,EAAE,CAAC;AAAA,EACL,EAAE,CAAC;AACL;AAmBA,IAAI,YAAY;AAChB,IAAI,SAAS;AACb,IAAI,uBAAuB,CAAC;AAAA,EAC1B;AACF,MAAM;AACJ,QAAM,SAAS,UAAW;AAC1B,QAAM,CAAC,cAAc,eAAe,QAAI,uBAAS,CAAC,CAAC;AACnD,QAAM,EAAE,KAAK,aAAa,IAAI,2BAA2B;AAAA,IACvD,UAAU;AAAA,IACV,QAAQ;AAAA,EACV,CAAC;AACD,QAAM,EAAE,iBAAiB,OAAO,WAAW,SAAS,MAAM,IAAI;AAAA,IAC5D;AAAA,MACE,GAAG;AAAA,MACH,QAAQ;AAAA,MACR,WAAW,EAAE,IAAI,SAAS,GAAG;AAAA,IAC/B;AAAA,IACA;AAAA,MACE,iBAAiB;AAAA,IACnB;AAAA,EACF;AACA,QAAM,EAAE,aAAa,4BAA4B,IAAI,+BAA+B,SAAS,EAAE;AAC/F,QAAM,UAAU,6BAA6B;AAC7C,QAAM,UAAU,WAAW,SAAS,EAAE;AACtC,QAAM,EAAE,MAAM,IAAI,aAAa;AAAA,IAC7B,MAAM,mBAAmB,CAAC;AAAA,IAC1B;AAAA,IACA;AAAA,IACA,UAAU,CAAC,QAAQ,IAAI;AAAA,IACvB,kBAAkB;AAAA,IAClB,oBAAoB;AAAA,IACpB,UAAU;AAAA,IACV,QAAQ;AAAA,IACR,cAAc;AAAA,MACZ,OAAO;AAAA,MACP,SAAS;AAAA,IACX;AAAA,EACF,CAAC;AACD,QAAM,eAAe,YAAY;AAC/B,UAAM,mBAAmB,OAAO,KAAK,YAAY;AACjD,UAAM,MAAM,MAAM,OAAO;AAAA,MACvB,OAAO,EAAE,oBAAoB;AAAA,MAC7B,aAAa,EAAE,+BAA+B;AAAA,QAC5C,QAAQ,mDAAiB,OAAO,CAAC,MAAM,iBAAiB,SAAS,EAAE,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,MAAM,KAAK;AAAA,MAClG,CAAC;AAAA,MACD,aAAa,EAAE,gBAAgB;AAAA,MAC/B,YAAY,EAAE,gBAAgB;AAAA,IAChC,CAAC;AACD,QAAI,CAAC,KAAK;AACR;AAAA,IACF;AACA,UAAM;AAAA,MACJ,EAAE,QAAQ,iBAAiB;AAAA,MAC3B;AAAA,QACE,WAAW,MAAM;AACf,gBAAO;AAAA,YACL,EAAE,oCAAoC;AAAA,cACpC,QAAQ,gBAAgB,OAAO,CAAC,OAAO,iBAAiB,SAAS,GAAG,EAAE,CAAC,EAAE,IAAI,CAAC,OAAO,yBAAI,IAAI;AAAA,YAC/F,CAAC;AAAA,UACH;AAAA,QACF;AAAA,QACA,SAAS,CAAC,WAAW;AACnB,gBAAO,MAAM,OAAO,OAAO;AAAA,QAC7B;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,oBAAAC,MAAM,WAAY,EAAE,WAAW,gBAAgB,UAAU;AAAA,QAC9D,oBAAAA,MAAM,OAAO,EAAE,WAAW,+CAA+C,UAAU;AAAA,UACjF,oBAAAC,KAAK,SAAU,EAAE,OAAO,MAAM,UAAU,EAAE,uBAAuB,EAAE,CAAC;AAAA,UACpE,oBAAAA,KAAK,MAAO,EAAE,IAAI,cAAc,SAAS,EAAE,wBAAwB,cAA0B,oBAAAA,KAAK,QAAQ,EAAE,SAAS,aAAa,MAAM,SAAS,UAAU,EAAE,aAAa,EAAE,CAAC,EAAE,CAAC;AAAA,IAClM,EAAE,CAAC;AAAA,QACa,oBAAAA;AAAA,MACd;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,QACA,UAAU;AAAA,QACV;AAAA,QACA;AAAA,QACA,QAAQ;AAAA,QACR,YAAY,CAAC,QAAQ,oBAAoB,IAAI,EAAE;AAAA,QAC/C;AAAA,QACA,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,SAAS;AAAA,UACP,EAAE,KAAK,QAAQ,OAAO,EAAE,aAAa,EAAE;AAAA,UACvC,EAAE,KAAK,cAAc,OAAO,EAAE,kBAAkB,EAAE;AAAA,UAClD,EAAE,KAAK,cAAc,OAAO,EAAE,kBAAkB,EAAE;AAAA,QACpD;AAAA,QACA,UAAU;AAAA,UACR;AAAA,YACE,QAAQ;AAAA,YACR,OAAO,EAAE,gBAAgB;AAAA,YACzB,UAAU;AAAA,UACZ;AAAA,QACF;AAAA,QACA,aAAa;AAAA,QACb,WAAW;AAAA,UACT,SAAS,EAAE,wCAAwC;AAAA,QACrD;AAAA,MACF;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;AACA,IAAI,0BAA0B,CAAC;AAAA,EAC7B;AAAA,EACA;AACF,MAAM;AACJ,QAAM,SAAS,UAAW;AAC1B,QAAM,EAAE,GAAG,GAAG,IAAI,eAAgB;AAClC,QAAM,EAAE,YAAY,IAAI,4BAA4B,MAAM,EAAE;AAC5D,QAAM,WAAW,YAAY;AAC3B,UAAM,MAAM,MAAM,OAAO;AAAA,MACvB,OAAO,GAAG,oBAAoB;AAAA,MAC9B,aAAa,GAAG,2BAA2B;AAAA,QACzC,MAAM,MAAM;AAAA,MACd,CAAC;AAAA,MACD,aAAa,GAAG,gBAAgB;AAAA,MAChC,YAAY,GAAG,gBAAgB;AAAA,IACjC,CAAC;AACD,QAAI,CAAC,KAAK;AACR;AAAA,IACF;AACA,UAAM,YAAY,CAAC,UAAU,GAAG;AAAA,MAC9B,SAAS,CAAC,UAAU;AAClB,cAAO,MAAM,MAAM,OAAO;AAAA,MAC5B;AAAA,IACF,CAAC;AAAA,EACH;AACA,aAAuB,oBAAAA;AAAA,IACrB;AAAA,IACA;AAAA,MACE,QAAQ;AAAA,QACN;AAAA,UACE,SAAS;AAAA,YACP;AAAA,cACE,OAAO,GAAG,cAAc;AAAA,cACxB,UAAsB,oBAAAA,KAAK,cAAe,CAAC,CAAC;AAAA,cAC5C,IAAI,oBAAoB,MAAM,EAAE;AAAA,YAClC;AAAA,YACA;AAAA,cACE,OAAO,GAAG,gBAAgB;AAAA,cAC1B,SAAS;AAAA,cACT,UAAsB,oBAAAA,KAAK,OAAQ,CAAC,CAAC;AAAA,YACvC;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAI,eAAe,mBAAmB;AACtC,IAAI,aAAa,CAAC,eAAe;AAC/B,QAAM,UAAU,6BAA6B;AAC7C,aAAO;AAAA,IACL,MAAM;AAAA,MACJ,aAAa,QAAQ;AAAA,QACnB,IAAI;AAAA,QACJ,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,qBAAuB,oBAAAA;AAAA,YACrB;AAAA,YACA;AAAA,cACE,SAAS,MAAM,0BAA0B,IAAI,kBAAkB,MAAM,yBAAyB;AAAA,cAC9F,iBAAiB,CAAC,UAAU,MAAM,0BAA0B,CAAC,CAAC,KAAK;AAAA,YACrE;AAAA,UACF;AAAA,QACF;AAAA,QACA,MAAM,CAAC,EAAE,IAAI,MAAM;AACjB,qBAAuB,oBAAAA;AAAA,YACrB;AAAA,YACA;AAAA,cACE,SAAS,IAAI,cAAc;AAAA,cAC3B,iBAAiB,CAAC,UAAU,IAAI,eAAe,CAAC,CAAC,KAAK;AAAA,cACtD,SAAS,CAAC,MAAM;AACd,kBAAE,gBAAgB;AAAA,cACpB;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AAAA,MACD,GAAG;AAAA,MACH,aAAa,QAAQ;AAAA,QACnB,IAAI;AAAA,QACJ,MAAM,CAAC,EAAE,IAAI,UAAsB,oBAAAA;AAAA,UACjC;AAAA,UACA;AAAA,YACE,OAAO,IAAI;AAAA,YACX;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAAA,IACA,CAAC,SAAS,UAAU;AAAA,EACtB;AACF;AAUA,IAAI,UAAU;AACd,IAAI,aAAa;AACjB,IAAI,oBAAoB;AACxB,IAAI,iBAAiB;AACrB,IAAI,uBAAuB,CAAC;AAAA,EAC1B;AACF,MAAM;AACJ,QAAM,EAAE,GAAG,GAAG,IAAI,eAAgB;AAClC,QAAM,EAAE,cAAc,IAAI,IAAI,mBAAmB;AAAA,IAC/C,UAAU;AAAA,IACV,QAAQ;AAAA,EACV,CAAC;AACD,QAAM,EAAE,QAAQ,OAAO,WAAW,SAAS,MAAM,IAAI;AAAA,IACnD;AAAA,MACE,aAAa,SAAS;AAAA,MACtB,QAAQ,iBAAiB,MAAM;AAAA,MAC/B,GAAG;AAAA,IACL;AAAA,IACA;AAAA,MACE,iBAAiB;AAAA,IACnB;AAAA,EACF;AACA,QAAM,UAAU,YAAY;AAC5B,QAAM,UAAU,qBAAqB;AACrC,QAAM,EAAE,MAAM,IAAI,aAAa;AAAA,IAC7B,MAAM,UAAU,CAAC;AAAA,IACjB;AAAA,IACA,kBAAkB;AAAA,IAClB;AAAA,IACA,UAAU;AAAA,IACV,QAAQ;AAAA,EACV,CAAC;AACD,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,oBAAAC,MAAM,WAAY,EAAE,WAAW,gBAAgB,UAAU;AAAA,QAC9D,oBAAAC,KAAK,OAAO,EAAE,WAAW,+CAA+C,cAA0B,oBAAAA,KAAK,SAAU,EAAE,OAAO,MAAM,UAAU,GAAG,eAAe,EAAE,CAAC,EAAE,CAAC;AAAA,QAClK,oBAAAA;AAAA,MACd;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,QACA,YAAY;AAAA,QACZ,YAAY,CAAC,QAAQ,WAAW,IAAI,SAAS,EAAE;AAAA,QAC/C;AAAA,QACA;AAAA,QACA;AAAA,QACA,UAAU;AAAA,QACV,SAAS;AAAA,UACP,EAAE,KAAK,cAAc,OAAO,GAAG,yBAAyB,EAAE;AAAA,UAC1D,EAAE,KAAK,cAAc,OAAO,GAAG,kBAAkB,EAAE;AAAA,UACnD,EAAE,KAAK,cAAc,OAAO,GAAG,kBAAkB,EAAE;AAAA,QACrD;AAAA,QACA,QAAQ;AAAA,QACR,aAAa;AAAA,QACb,QAAQ;AAAA,MACV;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;AACA,IAAI,uBAAuB,CAAC,EAAE,MAAM,MAAM;AACxC,QAAM,EAAE,GAAG,GAAG,IAAI,eAAgB;AAClC,aAAuB,oBAAAA;AAAA,IACrB;AAAA,IACA;AAAA,MACE,QAAQ;AAAA,QACN;AAAA,UACE,SAAS;AAAA,YACP;AAAA,cACE,OAAO,GAAG,yBAAyB;AAAA,cACnC,IAAI,GAAG,MAAM,EAAE;AAAA,cACf,UAAsB,oBAAAA,KAAK,WAAW,CAAC,CAAC;AAAA,YAC1C;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAI,gBAAgB,mBAAoB;AACxC,IAAI,cAAc,MAAM;AACtB,QAAM,OAAO,qBAAqB,EAAE,SAAS,CAAC,UAAU,EAAE,CAAC;AAC3D,aAAO,cAAAC;AAAA,IACL,MAAM;AAAA,MACJ,GAAG;AAAA,MACH,cAAc,QAAQ;AAAA,QACpB,IAAI;AAAA,QACJ,MAAM,CAAC,EAAE,IAAI,UAAsB,oBAAAD,KAAK,sBAAsB,EAAE,OAAO,IAAI,SAAS,CAAC;AAAA,MACvF,CAAC;AAAA,IACH;AAAA,IACA,CAAC,IAAI;AAAA,EACP;AACF;AAIA,IAAI,iBAAiB,MAAM;AACzB,QAAM,EAAE,GAAG,IAAI,UAAU;AACzB,QAAM,cAAc,cAAc;AAClC,QAAM,EAAE,UAAU,WAAW,SAAS,MAAM,IAAI;AAAA,IAC9C;AAAA,IACA,EAAE,QAAQ,cAAc;AAAA,IACxB,EAAE,YAAY;AAAA,EAChB;AACA,QAAM,EAAE,WAAW,IAAI,aAAa;AACpC,MAAI,aAAa,CAAC,UAAU;AAC1B,eAAuB,oBAAAE,KAAK,0BAA0B,EAAE,UAAU,GAAG,UAAU,MAAM,cAAc,KAAK,CAAC;AAAA,EAC3G;AACA,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,oBAAAC;AAAA,IACrB;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP,QAAQ,WAAW,yBAAyB;AAAA,QAC5C,OAAO,WAAW,wBAAwB;AAAA,QAC1C,WAAW,WAAW,6BAA6B;AAAA,QACnD,YAAY,WAAW,8BAA8B;AAAA,MACvD;AAAA,MACA,MAAM;AAAA,MACN,WAAW;AAAA,MACX,UAAU;AAAA,MACV,cAAc;AAAA,MACd,UAAU;AAAA,YACQ,oBAAAA,MAAM,cAAc,MAAM,EAAE,UAAU;AAAA,cACpC,oBAAAD,KAAK,wBAAwB,EAAE,SAAS,CAAC;AAAA,cACzC,oBAAAA,KAAK,sBAAsB,EAAE,SAAS,CAAC;AAAA,cACvC,oBAAAA,KAAK,sBAAsB,EAAE,SAAS,CAAC;AAAA,QACzD,EAAE,CAAC;AAAA,YACa,oBAAAA,KAAK,cAAc,SAAS,EAAE,cAA0B,oBAAAA,KAAK,wBAAwB,EAAE,SAAS,CAAC,EAAE,CAAC;AAAA,MACtH;AAAA,IACF;AAAA,EACF;AACF;AAGA,IAAI,sBAAsB,CAAC,QAAQ;AAAA,EACjC,UAAU,kBAAkB,OAAO,EAAE;AAAA,EACrC,SAAS,YAAY,IAAI,MAAM,SAAS,SAAS,IAAI;AAAA,IACnD,QAAQ;AAAA,EACV,CAAC;AACH;AACA,IAAI,iBAAiB,OAAO,EAAE,OAAO,MAAM;AACzC,QAAM,KAAK,OAAO;AAClB,QAAM,QAAQ,oBAAoB,EAAE;AACpC,SAAO,YAAY,gBAAgB,KAAK;AAC1C;", "names": ["import_jsx_runtime", "import_jsx_runtime", "import_jsx_runtime", "import_react", "import_jsx_runtime", "jsx2", "jsxs2", "jsx3", "jsxs3", "jsx4", "jsxs4", "jsx5", "jsxs5", "jsx6", "useMemo2", "jsx7", "jsxs6"]}