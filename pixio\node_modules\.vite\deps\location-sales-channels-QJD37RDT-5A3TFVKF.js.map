{"version": 3, "sources": ["../../@medusajs/dashboard/dist/location-sales-channels-QJD37RDT.mjs"], "sourcesContent": ["import {\n  VisuallyHidden\n} from \"./chunk-F6ZOHZVB.mjs\";\nimport {\n  useSalesChannelTableColumns,\n  useSalesChannelTableEmptyState,\n  useSalesChannelTableFilters,\n  useSalesChannelTableQuery\n} from \"./chunk-KII2CI7D.mjs\";\nimport \"./chunk-FMEOUN2H.mjs\";\nimport {\n  DataTable\n} from \"./chunk-DT3HDJSC.mjs\";\nimport \"./chunk-C76H5USB.mjs\";\nimport \"./chunk-KSV3NQOT.mjs\";\nimport \"./chunk-IUCDCPJU.mjs\";\nimport {\n  KeyboundForm\n} from \"./chunk-6HTZNHPT.mjs\";\nimport {\n  RouteFocusModal,\n  useRouteModal\n} from \"./chunk-4TC5YS65.mjs\";\nimport \"./chunk-3NJTXRIY.mjs\";\nimport \"./chunk-OC7BQLYI.mjs\";\nimport \"./chunk-NYXYHZE6.mjs\";\nimport \"./chunk-OBQI23QM.mjs\";\nimport {\n  useStockLocation,\n  useUpdateStockLocationSalesChannels\n} from \"./chunk-32IQRUVY.mjs\";\nimport {\n  useSalesChannels\n} from \"./chunk-PNU5HPGY.mjs\";\nimport \"./chunk-6I62UDJA.mjs\";\nimport \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-RPUOO7AV.mjs\";\n\n// src/routes/locations/location-sales-channels/location-sales-channels.tsx\nimport { useParams } from \"react-router-dom\";\n\n// src/routes/locations/location-sales-channels/components/edit-sales-channels-form/edit-sales-channels-form.tsx\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport {\n  Button,\n  createDataTableColumnHelper,\n  toast\n} from \"@medusajs/ui\";\nimport { keepPreviousData } from \"@tanstack/react-query\";\nimport { useMemo, useState } from \"react\";\nimport { useForm } from \"react-hook-form\";\nimport { useTranslation } from \"react-i18next\";\nimport * as zod from \"zod\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar EditSalesChannelsSchema = zod.object({\n  sales_channels: zod.array(zod.string()).optional()\n});\nvar PAGE_SIZE = 50;\nvar PREFIX = \"sc\";\nvar LocationEditSalesChannelsForm = ({\n  location\n}) => {\n  const { t } = useTranslation();\n  const { handleSuccess } = useRouteModal();\n  const form = useForm({\n    defaultValues: {\n      sales_channels: location.sales_channels?.map((sc) => sc.id) ?? []\n    },\n    resolver: zodResolver(EditSalesChannelsSchema)\n  });\n  const { setValue } = form;\n  const [rowSelection, setRowSelection] = useState(\n    getInitialState(location)\n  );\n  const onRowSelectionChange = (selection) => {\n    const ids = Object.keys(selection);\n    setValue(\"sales_channels\", ids, {\n      shouldDirty: true,\n      shouldTouch: true\n    });\n    setRowSelection(selection);\n  };\n  const searchParams = useSalesChannelTableQuery({\n    pageSize: PAGE_SIZE,\n    prefix: PREFIX\n  });\n  const { sales_channels, count, isPending, isError, error } = useSalesChannels(\n    {\n      ...searchParams\n    },\n    {\n      placeholderData: keepPreviousData\n    }\n  );\n  const filters = useSalesChannelTableFilters();\n  const columns = useColumns();\n  const emptyState = useSalesChannelTableEmptyState();\n  const { mutateAsync, isPending: isMutating } = useUpdateStockLocationSalesChannels(location.id);\n  const handleSubmit = form.handleSubmit(async (data) => {\n    const originalIds = location.sales_channels?.map((sc) => sc.id);\n    const arr = data.sales_channels ?? [];\n    await mutateAsync(\n      {\n        add: arr.filter((i) => !originalIds?.includes(i)),\n        remove: originalIds?.filter((i) => !arr.includes(i))\n      },\n      {\n        onSuccess: () => {\n          toast.success(t(\"stockLocations.salesChannels.successToast\"));\n          handleSuccess(`/settings/locations/${location.id}`);\n        },\n        onError: (e) => {\n          toast.error(e.message);\n        }\n      }\n    );\n  });\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsx(RouteFocusModal.Form, { form, children: /* @__PURE__ */ jsxs(KeyboundForm, { onSubmit: handleSubmit, className: \"flex h-full flex-col\", children: [\n    /* @__PURE__ */ jsxs(RouteFocusModal.Header, { children: [\n      /* @__PURE__ */ jsx(RouteFocusModal.Title, { asChild: true, children: /* @__PURE__ */ jsx(VisuallyHidden, { children: t(\"stockLocations.salesChannels.header\") }) }),\n      /* @__PURE__ */ jsx(RouteFocusModal.Description, { asChild: true, children: /* @__PURE__ */ jsx(VisuallyHidden, { children: t(\"stockLocations.salesChannels.hint\") }) })\n    ] }),\n    /* @__PURE__ */ jsx(RouteFocusModal.Body, { className: \"flex flex-1 flex-col overflow-auto\", children: /* @__PURE__ */ jsx(\n      DataTable,\n      {\n        data: sales_channels,\n        columns,\n        filters,\n        emptyState,\n        prefix: PREFIX,\n        rowSelection: {\n          state: rowSelection,\n          onRowSelectionChange\n        },\n        pageSize: PAGE_SIZE,\n        isLoading: isPending,\n        rowCount: count,\n        layout: \"fill\",\n        getRowId: (row) => row.id\n      }\n    ) }),\n    /* @__PURE__ */ jsx(RouteFocusModal.Footer, { children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center justify-end gap-x-2\", children: [\n      /* @__PURE__ */ jsx(RouteFocusModal.Close, { asChild: true, children: /* @__PURE__ */ jsx(Button, { size: \"small\", variant: \"secondary\", type: \"button\", children: t(\"actions.cancel\") }) }),\n      /* @__PURE__ */ jsx(Button, { size: \"small\", isLoading: isMutating, type: \"submit\", children: t(\"actions.save\") })\n    ] }) })\n  ] }) });\n};\nvar columnHelper = createDataTableColumnHelper();\nvar useColumns = () => {\n  const base = useSalesChannelTableColumns();\n  return useMemo(() => [columnHelper.select(), ...base], [base]);\n};\nfunction getInitialState(location) {\n  return location.sales_channels?.reduce((acc, curr) => {\n    acc[curr.id] = true;\n    return acc;\n  }, {}) ?? {};\n}\n\n// src/routes/locations/location-sales-channels/location-sales-channels.tsx\nimport { jsx as jsx2 } from \"react/jsx-runtime\";\nvar LocationSalesChannels = () => {\n  const { location_id } = useParams();\n  const { stock_location, isPending, isError, error } = useStockLocation(\n    location_id,\n    {\n      fields: \"id,*sales_channels\"\n    }\n  );\n  const ready = !isPending && !!stock_location;\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsx2(RouteFocusModal, { children: ready && /* @__PURE__ */ jsx2(LocationEditSalesChannelsForm, { location: stock_location }) });\n};\nexport {\n  LocationSalesChannels as Component\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmDA,mBAAkC;AAIlC,yBAA0B;AA8G1B,IAAAA,sBAA4B;AA7G5B,IAAI,0BAA8B,WAAO;AAAA,EACvC,gBAAoB,UAAU,WAAO,CAAC,EAAE,SAAS;AACnD,CAAC;AACD,IAAI,YAAY;AAChB,IAAI,SAAS;AACb,IAAI,gCAAgC,CAAC;AAAA,EACnC;AACF,MAAM;AA/DN;AAgEE,QAAM,EAAE,GAAAC,GAAE,IAAI,eAAe;AAC7B,QAAM,EAAE,cAAc,IAAI,cAAc;AACxC,QAAM,OAAO,QAAQ;AAAA,IACnB,eAAe;AAAA,MACb,kBAAgB,cAAS,mBAAT,mBAAyB,IAAI,CAAC,OAAO,GAAG,QAAO,CAAC;AAAA,IAClE;AAAA,IACA,UAAU,EAAY,uBAAuB;AAAA,EAC/C,CAAC;AACD,QAAM,EAAE,SAAS,IAAI;AACrB,QAAM,CAAC,cAAc,eAAe,QAAI;AAAA,IACtC,gBAAgB,QAAQ;AAAA,EAC1B;AACA,QAAM,uBAAuB,CAAC,cAAc;AAC1C,UAAM,MAAM,OAAO,KAAK,SAAS;AACjC,aAAS,kBAAkB,KAAK;AAAA,MAC9B,aAAa;AAAA,MACb,aAAa;AAAA,IACf,CAAC;AACD,oBAAgB,SAAS;AAAA,EAC3B;AACA,QAAM,eAAe,0BAA0B;AAAA,IAC7C,UAAU;AAAA,IACV,QAAQ;AAAA,EACV,CAAC;AACD,QAAM,EAAE,gBAAgB,OAAO,WAAW,SAAS,MAAM,IAAI;AAAA,IAC3D;AAAA,MACE,GAAG;AAAA,IACL;AAAA,IACA;AAAA,MACE,iBAAiB;AAAA,IACnB;AAAA,EACF;AACA,QAAM,UAAU,4BAA4B;AAC5C,QAAM,UAAU,WAAW;AAC3B,QAAM,aAAa,+BAA+B;AAClD,QAAM,EAAE,aAAa,WAAW,WAAW,IAAI,oCAAoC,SAAS,EAAE;AAC9F,QAAM,eAAe,KAAK,aAAa,OAAO,SAAS;AApGzD,QAAAC;AAqGI,UAAM,eAAcA,MAAA,SAAS,mBAAT,gBAAAA,IAAyB,IAAI,CAAC,OAAO,GAAG;AAC5D,UAAM,MAAM,KAAK,kBAAkB,CAAC;AACpC,UAAM;AAAA,MACJ;AAAA,QACE,KAAK,IAAI,OAAO,CAAC,MAAM,EAAC,2CAAa,SAAS,GAAE;AAAA,QAChD,QAAQ,2CAAa,OAAO,CAAC,MAAM,CAAC,IAAI,SAAS,CAAC;AAAA,MACpD;AAAA,MACA;AAAA,QACE,WAAW,MAAM;AACf,gBAAM,QAAQD,GAAE,2CAA2C,CAAC;AAC5D,wBAAc,uBAAuB,SAAS,EAAE,EAAE;AAAA,QACpD;AAAA,QACA,SAAS,CAAC,MAAM;AACd,gBAAM,MAAM,EAAE,OAAO;AAAA,QACvB;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AACD,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,wBAAI,gBAAgB,MAAM,EAAE,MAAM,cAA0B,yBAAK,cAAc,EAAE,UAAU,cAAc,WAAW,wBAAwB,UAAU;AAAA,QAC3J,yBAAK,gBAAgB,QAAQ,EAAE,UAAU;AAAA,UACvC,wBAAI,gBAAgB,OAAO,EAAE,SAAS,MAAM,cAA0B,wBAAI,gBAAgB,EAAE,UAAUA,GAAE,qCAAqC,EAAE,CAAC,EAAE,CAAC;AAAA,UACnJ,wBAAI,gBAAgB,aAAa,EAAE,SAAS,MAAM,cAA0B,wBAAI,gBAAgB,EAAE,UAAUA,GAAE,mCAAmC,EAAE,CAAC,EAAE,CAAC;AAAA,IACzK,EAAE,CAAC;AAAA,QACa,wBAAI,gBAAgB,MAAM,EAAE,WAAW,sCAAsC,cAA0B;AAAA,MACrH;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA;AAAA,QACA,QAAQ;AAAA,QACR,cAAc;AAAA,UACZ,OAAO;AAAA,UACP;AAAA,QACF;AAAA,QACA,UAAU;AAAA,QACV,WAAW;AAAA,QACX,UAAU;AAAA,QACV,QAAQ;AAAA,QACR,UAAU,CAAC,QAAQ,IAAI;AAAA,MACzB;AAAA,IACF,EAAE,CAAC;AAAA,QACa,wBAAI,gBAAgB,QAAQ,EAAE,cAA0B,yBAAK,OAAO,EAAE,WAAW,yCAAyC,UAAU;AAAA,UAClI,wBAAI,gBAAgB,OAAO,EAAE,SAAS,MAAM,cAA0B,wBAAI,QAAQ,EAAE,MAAM,SAAS,SAAS,aAAa,MAAM,UAAU,UAAUA,GAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;AAAA,UAC3K,wBAAI,QAAQ,EAAE,MAAM,SAAS,WAAW,YAAY,MAAM,UAAU,UAAUA,GAAE,cAAc,EAAE,CAAC;AAAA,IACnH,EAAE,CAAC,EAAE,CAAC;AAAA,EACR,EAAE,CAAC,EAAE,CAAC;AACR;AACA,IAAI,eAAe,4BAA4B;AAC/C,IAAI,aAAa,MAAM;AACrB,QAAM,OAAO,4BAA4B;AACzC,aAAO,sBAAQ,MAAM,CAAC,aAAa,OAAO,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC;AAC/D;AACA,SAAS,gBAAgB,UAAU;AA7JnC;AA8JE,WAAO,cAAS,mBAAT,mBAAyB,OAAO,CAAC,KAAK,SAAS;AACpD,QAAI,KAAK,EAAE,IAAI;AACf,WAAO;AAAA,EACT,GAAG,CAAC,OAAM,CAAC;AACb;AAIA,IAAI,wBAAwB,MAAM;AAChC,QAAM,EAAE,YAAY,IAAI,UAAU;AAClC,QAAM,EAAE,gBAAgB,WAAW,SAAS,MAAM,IAAI;AAAA,IACpD;AAAA,IACA;AAAA,MACE,QAAQ;AAAA,IACV;AAAA,EACF;AACA,QAAM,QAAQ,CAAC,aAAa,CAAC,CAAC;AAC9B,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,oBAAAE,KAAK,iBAAiB,EAAE,UAAU,aAAyB,oBAAAA,KAAK,+BAA+B,EAAE,UAAU,eAAe,CAAC,EAAE,CAAC;AACvJ;", "names": ["import_jsx_runtime", "t", "_a", "jsx2"]}