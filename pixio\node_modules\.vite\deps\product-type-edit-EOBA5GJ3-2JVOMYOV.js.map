{"version": 3, "sources": ["../../@medusajs/dashboard/dist/product-type-edit-EOBA5GJ3.mjs"], "sourcesContent": ["import \"./chunk-IUCDCPJU.mjs\";\nimport {\n  KeyboundForm\n} from \"./chunk-6HTZNHPT.mjs\";\nimport {\n  RouteDrawer,\n  useRouteModal\n} from \"./chunk-4TC5YS65.mjs\";\nimport {\n  Form\n} from \"./chunk-OBQI23QM.mjs\";\nimport {\n  useProductType,\n  useUpdateProductType\n} from \"./chunk-B4GODIOW.mjs\";\nimport \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-RPUOO7AV.mjs\";\n\n// src/routes/product-types/product-type-edit/product-type-edit.tsx\nimport { Heading } from \"@medusajs/ui\";\nimport { useTranslation as useTranslation2 } from \"react-i18next\";\nimport { useParams } from \"react-router-dom\";\n\n// src/routes/product-types/product-type-edit/components/edit-product-type-form/edit-product-type-form.tsx\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { Button, Input, toast } from \"@medusajs/ui\";\nimport { useForm } from \"react-hook-form\";\nimport { useTranslation } from \"react-i18next\";\nimport { z } from \"zod\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar EditProductTypeSchema = z.object({\n  value: z.string().min(1)\n});\nvar EditProductTypeForm = ({\n  productType\n}) => {\n  const { t } = useTranslation();\n  const { handleSuccess } = useRouteModal();\n  const form = useForm({\n    defaultValues: {\n      value: productType.value\n    },\n    resolver: zodResolver(EditProductTypeSchema)\n  });\n  const { mutateAsync, isPending } = useUpdateProductType(productType.id);\n  const handleSubmit = form.handleSubmit(async (data) => {\n    await mutateAsync(\n      {\n        value: data.value\n      },\n      {\n        onSuccess: ({ product_type }) => {\n          toast.success(\n            t(\"productTypes.edit.successToast\", {\n              value: product_type.value\n            })\n          );\n          handleSuccess();\n        },\n        onError: (error) => {\n          toast.error(error.message);\n        }\n      }\n    );\n  });\n  return /* @__PURE__ */ jsx(RouteDrawer.Form, { form, children: /* @__PURE__ */ jsxs(\n    KeyboundForm,\n    {\n      onSubmit: handleSubmit,\n      className: \"flex flex-1 flex-col overflow-hidden\",\n      children: [\n        /* @__PURE__ */ jsx(RouteDrawer.Body, { className: \"flex flex-1 flex-col gap-y-8 overflow-y-auto\", children: /* @__PURE__ */ jsx(\n          Form.Field,\n          {\n            control: form.control,\n            name: \"value\",\n            render: ({ field }) => {\n              return /* @__PURE__ */ jsxs(Form.Item, { children: [\n                /* @__PURE__ */ jsx(Form.Label, { children: t(\"productTypes.fields.value\") }),\n                /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(Input, { ...field }) }),\n                /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n              ] });\n            }\n          }\n        ) }),\n        /* @__PURE__ */ jsx(RouteDrawer.Footer, { children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center justify-end gap-x-2\", children: [\n          /* @__PURE__ */ jsx(RouteDrawer.Close, { asChild: true, children: /* @__PURE__ */ jsx(Button, { size: \"small\", variant: \"secondary\", children: t(\"actions.cancel\") }) }),\n          /* @__PURE__ */ jsx(Button, { size: \"small\", type: \"submit\", isLoading: isPending, children: t(\"actions.save\") })\n        ] }) })\n      ]\n    }\n  ) });\n};\n\n// src/routes/product-types/product-type-edit/product-type-edit.tsx\nimport { jsx as jsx2, jsxs as jsxs2 } from \"react/jsx-runtime\";\nvar ProductTypeEdit = () => {\n  const { id } = useParams();\n  const { t } = useTranslation2();\n  const { product_type, isPending, isError, error } = useProductType(id);\n  const ready = !isPending && !!product_type;\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsxs2(RouteDrawer, { children: [\n    /* @__PURE__ */ jsx2(RouteDrawer.Header, { children: /* @__PURE__ */ jsx2(Heading, { children: t(\"productTypes.edit.header\") }) }),\n    ready && /* @__PURE__ */ jsx2(EditProductTypeForm, { productType: product_type })\n  ] });\n};\nexport {\n  ProductTypeEdit as Component\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+BA,yBAA0B;AAkE1B,IAAAA,sBAA2C;AAjE3C,IAAI,wBAAwB,EAAE,OAAO;AAAA,EACnC,OAAO,EAAE,OAAO,EAAE,IAAI,CAAC;AACzB,CAAC;AACD,IAAI,sBAAsB,CAAC;AAAA,EACzB;AACF,MAAM;AACJ,QAAM,EAAE,GAAAC,GAAE,IAAI,eAAe;AAC7B,QAAM,EAAE,cAAc,IAAI,cAAc;AACxC,QAAM,OAAO,QAAQ;AAAA,IACnB,eAAe;AAAA,MACb,OAAO,YAAY;AAAA,IACrB;AAAA,IACA,UAAU,EAAY,qBAAqB;AAAA,EAC7C,CAAC;AACD,QAAM,EAAE,aAAa,UAAU,IAAI,qBAAqB,YAAY,EAAE;AACtE,QAAM,eAAe,KAAK,aAAa,OAAO,SAAS;AACrD,UAAM;AAAA,MACJ;AAAA,QACE,OAAO,KAAK;AAAA,MACd;AAAA,MACA;AAAA,QACE,WAAW,CAAC,EAAE,aAAa,MAAM;AAC/B,gBAAM;AAAA,YACJA,GAAE,kCAAkC;AAAA,cAClC,OAAO,aAAa;AAAA,YACtB,CAAC;AAAA,UACH;AACA,wBAAc;AAAA,QAChB;AAAA,QACA,SAAS,CAAC,UAAU;AAClB,gBAAM,MAAM,MAAM,OAAO;AAAA,QAC3B;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AACD,aAAuB,wBAAI,YAAY,MAAM,EAAE,MAAM,cAA0B;AAAA,IAC7E;AAAA,IACA;AAAA,MACE,UAAU;AAAA,MACV,WAAW;AAAA,MACX,UAAU;AAAA,YACQ,wBAAI,YAAY,MAAM,EAAE,WAAW,gDAAgD,cAA0B;AAAA,UAC3H,KAAK;AAAA,UACL;AAAA,YACE,SAAS,KAAK;AAAA,YACd,MAAM;AAAA,YACN,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,yBAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,oBACjC,wBAAI,KAAK,OAAO,EAAE,UAAUA,GAAE,2BAA2B,EAAE,CAAC;AAAA,oBAC5D,wBAAI,KAAK,SAAS,EAAE,cAA0B,wBAAI,OAAO,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC;AAAA,oBACxE,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,cAC3C,EAAE,CAAC;AAAA,YACL;AAAA,UACF;AAAA,QACF,EAAE,CAAC;AAAA,YACa,wBAAI,YAAY,QAAQ,EAAE,cAA0B,yBAAK,OAAO,EAAE,WAAW,yCAAyC,UAAU;AAAA,cAC9H,wBAAI,YAAY,OAAO,EAAE,SAAS,MAAM,cAA0B,wBAAI,QAAQ,EAAE,MAAM,SAAS,SAAS,aAAa,UAAUA,GAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;AAAA,cACvJ,wBAAI,QAAQ,EAAE,MAAM,SAAS,MAAM,UAAU,WAAW,WAAW,UAAUA,GAAE,cAAc,EAAE,CAAC;AAAA,QAClH,EAAE,CAAC,EAAE,CAAC;AAAA,MACR;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;AAIA,IAAI,kBAAkB,MAAM;AAC1B,QAAM,EAAE,GAAG,IAAI,UAAU;AACzB,QAAM,EAAE,GAAAA,GAAE,IAAI,eAAgB;AAC9B,QAAM,EAAE,cAAc,WAAW,SAAS,MAAM,IAAI,eAAe,EAAE;AACrE,QAAM,QAAQ,CAAC,aAAa,CAAC,CAAC;AAC9B,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,oBAAAC,MAAM,aAAa,EAAE,UAAU;AAAA,QACpC,oBAAAC,KAAK,YAAY,QAAQ,EAAE,cAA0B,oBAAAA,KAAK,SAAS,EAAE,UAAUF,GAAE,0BAA0B,EAAE,CAAC,EAAE,CAAC;AAAA,IACjI,aAAyB,oBAAAE,KAAK,qBAAqB,EAAE,aAAa,aAAa,CAAC;AAAA,EAClF,EAAE,CAAC;AACL;", "names": ["import_jsx_runtime", "t", "jsxs2", "jsx2"]}