import {
  By
} from "./chunk-VZOXS2K3.js";
import "./chunk-S4XCFSZC.js";
import {
  useCancelExchange,
  useCancelExchangeRequest,
  useExchanges
} from "./chunk-J2IKVYCD.js";
import {
  useCancelOrderEdit,
  useConfirmOrderEdit
} from "./chunk-DUOWWCXF.js";
import {
  OrderPaymentSection,
  getPaymentsFromOrder,
  getTotalCaptured
} from "./chunk-ZS4C5DA2.js";
import {
  formatProvider
} from "./chunk-LVAKEKGS.js";
import {
  formatCurrency
} from "./chunk-SCWUY6XB.js";
import {
  getCanceledOrderStatus,
  getOrderFulfillmentStatus,
  getOrderPaymentStatus
} from "./chunk-5ZQBU3TD.js";
import {
  useCancelClaim,
  useCancelClaimRequest,
  useClaims
} from "./chunk-UWYBVEMG.js";
import {
  getReturnableQuantity
} from "./chunk-PM26KX6Y.js";
import {
  DEFAULT_FIELDS
} from "./chunk-UKYP5JCA.js";
import {
  useCancelReturn,
  useCancelReturnRequest,
  useReturns
} from "./chunk-YJDYMRQS.js";
import {
  getLocaleAmount,
  getStylizedAmount,
  isAmountLessThenRoundingError
} from "./chunk-UDMOPZAP.js";
import {
  getFormattedAddress,
  isSameAddress
} from "./chunk-CBWRFFO7.js";
import {
  TwoColumnPage
} from "./chunk-3LNIL4XX.js";
import "./chunk-H3DTEG3J.js";
import {
  useDate
} from "./chunk-2E2FUO6N.js";
import "./chunk-HPGXK5DQ.js";
import {
  Thumbnail
} from "./chunk-6GQUHAET.js";
import {
  useExtension
} from "./chunk-FTD3ZWHZ.js";
import {
  Skeleton,
  TwoColumnPageSkeleton
} from "./chunk-YXT43UJF.js";
import {
  ActionMenu
} from "./chunk-CFRQOB2M.js";
import "./chunk-66DVUN72.js";
import "./chunk-BF7OBKIN.js";
import {
  format
} from "./chunk-7UAYECTW.js";
import "./chunk-Y3NYV3NU.js";
import {
  useMarkPaymentCollectionAsPaid
} from "./chunk-WISME5HP.js";
import "./chunk-WAUSOHFQ.js";
import "./chunk-3ORK3QZ4.js";
import "./chunk-3A5TVVNI.js";
import "./chunk-EZ62MM7J.js";
import "./chunk-3R6LYIXM.js";
import "./chunk-2P2O4TMQ.js";
import "./chunk-KI3HEITX.js";
import "./chunk-2VUCAO7L.js";
import "./chunk-PTIULTKD.js";
import "./chunk-KISXY2LD.js";
import "./chunk-L7C3XPHJ.js";
import "./chunk-T7MG2EIJ.js";
import "./chunk-MER4VYPR.js";
import {
  useCustomer
} from "./chunk-2AWKOUCD.js";
import "./chunk-YL3TYBFR.js";
import "./chunk-TZWW72YW.js";
import "./chunk-MSQ25CWB.js";
import {
  useStockLocation
} from "./chunk-ONYSAQ5Z.js";
import {
  ordersQueryKeys,
  useCancelOrder,
  useCancelOrderFulfillment,
  useCancelOrderTransfer,
  useMarkOrderFulfillmentAsDelivered,
  useOrder,
  useOrderChanges,
  useOrderLineItems,
  useOrderPreview
} from "./chunk-NIH7SAXN.js";
import {
  useReservationItems
} from "./chunk-UEJFWAFE.js";
import "./chunk-GR55MFKT.js";
import "./chunk-AAFHKNJG.js";
import "./chunk-7XDBFDTZ.js";
import "./chunk-3JKGO5XL.js";
import "./chunk-OISUTS7G.js";
import "./chunk-AJYMIHLQ.js";
import {
  queryClient
} from "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import {
  sdk
} from "./chunk-JFCIYOH4.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-5GF3RGIE.js";
import {
  Link,
  useLoaderData,
  useNavigate,
  useParams
} from "./chunk-T7YBVUWZ.js";
import {
  ArrowDownRightMini,
  ArrowLongRight,
  ArrowPath,
  ArrowUturnLeft,
  Avatar,
  Badge,
  Buildings,
  Button,
  CheckCircleSolid,
  Container,
  Copy,
  CurrencyDollar,
  DocumentText,
  Envelope,
  ExclamationCircle,
  ExclamationCircleSolid,
  FlyingBox,
  Heading,
  InformationCircleSolid,
  PencilSquare,
  Popover,
  SquareTwoStack,
  StatusBadge,
  Text,
  Tooltip,
  TriangleDownMini,
  XCircle,
  clx,
  dist_exports,
  require_copy_to_clipboard,
  toast,
  usePrompt
} from "./chunk-YP2LLXWB.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-ZSQ362B4.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/order-detail-PI2MRGPH.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime4 = __toESM(require_jsx_runtime(), 1);
var import_react = __toESM(require_react(), 1);
var import_jsx_runtime5 = __toESM(require_jsx_runtime(), 1);
var import_react2 = __toESM(require_react(), 1);
var import_react3 = __toESM(require_react(), 1);
var import_jsx_runtime6 = __toESM(require_jsx_runtime(), 1);
var import_react4 = __toESM(require_react(), 1);
var import_jsx_runtime7 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime8 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime9 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime10 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime11 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime12 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime13 = __toESM(require_jsx_runtime(), 1);
var import_react5 = __toESM(require_react(), 1);
var import_copy_to_clipboard = __toESM(require_copy_to_clipboard(), 1);
var import_react6 = __toESM(require_react(), 1);
var import_jsx_runtime14 = __toESM(require_jsx_runtime(), 1);
var import_react7 = __toESM(require_react(), 1);
var import_jsx_runtime15 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime16 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime17 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime18 = __toESM(require_jsx_runtime(), 1);
var OrderDetailBreadcrumb = (props) => {
  const { id } = props.params || {};
  const { order } = useOrder(
    id,
    {
      fields: DEFAULT_FIELDS
    },
    {
      initialData: props.data,
      enabled: Boolean(id)
    }
  );
  if (!order) {
    return null;
  }
  return (0, import_jsx_runtime.jsxs)("span", { children: [
    "#",
    order.display_id
  ] });
};
var orderDetailQuery = (id) => ({
  queryKey: ordersQueryKeys.detail(id),
  queryFn: async () => sdk.admin.order.retrieve(id, {
    fields: DEFAULT_FIELDS
  })
});
var orderLoader = async ({ params }) => {
  const id = params.id;
  const query = orderDetailQuery(id);
  return queryClient.ensureQueryData(query);
};
var ActiveOrderClaimSection = ({
  orderPreview
}) => {
  var _a;
  const { t } = useTranslation();
  const claimId = (_a = orderPreview == null ? void 0 : orderPreview.order_change) == null ? void 0 : _a.claim_id;
  const { mutateAsync: cancelClaim } = useCancelClaimRequest(
    claimId,
    orderPreview.id
  );
  const navigate = useNavigate();
  const onContinueClaim = async () => {
    navigate(`/orders/${orderPreview.id}/claims`);
  };
  const onCancelClaim = async () => {
    await cancelClaim(void 0, {
      onSuccess: () => {
        toast.success(t("orders.claims.toast.canceledSuccessfully"));
      },
      onError: (error) => {
        toast.error(error.message);
      }
    });
  };
  if (!claimId) {
    return;
  }
  return (0, import_jsx_runtime2.jsx)(
    "div",
    {
      style: {
        background: "repeating-linear-gradient(-45deg, rgb(212, 212, 216, 0.15), rgb(212, 212, 216,.15) 10px, transparent 10px, transparent 20px)"
      },
      className: "-m-4 mb-1 border-b border-l p-4",
      children: (0, import_jsx_runtime2.jsx)(Container, { className: "flex items-center justify-between p-0", children: (0, import_jsx_runtime2.jsxs)("div", { className: "flex w-full flex-row justify-between", children: [
        (0, import_jsx_runtime2.jsxs)("div", { children: [
          (0, import_jsx_runtime2.jsxs)("div", { className: "mb-2 flex items-center gap-2 px-6 pt-4", children: [
            (0, import_jsx_runtime2.jsx)(ExclamationCircle, { className: "text-ui-fg-subtle" }),
            (0, import_jsx_runtime2.jsx)(Heading, { level: "h2", children: t("orders.claims.panel.title") })
          ] }),
          (0, import_jsx_runtime2.jsx)("div", { className: "gap-2 px-6 pb-4", children: (0, import_jsx_runtime2.jsx)(Text, { children: t("orders.claims.panel.description") }) })
        ] }),
        (0, import_jsx_runtime2.jsxs)("div", { className: "flex items-center justify-end gap-x-2 rounded-b-xl px-4 py-4", children: [
          (0, import_jsx_runtime2.jsx)(Button, { size: "small", variant: "secondary", onClick: onCancelClaim, children: t("orders.claims.cancel.title") }),
          (0, import_jsx_runtime2.jsx)(Button, { size: "small", variant: "secondary", onClick: onContinueClaim, children: t("actions.continue") })
        ] })
      ] }) })
    }
  );
};
var ActiveOrderExchangeSection = ({
  orderPreview
}) => {
  var _a;
  const { t } = useTranslation();
  const exchangeId = (_a = orderPreview == null ? void 0 : orderPreview.order_change) == null ? void 0 : _a.exchange_id;
  const { mutateAsync: cancelExchange } = useCancelExchangeRequest(
    exchangeId,
    orderPreview.id
  );
  const navigate = useNavigate();
  const onContinueExchange = async () => {
    navigate(`/orders/${orderPreview.id}/exchanges`);
  };
  const onCancelExchange = async () => {
    await cancelExchange(void 0, {
      onSuccess: () => {
        toast.success(t("orders.exchanges.toast.canceledSuccessfully"));
      },
      onError: (error) => {
        toast.error(error.message);
      }
    });
  };
  if (!exchangeId) {
    return;
  }
  return (0, import_jsx_runtime3.jsx)(
    "div",
    {
      style: {
        background: "repeating-linear-gradient(-45deg, rgb(212, 212, 216, 0.15), rgb(212, 212, 216,.15) 10px, transparent 10px, transparent 20px)"
      },
      className: "-m-4 mb-1 border-b border-l p-4",
      children: (0, import_jsx_runtime3.jsx)(Container, { className: "flex items-center justify-between p-0", children: (0, import_jsx_runtime3.jsxs)("div", { className: "flex w-full flex-row justify-between", children: [
        (0, import_jsx_runtime3.jsxs)("div", { children: [
          (0, import_jsx_runtime3.jsxs)("div", { className: "mb-2 flex items-center gap-2 px-6 pt-4", children: [
            (0, import_jsx_runtime3.jsx)(ArrowPath, { className: "text-ui-fg-subtle" }),
            (0, import_jsx_runtime3.jsx)(Heading, { level: "h2", children: t("orders.exchanges.panel.title") })
          ] }),
          (0, import_jsx_runtime3.jsx)("div", { className: "gap-2 px-6 pb-4", children: (0, import_jsx_runtime3.jsx)(Text, { children: t("orders.exchanges.panel.description") }) })
        ] }),
        (0, import_jsx_runtime3.jsxs)("div", { className: "flex items-center justify-end gap-x-2 rounded-b-xl px-4 py-4", children: [
          (0, import_jsx_runtime3.jsx)(Button, { size: "small", variant: "secondary", onClick: onCancelExchange, children: t("orders.exchanges.cancel.title") }),
          (0, import_jsx_runtime3.jsx)(
            Button,
            {
              size: "small",
              variant: "secondary",
              onClick: onContinueExchange,
              children: t("actions.continue")
            }
          )
        ] })
      ] }) })
    }
  );
};
var ActiveOrderReturnSection = ({
  orderPreview
}) => {
  const { t } = useTranslation();
  const orderChange = orderPreview == null ? void 0 : orderPreview.order_change;
  const returnId = orderChange == null ? void 0 : orderChange.return_id;
  const isReturnRequest = (orderChange == null ? void 0 : orderChange.change_type) === "return_request" && !!orderChange.return_id;
  const { mutateAsync: cancelReturn } = useCancelReturnRequest(
    returnId,
    orderPreview.id
  );
  const navigate = useNavigate();
  const onContinueReturn = async () => {
    navigate(`/orders/${orderPreview.id}/returns`);
  };
  const onCancelReturn = async () => {
    await cancelReturn(void 0, {
      onSuccess: () => {
        toast.success(t("orders.returns.toast.canceledSuccessfully"));
      },
      onError: (error) => {
        toast.error(error.message);
      }
    });
  };
  if (!returnId || !isReturnRequest) {
    return;
  }
  return (0, import_jsx_runtime4.jsx)(
    "div",
    {
      style: {
        background: "repeating-linear-gradient(-45deg, rgb(212, 212, 216, 0.15), rgb(212, 212, 216,.15) 10px, transparent 10px, transparent 20px)"
      },
      className: "-m-4 mb-1 border-b border-l p-4",
      children: (0, import_jsx_runtime4.jsx)(Container, { className: "flex items-center justify-between p-0", children: (0, import_jsx_runtime4.jsxs)("div", { className: "flex w-full flex-row justify-between", children: [
        (0, import_jsx_runtime4.jsxs)("div", { children: [
          (0, import_jsx_runtime4.jsxs)("div", { className: "mb-2 flex items-center gap-2 px-6 pt-4", children: [
            (0, import_jsx_runtime4.jsx)(ArrowUturnLeft, { className: "text-ui-fg-subtle" }),
            (0, import_jsx_runtime4.jsx)(Heading, { level: "h2", children: t("orders.returns.panel.title") })
          ] }),
          (0, import_jsx_runtime4.jsx)("div", { className: "gap-2 px-6 pb-4", children: (0, import_jsx_runtime4.jsx)(Text, { children: t("orders.returns.panel.description") }) })
        ] }),
        (0, import_jsx_runtime4.jsxs)("div", { className: "flex items-center justify-end gap-x-2 rounded-b-xl px-4 py-4", children: [
          (0, import_jsx_runtime4.jsx)(Button, { size: "small", variant: "secondary", onClick: onCancelReturn, children: t("orders.returns.cancel.title") }),
          (0, import_jsx_runtime4.jsx)(Button, { size: "small", variant: "secondary", onClick: onContinueReturn, children: t("actions.continue") })
        ] })
      ] }) })
    }
  );
};
function EditItem({
  item,
  quantity
}) {
  return (0, import_jsx_runtime5.jsx)("div", { className: "text-ui-fg-subtle items-center gap-x-2", children: (0, import_jsx_runtime5.jsxs)("div", { className: "flex items-center gap-x-2", children: [
    (0, import_jsx_runtime5.jsxs)("div", { className: "w-fit min-w-[27px]", children: [
      (0, import_jsx_runtime5.jsx)("span", { className: "txt-small tabular-nums", children: quantity }),
      "x"
    ] }),
    (0, import_jsx_runtime5.jsx)(Thumbnail, { src: item.thumbnail }),
    (0, import_jsx_runtime5.jsx)("span", { className: "txt-small text-ui-fg-subtle font-medium", children: item.title }),
    item.variant_sku && " · ",
    item.variant_sku && (0, import_jsx_runtime5.jsxs)("div", { className: "flex items-center gap-x-1", children: [
      (0, import_jsx_runtime5.jsx)("span", { className: "txt-small", children: item.variant_sku }),
      (0, import_jsx_runtime5.jsx)(Copy, { content: item.variant_sku, className: "text-ui-fg-muted" })
    ] })
  ] }) }, item.id);
}
var OrderActiveEditSection = ({
  order
}) => {
  var _a, _b;
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { order: orderPreview } = useOrderPreview(order.id);
  const { mutateAsync: cancelOrderEdit } = useCancelOrderEdit(order.id);
  const { mutateAsync: confirmOrderEdit } = useConfirmOrderEdit(order.id);
  const isPending = ((_a = orderPreview.order_change) == null ? void 0 : _a.status) === "pending";
  const [addedItems, removedItems] = (0, import_react.useMemo)(() => {
    const added = [];
    const removed = [];
    const orderLookupMap = new Map(order.items.map((i) => [i.id, i]));
    ((orderPreview == null ? void 0 : orderPreview.items) || []).forEach((currentItem) => {
      const originalItem = orderLookupMap.get(currentItem.id);
      if (!originalItem) {
        added.push({ item: currentItem, quantity: currentItem.quantity });
        return;
      }
      if (originalItem.quantity > currentItem.quantity) {
        removed.push({
          item: currentItem,
          quantity: originalItem.quantity - currentItem.quantity
        });
      }
      if (originalItem.quantity < currentItem.quantity) {
        added.push({
          item: currentItem,
          quantity: currentItem.quantity - originalItem.quantity
        });
      }
    });
    return [added, removed];
  }, [orderPreview]);
  const onConfirmOrderEdit = async () => {
    try {
      await confirmOrderEdit();
      toast.success(t("orders.edits.toast.confirmedSuccessfully"));
    } catch (e) {
      toast.error(e.message);
    }
  };
  const onCancelOrderEdit = async () => {
    try {
      await cancelOrderEdit();
      toast.success(t("orders.edits.toast.canceledSuccessfully"));
    } catch (e) {
      toast.error(e.message);
    }
  };
  if (!orderPreview || ((_b = orderPreview.order_change) == null ? void 0 : _b.change_type) !== "edit") {
    return null;
  }
  return (0, import_jsx_runtime5.jsx)(
    "div",
    {
      style: {
        background: "repeating-linear-gradient(-45deg, rgb(212, 212, 216, 0.15), rgb(212, 212, 216,.15) 10px, transparent 10px, transparent 20px)"
      },
      className: "-m-4 mb-1 border-b border-l p-4",
      children: (0, import_jsx_runtime5.jsx)(Container, { className: "flex items-center justify-between p-0", children: (0, import_jsx_runtime5.jsxs)("div", { className: "flex w-full flex-col divide-y divide-dashed", children: [
        (0, import_jsx_runtime5.jsxs)("div", { className: "flex items-center gap-2 px-6 py-4", children: [
          (0, import_jsx_runtime5.jsx)(ExclamationCircleSolid, { className: "text-blue-500" }),
          (0, import_jsx_runtime5.jsx)(Heading, { level: "h2", children: t(
            isPending ? "orders.edits.panel.titlePending" : "orders.edits.panel.title"
          ) })
        ] }),
        !!addedItems.length && (0, import_jsx_runtime5.jsxs)("div", { className: "txt-small text-ui-fg-subtle flex flex-row px-6 py-4", children: [
          (0, import_jsx_runtime5.jsx)("span", { className: "flex-1 font-medium", children: t("labels.added") }),
          (0, import_jsx_runtime5.jsx)("div", { className: "flex flex-1 flex-col gap-y-2", children: addedItems.map(({ item, quantity }) => (0, import_jsx_runtime5.jsx)(EditItem, { item, quantity }, item.id)) })
        ] }),
        !!removedItems.length && (0, import_jsx_runtime5.jsxs)("div", { className: "txt-small text-ui-fg-subtle flex flex-row px-6 py-4", children: [
          (0, import_jsx_runtime5.jsx)("span", { className: "flex-1 font-medium", children: t("labels.removed") }),
          (0, import_jsx_runtime5.jsx)("div", { className: "flex flex-1 flex-col gap-y-2", children: removedItems.map(({ item, quantity }) => (0, import_jsx_runtime5.jsx)(EditItem, { item, quantity }, item.id)) })
        ] }),
        (0, import_jsx_runtime5.jsxs)("div", { className: "bg-ui-bg-subtle flex items-center justify-end gap-x-2 rounded-b-xl px-4 py-4", children: [
          isPending ? (0, import_jsx_runtime5.jsx)(
            Button,
            {
              size: "small",
              variant: "secondary",
              onClick: () => navigate(`/orders/${order.id}/edits`),
              children: t("actions.continueEdit")
            }
          ) : (0, import_jsx_runtime5.jsx)(
            Button,
            {
              size: "small",
              variant: "secondary",
              onClick: onConfirmOrderEdit,
              children: t("actions.forceConfirm")
            }
          ),
          (0, import_jsx_runtime5.jsx)(
            Button,
            {
              size: "small",
              variant: "secondary",
              onClick: onCancelOrderEdit,
              children: t("actions.cancel")
            }
          )
        ] })
      ] }) })
    }
  );
};
function ActivityItems(props) {
  const { t } = useTranslation();
  const [open, setOpen] = (0, import_react3.useState)(false);
  const itemsToSend = props.itemsToSend;
  const itemsToReturn = props.itemsToReturn;
  const itemsMap = props.itemsMap;
  const title = props.title;
  const handleMouseEnter = () => {
    setOpen(true);
  };
  const handleMouseLeave = () => {
    setOpen(false);
  };
  if (!(itemsToSend == null ? void 0 : itemsToSend.length) && !(itemsToReturn == null ? void 0 : itemsToReturn.length)) {
    return;
  }
  return (0, import_jsx_runtime6.jsxs)(Popover, { open, children: [
    (0, import_jsx_runtime6.jsx)(
      Popover.Trigger,
      {
        onMouseEnter: handleMouseEnter,
        onMouseLeave: handleMouseLeave,
        autoFocus: false,
        className: "focus-visible:outline-none",
        children: (0, import_jsx_runtime6.jsx)(Text, { size: "small", leading: "compact", weight: "plus", children: title })
      }
    ),
    (0, import_jsx_runtime6.jsx)(
      Popover.Content,
      {
        align: "center",
        side: "top",
        className: "bg-ui-bg-component max-w-[200px] p-0 focus-visible:outline-none",
        children: (0, import_jsx_runtime6.jsxs)("div", { className: "flex flex-col", children: [
          !!(itemsToSend == null ? void 0 : itemsToSend.length) && (0, import_jsx_runtime6.jsxs)("div", { className: "p-3", children: [
            (0, import_jsx_runtime6.jsx)("div", { className: "txt-compact-small-plus mb-1", children: t("orders.activity.events.common.toSend") }),
            (0, import_jsx_runtime6.jsxs)("div", { className: "flex flex-col", children: [
              itemsToSend == null ? void 0 : itemsToSend.map((item) => {
                const originalItem = itemsMap == null ? void 0 : itemsMap.get(item.item_id);
                return (0, import_jsx_runtime6.jsxs)("div", { className: "flex items-center gap-x-3", children: [
                  (0, import_jsx_runtime6.jsxs)(Text, { size: "small", className: "text-ui-fg-subtle", children: [
                    item.quantity,
                    "x"
                  ] }),
                  (0, import_jsx_runtime6.jsx)(Thumbnail, { src: originalItem == null ? void 0 : originalItem.thumbnail }),
                  (0, import_jsx_runtime6.jsx)(Text, { className: "txt-compact-small text-ui-fg-subtle truncate", children: `${originalItem == null ? void 0 : originalItem.variant_title} · ${originalItem == null ? void 0 : originalItem.product_title}` })
                ] }, item.id);
              }),
              (0, import_jsx_runtime6.jsx)("div", { className: "flex flex-1 flex-row items-center gap-2" })
            ] })
          ] }),
          !!(itemsToReturn == null ? void 0 : itemsToReturn.length) && (0, import_jsx_runtime6.jsxs)("div", { className: "border-t-2 border-dotted p-3", children: [
            (0, import_jsx_runtime6.jsx)("div", { className: "txt-compact-small-plus mb-1", children: t("orders.activity.events.common.toReturn") }),
            (0, import_jsx_runtime6.jsxs)("div", { className: "flex flex-col", children: [
              itemsToReturn == null ? void 0 : itemsToReturn.map((item) => {
                const originalItem = itemsMap == null ? void 0 : itemsMap.get(item.item_id);
                return (0, import_jsx_runtime6.jsxs)("div", { className: "flex items-center gap-x-3", children: [
                  (0, import_jsx_runtime6.jsxs)(Text, { size: "small", className: "text-ui-fg-subtle", children: [
                    item.quantity,
                    "x"
                  ] }),
                  (0, import_jsx_runtime6.jsx)(Thumbnail, { src: originalItem == null ? void 0 : originalItem.thumbnail }),
                  (0, import_jsx_runtime6.jsx)(Text, { className: "txt-compact-small text-ui-fg-subtle truncate", children: `${originalItem == null ? void 0 : originalItem.variant_title} · ${originalItem == null ? void 0 : originalItem.product_title}` })
                ] }, item.id);
              }),
              (0, import_jsx_runtime6.jsx)("div", { className: "flex flex-1 flex-row items-center gap-2" })
            ] })
          ] })
        ] })
      }
    )
  ] });
}
var activity_items_default = ActivityItems;
function ChangeDetailsTooltip(props) {
  const { t } = useTranslation();
  const [open, setOpen] = (0, import_react4.useState)(false);
  const previous = props.previous;
  const next = props.next;
  const title = props.title;
  const handleMouseEnter = () => {
    setOpen(true);
  };
  const handleMouseLeave = () => {
    setOpen(false);
  };
  if (!previous && !next) {
    return null;
  }
  return (0, import_jsx_runtime7.jsxs)(Popover, { open, children: [
    (0, import_jsx_runtime7.jsx)(
      Popover.Trigger,
      {
        onMouseEnter: handleMouseEnter,
        onMouseLeave: handleMouseLeave,
        autoFocus: false,
        className: "focus-visible:outline-none",
        children: (0, import_jsx_runtime7.jsx)(Text, { size: "small", leading: "compact", weight: "plus", children: title })
      }
    ),
    (0, import_jsx_runtime7.jsx)(
      Popover.Content,
      {
        align: "center",
        side: "top",
        className: "bg-ui-bg-component max-w-[200px] p-0 focus-visible:outline-none",
        children: (0, import_jsx_runtime7.jsxs)("div", { className: "flex flex-col", children: [
          !!previous && (0, import_jsx_runtime7.jsxs)("div", { className: "p-3", children: [
            (0, import_jsx_runtime7.jsx)("div", { className: "txt-compact-small-plus mb-1", children: t("labels.from") }),
            (0, import_jsx_runtime7.jsx)("p", { className: "txt-compact-small text-ui-fg-subtle", children: previous })
          ] }),
          !!next && (0, import_jsx_runtime7.jsxs)("div", { className: "border-t-2 border-dotted p-3", children: [
            (0, import_jsx_runtime7.jsx)("div", { className: "txt-compact-small-plus mb-1", children: t("labels.to") }),
            (0, import_jsx_runtime7.jsx)("p", { className: "txt-compact-small text-ui-fg-subtle", children: next })
          ] })
        ] })
      }
    )
  ] });
}
var change_details_tooltip_default = ChangeDetailsTooltip;
var NON_RMA_CHANGE_TYPES = ["transfer", "update_order"];
var OrderTimeline = ({ order }) => {
  const items = useActivityItems(order);
  if (items.length <= 3) {
    return (0, import_jsx_runtime8.jsx)("div", { className: "flex flex-col gap-y-0.5", children: items.map((item, index) => {
      return (0, import_jsx_runtime8.jsx)(
        OrderActivityItem,
        {
          title: item.title,
          timestamp: item.timestamp,
          isFirst: index === items.length - 1,
          itemsToSend: item.itemsToSend,
          itemsToReturn: item.itemsToReturn,
          itemsMap: item.itemsMap,
          children: item.children
        },
        index
      );
    }) });
  }
  const lastItems = items.slice(0, 2);
  const collapsibleItems = items.slice(2, items.length - 1);
  const firstItem = items[items.length - 1];
  return (0, import_jsx_runtime8.jsxs)("div", { className: "flex flex-col gap-y-0.5", children: [
    lastItems.map((item, index) => {
      return (0, import_jsx_runtime8.jsx)(
        OrderActivityItem,
        {
          title: item.title,
          timestamp: item.timestamp,
          itemsToSend: item.itemsToSend,
          itemsToReturn: item.itemsToReturn,
          itemsMap: item.itemsMap,
          children: item.children
        },
        index
      );
    }),
    (0, import_jsx_runtime8.jsx)(OrderActivityCollapsible, { activities: collapsibleItems }),
    (0, import_jsx_runtime8.jsx)(
      OrderActivityItem,
      {
        title: firstItem.title,
        timestamp: firstItem.timestamp,
        isFirst: true,
        itemsToSend: firstItem.itemsToSend,
        itemsToReturn: firstItem.itemsToReturn,
        itemsMap: firstItem.itemsMap,
        children: firstItem.children
      }
    )
  ] });
};
var useActivityItems = (order) => {
  const { t } = useTranslation();
  const { order_changes: orderChanges = [] } = useOrderChanges(order.id, {
    change_type: [
      "edit",
      "claim",
      "exchange",
      "return",
      "transfer",
      "update_order"
    ]
  });
  const rmaChanges = orderChanges.filter(
    (oc) => !NON_RMA_CHANGE_TYPES.includes(oc.change_type)
  );
  const missingLineItemIds = getMissingLineItemIds(order, rmaChanges);
  const { order_items: removedLineItems = [] } = useOrderLineItems(
    order.id,
    {
      fields: "+quantity",
      item_id: missingLineItemIds
    },
    {
      enabled: !!rmaChanges.length
    }
  );
  const itemsMap = (0, import_react2.useMemo)(() => {
    var _a;
    const _itemsMap = new Map((_a = order == null ? void 0 : order.items) == null ? void 0 : _a.map((i) => [i.id, i]));
    for (const id of missingLineItemIds) {
      const i = removedLineItems.find((i2) => i2.item.id === id);
      if (i) {
        _itemsMap.set(id, { ...i.item, quantity: i.quantity });
      }
    }
    return _itemsMap;
  }, [order.items, removedLineItems, missingLineItemIds]);
  const { returns = [] } = useReturns({
    order_id: order.id,
    fields: "+received_at,*items"
  });
  const { claims = [] } = useClaims({
    order_id: order.id,
    fields: "*additional_items"
  });
  const { exchanges = [] } = useExchanges({
    order_id: order.id,
    fields: "*additional_items"
  });
  const payments = getPaymentsFromOrder(order);
  const notes = [];
  const isLoading = false;
  return (0, import_react2.useMemo)(() => {
    var _a, _b;
    if (isLoading) {
      return [];
    }
    const items = [];
    for (const payment of payments) {
      const amount = payment.amount;
      items.push({
        title: t("orders.activity.events.payment.awaiting"),
        timestamp: payment.created_at,
        children: (0, import_jsx_runtime8.jsx)(Text, { size: "small", className: "text-ui-fg-subtle", children: getStylizedAmount(amount, payment.currency_code) })
      });
      if (payment.canceled_at) {
        items.push({
          title: t("orders.activity.events.payment.canceled"),
          timestamp: payment.canceled_at,
          children: (0, import_jsx_runtime8.jsx)(Text, { size: "small", className: "text-ui-fg-subtle", children: getStylizedAmount(amount, payment.currency_code) })
        });
      }
      if (payment.captured_at) {
        items.push({
          title: t("orders.activity.events.payment.captured"),
          timestamp: payment.captured_at,
          children: (0, import_jsx_runtime8.jsx)(Text, { size: "small", className: "text-ui-fg-subtle", children: getStylizedAmount(amount, payment.currency_code) })
        });
      }
      for (const refund of payment.refunds || []) {
        items.push({
          title: t("orders.activity.events.payment.refunded"),
          timestamp: refund.created_at,
          children: (0, import_jsx_runtime8.jsx)(Text, { size: "small", className: "text-ui-fg-subtle", children: getStylizedAmount(
            refund.amount,
            payment.currency_code
          ) })
        });
      }
    }
    for (const fulfillment of order.fulfillments || []) {
      items.push({
        title: t("orders.activity.events.fulfillment.created"),
        timestamp: fulfillment.created_at,
        children: (0, import_jsx_runtime8.jsx)(FulfillmentCreatedBody, { fulfillment })
      });
      if (fulfillment.delivered_at) {
        items.push({
          title: t("orders.activity.events.fulfillment.delivered"),
          timestamp: fulfillment.delivered_at,
          children: (0, import_jsx_runtime8.jsx)(FulfillmentCreatedBody, { fulfillment })
        });
      }
      if (fulfillment.shipped_at) {
        items.push({
          title: t("orders.activity.events.fulfillment.shipped"),
          timestamp: fulfillment.shipped_at,
          children: (0, import_jsx_runtime8.jsx)(FulfillmentCreatedBody, { fulfillment, isShipment: true })
        });
      }
      if (fulfillment.canceled_at) {
        items.push({
          title: t("orders.activity.events.fulfillment.canceled"),
          timestamp: fulfillment.canceled_at
        });
      }
    }
    const returnMap = /* @__PURE__ */ new Map();
    for (const ret of returns) {
      returnMap.set(ret.id, ret);
      if (ret.claim_id || ret.exchange_id) {
        continue;
      }
      items.push({
        title: t("orders.activity.events.return.created", {
          returnId: ret.id.slice(-7)
        }),
        timestamp: ret.created_at,
        itemsToReturn: ret == null ? void 0 : ret.items,
        itemsMap,
        children: (0, import_jsx_runtime8.jsx)(ReturnBody, { orderReturn: ret, isCreated: !ret.canceled_at })
      });
      if (ret.canceled_at) {
        items.push({
          title: t("orders.activity.events.return.canceled", {
            returnId: ret.id.slice(-7)
          }),
          timestamp: ret.canceled_at
        });
      }
      if (ret.status === "received" || ret.status === "partially_received") {
        items.push({
          title: t("orders.activity.events.return.received", {
            returnId: ret.id.slice(-7)
          }),
          timestamp: ret.received_at,
          itemsToReturn: ret == null ? void 0 : ret.items,
          itemsMap,
          children: (0, import_jsx_runtime8.jsx)(ReturnBody, { orderReturn: ret, isReceived: true })
        });
      }
    }
    for (const claim of claims) {
      const claimReturn = returnMap.get(claim.return_id);
      items.push({
        title: t(
          claim.canceled_at ? "orders.activity.events.claim.canceled" : "orders.activity.events.claim.created",
          {
            claimId: claim.id.slice(-7)
          }
        ),
        timestamp: claim.canceled_at || claim.created_at,
        itemsToSend: claim.additional_items,
        itemsToReturn: claimReturn == null ? void 0 : claimReturn.items,
        itemsMap,
        children: (0, import_jsx_runtime8.jsx)(ClaimBody, { claim, claimReturn })
      });
    }
    for (const exchange of exchanges) {
      const exchangeReturn = returnMap.get(exchange.return_id);
      items.push({
        title: t(
          exchange.canceled_at ? "orders.activity.events.exchange.canceled" : "orders.activity.events.exchange.created",
          {
            exchangeId: exchange.id.slice(-7)
          }
        ),
        timestamp: exchange.canceled_at || exchange.created_at,
        itemsToSend: exchange.additional_items,
        itemsToReturn: exchangeReturn == null ? void 0 : exchangeReturn.items,
        itemsMap,
        children: (0, import_jsx_runtime8.jsx)(ExchangeBody, { exchange, exchangeReturn })
      });
    }
    for (const edit of orderChanges.filter((oc) => oc.change_type === "edit")) {
      const isConfirmed = edit.status === "confirmed";
      const isPending = edit.status === "pending";
      if (isPending) {
        continue;
      }
      items.push({
        title: t(`orders.activity.events.edit.${edit.status}`, {
          editId: edit.id.slice(-7)
        }),
        timestamp: edit.status === "requested" ? edit.requested_at : edit.status === "confirmed" ? edit.confirmed_at : edit.status === "declined" ? edit.declined_at : edit.status === "canceled" ? edit.canceled_at : edit.created_at,
        children: isConfirmed ? (0, import_jsx_runtime8.jsx)(OrderEditBody, { edit }) : null
      });
    }
    for (const transfer of orderChanges.filter(
      (oc) => oc.change_type === "transfer"
    )) {
      if (transfer.requested_at) {
        items.push({
          title: t(`orders.activity.events.transfer.requested`, {
            transferId: transfer.id.slice(-7)
          }),
          timestamp: transfer.requested_at,
          children: (0, import_jsx_runtime8.jsx)(TransferOrderRequestBody, { transfer })
        });
      }
      if (transfer.confirmed_at) {
        items.push({
          title: t(`orders.activity.events.transfer.confirmed`, {
            transferId: transfer.id.slice(-7)
          }),
          timestamp: transfer.confirmed_at
        });
      }
      if (transfer.declined_at) {
        items.push({
          title: t(`orders.activity.events.transfer.declined`, {
            transferId: transfer.id.slice(-7)
          }),
          timestamp: transfer.declined_at
        });
      }
    }
    for (const update of orderChanges.filter(
      (oc) => oc.change_type === "update_order"
    )) {
      const updateType = (_b = (_a = update.actions[0]) == null ? void 0 : _a.details) == null ? void 0 : _b.type;
      if (updateType === "shipping_address") {
        items.push({
          title: (0, import_jsx_runtime8.jsx)(
            change_details_tooltip_default,
            {
              title: t(`orders.activity.events.update_order.shipping_address`),
              previous: getFormattedAddress({
                address: update.actions[0].details.old
              }).join(", "),
              next: getFormattedAddress({
                address: update.actions[0].details.new
              }).join(", ")
            }
          ),
          timestamp: update.created_at,
          children: (0, import_jsx_runtime8.jsxs)("div", { className: "text-ui-fg-subtle mt-2 flex gap-x-2 text-sm", children: [
            t("fields.by"),
            " ",
            (0, import_jsx_runtime8.jsx)(By, { id: update.created_by })
          ] })
        });
      }
      if (updateType === "billing_address") {
        items.push({
          title: (0, import_jsx_runtime8.jsx)(
            change_details_tooltip_default,
            {
              title: t(`orders.activity.events.update_order.billing_address`),
              previous: getFormattedAddress({
                address: update.actions[0].details.old
              }).join(", "),
              next: getFormattedAddress({
                address: update.actions[0].details.new
              }).join(", ")
            }
          ),
          timestamp: update.created_at,
          children: (0, import_jsx_runtime8.jsxs)("div", { className: "text-ui-fg-subtle mt-2 flex gap-x-2 text-sm", children: [
            t("fields.by"),
            " ",
            (0, import_jsx_runtime8.jsx)(By, { id: update.created_by })
          ] })
        });
      }
      if (updateType === "email") {
        items.push({
          title: (0, import_jsx_runtime8.jsx)(
            change_details_tooltip_default,
            {
              title: t(`orders.activity.events.update_order.email`),
              previous: update.actions[0].details.old,
              next: update.actions[0].details.new
            }
          ),
          timestamp: update.created_at,
          children: (0, import_jsx_runtime8.jsxs)("div", { className: "text-ui-fg-subtle mt-2 flex gap-x-2 text-sm", children: [
            t("fields.by"),
            " ",
            (0, import_jsx_runtime8.jsx)(By, { id: update.created_by })
          ] })
        });
      }
    }
    if (order.canceled_at) {
      items.push({
        title: t("orders.activity.events.canceled.title"),
        timestamp: order.canceled_at
      });
    }
    const sortedActivities = items.sort((a, b) => {
      return new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime();
    });
    const createdAt = {
      title: t("orders.activity.events.placed.title"),
      timestamp: order.created_at,
      children: (0, import_jsx_runtime8.jsx)(Text, { size: "small", className: "text-ui-fg-subtle", children: getStylizedAmount(order.total, order.currency_code) })
    };
    return [...sortedActivities, createdAt];
  }, [
    order,
    payments,
    returns,
    exchanges,
    orderChanges,
    notes,
    isLoading,
    itemsMap
  ]);
};
var OrderActivityItem = ({
  title,
  timestamp,
  isFirst = false,
  children,
  itemsToSend,
  itemsToReturn,
  itemsMap
}) => {
  const { getFullDate, getRelativeDate } = useDate();
  return (0, import_jsx_runtime8.jsxs)("div", { className: "grid grid-cols-[20px_1fr] items-start gap-2", children: [
    (0, import_jsx_runtime8.jsxs)("div", { className: "flex size-full flex-col items-center gap-y-0.5", children: [
      (0, import_jsx_runtime8.jsx)("div", { className: "flex size-5 items-center justify-center", children: (0, import_jsx_runtime8.jsx)("div", { className: "bg-ui-bg-base shadow-borders-base flex size-2.5 items-center justify-center rounded-full", children: (0, import_jsx_runtime8.jsx)("div", { className: "bg-ui-tag-neutral-icon size-1.5 rounded-full" }) }) }),
      !isFirst && (0, import_jsx_runtime8.jsx)("div", { className: "bg-ui-border-base w-px flex-1" })
    ] }),
    (0, import_jsx_runtime8.jsxs)(
      "div",
      {
        className: clx({
          "pb-4": !isFirst
        }),
        children: [
          (0, import_jsx_runtime8.jsxs)("div", { className: "flex items-center justify-between", children: [
            (itemsToSend == null ? void 0 : itemsToSend.length) || (itemsToReturn == null ? void 0 : itemsToReturn.length) ? (0, import_jsx_runtime8.jsx)(
              activity_items_default,
              {
                title,
                itemsToSend,
                itemsToReturn,
                itemsMap
              },
              title
            ) : (0, import_jsx_runtime8.jsx)(Text, { size: "small", leading: "compact", weight: "plus", children: title }),
            timestamp && (0, import_jsx_runtime8.jsx)(
              Tooltip,
              {
                content: getFullDate({ date: timestamp, includeTime: true }),
                children: (0, import_jsx_runtime8.jsx)(
                  Text,
                  {
                    size: "small",
                    leading: "compact",
                    className: "text-ui-fg-subtle text-right",
                    children: getRelativeDate(timestamp)
                  }
                )
              }
            )
          ] }),
          (0, import_jsx_runtime8.jsx)("div", { children })
        ]
      }
    )
  ] });
};
var OrderActivityCollapsible = ({
  activities
}) => {
  const [open, setOpen] = (0, import_react2.useState)(false);
  const { t } = useTranslation();
  if (!activities.length) {
    return null;
  }
  return (0, import_jsx_runtime8.jsxs)(dist_exports.Root, { open, onOpenChange: setOpen, children: [
    !open && (0, import_jsx_runtime8.jsxs)("div", { className: "grid grid-cols-[20px_1fr] items-start gap-2", children: [
      (0, import_jsx_runtime8.jsx)("div", { className: "flex size-full flex-col items-center", children: (0, import_jsx_runtime8.jsx)("div", { className: "border-ui-border-strong w-px flex-1 bg-[linear-gradient(var(--border-strong)_33%,rgba(255,255,255,0)_0%)] bg-[length:1px_3px] bg-right bg-repeat-y" }) }),
      (0, import_jsx_runtime8.jsx)("div", { className: "pb-4", children: (0, import_jsx_runtime8.jsx)(dist_exports.Trigger, { className: "text-left", children: (0, import_jsx_runtime8.jsx)(
        Text,
        {
          size: "small",
          leading: "compact",
          weight: "plus",
          className: "text-ui-fg-muted",
          children: t("orders.activity.showMoreActivities", {
            count: activities.length
          })
        }
      ) }) })
    ] }),
    (0, import_jsx_runtime8.jsx)(dist_exports.Content, { children: (0, import_jsx_runtime8.jsx)("div", { className: "flex flex-col gap-y-0.5", children: activities.map((item, index) => {
      return (0, import_jsx_runtime8.jsx)(
        OrderActivityItem,
        {
          title: item.title,
          timestamp: item.timestamp,
          itemsToSend: item.itemsToSend,
          itemsToReturn: item.itemsToReturn,
          itemsMap: item.itemsMap,
          children: item.children
        },
        index
      );
    }) }) })
  ] });
};
var FulfillmentCreatedBody = ({
  fulfillment
}) => {
  const { t } = useTranslation();
  const numberOfItems = fulfillment.items.reduce((acc, item) => {
    return acc + item.quantity;
  }, 0);
  return (0, import_jsx_runtime8.jsx)("div", { children: (0, import_jsx_runtime8.jsx)(Text, { size: "small", className: "text-ui-fg-subtle", children: t("orders.activity.events.fulfillment.items", {
    count: numberOfItems
  }) }) });
};
var ReturnBody = ({
  orderReturn,
  isCreated,
  isReceived
}) => {
  const prompt = usePrompt();
  const { t } = useTranslation();
  const { mutateAsync: cancelReturnRequest } = useCancelReturn(
    orderReturn.id,
    orderReturn.order_id
  );
  const onCancel = async () => {
    const res = await prompt({
      title: t("orders.returns.cancel.title"),
      description: t("orders.returns.cancel.description"),
      confirmText: t("actions.confirm"),
      cancelText: t("actions.cancel")
    });
    if (!res) {
      return;
    }
    await cancelReturnRequest();
  };
  const numberOfItems = orderReturn.items.reduce((acc, item) => {
    return acc + (isReceived ? item.received_quantity : item.quantity);
  }, 0);
  return (0, import_jsx_runtime8.jsxs)("div", { className: "flex items-start gap-1", children: [
    (0, import_jsx_runtime8.jsx)(Text, { size: "small", className: "text-ui-fg-subtle", children: t("orders.activity.events.return.items", {
      count: numberOfItems
    }) }),
    isCreated && (0, import_jsx_runtime8.jsxs)(import_jsx_runtime8.Fragment, { children: [
      (0, import_jsx_runtime8.jsx)("div", { className: "mt-[2px] flex items-center leading-none", children: "⋅" }),
      (0, import_jsx_runtime8.jsx)(
        Button,
        {
          onClick: onCancel,
          className: "text-ui-fg-subtle h-auto px-0 leading-none hover:bg-transparent",
          variant: "transparent",
          size: "small",
          children: t("actions.cancel")
        }
      )
    ] })
  ] });
};
var ClaimBody = ({
  claim,
  claimReturn
}) => {
  const prompt = usePrompt();
  const { t } = useTranslation();
  const isCanceled = !!claim.created_at;
  const { mutateAsync: cancelClaim } = useCancelClaim(claim.id, claim.order_id);
  const onCancel = async () => {
    const res = await prompt({
      title: t("orders.claims.cancel.title"),
      description: t("orders.claims.cancel.description"),
      confirmText: t("actions.confirm"),
      cancelText: t("actions.cancel")
    });
    if (!res) {
      return;
    }
    await cancelClaim();
  };
  const outboundItems = (claim.additional_items || []).reduce(
    (acc, item) => acc + item.quantity,
    0
  );
  const inboundItems = ((claimReturn == null ? void 0 : claimReturn.items) || []).reduce(
    (acc, item) => acc + item.quantity,
    0
  );
  return (0, import_jsx_runtime8.jsxs)("div", { children: [
    outboundItems > 0 && (0, import_jsx_runtime8.jsx)(Text, { size: "small", className: "text-ui-fg-subtle", children: t("orders.activity.events.claim.itemsInbound", {
      count: outboundItems
    }) }),
    inboundItems > 0 && (0, import_jsx_runtime8.jsx)(Text, { size: "small", className: "text-ui-fg-subtle", children: t("orders.activity.events.claim.itemsOutbound", {
      count: inboundItems
    }) }),
    !isCanceled && (0, import_jsx_runtime8.jsx)(
      Button,
      {
        onClick: onCancel,
        className: "text-ui-fg-subtle h-auto px-0 leading-none hover:bg-transparent",
        variant: "transparent",
        size: "small",
        children: t("actions.cancel")
      }
    )
  ] });
};
var ExchangeBody = ({
  exchange,
  exchangeReturn
}) => {
  const prompt = usePrompt();
  const { t } = useTranslation();
  const isCanceled = !!exchange.canceled_at;
  const { mutateAsync: cancelExchange } = useCancelExchange(
    exchange.id,
    exchange.order_id
  );
  const onCancel = async () => {
    const res = await prompt({
      title: t("orders.exchanges.cancel.title"),
      description: t("orders.exchanges.cancel.description"),
      confirmText: t("actions.confirm"),
      cancelText: t("actions.cancel")
    });
    if (!res) {
      return;
    }
    await cancelExchange();
  };
  const outboundItems = (exchange.additional_items || []).reduce(
    (acc, item) => acc + item.quantity,
    0
  );
  const inboundItems = ((exchangeReturn == null ? void 0 : exchangeReturn.items) || []).reduce(
    (acc, item) => acc + item.quantity,
    0
  );
  return (0, import_jsx_runtime8.jsxs)("div", { children: [
    outboundItems > 0 && (0, import_jsx_runtime8.jsx)(Text, { size: "small", className: "text-ui-fg-subtle", children: t("orders.activity.events.exchange.itemsInbound", {
      count: outboundItems
    }) }),
    inboundItems > 0 && (0, import_jsx_runtime8.jsx)(Text, { size: "small", className: "text-ui-fg-subtle", children: t("orders.activity.events.exchange.itemsOutbound", {
      count: inboundItems
    }) }),
    !isCanceled && (0, import_jsx_runtime8.jsx)(
      Button,
      {
        onClick: onCancel,
        className: "text-ui-fg-subtle h-auto px-0 leading-none hover:bg-transparent",
        variant: "transparent",
        size: "small",
        children: t("actions.cancel")
      }
    )
  ] });
};
var OrderEditBody = ({ edit }) => {
  const { t } = useTranslation();
  const [itemsAdded, itemsRemoved] = (0, import_react2.useMemo)(
    () => countItemsChange(edit.actions),
    [edit]
  );
  return (0, import_jsx_runtime8.jsxs)("div", { children: [
    itemsAdded > 0 && (0, import_jsx_runtime8.jsxs)(Text, { size: "small", className: "text-ui-fg-subtle", children: [
      t("labels.added"),
      ": ",
      itemsAdded
    ] }),
    itemsRemoved > 0 && (0, import_jsx_runtime8.jsxs)(Text, { size: "small", className: "text-ui-fg-subtle", children: [
      t("labels.removed"),
      ": ",
      itemsRemoved
    ] })
  ] });
};
var TransferOrderRequestBody = ({
  transfer
}) => {
  var _a;
  const prompt = usePrompt();
  const { t } = useTranslation();
  const action = transfer.actions[0];
  const { customer } = useCustomer(action.reference_id);
  const isCompleted = !!transfer.confirmed_at;
  const { mutateAsync: cancelTransfer } = useCancelOrderTransfer(
    transfer.order_id
  );
  const handleDelete = async () => {
    const res = await prompt({
      title: t("general.areYouSure"),
      description: t("actions.cannotUndo"),
      confirmText: t("actions.delete"),
      cancelText: t("actions.cancel")
    });
    if (!res) {
      return;
    }
    await cancelTransfer();
  };
  return (0, import_jsx_runtime8.jsxs)("div", { children: [
    (0, import_jsx_runtime8.jsxs)(Text, { size: "small", className: "text-ui-fg-subtle", children: [
      t("orders.activity.from"),
      ": ",
      (_a = action.details) == null ? void 0 : _a.original_email
    ] }),
    (0, import_jsx_runtime8.jsxs)(Text, { size: "small", className: "text-ui-fg-subtle", children: [
      t("orders.activity.to"),
      ":",
      " ",
      (customer == null ? void 0 : customer.first_name) ? `${customer == null ? void 0 : customer.first_name} ${customer == null ? void 0 : customer.last_name}` : customer == null ? void 0 : customer.email
    ] }),
    !isCompleted && (0, import_jsx_runtime8.jsx)(
      Button,
      {
        onClick: handleDelete,
        className: "text-ui-fg-subtle h-auto px-0 leading-none hover:bg-transparent",
        variant: "transparent",
        size: "small",
        children: t("actions.cancel")
      }
    )
  ] });
};
function countItemsChange(actions) {
  let added = 0;
  let removed = 0;
  actions.forEach((action) => {
    if (action.action === "ITEM_ADD") {
      added += action.details.quantity;
    }
    if (action.action === "ITEM_UPDATE") {
      const quantityDiff = action.details.quantity_diff;
      if (quantityDiff > 0) {
        added += quantityDiff;
      } else {
        removed += Math.abs(quantityDiff);
      }
    }
  });
  return [added, removed];
}
function getMissingLineItemIds(order, changes) {
  if (!(changes == null ? void 0 : changes.length)) {
    return [];
  }
  const retIds = /* @__PURE__ */ new Set();
  const existingItemsMap = new Map(order.items.map((item) => [item.id, true]));
  changes.forEach((change) => {
    change.actions.forEach((action) => {
      var _a;
      if (!((_a = action.details) == null ? void 0 : _a.reference_id)) {
        return;
      }
      if (action.details.reference_id.startsWith("ordli_") && !existingItemsMap.has(action.details.reference_id)) {
        retIds.add(action.details.reference_id);
      }
    });
  });
  return Array.from(retIds);
}
var OrderActivitySection = ({ order }) => {
  const { t } = useTranslation();
  return (0, import_jsx_runtime9.jsxs)(Container, { className: "flex flex-col gap-y-8 px-6 py-4", children: [
    (0, import_jsx_runtime9.jsx)("div", { className: "flex flex-col gap-y-4", children: (0, import_jsx_runtime9.jsx)("div", { className: "flex items-center justify-between", children: (0, import_jsx_runtime9.jsx)(Heading, { level: "h2", children: t("orders.activity.header") }) }) }),
    (0, import_jsx_runtime9.jsx)(OrderTimeline, { order })
  ] });
};
var ID = ({ data }) => {
  const { t } = useTranslation();
  const id = data.customer_id;
  const name = getOrderCustomer(data);
  const email = data.email;
  const fallback = (name || email || "").charAt(0).toUpperCase();
  return (0, import_jsx_runtime10.jsxs)("div", { className: "text-ui-fg-subtle grid grid-cols-2 items-center px-6 py-4", children: [
    (0, import_jsx_runtime10.jsx)(Text, { size: "small", leading: "compact", weight: "plus", children: t("fields.id") }),
    (0, import_jsx_runtime10.jsx)(
      Link,
      {
        to: `/customers/${id}`,
        className: "focus:shadow-borders-focus rounded-[4px] outline-none transition-shadow",
        children: (0, import_jsx_runtime10.jsxs)("div", { className: "flex items-center gap-x-2 overflow-hidden", children: [
          (0, import_jsx_runtime10.jsx)(Avatar, { size: "2xsmall", fallback }),
          (0, import_jsx_runtime10.jsx)(
            Text,
            {
              size: "small",
              leading: "compact",
              className: "text-ui-fg-subtle hover:text-ui-fg-base transition-fg truncate",
              children: name || email
            }
          )
        ] })
      }
    )
  ] });
};
var Company = ({ data }) => {
  var _a, _b;
  const { t } = useTranslation();
  const company = ((_a = data.shipping_address) == null ? void 0 : _a.company) || ((_b = data.billing_address) == null ? void 0 : _b.company);
  if (!company) {
    return null;
  }
  return (0, import_jsx_runtime10.jsxs)("div", { className: "text-ui-fg-subtle grid grid-cols-2 items-center px-6 py-4", children: [
    (0, import_jsx_runtime10.jsx)(Text, { size: "small", leading: "compact", weight: "plus", children: t("fields.company") }),
    (0, import_jsx_runtime10.jsx)(Text, { size: "small", leading: "compact", className: "truncate", children: company })
  ] });
};
var Contact = ({ data }) => {
  var _a, _b;
  const { t } = useTranslation();
  const phone = ((_a = data.shipping_address) == null ? void 0 : _a.phone) || ((_b = data.billing_address) == null ? void 0 : _b.phone);
  const email = data.email || "";
  return (0, import_jsx_runtime10.jsxs)("div", { className: "text-ui-fg-subtle grid grid-cols-2 items-start px-6 py-4", children: [
    (0, import_jsx_runtime10.jsx)(Text, { size: "small", leading: "compact", weight: "plus", children: t("orders.customer.contactLabel") }),
    (0, import_jsx_runtime10.jsxs)("div", { className: "flex flex-col gap-y-2", children: [
      (0, import_jsx_runtime10.jsxs)("div", { className: "grid grid-cols-[1fr_20px] items-start gap-x-2", children: [
        (0, import_jsx_runtime10.jsx)(
          Text,
          {
            size: "small",
            leading: "compact",
            className: "text-pretty break-all",
            children: email
          }
        ),
        (0, import_jsx_runtime10.jsx)("div", { className: "flex justify-end", children: (0, import_jsx_runtime10.jsx)(Copy, { content: email, className: "text-ui-fg-muted" }) })
      ] }),
      phone && (0, import_jsx_runtime10.jsxs)("div", { className: "grid grid-cols-[1fr_20px] items-start gap-x-2", children: [
        (0, import_jsx_runtime10.jsx)(
          Text,
          {
            size: "small",
            leading: "compact",
            className: "text-pretty break-all",
            children: phone
          }
        ),
        (0, import_jsx_runtime10.jsx)("div", { className: "flex justify-end", children: (0, import_jsx_runtime10.jsx)(Copy, { content: email, className: "text-ui-fg-muted" }) })
      ] })
    ] })
  ] });
};
var AddressPrint = ({
  address,
  type
}) => {
  const { t } = useTranslation();
  return (0, import_jsx_runtime10.jsxs)("div", { className: "text-ui-fg-subtle grid grid-cols-2 items-start px-6 py-4", children: [
    (0, import_jsx_runtime10.jsx)(Text, { size: "small", leading: "compact", weight: "plus", children: type === "shipping" ? t("addresses.shippingAddress.label") : t("addresses.billingAddress.label") }),
    address ? (0, import_jsx_runtime10.jsxs)("div", { className: "grid grid-cols-[1fr_20px] items-start gap-x-2", children: [
      (0, import_jsx_runtime10.jsx)(Text, { size: "small", leading: "compact", children: getFormattedAddress({ address }).map((line, i) => {
        return (0, import_jsx_runtime10.jsxs)("span", { className: "break-words", children: [
          line,
          (0, import_jsx_runtime10.jsx)("br", {})
        ] }, i);
      }) }),
      (0, import_jsx_runtime10.jsx)("div", { className: "flex justify-end", children: (0, import_jsx_runtime10.jsx)(
        Copy,
        {
          content: getFormattedAddress({ address }).join("\n"),
          className: "text-ui-fg-muted"
        }
      ) })
    ] }) : (0, import_jsx_runtime10.jsx)(Text, { size: "small", leading: "compact", children: "-" })
  ] });
};
var Addresses = ({ data }) => {
  const { t } = useTranslation();
  return (0, import_jsx_runtime10.jsxs)("div", { className: "divide-y", children: [
    (0, import_jsx_runtime10.jsx)(AddressPrint, { address: data.shipping_address, type: "shipping" }),
    !isSameAddress(data.shipping_address, data.billing_address) ? (0, import_jsx_runtime10.jsx)(AddressPrint, { address: data.billing_address, type: "billing" }) : (0, import_jsx_runtime10.jsxs)("div", { className: "grid grid-cols-2 items-center px-6 py-4", children: [
      (0, import_jsx_runtime10.jsx)(
        Text,
        {
          size: "small",
          leading: "compact",
          weight: "plus",
          className: "text-ui-fg-subtle",
          children: t("addresses.billingAddress.label")
        }
      ),
      (0, import_jsx_runtime10.jsx)(Text, { size: "small", leading: "compact", className: "text-ui-fg-muted", children: t("addresses.billingAddress.sameAsShipping") })
    ] })
  ] });
};
var CustomerInfo = Object.assign(
  {},
  {
    ID,
    Company,
    Contact,
    Addresses
  }
);
var getOrderCustomer = (obj) => {
  const { first_name: sFirstName, last_name: sLastName } = obj.shipping_address || {};
  const { first_name: bFirstName, last_name: bLastName } = obj.billing_address || {};
  const { first_name: cFirstName, last_name: cLastName } = obj.customer || {};
  const customerName = [cFirstName, cLastName].filter(Boolean).join(" ");
  const shippingName = [sFirstName, sLastName].filter(Boolean).join(" ");
  const billingName = [bFirstName, bLastName].filter(Boolean).join(" ");
  const name = customerName || shippingName || billingName;
  return name;
};
var OrderCustomerSection = ({ order }) => {
  return (0, import_jsx_runtime11.jsxs)(Container, { className: "divide-y p-0", children: [
    (0, import_jsx_runtime11.jsx)(Header, {}),
    (0, import_jsx_runtime11.jsx)(CustomerInfo.ID, { data: order }),
    (0, import_jsx_runtime11.jsx)(CustomerInfo.Contact, { data: order }),
    (0, import_jsx_runtime11.jsx)(CustomerInfo.Company, { data: order }),
    (0, import_jsx_runtime11.jsx)(CustomerInfo.Addresses, { data: order })
  ] });
};
var Header = () => {
  const { t } = useTranslation();
  return (0, import_jsx_runtime11.jsxs)("div", { className: "flex items-center justify-between px-6 py-4", children: [
    (0, import_jsx_runtime11.jsx)(Heading, { level: "h2", children: t("fields.customer") }),
    (0, import_jsx_runtime11.jsx)(
      ActionMenu,
      {
        groups: [
          {
            actions: [
              {
                label: t("transferOwnership.label"),
                to: `transfer`,
                icon: (0, import_jsx_runtime11.jsx)(ArrowPath, {})
              }
            ]
          },
          {
            actions: [
              {
                label: t("addresses.shippingAddress.editLabel"),
                to: "shipping-address",
                icon: (0, import_jsx_runtime11.jsx)(FlyingBox, {})
              },
              {
                label: t("addresses.billingAddress.editLabel"),
                to: "billing-address",
                icon: (0, import_jsx_runtime11.jsx)(CurrencyDollar, {})
              }
            ]
          },
          {
            actions: [
              {
                label: t("email.editLabel"),
                to: `email`,
                icon: (0, import_jsx_runtime11.jsx)(Envelope, {})
              }
            ]
          }
        ]
      }
    )
  ] });
};
var OrderFulfillmentSection = ({
  order
}) => {
  const fulfillments = order.fulfillments || [];
  return (0, import_jsx_runtime12.jsxs)("div", { className: "flex flex-col gap-y-3", children: [
    (0, import_jsx_runtime12.jsx)(UnfulfilledItemBreakdown, { order }),
    fulfillments.map((f, index) => (0, import_jsx_runtime12.jsx)(Fulfillment, { index, fulfillment: f, order }, f.id))
  ] });
};
var UnfulfilledItem = ({
  item,
  currencyCode
}) => {
  var _a;
  return (0, import_jsx_runtime12.jsxs)(
    "div",
    {
      className: "text-ui-fg-subtle grid grid-cols-2 items-start px-6 py-4",
      children: [
        (0, import_jsx_runtime12.jsxs)("div", { className: "flex items-start gap-x-4", children: [
          (0, import_jsx_runtime12.jsx)(Thumbnail, { src: item.thumbnail }),
          (0, import_jsx_runtime12.jsxs)("div", { children: [
            (0, import_jsx_runtime12.jsx)(
              Text,
              {
                size: "small",
                leading: "compact",
                weight: "plus",
                className: "text-ui-fg-base",
                children: item.title
              }
            ),
            item.variant_sku && (0, import_jsx_runtime12.jsxs)("div", { className: "flex items-center gap-x-1", children: [
              (0, import_jsx_runtime12.jsx)(Text, { size: "small", children: item.variant_sku }),
              (0, import_jsx_runtime12.jsx)(Copy, { content: item.variant_sku, className: "text-ui-fg-muted" })
            ] }),
            (0, import_jsx_runtime12.jsx)(Text, { size: "small", children: (_a = item.variant) == null ? void 0 : _a.options.map((o) => o.value).join(" · ") })
          ] })
        ] }),
        (0, import_jsx_runtime12.jsxs)("div", { className: "grid grid-cols-3 items-center gap-x-4", children: [
          (0, import_jsx_runtime12.jsx)("div", { className: "flex items-center justify-end", children: (0, import_jsx_runtime12.jsx)(Text, { size: "small", children: getLocaleAmount(item.unit_price, currencyCode) }) }),
          (0, import_jsx_runtime12.jsx)("div", { className: "flex items-center justify-end", children: (0, import_jsx_runtime12.jsxs)(Text, { children: [
            (0, import_jsx_runtime12.jsx)("span", { className: "tabular-nums", children: item.quantity - item.detail.fulfilled_quantity }),
            "x"
          ] }) }),
          (0, import_jsx_runtime12.jsx)("div", { className: "flex items-center justify-end", children: (0, import_jsx_runtime12.jsx)(Text, { size: "small", children: getLocaleAmount(item.subtotal || 0, currencyCode) }) })
        ] })
      ]
    },
    item.id
  );
};
var UnfulfilledItemBreakdown = ({ order }) => {
  const unfulfilledItemsWithShipping = order.items.filter(
    (i) => i.requires_shipping && i.detail.fulfilled_quantity < i.quantity
  );
  const unfulfilledItemsWithoutShipping = order.items.filter(
    (i) => !i.requires_shipping && i.detail.fulfilled_quantity < i.quantity
  );
  return (0, import_jsx_runtime12.jsxs)(import_jsx_runtime12.Fragment, { children: [
    !!unfulfilledItemsWithShipping.length && (0, import_jsx_runtime12.jsx)(
      UnfulfilledItemDisplay,
      {
        order,
        unfulfilledItems: unfulfilledItemsWithShipping,
        requiresShipping: true
      }
    ),
    !!unfulfilledItemsWithoutShipping.length && (0, import_jsx_runtime12.jsx)(
      UnfulfilledItemDisplay,
      {
        order,
        unfulfilledItems: unfulfilledItemsWithoutShipping,
        requiresShipping: false
      }
    )
  ] });
};
var UnfulfilledItemDisplay = ({
  order,
  unfulfilledItems,
  requiresShipping = false
}) => {
  const { t } = useTranslation();
  if (order.status === "canceled") {
    return;
  }
  return (0, import_jsx_runtime12.jsxs)(Container, { className: "divide-y p-0", children: [
    (0, import_jsx_runtime12.jsxs)("div", { className: "flex items-center justify-between px-6 py-4", children: [
      (0, import_jsx_runtime12.jsx)(Heading, { level: "h2", children: t("orders.fulfillment.unfulfilledItems") }),
      (0, import_jsx_runtime12.jsxs)("div", { className: "flex items-center gap-x-4", children: [
        requiresShipping && (0, import_jsx_runtime12.jsx)(StatusBadge, { color: "red", className: "text-nowrap", children: t("orders.fulfillment.requiresShipping") }),
        (0, import_jsx_runtime12.jsx)(StatusBadge, { color: "red", className: "text-nowrap", children: t("orders.fulfillment.awaitingFulfillmentBadge") }),
        (0, import_jsx_runtime12.jsx)(
          ActionMenu,
          {
            groups: [
              {
                actions: [
                  {
                    label: t("orders.fulfillment.fulfillItems"),
                    icon: (0, import_jsx_runtime12.jsx)(Buildings, {}),
                    to: `/orders/${order.id}/fulfillment?requires_shipping=${requiresShipping}`
                  }
                ]
              }
            ]
          }
        )
      ] })
    ] }),
    (0, import_jsx_runtime12.jsx)("div", { children: unfulfilledItems.map((item) => (0, import_jsx_runtime12.jsx)(
      UnfulfilledItem,
      {
        item,
        currencyCode: order.currency_code
      },
      item.id
    )) })
  ] });
};
var Fulfillment = ({
  fulfillment,
  order,
  index
}) => {
  var _a;
  const { t } = useTranslation();
  const prompt = usePrompt();
  const navigate = useNavigate();
  const showLocation = !!fulfillment.location_id;
  const isPickUpFulfillment = ((_a = fulfillment.shipping_option) == null ? void 0 : _a.service_zone.fulfillment_set.type) === "pickup";
  const { stock_location, isError, error } = useStockLocation(
    fulfillment.location_id,
    void 0,
    {
      enabled: showLocation
    }
  );
  let statusText = fulfillment.requires_shipping ? isPickUpFulfillment ? "Awaiting pickup" : "Awaiting shipping" : "Awaiting delivery";
  let statusColor = "blue";
  let statusTimestamp = fulfillment.created_at;
  if (fulfillment.canceled_at) {
    statusText = "Canceled";
    statusColor = "red";
    statusTimestamp = fulfillment.canceled_at;
  } else if (fulfillment.delivered_at) {
    statusText = "Delivered";
    statusColor = "green";
    statusTimestamp = fulfillment.delivered_at;
  } else if (fulfillment.shipped_at) {
    statusText = "Shipped";
    statusColor = "green";
    statusTimestamp = fulfillment.shipped_at;
  }
  const { mutateAsync } = useCancelOrderFulfillment(order.id, fulfillment.id);
  const { mutateAsync: markAsDelivered } = useMarkOrderFulfillmentAsDelivered(
    order.id,
    fulfillment.id
  );
  const showShippingButton = !fulfillment.canceled_at && !fulfillment.shipped_at && !fulfillment.delivered_at && fulfillment.requires_shipping && !isPickUpFulfillment;
  const showDeliveryButton = !fulfillment.canceled_at && !fulfillment.delivered_at;
  const handleMarkAsDelivered = async () => {
    const res = await prompt({
      title: t("general.areYouSure"),
      description: t("orders.fulfillment.markAsDeliveredWarning"),
      confirmText: t("actions.continue"),
      cancelText: t("actions.cancel"),
      variant: "confirmation"
    });
    if (res) {
      await markAsDelivered(void 0, {
        onSuccess: () => {
          toast.success(
            t(
              isPickUpFulfillment ? "orders.fulfillment.toast.fulfillmentPickedUp" : "orders.fulfillment.toast.fulfillmentDelivered"
            )
          );
        },
        onError: (e) => {
          toast.error(e.message);
        }
      });
    }
  };
  const handleCancel = async () => {
    if (fulfillment.shipped_at) {
      toast.warning(t("orders.fulfillment.toast.fulfillmentShipped"));
      return;
    }
    const res = await prompt({
      title: t("general.areYouSure"),
      description: t("orders.fulfillment.cancelWarning"),
      confirmText: t("actions.continue"),
      cancelText: t("actions.cancel")
    });
    if (res) {
      await mutateAsync(void 0, {
        onSuccess: () => {
          toast.success(t("orders.fulfillment.toast.canceled"));
        },
        onError: (e) => {
          toast.error(e.message);
        }
      });
    }
  };
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime12.jsxs)(Container, { className: "divide-y p-0", children: [
    (0, import_jsx_runtime12.jsxs)("div", { className: "flex items-center justify-between px-6 py-4", children: [
      (0, import_jsx_runtime12.jsx)(Heading, { level: "h2", children: t("orders.fulfillment.number", {
        number: index + 1
      }) }),
      (0, import_jsx_runtime12.jsxs)("div", { className: "flex items-center gap-x-4", children: [
        (0, import_jsx_runtime12.jsx)(
          Tooltip,
          {
            content: format(
              new Date(statusTimestamp),
              "dd MMM, yyyy, HH:mm:ss"
            ),
            children: (0, import_jsx_runtime12.jsx)(StatusBadge, { color: statusColor, className: "text-nowrap", children: statusText })
          }
        ),
        (0, import_jsx_runtime12.jsx)(
          ActionMenu,
          {
            groups: [
              {
                actions: [
                  {
                    label: t("actions.cancel"),
                    icon: (0, import_jsx_runtime12.jsx)(XCircle, {}),
                    onClick: handleCancel,
                    disabled: !!fulfillment.canceled_at || !!fulfillment.shipped_at || !!fulfillment.delivered_at
                  }
                ]
              }
            ]
          }
        )
      ] })
    ] }),
    (0, import_jsx_runtime12.jsxs)("div", { className: "text-ui-fg-subtle grid grid-cols-2 items-start px-6 py-4", children: [
      (0, import_jsx_runtime12.jsx)(Text, { size: "small", leading: "compact", weight: "plus", children: t("orders.fulfillment.itemsLabel") }),
      (0, import_jsx_runtime12.jsx)("ul", { children: fulfillment.items.map((f_item) => (0, import_jsx_runtime12.jsx)("li", { children: (0, import_jsx_runtime12.jsxs)(Text, { size: "small", leading: "compact", children: [
        f_item.quantity,
        "x ",
        f_item.title
      ] }) }, f_item.line_item_id)) })
    ] }),
    showLocation && (0, import_jsx_runtime12.jsxs)("div", { className: "text-ui-fg-subtle grid grid-cols-2 items-center px-6 py-4", children: [
      (0, import_jsx_runtime12.jsx)(Text, { size: "small", leading: "compact", weight: "plus", children: t("orders.fulfillment.shippingFromLabel") }),
      stock_location ? (0, import_jsx_runtime12.jsx)(
        Link,
        {
          to: `/settings/locations/${stock_location.id}`,
          className: "text-ui-fg-interactive hover:text-ui-fg-interactive-hover transition-fg",
          children: (0, import_jsx_runtime12.jsx)(Text, { size: "small", leading: "compact", children: stock_location.name })
        }
      ) : (0, import_jsx_runtime12.jsx)(Skeleton, { className: "w-16" })
    ] }),
    (0, import_jsx_runtime12.jsxs)("div", { className: "text-ui-fg-subtle grid grid-cols-2 items-center px-6 py-4", children: [
      (0, import_jsx_runtime12.jsx)(Text, { size: "small", leading: "compact", weight: "plus", children: t("fields.provider") }),
      (0, import_jsx_runtime12.jsx)(Text, { size: "small", leading: "compact", children: formatProvider(fulfillment.provider_id) })
    ] }),
    (0, import_jsx_runtime12.jsxs)("div", { className: "text-ui-fg-subtle grid grid-cols-2 items-start px-6 py-4", children: [
      (0, import_jsx_runtime12.jsx)(Text, { size: "small", leading: "compact", weight: "plus", children: t("orders.fulfillment.trackingLabel") }),
      (0, import_jsx_runtime12.jsx)("div", { children: fulfillment.labels && fulfillment.labels.length > 0 ? (0, import_jsx_runtime12.jsx)("ul", { children: fulfillment.labels.map((tlink) => {
        const hasUrl = tlink.url && tlink.url.length > 0 && tlink.url !== "#";
        if (hasUrl) {
          return (0, import_jsx_runtime12.jsx)("li", { children: (0, import_jsx_runtime12.jsx)(
            "a",
            {
              href: tlink.url,
              target: "_blank",
              rel: "noopener noreferrer",
              className: "text-ui-fg-interactive hover:text-ui-fg-interactive-hover transition-fg",
              children: (0, import_jsx_runtime12.jsx)(Text, { size: "small", leading: "compact", children: tlink.tracking_number })
            }
          ) }, tlink.tracking_number);
        }
        return (0, import_jsx_runtime12.jsx)("li", { children: (0, import_jsx_runtime12.jsx)(Text, { size: "small", leading: "compact", children: tlink.tracking_number }) }, tlink.tracking_number);
      }) }) : (0, import_jsx_runtime12.jsx)(Text, { size: "small", leading: "compact", children: "-" }) })
    ] }),
    (showShippingButton || showDeliveryButton) && (0, import_jsx_runtime12.jsxs)("div", { className: "bg-ui-bg-subtle flex items-center justify-end gap-x-2 rounded-b-xl px-4 py-4", children: [
      showDeliveryButton && (0, import_jsx_runtime12.jsx)(Button, { onClick: handleMarkAsDelivered, variant: "secondary", children: t(
        isPickUpFulfillment ? "orders.fulfillment.markAsPickedUp" : "orders.fulfillment.markAsDelivered"
      ) }),
      showShippingButton && (0, import_jsx_runtime12.jsx)(
        Button,
        {
          onClick: () => navigate(`./${fulfillment.id}/create-shipment`),
          variant: "secondary",
          children: t("orders.fulfillment.markAsShipped")
        }
      )
    ] })
  ] });
};
var OrderGeneralSection = ({ order }) => {
  var _a;
  const { t } = useTranslation();
  const prompt = usePrompt();
  const { getFullDate } = useDate();
  const { mutateAsync: cancelOrder } = useCancelOrder(order.id);
  const handleCancel = async () => {
    const res = await prompt({
      title: t("general.areYouSure"),
      description: t("orders.cancelWarning", {
        id: `#${order.display_id}`
      }),
      confirmText: t("actions.continue"),
      cancelText: t("actions.cancel")
    });
    if (!res) {
      return;
    }
    await cancelOrder(void 0, {
      onSuccess: () => {
        toast.success(t("orders.orderCanceled"));
      },
      onError: (e) => {
        toast.error(e.message);
      }
    });
  };
  return (0, import_jsx_runtime13.jsxs)(Container, { className: "flex items-center justify-between px-6 py-4", children: [
    (0, import_jsx_runtime13.jsxs)("div", { children: [
      (0, import_jsx_runtime13.jsxs)("div", { className: "flex items-center gap-x-1", children: [
        (0, import_jsx_runtime13.jsxs)(Heading, { children: [
          "#",
          order.display_id
        ] }),
        (0, import_jsx_runtime13.jsx)(Copy, { content: `#${order.display_id}`, className: "text-ui-fg-muted" })
      ] }),
      (0, import_jsx_runtime13.jsx)(Text, { size: "small", className: "text-ui-fg-subtle", children: t("orders.onDateFromSalesChannel", {
        date: getFullDate({ date: order.created_at, includeTime: true }),
        salesChannel: (_a = order.sales_channel) == null ? void 0 : _a.name
      }) })
    ] }),
    (0, import_jsx_runtime13.jsxs)("div", { className: "flex items-center gap-x-4", children: [
      (0, import_jsx_runtime13.jsxs)("div", { className: "flex items-center gap-x-1.5", children: [
        (0, import_jsx_runtime13.jsx)(OrderBadge, { order }),
        (0, import_jsx_runtime13.jsx)(PaymentBadge, { order }),
        (0, import_jsx_runtime13.jsx)(FulfillmentBadge, { order })
      ] }),
      (0, import_jsx_runtime13.jsx)(
        ActionMenu,
        {
          groups: [
            {
              actions: [
                {
                  label: t("actions.cancel"),
                  onClick: handleCancel,
                  disabled: !!order.canceled_at,
                  icon: (0, import_jsx_runtime13.jsx)(XCircle, {})
                }
              ]
            }
          ]
        }
      )
    ] })
  ] });
};
var FulfillmentBadge = ({ order }) => {
  const { t } = useTranslation();
  const { label, color } = getOrderFulfillmentStatus(
    t,
    order.fulfillment_status
  );
  return (0, import_jsx_runtime13.jsx)(StatusBadge, { color, className: "text-nowrap", children: label });
};
var PaymentBadge = ({ order }) => {
  const { t } = useTranslation();
  const { label, color } = getOrderPaymentStatus(t, order.payment_status);
  return (0, import_jsx_runtime13.jsx)(StatusBadge, { color, className: "text-nowrap", children: label });
};
var OrderBadge = ({ order }) => {
  const { t } = useTranslation();
  const orderStatus = getCanceledOrderStatus(t, order.status);
  if (!orderStatus) {
    return null;
  }
  return (0, import_jsx_runtime13.jsx)(StatusBadge, { color: orderStatus.color, className: "text-nowrap", children: orderStatus.label });
};
var MEDUSA_STOREFRONT_URL = __STOREFRONT_URL__ ?? "http://localhost:8000";
var CopyPaymentLink = import_react6.default.forwardRef(
  ({ paymentCollection, order }, ref) => {
    const [done, setDone] = (0, import_react6.useState)(false);
    const [open, setOpen] = (0, import_react6.useState)(false);
    const [text, setText] = (0, import_react6.useState)("CopyPaymentLink");
    const { t } = useTranslation();
    const copyToClipboard = async (e) => {
      e.stopPropagation();
      setDone(true);
      (0, import_copy_to_clipboard.default)(
        `${MEDUSA_STOREFRONT_URL}/payment-collection/${paymentCollection.id}`
      );
      setTimeout(() => {
        setDone(false);
      }, 2e3);
    };
    import_react6.default.useEffect(() => {
      if (done) {
        setText(t("actions.copied"));
        return;
      }
      setTimeout(() => {
        setText(t("actions.copy"));
      }, 500);
    }, [done]);
    return (0, import_jsx_runtime14.jsx)(Tooltip, { content: text, open: done || open, onOpenChange: setOpen, children: (0, import_jsx_runtime14.jsxs)(
      Button,
      {
        ref,
        variant: "secondary",
        size: "small",
        "aria-label": "CopyPaymentLink code snippet",
        onClick: copyToClipboard,
        children: [
          done ? (0, import_jsx_runtime14.jsx)(CheckCircleSolid, { className: "inline" }) : (0, import_jsx_runtime14.jsx)(SquareTwoStack, { className: "inline" }),
          t("orders.payment.paymentLink", {
            amount: getStylizedAmount(
              paymentCollection.amount,
              order == null ? void 0 : order.currency_code
            )
          })
        ]
      }
    ) });
  }
);
CopyPaymentLink.displayName = "CopyPaymentLink";
function ReturnInfoPopover({ orderReturn }) {
  const { t } = useTranslation();
  const [open, setOpen] = (0, import_react7.useState)(false);
  const { getFullDate } = useDate();
  const handleMouseEnter = () => {
    setOpen(true);
  };
  const handleMouseLeave = () => {
    setOpen(false);
  };
  let returnType = "Return";
  let returnTypeId = orderReturn.id;
  if (orderReturn.claim_id) {
    returnType = "Claim";
    returnTypeId = orderReturn.claim_id;
  }
  if (orderReturn.exchange_id) {
    returnType = "Exchange";
    returnTypeId = orderReturn.exchange_id;
  }
  if (typeof orderReturn !== "object") {
    return;
  }
  return (0, import_jsx_runtime15.jsxs)(Popover, { open, children: [
    (0, import_jsx_runtime15.jsx)(
      Popover.Trigger,
      {
        onMouseEnter: handleMouseEnter,
        onMouseLeave: handleMouseLeave,
        autoFocus: false,
        className: "align-sub focus-visible:outline-none",
        children: (0, import_jsx_runtime15.jsx)(InformationCircleSolid, {})
      }
    ),
    (0, import_jsx_runtime15.jsx)(
      Popover.Content,
      {
        align: "center",
        side: "top",
        className: "bg-ui-bg-component p-2 focus-visible:outline-none",
        children: (0, import_jsx_runtime15.jsxs)("div", { className: "", children: [
          (0, import_jsx_runtime15.jsxs)(Badge, { size: "2xsmall", className: "mb-2", rounded: "full", children: [
            returnType,
            ": #",
            returnTypeId.slice(-7)
          ] }),
          (0, import_jsx_runtime15.jsxs)(Text, { size: "xsmall", children: [
            (0, import_jsx_runtime15.jsx)("span", { className: "text-ui-fg-subtle", children: t(`orders.returns.returnRequested`) }),
            " · ",
            getFullDate({ date: orderReturn.requested_at, includeTime: true })
          ] }),
          (0, import_jsx_runtime15.jsxs)(Text, { size: "xsmall", children: [
            (0, import_jsx_runtime15.jsx)("span", { className: "text-ui-fg-subtle", children: t(`orders.returns.itemReceived`) }),
            " · ",
            orderReturn.received_at ? getFullDate({
              date: orderReturn.received_at,
              includeTime: true
            }) : "-"
          ] })
        ] })
      }
    )
  ] });
}
var return_info_popover_default = ReturnInfoPopover;
function ShippingInfoPopover({ shippingMethod }) {
  const { t } = useTranslation();
  const shippingDetail = shippingMethod == null ? void 0 : shippingMethod.detail;
  if (!shippingDetail) {
    return;
  }
  let rmaType = t("orders.return");
  let rmaId = shippingDetail.return_id;
  if (shippingDetail.claim_id) {
    rmaType = t("orders.claim");
    rmaId = shippingDetail.claim_id;
  }
  if (shippingDetail.exchange_id) {
    rmaType = t("orders.exchange");
    rmaId = shippingDetail.exchange_id;
  }
  if (!rmaId) {
    return;
  }
  return (0, import_jsx_runtime16.jsx)(
    Tooltip,
    {
      content: (0, import_jsx_runtime16.jsxs)(Badge, { size: "2xsmall", rounded: "full", children: [
        rmaType,
        ": #",
        rmaId.slice(-7)
      ] }),
      children: (0, import_jsx_runtime16.jsx)(InformationCircleSolid, { className: "inline-block text-ui-fg-muted ml-1" })
    }
  );
}
var shipping_info_popover_default = ShippingInfoPopover;
var OrderSummarySection = ({ order }) => {
  var _a, _b;
  const { t } = useTranslation();
  const prompt = usePrompt();
  const { reservations } = useReservationItems(
    {
      line_item_id: (_a = order == null ? void 0 : order.items) == null ? void 0 : _a.map((i) => i.id)
    },
    { enabled: Array.isArray(order == null ? void 0 : order.items) }
  );
  const { order: orderPreview } = useOrderPreview(order.id);
  const { returns = [] } = useReturns({
    status: "requested",
    order_id: order.id,
    fields: "+received_at"
  });
  const receivableReturns = (0, import_react5.useMemo)(
    () => returns.filter((r) => !r.canceled_at),
    [returns]
  );
  const showReturns = !!receivableReturns.length;
  const showAllocateButton = (0, import_react5.useMemo)(() => {
    var _a2;
    if (!reservations) {
      return false;
    }
    const reservationsMap = new Map(
      reservations.map((r) => [r.line_item_id, r.id])
    );
    for (const item of order.items) {
      if ((_a2 = item.variant) == null ? void 0 : _a2.manage_inventory) {
        if (item.quantity - item.detail.fulfilled_quantity > 0) {
          if (!reservationsMap.has(item.id)) {
            return true;
          }
        }
      }
    }
    return false;
  }, [order.items, reservations]);
  const unpaidPaymentCollection = order.payment_collections.find(
    (pc) => pc.status === "not_paid"
  );
  const { mutateAsync: markAsPaid } = useMarkPaymentCollectionAsPaid(
    order.id,
    unpaidPaymentCollection == null ? void 0 : unpaidPaymentCollection.id
  );
  const pendingDifference = ((_b = order.summary) == null ? void 0 : _b.pending_difference) || 0;
  const isAmountSignificant = !isAmountLessThenRoundingError(
    pendingDifference,
    order.currency_code
  );
  const showPayment = unpaidPaymentCollection && pendingDifference > 0 && isAmountSignificant;
  const showRefund = unpaidPaymentCollection && pendingDifference < 0 && isAmountSignificant;
  const handleMarkAsPaid = async (paymentCollection) => {
    const res = await prompt({
      title: t("orders.payment.markAsPaid"),
      description: t("orders.payment.markAsPaidPayment", {
        amount: formatCurrency(
          paymentCollection.amount,
          order.currency_code
        )
      }),
      confirmText: t("actions.confirm"),
      cancelText: t("actions.cancel"),
      variant: "confirmation"
    });
    if (!res) {
      return;
    }
    await markAsPaid(
      { order_id: order.id },
      {
        onSuccess: () => {
          toast.success(
            t("orders.payment.markAsPaidPaymentSuccess", {
              amount: formatCurrency(
                paymentCollection.amount,
                order.currency_code
              )
            })
          );
        },
        onError: (error) => {
          toast.error(error.message);
        }
      }
    );
  };
  return (0, import_jsx_runtime17.jsxs)(Container, { className: "divide-y divide-dashed p-0", children: [
    (0, import_jsx_runtime17.jsx)(Header2, { order, orderPreview }),
    (0, import_jsx_runtime17.jsx)(ItemBreakdown, { order, reservations }),
    (0, import_jsx_runtime17.jsx)(CostBreakdown, { order }),
    (0, import_jsx_runtime17.jsx)(Total, { order }),
    (showAllocateButton || showReturns || showPayment || showRefund) && (0, import_jsx_runtime17.jsxs)("div", { className: "bg-ui-bg-subtle flex items-center justify-end gap-x-2 rounded-b-xl px-4 py-4", children: [
      showReturns && (receivableReturns.length === 1 ? (0, import_jsx_runtime17.jsx)(Button, { asChild: true, variant: "secondary", size: "small", children: (0, import_jsx_runtime17.jsx)(
        Link,
        {
          to: `/orders/${order.id}/returns/${receivableReturns[0].id}/receive`,
          children: t("orders.returns.receive.action")
        }
      ) }) : (0, import_jsx_runtime17.jsx)(
        ActionMenu,
        {
          groups: [
            {
              actions: receivableReturns.map((r) => {
                let id = r.id;
                let returnType = "Return";
                if (r.exchange_id) {
                  id = r.exchange_id;
                  returnType = "Exchange";
                }
                if (r.claim_id) {
                  id = r.claim_id;
                  returnType = "Claim";
                }
                return {
                  label: t("orders.returns.receive.receiveItems", {
                    id: `#${id.slice(-7)}`,
                    returnType
                  }),
                  icon: (0, import_jsx_runtime17.jsx)(ArrowLongRight, {}),
                  to: `/orders/${order.id}/returns/${r.id}/receive`
                };
              })
            }
          ],
          children: (0, import_jsx_runtime17.jsx)(Button, { variant: "secondary", size: "small", children: t("orders.returns.receive.action") })
        }
      )),
      showAllocateButton && (0, import_jsx_runtime17.jsx)(Button, { asChild: true, variant: "secondary", size: "small", children: (0, import_jsx_runtime17.jsx)(Link, { to: "allocate-items", children: t("orders.allocateItems.action") }) }),
      showPayment && (0, import_jsx_runtime17.jsx)(
        CopyPaymentLink,
        {
          paymentCollection: unpaidPaymentCollection,
          order
        }
      ),
      showPayment && (0, import_jsx_runtime17.jsx)(
        Button,
        {
          size: "small",
          variant: "secondary",
          onClick: () => handleMarkAsPaid(unpaidPaymentCollection),
          children: t("orders.payment.markAsPaid")
        }
      ),
      showRefund && (0, import_jsx_runtime17.jsx)(Button, { size: "small", variant: "secondary", asChild: true, children: (0, import_jsx_runtime17.jsx)(Link, { to: `/orders/${order.id}/refund`, children: t("orders.payment.refundAmount", {
        amount: getStylizedAmount(
          pendingDifference * -1,
          order == null ? void 0 : order.currency_code
        )
      }) }) })
    ] })
  ] });
};
var Header2 = ({
  order,
  orderPreview
}) => {
  var _a, _b, _c, _d, _e, _f, _g, _h, _i, _j, _k, _l, _m, _n, _o, _p, _q, _r;
  const { t } = useTranslation();
  const shouldDisableReturn = order.items.every(
    (i) => !(getReturnableQuantity(i) > 0)
  );
  const isOrderEditActive = ((_a = orderPreview == null ? void 0 : orderPreview.order_change) == null ? void 0 : _a.change_type) === "edit";
  const isOrderEditPending = ((_b = orderPreview == null ? void 0 : orderPreview.order_change) == null ? void 0 : _b.change_type) === "edit" && ((_c = orderPreview == null ? void 0 : orderPreview.order_change) == null ? void 0 : _c.status) === "pending";
  return (0, import_jsx_runtime17.jsxs)("div", { className: "flex items-center justify-between px-6 py-4", children: [
    (0, import_jsx_runtime17.jsx)(Heading, { level: "h2", children: t("fields.summary") }),
    (0, import_jsx_runtime17.jsx)(
      ActionMenu,
      {
        groups: [
          {
            actions: [
              {
                label: t(
                  isOrderEditPending ? "orders.summary.editOrderContinue" : "orders.summary.editOrder"
                ),
                to: `/orders/${order.id}/edits`,
                icon: (0, import_jsx_runtime17.jsx)(PencilSquare, {}),
                disabled: order.status === "canceled" || (orderPreview == null ? void 0 : orderPreview.order_change) && ((_d = orderPreview == null ? void 0 : orderPreview.order_change) == null ? void 0 : _d.change_type) !== "edit" || ((_e = orderPreview == null ? void 0 : orderPreview.order_change) == null ? void 0 : _e.change_type) === "edit" && ((_f = orderPreview == null ? void 0 : orderPreview.order_change) == null ? void 0 : _f.status) === "requested"
              }
            ]
          },
          {
            actions: [
              {
                label: t("orders.returns.create"),
                to: `/orders/${order.id}/returns`,
                icon: (0, import_jsx_runtime17.jsx)(ArrowUturnLeft, {}),
                disabled: shouldDisableReturn || isOrderEditActive || !!((_g = orderPreview == null ? void 0 : orderPreview.order_change) == null ? void 0 : _g.exchange_id) || !!((_h = orderPreview == null ? void 0 : orderPreview.order_change) == null ? void 0 : _h.claim_id)
              },
              {
                label: ((_i = orderPreview == null ? void 0 : orderPreview.order_change) == null ? void 0 : _i.id) && ((_j = orderPreview == null ? void 0 : orderPreview.order_change) == null ? void 0 : _j.exchange_id) ? t("orders.exchanges.manage") : t("orders.exchanges.create"),
                to: `/orders/${order.id}/exchanges`,
                icon: (0, import_jsx_runtime17.jsx)(ArrowPath, {}),
                disabled: shouldDisableReturn || isOrderEditActive || !!((_k = orderPreview == null ? void 0 : orderPreview.order_change) == null ? void 0 : _k.return_id) && !((_l = orderPreview == null ? void 0 : orderPreview.order_change) == null ? void 0 : _l.exchange_id) || !!((_m = orderPreview == null ? void 0 : orderPreview.order_change) == null ? void 0 : _m.claim_id)
              },
              {
                label: ((_n = orderPreview == null ? void 0 : orderPreview.order_change) == null ? void 0 : _n.id) && ((_o = orderPreview == null ? void 0 : orderPreview.order_change) == null ? void 0 : _o.claim_id) ? t("orders.claims.manage") : t("orders.claims.create"),
                to: `/orders/${order.id}/claims`,
                icon: (0, import_jsx_runtime17.jsx)(ExclamationCircle, {}),
                disabled: shouldDisableReturn || isOrderEditActive || !!((_p = orderPreview == null ? void 0 : orderPreview.order_change) == null ? void 0 : _p.return_id) && !((_q = orderPreview == null ? void 0 : orderPreview.order_change) == null ? void 0 : _q.claim_id) || !!((_r = orderPreview == null ? void 0 : orderPreview.order_change) == null ? void 0 : _r.exchange_id)
              }
            ]
          }
        ]
      }
    )
  ] });
};
var Item = ({
  item,
  currencyCode,
  reservation,
  returns,
  claims,
  exchanges
}) => {
  var _a, _b, _c, _d, _e;
  const { t } = useTranslation();
  const isInventoryManaged = (_a = item.variant) == null ? void 0 : _a.manage_inventory;
  const hasInventoryKit = isInventoryManaged && (((_c = (_b = item.variant) == null ? void 0 : _b.inventory_items) == null ? void 0 : _c.length) || 0) > 1;
  const hasUnfulfilledItems = item.quantity - item.detail.fulfilled_quantity > 0;
  return (0, import_jsx_runtime17.jsxs)(import_jsx_runtime17.Fragment, { children: [
    (0, import_jsx_runtime17.jsxs)(
      "div",
      {
        className: "text-ui-fg-subtle grid grid-cols-2 items-center gap-x-4 px-6 py-4",
        children: [
          (0, import_jsx_runtime17.jsxs)("div", { className: "flex items-start gap-x-4", children: [
            (0, import_jsx_runtime17.jsx)(Thumbnail, { src: item.thumbnail }),
            (0, import_jsx_runtime17.jsxs)("div", { children: [
              (0, import_jsx_runtime17.jsx)(
                Text,
                {
                  size: "small",
                  leading: "compact",
                  weight: "plus",
                  className: "text-ui-fg-base",
                  children: item.title
                }
              ),
              item.variant_sku && (0, import_jsx_runtime17.jsxs)("div", { className: "flex items-center gap-x-1", children: [
                (0, import_jsx_runtime17.jsx)(Text, { size: "small", children: item.variant_sku }),
                (0, import_jsx_runtime17.jsx)(Copy, { content: item.variant_sku, className: "text-ui-fg-muted" })
              ] }),
              (0, import_jsx_runtime17.jsx)(Text, { size: "small", children: (_e = (_d = item.variant) == null ? void 0 : _d.options) == null ? void 0 : _e.map((o) => o.value).join(" · ") })
            ] })
          ] }),
          (0, import_jsx_runtime17.jsxs)("div", { className: "grid grid-cols-3 items-center gap-x-4", children: [
            (0, import_jsx_runtime17.jsx)("div", { className: "flex items-center justify-end gap-x-4", children: (0, import_jsx_runtime17.jsx)(Text, { size: "small", children: getLocaleAmount(item.unit_price, currencyCode) }) }),
            (0, import_jsx_runtime17.jsxs)("div", { className: "flex items-center gap-x-2", children: [
              (0, import_jsx_runtime17.jsx)("div", { className: "w-fit min-w-[27px]", children: (0, import_jsx_runtime17.jsxs)(Text, { size: "small", children: [
                (0, import_jsx_runtime17.jsx)("span", { className: "tabular-nums", children: item.quantity }),
                "x"
              ] }) }),
              (0, import_jsx_runtime17.jsx)("div", { className: "overflow-visible", children: isInventoryManaged && hasUnfulfilledItems && (0, import_jsx_runtime17.jsx)(
                StatusBadge,
                {
                  color: reservation ? "green" : "orange",
                  className: "text-nowrap",
                  children: reservation ? t("orders.reservations.allocatedLabel") : t("orders.reservations.notAllocatedLabel")
                }
              ) })
            ] }),
            (0, import_jsx_runtime17.jsx)("div", { className: "flex items-center justify-end", children: (0, import_jsx_runtime17.jsx)(Text, { size: "small", className: "pt-[1px]", children: getLocaleAmount(item.subtotal || 0, currencyCode) }) })
          ] })
        ]
      },
      item.id
    ),
    hasInventoryKit && (0, import_jsx_runtime17.jsx)(InventoryKitBreakdown, { item }),
    returns.map((r) => (0, import_jsx_runtime17.jsx)(ReturnBreakdown, { orderReturn: r, itemId: item.id }, r.id)),
    claims.map((claim) => (0, import_jsx_runtime17.jsx)(ClaimBreakdown, { claim, itemId: item.id }, claim.id)),
    exchanges.map((exchange) => (0, import_jsx_runtime17.jsx)(
      ExchangeBreakdown,
      {
        exchange,
        itemId: item.id
      },
      exchange.id
    ))
  ] });
};
var ItemBreakdown = ({
  order,
  reservations
}) => {
  var _a;
  const { claims = [] } = useClaims({
    order_id: order.id,
    fields: "*additional_items"
  });
  const { exchanges = [] } = useExchanges({
    order_id: order.id,
    fields: "*additional_items"
  });
  const { returns = [] } = useReturns({
    order_id: order.id,
    fields: "*items,*items.reason"
  });
  const reservationsMap = (0, import_react5.useMemo)(
    () => new Map((reservations || []).map((r) => [r.line_item_id, r])),
    [reservations]
  );
  return (0, import_jsx_runtime17.jsx)("div", { children: (_a = order.items) == null ? void 0 : _a.map((item) => {
    const reservation = reservationsMap.get(item.id);
    return (0, import_jsx_runtime17.jsx)(
      Item,
      {
        item,
        currencyCode: order.currency_code,
        reservation,
        returns,
        exchanges,
        claims
      },
      item.id
    );
  }) });
};
var Cost = ({
  label,
  value,
  secondaryValue,
  tooltip
}) => (0, import_jsx_runtime17.jsxs)("div", { className: "grid grid-cols-3 items-center", children: [
  (0, import_jsx_runtime17.jsxs)(Text, { size: "small", leading: "compact", children: [
    label,
    " ",
    tooltip
  ] }),
  (0, import_jsx_runtime17.jsx)("div", { className: "text-right", children: (0, import_jsx_runtime17.jsx)(Text, { size: "small", leading: "compact", children: secondaryValue }) }),
  (0, import_jsx_runtime17.jsx)("div", { className: "text-right", children: (0, import_jsx_runtime17.jsx)(Text, { size: "small", leading: "compact", children: value }) })
] });
var CostBreakdown = ({
  order
}) => {
  var _a;
  const { t } = useTranslation();
  const [isTaxOpen, setIsTaxOpen] = (0, import_react5.useState)(false);
  const [isShippingOpen, setIsShippingOpen] = (0, import_react5.useState)(false);
  const discountCodes = (0, import_react5.useMemo)(() => {
    const codes = /* @__PURE__ */ new Set();
    order.items.forEach(
      (item) => {
        var _a2;
        return (_a2 = item.adjustments) == null ? void 0 : _a2.forEach((adj) => {
          codes.add(adj.code);
        });
      }
    );
    return Array.from(codes).sort();
  }, [order]);
  const taxCodes = (0, import_react5.useMemo)(() => {
    const taxCodeMap = {};
    order.items.forEach((item) => {
      var _a2;
      (_a2 = item.tax_lines) == null ? void 0 : _a2.forEach((line) => {
        taxCodeMap[line.code] = (taxCodeMap[line.code] || 0) + line.total;
      });
    });
    order.shipping_methods.forEach((sm) => {
      var _a2;
      (_a2 = sm.tax_lines) == null ? void 0 : _a2.forEach((line) => {
        taxCodeMap[line.code] = (taxCodeMap[line.code] || 0) + line.total;
      });
    });
    return taxCodeMap;
  }, [order]);
  const automaticTaxesOn = !!((_a = order.region) == null ? void 0 : _a.automatic_taxes);
  const hasTaxLines = !!Object.keys(taxCodes).length;
  const discountTotal = automaticTaxesOn ? order.discount_total : order.discount_subtotal;
  return (0, import_jsx_runtime17.jsxs)("div", { className: "text-ui-fg-subtle flex flex-col gap-y-2 px-6 py-4", children: [
    (0, import_jsx_runtime17.jsx)(
      Cost,
      {
        label: t(
          automaticTaxesOn ? "orders.summary.itemTotal" : "orders.summary.itemSubtotal"
        ),
        value: getLocaleAmount(order.item_total, order.currency_code)
      }
    ),
    (0, import_jsx_runtime17.jsx)(
      Cost,
      {
        label: (0, import_jsx_runtime17.jsxs)(
          "div",
          {
            onClick: () => setIsShippingOpen((o) => !o),
            className: "flex cursor-pointer items-center gap-1",
            children: [
              (0, import_jsx_runtime17.jsx)("span", { children: t(
                automaticTaxesOn ? "orders.summary.shippingTotal" : "orders.summary.shippingSubtotal"
              ) }),
              (0, import_jsx_runtime17.jsx)(
                TriangleDownMini,
                {
                  style: {
                    transform: `rotate(${isShippingOpen ? 0 : -90}deg)`
                  }
                }
              )
            ]
          }
        ),
        value: getLocaleAmount(
          automaticTaxesOn ? order.shipping_total : order.shipping_subtotal,
          order.currency_code
        )
      }
    ),
    isShippingOpen && (0, import_jsx_runtime17.jsx)("div", { className: "flex flex-col gap-1 pl-5", children: (order.shipping_methods || []).sort(
      (m1, m2) => m1.created_at.localeCompare(m2.created_at)
    ).map((sm, i) => {
      return (0, import_jsx_runtime17.jsxs)(
        "div",
        {
          className: "flex items-center justify-between gap-x-2",
          children: [
            (0, import_jsx_runtime17.jsx)("div", { children: (0, import_jsx_runtime17.jsxs)("span", { className: "txt-small text-ui-fg-subtle font-medium", children: [
              sm.name,
              sm.detail.return_id && ` (${t("fields.returnShipping")})`,
              " ",
              (0, import_jsx_runtime17.jsx)(shipping_info_popover_default, { shippingMethod: sm }, i)
            ] }) }),
            (0, import_jsx_runtime17.jsx)("div", { className: "relative flex-1", children: (0, import_jsx_runtime17.jsx)("div", { className: "bottom-[calc(50% - 2px)] absolute h-[1px] w-full border-b border-dashed" }) }),
            (0, import_jsx_runtime17.jsx)("span", { className: "txt-small text-ui-fg-muted", children: getLocaleAmount(
              automaticTaxesOn ? sm.total : sm.subtotal,
              order.currency_code
            ) })
          ]
        },
        sm.id
      );
    }) }),
    (0, import_jsx_runtime17.jsx)(
      Cost,
      {
        label: t(
          automaticTaxesOn ? "orders.summary.discountTotal" : "orders.summary.discountSubtotal"
        ),
        secondaryValue: discountCodes.join(", "),
        value: discountTotal > 0 ? `- ${getLocaleAmount(discountTotal, order.currency_code)}` : "-"
      }
    ),
    (0, import_jsx_runtime17.jsxs)(import_jsx_runtime17.Fragment, { children: [
      (0, import_jsx_runtime17.jsxs)("div", { className: "flex justify-between", children: [
        (0, import_jsx_runtime17.jsxs)(
          "div",
          {
            onClick: () => hasTaxLines && setIsTaxOpen((o) => !o),
            className: clx("flex items-center gap-1", {
              "cursor-pointer": hasTaxLines
            }),
            children: [
              (0, import_jsx_runtime17.jsx)("span", { className: "txt-small select-none", children: t(
                automaticTaxesOn ? "orders.summary.taxTotalIncl" : "orders.summary.taxTotal"
              ) }),
              hasTaxLines && (0, import_jsx_runtime17.jsx)(
                TriangleDownMini,
                {
                  style: {
                    transform: `rotate(${isTaxOpen ? 0 : -90}deg)`
                  }
                }
              )
            ]
          }
        ),
        (0, import_jsx_runtime17.jsx)("div", { className: "text-right", children: (0, import_jsx_runtime17.jsx)(Text, { size: "small", leading: "compact", children: getLocaleAmount(order.tax_total, order.currency_code) }) })
      ] }),
      isTaxOpen && (0, import_jsx_runtime17.jsx)("div", { className: "flex flex-col gap-1 pl-5", children: Object.entries(taxCodes).map(([code, total]) => {
        return (0, import_jsx_runtime17.jsxs)(
          "div",
          {
            className: "flex items-center justify-between gap-x-2",
            children: [
              (0, import_jsx_runtime17.jsx)("div", { children: (0, import_jsx_runtime17.jsx)("span", { className: "txt-small text-ui-fg-subtle font-medium", children: code }) }),
              (0, import_jsx_runtime17.jsx)("div", { className: "relative flex-1", children: (0, import_jsx_runtime17.jsx)("div", { className: "bottom-[calc(50% - 2px)] absolute h-[1px] w-full border-b border-dashed" }) }),
              (0, import_jsx_runtime17.jsx)("span", { className: "txt-small text-ui-fg-muted", children: getLocaleAmount(total, order.currency_code) })
            ]
          },
          code
        );
      }) })
    ] })
  ] });
};
var InventoryKitBreakdown = ({ item }) => {
  var _a;
  const { t } = useTranslation();
  const [isOpen, setIsOpen] = (0, import_react5.useState)(false);
  const inventory = ((_a = item.variant) == null ? void 0 : _a.inventory_items) || [];
  return (0, import_jsx_runtime17.jsxs)(import_jsx_runtime17.Fragment, { children: [
    (0, import_jsx_runtime17.jsxs)(
      "div",
      {
        onClick: () => setIsOpen((o) => !o),
        className: "flex cursor-pointer items-center gap-2 border-t border-dashed px-6 py-4",
        children: [
          (0, import_jsx_runtime17.jsx)(
            TriangleDownMini,
            {
              style: {
                transform: `rotate(${isOpen ? 0 : -90}deg)`
              }
            }
          ),
          (0, import_jsx_runtime17.jsx)("span", { className: "text-ui-fg-muted txt-small select-none", children: t("orders.summary.inventoryKit", { count: inventory.length }) })
        ]
      }
    ),
    isOpen && (0, import_jsx_runtime17.jsx)("div", { className: "flex flex-col gap-1 px-6 pb-4", children: inventory.map((i) => {
      return (0, import_jsx_runtime17.jsxs)(
        "div",
        {
          className: "flex items-center justify-between gap-x-2",
          children: [
            (0, import_jsx_runtime17.jsx)("div", { children: (0, import_jsx_runtime17.jsxs)("span", { className: "txt-small text-ui-fg-subtle font-medium", children: [
              i.inventory.title,
              i.inventory.sku && (0, import_jsx_runtime17.jsxs)("span", { className: "text-ui-fg-subtle font-normal", children: [
                " ",
                "⋅ ",
                i.inventory.sku
              ] })
            ] }) }),
            (0, import_jsx_runtime17.jsx)("div", { className: "relative flex-1", children: (0, import_jsx_runtime17.jsx)("div", { className: "bottom-[calc(50% - 2px)] absolute h-[1px] w-full border-b border-dashed" }) }),
            (0, import_jsx_runtime17.jsxs)("span", { className: "txt-small text-ui-fg-muted", children: [
              i.required_quantity,
              "x"
            ] })
          ]
        },
        i.inventory.id
      );
    }) })
  ] });
};
var ReturnBreakdownWithDamages = ({
  orderReturn,
  itemId
}) => {
  var _a, _b;
  const { t } = useTranslation();
  const item = (_a = orderReturn == null ? void 0 : orderReturn.items) == null ? void 0 : _a.find((ri) => ri.item_id === itemId);
  const damagedQuantity = (item == null ? void 0 : item.damaged_quantity) || 0;
  return item && (0, import_jsx_runtime17.jsxs)(
    "div",
    {
      className: "txt-compact-small-plus text-ui-fg-subtle bg-ui-bg-subtle flex flex-row justify-between gap-y-2 border-t-2 border-dotted px-6 py-4",
      children: [
        (0, import_jsx_runtime17.jsxs)("div", { className: "flex items-center gap-2", children: [
          (0, import_jsx_runtime17.jsx)(ArrowDownRightMini, { className: "text-ui-fg-muted" }),
          (0, import_jsx_runtime17.jsx)(Text, { size: "small", children: t(`orders.returns.damagedItemsReturned`, {
            quantity: damagedQuantity
          }) }),
          (item == null ? void 0 : item.note) && (0, import_jsx_runtime17.jsx)(Tooltip, { content: item.note, children: (0, import_jsx_runtime17.jsx)(DocumentText, { className: "text-ui-tag-neutral-icon ml-1 inline" }) }),
          (item == null ? void 0 : item.reason) && (0, import_jsx_runtime17.jsx)(
            Badge,
            {
              size: "2xsmall",
              className: "cursor-default select-none capitalize",
              rounded: "full",
              children: (_b = item == null ? void 0 : item.reason) == null ? void 0 : _b.label
            }
          )
        ] }),
        (0, import_jsx_runtime17.jsxs)(Text, { size: "small", leading: "compact", className: "text-ui-fg-muted", children: [
          t(`orders.returns.damagedItemReceived`),
          (0, import_jsx_runtime17.jsx)("span", { className: "ml-2", children: (0, import_jsx_runtime17.jsx)(return_info_popover_default, { orderReturn }) })
        ] })
      ]
    },
    orderReturn.id
  );
};
var ReturnBreakdown = ({
  orderReturn,
  itemId
}) => {
  var _a, _b;
  const { t } = useTranslation();
  const { getRelativeDate } = useDate();
  if (!["requested", "received", "partially_received"].includes(
    orderReturn.status || ""
  )) {
    return null;
  }
  const isRequested = orderReturn.status === "requested";
  const item = (_a = orderReturn == null ? void 0 : orderReturn.items) == null ? void 0 : _a.find((ri) => ri.item_id === itemId);
  const damagedQuantity = (item == null ? void 0 : item.damaged_quantity) || 0;
  return item && (0, import_jsx_runtime17.jsxs)(import_jsx_runtime17.Fragment, { children: [
    damagedQuantity > 0 && (0, import_jsx_runtime17.jsx)(
      ReturnBreakdownWithDamages,
      {
        orderReturn,
        itemId
      }
    ),
    (0, import_jsx_runtime17.jsxs)(
      "div",
      {
        className: "txt-compact-small-plus text-ui-fg-subtle bg-ui-bg-subtle flex flex-row justify-between gap-y-2 border-t-2 border-dotted px-6 py-4",
        children: [
          (0, import_jsx_runtime17.jsxs)("div", { className: "flex items-center gap-2", children: [
            (0, import_jsx_runtime17.jsx)(ArrowDownRightMini, { className: "text-ui-fg-muted" }),
            (0, import_jsx_runtime17.jsx)(Text, { size: "small", children: t(
              `orders.returns.${isRequested ? "returnRequestedInfo" : "returnReceivedInfo"}`,
              {
                requestedItemsCount: item == null ? void 0 : item[isRequested ? "quantity" : "received_quantity"]
              }
            ) }),
            (item == null ? void 0 : item.note) && (0, import_jsx_runtime17.jsx)(Tooltip, { content: item.note, children: (0, import_jsx_runtime17.jsx)(DocumentText, { className: "text-ui-tag-neutral-icon ml-1 inline" }) }),
            (item == null ? void 0 : item.reason) && (0, import_jsx_runtime17.jsx)(
              Badge,
              {
                size: "2xsmall",
                className: "cursor-default select-none capitalize",
                rounded: "full",
                children: (_b = item == null ? void 0 : item.reason) == null ? void 0 : _b.label
              }
            )
          ] }),
          orderReturn && isRequested && (0, import_jsx_runtime17.jsxs)(Text, { size: "small", leading: "compact", className: "text-ui-fg-muted", children: [
            getRelativeDate(orderReturn.created_at),
            (0, import_jsx_runtime17.jsx)("span", { className: "ml-2", children: (0, import_jsx_runtime17.jsx)(return_info_popover_default, { orderReturn }) })
          ] }),
          orderReturn && !isRequested && (0, import_jsx_runtime17.jsxs)(Text, { size: "small", leading: "compact", className: "text-ui-fg-muted", children: [
            t(`orders.returns.itemReceived`),
            (0, import_jsx_runtime17.jsx)("span", { className: "ml-2", children: (0, import_jsx_runtime17.jsx)(return_info_popover_default, { orderReturn }) })
          ] })
        ]
      },
      item.id
    )
  ] });
};
var ClaimBreakdown = ({
  claim,
  itemId
}) => {
  const { t } = useTranslation();
  const { getRelativeDate } = useDate();
  const items = claim.additional_items.filter(
    (item) => {
      var _a;
      return ((_a = item.item) == null ? void 0 : _a.id) === itemId;
    }
  );
  return !!items.length && (0, import_jsx_runtime17.jsxs)(
    "div",
    {
      className: "txt-compact-small-plus text-ui-fg-subtle bg-ui-bg-subtle flex flex-row justify-between gap-y-2 border-b-2 border-t-2 border-dotted px-6 py-4",
      children: [
        (0, import_jsx_runtime17.jsxs)("div", { className: "flex items-center gap-2", children: [
          (0, import_jsx_runtime17.jsx)(ArrowDownRightMini, { className: "text-ui-fg-muted" }),
          (0, import_jsx_runtime17.jsx)(Text, { size: "small", children: t(`orders.claims.outboundItemAdded`, {
            itemsCount: items.reduce(
              (acc, item) => acc = acc + item.quantity,
              0
            )
          }) })
        ] }),
        (0, import_jsx_runtime17.jsx)(Text, { size: "small", leading: "compact", className: "text-ui-fg-muted", children: getRelativeDate(claim.created_at) })
      ]
    },
    claim.id
  );
};
var ExchangeBreakdown = ({
  exchange,
  itemId
}) => {
  const { t } = useTranslation();
  const { getRelativeDate } = useDate();
  const items = exchange.additional_items.filter(
    (item) => {
      var _a;
      return ((_a = item == null ? void 0 : item.item) == null ? void 0 : _a.id) === itemId;
    }
  );
  return !!items.length && (0, import_jsx_runtime17.jsxs)(
    "div",
    {
      className: "txt-compact-small-plus text-ui-fg-subtle bg-ui-bg-subtle flex flex-row justify-between gap-y-2 border-b-2 border-t-2 border-dotted px-6 py-4",
      children: [
        (0, import_jsx_runtime17.jsxs)("div", { className: "flex items-center gap-2", children: [
          (0, import_jsx_runtime17.jsx)(ArrowDownRightMini, { className: "text-ui-fg-muted" }),
          (0, import_jsx_runtime17.jsx)(Text, { size: "small", children: t(`orders.exchanges.outboundItemAdded`, {
            itemsCount: items.reduce(
              (acc, item) => acc = acc + item.quantity,
              0
            )
          }) })
        ] }),
        (0, import_jsx_runtime17.jsx)(Text, { size: "small", leading: "compact", className: "text-ui-fg-muted", children: getRelativeDate(exchange.created_at) })
      ]
    },
    exchange.id
  );
};
var Total = ({ order }) => {
  const { t } = useTranslation();
  return (0, import_jsx_runtime17.jsxs)("div", { className: " flex flex-col gap-y-2 px-6 py-4", children: [
    (0, import_jsx_runtime17.jsxs)("div", { className: "text-ui-fg-base flex items-center justify-between", children: [
      (0, import_jsx_runtime17.jsx)(
        Text,
        {
          weight: "plus",
          className: "text-ui-fg-subtle",
          size: "small",
          leading: "compact",
          children: t("fields.total")
        }
      ),
      (0, import_jsx_runtime17.jsx)(
        Text,
        {
          weight: "plus",
          className: "text-ui-fg-subtle",
          size: "small",
          leading: "compact",
          children: getStylizedAmount(order.total, order.currency_code)
        }
      )
    ] }),
    (0, import_jsx_runtime17.jsxs)("div", { className: "text-ui-fg-base flex items-center justify-between", children: [
      (0, import_jsx_runtime17.jsx)(
        Text,
        {
          weight: "plus",
          className: "text-ui-fg-subtle",
          size: "small",
          leading: "compact",
          children: t("fields.paidTotal")
        }
      ),
      (0, import_jsx_runtime17.jsx)(
        Text,
        {
          weight: "plus",
          className: "text-ui-fg-subtle",
          size: "small",
          leading: "compact",
          children: getStylizedAmount(
            getTotalCaptured(order.payment_collections || []),
            order.currency_code
          )
        }
      )
    ] }),
    (0, import_jsx_runtime17.jsxs)("div", { className: "text-ui-fg-base flex items-center justify-between", children: [
      (0, import_jsx_runtime17.jsx)(
        Text,
        {
          className: "text-ui-fg-subtle text-semibold",
          size: "small",
          leading: "compact",
          weight: "plus",
          children: t("orders.returns.outstandingAmount")
        }
      ),
      (0, import_jsx_runtime17.jsx)(
        Text,
        {
          className: "text-ui-fg-subtle text-bold",
          size: "small",
          leading: "compact",
          weight: "plus",
          children: getStylizedAmount(
            order.summary.pending_difference || 0,
            order.currency_code
          )
        }
      )
    ] })
  ] });
};
var OrderDetail = () => {
  const initialData = useLoaderData();
  const { id } = useParams();
  const { getWidgets } = useExtension();
  const { order, isLoading, isError, error } = useOrder(
    id,
    {
      fields: DEFAULT_FIELDS
    },
    {
      initialData
    }
  );
  if (order) {
    order.items = order.items.sort((itemA, itemB) => {
      if (itemA.created_at > itemB.created_at) {
        return 1;
      }
      if (itemA.created_at < itemB.created_at) {
        return -1;
      }
      return 0;
    });
  }
  const { order: orderPreview, isLoading: isPreviewLoading } = useOrderPreview(
    id
  );
  if (isLoading || !order || isPreviewLoading) {
    return (0, import_jsx_runtime18.jsx)(TwoColumnPageSkeleton, { mainSections: 4, sidebarSections: 2, showJSON: true });
  }
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime18.jsxs)(
    TwoColumnPage,
    {
      widgets: {
        after: getWidgets("order.details.after"),
        before: getWidgets("order.details.before"),
        sideAfter: getWidgets("order.details.side.after"),
        sideBefore: getWidgets("order.details.side.before")
      },
      data: order,
      showJSON: true,
      showMetadata: true,
      hasOutlet: true,
      children: [
        (0, import_jsx_runtime18.jsxs)(TwoColumnPage.Main, { children: [
          (0, import_jsx_runtime18.jsx)(OrderActiveEditSection, { order }),
          (0, import_jsx_runtime18.jsx)(ActiveOrderClaimSection, { orderPreview }),
          (0, import_jsx_runtime18.jsx)(ActiveOrderExchangeSection, { orderPreview }),
          (0, import_jsx_runtime18.jsx)(ActiveOrderReturnSection, { orderPreview }),
          (0, import_jsx_runtime18.jsx)(OrderGeneralSection, { order }),
          (0, import_jsx_runtime18.jsx)(OrderSummarySection, { order }),
          (0, import_jsx_runtime18.jsx)(OrderPaymentSection, { order }),
          (0, import_jsx_runtime18.jsx)(OrderFulfillmentSection, { order })
        ] }),
        (0, import_jsx_runtime18.jsxs)(TwoColumnPage.Sidebar, { children: [
          (0, import_jsx_runtime18.jsx)(OrderCustomerSection, { order }),
          (0, import_jsx_runtime18.jsx)(OrderActivitySection, { order })
        ] })
      ]
    }
  );
};
export {
  OrderDetailBreadcrumb as Breadcrumb,
  OrderDetail as Component,
  orderLoader as loader
};
//# sourceMappingURL=order-detail-PI2MRGPH-KHCATUCM.js.map
