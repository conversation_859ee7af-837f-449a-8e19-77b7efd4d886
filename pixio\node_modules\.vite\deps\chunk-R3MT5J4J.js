// node_modules/@medusajs/dashboard/dist/chunk-G22WWLPG.mjs
function getApiKeyTypeFromPathname(pathname) {
  const isSecretKey = pathname.startsWith("/settings/secret-api-keys");
  switch (isSecretKey) {
    case true:
      return "secret";
    case false:
      return "publishable";
  }
}
function getApiKeyStatusProps(revokedAt, t) {
  if (!revokedAt) {
    return {
      color: "green",
      label: t("apiKeyManagement.status.active")
    };
  }
  return {
    color: "red",
    label: t("apiKeyManagement.status.revoked")
  };
}
function getApiKeyTypeProps(type, t) {
  if (type === "publishable") {
    return {
      color: "green",
      label: t("apiKeyManagement.type.publishable")
    };
  }
  return {
    color: "blue",
    label: t("apiKeyManagement.type.secret")
  };
}
var prettifyRedactedToken = (token) => {
  return token.replace("***", `•••`);
};

export {
  getApiKeyTypeFromPathname,
  getApiKeyStatusProps,
  getApiKeyTypeProps,
  prettifyRedactedToken
};
//# sourceMappingURL=chunk-R3MT5J4J.js.map
