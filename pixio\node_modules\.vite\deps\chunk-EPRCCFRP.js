import {
  queryClient
} from "./chunk-VMLNCWLE.js";
import {
  queryKeysFactory
} from "./chunk-CUPZIPFX.js";
import {
  sdk
} from "./chunk-JFCIYOH4.js";
import {
  useMutation,
  useQuery
} from "./chunk-R35JBZ3G.js";

// node_modules/@medusajs/dashboard/dist/chunk-2VTICXJR.mjs
var RETURN_REASONS_QUERY_KEY = "return_reasons";
var returnReasonsQueryKeys = queryKeysFactory(RETURN_REASONS_QUERY_KEY);
var useReturnReasons = (query, options) => {
  const { data, ...rest } = useQuery({
    queryFn: () => sdk.admin.returnReason.list(query),
    queryKey: returnReasonsQueryKeys.list(query),
    ...options
  });
  return { ...data, ...rest };
};
var useReturnReason = (id, query, options) => {
  const { data, ...rest } = useQuery({
    queryFn: () => sdk.admin.returnReason.retrieve(id, query),
    queryKey: returnReasonsQueryKeys.detail(id),
    ...options
  });
  return { ...data, ...rest };
};
var useCreateReturnReason = (query, options) => {
  return useMutation({
    mutationFn: async (data) => sdk.admin.returnReason.create(data, query),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({
        queryKey: returnReasonsQueryKeys.lists()
      });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useUpdateReturnReason = (id, query, options) => {
  return useMutation({
    mutationFn: async (data) => sdk.admin.returnReason.update(id, data, query),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({
        queryKey: returnReasonsQueryKeys.lists()
      });
      queryClient.invalidateQueries({
        queryKey: returnReasonsQueryKeys.detail(data.return_reason.id, query)
      });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useDeleteReturnReason = (id, options) => {
  return useMutation({
    mutationFn: () => sdk.admin.returnReason.delete(id),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({
        queryKey: returnReasonsQueryKeys.lists()
      });
      queryClient.invalidateQueries({
        queryKey: returnReasonsQueryKeys.detail(id)
      });
      queryClient.invalidateQueries({
        queryKey: returnReasonsQueryKeys.details()
      });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};

export {
  returnReasonsQueryKeys,
  useReturnReasons,
  useReturnReason,
  useCreateReturnReason,
  useUpdateReturnReason,
  useDeleteReturnReason
};
//# sourceMappingURL=chunk-EPRCCFRP.js.map
