import {
  clx
} from "./chunk-YP2LLXWB.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/chunk-ADOCJB6L.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var StatusCell = ({ color, children }) => {
  return (0, import_jsx_runtime.jsxs)("div", { className: "txt-compact-small text-ui-fg-subtle flex h-full w-full items-center gap-x-2 overflow-hidden", children: [
    (0, import_jsx_runtime.jsx)(
      "div",
      {
        role: "presentation",
        className: "flex h-5 w-2 items-center justify-center",
        children: (0, import_jsx_runtime.jsx)(
          "div",
          {
            className: clx(
              "h-2 w-2 rounded-sm shadow-[0px_0px_0px_1px_rgba(0,0,0,0.12)_inset]",
              {
                "bg-ui-tag-neutral-icon": color === "grey",
                "bg-ui-tag-green-icon": color === "green",
                "bg-ui-tag-red-icon": color === "red",
                "bg-ui-tag-blue-icon": color === "blue",
                "bg-ui-tag-orange-icon": color === "orange",
                "bg-ui-tag-purple-icon": color === "purple"
              }
            )
          }
        )
      }
    ),
    (0, import_jsx_runtime.jsx)("span", { className: "truncate", children })
  ] });
};

export {
  StatusCell
};
//# sourceMappingURL=chunk-OVCKROM5.js.map
