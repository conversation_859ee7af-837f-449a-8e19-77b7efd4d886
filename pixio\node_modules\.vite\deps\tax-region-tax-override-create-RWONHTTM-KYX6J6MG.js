import {
  TargetForm,
  TargetItem,
  TaxRateRuleReferenceSchema,
  createTaxRulePayload
} from "./chunk-PIUCNOJP.js";
import "./chunk-VD6KBTYK.js";
import "./chunk-U3YCCVIX.js";
import "./chunk-J7NC22T4.js";
import "./chunk-UEYKZWIS.js";
import "./chunk-5BDJR5GO.js";
import "./chunk-IP6YNP6I.js";
import "./chunk-EHZUZSWH.js";
import "./chunk-TPBCRBDT.js";
import "./chunk-GDXEFZZY.js";
import "./chunk-GEC36FCE.js";
import "./chunk-ITWRYKT3.js";
import "./chunk-AYBSQXJR.js";
import "./chunk-ASBI7JIX.js";
import "./chunk-QG4LHCCG.js";
import "./chunk-I2ZOQM4X.js";
import {
  PercentageInput
} from "./chunk-LFH2BKKX.js";
import "./chunk-EGZX7QZE.js";
import "./chunk-I45JH6GR.js";
import "./chunk-4FV466FW.js";
import "./chunk-QF476XOZ.js";
import "./chunk-MKZD3R7Z.js";
import "./chunk-ZJX5R5NM.js";
import "./chunk-LVAKEKGS.js";
import "./chunk-5ZQBU3TD.js";
import "./chunk-5AXVXNEZ.js";
import "./chunk-C43B7AQX.js";
import "./chunk-UDMOPZAP.js";
import "./chunk-7WCGWU4N.js";
import "./chunk-3M3PHA2D.js";
import "./chunk-NVCSASGM.js";
import "./chunk-OVCKROM5.js";
import "./chunk-EGRHWZRV.js";
import "./chunk-S5JEKJNE.js";
import "./chunk-VCX3BVQR.js";
import "./chunk-VCBFQV64.js";
import "./chunk-QLJZR2JY.js";
import "./chunk-X3TOWPPJ.js";
import "./chunk-H3DTEG3J.js";
import {
  SwitchBox
} from "./chunk-4LGSZLHH.js";
import "./chunk-EGRHWZRV.js";
import {
  KeyboundForm
} from "./chunk-DP54EP6X.js";
import "./chunk-32T72GVU.js";
import "./chunk-3LRISSP5.js";
import "./chunk-P5T2IZP5.js";
import "./chunk-I7242KR3.js";
import "./chunk-EB3HY52D.js";
import "./chunk-IONS3C54.js";
import "./chunk-XNFM7P3M.js";
import "./chunk-ZIXJCBL3.js";
import "./chunk-2TO4KOWC.js";
import "./chunk-4VJTA5FM.js";
import "./chunk-MX43XOWY.js";
import "./chunk-QX6SXRUW.js";
import "./chunk-7ANVLPZR.js";
import "./chunk-2E2FUO6N.js";
import {
  RouteFocusModal,
  StackedFocusModal,
  useRouteModal,
  useStackedModal
} from "./chunk-MVVOBQIC.js";
import "./chunk-HPGXK5DQ.js";
import {
  t
} from "./chunk-WHQIBI5S.js";
import {
  z
} from "./chunk-4XXECALA.js";
import "./chunk-6GQUHAET.js";
import "./chunk-YXT43UJF.js";
import "./chunk-66DVUN72.js";
import "./chunk-DL4QDYPT.js";
import "./chunk-NV2N3EWM.js";
import "./chunk-BF7OBKIN.js";
import "./chunk-7UAYECTW.js";
import "./chunk-Y3NYV3NU.js";
import "./chunk-MPXR7HT5.js";
import {
  Form,
  useFieldArray,
  useForm,
  useWatch
} from "./chunk-XXJU43CK.js";
import "./chunk-WISME5HP.js";
import "./chunk-WAUSOHFQ.js";
import "./chunk-3ORK3QZ4.js";
import {
  useCreateTaxRate
} from "./chunk-3A5TVVNI.js";
import {
  useTaxRegion
} from "./chunk-EZ62MM7J.js";
import "./chunk-3R6LYIXM.js";
import "./chunk-2P2O4TMQ.js";
import "./chunk-KI3HEITX.js";
import "./chunk-2VUCAO7L.js";
import "./chunk-PTIULTKD.js";
import "./chunk-KISXY2LD.js";
import "./chunk-L7C3XPHJ.js";
import "./chunk-T7MG2EIJ.js";
import "./chunk-MER4VYPR.js";
import "./chunk-2AWKOUCD.js";
import "./chunk-YL3TYBFR.js";
import "./chunk-TZWW72YW.js";
import "./chunk-MSQ25CWB.js";
import "./chunk-ONYSAQ5Z.js";
import "./chunk-NIH7SAXN.js";
import "./chunk-UEJFWAFE.js";
import "./chunk-GR55MFKT.js";
import "./chunk-AAFHKNJG.js";
import "./chunk-7XDBFDTZ.js";
import "./chunk-3JKGO5XL.js";
import "./chunk-OISUTS7G.js";
import "./chunk-AJYMIHLQ.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import "./chunk-JFCIYOH4.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-5GF3RGIE.js";
import {
  useParams
} from "./chunk-T7YBVUWZ.js";
import {
  Button,
  Divider,
  Heading,
  Hint,
  Input,
  Label,
  MagnifyingGlass,
  Select,
  Text,
  clx,
  toast
} from "./chunk-YP2LLXWB.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-ZSQ362B4.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/tax-region-tax-override-create-RWONHTTM.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var TaxRegionCreateTaxOverrideSchema = z.object({
  name: z.string().min(1),
  code: z.string().min(1),
  rate: z.object({
    float: z.number().optional(),
    value: z.string().optional()
  }).optional(),
  is_combinable: z.boolean().optional(),
  enabled_rules: z.object({
    product: z.boolean(),
    product_type: z.boolean()
    // product_collection: z.boolean(),
    // product_tag: z.boolean(),
    // customer_group: z.boolean(),
  }),
  product: z.array(TaxRateRuleReferenceSchema).optional(),
  product_type: z.array(TaxRateRuleReferenceSchema).optional()
  // product_collection: z.array(TaxRateRuleReferenceSchema).optional(),
  // product_tag: z.array(TaxRateRuleReferenceSchema).optional(),
  // customer_group: z.array(TaxRateRuleReferenceSchema).optional(),
});
var STACKED_MODAL_ID = "tr";
var getStackedModalId = (type) => `${STACKED_MODAL_ID}-${type}`;
var TaxRegionCreateTaxOverrideForm = ({
  taxRegion
}) => {
  const { t: t2 } = useTranslation();
  const { handleSuccess } = useRouteModal();
  const { setIsOpen } = useStackedModal();
  const form = useForm({
    defaultValues: {
      name: "",
      code: "",
      is_combinable: false,
      rate: {
        value: ""
      },
      enabled_rules: {
        product: true,
        product_type: false
        // product_collection: false,
        // product_tag: false,
        // customer_group: false,
      },
      product: [],
      product_type: []
      // product_collection: [],
      // product_tag: [],
      // customer_group: [],
    },
    resolver: t(TaxRegionCreateTaxOverrideSchema)
  });
  const { mutateAsync, isPending } = useCreateTaxRate();
  const handleSubmit = form.handleSubmit(async (values) => {
    var _a;
    const {
      product,
      product_type
      // customer_group,
      // product_collection,
      // product_tag,
    } = values;
    const productRules = createTaxRulePayload({
      reference_type: "product",
      references: product || []
    });
    const productTypeRules = createTaxRulePayload({
      reference_type: "product_type",
      references: product_type || []
    });
    const rules = [
      productRules,
      productTypeRules
      // customerGroupRules,
      // productCollectionRules,
      // productTagRules,
    ].filter((rule) => Boolean(rule)).flatMap((r) => r);
    mutateAsync(
      {
        name: values.name,
        tax_region_id: taxRegion.id,
        rate: (_a = values.rate) == null ? void 0 : _a.float,
        code: values.code,
        is_combinable: values.is_combinable,
        rules,
        is_default: false
      },
      {
        onSuccess: () => {
          handleSuccess();
        },
        onError: (error) => {
          toast.error(error.message);
        }
      }
    );
  });
  const products = useFieldArray({
    control: form.control,
    name: "product"
    /* PRODUCT */
  });
  const productTypes = useFieldArray({
    control: form.control,
    name: "product_type"
    /* PRODUCT_TYPE */
  });
  const getControls = (type) => {
    switch (type) {
      case "product":
        return products;
      case "product_type":
        return productTypes;
    }
  };
  const referenceTypeOptions = [
    {
      value: "product",
      label: t2("taxRegions.fields.targets.options.product")
    },
    {
      value: "product_type",
      label: t2("taxRegions.fields.targets.options.productType")
    }
    // {
    //   value: TaxRateRuleReferenceType.PRODUCT_COLLECTION,
    //   label: t("taxRegions.fields.targets.options.productCollection"),
    // },
    // {
    //   value: TaxRateRuleReferenceType.PRODUCT_TAG,
    //   label: t("taxRegions.fields.targets.options.productTag"),
    // },
    // {
    //   value: TaxRateRuleReferenceType.CUSTOMER_GROUP,
    //   label: t("taxRegions.fields.targets.options.customerGroup"),
    // },
  ];
  const searchPlaceholders = {
    [
      "product"
      /* PRODUCT */
    ]: t2(
      "taxRegions.fields.targets.placeholders.product"
    ),
    [
      "product_type"
      /* PRODUCT_TYPE */
    ]: t2(
      "taxRegions.fields.targets.placeholders.productType"
    )
    // [TaxRateRuleReferenceType.PRODUCT_COLLECTION]: t(
    //   "taxRegions.fields.targets.placeholders.productCollection"
    // ),
    // [TaxRateRuleReferenceType.PRODUCT_TAG]: t(
    //   "taxRegions.fields.targets.placeholders.productTag"
    // ),
    // [TaxRateRuleReferenceType.CUSTOMER_GROUP]: t(
    //   "taxRegions.fields.targets.placeholders.customerGroup"
    // ),
  };
  const getFieldHandler = (type) => {
    const { fields, remove, append } = getControls(type);
    const modalId = getStackedModalId(type);
    return (references) => {
      if (!references.length) {
        form.setValue(type, [], {
          shouldDirty: true
        });
        setIsOpen(modalId, false);
        return;
      }
      const newIds = references.map((reference) => reference.value);
      const fieldsToAdd = references.filter(
        (reference) => !fields.some((field) => field.value === reference.value)
      );
      for (const field of fields) {
        if (!newIds.includes(field.value)) {
          remove(fields.indexOf(field));
        }
      }
      append(fieldsToAdd);
      setIsOpen(modalId, false);
    };
  };
  const displayOrder = /* @__PURE__ */ new Set([
    "product"
    /* PRODUCT */
  ]);
  const disableRule = (type) => {
    form.setValue(type, [], {
      shouldDirty: true
    });
    form.setValue(`enabled_rules.${type}`, false, {
      shouldDirty: true
    });
    displayOrder.delete(type);
  };
  const enableRule = (type) => {
    form.setValue(`enabled_rules.${type}`, true, {
      shouldDirty: true
    });
    form.setValue(type, [], {
      shouldDirty: true
    });
    displayOrder.add(type);
  };
  const watchedEnabledRules = useWatch({
    control: form.control,
    name: "enabled_rules"
  });
  const addRule = () => {
    const firstDisabledRule = Object.keys(watchedEnabledRules).find(
      (key) => !watchedEnabledRules[key]
    );
    if (firstDisabledRule) {
      enableRule(firstDisabledRule);
    }
  };
  const visibleRuleTypes = referenceTypeOptions.filter((option) => watchedEnabledRules[option.value]).sort((a, b) => {
    const orderArray = Array.from(displayOrder);
    return orderArray.indexOf(b.value) - orderArray.indexOf(a.value);
  });
  const getAvailableRuleTypes = (type) => {
    return referenceTypeOptions.filter((option) => {
      return !visibleRuleTypes.some(
        (visibleOption) => visibleOption.value === option.value
      ) || option.value === type;
    });
  };
  const showAddButton = Object.values(watchedEnabledRules).some(
    (value) => !value
  );
  return (0, import_jsx_runtime.jsx)(RouteFocusModal.Form, { form, children: (0, import_jsx_runtime.jsxs)(
    KeyboundForm,
    {
      onSubmit: handleSubmit,
      className: "flex h-full flex-col overflow-hidden",
      children: [
        (0, import_jsx_runtime.jsx)(RouteFocusModal.Header, {}),
        (0, import_jsx_runtime.jsx)(RouteFocusModal.Body, { className: "flex flex-1 flex-col overflow-hidden", children: (0, import_jsx_runtime.jsx)("div", { className: "flex flex-1 flex-col items-center overflow-y-auto", children: (0, import_jsx_runtime.jsxs)("div", { className: "flex w-full max-w-[720px] flex-col gap-y-8 px-2 py-16", children: [
          (0, import_jsx_runtime.jsxs)("div", { children: [
            (0, import_jsx_runtime.jsx)(RouteFocusModal.Title, { asChild: true, children: (0, import_jsx_runtime.jsx)(Heading, { children: t2("taxRegions.taxOverrides.create.header") }) }),
            (0, import_jsx_runtime.jsx)(RouteFocusModal.Description, { asChild: true, children: (0, import_jsx_runtime.jsx)(Text, { size: "small", className: "text-ui-fg-subtle", children: t2("taxRegions.taxOverrides.create.hint") }) })
          ] }),
          (0, import_jsx_runtime.jsx)("div", { className: "flex flex-col gap-4", children: (0, import_jsx_runtime.jsx)("div", { className: "flex flex-col gap-y-4", children: (0, import_jsx_runtime.jsxs)("div", { className: "grid grid-cols-1 gap-4 md:grid-cols-2", children: [
            (0, import_jsx_runtime.jsx)(
              Form.Field,
              {
                control: form.control,
                name: "name",
                render: ({ field }) => {
                  return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                    (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("fields.name") }),
                    (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(Input, { ...field }) }),
                    (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                  ] });
                }
              }
            ),
            (0, import_jsx_runtime.jsx)(
              Form.Field,
              {
                control: form.control,
                name: "rate",
                render: ({ field: { value, onChange, ...field } }) => {
                  return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                    (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("taxRegions.fields.taxRate") }),
                    (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(
                      PercentageInput,
                      {
                        ...field,
                        placeholder: "0.00",
                        value: value == null ? void 0 : value.value,
                        onValueChange: (value2, _name, values) => onChange({
                          value: value2,
                          float: values == null ? void 0 : values.float
                        })
                      }
                    ) }),
                    (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                  ] });
                }
              }
            ),
            (0, import_jsx_runtime.jsx)(
              Form.Field,
              {
                control: form.control,
                name: "code",
                render: ({ field }) => {
                  return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                    (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("taxRegions.fields.taxCode") }),
                    (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(Input, { ...field }) }),
                    (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                  ] });
                }
              }
            )
          ] }) }) }),
          (0, import_jsx_runtime.jsx)(
            SwitchBox,
            {
              control: form.control,
              name: "is_combinable",
              label: t2("taxRegions.fields.isCombinable.label"),
              description: t2("taxRegions.fields.isCombinable.hint")
            }
          ),
          (0, import_jsx_runtime.jsxs)("div", { className: "flex flex-col gap-y-3", children: [
            (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center justify-between gap-x-4", children: [
              (0, import_jsx_runtime.jsxs)("div", { className: "flex flex-col", children: [
                (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center gap-x-1", children: [
                  (0, import_jsx_runtime.jsx)(
                    Label,
                    {
                      id: "tax_region_rules_label",
                      htmlFor: "tax_region_rules",
                      children: t2("taxRegions.fields.targets.label")
                    }
                  ),
                  (0, import_jsx_runtime.jsxs)(
                    Text,
                    {
                      size: "small",
                      leading: "compact",
                      className: "text-ui-fg-muted",
                      children: [
                        "(",
                        t2("fields.optional"),
                        ")"
                      ]
                    }
                  )
                ] }),
                (0, import_jsx_runtime.jsx)(
                  Hint,
                  {
                    id: "tax_region_rules_description",
                    className: "text-pretty",
                    children: t2("taxRegions.fields.targets.hint")
                  }
                )
              ] }),
              showAddButton && (0, import_jsx_runtime.jsx)(
                Button,
                {
                  onClick: addRule,
                  type: "button",
                  size: "small",
                  variant: "transparent",
                  className: "text-ui-fg-interactive hover:text-ui-fg-interactive-hover flex-shrink-0",
                  children: t2("taxRegions.fields.targets.action")
                }
              )
            ] }),
            (0, import_jsx_runtime.jsx)(
              "div",
              {
                id: "tax_region_rules",
                "aria-labelledby": "tax_region_rules_label",
                "aria-describedby": "tax_region_rules_description",
                role: "application",
                className: "flex flex-col gap-y-3",
                children: visibleRuleTypes.map((ruleType, index) => {
                  const type = ruleType.value;
                  const label = ruleType.label;
                  const isLast = index === visibleRuleTypes.length - 1;
                  const searchPlaceholder = searchPlaceholders[type];
                  const options = getAvailableRuleTypes(type);
                  const { fields, remove } = getControls(type);
                  const handler = getFieldHandler(type);
                  const modalId = getStackedModalId(type);
                  const handleChangeType = (value) => {
                    disableRule(type);
                    enableRule(value);
                  };
                  return (0, import_jsx_runtime.jsx)("div", { children: (0, import_jsx_runtime.jsx)(
                    Form.Field,
                    {
                      control: form.control,
                      name: ruleType.value,
                      render: ({
                        field: {
                          value: _value,
                          onChange: _onChange,
                          ...field
                        }
                      }) => {
                        return (0, import_jsx_runtime.jsxs)(Form.Item, { className: "space-y-0", children: [
                          (0, import_jsx_runtime.jsx)(Form.Label, { className: "sr-only", children: label }),
                          (0, import_jsx_runtime.jsxs)(
                            "div",
                            {
                              className: clx(
                                "bg-ui-bg-component shadow-elevation-card-rest transition-fg grid gap-1.5 rounded-xl py-1.5",
                                "aria-[invalid='true']:shadow-borders-error"
                              ),
                              role: "application",
                              ...field,
                              children: [
                                (0, import_jsx_runtime.jsxs)("div", { className: "text-ui-fg-subtle grid gap-1.5 px-1.5 md:grid-cols-2", children: [
                                  isLast ? (0, import_jsx_runtime.jsxs)(
                                    Select,
                                    {
                                      value: type,
                                      onValueChange: handleChangeType,
                                      children: [
                                        (0, import_jsx_runtime.jsx)(Select.Trigger, { className: "bg-ui-bg-field-component hover:bg-ui-bg-field-component-hover", children: (0, import_jsx_runtime.jsx)(Select.Value, {}) }),
                                        (0, import_jsx_runtime.jsx)(Select.Content, { children: options.map((option) => {
                                          return (0, import_jsx_runtime.jsx)(
                                            Select.Item,
                                            {
                                              value: option.value,
                                              children: option.label
                                            },
                                            option.value
                                          );
                                        }) })
                                      ]
                                    }
                                  ) : (0, import_jsx_runtime.jsx)("div", { className: "bg-ui-bg-field shadow-borders-base txt-compact-small rounded-md px-2 py-1.5", children: label }),
                                  (0, import_jsx_runtime.jsx)("div", { className: "bg-ui-bg-field shadow-borders-base txt-compact-small rounded-md px-2 py-1.5", children: t2(
                                    "taxRegions.fields.targets.operators.in"
                                  ) })
                                ] }),
                                (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center gap-1.5 px-1.5", children: [
                                  (0, import_jsx_runtime.jsxs)(StackedFocusModal, { id: modalId, children: [
                                    (0, import_jsx_runtime.jsx)(StackedFocusModal.Trigger, { asChild: true, children: (0, import_jsx_runtime.jsxs)(
                                      "button",
                                      {
                                        type: "button",
                                        className: "bg-ui-bg-field-component hover:bg-ui-bg-field-component-hover shadow-borders-base txt-compact-small text-ui-fg-muted transition-fg focus-visible:shadow-borders-interactive-with-active flex flex-1 items-center gap-x-2 rounded-md px-2 py-1.5 outline-none",
                                        children: [
                                          (0, import_jsx_runtime.jsx)(MagnifyingGlass, {}),
                                          searchPlaceholder
                                        ]
                                      }
                                    ) }),
                                    (0, import_jsx_runtime.jsx)(StackedFocusModal.Trigger, { asChild: true, children: (0, import_jsx_runtime.jsx)(Button, { variant: "secondary", children: t2("actions.browse") }) }),
                                    (0, import_jsx_runtime.jsxs)(StackedFocusModal.Content, { children: [
                                      (0, import_jsx_runtime.jsxs)(StackedFocusModal.Header, { children: [
                                        (0, import_jsx_runtime.jsx)(StackedFocusModal.Title, { asChild: true, children: (0, import_jsx_runtime.jsx)(Heading, { className: "sr-only", children: t2(
                                          "taxRegions.fields.targets.modal.header"
                                        ) }) }),
                                        (0, import_jsx_runtime.jsx)(StackedFocusModal.Description, { className: "sr-only", children: t2(
                                          "taxRegions.fields.targets.hint"
                                        ) })
                                      ] }),
                                      (0, import_jsx_runtime.jsx)(
                                        TargetForm,
                                        {
                                          type: "focus",
                                          referenceType: type,
                                          state: fields,
                                          setState: handler
                                        }
                                      )
                                    ] })
                                  ] }),
                                  (0, import_jsx_runtime.jsx)(
                                    Button,
                                    {
                                      variant: "secondary",
                                      onClick: () => disableRule(type),
                                      type: "button",
                                      children: t2("actions.delete")
                                    }
                                  )
                                ] }),
                                fields.length > 0 ? (0, import_jsx_runtime.jsxs)("div", { className: "flex flex-col gap-y-1.5", children: [
                                  (0, import_jsx_runtime.jsx)(Divider, { variant: "dashed" }),
                                  (0, import_jsx_runtime.jsx)("div", { className: "flex flex-col gap-y-1.5 px-1.5", children: fields.map((field2, index2) => {
                                    return (0, import_jsx_runtime.jsx)(
                                      TargetItem,
                                      {
                                        index: index2,
                                        label: field2.label,
                                        onRemove: remove
                                      },
                                      field2.id
                                    );
                                  }) })
                                ] }) : null
                              ]
                            }
                          ),
                          (0, import_jsx_runtime.jsx)(Form.ErrorMessage, { className: "mt-2" })
                        ] });
                      }
                    }
                  ) }, type);
                })
              }
            )
          ] })
        ] }) }) }),
        (0, import_jsx_runtime.jsx)(RouteFocusModal.Footer, { children: (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center justify-end gap-x-2", children: [
          (0, import_jsx_runtime.jsx)(RouteFocusModal.Close, { asChild: true, children: (0, import_jsx_runtime.jsx)(Button, { size: "small", variant: "secondary", children: t2("actions.cancel") }) }),
          (0, import_jsx_runtime.jsx)(Button, { size: "small", type: "submit", isLoading: isPending, children: t2("actions.save") })
        ] }) })
      ]
    }
  ) });
};
var TaxRegionCreateTaxOverride = () => {
  const { id, province_id } = useParams();
  const { tax_region, isPending, isError, error } = useTaxRegion(
    province_id || id
  );
  const ready = !isPending && !!tax_region;
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime2.jsx)(RouteFocusModal, { children: ready && (0, import_jsx_runtime2.jsx)(TaxRegionCreateTaxOverrideForm, { taxRegion: tax_region }) });
};
export {
  TaxRegionCreateTaxOverride as Component
};
//# sourceMappingURL=tax-region-tax-override-create-RWONHTTM-KYX6J6MG.js.map
