{"version": 3, "sources": ["../../@medusajs/dashboard/dist/order-allocate-items-NFV3KZAY.mjs"], "sourcesContent": ["import {\n  getFulfillableQuantity\n} from \"./chunk-WKOPGFW5.mjs\";\nimport \"./chunk-IUCDCPJU.mjs\";\nimport {\n  KeyboundForm\n} from \"./chunk-6HTZNHPT.mjs\";\nimport {\n  RouteFocusModal,\n  useRouteModal\n} from \"./chunk-4TC5YS65.mjs\";\nimport {\n  Thumbnail\n} from \"./chunk-MNXC6Q4F.mjs\";\nimport {\n  Form\n} from \"./chunk-OBQI23QM.mjs\";\nimport {\n  useStockLocations\n} from \"./chunk-32IQRUVY.mjs\";\nimport {\n  ordersQueryKeys,\n  useOrder\n} from \"./chunk-FNYASI54.mjs\";\nimport {\n  useCreateReservationItem\n} from \"./chunk-FVC7M755.mjs\";\nimport \"./chunk-6I62UDJA.mjs\";\nimport {\n  queryClient\n} from \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-RPUOO7AV.mjs\";\n\n// src/routes/orders/order-allocate-items/order-allocate-items.tsx\nimport { useParams } from \"react-router-dom\";\n\n// src/routes/orders/order-allocate-items/components/order-create-fulfillment-form/order-allocate-items-form.tsx\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { useEffect, useMemo as useMemo2, useState as useState2 } from \"react\";\nimport { useTranslation as useTranslation2 } from \"react-i18next\";\nimport { Alert, Button, Heading, Input as Input2, Select, toast } from \"@medusajs/ui\";\nimport { useForm, useWatch as useWatch2 } from \"react-hook-form\";\n\n// src/routes/orders/order-allocate-items/components/order-create-fulfillment-form/constants.ts\nimport { z } from \"zod\";\nvar AllocateItemsSchema = z.object({\n  location_id: z.string(),\n  quantity: z.record(z.string(), z.number().or(z.string()))\n});\n\n// src/routes/orders/order-allocate-items/components/order-create-fulfillment-form/order-allocate-items-item.tsx\nimport { useMemo, useState } from \"react\";\nimport { useTranslation } from \"react-i18next\";\nimport {\n  Component,\n  ExclamationCircleSolid,\n  TriangleDownMini\n} from \"@medusajs/icons\";\nimport { useWatch } from \"react-hook-form\";\nimport { Input, Text, clx } from \"@medusajs/ui\";\nimport { Fragment, jsx, jsxs } from \"react/jsx-runtime\";\nfunction OrderAllocateItemsItem({\n  item,\n  form,\n  locationId,\n  onQuantityChange\n}) {\n  const { t } = useTranslation();\n  const variant = item.variant;\n  const inventory = item.variant?.inventory || [];\n  const [isOpen, setIsOpen] = useState(false);\n  const quantityField = useWatch({\n    control: form.control,\n    name: \"quantity\"\n  });\n  const hasInventoryKit = !!variant?.inventory_items.length && variant?.inventory_items.length > 1;\n  const { availableQuantity, inStockQuantity } = useMemo(() => {\n    if (!variant || !locationId) {\n      return {};\n    }\n    const locationInventory = inventory[0]?.location_levels?.find(\n      (inv) => inv.location_id === locationId\n    );\n    if (!locationInventory) {\n      return {};\n    }\n    return {\n      availableQuantity: locationInventory.available_quantity,\n      inStockQuantity: locationInventory.stocked_quantity\n    };\n  }, [variant, locationId]);\n  const hasQuantityError = !hasInventoryKit && availableQuantity && quantityField[`${item.id}-${item.variant?.inventory[0].id}`] && quantityField[`${item.id}-${item.variant?.inventory[0].id}`] > availableQuantity;\n  const minValue = 0;\n  const maxValue = Math.min(\n    getFulfillableQuantity(item),\n    availableQuantity || Number.MAX_SAFE_INTEGER\n  );\n  return /* @__PURE__ */ jsxs(\"div\", { className: \"bg-ui-bg-subtle shadow-elevation-card-rest my-2 min-w-[720px] divide-y divide-dashed rounded-xl\", children: [\n    /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center gap-x-3 p-3 text-sm\", children: [\n      /* @__PURE__ */ jsx(\"div\", { className: \"flex flex-1 items-center\", children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center gap-x-3\", children: [\n        hasQuantityError && /* @__PURE__ */ jsx(ExclamationCircleSolid, { className: \"text-ui-fg-error\" }),\n        /* @__PURE__ */ jsx(Thumbnail, { src: item.thumbnail }),\n        /* @__PURE__ */ jsxs(\"div\", { className: \"flex flex-col\", children: [\n          /* @__PURE__ */ jsxs(\"div\", { className: \"flex flex-row\", children: [\n            /* @__PURE__ */ jsx(Text, { className: \"txt-small flex\", as: \"span\", weight: \"plus\", children: item.product_title }),\n            item.variant_sku && /* @__PURE__ */ jsxs(\"span\", { className: \"text-ui-fg-subtle\", children: [\n              \" \",\n              \"(\",\n              item.variant_sku,\n              \")\"\n            ] }),\n            hasInventoryKit && /* @__PURE__ */ jsx(Component, { className: \"text-ui-fg-muted ml-2 overflow-visible pt-[2px]\" })\n          ] }),\n          /* @__PURE__ */ jsx(Text, { as: \"div\", className: \"text-ui-fg-subtle txt-small\", children: item.title })\n        ] })\n      ] }) }),\n      /* @__PURE__ */ jsxs(\n        \"div\",\n        {\n          className: clx(\n            \"flex flex-1 items-center gap-x-3\",\n            hasInventoryKit ? \"justify-end\" : \"justify-between\"\n          ),\n          children: [\n            !hasInventoryKit && /* @__PURE__ */ jsxs(Fragment, { children: [\n              /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center gap-3\", children: [\n                /* @__PURE__ */ jsx(\"div\", { className: \"bg-ui-border-strong block h-[12px] w-[1px]\" }),\n                /* @__PURE__ */ jsxs(\"div\", { className: \"txt-small flex flex-col\", children: [\n                  /* @__PURE__ */ jsx(\"span\", { className: \"text-ui-fg-subtle font-medium\", children: t(\"labels.available\") }),\n                  /* @__PURE__ */ jsxs(\"span\", { className: \"text-ui-fg-muted\", children: [\n                    availableQuantity || \"-\",\n                    availableQuantity && !hasInventoryKit && quantityField[`${item.id}-${item.variant?.inventory[0].id}`] && /* @__PURE__ */ jsxs(\"span\", { className: \"text-ui-fg-error txt-small ml-1\", children: [\n                      \"-\",\n                      quantityField[`${item.id}-${item.variant?.inventory[0].id}`]\n                    ] })\n                  ] })\n                ] })\n              ] }),\n              /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center gap-3\", children: [\n                /* @__PURE__ */ jsx(\"div\", { className: \"bg-ui-border-strong block h-[12px] w-[1px]\" }),\n                /* @__PURE__ */ jsxs(\"div\", { className: \"txt-small flex flex-col\", children: [\n                  /* @__PURE__ */ jsx(\"span\", { className: \"text-ui-fg-subtle font-medium\", children: t(\"labels.inStock\") }),\n                  /* @__PURE__ */ jsx(\"span\", { className: \"text-ui-fg-muted\", children: inStockQuantity || \"-\" })\n                ] })\n              ] })\n            ] }),\n            /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center gap-3\", children: [\n              /* @__PURE__ */ jsx(\"div\", { className: \"bg-ui-border-strong block h-[12px] w-[1px]\" }),\n              /* @__PURE__ */ jsxs(\"div\", { className: \"text-ui-fg-subtle txt-small mr-2 flex flex-row items-center gap-2\", children: [\n                /* @__PURE__ */ jsx(\n                  Form.Field,\n                  {\n                    control: form.control,\n                    name: hasInventoryKit ? `quantity.${item.id}-` : `quantity.${item.id}-${item.variant?.inventory[0].id}`,\n                    rules: {\n                      required: !hasInventoryKit,\n                      min: !hasInventoryKit && minValue,\n                      max: maxValue\n                    },\n                    render: ({ field }) => {\n                      return /* @__PURE__ */ jsx(Form.Item, { children: /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(\n                        Input,\n                        {\n                          className: \"bg-ui-bg-base txt-small w-[46px] rounded-lg text-right [appearance:textfield] [&::-webkit-inner-spin-button]:appearance-none [&::-webkit-outer-spin-button]:appearance-none\",\n                          type: \"number\",\n                          ...field,\n                          disabled: !locationId,\n                          onChange: (e) => {\n                            const val = e.target.value === \"\" ? null : Number(e.target.value);\n                            onQuantityChange(\n                              item.variant?.inventory[0],\n                              item,\n                              hasInventoryKit,\n                              val,\n                              true\n                            );\n                          }\n                        }\n                      ) }) });\n                    }\n                  }\n                ),\n                \" \",\n                \"/ \",\n                item.quantity,\n                \" \",\n                t(\"fields.qty\")\n              ] })\n            ] })\n          ]\n        }\n      )\n    ] }),\n    hasInventoryKit && /* @__PURE__ */ jsx(\"div\", { className: \"px-4 py-2\", children: /* @__PURE__ */ jsxs(\n      \"div\",\n      {\n        onClick: () => setIsOpen((o) => !o),\n        className: \"flex items-center gap-x-2\",\n        children: [\n          /* @__PURE__ */ jsx(\n            TriangleDownMini,\n            {\n              style: { transform: `rotate(${isOpen ? -90 : 0}deg)` },\n              className: \"text-ui-fg-muted -mt-[1px]\"\n            }\n          ),\n          /* @__PURE__ */ jsx(\"span\", { className: \"txt-small text-ui-fg-muted cursor-pointer\", children: t(\"orders.allocateItems.consistsOf\", {\n            num: inventory.length\n          }) })\n        ]\n      }\n    ) }),\n    isOpen && variant.inventory.map((i, ind) => {\n      const location = i.location_levels.find(\n        (l) => l.location_id === locationId\n      );\n      const hasQuantityError2 = !!quantityField[`${item.id}-${i.id}`] && quantityField[`${item.id}-${i.id}`] > location.available_quantity;\n      return /* @__PURE__ */ jsxs(\"div\", { className: \"txt-small flex items-center gap-x-3 p-4\", children: [\n        /* @__PURE__ */ jsxs(\"div\", { className: \"flex flex-1 flex-row items-center gap-3\", children: [\n          hasQuantityError2 && /* @__PURE__ */ jsx(ExclamationCircleSolid, { className: \"text-ui-fg-error\" }),\n          /* @__PURE__ */ jsxs(\"div\", { className: \"flex flex-col\", children: [\n            /* @__PURE__ */ jsx(\"span\", { className: \"text-ui-fg-subtle\", children: i.title }),\n            /* @__PURE__ */ jsx(\"span\", { className: \"text-ui-fg-muted\", children: t(\"orders.allocateItems.requires\", {\n              num: variant.inventory_items[ind].required_quantity\n            }) })\n          ] })\n        ] }),\n        /* @__PURE__ */ jsxs(\"div\", { className: \"flex flex-1 flex-row justify-between\", children: [\n          /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center gap-3\", children: [\n            /* @__PURE__ */ jsx(\"div\", { className: \"bg-ui-border-strong block h-[12px] w-[1px]\" }),\n            /* @__PURE__ */ jsxs(\"div\", { className: \"txt-small flex flex-col\", children: [\n              /* @__PURE__ */ jsx(\"span\", { className: \"text-ui-fg-subtle font-medium\", children: t(\"labels.available\") }),\n              /* @__PURE__ */ jsxs(\"span\", { className: \"text-ui-fg-muted\", children: [\n                location?.available_quantity || \"-\",\n                location?.available_quantity && quantityField[`${item.id}-${i.id}`] && /* @__PURE__ */ jsxs(\"span\", { className: \"text-ui-fg-error txt-small ml-1\", children: [\n                  \"-\",\n                  quantityField[`${item.id}-${i.id}`]\n                ] })\n              ] })\n            ] })\n          ] }),\n          /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center gap-3\", children: [\n            /* @__PURE__ */ jsx(\"div\", { className: \"bg-ui-border-strong block h-[12px] w-[1px]\" }),\n            /* @__PURE__ */ jsxs(\"div\", { className: \"txt-small flex flex-col\", children: [\n              /* @__PURE__ */ jsx(\"span\", { className: \"text-ui-fg-subtle font-medium\", children: t(\"labels.inStock\") }),\n              /* @__PURE__ */ jsx(\"span\", { className: \"text-ui-fg-muted\", children: location?.stocked_quantity || \"-\" })\n            ] })\n          ] }),\n          /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center gap-3\", children: [\n            /* @__PURE__ */ jsx(\"div\", { className: \"bg-ui-border-strong block h-[12px] w-[1px]\" }),\n            /* @__PURE__ */ jsxs(\"div\", { className: \"text-ui-fg-subtle txt-small mr-1 flex flex-row items-center gap-2\", children: [\n              /* @__PURE__ */ jsx(\n                Form.Field,\n                {\n                  control: form.control,\n                  name: `quantity.${item.id}-${i.id}`,\n                  rules: {\n                    required: true,\n                    min: 0,\n                    max: location?.available_quantity\n                  },\n                  render: ({ field }) => {\n                    return /* @__PURE__ */ jsx(Form.Item, { children: /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(\n                      Input,\n                      {\n                        className: \"bg-ui-bg-base txt-small w-[46px] rounded-lg text-right [appearance:textfield] [&::-webkit-inner-spin-button]:appearance-none [&::-webkit-outer-spin-button]:appearance-none\",\n                        type: \"number\",\n                        ...field,\n                        disabled: !locationId,\n                        onChange: (e) => {\n                          const val = e.target.value === \"\" ? null : Number(e.target.value);\n                          onQuantityChange(\n                            i,\n                            item,\n                            hasInventoryKit,\n                            val\n                          );\n                        }\n                      }\n                    ) }) });\n                  }\n                }\n              ),\n              \"/\",\n              \" \",\n              item.quantity * variant.inventory_items[ind].required_quantity,\n              \" \",\n              t(\"fields.qty\")\n            ] })\n          ] })\n        ] })\n      ] }, i.id);\n    })\n  ] });\n}\n\n// src/routes/orders/order-allocate-items/components/order-create-fulfillment-form/order-allocate-items-form.tsx\nimport { jsx as jsx2, jsxs as jsxs2 } from \"react/jsx-runtime\";\nfunction OrderAllocateItemsForm({ order }) {\n  const { t } = useTranslation2();\n  const { handleSuccess } = useRouteModal();\n  const [disableSubmit, setDisableSubmit] = useState2(false);\n  const [filterTerm, setFilterTerm] = useState2(\"\");\n  const { mutateAsync: allocateItems, isPending: isMutating } = useCreateReservationItem();\n  const itemsToAllocate = useMemo2(\n    () => order.items.filter(\n      (item) => item.variant?.manage_inventory && item.variant?.inventory.length && item.quantity - item.detail.fulfilled_quantity > 0\n    ),\n    [order.items]\n  );\n  const filteredItems = useMemo2(() => {\n    return itemsToAllocate.filter(\n      (i) => i.variant_title.toLowerCase().includes(filterTerm) || i.product_title.toLowerCase().includes(filterTerm)\n    );\n  }, [itemsToAllocate, filterTerm]);\n  const noItemsToAllocate = !itemsToAllocate.length;\n  const form = useForm({\n    defaultValues: {\n      location_id: \"\",\n      quantity: defaultAllocations(itemsToAllocate)\n    },\n    resolver: zodResolver(AllocateItemsSchema)\n  });\n  const { stock_locations = [] } = useStockLocations();\n  const handleSubmit = form.handleSubmit(async (data) => {\n    try {\n      const payload = Object.entries(data.quantity).filter(([key]) => !key.endsWith(\"-\")).map(([key, quantity]) => [...key.split(\"-\"), quantity]);\n      if (payload.some((d) => d[2] === \"\")) {\n        form.setError(\"root.quantityNotAllocated\", {\n          type: \"manual\",\n          message: t(\"orders.allocateItems.error.quantityNotAllocated\")\n        });\n        return;\n      }\n      const promises = payload.map(\n        ([itemId, inventoryId, quantity]) => allocateItems({\n          location_id: data.location_id,\n          inventory_item_id: inventoryId,\n          line_item_id: itemId,\n          quantity\n        })\n      );\n      await Promise.all(promises);\n      await queryClient.invalidateQueries({\n        queryKey: ordersQueryKeys.details()\n      });\n      handleSuccess(`/orders/${order.id}`);\n      toast.success(t(\"general.success\"), {\n        description: t(\"orders.allocateItems.toast.created\"),\n        dismissLabel: t(\"actions.close\")\n      });\n    } catch (e) {\n      toast.error(t(\"general.error\"), {\n        description: e.message,\n        dismissLabel: t(\"actions.close\")\n      });\n    }\n  });\n  const onQuantityChange = (inventoryItem, lineItem, hasInventoryKit, value, isRoot) => {\n    let shouldDisableSubmit = false;\n    const key = isRoot && hasInventoryKit ? `quantity.${lineItem.id}-` : `quantity.${lineItem.id}-${inventoryItem.id}`;\n    form.setValue(key, value);\n    if (value) {\n      const location = inventoryItem.location_levels.find(\n        (l) => l.location_id === selectedLocationId\n      );\n      if (location) {\n        if (location.available_quantity < value) {\n          shouldDisableSubmit = true;\n        }\n      }\n    }\n    if (hasInventoryKit && !isRoot) {\n      form.resetField(`quantity.${lineItem.id}-`, { defaultValue: \"\" });\n    }\n    if (hasInventoryKit && isRoot) {\n      const item = itemsToAllocate.find((i) => i.id === lineItem.id);\n      item.variant?.inventory_items.forEach((ii, ind) => {\n        const num = value || 0;\n        const inventory = item.variant?.inventory[ind];\n        form.setValue(\n          `quantity.${lineItem.id}-${inventory.id}`,\n          num * ii.required_quantity\n        );\n        if (value) {\n          const location = inventory?.location_levels.find(\n            (l) => l.location_id === selectedLocationId\n          );\n          if (location) {\n            if (location.available_quantity < value) {\n              shouldDisableSubmit = true;\n            }\n          }\n        }\n      });\n    }\n    form.clearErrors(\"root.quantityNotAllocated\");\n    setDisableSubmit(shouldDisableSubmit);\n  };\n  const selectedLocationId = useWatch2({\n    name: \"location_id\",\n    control: form.control\n  });\n  useEffect(() => {\n    if (selectedLocationId) {\n      form.setValue(\"quantity\", defaultAllocations(itemsToAllocate));\n    }\n  }, [selectedLocationId]);\n  const allocationError = form.formState.errors?.root?.quantityNotAllocated?.message;\n  return /* @__PURE__ */ jsx2(RouteFocusModal.Form, { form, children: /* @__PURE__ */ jsxs2(\n    KeyboundForm,\n    {\n      onSubmit: handleSubmit,\n      className: \"flex h-full flex-col overflow-hidden\",\n      children: [\n        /* @__PURE__ */ jsx2(RouteFocusModal.Header, {}),\n        /* @__PURE__ */ jsx2(RouteFocusModal.Body, { className: \"flex h-full w-full flex-col items-center divide-y overflow-y-auto\", children: /* @__PURE__ */ jsx2(\"div\", { className: \"flex size-full flex-col items-center overflow-auto p-16\", children: /* @__PURE__ */ jsx2(\"div\", { className: \"flex w-full max-w-[736px] flex-col justify-center px-2 pb-2\", children: /* @__PURE__ */ jsxs2(\"div\", { className: \"flex flex-col gap-8 divide-y divide-dashed\", children: [\n          /* @__PURE__ */ jsx2(Heading, { children: t(\"orders.allocateItems.title\") }),\n          /* @__PURE__ */ jsxs2(\"div\", { className: \"flex-1 divide-y divide-dashed pt-8\", children: [\n            /* @__PURE__ */ jsx2(\n              Form.Field,\n              {\n                control: form.control,\n                name: \"location_id\",\n                render: ({ field: { onChange, ref, ...field } }) => {\n                  return /* @__PURE__ */ jsxs2(Form.Item, { children: [\n                    /* @__PURE__ */ jsxs2(\"div\", { className: \"flex items-center gap-3\", children: [\n                      /* @__PURE__ */ jsxs2(\"div\", { className: \"flex-1\", children: [\n                        /* @__PURE__ */ jsx2(Form.Label, { children: t(\"fields.location\") }),\n                        /* @__PURE__ */ jsx2(Form.Hint, { children: t(\"orders.allocateItems.locationDescription\") })\n                      ] }),\n                      /* @__PURE__ */ jsx2(\"div\", { className: \"flex-1\", children: /* @__PURE__ */ jsx2(Form.Control, { children: /* @__PURE__ */ jsxs2(Select, { onValueChange: onChange, ...field, children: [\n                        /* @__PURE__ */ jsx2(\n                          Select.Trigger,\n                          {\n                            className: \"bg-ui-bg-base\",\n                            ref,\n                            children: /* @__PURE__ */ jsx2(Select.Value, {})\n                          }\n                        ),\n                        /* @__PURE__ */ jsx2(Select.Content, { children: stock_locations.map((l) => /* @__PURE__ */ jsx2(Select.Item, { value: l.id, children: l.name }, l.id)) })\n                      ] }) }) })\n                    ] }),\n                    /* @__PURE__ */ jsx2(Form.ErrorMessage, {})\n                  ] });\n                }\n              }\n            ),\n            /* @__PURE__ */ jsxs2(Form.Item, { className: \"mt-8 pt-8\", children: [\n              /* @__PURE__ */ jsxs2(\"div\", { className: \"flex flex-row items-center\", children: [\n                /* @__PURE__ */ jsxs2(\"div\", { className: \"flex-1\", children: [\n                  /* @__PURE__ */ jsx2(Form.Label, { children: t(\"orders.allocateItems.itemsToAllocate\") }),\n                  /* @__PURE__ */ jsx2(Form.Hint, { children: t(\"orders.allocateItems.itemsToAllocateDesc\") })\n                ] }),\n                /* @__PURE__ */ jsx2(\"div\", { className: \"flex-1\", children: /* @__PURE__ */ jsx2(\n                  Input2,\n                  {\n                    value: filterTerm,\n                    onChange: (e) => setFilterTerm(e.target.value),\n                    placeholder: t(\"orders.allocateItems.search\"),\n                    autoComplete: \"off\",\n                    type: \"search\"\n                  }\n                ) })\n              ] }),\n              allocationError && /* @__PURE__ */ jsx2(Alert, { className: \"mb-4\", dismissible: true, variant: \"error\", children: allocationError }),\n              /* @__PURE__ */ jsx2(\"div\", { className: \"flex flex-col gap-y-1\", children: filteredItems.map((item) => /* @__PURE__ */ jsx2(\n                OrderAllocateItemsItem,\n                {\n                  form,\n                  item,\n                  locationId: selectedLocationId,\n                  onQuantityChange\n                },\n                item.id\n              )) })\n            ] })\n          ] })\n        ] }) }) }) }),\n        /* @__PURE__ */ jsx2(RouteFocusModal.Footer, { children: /* @__PURE__ */ jsxs2(\"div\", { className: \"flex items-center justify-end gap-x-2\", children: [\n          /* @__PURE__ */ jsx2(RouteFocusModal.Close, { asChild: true, children: /* @__PURE__ */ jsx2(Button, { size: \"small\", variant: \"secondary\", children: t(\"actions.cancel\") }) }),\n          /* @__PURE__ */ jsx2(\n            Button,\n            {\n              size: \"small\",\n              type: \"submit\",\n              isLoading: isMutating,\n              disabled: !selectedLocationId || disableSubmit,\n              children: t(\"orders.allocateItems.action\")\n            }\n          )\n        ] }) })\n      ]\n    }\n  ) });\n}\nfunction defaultAllocations(items) {\n  const ret = {};\n  items.forEach((item) => {\n    const hasInventoryKit = item.variant?.inventory_items.length > 1;\n    ret[hasInventoryKit ? `${item.id}-` : `${item.id}-${item.variant?.inventory[0].id}`] = \"\";\n    if (hasInventoryKit) {\n      item.variant?.inventory.forEach((i) => {\n        ret[`${item.id}-${i.id}`] = \"\";\n      });\n    }\n  });\n  return ret;\n}\n\n// src/routes/orders/order-allocate-items/order-allocate-items.tsx\nimport { jsx as jsx3 } from \"react/jsx-runtime\";\nfunction OrderAllocateItems() {\n  const { id } = useParams();\n  const { order, isLoading, isError, error } = useOrder(id, {\n    fields: \"currency_code,*items,*items.variant,+items.variant.product.title,*items.variant.inventory,*items.variant.inventory.location_levels,*items.variant.inventory_items,*shipping_address\"\n  });\n  if (isError) {\n    throw error;\n  }\n  const ready = !isLoading && order;\n  return /* @__PURE__ */ jsx3(RouteFocusModal, { children: ready && /* @__PURE__ */ jsx3(OrderAllocateItemsForm, { order }) });\n}\nexport {\n  OrderAllocateItems as Component\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwCA,mBAAsE;AAatE,IAAAA,gBAAkC;AASlC,yBAAoC;AA6OpC,IAAAC,sBAA2C;AAsN3C,IAAAA,sBAA4B;AAld5B,IAAI,sBAAsB,EAAE,OAAO;AAAA,EACjC,aAAa,EAAE,OAAO;AAAA,EACtB,UAAU,EAAE,OAAO,EAAE,OAAO,GAAG,EAAE,OAAO,EAAE,GAAG,EAAE,OAAO,CAAC,CAAC;AAC1D,CAAC;AAaD,SAAS,uBAAuB;AAAA,EAC9B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAAG;AApEH;AAqEE,QAAM,EAAE,GAAAC,GAAE,IAAI,eAAe;AAC7B,QAAM,UAAU,KAAK;AACrB,QAAM,cAAY,UAAK,YAAL,mBAAc,cAAa,CAAC;AAC9C,QAAM,CAAC,QAAQ,SAAS,QAAI,wBAAS,KAAK;AAC1C,QAAM,gBAAgB,SAAS;AAAA,IAC7B,SAAS,KAAK;AAAA,IACd,MAAM;AAAA,EACR,CAAC;AACD,QAAM,kBAAkB,CAAC,EAAC,mCAAS,gBAAgB,YAAU,mCAAS,gBAAgB,UAAS;AAC/F,QAAM,EAAE,mBAAmB,gBAAgB,QAAI,uBAAQ,MAAM;AA9E/D,QAAAC,KAAAC;AA+EI,QAAI,CAAC,WAAW,CAAC,YAAY;AAC3B,aAAO,CAAC;AAAA,IACV;AACA,UAAM,qBAAoBA,OAAAD,MAAA,UAAU,CAAC,MAAX,gBAAAA,IAAc,oBAAd,gBAAAC,IAA+B;AAAA,MACvD,CAAC,QAAQ,IAAI,gBAAgB;AAAA;AAE/B,QAAI,CAAC,mBAAmB;AACtB,aAAO,CAAC;AAAA,IACV;AACA,WAAO;AAAA,MACL,mBAAmB,kBAAkB;AAAA,MACrC,iBAAiB,kBAAkB;AAAA,IACrC;AAAA,EACF,GAAG,CAAC,SAAS,UAAU,CAAC;AACxB,QAAM,mBAAmB,CAAC,mBAAmB,qBAAqB,cAAc,GAAG,KAAK,EAAE,KAAI,UAAK,YAAL,mBAAc,UAAU,GAAG,EAAE,EAAE,KAAK,cAAc,GAAG,KAAK,EAAE,KAAI,UAAK,YAAL,mBAAc,UAAU,GAAG,EAAE,EAAE,IAAI;AACjM,QAAM,WAAW;AACjB,QAAM,WAAW,KAAK;AAAA,IACpB,uBAAuB,IAAI;AAAA,IAC3B,qBAAqB,OAAO;AAAA,EAC9B;AACA,aAAuB,yBAAK,OAAO,EAAE,WAAW,mGAAmG,UAAU;AAAA,QAC3I,yBAAK,OAAO,EAAE,WAAW,yCAAyC,UAAU;AAAA,UAC1E,wBAAI,OAAO,EAAE,WAAW,4BAA4B,cAA0B,yBAAK,OAAO,EAAE,WAAW,6BAA6B,UAAU;AAAA,QAC5J,wBAAoC,wBAAI,wBAAwB,EAAE,WAAW,mBAAmB,CAAC;AAAA,YACjF,wBAAI,WAAW,EAAE,KAAK,KAAK,UAAU,CAAC;AAAA,YACtC,yBAAK,OAAO,EAAE,WAAW,iBAAiB,UAAU;AAAA,cAClD,yBAAK,OAAO,EAAE,WAAW,iBAAiB,UAAU;AAAA,gBAClD,wBAAI,MAAM,EAAE,WAAW,kBAAkB,IAAI,QAAQ,QAAQ,QAAQ,UAAU,KAAK,cAAc,CAAC;AAAA,YACnH,KAAK,mBAA+B,yBAAK,QAAQ,EAAE,WAAW,qBAAqB,UAAU;AAAA,cAC3F;AAAA,cACA;AAAA,cACA,KAAK;AAAA,cACL;AAAA,YACF,EAAE,CAAC;AAAA,YACH,uBAAmC,wBAAI,WAAW,EAAE,WAAW,kDAAkD,CAAC;AAAA,UACpH,EAAE,CAAC;AAAA,cACa,wBAAI,MAAM,EAAE,IAAI,OAAO,WAAW,+BAA+B,UAAU,KAAK,MAAM,CAAC;AAAA,QACzG,EAAE,CAAC;AAAA,MACL,EAAE,CAAC,EAAE,CAAC;AAAA,UACU;AAAA,QACd;AAAA,QACA;AAAA,UACE,WAAW;AAAA,YACT;AAAA,YACA,kBAAkB,gBAAgB;AAAA,UACpC;AAAA,UACA,UAAU;AAAA,YACR,CAAC,uBAAmC,yBAAK,6BAAU,EAAE,UAAU;AAAA,kBAC7C,yBAAK,OAAO,EAAE,WAAW,2BAA2B,UAAU;AAAA,oBAC5D,wBAAI,OAAO,EAAE,WAAW,6CAA6C,CAAC;AAAA,oBACtE,yBAAK,OAAO,EAAE,WAAW,2BAA2B,UAAU;AAAA,sBAC5D,wBAAI,QAAQ,EAAE,WAAW,iCAAiC,UAAUF,GAAE,kBAAkB,EAAE,CAAC;AAAA,sBAC3F,yBAAK,QAAQ,EAAE,WAAW,oBAAoB,UAAU;AAAA,oBACtE,qBAAqB;AAAA,oBACrB,qBAAqB,CAAC,mBAAmB,cAAc,GAAG,KAAK,EAAE,KAAI,UAAK,YAAL,mBAAc,UAAU,GAAG,EAAE,EAAE,SAAqB,yBAAK,QAAQ,EAAE,WAAW,mCAAmC,UAAU;AAAA,sBAC9L;AAAA,sBACA,cAAc,GAAG,KAAK,EAAE,KAAI,UAAK,YAAL,mBAAc,UAAU,GAAG,EAAE,EAAE;AAAA,oBAC7D,EAAE,CAAC;AAAA,kBACL,EAAE,CAAC;AAAA,gBACL,EAAE,CAAC;AAAA,cACL,EAAE,CAAC;AAAA,kBACa,yBAAK,OAAO,EAAE,WAAW,2BAA2B,UAAU;AAAA,oBAC5D,wBAAI,OAAO,EAAE,WAAW,6CAA6C,CAAC;AAAA,oBACtE,yBAAK,OAAO,EAAE,WAAW,2BAA2B,UAAU;AAAA,sBAC5D,wBAAI,QAAQ,EAAE,WAAW,iCAAiC,UAAUA,GAAE,gBAAgB,EAAE,CAAC;AAAA,sBACzF,wBAAI,QAAQ,EAAE,WAAW,oBAAoB,UAAU,mBAAmB,IAAI,CAAC;AAAA,gBACjG,EAAE,CAAC;AAAA,cACL,EAAE,CAAC;AAAA,YACL,EAAE,CAAC;AAAA,gBACa,yBAAK,OAAO,EAAE,WAAW,2BAA2B,UAAU;AAAA,kBAC5D,wBAAI,OAAO,EAAE,WAAW,6CAA6C,CAAC;AAAA,kBACtE,yBAAK,OAAO,EAAE,WAAW,qEAAqE,UAAU;AAAA,oBACtG;AAAA,kBACd,KAAK;AAAA,kBACL;AAAA,oBACE,SAAS,KAAK;AAAA,oBACd,MAAM,kBAAkB,YAAY,KAAK,EAAE,MAAM,YAAY,KAAK,EAAE,KAAI,UAAK,YAAL,mBAAc,UAAU,GAAG,EAAE;AAAA,oBACrG,OAAO;AAAA,sBACL,UAAU,CAAC;AAAA,sBACX,KAAK,CAAC,mBAAmB;AAAA,sBACzB,KAAK;AAAA,oBACP;AAAA,oBACA,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,iCAAuB,wBAAI,KAAK,MAAM,EAAE,cAA0B,wBAAI,KAAK,SAAS,EAAE,cAA0B;AAAA,wBAC9G;AAAA,wBACA;AAAA,0BACE,WAAW;AAAA,0BACX,MAAM;AAAA,0BACN,GAAG;AAAA,0BACH,UAAU,CAAC;AAAA,0BACX,UAAU,CAAC,MAAM;AAzK3C,gCAAAC;AA0K4B,kCAAM,MAAM,EAAE,OAAO,UAAU,KAAK,OAAO,OAAO,EAAE,OAAO,KAAK;AAChE;AAAA,+BACEA,MAAA,KAAK,YAAL,gBAAAA,IAAc,UAAU;AAAA,8BACxB;AAAA,8BACA;AAAA,8BACA;AAAA,8BACA;AAAA,4BACF;AAAA,0BACF;AAAA,wBACF;AAAA,sBACF,EAAE,CAAC,EAAE,CAAC;AAAA,oBACR;AAAA,kBACF;AAAA,gBACF;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA,KAAK;AAAA,gBACL;AAAA,gBACAD,GAAE,YAAY;AAAA,cAChB,EAAE,CAAC;AAAA,YACL,EAAE,CAAC;AAAA,UACL;AAAA,QACF;AAAA,MACF;AAAA,IACF,EAAE,CAAC;AAAA,IACH,uBAAmC,wBAAI,OAAO,EAAE,WAAW,aAAa,cAA0B;AAAA,MAChG;AAAA,MACA;AAAA,QACE,SAAS,MAAM,UAAU,CAAC,MAAM,CAAC,CAAC;AAAA,QAClC,WAAW;AAAA,QACX,UAAU;AAAA,cACQ;AAAA,YACd;AAAA,YACA;AAAA,cACE,OAAO,EAAE,WAAW,UAAU,SAAS,MAAM,CAAC,OAAO;AAAA,cACrD,WAAW;AAAA,YACb;AAAA,UACF;AAAA,cACgB,wBAAI,QAAQ,EAAE,WAAW,6CAA6C,UAAUA,GAAE,mCAAmC;AAAA,YACnI,KAAK,UAAU;AAAA,UACjB,CAAC,EAAE,CAAC;AAAA,QACN;AAAA,MACF;AAAA,IACF,EAAE,CAAC;AAAA,IACH,UAAU,QAAQ,UAAU,IAAI,CAAC,GAAG,QAAQ;AAC1C,YAAM,WAAW,EAAE,gBAAgB;AAAA,QACjC,CAAC,MAAM,EAAE,gBAAgB;AAAA,MAC3B;AACA,YAAM,oBAAoB,CAAC,CAAC,cAAc,GAAG,KAAK,EAAE,IAAI,EAAE,EAAE,EAAE,KAAK,cAAc,GAAG,KAAK,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,SAAS;AAClH,iBAAuB,yBAAK,OAAO,EAAE,WAAW,2CAA2C,UAAU;AAAA,YACnF,yBAAK,OAAO,EAAE,WAAW,2CAA2C,UAAU;AAAA,UAC5F,yBAAqC,wBAAI,wBAAwB,EAAE,WAAW,mBAAmB,CAAC;AAAA,cAClF,yBAAK,OAAO,EAAE,WAAW,iBAAiB,UAAU;AAAA,gBAClD,wBAAI,QAAQ,EAAE,WAAW,qBAAqB,UAAU,EAAE,MAAM,CAAC;AAAA,gBACjE,wBAAI,QAAQ,EAAE,WAAW,oBAAoB,UAAUA,GAAE,iCAAiC;AAAA,cACxG,KAAK,QAAQ,gBAAgB,GAAG,EAAE;AAAA,YACpC,CAAC,EAAE,CAAC;AAAA,UACN,EAAE,CAAC;AAAA,QACL,EAAE,CAAC;AAAA,YACa,yBAAK,OAAO,EAAE,WAAW,wCAAwC,UAAU;AAAA,cACzE,yBAAK,OAAO,EAAE,WAAW,2BAA2B,UAAU;AAAA,gBAC5D,wBAAI,OAAO,EAAE,WAAW,6CAA6C,CAAC;AAAA,gBACtE,yBAAK,OAAO,EAAE,WAAW,2BAA2B,UAAU;AAAA,kBAC5D,wBAAI,QAAQ,EAAE,WAAW,iCAAiC,UAAUA,GAAE,kBAAkB,EAAE,CAAC;AAAA,kBAC3F,yBAAK,QAAQ,EAAE,WAAW,oBAAoB,UAAU;AAAA,iBACtE,qCAAU,uBAAsB;AAAA,iBAChC,qCAAU,uBAAsB,cAAc,GAAG,KAAK,EAAE,IAAI,EAAE,EAAE,EAAE,SAAqB,yBAAK,QAAQ,EAAE,WAAW,mCAAmC,UAAU;AAAA,kBAC5J;AAAA,kBACA,cAAc,GAAG,KAAK,EAAE,IAAI,EAAE,EAAE,EAAE;AAAA,gBACpC,EAAE,CAAC;AAAA,cACL,EAAE,CAAC;AAAA,YACL,EAAE,CAAC;AAAA,UACL,EAAE,CAAC;AAAA,cACa,yBAAK,OAAO,EAAE,WAAW,2BAA2B,UAAU;AAAA,gBAC5D,wBAAI,OAAO,EAAE,WAAW,6CAA6C,CAAC;AAAA,gBACtE,yBAAK,OAAO,EAAE,WAAW,2BAA2B,UAAU;AAAA,kBAC5D,wBAAI,QAAQ,EAAE,WAAW,iCAAiC,UAAUA,GAAE,gBAAgB,EAAE,CAAC;AAAA,kBACzF,wBAAI,QAAQ,EAAE,WAAW,oBAAoB,WAAU,qCAAU,qBAAoB,IAAI,CAAC;AAAA,YAC5G,EAAE,CAAC;AAAA,UACL,EAAE,CAAC;AAAA,cACa,yBAAK,OAAO,EAAE,WAAW,2BAA2B,UAAU;AAAA,gBAC5D,wBAAI,OAAO,EAAE,WAAW,6CAA6C,CAAC;AAAA,gBACtE,yBAAK,OAAO,EAAE,WAAW,qEAAqE,UAAU;AAAA,kBACtG;AAAA,gBACd,KAAK;AAAA,gBACL;AAAA,kBACE,SAAS,KAAK;AAAA,kBACd,MAAM,YAAY,KAAK,EAAE,IAAI,EAAE,EAAE;AAAA,kBACjC,OAAO;AAAA,oBACL,UAAU;AAAA,oBACV,KAAK;AAAA,oBACL,KAAK,qCAAU;AAAA,kBACjB;AAAA,kBACA,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,+BAAuB,wBAAI,KAAK,MAAM,EAAE,cAA0B,wBAAI,KAAK,SAAS,EAAE,cAA0B;AAAA,sBAC9G;AAAA,sBACA;AAAA,wBACE,WAAW;AAAA,wBACX,MAAM;AAAA,wBACN,GAAG;AAAA,wBACH,UAAU,CAAC;AAAA,wBACX,UAAU,CAAC,MAAM;AACf,gCAAM,MAAM,EAAE,OAAO,UAAU,KAAK,OAAO,OAAO,EAAE,OAAO,KAAK;AAChE;AAAA,4BACE;AAAA,4BACA;AAAA,4BACA;AAAA,4BACA;AAAA,0BACF;AAAA,wBACF;AAAA,sBACF;AAAA,oBACF,EAAE,CAAC,EAAE,CAAC;AAAA,kBACR;AAAA,gBACF;AAAA,cACF;AAAA,cACA;AAAA,cACA;AAAA,cACA,KAAK,WAAW,QAAQ,gBAAgB,GAAG,EAAE;AAAA,cAC7C;AAAA,cACAA,GAAE,YAAY;AAAA,YAChB,EAAE,CAAC;AAAA,UACL,EAAE,CAAC;AAAA,QACL,EAAE,CAAC;AAAA,MACL,EAAE,GAAG,EAAE,EAAE;AAAA,IACX,CAAC;AAAA,EACH,EAAE,CAAC;AACL;AAIA,SAAS,uBAAuB,EAAE,MAAM,GAAG;AA5S3C;AA6SE,QAAM,EAAE,GAAAA,GAAE,IAAI,eAAgB;AAC9B,QAAM,EAAE,cAAc,IAAI,cAAc;AACxC,QAAM,CAAC,eAAe,gBAAgB,QAAI,aAAAG,UAAU,KAAK;AACzD,QAAM,CAAC,YAAY,aAAa,QAAI,aAAAA,UAAU,EAAE;AAChD,QAAM,EAAE,aAAa,eAAe,WAAW,WAAW,IAAI,yBAAyB;AACvF,QAAM,sBAAkB,aAAAC;AAAA,IACtB,MAAM,MAAM,MAAM;AAAA,MAChB,CAAC,SAAM;AApTb,YAAAH,KAAAC;AAoTgB,iBAAAD,MAAA,KAAK,YAAL,gBAAAA,IAAc,uBAAoBC,MAAA,KAAK,YAAL,gBAAAA,IAAc,UAAU,WAAU,KAAK,WAAW,KAAK,OAAO,qBAAqB;AAAA;AAAA,IACjI;AAAA,IACA,CAAC,MAAM,KAAK;AAAA,EACd;AACA,QAAM,oBAAgB,aAAAE,SAAS,MAAM;AACnC,WAAO,gBAAgB;AAAA,MACrB,CAAC,MAAM,EAAE,cAAc,YAAY,EAAE,SAAS,UAAU,KAAK,EAAE,cAAc,YAAY,EAAE,SAAS,UAAU;AAAA,IAChH;AAAA,EACF,GAAG,CAAC,iBAAiB,UAAU,CAAC;AAChC,QAAM,oBAAoB,CAAC,gBAAgB;AAC3C,QAAM,OAAO,QAAQ;AAAA,IACnB,eAAe;AAAA,MACb,aAAa;AAAA,MACb,UAAU,mBAAmB,eAAe;AAAA,IAC9C;AAAA,IACA,UAAU,EAAY,mBAAmB;AAAA,EAC3C,CAAC;AACD,QAAM,EAAE,kBAAkB,CAAC,EAAE,IAAI,kBAAkB;AACnD,QAAM,eAAe,KAAK,aAAa,OAAO,SAAS;AACrD,QAAI;AACF,YAAM,UAAU,OAAO,QAAQ,KAAK,QAAQ,EAAE,OAAO,CAAC,CAAC,GAAG,MAAM,CAAC,IAAI,SAAS,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC,KAAK,QAAQ,MAAM,CAAC,GAAG,IAAI,MAAM,GAAG,GAAG,QAAQ,CAAC;AAC1I,UAAI,QAAQ,KAAK,CAAC,MAAM,EAAE,CAAC,MAAM,EAAE,GAAG;AACpC,aAAK,SAAS,6BAA6B;AAAA,UACzC,MAAM;AAAA,UACN,SAASJ,GAAE,iDAAiD;AAAA,QAC9D,CAAC;AACD;AAAA,MACF;AACA,YAAM,WAAW,QAAQ;AAAA,QACvB,CAAC,CAAC,QAAQ,aAAa,QAAQ,MAAM,cAAc;AAAA,UACjD,aAAa,KAAK;AAAA,UAClB,mBAAmB;AAAA,UACnB,cAAc;AAAA,UACd;AAAA,QACF,CAAC;AAAA,MACH;AACA,YAAM,QAAQ,IAAI,QAAQ;AAC1B,YAAM,YAAY,kBAAkB;AAAA,QAClC,UAAU,gBAAgB,QAAQ;AAAA,MACpC,CAAC;AACD,oBAAc,WAAW,MAAM,EAAE,EAAE;AACnC,YAAM,QAAQA,GAAE,iBAAiB,GAAG;AAAA,QAClC,aAAaA,GAAE,oCAAoC;AAAA,QACnD,cAAcA,GAAE,eAAe;AAAA,MACjC,CAAC;AAAA,IACH,SAAS,GAAG;AACV,YAAM,MAAMA,GAAE,eAAe,GAAG;AAAA,QAC9B,aAAa,EAAE;AAAA,QACf,cAAcA,GAAE,eAAe;AAAA,MACjC,CAAC;AAAA,IACH;AAAA,EACF,CAAC;AACD,QAAM,mBAAmB,CAAC,eAAe,UAAU,iBAAiB,OAAO,WAAW;AAxWxF,QAAAC;AAyWI,QAAI,sBAAsB;AAC1B,UAAM,MAAM,UAAU,kBAAkB,YAAY,SAAS,EAAE,MAAM,YAAY,SAAS,EAAE,IAAI,cAAc,EAAE;AAChH,SAAK,SAAS,KAAK,KAAK;AACxB,QAAI,OAAO;AACT,YAAM,WAAW,cAAc,gBAAgB;AAAA,QAC7C,CAAC,MAAM,EAAE,gBAAgB;AAAA,MAC3B;AACA,UAAI,UAAU;AACZ,YAAI,SAAS,qBAAqB,OAAO;AACvC,gCAAsB;AAAA,QACxB;AAAA,MACF;AAAA,IACF;AACA,QAAI,mBAAmB,CAAC,QAAQ;AAC9B,WAAK,WAAW,YAAY,SAAS,EAAE,KAAK,EAAE,cAAc,GAAG,CAAC;AAAA,IAClE;AACA,QAAI,mBAAmB,QAAQ;AAC7B,YAAM,OAAO,gBAAgB,KAAK,CAAC,MAAM,EAAE,OAAO,SAAS,EAAE;AAC7D,OAAAA,MAAA,KAAK,YAAL,gBAAAA,IAAc,gBAAgB,QAAQ,CAAC,IAAI,QAAQ;AA3XzD,YAAAA;AA4XQ,cAAM,MAAM,SAAS;AACrB,cAAM,aAAYA,MAAA,KAAK,YAAL,gBAAAA,IAAc,UAAU;AAC1C,aAAK;AAAA,UACH,YAAY,SAAS,EAAE,IAAI,UAAU,EAAE;AAAA,UACvC,MAAM,GAAG;AAAA,QACX;AACA,YAAI,OAAO;AACT,gBAAM,WAAW,uCAAW,gBAAgB;AAAA,YAC1C,CAAC,MAAM,EAAE,gBAAgB;AAAA;AAE3B,cAAI,UAAU;AACZ,gBAAI,SAAS,qBAAqB,OAAO;AACvC,oCAAsB;AAAA,YACxB;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,SAAK,YAAY,2BAA2B;AAC5C,qBAAiB,mBAAmB;AAAA,EACtC;AACA,QAAM,qBAAqB,SAAU;AAAA,IACnC,MAAM;AAAA,IACN,SAAS,KAAK;AAAA,EAChB,CAAC;AACD,8BAAU,MAAM;AACd,QAAI,oBAAoB;AACtB,WAAK,SAAS,YAAY,mBAAmB,eAAe,CAAC;AAAA,IAC/D;AAAA,EACF,GAAG,CAAC,kBAAkB,CAAC;AACvB,QAAM,mBAAkB,sBAAK,UAAU,WAAf,mBAAuB,SAAvB,mBAA6B,yBAA7B,mBAAmD;AAC3E,aAAuB,oBAAAI,KAAK,gBAAgB,MAAM,EAAE,MAAM,cAA0B,oBAAAC;AAAA,IAClF;AAAA,IACA;AAAA,MACE,UAAU;AAAA,MACV,WAAW;AAAA,MACX,UAAU;AAAA,YACQ,oBAAAD,KAAK,gBAAgB,QAAQ,CAAC,CAAC;AAAA,YAC/B,oBAAAA,KAAK,gBAAgB,MAAM,EAAE,WAAW,qEAAqE,cAA0B,oBAAAA,KAAK,OAAO,EAAE,WAAW,2DAA2D,cAA0B,oBAAAA,KAAK,OAAO,EAAE,WAAW,+DAA+D,cAA0B,oBAAAC,MAAM,OAAO,EAAE,WAAW,8CAA8C,UAAU;AAAA,cACvb,oBAAAD,KAAK,SAAS,EAAE,UAAUL,GAAE,4BAA4B,EAAE,CAAC;AAAA,cAC3D,oBAAAM,MAAM,OAAO,EAAE,WAAW,sCAAsC,UAAU;AAAA,gBACxE,oBAAAD;AAAA,cACd,KAAK;AAAA,cACL;AAAA,gBACE,SAAS,KAAK;AAAA,gBACd,MAAM;AAAA,gBACN,QAAQ,CAAC,EAAE,OAAO,EAAE,UAAU,KAAK,GAAG,MAAM,EAAE,MAAM;AAClD,6BAAuB,oBAAAC,MAAM,KAAK,MAAM,EAAE,UAAU;AAAA,wBAClC,oBAAAA,MAAM,OAAO,EAAE,WAAW,2BAA2B,UAAU;AAAA,0BAC7D,oBAAAA,MAAM,OAAO,EAAE,WAAW,UAAU,UAAU;AAAA,4BAC5C,oBAAAD,KAAK,KAAK,OAAO,EAAE,UAAUL,GAAE,iBAAiB,EAAE,CAAC;AAAA,4BACnD,oBAAAK,KAAK,KAAK,MAAM,EAAE,UAAUL,GAAE,0CAA0C,EAAE,CAAC;AAAA,sBAC7F,EAAE,CAAC;AAAA,0BACa,oBAAAK,KAAK,OAAO,EAAE,WAAW,UAAU,cAA0B,oBAAAA,KAAK,KAAK,SAAS,EAAE,cAA0B,oBAAAC,MAAM,QAAQ,EAAE,eAAe,UAAU,GAAG,OAAO,UAAU;AAAA,4BACvK,oBAAAD;AAAA,0BACd,OAAO;AAAA,0BACP;AAAA,4BACE,WAAW;AAAA,4BACX;AAAA,4BACA,cAA0B,oBAAAA,KAAK,OAAO,OAAO,CAAC,CAAC;AAAA,0BACjD;AAAA,wBACF;AAAA,4BACgB,oBAAAA,KAAK,OAAO,SAAS,EAAE,UAAU,gBAAgB,IAAI,CAAC,UAAsB,oBAAAA,KAAK,OAAO,MAAM,EAAE,OAAO,EAAE,IAAI,UAAU,EAAE,KAAK,GAAG,EAAE,EAAE,CAAC,EAAE,CAAC;AAAA,sBAC3J,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;AAAA,oBACX,EAAE,CAAC;AAAA,wBACa,oBAAAA,KAAK,KAAK,cAAc,CAAC,CAAC;AAAA,kBAC5C,EAAE,CAAC;AAAA,gBACL;AAAA,cACF;AAAA,YACF;AAAA,gBACgB,oBAAAC,MAAM,KAAK,MAAM,EAAE,WAAW,aAAa,UAAU;AAAA,kBACnD,oBAAAA,MAAM,OAAO,EAAE,WAAW,8BAA8B,UAAU;AAAA,oBAChE,oBAAAA,MAAM,OAAO,EAAE,WAAW,UAAU,UAAU;AAAA,sBAC5C,oBAAAD,KAAK,KAAK,OAAO,EAAE,UAAUL,GAAE,sCAAsC,EAAE,CAAC;AAAA,sBACxE,oBAAAK,KAAK,KAAK,MAAM,EAAE,UAAUL,GAAE,0CAA0C,EAAE,CAAC;AAAA,gBAC7F,EAAE,CAAC;AAAA,oBACa,oBAAAK,KAAK,OAAO,EAAE,WAAW,UAAU,cAA0B,oBAAAA;AAAA,kBAC3E;AAAA,kBACA;AAAA,oBACE,OAAO;AAAA,oBACP,UAAU,CAAC,MAAM,cAAc,EAAE,OAAO,KAAK;AAAA,oBAC7C,aAAaL,GAAE,6BAA6B;AAAA,oBAC5C,cAAc;AAAA,oBACd,MAAM;AAAA,kBACR;AAAA,gBACF,EAAE,CAAC;AAAA,cACL,EAAE,CAAC;AAAA,cACH,uBAAmC,oBAAAK,KAAK,OAAO,EAAE,WAAW,QAAQ,aAAa,MAAM,SAAS,SAAS,UAAU,gBAAgB,CAAC;AAAA,kBACpH,oBAAAA,KAAK,OAAO,EAAE,WAAW,yBAAyB,UAAU,cAAc,IAAI,CAAC,aAAyB,oBAAAA;AAAA,gBACtH;AAAA,gBACA;AAAA,kBACE;AAAA,kBACA;AAAA,kBACA,YAAY;AAAA,kBACZ;AAAA,gBACF;AAAA,gBACA,KAAK;AAAA,cACP,CAAC,EAAE,CAAC;AAAA,YACN,EAAE,CAAC;AAAA,UACL,EAAE,CAAC;AAAA,QACL,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;AAAA,YACI,oBAAAA,KAAK,gBAAgB,QAAQ,EAAE,cAA0B,oBAAAC,MAAM,OAAO,EAAE,WAAW,yCAAyC,UAAU;AAAA,cACpI,oBAAAD,KAAK,gBAAgB,OAAO,EAAE,SAAS,MAAM,cAA0B,oBAAAA,KAAK,QAAQ,EAAE,MAAM,SAAS,SAAS,aAAa,UAAUL,GAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;AAAA,cAC7J,oBAAAK;AAAA,YACd;AAAA,YACA;AAAA,cACE,MAAM;AAAA,cACN,MAAM;AAAA,cACN,WAAW;AAAA,cACX,UAAU,CAAC,sBAAsB;AAAA,cACjC,UAAUL,GAAE,6BAA6B;AAAA,YAC3C;AAAA,UACF;AAAA,QACF,EAAE,CAAC,EAAE,CAAC;AAAA,MACR;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;AACA,SAAS,mBAAmB,OAAO;AACjC,QAAM,MAAM,CAAC;AACb,QAAM,QAAQ,CAAC,SAAS;AApf1B;AAqfI,UAAM,oBAAkB,UAAK,YAAL,mBAAc,gBAAgB,UAAS;AAC/D,QAAI,kBAAkB,GAAG,KAAK,EAAE,MAAM,GAAG,KAAK,EAAE,KAAI,UAAK,YAAL,mBAAc,UAAU,GAAG,EAAE,EAAE,IAAI;AACvF,QAAI,iBAAiB;AACnB,iBAAK,YAAL,mBAAc,UAAU,QAAQ,CAAC,MAAM;AACrC,YAAI,GAAG,KAAK,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI;AAAA,MAC9B;AAAA,IACF;AAAA,EACF,CAAC;AACD,SAAO;AACT;AAIA,SAAS,qBAAqB;AAC5B,QAAM,EAAE,GAAG,IAAI,UAAU;AACzB,QAAM,EAAE,OAAO,WAAW,SAAS,MAAM,IAAI,SAAS,IAAI;AAAA,IACxD,QAAQ;AAAA,EACV,CAAC;AACD,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,QAAM,QAAQ,CAAC,aAAa;AAC5B,aAAuB,oBAAAO,KAAK,iBAAiB,EAAE,UAAU,aAAyB,oBAAAA,KAAK,wBAAwB,EAAE,MAAM,CAAC,EAAE,CAAC;AAC7H;", "names": ["import_react", "import_jsx_runtime", "t", "_a", "_b", "useState2", "useMemo2", "jsx2", "jsxs2", "jsx3"]}