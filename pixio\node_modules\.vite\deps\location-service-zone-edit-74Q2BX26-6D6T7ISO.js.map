{"version": 3, "sources": ["../../@medusajs/dashboard/dist/location-service-zone-edit-74Q2BX26.mjs"], "sourcesContent": ["import \"./chunk-IUCDCPJU.mjs\";\nimport {\n  KeyboundForm\n} from \"./chunk-6HTZNHPT.mjs\";\nimport {\n  RouteDrawer,\n  useRouteModal\n} from \"./chunk-4TC5YS65.mjs\";\nimport {\n  Form\n} from \"./chunk-OBQI23QM.mjs\";\nimport {\n  useUpdateFulfillmentSetServiceZone\n} from \"./chunk-ENV6YVOM.mjs\";\nimport \"./chunk-GRT22PE5.mjs\";\nimport {\n  useStockLocation\n} from \"./chunk-32IQRUVY.mjs\";\nimport \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-RPUOO7AV.mjs\";\n\n// src/routes/locations/location-service-zone-edit/location-service-zone-edit.tsx\nimport { Heading } from \"@medusajs/ui\";\nimport { useTranslation as useTranslation2 } from \"react-i18next\";\nimport { json, useParams } from \"react-router-dom\";\n\n// src/routes/locations/location-service-zone-edit/components/edit-region-form/edit-service-zone-form.tsx\nimport { Button, InlineTip, Input, toast } from \"@medusajs/ui\";\nimport { useForm } from \"react-hook-form\";\nimport { useTranslation } from \"react-i18next\";\nimport * as zod from \"zod\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar EditServiceZoneSchema = zod.object({\n  name: zod.string().min(1)\n});\nvar EditServiceZoneForm = ({\n  zone,\n  fulfillmentSetId,\n  locationId\n}) => {\n  const { t } = useTranslation();\n  const { handleSuccess } = useRouteModal();\n  const form = useForm({\n    defaultValues: {\n      name: zone.name\n    }\n  });\n  const { mutateAsync, isPending: isLoading } = useUpdateFulfillmentSetServiceZone(fulfillmentSetId, zone.id);\n  const handleSubmit = form.handleSubmit(async (values) => {\n    await mutateAsync(\n      {\n        name: values.name\n      },\n      {\n        onSuccess: () => {\n          toast.success(\n            t(\"stockLocations.serviceZones.edit.successToast\", {\n              name: values.name\n            })\n          );\n          handleSuccess(`/settings/locations/${locationId}`);\n        },\n        onError: (e) => {\n          toast.error(e.message);\n        }\n      }\n    );\n  });\n  return /* @__PURE__ */ jsx(RouteDrawer.Form, { form, children: /* @__PURE__ */ jsxs(\n    KeyboundForm,\n    {\n      onSubmit: handleSubmit,\n      className: \"flex size-full flex-col overflow-hidden\",\n      children: [\n        /* @__PURE__ */ jsx(RouteDrawer.Body, { className: \"flex-1 overflow-auto\", children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex flex-col gap-y-8\", children: [\n          /* @__PURE__ */ jsx(\"div\", { className: \"flex flex-col gap-y-4\", children: /* @__PURE__ */ jsx(\n            Form.Field,\n            {\n              control: form.control,\n              name: \"name\",\n              render: ({ field }) => {\n                return /* @__PURE__ */ jsxs(Form.Item, { children: [\n                  /* @__PURE__ */ jsx(Form.Label, { children: t(\"fields.name\") }),\n                  /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(Input, { ...field }) }),\n                  /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n                ] });\n              }\n            }\n          ) }),\n          /* @__PURE__ */ jsx(InlineTip, { label: t(\"general.tip\"), children: t(\"stockLocations.serviceZones.fields.tip\") })\n        ] }) }),\n        /* @__PURE__ */ jsx(RouteDrawer.Footer, { children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center gap-x-2\", children: [\n          /* @__PURE__ */ jsx(RouteDrawer.Close, { asChild: true, children: /* @__PURE__ */ jsx(Button, { size: \"small\", variant: \"secondary\", children: t(\"actions.cancel\") }) }),\n          /* @__PURE__ */ jsx(Button, { size: \"small\", type: \"submit\", isLoading, children: t(\"actions.save\") })\n        ] }) })\n      ]\n    }\n  ) });\n};\n\n// src/routes/locations/location-service-zone-edit/location-service-zone-edit.tsx\nimport { jsx as jsx2, jsxs as jsxs2 } from \"react/jsx-runtime\";\nvar LocationServiceZoneEdit = () => {\n  const { t } = useTranslation2();\n  const { location_id, fset_id, zone_id } = useParams();\n  const { stock_location, isPending, isFetching, isError, error } = useStockLocation(location_id, {\n    fields: \"*fulfillment_sets.service_zones\"\n  });\n  const serviceZone = stock_location?.fulfillment_sets?.find((f) => f.id === fset_id)?.service_zones.find((z) => z.id === zone_id);\n  if (!isPending && !isFetching && !serviceZone) {\n    throw json(\n      { message: `Service zone with ID ${zone_id} was not found` },\n      404\n    );\n  }\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsxs2(RouteDrawer, { prev: `/settings/locations/${location_id}`, children: [\n    /* @__PURE__ */ jsx2(RouteDrawer.Header, { children: /* @__PURE__ */ jsx2(Heading, { children: t(\"stockLocations.serviceZones.edit.header\") }) }),\n    serviceZone && /* @__PURE__ */ jsx2(\n      EditServiceZoneForm,\n      {\n        zone: serviceZone,\n        fulfillmentSetId: fset_id,\n        locationId: location_id\n      }\n    )\n  ] });\n};\nexport {\n  LocationServiceZoneEdit as Component\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiCA,yBAA0B;AAsE1B,IAAAA,sBAA2C;AArE3C,IAAI,wBAA4B,WAAO;AAAA,EACrC,MAAU,WAAO,EAAE,IAAI,CAAC;AAC1B,CAAC;AACD,IAAI,sBAAsB,CAAC;AAAA,EACzB;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACJ,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,QAAM,EAAE,cAAc,IAAI,cAAc;AACxC,QAAM,OAAO,QAAQ;AAAA,IACnB,eAAe;AAAA,MACb,MAAM,KAAK;AAAA,IACb;AAAA,EACF,CAAC;AACD,QAAM,EAAE,aAAa,WAAW,UAAU,IAAI,mCAAmC,kBAAkB,KAAK,EAAE;AAC1G,QAAM,eAAe,KAAK,aAAa,OAAO,WAAW;AACvD,UAAM;AAAA,MACJ;AAAA,QACE,MAAM,OAAO;AAAA,MACf;AAAA,MACA;AAAA,QACE,WAAW,MAAM;AACf,gBAAM;AAAA,YACJ,EAAE,iDAAiD;AAAA,cACjD,MAAM,OAAO;AAAA,YACf,CAAC;AAAA,UACH;AACA,wBAAc,uBAAuB,UAAU,EAAE;AAAA,QACnD;AAAA,QACA,SAAS,CAAC,MAAM;AACd,gBAAM,MAAM,EAAE,OAAO;AAAA,QACvB;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AACD,aAAuB,wBAAI,YAAY,MAAM,EAAE,MAAM,cAA0B;AAAA,IAC7E;AAAA,IACA;AAAA,MACE,UAAU;AAAA,MACV,WAAW;AAAA,MACX,UAAU;AAAA,YACQ,wBAAI,YAAY,MAAM,EAAE,WAAW,wBAAwB,cAA0B,yBAAK,OAAO,EAAE,WAAW,yBAAyB,UAAU;AAAA,cAC/I,wBAAI,OAAO,EAAE,WAAW,yBAAyB,cAA0B;AAAA,YACzF,KAAK;AAAA,YACL;AAAA,cACE,SAAS,KAAK;AAAA,cACd,MAAM;AAAA,cACN,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,2BAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,sBACjC,wBAAI,KAAK,OAAO,EAAE,UAAU,EAAE,aAAa,EAAE,CAAC;AAAA,sBAC9C,wBAAI,KAAK,SAAS,EAAE,cAA0B,wBAAI,OAAO,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC;AAAA,sBACxE,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,gBAC3C,EAAE,CAAC;AAAA,cACL;AAAA,YACF;AAAA,UACF,EAAE,CAAC;AAAA,cACa,wBAAI,WAAW,EAAE,OAAO,EAAE,aAAa,GAAG,UAAU,EAAE,wCAAwC,EAAE,CAAC;AAAA,QACnH,EAAE,CAAC,EAAE,CAAC;AAAA,YACU,wBAAI,YAAY,QAAQ,EAAE,cAA0B,yBAAK,OAAO,EAAE,WAAW,6BAA6B,UAAU;AAAA,cAClH,wBAAI,YAAY,OAAO,EAAE,SAAS,MAAM,cAA0B,wBAAI,QAAQ,EAAE,MAAM,SAAS,SAAS,aAAa,UAAU,EAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;AAAA,cACvJ,wBAAI,QAAQ,EAAE,MAAM,SAAS,MAAM,UAAU,WAAW,UAAU,EAAE,cAAc,EAAE,CAAC;AAAA,QACvG,EAAE,CAAC,EAAE,CAAC;AAAA,MACR;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;AAIA,IAAI,0BAA0B,MAAM;AAxGpC;AAyGE,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,QAAM,EAAE,aAAa,SAAS,QAAQ,IAAI,UAAU;AACpD,QAAM,EAAE,gBAAgB,WAAW,YAAY,SAAS,MAAM,IAAI,iBAAiB,aAAa;AAAA,IAC9F,QAAQ;AAAA,EACV,CAAC;AACD,QAAM,eAAc,4DAAgB,qBAAhB,mBAAkC,KAAK,CAAC,MAAM,EAAE,OAAO,aAAvD,mBAAiE,cAAc,KAAK,CAAC,MAAM,EAAE,OAAO;AACxH,MAAI,CAAC,aAAa,CAAC,cAAc,CAAC,aAAa;AAC7C,UAAM;AAAA,MACJ,EAAE,SAAS,wBAAwB,OAAO,iBAAiB;AAAA,MAC3D;AAAA,IACF;AAAA,EACF;AACA,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,oBAAAC,MAAM,aAAa,EAAE,MAAM,uBAAuB,WAAW,IAAI,UAAU;AAAA,QAChF,oBAAAC,KAAK,YAAY,QAAQ,EAAE,cAA0B,oBAAAA,KAAK,SAAS,EAAE,UAAU,EAAE,yCAAyC,EAAE,CAAC,EAAE,CAAC;AAAA,IAChJ,mBAA+B,oBAAAA;AAAA,MAC7B;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,kBAAkB;AAAA,QAClB,YAAY;AAAA,MACd;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;", "names": ["import_jsx_runtime", "jsxs2", "jsx2"]}