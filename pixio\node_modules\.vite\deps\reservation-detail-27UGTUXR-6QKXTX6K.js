import {
  InventoryItemGeneralSection
} from "./chunk-7CRCP65D.js";
import "./chunk-EGRHWZRV.js";
import {
  SectionRow
} from "./chunk-A5MCDQUY.js";
import {
  TwoColumnPage
} from "./chunk-3LNIL4XX.js";
import {
  useExtension
} from "./chunk-FTD3ZWHZ.js";
import {
  TwoColumnPageSkeleton
} from "./chunk-YXT43UJF.js";
import {
  ActionMenu
} from "./chunk-CFRQOB2M.js";
import "./chunk-66DVUN72.js";
import "./chunk-WISME5HP.js";
import "./chunk-WAUSOHFQ.js";
import "./chunk-3ORK3QZ4.js";
import "./chunk-3A5TVVNI.js";
import "./chunk-EZ62MM7J.js";
import "./chunk-3R6LYIXM.js";
import "./chunk-2P2O4TMQ.js";
import "./chunk-KI3HEITX.js";
import "./chunk-2VUCAO7L.js";
import "./chunk-PTIULTKD.js";
import "./chunk-KISXY2LD.js";
import "./chunk-L7C3XPHJ.js";
import "./chunk-T7MG2EIJ.js";
import "./chunk-MER4VYPR.js";
import "./chunk-2AWKOUCD.js";
import "./chunk-YL3TYBFR.js";
import "./chunk-TZWW72YW.js";
import "./chunk-MSQ25CWB.js";
import {
  useStockLocation
} from "./chunk-ONYSAQ5Z.js";
import "./chunk-NIH7SAXN.js";
import {
  reservationItemsQueryKeys,
  useReservationItem
} from "./chunk-UEJFWAFE.js";
import "./chunk-GR55MFKT.js";
import "./chunk-AAFHKNJG.js";
import "./chunk-7XDBFDTZ.js";
import "./chunk-3JKGO5XL.js";
import "./chunk-OISUTS7G.js";
import {
  useInventoryItem
} from "./chunk-AJYMIHLQ.js";
import {
  queryClient
} from "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import {
  sdk
} from "./chunk-JFCIYOH4.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-5GF3RGIE.js";
import {
  useLoaderData,
  useParams
} from "./chunk-T7YBVUWZ.js";
import {
  Container,
  Heading,
  PencilSquare
} from "./chunk-YP2LLXWB.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-ZSQ362B4.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/reservation-detail-27UGTUXR.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var ReservationDetailBreadcrumb = (props) => {
  var _a, _b;
  const { id } = props.params || {};
  const { reservation } = useReservationItem(id, void 0, {
    initialData: props.data,
    enabled: Boolean(id)
  });
  if (!reservation) {
    return null;
  }
  const display = ((_a = reservation == null ? void 0 : reservation.inventory_item) == null ? void 0 : _a.title) ?? ((_b = reservation == null ? void 0 : reservation.inventory_item) == null ? void 0 : _b.sku) ?? reservation.id;
  return (0, import_jsx_runtime.jsx)("span", { children: display });
};
var reservationDetailQuery = (id) => ({
  queryKey: reservationItemsQueryKeys.detail(id),
  queryFn: async () => sdk.admin.reservation.retrieve(id)
});
var reservationItemLoader = async ({ params }) => {
  const id = params.id;
  const query = reservationDetailQuery(id);
  return queryClient.ensureQueryData(query);
};
var ReservationGeneralSection = ({
  reservation
}) => {
  const { t } = useTranslation();
  const { inventory_item: inventoryItem, isPending: isLoadingInventoryItem } = useInventoryItem(reservation.inventory_item_id);
  const { stock_location: location, isPending: isLoadingLocation } = useStockLocation(reservation.location_id);
  if (isLoadingInventoryItem || !inventoryItem || isLoadingLocation || !location) {
    return (0, import_jsx_runtime2.jsx)("div", { children: "Loading..." });
  }
  const locationLevel = inventoryItem.location_levels.find(
    (l) => l.location_id === reservation.location_id
  );
  return (0, import_jsx_runtime2.jsxs)(Container, { className: "divide-y p-0", children: [
    (0, import_jsx_runtime2.jsxs)("div", { className: "flex items-center justify-between px-6 py-4", children: [
      (0, import_jsx_runtime2.jsx)(Heading, { children: t("inventory.reservation.header", {
        itemName: inventoryItem.title ?? inventoryItem.sku
      }) }),
      (0, import_jsx_runtime2.jsx)(
        ActionMenu,
        {
          groups: [
            {
              actions: [
                {
                  icon: (0, import_jsx_runtime2.jsx)(PencilSquare, {}),
                  label: t("actions.edit"),
                  to: `edit`
                }
              ]
            }
          ]
        }
      )
    ] }),
    (0, import_jsx_runtime2.jsx)(
      SectionRow,
      {
        title: t("inventory.reservation.lineItemId"),
        value: reservation.line_item_id
      }
    ),
    (0, import_jsx_runtime2.jsx)(
      SectionRow,
      {
        title: t("inventory.reservation.description"),
        value: reservation.description
      }
    ),
    (0, import_jsx_runtime2.jsx)(
      SectionRow,
      {
        title: t("inventory.reservation.location"),
        value: location == null ? void 0 : location.name
      }
    ),
    (0, import_jsx_runtime2.jsx)(
      SectionRow,
      {
        title: t("inventory.reservation.inStockAtLocation"),
        value: locationLevel == null ? void 0 : locationLevel.stocked_quantity
      }
    ),
    (0, import_jsx_runtime2.jsx)(
      SectionRow,
      {
        title: t("inventory.reservation.availableAtLocation"),
        value: locationLevel == null ? void 0 : locationLevel.available_quantity
      }
    ),
    (0, import_jsx_runtime2.jsx)(
      SectionRow,
      {
        title: t("inventory.reservation.reservedAtLocation"),
        value: locationLevel == null ? void 0 : locationLevel.reserved_quantity
      }
    )
  ] });
};
var ReservationDetail = () => {
  var _a, _b;
  const { id } = useParams();
  const initialData = useLoaderData();
  const { reservation, isLoading, isError, error } = useReservationItem(
    id,
    void 0,
    {
      initialData
    }
  );
  const { inventory_item } = useInventoryItem(
    (_a = reservation == null ? void 0 : reservation.inventory_item) == null ? void 0 : _a.id,
    void 0,
    { enabled: !!((_b = reservation == null ? void 0 : reservation.inventory_item) == null ? void 0 : _b.id) }
  );
  const { getWidgets } = useExtension();
  if (isLoading || !reservation) {
    return (0, import_jsx_runtime3.jsx)(
      TwoColumnPageSkeleton,
      {
        mainSections: 1,
        sidebarSections: 1,
        showJSON: true,
        showMetadata: true
      }
    );
  }
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime3.jsxs)(
    TwoColumnPage,
    {
      widgets: {
        before: getWidgets("reservation.details.before"),
        after: getWidgets("reservation.details.after"),
        sideBefore: getWidgets("reservation.details.side.before"),
        sideAfter: getWidgets("reservation.details.side.after")
      },
      data: reservation,
      showJSON: true,
      showMetadata: true,
      children: [
        (0, import_jsx_runtime3.jsx)(TwoColumnPage.Main, { children: (0, import_jsx_runtime3.jsx)(ReservationGeneralSection, { reservation }) }),
        (0, import_jsx_runtime3.jsx)(TwoColumnPage.Sidebar, { children: inventory_item && (0, import_jsx_runtime3.jsx)(InventoryItemGeneralSection, { inventoryItem: inventory_item }) })
      ]
    }
  );
};
export {
  ReservationDetailBreadcrumb as Breadcrumb,
  ReservationDetail as Component,
  reservationItemLoader as loader
};
//# sourceMappingURL=reservation-detail-27UGTUXR-6QKXTX6K.js.map
