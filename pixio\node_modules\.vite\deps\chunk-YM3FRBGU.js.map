{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-D3YQN7HV.mjs"], "sourcesContent": ["// src/components/common/progress-bar/progress-bar.tsx\nimport { motion } from \"motion/react\";\nimport { jsx } from \"react/jsx-runtime\";\nvar ProgressBar = ({ duration = 2 }) => {\n  return /* @__PURE__ */ jsx(\n    motion.div,\n    {\n      className: \"bg-ui-fg-subtle size-full\",\n      initial: {\n        width: \"0%\"\n      },\n      transition: {\n        delay: 0.2,\n        duration,\n        ease: \"linear\"\n      },\n      animate: {\n        width: \"90%\"\n      },\n      exit: {\n        width: \"100%\",\n        transition: { duration: 0.2, ease: \"linear\" }\n      }\n    }\n  );\n};\n\nexport {\n  ProgressBar\n};\n"], "mappings": ";;;;;;;;;;;AAEA,yBAAoB;AACpB,IAAI,cAAc,CAAC,EAAE,WAAW,EAAE,MAAM;AACtC,aAAuB;AAAA,IACrB,OAAO;AAAA,IACP;AAAA,MACE,WAAW;AAAA,MACX,SAAS;AAAA,QACP,OAAO;AAAA,MACT;AAAA,MACA,YAAY;AAAA,QACV,OAAO;AAAA,QACP;AAAA,QACA,MAAM;AAAA,MACR;AAAA,MACA,SAAS;AAAA,QACP,OAAO;AAAA,MACT;AAAA,MACA,MAAM;AAAA,QACJ,OAAO;AAAA,QACP,YAAY,EAAE,UAAU,KAAK,MAAM,SAAS;AAAA,MAC9C;AAAA,IACF;AAAA,EACF;AACF;", "names": []}