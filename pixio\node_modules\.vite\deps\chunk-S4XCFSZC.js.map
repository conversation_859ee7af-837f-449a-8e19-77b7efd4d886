{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-PYIO3TDQ.mjs"], "sourcesContent": ["// src/routes/locations/common/constants.ts\nvar ShippingOptionPriceType = /* @__PURE__ */ ((ShippingOptionPriceType2) => {\n  ShippingOptionPriceType2[\"FlatRate\"] = \"flat\";\n  ShippingOptionPriceType2[\"Calculated\"] = \"calculated\";\n  return ShippingOptionPriceType2;\n})(ShippingOptionPriceType || {});\nvar GEO_ZONE_STACKED_MODAL_ID = \"geo-zone\";\nvar CONDITIONAL_PRICES_STACKED_MODAL_ID = \"conditional-prices\";\nvar ITEM_TOTAL_ATTRIBUTE = \"item_total\";\nvar REGION_ID_ATTRIBUTE = \"region_id\";\n\nexport {\n  ShippingOptionPriceType,\n  GEO_ZONE_STACKED_MODAL_ID,\n  CONDITIONAL_PRICES_STACKED_MODAL_ID,\n  ITEM_TOTAL_ATTRIBUTE,\n  REGION_ID_ATTRIBUTE\n};\n"], "mappings": ";AACA,IAAI,2BAA2C,CAAC,6BAA6B;AAC3E,2BAAyB,UAAU,IAAI;AACvC,2BAAyB,YAAY,IAAI;AACzC,SAAO;AACT,GAAG,2BAA2B,CAAC,CAAC;AAChC,IAAI,4BAA4B;AAChC,IAAI,sCAAsC;AAC1C,IAAI,uBAAuB;AAC3B,IAAI,sBAAsB;", "names": []}