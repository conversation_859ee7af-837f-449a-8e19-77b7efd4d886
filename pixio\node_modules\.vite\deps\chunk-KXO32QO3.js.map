{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-S2UEWRDA.mjs"], "sourcesContent": ["import {\n  KeyboundForm\n} from \"./chunk-6HTZNHPT.mjs\";\nimport {\n  RouteDrawer,\n  useRouteModal\n} from \"./chunk-4TC5YS65.mjs\";\nimport {\n  Skeleton\n} from \"./chunk-LPEUYMRK.mjs\";\nimport {\n  ConditionalTooltip\n} from \"./chunk-OC7BQLYI.mjs\";\nimport {\n  Form\n} from \"./chunk-OBQI23QM.mjs\";\n\n// src/components/forms/metadata-form/metadata-form.tsx\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport {\n  Button,\n  DropdownMenu,\n  Heading,\n  IconButton,\n  InlineTip,\n  clx,\n  toast\n} from \"@medusajs/ui\";\nimport { useFieldArray, useForm } from \"react-hook-form\";\nimport { useTranslation } from \"react-i18next\";\nimport { z } from \"zod\";\nimport {\n  ArrowDownMini,\n  ArrowUpMini,\n  EllipsisVertical,\n  Trash\n} from \"@medusajs/icons\";\nimport { forwardRef } from \"react\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar MetadataFieldSchema = z.object({\n  key: z.string(),\n  disabled: z.boolean().optional(),\n  value: z.any()\n});\nvar MetadataSchema = z.object({\n  metadata: z.array(MetadataFieldSchema)\n});\nvar MetadataForm = (props) => {\n  const { t } = useTranslation();\n  const { isPending, ...innerProps } = props;\n  return /* @__PURE__ */ jsxs(RouteDrawer, { children: [\n    /* @__PURE__ */ jsxs(RouteDrawer.Header, { children: [\n      /* @__PURE__ */ jsx(RouteDrawer.Title, { asChild: true, children: /* @__PURE__ */ jsx(Heading, { children: t(\"metadata.edit.header\") }) }),\n      /* @__PURE__ */ jsx(RouteDrawer.Description, { className: \"sr-only\", children: t(\"metadata.edit.description\") })\n    ] }),\n    isPending ? /* @__PURE__ */ jsx(PlaceholderInner, {}) : /* @__PURE__ */ jsx(InnerForm, { ...innerProps })\n  ] });\n};\nvar METADATA_KEY_LABEL_ID = \"metadata-form-key-label\";\nvar METADATA_VALUE_LABEL_ID = \"metadata-form-value-label\";\nvar InnerForm = ({\n  metadata,\n  hook,\n  isMutating\n}) => {\n  const { t } = useTranslation();\n  const { handleSuccess } = useRouteModal();\n  const hasUneditableRows = getHasUneditableRows(metadata);\n  const form = useForm({\n    defaultValues: {\n      metadata: getDefaultValues(metadata)\n    },\n    resolver: zodResolver(MetadataSchema)\n  });\n  const handleSubmit = form.handleSubmit(async (data) => {\n    const parsedData = parseValues(data, metadata);\n    await hook(\n      {\n        metadata: parsedData\n      },\n      {\n        onSuccess: () => {\n          toast.success(t(\"metadata.edit.successToast\"));\n          handleSuccess();\n        },\n        onError: (error) => {\n          toast.error(error.message);\n        }\n      }\n    );\n  });\n  const { fields, insert, remove } = useFieldArray({\n    control: form.control,\n    name: \"metadata\"\n  });\n  function deleteRow(index) {\n    remove(index);\n    if (fields.length === 1) {\n      insert(0, {\n        key: \"\",\n        value: \"\",\n        disabled: false\n      });\n    }\n  }\n  function insertRow(index, position) {\n    insert(index + (position === \"above\" ? 0 : 1), {\n      key: \"\",\n      value: \"\",\n      disabled: false\n    });\n  }\n  return /* @__PURE__ */ jsx(RouteDrawer.Form, { form, children: /* @__PURE__ */ jsxs(\n    KeyboundForm,\n    {\n      onSubmit: handleSubmit,\n      className: \"flex flex-1 flex-col overflow-hidden\",\n      children: [\n        /* @__PURE__ */ jsxs(RouteDrawer.Body, { className: \"flex flex-1 flex-col gap-y-8 overflow-y-auto\", children: [\n          /* @__PURE__ */ jsxs(\"div\", { className: \"bg-ui-bg-base shadow-elevation-card-rest grid grid-cols-1 divide-y rounded-lg\", children: [\n            /* @__PURE__ */ jsxs(\"div\", { className: \"bg-ui-bg-subtle grid grid-cols-2 divide-x rounded-t-lg\", children: [\n              /* @__PURE__ */ jsx(\"div\", { className: \"txt-compact-small-plus text-ui-fg-subtle px-2 py-1.5\", children: /* @__PURE__ */ jsx(\"label\", { id: METADATA_KEY_LABEL_ID, children: t(\"metadata.edit.labels.key\") }) }),\n              /* @__PURE__ */ jsx(\"div\", { className: \"txt-compact-small-plus text-ui-fg-subtle px-2 py-1.5\", children: /* @__PURE__ */ jsx(\"label\", { id: METADATA_VALUE_LABEL_ID, children: t(\"metadata.edit.labels.value\") }) })\n            ] }),\n            fields.map((field, index) => {\n              const isDisabled = field.disabled || false;\n              let placeholder = \"-\";\n              if (typeof field.value === \"object\") {\n                placeholder = \"{ ... }\";\n              }\n              if (Array.isArray(field.value)) {\n                placeholder = \"[ ... ]\";\n              }\n              return /* @__PURE__ */ jsx(\n                ConditionalTooltip,\n                {\n                  showTooltip: isDisabled,\n                  content: t(\"metadata.edit.complexRow.tooltip\"),\n                  children: /* @__PURE__ */ jsxs(\"div\", { className: \"group/table relative\", children: [\n                    /* @__PURE__ */ jsxs(\n                      \"div\",\n                      {\n                        className: clx(\"grid grid-cols-2 divide-x\", {\n                          \"overflow-hidden rounded-b-lg\": index === fields.length - 1\n                        }),\n                        children: [\n                          /* @__PURE__ */ jsx(\n                            Form.Field,\n                            {\n                              control: form.control,\n                              name: `metadata.${index}.key`,\n                              render: ({ field: field2 }) => {\n                                return /* @__PURE__ */ jsx(Form.Item, { children: /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(\n                                  GridInput,\n                                  {\n                                    \"aria-labelledby\": METADATA_KEY_LABEL_ID,\n                                    ...field2,\n                                    disabled: isDisabled,\n                                    placeholder: \"Key\"\n                                  }\n                                ) }) });\n                              }\n                            }\n                          ),\n                          /* @__PURE__ */ jsx(\n                            Form.Field,\n                            {\n                              control: form.control,\n                              name: `metadata.${index}.value`,\n                              render: ({ field: { value, ...field2 } }) => {\n                                return /* @__PURE__ */ jsx(Form.Item, { children: /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(\n                                  GridInput,\n                                  {\n                                    \"aria-labelledby\": METADATA_VALUE_LABEL_ID,\n                                    ...field2,\n                                    value: isDisabled ? placeholder : value,\n                                    disabled: isDisabled,\n                                    placeholder: \"Value\"\n                                  }\n                                ) }) });\n                              }\n                            }\n                          )\n                        ]\n                      }\n                    ),\n                    /* @__PURE__ */ jsxs(DropdownMenu, { children: [\n                      /* @__PURE__ */ jsx(\n                        DropdownMenu.Trigger,\n                        {\n                          className: clx(\n                            \"invisible absolute inset-y-0 -right-2.5 my-auto group-hover/table:visible data-[state='open']:visible\",\n                            {\n                              hidden: isDisabled\n                            }\n                          ),\n                          disabled: isDisabled,\n                          asChild: true,\n                          children: /* @__PURE__ */ jsx(IconButton, { size: \"2xsmall\", children: /* @__PURE__ */ jsx(EllipsisVertical, {}) })\n                        }\n                      ),\n                      /* @__PURE__ */ jsxs(DropdownMenu.Content, { children: [\n                        /* @__PURE__ */ jsxs(\n                          DropdownMenu.Item,\n                          {\n                            className: \"gap-x-2\",\n                            onClick: () => insertRow(index, \"above\"),\n                            children: [\n                              /* @__PURE__ */ jsx(ArrowUpMini, { className: \"text-ui-fg-subtle\" }),\n                              t(\"metadata.edit.actions.insertRowAbove\")\n                            ]\n                          }\n                        ),\n                        /* @__PURE__ */ jsxs(\n                          DropdownMenu.Item,\n                          {\n                            className: \"gap-x-2\",\n                            onClick: () => insertRow(index, \"below\"),\n                            children: [\n                              /* @__PURE__ */ jsx(ArrowDownMini, { className: \"text-ui-fg-subtle\" }),\n                              t(\"metadata.edit.actions.insertRowBelow\")\n                            ]\n                          }\n                        ),\n                        /* @__PURE__ */ jsx(DropdownMenu.Separator, {}),\n                        /* @__PURE__ */ jsxs(\n                          DropdownMenu.Item,\n                          {\n                            className: \"gap-x-2\",\n                            onClick: () => deleteRow(index),\n                            children: [\n                              /* @__PURE__ */ jsx(Trash, { className: \"text-ui-fg-subtle\" }),\n                              t(\"metadata.edit.actions.deleteRow\")\n                            ]\n                          }\n                        )\n                      ] })\n                    ] })\n                  ] })\n                },\n                field.id\n              );\n            })\n          ] }),\n          hasUneditableRows && /* @__PURE__ */ jsx(\n            InlineTip,\n            {\n              variant: \"warning\",\n              label: t(\"metadata.edit.complexRow.label\"),\n              children: t(\"metadata.edit.complexRow.description\")\n            }\n          )\n        ] }),\n        /* @__PURE__ */ jsx(RouteDrawer.Footer, { children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center justify-end gap-x-2\", children: [\n          /* @__PURE__ */ jsx(RouteDrawer.Close, { asChild: true, children: /* @__PURE__ */ jsx(\n            Button,\n            {\n              size: \"small\",\n              variant: \"secondary\",\n              type: \"button\",\n              disabled: isMutating,\n              children: t(\"actions.cancel\")\n            }\n          ) }),\n          /* @__PURE__ */ jsx(Button, { size: \"small\", type: \"submit\", isLoading: isMutating, children: t(\"actions.save\") })\n        ] }) })\n      ]\n    }\n  ) });\n};\nvar GridInput = forwardRef(({ className, ...props }, ref) => {\n  return /* @__PURE__ */ jsx(\n    \"input\",\n    {\n      ref,\n      ...props,\n      autoComplete: \"off\",\n      className: clx(\n        \"txt-compact-small text-ui-fg-base placeholder:text-ui-fg-muted disabled:text-ui-fg-disabled disabled:bg-ui-bg-base bg-transparent px-2 py-1.5 outline-none\",\n        className\n      )\n    }\n  );\n});\nGridInput.displayName = \"MetadataForm.GridInput\";\nvar PlaceholderInner = () => {\n  return /* @__PURE__ */ jsxs(\"div\", { className: \"flex flex-1 flex-col overflow-hidden\", children: [\n    /* @__PURE__ */ jsx(RouteDrawer.Body, { children: /* @__PURE__ */ jsx(Skeleton, { className: \"h-[148ox] w-full rounded-lg\" }) }),\n    /* @__PURE__ */ jsx(RouteDrawer.Footer, { children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center justify-end gap-x-2\", children: [\n      /* @__PURE__ */ jsx(Skeleton, { className: \"h-7 w-12 rounded-md\" }),\n      /* @__PURE__ */ jsx(Skeleton, { className: \"h-7 w-12 rounded-md\" })\n    ] }) })\n  ] });\n};\nvar EDITABLE_TYPES = [\"string\", \"number\", \"boolean\"];\nfunction getDefaultValues(metadata) {\n  if (!metadata || !Object.keys(metadata).length) {\n    return [\n      {\n        key: \"\",\n        value: \"\",\n        disabled: false\n      }\n    ];\n  }\n  return Object.entries(metadata).map(([key, value]) => {\n    if (!EDITABLE_TYPES.includes(typeof value)) {\n      return {\n        key,\n        value,\n        disabled: true\n      };\n    }\n    let stringValue = value;\n    if (typeof value !== \"string\") {\n      stringValue = JSON.stringify(value);\n    }\n    return {\n      key,\n      value: stringValue,\n      original_key: key\n    };\n  });\n}\nfunction parseValues(values, original) {\n  const metadata = values.metadata;\n  const isEmpty = !metadata.length || metadata.length === 1 && !metadata[0].key && !metadata[0].value;\n  if (isEmpty) {\n    return null;\n  }\n  const update = {};\n  if (original) {\n    Object.keys(original).forEach((originalKey) => {\n      const exists = metadata.some((field) => field.key === originalKey);\n      if (!exists) {\n        update[originalKey] = \"\";\n      }\n    });\n  }\n  metadata.forEach((field) => {\n    let key = field.key;\n    let value = field.value;\n    const disabled = field.disabled;\n    if (!key) {\n      return;\n    }\n    if (disabled) {\n      update[key] = value;\n      return;\n    }\n    key = key.trim();\n    value = value?.trim() ?? \"\";\n    if (value === \"true\") {\n      update[key] = true;\n    } else if (value === \"false\") {\n      update[key] = false;\n    } else {\n      const isNumeric = /^-?\\d*\\.?\\d+$/.test(value);\n      if (isNumeric) {\n        update[key] = parseFloat(value);\n      } else {\n        update[key] = value;\n      }\n    }\n  });\n  return update;\n}\nfunction getHasUneditableRows(metadata) {\n  if (!metadata) {\n    return false;\n  }\n  return Object.values(metadata).some(\n    (value) => !EDITABLE_TYPES.includes(typeof value)\n  );\n}\n\nexport {\n  MetadataForm\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqCA,mBAA2B;AAC3B,yBAA0B;AAC1B,IAAI,sBAAsB,EAAE,OAAO;AAAA,EACjC,KAAK,EAAE,OAAO;AAAA,EACd,UAAU,EAAE,QAAQ,EAAE,SAAS;AAAA,EAC/B,OAAO,EAAE,IAAI;AACf,CAAC;AACD,IAAI,iBAAiB,EAAE,OAAO;AAAA,EAC5B,UAAU,EAAE,MAAM,mBAAmB;AACvC,CAAC;AACD,IAAI,eAAe,CAAC,UAAU;AAC5B,QAAM,EAAE,GAAAA,GAAE,IAAI,eAAe;AAC7B,QAAM,EAAE,WAAW,GAAG,WAAW,IAAI;AACrC,aAAuB,yBAAK,aAAa,EAAE,UAAU;AAAA,QACnC,yBAAK,YAAY,QAAQ,EAAE,UAAU;AAAA,UACnC,wBAAI,YAAY,OAAO,EAAE,SAAS,MAAM,cAA0B,wBAAI,SAAS,EAAE,UAAUA,GAAE,sBAAsB,EAAE,CAAC,EAAE,CAAC;AAAA,UACzH,wBAAI,YAAY,aAAa,EAAE,WAAW,WAAW,UAAUA,GAAE,2BAA2B,EAAE,CAAC;AAAA,IACjH,EAAE,CAAC;AAAA,IACH,gBAA4B,wBAAI,kBAAkB,CAAC,CAAC,QAAoB,wBAAI,WAAW,EAAE,GAAG,WAAW,CAAC;AAAA,EAC1G,EAAE,CAAC;AACL;AACA,IAAI,wBAAwB;AAC5B,IAAI,0BAA0B;AAC9B,IAAI,YAAY,CAAC;AAAA,EACf;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACJ,QAAM,EAAE,GAAAA,GAAE,IAAI,eAAe;AAC7B,QAAM,EAAE,cAAc,IAAI,cAAc;AACxC,QAAM,oBAAoB,qBAAqB,QAAQ;AACvD,QAAM,OAAO,QAAQ;AAAA,IACnB,eAAe;AAAA,MACb,UAAU,iBAAiB,QAAQ;AAAA,IACrC;AAAA,IACA,UAAU,EAAY,cAAc;AAAA,EACtC,CAAC;AACD,QAAM,eAAe,KAAK,aAAa,OAAO,SAAS;AACrD,UAAM,aAAa,YAAY,MAAM,QAAQ;AAC7C,UAAM;AAAA,MACJ;AAAA,QACE,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,WAAW,MAAM;AACf,gBAAM,QAAQA,GAAE,4BAA4B,CAAC;AAC7C,wBAAc;AAAA,QAChB;AAAA,QACA,SAAS,CAAC,UAAU;AAClB,gBAAM,MAAM,MAAM,OAAO;AAAA,QAC3B;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AACD,QAAM,EAAE,QAAQ,QAAQ,OAAO,IAAI,cAAc;AAAA,IAC/C,SAAS,KAAK;AAAA,IACd,MAAM;AAAA,EACR,CAAC;AACD,WAAS,UAAU,OAAO;AACxB,WAAO,KAAK;AACZ,QAAI,OAAO,WAAW,GAAG;AACvB,aAAO,GAAG;AAAA,QACR,KAAK;AAAA,QACL,OAAO;AAAA,QACP,UAAU;AAAA,MACZ,CAAC;AAAA,IACH;AAAA,EACF;AACA,WAAS,UAAU,OAAO,UAAU;AAClC,WAAO,SAAS,aAAa,UAAU,IAAI,IAAI;AAAA,MAC7C,KAAK;AAAA,MACL,OAAO;AAAA,MACP,UAAU;AAAA,IACZ,CAAC;AAAA,EACH;AACA,aAAuB,wBAAI,YAAY,MAAM,EAAE,MAAM,cAA0B;AAAA,IAC7E;AAAA,IACA;AAAA,MACE,UAAU;AAAA,MACV,WAAW;AAAA,MACX,UAAU;AAAA,YACQ,yBAAK,YAAY,MAAM,EAAE,WAAW,gDAAgD,UAAU;AAAA,cAC5F,yBAAK,OAAO,EAAE,WAAW,iFAAiF,UAAU;AAAA,gBAClH,yBAAK,OAAO,EAAE,WAAW,0DAA0D,UAAU;AAAA,kBAC3F,wBAAI,OAAO,EAAE,WAAW,wDAAwD,cAA0B,wBAAI,SAAS,EAAE,IAAI,uBAAuB,UAAUA,GAAE,0BAA0B,EAAE,CAAC,EAAE,CAAC;AAAA,kBAChM,wBAAI,OAAO,EAAE,WAAW,wDAAwD,cAA0B,wBAAI,SAAS,EAAE,IAAI,yBAAyB,UAAUA,GAAE,4BAA4B,EAAE,CAAC,EAAE,CAAC;AAAA,YACtN,EAAE,CAAC;AAAA,YACH,OAAO,IAAI,CAAC,OAAO,UAAU;AAC3B,oBAAM,aAAa,MAAM,YAAY;AACrC,kBAAI,cAAc;AAClB,kBAAI,OAAO,MAAM,UAAU,UAAU;AACnC,8BAAc;AAAA,cAChB;AACA,kBAAI,MAAM,QAAQ,MAAM,KAAK,GAAG;AAC9B,8BAAc;AAAA,cAChB;AACA,yBAAuB;AAAA,gBACrB;AAAA,gBACA;AAAA,kBACE,aAAa;AAAA,kBACb,SAASA,GAAE,kCAAkC;AAAA,kBAC7C,cAA0B,yBAAK,OAAO,EAAE,WAAW,wBAAwB,UAAU;AAAA,wBACnE;AAAA,sBACd;AAAA,sBACA;AAAA,wBACE,WAAW,IAAI,6BAA6B;AAAA,0BAC1C,gCAAgC,UAAU,OAAO,SAAS;AAAA,wBAC5D,CAAC;AAAA,wBACD,UAAU;AAAA,8BACQ;AAAA,4BACd,KAAK;AAAA,4BACL;AAAA,8BACE,SAAS,KAAK;AAAA,8BACd,MAAM,YAAY,KAAK;AAAA,8BACvB,QAAQ,CAAC,EAAE,OAAO,OAAO,MAAM;AAC7B,2CAAuB,wBAAI,KAAK,MAAM,EAAE,cAA0B,wBAAI,KAAK,SAAS,EAAE,cAA0B;AAAA,kCAC9G;AAAA,kCACA;AAAA,oCACE,mBAAmB;AAAA,oCACnB,GAAG;AAAA,oCACH,UAAU;AAAA,oCACV,aAAa;AAAA,kCACf;AAAA,gCACF,EAAE,CAAC,EAAE,CAAC;AAAA,8BACR;AAAA,4BACF;AAAA,0BACF;AAAA,8BACgB;AAAA,4BACd,KAAK;AAAA,4BACL;AAAA,8BACE,SAAS,KAAK;AAAA,8BACd,MAAM,YAAY,KAAK;AAAA,8BACvB,QAAQ,CAAC,EAAE,OAAO,EAAE,OAAO,GAAG,OAAO,EAAE,MAAM;AAC3C,2CAAuB,wBAAI,KAAK,MAAM,EAAE,cAA0B,wBAAI,KAAK,SAAS,EAAE,cAA0B;AAAA,kCAC9G;AAAA,kCACA;AAAA,oCACE,mBAAmB;AAAA,oCACnB,GAAG;AAAA,oCACH,OAAO,aAAa,cAAc;AAAA,oCAClC,UAAU;AAAA,oCACV,aAAa;AAAA,kCACf;AAAA,gCACF,EAAE,CAAC,EAAE,CAAC;AAAA,8BACR;AAAA,4BACF;AAAA,0BACF;AAAA,wBACF;AAAA,sBACF;AAAA,oBACF;AAAA,wBACgB,yBAAK,cAAc,EAAE,UAAU;AAAA,0BAC7B;AAAA,wBACd,aAAa;AAAA,wBACb;AAAA,0BACE,WAAW;AAAA,4BACT;AAAA,4BACA;AAAA,8BACE,QAAQ;AAAA,4BACV;AAAA,0BACF;AAAA,0BACA,UAAU;AAAA,0BACV,SAAS;AAAA,0BACT,cAA0B,wBAAI,YAAY,EAAE,MAAM,WAAW,cAA0B,wBAAI,kBAAkB,CAAC,CAAC,EAAE,CAAC;AAAA,wBACpH;AAAA,sBACF;AAAA,0BACgB,yBAAK,aAAa,SAAS,EAAE,UAAU;AAAA,4BACrC;AAAA,0BACd,aAAa;AAAA,0BACb;AAAA,4BACE,WAAW;AAAA,4BACX,SAAS,MAAM,UAAU,OAAO,OAAO;AAAA,4BACvC,UAAU;AAAA,kCACQ,wBAAI,aAAa,EAAE,WAAW,oBAAoB,CAAC;AAAA,8BACnEA,GAAE,sCAAsC;AAAA,4BAC1C;AAAA,0BACF;AAAA,wBACF;AAAA,4BACgB;AAAA,0BACd,aAAa;AAAA,0BACb;AAAA,4BACE,WAAW;AAAA,4BACX,SAAS,MAAM,UAAU,OAAO,OAAO;AAAA,4BACvC,UAAU;AAAA,kCACQ,wBAAI,eAAe,EAAE,WAAW,oBAAoB,CAAC;AAAA,8BACrEA,GAAE,sCAAsC;AAAA,4BAC1C;AAAA,0BACF;AAAA,wBACF;AAAA,4BACgB,wBAAI,aAAa,WAAW,CAAC,CAAC;AAAA,4BAC9B;AAAA,0BACd,aAAa;AAAA,0BACb;AAAA,4BACE,WAAW;AAAA,4BACX,SAAS,MAAM,UAAU,KAAK;AAAA,4BAC9B,UAAU;AAAA,kCACQ,wBAAI,OAAO,EAAE,WAAW,oBAAoB,CAAC;AAAA,8BAC7DA,GAAE,iCAAiC;AAAA,4BACrC;AAAA,0BACF;AAAA,wBACF;AAAA,sBACF,EAAE,CAAC;AAAA,oBACL,EAAE,CAAC;AAAA,kBACL,EAAE,CAAC;AAAA,gBACL;AAAA,gBACA,MAAM;AAAA,cACR;AAAA,YACF,CAAC;AAAA,UACH,EAAE,CAAC;AAAA,UACH,yBAAqC;AAAA,YACnC;AAAA,YACA;AAAA,cACE,SAAS;AAAA,cACT,OAAOA,GAAE,gCAAgC;AAAA,cACzC,UAAUA,GAAE,sCAAsC;AAAA,YACpD;AAAA,UACF;AAAA,QACF,EAAE,CAAC;AAAA,YACa,wBAAI,YAAY,QAAQ,EAAE,cAA0B,yBAAK,OAAO,EAAE,WAAW,yCAAyC,UAAU;AAAA,cAC9H,wBAAI,YAAY,OAAO,EAAE,SAAS,MAAM,cAA0B;AAAA,YAChF;AAAA,YACA;AAAA,cACE,MAAM;AAAA,cACN,SAAS;AAAA,cACT,MAAM;AAAA,cACN,UAAU;AAAA,cACV,UAAUA,GAAE,gBAAgB;AAAA,YAC9B;AAAA,UACF,EAAE,CAAC;AAAA,cACa,wBAAI,QAAQ,EAAE,MAAM,SAAS,MAAM,UAAU,WAAW,YAAY,UAAUA,GAAE,cAAc,EAAE,CAAC;AAAA,QACnH,EAAE,CAAC,EAAE,CAAC;AAAA,MACR;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;AACA,IAAI,gBAAY,yBAAW,CAAC,EAAE,WAAW,GAAG,MAAM,GAAG,QAAQ;AAC3D,aAAuB;AAAA,IACrB;AAAA,IACA;AAAA,MACE;AAAA,MACA,GAAG;AAAA,MACH,cAAc;AAAA,MACd,WAAW;AAAA,QACT;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF,CAAC;AACD,UAAU,cAAc;AACxB,IAAI,mBAAmB,MAAM;AAC3B,aAAuB,yBAAK,OAAO,EAAE,WAAW,wCAAwC,UAAU;AAAA,QAChF,wBAAI,YAAY,MAAM,EAAE,cAA0B,wBAAI,UAAU,EAAE,WAAW,8BAA8B,CAAC,EAAE,CAAC;AAAA,QAC/G,wBAAI,YAAY,QAAQ,EAAE,cAA0B,yBAAK,OAAO,EAAE,WAAW,yCAAyC,UAAU;AAAA,UAC9H,wBAAI,UAAU,EAAE,WAAW,sBAAsB,CAAC;AAAA,UAClD,wBAAI,UAAU,EAAE,WAAW,sBAAsB,CAAC;AAAA,IACpE,EAAE,CAAC,EAAE,CAAC;AAAA,EACR,EAAE,CAAC;AACL;AACA,IAAI,iBAAiB,CAAC,UAAU,UAAU,SAAS;AACnD,SAAS,iBAAiB,UAAU;AAClC,MAAI,CAAC,YAAY,CAAC,OAAO,KAAK,QAAQ,EAAE,QAAQ;AAC9C,WAAO;AAAA,MACL;AAAA,QACE,KAAK;AAAA,QACL,OAAO;AAAA,QACP,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AACA,SAAO,OAAO,QAAQ,QAAQ,EAAE,IAAI,CAAC,CAAC,KAAK,KAAK,MAAM;AACpD,QAAI,CAAC,eAAe,SAAS,OAAO,KAAK,GAAG;AAC1C,aAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA,UAAU;AAAA,MACZ;AAAA,IACF;AACA,QAAI,cAAc;AAClB,QAAI,OAAO,UAAU,UAAU;AAC7B,oBAAc,KAAK,UAAU,KAAK;AAAA,IACpC;AACA,WAAO;AAAA,MACL;AAAA,MACA,OAAO;AAAA,MACP,cAAc;AAAA,IAChB;AAAA,EACF,CAAC;AACH;AACA,SAAS,YAAY,QAAQ,UAAU;AACrC,QAAM,WAAW,OAAO;AACxB,QAAM,UAAU,CAAC,SAAS,UAAU,SAAS,WAAW,KAAK,CAAC,SAAS,CAAC,EAAE,OAAO,CAAC,SAAS,CAAC,EAAE;AAC9F,MAAI,SAAS;AACX,WAAO;AAAA,EACT;AACA,QAAM,SAAS,CAAC;AAChB,MAAI,UAAU;AACZ,WAAO,KAAK,QAAQ,EAAE,QAAQ,CAAC,gBAAgB;AAC7C,YAAM,SAAS,SAAS,KAAK,CAAC,UAAU,MAAM,QAAQ,WAAW;AACjE,UAAI,CAAC,QAAQ;AACX,eAAO,WAAW,IAAI;AAAA,MACxB;AAAA,IACF,CAAC;AAAA,EACH;AACA,WAAS,QAAQ,CAAC,UAAU;AAC1B,QAAI,MAAM,MAAM;AAChB,QAAI,QAAQ,MAAM;AAClB,UAAM,WAAW,MAAM;AACvB,QAAI,CAAC,KAAK;AACR;AAAA,IACF;AACA,QAAI,UAAU;AACZ,aAAO,GAAG,IAAI;AACd;AAAA,IACF;AACA,UAAM,IAAI,KAAK;AACf,aAAQ,+BAAO,WAAU;AACzB,QAAI,UAAU,QAAQ;AACpB,aAAO,GAAG,IAAI;AAAA,IAChB,WAAW,UAAU,SAAS;AAC5B,aAAO,GAAG,IAAI;AAAA,IAChB,OAAO;AACL,YAAM,YAAY,gBAAgB,KAAK,KAAK;AAC5C,UAAI,WAAW;AACb,eAAO,GAAG,IAAI,WAAW,KAAK;AAAA,MAChC,OAAO;AACL,eAAO,GAAG,IAAI;AAAA,MAChB;AAAA,IACF;AAAA,EACF,CAAC;AACD,SAAO;AACT;AACA,SAAS,qBAAqB,UAAU;AACtC,MAAI,CAAC,UAAU;AACb,WAAO;AAAA,EACT;AACA,SAAO,OAAO,OAAO,QAAQ,EAAE;AAAA,IAC7B,CAAC,UAAU,CAAC,eAAe,SAAS,OAAO,KAAK;AAAA,EAClD;AACF;", "names": ["t"]}