{"version": 3, "sources": ["../../@medusajs/dashboard/dist/collection-list-TVDNVOBK.mjs"], "sourcesContent": ["import {\n  useCollectionTableColumns,\n  useCollectionTableQuery\n} from \"./chunk-DFA6WGYO.mjs\";\nimport \"./chunk-I5HYE2RW.mjs\";\nimport \"./chunk-RIV7FKGN.mjs\";\nimport \"./chunk-TDK3JDOB.mjs\";\nimport \"./chunk-FHSC5X62.mjs\";\nimport \"./chunk-XR4GEMGR.mjs\";\nimport \"./chunk-WRSGHGAT.mjs\";\nimport \"./chunk-MOSRJHJ3.mjs\";\nimport \"./chunk-OMC5JCQH.mjs\";\nimport \"./chunk-XMAWMECC.mjs\";\nimport \"./chunk-MSDRGCRR.mjs\";\nimport \"./chunk-PCFUZKDS.mjs\";\nimport \"./chunk-LQTHYS2Z.mjs\";\nimport \"./chunk-P3UUX2T6.mjs\";\nimport {\n  _DataTable,\n  useDataTable\n} from \"./chunk-UE6PO4FK.mjs\";\nimport \"./chunk-YEDAFXMB.mjs\";\nimport \"./chunk-AOFGTNG6.mjs\";\nimport \"./chunk-EMIHDNB7.mjs\";\nimport {\n  useCollectionTableFilters\n} from \"./chunk-GW6TVOAA.mjs\";\nimport \"./chunk-CBSCX7RE.mjs\";\nimport \"./chunk-LT4MVCA7.mjs\";\nimport \"./chunk-A2UMBW3V.mjs\";\nimport \"./chunk-W7625H47.mjs\";\nimport \"./chunk-DLZWPHHO.mjs\";\nimport \"./chunk-LSEYENCI.mjs\";\nimport \"./chunk-FVK4ZYYM.mjs\";\nimport \"./chunk-FZRIVT5D.mjs\";\nimport \"./chunk-SXYXTC2L.mjs\";\nimport \"./chunk-M3VFKDXJ.mjs\";\nimport {\n  SingleColumnPage\n} from \"./chunk-2RQLKDBF.mjs\";\nimport \"./chunk-C76H5USB.mjs\";\nimport \"./chunk-KSV3NQOT.mjs\";\nimport {\n  useExtension\n} from \"./chunk-C5P5PL3E.mjs\";\nimport \"./chunk-LPEUYMRK.mjs\";\nimport {\n  ActionMenu\n} from \"./chunk-3NJTXRIY.mjs\";\nimport \"./chunk-OC7BQLYI.mjs\";\nimport \"./chunk-NYXYHZE6.mjs\";\nimport \"./chunk-Z5UDPQIH.mjs\";\nimport \"./chunk-KOSCMAIC.mjs\";\nimport \"./chunk-X6DSNTTX.mjs\";\nimport \"./chunk-I6E6CALJ.mjs\";\nimport \"./chunk-B4GODIOW.mjs\";\nimport \"./chunk-F6IJV2I2.mjs\";\nimport \"./chunk-QTCZFYFH.mjs\";\nimport \"./chunk-ENV6YVOM.mjs\";\nimport \"./chunk-PIR2H25N.mjs\";\nimport \"./chunk-RLY2SL5E.mjs\";\nimport \"./chunk-C5LYZZZ5.mjs\";\nimport \"./chunk-2ZKVRTBW.mjs\";\nimport \"./chunk-FO3VP56P.mjs\";\nimport \"./chunk-YS65UGPC.mjs\";\nimport \"./chunk-F6PXCY3N.mjs\";\nimport {\n  useCollections,\n  useDeleteCollection\n} from \"./chunk-3OHH43G6.mjs\";\nimport \"./chunk-G2H6MAK7.mjs\";\nimport \"./chunk-GRT22PE5.mjs\";\nimport \"./chunk-32IQRUVY.mjs\";\nimport \"./chunk-FNYASI54.mjs\";\nimport \"./chunk-FVC7M755.mjs\";\nimport \"./chunk-ZJ3OFMHB.mjs\";\nimport \"./chunk-PNU5HPGY.mjs\";\nimport \"./chunk-V2LANK5S.mjs\";\nimport \"./chunk-QZ6PT4QV.mjs\";\nimport \"./chunk-QL4XKIVL.mjs\";\nimport \"./chunk-6I62UDJA.mjs\";\nimport \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-RPUOO7AV.mjs\";\n\n// src/routes/collections/collection-list/components/collection-list-table/collection-list-table.tsx\nimport { Button, Container, Heading, Text } from \"@medusajs/ui\";\nimport { useTranslation as useTranslation2 } from \"react-i18next\";\nimport { Link } from \"react-router-dom\";\nimport { keepPreviousData } from \"@tanstack/react-query\";\nimport { createColumnHelper } from \"@tanstack/react-table\";\nimport { useMemo } from \"react\";\n\n// src/routes/collections/collection-list/components/collection-list-table/collection-row-actions.tsx\nimport { PencilSquare, Trash } from \"@medusajs/icons\";\nimport { usePrompt } from \"@medusajs/ui\";\nimport { useTranslation } from \"react-i18next\";\nimport { jsx } from \"react/jsx-runtime\";\nvar CollectionRowActions = ({\n  collection\n}) => {\n  const { t } = useTranslation();\n  const prompt = usePrompt();\n  const { mutateAsync } = useDeleteCollection(collection.id);\n  const handleDeleteCollection = async () => {\n    const res = await prompt({\n      title: t(\"general.areYouSure\"),\n      description: t(\"collections.deleteWarning\", {\n        title: collection.title\n      }),\n      verificationText: collection.title,\n      verificationInstruction: t(\"general.typeToConfirm\"),\n      confirmText: t(\"actions.delete\"),\n      cancelText: t(\"actions.cancel\")\n    });\n    if (!res) {\n      return;\n    }\n    await mutateAsync();\n  };\n  return /* @__PURE__ */ jsx(\n    ActionMenu,\n    {\n      groups: [\n        {\n          actions: [\n            {\n              label: t(\"actions.edit\"),\n              to: `/collections/${collection.id}/edit`,\n              icon: /* @__PURE__ */ jsx(PencilSquare, {})\n            }\n          ]\n        },\n        {\n          actions: [\n            {\n              label: t(\"actions.delete\"),\n              onClick: handleDeleteCollection,\n              icon: /* @__PURE__ */ jsx(Trash, {}),\n              disabled: !collection.id\n            }\n          ]\n        }\n      ]\n    }\n  );\n};\n\n// src/routes/collections/collection-list/components/collection-list-table/collection-list-table.tsx\nimport { jsx as jsx2, jsxs } from \"react/jsx-runtime\";\nvar PAGE_SIZE = 20;\nvar CollectionListTable = () => {\n  const { t } = useTranslation2();\n  const { searchParams, raw } = useCollectionTableQuery({ pageSize: PAGE_SIZE });\n  const { collections, count, isError, error, isLoading } = useCollections(\n    {\n      ...searchParams,\n      fields: \"+products.id\"\n    },\n    {\n      placeholderData: keepPreviousData\n    }\n  );\n  const filters = useCollectionTableFilters();\n  const columns = useColumns();\n  const { table } = useDataTable({\n    data: collections ?? [],\n    columns,\n    count,\n    enablePagination: true,\n    getRowId: (row, index) => row.id ?? `${index}`,\n    pageSize: PAGE_SIZE\n  });\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsxs(Container, { className: \"divide-y p-0\", children: [\n    /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center justify-between px-6 py-4\", children: [\n      /* @__PURE__ */ jsxs(\"div\", { children: [\n        /* @__PURE__ */ jsx2(Heading, { children: t(\"collections.domain\") }),\n        /* @__PURE__ */ jsx2(Text, { className: \"text-ui-fg-subtle\", size: \"small\", children: t(\"collections.subtitle\") })\n      ] }),\n      /* @__PURE__ */ jsx2(Link, { to: \"/collections/create\", children: /* @__PURE__ */ jsx2(Button, { size: \"small\", variant: \"secondary\", children: t(\"actions.create\") }) })\n    ] }),\n    /* @__PURE__ */ jsx2(\n      _DataTable,\n      {\n        table,\n        columns,\n        pageSize: PAGE_SIZE,\n        count,\n        filters,\n        orderBy: [\n          { key: \"title\", label: t(\"fields.title\") },\n          { key: \"handle\", label: t(\"fields.handle\") },\n          { key: \"created_at\", label: t(\"fields.createdAt\") },\n          { key: \"updated_at\", label: t(\"fields.updatedAt\") }\n        ],\n        search: true,\n        navigateTo: (row) => `/collections/${row.original.id}`,\n        queryObject: raw,\n        isLoading\n      }\n    )\n  ] });\n};\nvar columnHelper = createColumnHelper();\nvar useColumns = () => {\n  const base = useCollectionTableColumns();\n  return useMemo(\n    () => [\n      ...base,\n      columnHelper.display({\n        id: \"actions\",\n        cell: ({ row }) => /* @__PURE__ */ jsx2(CollectionRowActions, { collection: row.original })\n      })\n    ],\n    [base]\n  );\n};\n\n// src/routes/collections/collection-list/collection-list.tsx\nimport { jsx as jsx3 } from \"react/jsx-runtime\";\nvar CollectionList = () => {\n  const { getWidgets } = useExtension();\n  return /* @__PURE__ */ jsx3(\n    SingleColumnPage,\n    {\n      widgets: {\n        after: getWidgets(\"product_collection.list.after\"),\n        before: getWidgets(\"product_collection.list.before\")\n      },\n      children: /* @__PURE__ */ jsx3(CollectionListTable, {})\n    }\n  );\n};\nexport {\n  CollectionList as Component\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4FA,mBAAwB;AAMxB,yBAAoB;AAoDpB,IAAAA,sBAAkC;AAyElC,IAAAA,sBAA4B;AA5H5B,IAAI,uBAAuB,CAAC;AAAA,EAC1B;AACF,MAAM;AACJ,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,QAAM,SAAS,UAAU;AACzB,QAAM,EAAE,YAAY,IAAI,oBAAoB,WAAW,EAAE;AACzD,QAAM,yBAAyB,YAAY;AACzC,UAAM,MAAM,MAAM,OAAO;AAAA,MACvB,OAAO,EAAE,oBAAoB;AAAA,MAC7B,aAAa,EAAE,6BAA6B;AAAA,QAC1C,OAAO,WAAW;AAAA,MACpB,CAAC;AAAA,MACD,kBAAkB,WAAW;AAAA,MAC7B,yBAAyB,EAAE,uBAAuB;AAAA,MAClD,aAAa,EAAE,gBAAgB;AAAA,MAC/B,YAAY,EAAE,gBAAgB;AAAA,IAChC,CAAC;AACD,QAAI,CAAC,KAAK;AACR;AAAA,IACF;AACA,UAAM,YAAY;AAAA,EACpB;AACA,aAAuB;AAAA,IACrB;AAAA,IACA;AAAA,MACE,QAAQ;AAAA,QACN;AAAA,UACE,SAAS;AAAA,YACP;AAAA,cACE,OAAO,EAAE,cAAc;AAAA,cACvB,IAAI,gBAAgB,WAAW,EAAE;AAAA,cACjC,UAAsB,wBAAI,cAAc,CAAC,CAAC;AAAA,YAC5C;AAAA,UACF;AAAA,QACF;AAAA,QACA;AAAA,UACE,SAAS;AAAA,YACP;AAAA,cACE,OAAO,EAAE,gBAAgB;AAAA,cACzB,SAAS;AAAA,cACT,UAAsB,wBAAI,OAAO,CAAC,CAAC;AAAA,cACnC,UAAU,CAAC,WAAW;AAAA,YACxB;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAIA,IAAI,YAAY;AAChB,IAAI,sBAAsB,MAAM;AAC9B,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,QAAM,EAAE,cAAc,IAAI,IAAI,wBAAwB,EAAE,UAAU,UAAU,CAAC;AAC7E,QAAM,EAAE,aAAa,OAAO,SAAS,OAAO,UAAU,IAAI;AAAA,IACxD;AAAA,MACE,GAAG;AAAA,MACH,QAAQ;AAAA,IACV;AAAA,IACA;AAAA,MACE,iBAAiB;AAAA,IACnB;AAAA,EACF;AACA,QAAM,UAAU,0BAA0B;AAC1C,QAAM,UAAU,WAAW;AAC3B,QAAM,EAAE,MAAM,IAAI,aAAa;AAAA,IAC7B,MAAM,eAAe,CAAC;AAAA,IACtB;AAAA,IACA;AAAA,IACA,kBAAkB;AAAA,IAClB,UAAU,CAAC,KAAK,UAAU,IAAI,MAAM,GAAG,KAAK;AAAA,IAC5C,UAAU;AAAA,EACZ,CAAC;AACD,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,0BAAK,WAAW,EAAE,WAAW,gBAAgB,UAAU;AAAA,QAC5D,0BAAK,OAAO,EAAE,WAAW,+CAA+C,UAAU;AAAA,UAChF,0BAAK,OAAO,EAAE,UAAU;AAAA,YACtB,oBAAAC,KAAK,SAAS,EAAE,UAAU,EAAE,oBAAoB,EAAE,CAAC;AAAA,YACnD,oBAAAA,KAAK,MAAM,EAAE,WAAW,qBAAqB,MAAM,SAAS,UAAU,EAAE,sBAAsB,EAAE,CAAC;AAAA,MACnH,EAAE,CAAC;AAAA,UACa,oBAAAA,KAAK,MAAM,EAAE,IAAI,uBAAuB,cAA0B,oBAAAA,KAAK,QAAQ,EAAE,MAAM,SAAS,SAAS,aAAa,UAAU,EAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;AAAA,IAC1K,EAAE,CAAC;AAAA,QACa,oBAAAA;AAAA,MACd;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,QACA,UAAU;AAAA,QACV;AAAA,QACA;AAAA,QACA,SAAS;AAAA,UACP,EAAE,KAAK,SAAS,OAAO,EAAE,cAAc,EAAE;AAAA,UACzC,EAAE,KAAK,UAAU,OAAO,EAAE,eAAe,EAAE;AAAA,UAC3C,EAAE,KAAK,cAAc,OAAO,EAAE,kBAAkB,EAAE;AAAA,UAClD,EAAE,KAAK,cAAc,OAAO,EAAE,kBAAkB,EAAE;AAAA,QACpD;AAAA,QACA,QAAQ;AAAA,QACR,YAAY,CAAC,QAAQ,gBAAgB,IAAI,SAAS,EAAE;AAAA,QACpD,aAAa;AAAA,QACb;AAAA,MACF;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;AACA,IAAI,eAAe,mBAAmB;AACtC,IAAI,aAAa,MAAM;AACrB,QAAM,OAAO,0BAA0B;AACvC,aAAO;AAAA,IACL,MAAM;AAAA,MACJ,GAAG;AAAA,MACH,aAAa,QAAQ;AAAA,QACnB,IAAI;AAAA,QACJ,MAAM,CAAC,EAAE,IAAI,UAAsB,oBAAAA,KAAK,sBAAsB,EAAE,YAAY,IAAI,SAAS,CAAC;AAAA,MAC5F,CAAC;AAAA,IACH;AAAA,IACA,CAAC,IAAI;AAAA,EACP;AACF;AAIA,IAAI,iBAAiB,MAAM;AACzB,QAAM,EAAE,WAAW,IAAI,aAAa;AACpC,aAAuB,oBAAAC;AAAA,IACrB;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP,OAAO,WAAW,+BAA+B;AAAA,QACjD,QAAQ,WAAW,gCAAgC;AAAA,MACrD;AAAA,MACA,cAA0B,oBAAAA,KAAK,qBAAqB,CAAC,CAAC;AAAA,IACxD;AAAA,EACF;AACF;", "names": ["import_jsx_runtime", "jsx2", "jsx3"]}