import {
  getCountryProvinceObjectByIso2
} from "./chunk-KL72CHML.js";
import {
  PercentageInput
} from "./chunk-LFH2BKKX.js";
import {
  SwitchBox
} from "./chunk-4LGSZLHH.js";
import "./chunk-EGRHWZRV.js";
import {
  KeyboundForm
} from "./chunk-DP54EP6X.js";
import {
  RouteFocusModal,
  useRouteModal
} from "./chunk-MVVOBQIC.js";
import {
  t
} from "./chunk-WHQIBI5S.js";
import {
  z
} from "./chunk-4XXECALA.js";
import {
  Form,
  useForm
} from "./chunk-XXJU43CK.js";
import {
  useCreateTaxRegion,
  useTaxRegion
} from "./chunk-EZ62MM7J.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import "./chunk-JFCIYOH4.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-5GF3RGIE.js";
import {
  useParams
} from "./chunk-T7YBVUWZ.js";
import {
  Button,
  Heading,
  InformationCircleSolid,
  Input,
  Text,
  Tooltip,
  TrianglesMini,
  clx,
  toast
} from "./chunk-YP2LLXWB.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-ZSQ362B4.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/tax-region-province-create-FFXYN7M4.mjs
var import_react = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var ProvinceSelect = (0, import_react.forwardRef)(
  ({
    className,
    disabled,
    placeholder,
    country_code,
    valueAs = "iso_2",
    ...props
  }, ref) => {
    var _a;
    const { t: t2 } = useTranslation();
    const innerRef = (0, import_react.useRef)(null);
    (0, import_react.useImperativeHandle)(ref, () => innerRef.current);
    const isPlaceholder = ((_a = innerRef.current) == null ? void 0 : _a.value) === "";
    const provinceObject = getCountryProvinceObjectByIso2(country_code);
    if (!provinceObject) {
      disabled = true;
    }
    const options = Object.entries((provinceObject == null ? void 0 : provinceObject.options) ?? {}).map(
      ([iso2, name]) => {
        return (0, import_jsx_runtime.jsx)("option", { value: valueAs === "iso_2" ? iso2 : name, children: name }, iso2);
      }
    );
    const placeholderText = provinceObject ? t2(`taxRegions.fields.sublevels.placeholders.${provinceObject.type}`) : "";
    const placeholderOption = provinceObject ? (0, import_jsx_runtime.jsx)("option", { value: "", disabled: true, className: "text-ui-fg-muted", children: placeholder || placeholderText }) : null;
    return (0, import_jsx_runtime.jsxs)("div", { className: "relative", children: [
      (0, import_jsx_runtime.jsx)(
        TrianglesMini,
        {
          className: clx(
            "text-ui-fg-muted transition-fg pointer-events-none absolute right-2 top-1/2 -translate-y-1/2",
            {
              "text-ui-fg-disabled": disabled
            }
          )
        }
      ),
      (0, import_jsx_runtime.jsxs)(
        "select",
        {
          disabled,
          className: clx(
            "bg-ui-bg-field shadow-buttons-neutral transition-fg txt-compact-small flex w-full select-none appearance-none items-center justify-between rounded-md px-2 py-1.5 outline-none",
            "placeholder:text-ui-fg-muted text-ui-fg-base",
            "hover:bg-ui-bg-field-hover",
            "focus-visible:shadow-borders-interactive-with-active data-[state=open]:!shadow-borders-interactive-with-active",
            "aria-[invalid=true]:border-ui-border-error aria-[invalid=true]:shadow-borders-error",
            "invalid::border-ui-border-error invalid:shadow-borders-error",
            "disabled:!bg-ui-bg-disabled disabled:!text-ui-fg-disabled",
            {
              "text-ui-fg-muted": isPlaceholder
            },
            className
          ),
          ...props,
          ref: innerRef,
          children: [
            placeholderOption,
            options
          ]
        }
      )
    ] });
  }
);
ProvinceSelect.displayName = "CountrySelect";
var CreateTaxRegionProvinceSchema = z.object({
  province_code: z.string().min(1),
  name: z.string().optional(),
  code: z.string().min(1),
  rate: z.object({
    float: z.number().optional(),
    value: z.string().optional()
  }).optional(),
  is_combinable: z.boolean().optional()
});
var TaxRegionProvinceCreateForm = ({
  parent
}) => {
  const { t: t2 } = useTranslation();
  const { handleSuccess } = useRouteModal();
  const form = useForm({
    defaultValues: {
      province_code: "",
      code: "",
      is_combinable: false,
      name: "",
      rate: {
        value: ""
      }
    },
    resolver: t(CreateTaxRegionProvinceSchema)
  });
  const { mutateAsync, isPending } = useCreateTaxRegion();
  const handleSubmit = form.handleSubmit(async (values) => {
    var _a;
    const defaultRate = values.name && ((_a = values.rate) == null ? void 0 : _a.float) ? {
      name: values.name,
      rate: values.rate.float,
      code: values.code,
      is_combinable: values.is_combinable
    } : void 0;
    await mutateAsync(
      {
        country_code: parent.country_code,
        province_code: values.province_code,
        parent_id: parent.id,
        default_tax_rate: defaultRate
      },
      {
        onSuccess: ({ tax_region }) => {
          toast.success(t2("taxRegions.create.successToast"));
          handleSuccess(
            `/settings/tax-regions/${parent.id}/provinces/${tax_region.id}`
          );
        },
        onError: (error) => {
          toast.error(error.message);
        }
      }
    );
  });
  const countryProvinceObject = getCountryProvinceObjectByIso2(
    parent.country_code
  );
  const type = (countryProvinceObject == null ? void 0 : countryProvinceObject.type) || "sublevel";
  const label = t2(`taxRegions.fields.sublevels.labels.${type}`);
  return (0, import_jsx_runtime2.jsx)(RouteFocusModal.Form, { form, children: (0, import_jsx_runtime2.jsxs)(
    KeyboundForm,
    {
      onSubmit: handleSubmit,
      className: "flex h-full flex-col overflow-hidden",
      children: [
        (0, import_jsx_runtime2.jsx)(RouteFocusModal.Header, {}),
        (0, import_jsx_runtime2.jsx)(RouteFocusModal.Body, { className: "flex flex-1 flex-col overflow-hidden", children: (0, import_jsx_runtime2.jsx)("div", { className: "flex flex-1 flex-col items-center overflow-y-auto", children: (0, import_jsx_runtime2.jsxs)("div", { className: "flex w-full max-w-[720px] flex-col gap-y-8 px-2 py-16", children: [
          (0, import_jsx_runtime2.jsxs)("div", { children: [
            (0, import_jsx_runtime2.jsx)(Heading, { children: t2(`taxRegions.${type}.create.header`) }),
            (0, import_jsx_runtime2.jsx)(Text, { size: "small", className: "text-ui-fg-subtle", children: t2(`taxRegions.${type}.create.hint`) })
          ] }),
          (0, import_jsx_runtime2.jsx)("div", { className: "grid gap-4 md:grid-cols-2", children: (0, import_jsx_runtime2.jsx)(
            Form.Field,
            {
              control: form.control,
              name: "province_code",
              render: ({ field }) => {
                return (0, import_jsx_runtime2.jsxs)(Form.Item, { children: [
                  (0, import_jsx_runtime2.jsx)(
                    Form.Label,
                    {
                      tooltip: !countryProvinceObject && t2("taxRegions.fields.sublevels.tooltips.sublevel"),
                      children: label
                    }
                  ),
                  (0, import_jsx_runtime2.jsx)(Form.Control, { children: countryProvinceObject ? (0, import_jsx_runtime2.jsx)(
                    ProvinceSelect,
                    {
                      country_code: parent.country_code,
                      ...field
                    }
                  ) : (0, import_jsx_runtime2.jsx)(Input, { ...field, placeholder: "KR-26" }) }),
                  (0, import_jsx_runtime2.jsx)(Form.ErrorMessage, {})
                ] });
              }
            }
          ) }),
          (0, import_jsx_runtime2.jsxs)("div", { className: "flex flex-col gap-4", children: [
            (0, import_jsx_runtime2.jsxs)("div", { className: "flex items-center gap-x-1", children: [
              (0, import_jsx_runtime2.jsx)(Heading, { level: "h2", className: "!txt-compact-small-plus", children: t2("taxRegions.fields.defaultTaxRate.label") }),
              (0, import_jsx_runtime2.jsxs)(
                Text,
                {
                  size: "small",
                  leading: "compact",
                  className: "text-ui-fg-muted",
                  children: [
                    "(",
                    t2("fields.optional"),
                    ")"
                  ]
                }
              ),
              (0, import_jsx_runtime2.jsx)(
                Tooltip,
                {
                  content: t2("taxRegions.fields.defaultTaxRate.tooltip"),
                  children: (0, import_jsx_runtime2.jsx)(InformationCircleSolid, { className: "text-ui-fg-muted" })
                }
              )
            ] }),
            (0, import_jsx_runtime2.jsxs)("div", { className: "grid grid-cols-1 gap-4 md:grid-cols-2", children: [
              (0, import_jsx_runtime2.jsx)(
                Form.Field,
                {
                  control: form.control,
                  name: "name",
                  render: ({ field }) => {
                    return (0, import_jsx_runtime2.jsxs)(Form.Item, { children: [
                      (0, import_jsx_runtime2.jsx)(Form.Label, { children: t2("fields.name") }),
                      (0, import_jsx_runtime2.jsx)(Form.Control, { children: (0, import_jsx_runtime2.jsx)(Input, { ...field }) }),
                      (0, import_jsx_runtime2.jsx)(Form.ErrorMessage, {})
                    ] });
                  }
                }
              ),
              (0, import_jsx_runtime2.jsx)(
                Form.Field,
                {
                  control: form.control,
                  name: "rate",
                  render: ({ field: { value, onChange, ...field } }) => {
                    return (0, import_jsx_runtime2.jsxs)(Form.Item, { children: [
                      (0, import_jsx_runtime2.jsx)(Form.Label, { children: t2("taxRegions.fields.taxRate") }),
                      (0, import_jsx_runtime2.jsx)(Form.Control, { children: (0, import_jsx_runtime2.jsx)(
                        PercentageInput,
                        {
                          ...field,
                          value: value == null ? void 0 : value.value,
                          onValueChange: (value2, _name, values) => onChange({
                            value: value2,
                            float: values == null ? void 0 : values.float
                          })
                        }
                      ) }),
                      (0, import_jsx_runtime2.jsx)(Form.ErrorMessage, {})
                    ] });
                  }
                }
              ),
              (0, import_jsx_runtime2.jsx)(
                Form.Field,
                {
                  control: form.control,
                  name: "code",
                  render: ({ field }) => {
                    return (0, import_jsx_runtime2.jsxs)(Form.Item, { children: [
                      (0, import_jsx_runtime2.jsx)(Form.Label, { children: t2("taxRegions.fields.taxCode") }),
                      (0, import_jsx_runtime2.jsx)(Form.Control, { children: (0, import_jsx_runtime2.jsx)(Input, { ...field }) }),
                      (0, import_jsx_runtime2.jsx)(Form.ErrorMessage, {})
                    ] });
                  }
                }
              )
            ] })
          ] }),
          (0, import_jsx_runtime2.jsx)(
            SwitchBox,
            {
              control: form.control,
              name: "is_combinable",
              label: t2("taxRegions.fields.isCombinable.label"),
              description: t2("taxRegions.fields.isCombinable.hint")
            }
          )
        ] }) }) }),
        (0, import_jsx_runtime2.jsx)(RouteFocusModal.Footer, { children: (0, import_jsx_runtime2.jsxs)("div", { className: "flex items-center justify-end gap-x-2", children: [
          (0, import_jsx_runtime2.jsx)(RouteFocusModal.Close, { asChild: true, children: (0, import_jsx_runtime2.jsx)(Button, { size: "small", variant: "secondary", children: t2("actions.cancel") }) }),
          (0, import_jsx_runtime2.jsx)(Button, { size: "small", type: "submit", isLoading: isPending, children: t2("actions.save") })
        ] }) })
      ]
    }
  ) });
};
var TaxProvinceCreate = () => {
  const { id } = useParams();
  const { tax_region, isPending, isError, error } = useTaxRegion(id);
  const ready = !isPending && !!tax_region;
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime3.jsx)(RouteFocusModal, { children: ready && (0, import_jsx_runtime3.jsx)(TaxRegionProvinceCreateForm, { parent: tax_region }) });
};
export {
  TaxProvinceCreate as Component,
  TaxProvinceCreate
};
//# sourceMappingURL=tax-region-province-create-FFXYN7M4-VDAKZRFP.js.map
