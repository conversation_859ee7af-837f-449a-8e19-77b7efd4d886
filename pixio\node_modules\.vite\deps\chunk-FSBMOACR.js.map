{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-FMEOUN2H.mjs"], "sourcesContent": ["import {\n  useDate\n} from \"./chunk-KSV3NQOT.mjs\";\n\n// src/components/data-table/helpers/general/use-data-table-date-columns.tsx\nimport {\n  createDataTableColumnHelper,\n  Tooltip\n} from \"@medusajs/ui\";\nimport { useMemo } from \"react\";\nimport { useTranslation } from \"react-i18next\";\nimport { jsx } from \"react/jsx-runtime\";\nvar columnHelper = createDataTableColumnHelper();\nvar useDataTableDateColumns = () => {\n  const { t } = useTranslation();\n  const { getFullDate } = useDate();\n  return useMemo(() => {\n    return [\n      columnHelper.accessor(\"created_at\", {\n        header: t(\"fields.createdAt\"),\n        cell: ({ row }) => {\n          return /* @__PURE__ */ jsx(\n            Tooltip,\n            {\n              content: getFullDate({\n                date: row.original.created_at,\n                includeTime: true\n              }),\n              children: /* @__PURE__ */ jsx(\"span\", { children: getFullDate({ date: row.original.created_at }) })\n            }\n          );\n        },\n        enableSorting: true,\n        sortAscLabel: t(\"filters.sorting.dateAsc\"),\n        sortDescLabel: t(\"filters.sorting.dateDesc\")\n      }),\n      columnHelper.accessor(\"updated_at\", {\n        header: t(\"fields.updatedAt\"),\n        cell: ({ row }) => {\n          return /* @__PURE__ */ jsx(\n            Tooltip,\n            {\n              content: getFullDate({\n                date: row.original.updated_at,\n                includeTime: true\n              }),\n              children: /* @__PURE__ */ jsx(\"span\", { children: getFullDate({ date: row.original.updated_at }) })\n            }\n          );\n        },\n        enableSorting: true,\n        sortAscLabel: t(\"filters.sorting.dateAsc\"),\n        sortDescLabel: t(\"filters.sorting.dateDesc\")\n      })\n    ];\n  }, [t, getFullDate]);\n};\n\nexport {\n  useDataTableDateColumns\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AASA,mBAAwB;AAExB,yBAAoB;AACpB,IAAI,eAAe,4BAA4B;AAC/C,IAAI,0BAA0B,MAAM;AAClC,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,QAAM,EAAE,YAAY,IAAI,QAAQ;AAChC,aAAO,sBAAQ,MAAM;AACnB,WAAO;AAAA,MACL,aAAa,SAAS,cAAc;AAAA,QAClC,QAAQ,EAAE,kBAAkB;AAAA,QAC5B,MAAM,CAAC,EAAE,IAAI,MAAM;AACjB,qBAAuB;AAAA,YACrB;AAAA,YACA;AAAA,cACE,SAAS,YAAY;AAAA,gBACnB,MAAM,IAAI,SAAS;AAAA,gBACnB,aAAa;AAAA,cACf,CAAC;AAAA,cACD,cAA0B,wBAAI,QAAQ,EAAE,UAAU,YAAY,EAAE,MAAM,IAAI,SAAS,WAAW,CAAC,EAAE,CAAC;AAAA,YACpG;AAAA,UACF;AAAA,QACF;AAAA,QACA,eAAe;AAAA,QACf,cAAc,EAAE,yBAAyB;AAAA,QACzC,eAAe,EAAE,0BAA0B;AAAA,MAC7C,CAAC;AAAA,MACD,aAAa,SAAS,cAAc;AAAA,QAClC,QAAQ,EAAE,kBAAkB;AAAA,QAC5B,MAAM,CAAC,EAAE,IAAI,MAAM;AACjB,qBAAuB;AAAA,YACrB;AAAA,YACA;AAAA,cACE,SAAS,YAAY;AAAA,gBACnB,MAAM,IAAI,SAAS;AAAA,gBACnB,aAAa;AAAA,cACf,CAAC;AAAA,cACD,cAA0B,wBAAI,QAAQ,EAAE,UAAU,YAAY,EAAE,MAAM,IAAI,SAAS,WAAW,CAAC,EAAE,CAAC;AAAA,YACpG;AAAA,UACF;AAAA,QACF;AAAA,QACA,eAAe;AAAA,QACf,cAAc,EAAE,yBAAyB;AAAA,QACzC,eAAe,EAAE,0BAA0B;AAAA,MAC7C,CAAC;AAAA,IACH;AAAA,EACF,GAAG,CAAC,GAAG,WAAW,CAAC;AACrB;", "names": []}