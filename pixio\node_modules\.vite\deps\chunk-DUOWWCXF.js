import {
  ordersQueryKeys
} from "./chunk-NIH7SAXN.js";
import {
  reservationItemsQueryKeys
} from "./chunk-UEJFWAFE.js";
import {
  inventoryItemsQueryKeys
} from "./chunk-AJYMIHLQ.js";
import {
  queryClient
} from "./chunk-VMLNCWLE.js";
import {
  sdk
} from "./chunk-JFCIYOH4.js";
import {
  useMutation
} from "./chunk-R35JBZ3G.js";

// node_modules/@medusajs/dashboard/dist/chunk-5CCKT6WV.mjs
var useCreateOrderEdit = (orderId, options) => {
  return useMutation({
    mutationFn: (payload) => sdk.admin.orderEdit.initiateRequest(payload),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({
        queryKey: ordersQueryKeys.details()
      });
      queryClient.invalidateQueries({
        queryKey: ordersQueryKeys.preview(orderId)
      });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useRequestOrderEdit = (id, options) => {
  return useMutation({
    mutationFn: () => sdk.admin.orderEdit.request(id),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({
        queryKey: ordersQueryKeys.details()
      });
      queryClient.invalidateQueries({
        queryKey: ordersQueryKeys.preview(id)
      });
      queryClient.invalidateQueries({
        queryKey: ordersQueryKeys.changes(id)
      });
      queryClient.invalidateQueries({
        queryKey: ordersQueryKeys.lineItems(id)
      });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useConfirmOrderEdit = (id, options) => {
  return useMutation({
    mutationFn: () => sdk.admin.orderEdit.confirm(id),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({
        queryKey: ordersQueryKeys.details()
      });
      queryClient.invalidateQueries({
        queryKey: ordersQueryKeys.preview(id)
      });
      queryClient.invalidateQueries({
        queryKey: ordersQueryKeys.changes(id)
      });
      queryClient.invalidateQueries({
        queryKey: ordersQueryKeys.lineItems(id)
      });
      queryClient.invalidateQueries({
        queryKey: reservationItemsQueryKeys.lists()
      });
      queryClient.invalidateQueries({
        queryKey: inventoryItemsQueryKeys.lists()
      });
      queryClient.invalidateQueries({
        queryKey: inventoryItemsQueryKeys.details()
      });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useCancelOrderEdit = (orderId, options) => {
  return useMutation({
    mutationFn: () => sdk.admin.orderEdit.cancelRequest(orderId),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({
        queryKey: ordersQueryKeys.details()
      });
      queryClient.invalidateQueries({
        queryKey: ordersQueryKeys.preview(orderId)
      });
      queryClient.invalidateQueries({
        queryKey: ordersQueryKeys.changes(orderId)
      });
      queryClient.invalidateQueries({
        queryKey: ordersQueryKeys.lineItems(orderId)
      });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useAddOrderEditItems = (id, options) => {
  return useMutation({
    mutationFn: (payload) => sdk.admin.orderEdit.addItems(id, payload),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({
        queryKey: ordersQueryKeys.preview(id)
      });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useUpdateOrderEditOriginalItem = (id, options) => {
  return useMutation({
    mutationFn: ({
      itemId,
      ...payload
    }) => {
      return sdk.admin.orderEdit.updateOriginalItem(id, itemId, payload);
    },
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({
        queryKey: ordersQueryKeys.preview(id)
      });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useUpdateOrderEditAddedItem = (id, options) => {
  return useMutation({
    mutationFn: ({
      actionId,
      ...payload
    }) => {
      return sdk.admin.orderEdit.updateAddedItem(id, actionId, payload);
    },
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({
        queryKey: ordersQueryKeys.preview(id)
      });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useRemoveOrderEditItem = (id, options) => {
  return useMutation({
    mutationFn: (actionId) => sdk.admin.orderEdit.removeAddedItem(id, actionId),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({
        queryKey: ordersQueryKeys.preview(id)
      });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};

export {
  useCreateOrderEdit,
  useRequestOrderEdit,
  useConfirmOrderEdit,
  useCancelOrderEdit,
  useAddOrderEditItems,
  useUpdateOrderEditOriginalItem,
  useUpdateOrderEditAddedItem,
  useRemoveOrderEditItem
};
//# sourceMappingURL=chunk-DUOWWCXF.js.map
