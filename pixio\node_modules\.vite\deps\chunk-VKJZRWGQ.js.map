{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-KESQIJZQ.mjs"], "sourcesContent": ["import {\n  TaxRateRuleReferenceType\n} from \"./chunk-V3MOBCDF.mjs\";\nimport {\n  LocalizedTablePagination,\n  useDeleteTaxRateAction\n} from \"./chunk-4FUGAJJD.mjs\";\nimport {\n  formatPercentage\n} from \"./chunk-3WXBLS2P.mjs\";\nimport {\n  DataTableSearch\n} from \"./chunk-YEDAFXMB.mjs\";\nimport {\n  DataTableOrderBy\n} from \"./chunk-AOFGTNG6.mjs\";\nimport {\n  NoRecords,\n  NoResults\n} from \"./chunk-EMIHDNB7.mjs\";\nimport {\n  TableFooterSkeleton\n} from \"./chunk-LPEUYMRK.mjs\";\nimport {\n  ActionMenu\n} from \"./chunk-3NJTXRIY.mjs\";\nimport {\n  useProductTypes\n} from \"./chunk-B4GODIOW.mjs\";\nimport {\n  useProducts\n} from \"./chunk-6I62UDJA.mjs\";\n\n// src/routes/tax-regions/common/components/tax-rate-line/tax-rate-line.tsx\nimport { PencilSquare, Trash } from \"@medusajs/icons\";\nimport { StatusBadge, Text } from \"@medusajs/ui\";\nimport { useTranslation } from \"react-i18next\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar TaxRateLine = ({\n  taxRate,\n  isSublevelTaxRate\n}) => {\n  const { t } = useTranslation();\n  return /* @__PURE__ */ jsxs(\"div\", { className: \"text-ui-fg-subtle grid grid-cols-[1fr_1fr_auto] items-center gap-4 px-6 py-4\", children: [\n    /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center gap-x-1.5\", children: [\n      /* @__PURE__ */ jsx(Text, { size: \"small\", weight: \"plus\", leading: \"compact\", children: taxRate.name }),\n      taxRate.code && /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center gap-x-1.5\", children: [\n        /* @__PURE__ */ jsx(Text, { size: \"small\", leading: \"compact\", children: \"\\xB7\" }),\n        /* @__PURE__ */ jsx(Text, { size: \"small\", leading: \"compact\", children: taxRate.code })\n      ] })\n    ] }),\n    /* @__PURE__ */ jsx(Text, { size: \"small\", leading: \"compact\", children: formatPercentage(taxRate.rate) }),\n    /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center justify-end gap-x-2\", children: [\n      isSublevelTaxRate && /* @__PURE__ */ jsx(StatusBadge, { color: taxRate.is_combinable ? \"green\" : \"grey\", children: taxRate.is_combinable ? t(\"taxRegions.fields.isCombinable.true\") : t(\"taxRegions.fields.isCombinable.false\") }),\n      /* @__PURE__ */ jsx(TaxRateActions, { taxRate })\n    ] })\n  ] });\n};\nvar TaxRateActions = ({ taxRate }) => {\n  const { t } = useTranslation();\n  const handleDelete = useDeleteTaxRateAction(taxRate);\n  return /* @__PURE__ */ jsx(\n    ActionMenu,\n    {\n      groups: [\n        {\n          actions: [\n            {\n              label: t(\"actions.edit\"),\n              icon: /* @__PURE__ */ jsx(PencilSquare, {}),\n              to: `tax-rates/${taxRate.id}/edit`\n            }\n          ]\n        },\n        {\n          actions: [\n            {\n              label: t(\"actions.delete\"),\n              icon: /* @__PURE__ */ jsx(Trash, {}),\n              onClick: handleDelete\n            }\n          ]\n        }\n      ]\n    }\n  );\n};\n\n// src/routes/tax-regions/common/components/tax-override-table/tax-override-table.tsx\nimport { Button } from \"@medusajs/ui\";\nimport { Link } from \"react-router-dom\";\n\n// src/routes/tax-regions/common/components/tax-override-card/tax-override-card.tsx\nimport {\n  ArrowDownRightMini,\n  PencilSquare as PencilSquare2,\n  Trash as Trash2,\n  TriangleRightMini\n} from \"@medusajs/icons\";\nimport {\n  Badge,\n  Divider,\n  IconButton,\n  StatusBadge as StatusBadge2,\n  Text as Text2,\n  Tooltip\n} from \"@medusajs/ui\";\nimport { Collapsible as RadixCollapsible } from \"radix-ui\";\nimport { useTranslation as useTranslation2 } from \"react-i18next\";\nimport { jsx as jsx2, jsxs as jsxs2 } from \"react/jsx-runtime\";\nvar TaxOverrideCard = ({ taxRate }) => {\n  const { t } = useTranslation2();\n  const handleDelete = useDeleteTaxRateAction(taxRate);\n  if (taxRate.is_default) {\n    return null;\n  }\n  const groupedRules = taxRate.rules.reduce(\n    (acc, rule) => {\n      if (!acc[rule.reference]) {\n        acc[rule.reference] = [];\n      }\n      acc[rule.reference].push(rule.reference_id);\n      return acc;\n    },\n    {}\n  );\n  const validKeys = Object.values(TaxRateRuleReferenceType);\n  const numberOfTargets = Object.keys(groupedRules).map(\n    (key) => validKeys.includes(key)\n  ).length;\n  return /* @__PURE__ */ jsxs2(RadixCollapsible.Root, { children: [\n    /* @__PURE__ */ jsxs2(\"div\", { className: \"flex items-center justify-between px-6 py-3\", children: [\n      /* @__PURE__ */ jsxs2(\"div\", { className: \"flex items-center gap-x-2\", children: [\n        /* @__PURE__ */ jsx2(RadixCollapsible.Trigger, { asChild: true, children: /* @__PURE__ */ jsx2(IconButton, { size: \"2xsmall\", variant: \"transparent\", className: \"group\", children: /* @__PURE__ */ jsx2(TriangleRightMini, { className: \"text-ui-fg-muted transition-transform group-data-[state='open']:rotate-90\" }) }) }),\n        /* @__PURE__ */ jsxs2(\"div\", { className: \"flex items-center gap-x-1.5\", children: [\n          /* @__PURE__ */ jsx2(Text2, { size: \"small\", weight: \"plus\", leading: \"compact\", children: taxRate.name }),\n          taxRate.code && /* @__PURE__ */ jsxs2(\"div\", { className: \"text-ui-fg-subtle flex items-center gap-x-1.5\", children: [\n            /* @__PURE__ */ jsx2(Text2, { size: \"small\", leading: \"compact\", children: \"\\xB7\" }),\n            /* @__PURE__ */ jsx2(Text2, { size: \"small\", leading: \"compact\", children: taxRate.code })\n          ] })\n        ] })\n      ] }),\n      /* @__PURE__ */ jsxs2(\"div\", { className: \"flex items-center gap-x-3\", children: [\n        /* @__PURE__ */ jsx2(Text2, { size: \"small\", leading: \"compact\", className: \"text-ui-fg-subtle\", children: t(\"taxRegions.fields.targets.numberOfTargets\", {\n          count: numberOfTargets\n        }) }),\n        /* @__PURE__ */ jsx2(\"div\", { className: \"bg-ui-border-base h-3 w-px\" }),\n        /* @__PURE__ */ jsx2(StatusBadge2, { color: taxRate.is_combinable ? \"green\" : \"grey\", children: taxRate.is_combinable ? t(\"taxRegions.fields.isCombinable.true\") : t(\"taxRegions.fields.isCombinable.false\") }),\n        /* @__PURE__ */ jsx2(\n          ActionMenu,\n          {\n            groups: [\n              {\n                actions: [\n                  {\n                    label: t(\"actions.edit\"),\n                    icon: /* @__PURE__ */ jsx2(PencilSquare2, {}),\n                    to: `overrides/${taxRate.id}/edit`\n                  }\n                ]\n              },\n              {\n                actions: [\n                  {\n                    label: t(\"actions.delete\"),\n                    icon: /* @__PURE__ */ jsx2(Trash2, {}),\n                    onClick: handleDelete\n                  }\n                ]\n              }\n            ]\n          }\n        )\n      ] })\n    ] }),\n    /* @__PURE__ */ jsx2(RadixCollapsible.Content, { children: /* @__PURE__ */ jsxs2(\"div\", { className: \"bg-ui-bg-subtle\", children: [\n      /* @__PURE__ */ jsx2(Divider, { variant: \"dashed\" }),\n      /* @__PURE__ */ jsx2(\"div\", { className: \"px-6 py-3\", children: /* @__PURE__ */ jsxs2(\"div\", { className: \"flex items-center gap-x-3\", children: [\n        /* @__PURE__ */ jsx2(\"div\", { className: \"text-ui-fg-muted flex size-5 items-center justify-center\", children: /* @__PURE__ */ jsx2(ArrowDownRightMini, {}) }),\n        /* @__PURE__ */ jsxs2(\"div\", { className: \"flex flex-wrap items-center gap-x-1.5 gap-y-2\", children: [\n          /* @__PURE__ */ jsx2(Badge, { size: \"2xsmall\", children: formatPercentage(taxRate.rate) }),\n          /* @__PURE__ */ jsx2(\n            Text2,\n            {\n              size: \"small\",\n              leading: \"compact\",\n              className: \"text-ui-fg-subtle\",\n              children: t(\"taxRegions.fields.targets.operators.on\")\n            }\n          ),\n          Object.entries(groupedRules).map(([reference, ids], index) => {\n            return /* @__PURE__ */ jsxs2(\n              \"div\",\n              {\n                className: \"flex items-center gap-x-1.5\",\n                children: [\n                  /* @__PURE__ */ jsx2(\n                    Reference,\n                    {\n                      reference,\n                      ids\n                    },\n                    reference\n                  ),\n                  index < Object.keys(groupedRules).length - 1 && /* @__PURE__ */ jsx2(\n                    Text2,\n                    {\n                      size: \"small\",\n                      leading: \"compact\",\n                      className: \"text-ui-fg-subtle\",\n                      children: t(\"taxRegions.fields.targets.operators.and\")\n                    }\n                  )\n                ]\n              },\n              reference\n            );\n          })\n        ] })\n      ] }) })\n    ] }) })\n  ] });\n};\nvar Reference = ({\n  reference,\n  ids\n}) => {\n  return /* @__PURE__ */ jsxs2(\"div\", { className: \"flex items-center gap-x-1.5\", children: [\n    /* @__PURE__ */ jsx2(ReferenceBadge, { reference }),\n    /* @__PURE__ */ jsx2(ReferenceValues, { type: reference, ids })\n  ] });\n};\nvar ReferenceBadge = ({\n  reference\n}) => {\n  const { t } = useTranslation2();\n  let label = null;\n  switch (reference) {\n    case \"product\" /* PRODUCT */:\n      label = t(\"taxRegions.fields.targets.tags.product\");\n      break;\n    case \"product_type\" /* PRODUCT_TYPE */:\n      label = t(\"taxRegions.fields.targets.tags.productType\");\n      break;\n  }\n  if (!label) {\n    return null;\n  }\n  return /* @__PURE__ */ jsx2(Badge, { size: \"2xsmall\", children: label });\n};\nvar ReferenceValues = ({\n  type,\n  ids\n}) => {\n  const { t } = useTranslation2();\n  const { isPending, additional, labels, isError, error } = useReferenceValues(\n    type,\n    ids\n  );\n  if (isError) {\n    throw error;\n  }\n  if (isPending) {\n    return /* @__PURE__ */ jsx2(\"div\", { className: \"bg-ui-tag-neutral-bg border-ui-tag-neutral-border h-5 w-14 animate-pulse rounded-md\" });\n  }\n  return /* @__PURE__ */ jsx2(\n    Tooltip,\n    {\n      content: /* @__PURE__ */ jsxs2(\"ul\", { children: [\n        labels?.map((label, index) => /* @__PURE__ */ jsx2(\"li\", { children: label }, index)),\n        additional > 0 && /* @__PURE__ */ jsx2(\"li\", { children: t(\"taxRegions.fields.targets.additionalValues\", {\n          count: additional\n        }) })\n      ] }),\n      children: /* @__PURE__ */ jsx2(Badge, { size: \"2xsmall\", children: t(\"taxRegions.fields.targets.values\", {\n        count: ids.length\n      }) })\n    }\n  );\n};\nvar useReferenceValues = (type, ids) => {\n  const products = useProducts(\n    {\n      id: ids,\n      limit: 10\n    },\n    {\n      enabled: !!ids.length && type === \"product\" /* PRODUCT */\n    }\n  );\n  const productTypes = useProductTypes(\n    {\n      id: ids,\n      limit: 10\n    },\n    {\n      enabled: !!ids.length && type === \"product_type\" /* PRODUCT_TYPE */\n    }\n  );\n  switch (type) {\n    case \"product\" /* PRODUCT */:\n      return {\n        labels: products.products?.map((product) => product.title),\n        isPending: products.isPending,\n        additional: products.products && products.count ? products.count - products.products.length : 0,\n        isError: products.isError,\n        error: products.error\n      };\n    case \"product_type\" /* PRODUCT_TYPE */:\n      return {\n        labels: productTypes.product_types?.map((type2) => type2.value),\n        isPending: productTypes.isPending,\n        additional: productTypes.product_types && productTypes.count ? productTypes.count - productTypes.product_types.length : 0,\n        isError: productTypes.isError,\n        error: productTypes.error\n      };\n  }\n};\n\n// src/routes/tax-regions/common/components/tax-override-table/tax-override-table.tsx\nimport { jsx as jsx3, jsxs as jsxs3 } from \"react/jsx-runtime\";\nvar TaxOverrideTable = ({\n  isPending,\n  action,\n  count = 0,\n  table,\n  queryObject,\n  prefix,\n  children\n}) => {\n  if (isPending) {\n    return /* @__PURE__ */ jsxs3(\"div\", { className: \"flex flex-col divide-y\", children: [\n      Array.from({ length: 3 }).map((_, index) => {\n        return /* @__PURE__ */ jsx3(\n          \"div\",\n          {\n            className: \"bg-ui-bg-field-component h-[52px] w-full animate-pulse\"\n          },\n          index\n        );\n      }),\n      /* @__PURE__ */ jsx3(TableFooterSkeleton, { layout: \"fit\" })\n    ] });\n  }\n  const noQuery = Object.values(queryObject).filter((v) => Boolean(v)).length === 0;\n  const noResults = !isPending && count === 0 && !noQuery;\n  const noRecords = !isPending && count === 0 && noQuery;\n  const { pageIndex, pageSize } = table.getState().pagination;\n  return /* @__PURE__ */ jsxs3(\"div\", { className: \"flex flex-col divide-y\", children: [\n    /* @__PURE__ */ jsxs3(\"div\", { className: \"flex flex-col justify-between gap-x-4 gap-y-3 px-6 py-4 md:flex-row md:items-center\", children: [\n      /* @__PURE__ */ jsx3(\"div\", { children }),\n      /* @__PURE__ */ jsxs3(\"div\", { className: \"flex items-center gap-x-2\", children: [\n        !noRecords && /* @__PURE__ */ jsxs3(\"div\", { className: \"flex w-full items-center gap-x-2 md:w-fit\", children: [\n          /* @__PURE__ */ jsx3(\"div\", { className: \"w-full md:w-fit\", children: /* @__PURE__ */ jsx3(DataTableSearch, { prefix }) }),\n          /* @__PURE__ */ jsx3(\n            DataTableOrderBy,\n            {\n              keys: [\"name\", \"rate\", \"code\", \"updated_at\", \"created_at\"],\n              prefix\n            }\n          )\n        ] }),\n        /* @__PURE__ */ jsx3(Link, { to: action.to, children: /* @__PURE__ */ jsx3(Button, { size: \"small\", variant: \"secondary\", children: action.label }) })\n      ] })\n    ] }),\n    noResults && /* @__PURE__ */ jsx3(NoResults, {}),\n    noRecords && /* @__PURE__ */ jsx3(NoRecords, {}),\n    !noRecords && !noResults ? !isPending ? table.getRowModel().rows.map((row) => {\n      return /* @__PURE__ */ jsx3(\n        TaxOverrideCard,\n        {\n          taxRate: row.original,\n          role: \"row\",\n          \"aria-rowindex\": row.index\n        },\n        row.id\n      );\n    }) : Array.from({ length: 3 }).map((_, index) => {\n      return /* @__PURE__ */ jsx3(\n        \"div\",\n        {\n          className: \"bg-ui-bg-field-component h-[60px] w-full animate-pulse\"\n        },\n        index\n      );\n    }) : null,\n    !noRecords && /* @__PURE__ */ jsx3(\n      LocalizedTablePagination,\n      {\n        prefix,\n        canNextPage: table.getCanNextPage(),\n        canPreviousPage: table.getCanPreviousPage(),\n        count,\n        nextPage: table.nextPage,\n        previousPage: table.previousPage,\n        pageCount: table.getPageCount(),\n        pageIndex,\n        pageSize\n      }\n    )\n  ] });\n};\n\n// src/routes/tax-regions/common/hooks/use-tax-override-table.tsx\nimport {\n  getCoreRowModel,\n  getPaginationRowModel,\n  useReactTable\n} from \"@tanstack/react-table\";\nimport { useEffect, useMemo, useState } from \"react\";\nimport { useSearchParams } from \"react-router-dom\";\nvar useTaxOverrideTable = ({\n  data = [],\n  count = 0,\n  pageSize: _pageSize = 10,\n  prefix\n}) => {\n  const [searchParams, setSearchParams] = useSearchParams();\n  const offsetKey = `${prefix ? `${prefix}_` : \"\"}offset`;\n  const offset = searchParams.get(offsetKey);\n  const [{ pageIndex, pageSize }, setPagination] = useState({\n    pageIndex: offset ? Math.ceil(Number(offset) / _pageSize) : 0,\n    pageSize: _pageSize\n  });\n  const pagination = useMemo(\n    () => ({\n      pageIndex,\n      pageSize\n    }),\n    [pageIndex, pageSize]\n  );\n  useEffect(() => {\n    const index = offset ? Math.ceil(Number(offset) / _pageSize) : 0;\n    if (index === pageIndex) {\n      return;\n    }\n    setPagination((prev) => ({\n      ...prev,\n      pageIndex: index\n    }));\n  }, [offset, _pageSize, pageIndex]);\n  const onPaginationChange = (updater) => {\n    const state = updater(pagination);\n    const { pageIndex: pageIndex2, pageSize: pageSize2 } = state;\n    setSearchParams((prev) => {\n      if (!pageIndex2) {\n        prev.delete(offsetKey);\n        return prev;\n      }\n      const newSearch = new URLSearchParams(prev);\n      newSearch.set(offsetKey, String(pageIndex2 * pageSize2));\n      return newSearch;\n    });\n    setPagination(state);\n    return state;\n  };\n  const table = useReactTable({\n    data,\n    columns: [],\n    // We don't actually want to render any columns\n    pageCount: Math.ceil(count / pageSize),\n    state: {\n      pagination\n    },\n    getCoreRowModel: getCoreRowModel(),\n    onPaginationChange,\n    getPaginationRowModel: getPaginationRowModel(),\n    manualPagination: true\n  });\n  return {\n    table\n  };\n};\n\nexport {\n  TaxRateLine,\n  TaxOverrideTable,\n  useTaxOverrideTable\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqCA,yBAA0B;AAwE1B,IAAAA,sBAA2C;AAmN3C,IAAAA,sBAA2C;AAyF3C,mBAA6C;AAnX7C,IAAI,cAAc,CAAC;AAAA,EACjB;AAAA,EACA;AACF,MAAM;AACJ,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,aAAuB,yBAAK,OAAO,EAAE,WAAW,gFAAgF,UAAU;AAAA,QACxH,yBAAK,OAAO,EAAE,WAAW,+BAA+B,UAAU;AAAA,UAChE,wBAAI,MAAM,EAAE,MAAM,SAAS,QAAQ,QAAQ,SAAS,WAAW,UAAU,QAAQ,KAAK,CAAC;AAAA,MACvG,QAAQ,YAAwB,yBAAK,OAAO,EAAE,WAAW,+BAA+B,UAAU;AAAA,YAChF,wBAAI,MAAM,EAAE,MAAM,SAAS,SAAS,WAAW,UAAU,IAAO,CAAC;AAAA,YACjE,wBAAI,MAAM,EAAE,MAAM,SAAS,SAAS,WAAW,UAAU,QAAQ,KAAK,CAAC;AAAA,MACzF,EAAE,CAAC;AAAA,IACL,EAAE,CAAC;AAAA,QACa,wBAAI,MAAM,EAAE,MAAM,SAAS,SAAS,WAAW,UAAU,iBAAiB,QAAQ,IAAI,EAAE,CAAC;AAAA,QACzF,yBAAK,OAAO,EAAE,WAAW,yCAAyC,UAAU;AAAA,MAC1F,yBAAqC,wBAAI,aAAa,EAAE,OAAO,QAAQ,gBAAgB,UAAU,QAAQ,UAAU,QAAQ,gBAAgB,EAAE,qCAAqC,IAAI,EAAE,sCAAsC,EAAE,CAAC;AAAA,UACjN,wBAAI,gBAAgB,EAAE,QAAQ,CAAC;AAAA,IACjD,EAAE,CAAC;AAAA,EACL,EAAE,CAAC;AACL;AACA,IAAI,iBAAiB,CAAC,EAAE,QAAQ,MAAM;AACpC,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,QAAM,eAAe,uBAAuB,OAAO;AACnD,aAAuB;AAAA,IACrB;AAAA,IACA;AAAA,MACE,QAAQ;AAAA,QACN;AAAA,UACE,SAAS;AAAA,YACP;AAAA,cACE,OAAO,EAAE,cAAc;AAAA,cACvB,UAAsB,wBAAI,cAAc,CAAC,CAAC;AAAA,cAC1C,IAAI,aAAa,QAAQ,EAAE;AAAA,YAC7B;AAAA,UACF;AAAA,QACF;AAAA,QACA;AAAA,UACE,SAAS;AAAA,YACP;AAAA,cACE,OAAO,EAAE,gBAAgB;AAAA,cACzB,UAAsB,wBAAI,OAAO,CAAC,CAAC;AAAA,cACnC,SAAS;AAAA,YACX;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAwBA,IAAI,kBAAkB,CAAC,EAAE,QAAQ,MAAM;AACrC,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,QAAM,eAAe,uBAAuB,OAAO;AACnD,MAAI,QAAQ,YAAY;AACtB,WAAO;AAAA,EACT;AACA,QAAM,eAAe,QAAQ,MAAM;AAAA,IACjC,CAAC,KAAK,SAAS;AACb,UAAI,CAAC,IAAI,KAAK,SAAS,GAAG;AACxB,YAAI,KAAK,SAAS,IAAI,CAAC;AAAA,MACzB;AACA,UAAI,KAAK,SAAS,EAAE,KAAK,KAAK,YAAY;AAC1C,aAAO;AAAA,IACT;AAAA,IACA,CAAC;AAAA,EACH;AACA,QAAM,YAAY,OAAO,OAAO,wBAAwB;AACxD,QAAM,kBAAkB,OAAO,KAAK,YAAY,EAAE;AAAA,IAChD,CAAC,QAAQ,UAAU,SAAS,GAAG;AAAA,EACjC,EAAE;AACF,aAAuB,oBAAAC,MAAM,aAAiB,MAAM,EAAE,UAAU;AAAA,QAC9C,oBAAAA,MAAM,OAAO,EAAE,WAAW,+CAA+C,UAAU;AAAA,UACjF,oBAAAA,MAAM,OAAO,EAAE,WAAW,6BAA6B,UAAU;AAAA,YAC/D,oBAAAC,KAAK,aAAiB,SAAS,EAAE,SAAS,MAAM,cAA0B,oBAAAA,KAAK,YAAY,EAAE,MAAM,WAAW,SAAS,eAAe,WAAW,SAAS,cAA0B,oBAAAA,KAAK,mBAAmB,EAAE,WAAW,4EAA4E,CAAC,EAAE,CAAC,EAAE,CAAC;AAAA,YAC5S,oBAAAD,MAAM,OAAO,EAAE,WAAW,+BAA+B,UAAU;AAAA,cACjE,oBAAAC,KAAK,MAAO,EAAE,MAAM,SAAS,QAAQ,QAAQ,SAAS,WAAW,UAAU,QAAQ,KAAK,CAAC;AAAA,UACzG,QAAQ,YAAwB,oBAAAD,MAAM,OAAO,EAAE,WAAW,iDAAiD,UAAU;AAAA,gBACnG,oBAAAC,KAAK,MAAO,EAAE,MAAM,SAAS,SAAS,WAAW,UAAU,IAAO,CAAC;AAAA,gBACnE,oBAAAA,KAAK,MAAO,EAAE,MAAM,SAAS,SAAS,WAAW,UAAU,QAAQ,KAAK,CAAC;AAAA,UAC3F,EAAE,CAAC;AAAA,QACL,EAAE,CAAC;AAAA,MACL,EAAE,CAAC;AAAA,UACa,oBAAAD,MAAM,OAAO,EAAE,WAAW,6BAA6B,UAAU;AAAA,YAC/D,oBAAAC,KAAK,MAAO,EAAE,MAAM,SAAS,SAAS,WAAW,WAAW,qBAAqB,UAAU,EAAE,6CAA6C;AAAA,UACxJ,OAAO;AAAA,QACT,CAAC,EAAE,CAAC;AAAA,YACY,oBAAAA,KAAK,OAAO,EAAE,WAAW,6BAA6B,CAAC;AAAA,YACvD,oBAAAA,KAAK,aAAc,EAAE,OAAO,QAAQ,gBAAgB,UAAU,QAAQ,UAAU,QAAQ,gBAAgB,EAAE,qCAAqC,IAAI,EAAE,sCAAsC,EAAE,CAAC;AAAA,YAC9L,oBAAAA;AAAA,UACd;AAAA,UACA;AAAA,YACE,QAAQ;AAAA,cACN;AAAA,gBACE,SAAS;AAAA,kBACP;AAAA,oBACE,OAAO,EAAE,cAAc;AAAA,oBACvB,UAAsB,oBAAAA,KAAK,cAAe,CAAC,CAAC;AAAA,oBAC5C,IAAI,aAAa,QAAQ,EAAE;AAAA,kBAC7B;AAAA,gBACF;AAAA,cACF;AAAA,cACA;AAAA,gBACE,SAAS;AAAA,kBACP;AAAA,oBACE,OAAO,EAAE,gBAAgB;AAAA,oBACzB,UAAsB,oBAAAA,KAAK,OAAQ,CAAC,CAAC;AAAA,oBACrC,SAAS;AAAA,kBACX;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF,EAAE,CAAC;AAAA,IACL,EAAE,CAAC;AAAA,QACa,oBAAAA,KAAK,aAAiB,SAAS,EAAE,cAA0B,oBAAAD,MAAM,OAAO,EAAE,WAAW,mBAAmB,UAAU;AAAA,UAChH,oBAAAC,KAAK,SAAS,EAAE,SAAS,SAAS,CAAC;AAAA,UACnC,oBAAAA,KAAK,OAAO,EAAE,WAAW,aAAa,cAA0B,oBAAAD,MAAM,OAAO,EAAE,WAAW,6BAA6B,UAAU;AAAA,YAC/H,oBAAAC,KAAK,OAAO,EAAE,WAAW,4DAA4D,cAA0B,oBAAAA,KAAK,oBAAoB,CAAC,CAAC,EAAE,CAAC;AAAA,YAC7I,oBAAAD,MAAM,OAAO,EAAE,WAAW,iDAAiD,UAAU;AAAA,cACnF,oBAAAC,KAAK,OAAO,EAAE,MAAM,WAAW,UAAU,iBAAiB,QAAQ,IAAI,EAAE,CAAC;AAAA,cACzE,oBAAAA;AAAA,YACd;AAAA,YACA;AAAA,cACE,MAAM;AAAA,cACN,SAAS;AAAA,cACT,WAAW;AAAA,cACX,UAAU,EAAE,wCAAwC;AAAA,YACtD;AAAA,UACF;AAAA,UACA,OAAO,QAAQ,YAAY,EAAE,IAAI,CAAC,CAAC,WAAW,GAAG,GAAG,UAAU;AAC5D,uBAAuB,oBAAAD;AAAA,cACrB;AAAA,cACA;AAAA,gBACE,WAAW;AAAA,gBACX,UAAU;AAAA,sBACQ,oBAAAC;AAAA,oBACd;AAAA,oBACA;AAAA,sBACE;AAAA,sBACA;AAAA,oBACF;AAAA,oBACA;AAAA,kBACF;AAAA,kBACA,QAAQ,OAAO,KAAK,YAAY,EAAE,SAAS,SAAqB,oBAAAA;AAAA,oBAC9D;AAAA,oBACA;AAAA,sBACE,MAAM;AAAA,sBACN,SAAS;AAAA,sBACT,WAAW;AAAA,sBACX,UAAU,EAAE,yCAAyC;AAAA,oBACvD;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAAA,cACA;AAAA,YACF;AAAA,UACF,CAAC;AAAA,QACH,EAAE,CAAC;AAAA,MACL,EAAE,CAAC,EAAE,CAAC;AAAA,IACR,EAAE,CAAC,EAAE,CAAC;AAAA,EACR,EAAE,CAAC;AACL;AACA,IAAI,YAAY,CAAC;AAAA,EACf;AAAA,EACA;AACF,MAAM;AACJ,aAAuB,oBAAAD,MAAM,OAAO,EAAE,WAAW,+BAA+B,UAAU;AAAA,QACxE,oBAAAC,KAAK,gBAAgB,EAAE,UAAU,CAAC;AAAA,QAClC,oBAAAA,KAAK,iBAAiB,EAAE,MAAM,WAAW,IAAI,CAAC;AAAA,EAChE,EAAE,CAAC;AACL;AACA,IAAI,iBAAiB,CAAC;AAAA,EACpB;AACF,MAAM;AACJ,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,MAAI,QAAQ;AACZ,UAAQ,WAAW;AAAA,IACjB,KAAK;AACH,cAAQ,EAAE,wCAAwC;AAClD;AAAA,IACF,KAAK;AACH,cAAQ,EAAE,4CAA4C;AACtD;AAAA,EACJ;AACA,MAAI,CAAC,OAAO;AACV,WAAO;AAAA,EACT;AACA,aAAuB,oBAAAA,KAAK,OAAO,EAAE,MAAM,WAAW,UAAU,MAAM,CAAC;AACzE;AACA,IAAI,kBAAkB,CAAC;AAAA,EACrB;AAAA,EACA;AACF,MAAM;AACJ,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,QAAM,EAAE,WAAW,YAAY,QAAQ,SAAS,MAAM,IAAI;AAAA,IACxD;AAAA,IACA;AAAA,EACF;AACA,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,MAAI,WAAW;AACb,eAAuB,oBAAAA,KAAK,OAAO,EAAE,WAAW,sFAAsF,CAAC;AAAA,EACzI;AACA,aAAuB,oBAAAA;AAAA,IACrB;AAAA,IACA;AAAA,MACE,aAAyB,oBAAAD,MAAM,MAAM,EAAE,UAAU;AAAA,QAC/C,iCAAQ,IAAI,CAAC,OAAO,cAA0B,oBAAAC,KAAK,MAAM,EAAE,UAAU,MAAM,GAAG,KAAK;AAAA,QACnF,aAAa,SAAqB,oBAAAA,KAAK,MAAM,EAAE,UAAU,EAAE,8CAA8C;AAAA,UACvG,OAAO;AAAA,QACT,CAAC,EAAE,CAAC;AAAA,MACN,EAAE,CAAC;AAAA,MACH,cAA0B,oBAAAA,KAAK,OAAO,EAAE,MAAM,WAAW,UAAU,EAAE,oCAAoC;AAAA,QACvG,OAAO,IAAI;AAAA,MACb,CAAC,EAAE,CAAC;AAAA,IACN;AAAA,EACF;AACF;AACA,IAAI,qBAAqB,CAAC,MAAM,QAAQ;AAxRxC;AAyRE,QAAM,WAAW;AAAA,IACf;AAAA,MACE,IAAI;AAAA,MACJ,OAAO;AAAA,IACT;AAAA,IACA;AAAA,MACE,SAAS,CAAC,CAAC,IAAI,UAAU,SAAS;AAAA;AAAA,IACpC;AAAA,EACF;AACA,QAAM,eAAe;AAAA,IACnB;AAAA,MACE,IAAI;AAAA,MACJ,OAAO;AAAA,IACT;AAAA,IACA;AAAA,MACE,SAAS,CAAC,CAAC,IAAI,UAAU,SAAS;AAAA;AAAA,IACpC;AAAA,EACF;AACA,UAAQ,MAAM;AAAA,IACZ,KAAK;AACH,aAAO;AAAA,QACL,SAAQ,cAAS,aAAT,mBAAmB,IAAI,CAAC,YAAY,QAAQ;AAAA,QACpD,WAAW,SAAS;AAAA,QACpB,YAAY,SAAS,YAAY,SAAS,QAAQ,SAAS,QAAQ,SAAS,SAAS,SAAS;AAAA,QAC9F,SAAS,SAAS;AAAA,QAClB,OAAO,SAAS;AAAA,MAClB;AAAA,IACF,KAAK;AACH,aAAO;AAAA,QACL,SAAQ,kBAAa,kBAAb,mBAA4B,IAAI,CAAC,UAAU,MAAM;AAAA,QACzD,WAAW,aAAa;AAAA,QACxB,YAAY,aAAa,iBAAiB,aAAa,QAAQ,aAAa,QAAQ,aAAa,cAAc,SAAS;AAAA,QACxH,SAAS,aAAa;AAAA,QACtB,OAAO,aAAa;AAAA,MACtB;AAAA,EACJ;AACF;AAIA,IAAI,mBAAmB,CAAC;AAAA,EACtB;AAAA,EACA;AAAA,EACA,QAAQ;AAAA,EACR;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACJ,MAAI,WAAW;AACb,eAAuB,oBAAAC,MAAM,OAAO,EAAE,WAAW,0BAA0B,UAAU;AAAA,MACnF,MAAM,KAAK,EAAE,QAAQ,EAAE,CAAC,EAAE,IAAI,CAAC,GAAG,UAAU;AAC1C,mBAAuB,oBAAAC;AAAA,UACrB;AAAA,UACA;AAAA,YACE,WAAW;AAAA,UACb;AAAA,UACA;AAAA,QACF;AAAA,MACF,CAAC;AAAA,UACe,oBAAAA,KAAK,qBAAqB,EAAE,QAAQ,MAAM,CAAC;AAAA,IAC7D,EAAE,CAAC;AAAA,EACL;AACA,QAAM,UAAU,OAAO,OAAO,WAAW,EAAE,OAAO,CAAC,MAAM,QAAQ,CAAC,CAAC,EAAE,WAAW;AAChF,QAAM,YAAY,CAAC,aAAa,UAAU,KAAK,CAAC;AAChD,QAAM,YAAY,CAAC,aAAa,UAAU,KAAK;AAC/C,QAAM,EAAE,WAAW,SAAS,IAAI,MAAM,SAAS,EAAE;AACjD,aAAuB,oBAAAD,MAAM,OAAO,EAAE,WAAW,0BAA0B,UAAU;AAAA,QACnE,oBAAAA,MAAM,OAAO,EAAE,WAAW,uFAAuF,UAAU;AAAA,UACzH,oBAAAC,KAAK,OAAO,EAAE,SAAS,CAAC;AAAA,UACxB,oBAAAD,MAAM,OAAO,EAAE,WAAW,6BAA6B,UAAU;AAAA,QAC/E,CAAC,iBAA6B,oBAAAA,MAAM,OAAO,EAAE,WAAW,6CAA6C,UAAU;AAAA,cAC7F,oBAAAC,KAAK,OAAO,EAAE,WAAW,mBAAmB,cAA0B,oBAAAA,KAAK,iBAAiB,EAAE,OAAO,CAAC,EAAE,CAAC;AAAA,cACzG,oBAAAA;AAAA,YACd;AAAA,YACA;AAAA,cACE,MAAM,CAAC,QAAQ,QAAQ,QAAQ,cAAc,YAAY;AAAA,cACzD;AAAA,YACF;AAAA,UACF;AAAA,QACF,EAAE,CAAC;AAAA,YACa,oBAAAA,KAAK,MAAM,EAAE,IAAI,OAAO,IAAI,cAA0B,oBAAAA,KAAK,QAAQ,EAAE,MAAM,SAAS,SAAS,aAAa,UAAU,OAAO,MAAM,CAAC,EAAE,CAAC;AAAA,MACvJ,EAAE,CAAC;AAAA,IACL,EAAE,CAAC;AAAA,IACH,iBAA6B,oBAAAA,KAAK,WAAW,CAAC,CAAC;AAAA,IAC/C,iBAA6B,oBAAAA,KAAK,WAAW,CAAC,CAAC;AAAA,IAC/C,CAAC,aAAa,CAAC,YAAY,CAAC,YAAY,MAAM,YAAY,EAAE,KAAK,IAAI,CAAC,QAAQ;AAC5E,iBAAuB,oBAAAA;AAAA,QACrB;AAAA,QACA;AAAA,UACE,SAAS,IAAI;AAAA,UACb,MAAM;AAAA,UACN,iBAAiB,IAAI;AAAA,QACvB;AAAA,QACA,IAAI;AAAA,MACN;AAAA,IACF,CAAC,IAAI,MAAM,KAAK,EAAE,QAAQ,EAAE,CAAC,EAAE,IAAI,CAAC,GAAG,UAAU;AAC/C,iBAAuB,oBAAAA;AAAA,QACrB;AAAA,QACA;AAAA,UACE,WAAW;AAAA,QACb;AAAA,QACA;AAAA,MACF;AAAA,IACF,CAAC,IAAI;AAAA,IACL,CAAC,iBAA6B,oBAAAA;AAAA,MAC5B;AAAA,MACA;AAAA,QACE;AAAA,QACA,aAAa,MAAM,eAAe;AAAA,QAClC,iBAAiB,MAAM,mBAAmB;AAAA,QAC1C;AAAA,QACA,UAAU,MAAM;AAAA,QAChB,cAAc,MAAM;AAAA,QACpB,WAAW,MAAM,aAAa;AAAA,QAC9B;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;AAUA,IAAI,sBAAsB,CAAC;AAAA,EACzB,OAAO,CAAC;AAAA,EACR,QAAQ;AAAA,EACR,UAAU,YAAY;AAAA,EACtB;AACF,MAAM;AACJ,QAAM,CAAC,cAAc,eAAe,IAAI,gBAAgB;AACxD,QAAM,YAAY,GAAG,SAAS,GAAG,MAAM,MAAM,EAAE;AAC/C,QAAM,SAAS,aAAa,IAAI,SAAS;AACzC,QAAM,CAAC,EAAE,WAAW,SAAS,GAAG,aAAa,QAAI,uBAAS;AAAA,IACxD,WAAW,SAAS,KAAK,KAAK,OAAO,MAAM,IAAI,SAAS,IAAI;AAAA,IAC5D,UAAU;AAAA,EACZ,CAAC;AACD,QAAM,iBAAa;AAAA,IACjB,OAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,IACA,CAAC,WAAW,QAAQ;AAAA,EACtB;AACA,8BAAU,MAAM;AACd,UAAM,QAAQ,SAAS,KAAK,KAAK,OAAO,MAAM,IAAI,SAAS,IAAI;AAC/D,QAAI,UAAU,WAAW;AACvB;AAAA,IACF;AACA,kBAAc,CAAC,UAAU;AAAA,MACvB,GAAG;AAAA,MACH,WAAW;AAAA,IACb,EAAE;AAAA,EACJ,GAAG,CAAC,QAAQ,WAAW,SAAS,CAAC;AACjC,QAAM,qBAAqB,CAAC,YAAY;AACtC,UAAM,QAAQ,QAAQ,UAAU;AAChC,UAAM,EAAE,WAAW,YAAY,UAAU,UAAU,IAAI;AACvD,oBAAgB,CAAC,SAAS;AACxB,UAAI,CAAC,YAAY;AACf,aAAK,OAAO,SAAS;AACrB,eAAO;AAAA,MACT;AACA,YAAM,YAAY,IAAI,gBAAgB,IAAI;AAC1C,gBAAU,IAAI,WAAW,OAAO,aAAa,SAAS,CAAC;AACvD,aAAO;AAAA,IACT,CAAC;AACD,kBAAc,KAAK;AACnB,WAAO;AAAA,EACT;AACA,QAAM,QAAQ,cAAc;AAAA,IAC1B;AAAA,IACA,SAAS,CAAC;AAAA;AAAA,IAEV,WAAW,KAAK,KAAK,QAAQ,QAAQ;AAAA,IACrC,OAAO;AAAA,MACL;AAAA,IACF;AAAA,IACA,iBAAiB,gBAAgB;AAAA,IACjC;AAAA,IACA,uBAAuB,sBAAsB;AAAA,IAC7C,kBAAkB;AAAA,EACpB,CAAC;AACD,SAAO;AAAA,IACL;AAAA,EACF;AACF;", "names": ["import_jsx_runtime", "jsxs2", "jsx2", "jsxs3", "jsx3"]}