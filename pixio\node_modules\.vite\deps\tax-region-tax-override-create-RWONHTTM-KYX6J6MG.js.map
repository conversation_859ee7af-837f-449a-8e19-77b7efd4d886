{"version": 3, "sources": ["../../@medusajs/dashboard/dist/tax-region-tax-override-create-RWONHTTM.mjs"], "sourcesContent": ["import {\n  TargetForm,\n  TargetItem,\n  TaxRateRuleReferenceSchema,\n  createTaxRulePayload\n} from \"./chunk-L2JMAQ3A.mjs\";\nimport \"./chunk-V3MOBCDF.mjs\";\nimport \"./chunk-R2HUTVC3.mjs\";\nimport \"./chunk-QBVCETMI.mjs\";\nimport \"./chunk-QMRGIWOP.mjs\";\nimport \"./chunk-44JF4VLS.mjs\";\nimport \"./chunk-I3VB6NM2.mjs\";\nimport \"./chunk-ZJRFL6ZN.mjs\";\nimport \"./chunk-DFA6WGYO.mjs\";\nimport \"./chunk-I5HYE2RW.mjs\";\nimport \"./chunk-RIV7FKGN.mjs\";\nimport \"./chunk-TDK3JDOB.mjs\";\nimport \"./chunk-FHSC5X62.mjs\";\nimport \"./chunk-XR4GEMGR.mjs\";\nimport \"./chunk-WRSGHGAT.mjs\";\nimport \"./chunk-MOSRJHJ3.mjs\";\nimport \"./chunk-BFAYZKJV.mjs\";\nimport \"./chunk-OMC5JCQH.mjs\";\nimport {\n  PercentageInput\n} from \"./chunk-YRY2CZ6I.mjs\";\nimport \"./chunk-RORIX3PU.mjs\";\nimport \"./chunk-XMAWMECC.mjs\";\nimport \"./chunk-5HNRTDDS.mjs\";\nimport \"./chunk-NNBHHXXN.mjs\";\nimport \"./chunk-IR5DHEKS.mjs\";\nimport \"./chunk-7DXVXBSA.mjs\";\nimport \"./chunk-PDWBYQOW.mjs\";\nimport \"./chunk-MSDRGCRR.mjs\";\nimport \"./chunk-G3QXMPRB.mjs\";\nimport \"./chunk-PCFUZKDS.mjs\";\nimport \"./chunk-IQBAUTU5.mjs\";\nimport \"./chunk-ADOCJB6L.mjs\";\nimport \"./chunk-LQTHYS2Z.mjs\";\nimport \"./chunk-P3UUX2T6.mjs\";\nimport \"./chunk-UE6PO4FK.mjs\";\nimport \"./chunk-YEDAFXMB.mjs\";\nimport \"./chunk-AOFGTNG6.mjs\";\nimport \"./chunk-EMIHDNB7.mjs\";\nimport \"./chunk-MWVM4TYO.mjs\";\nimport \"./chunk-GW6TVOAA.mjs\";\nimport \"./chunk-CBSCX7RE.mjs\";\nimport \"./chunk-LT4MVCA7.mjs\";\nimport \"./chunk-A2UMBW3V.mjs\";\nimport \"./chunk-W7625H47.mjs\";\nimport \"./chunk-DLZWPHHO.mjs\";\nimport \"./chunk-LSEYENCI.mjs\";\nimport \"./chunk-FVK4ZYYM.mjs\";\nimport \"./chunk-FZRIVT5D.mjs\";\nimport \"./chunk-SXYXTC2L.mjs\";\nimport \"./chunk-M3VFKDXJ.mjs\";\nimport \"./chunk-C76H5USB.mjs\";\nimport \"./chunk-KSV3NQOT.mjs\";\nimport {\n  SwitchBox\n} from \"./chunk-D7H6ZNK4.mjs\";\nimport \"./chunk-IUCDCPJU.mjs\";\nimport {\n  KeyboundForm\n} from \"./chunk-6HTZNHPT.mjs\";\nimport {\n  RouteFocusModal,\n  StackedFocusModal,\n  useRouteModal,\n  useStackedModal\n} from \"./chunk-4TC5YS65.mjs\";\nimport \"./chunk-DG7J63J2.mjs\";\nimport \"./chunk-MNXC6Q4F.mjs\";\nimport \"./chunk-LPEUYMRK.mjs\";\nimport \"./chunk-OC7BQLYI.mjs\";\nimport \"./chunk-NYXYHZE6.mjs\";\nimport {\n  Form\n} from \"./chunk-OBQI23QM.mjs\";\nimport \"./chunk-Z5UDPQIH.mjs\";\nimport \"./chunk-KOSCMAIC.mjs\";\nimport {\n  useCreateTaxRate\n} from \"./chunk-X6DSNTTX.mjs\";\nimport {\n  useTaxRegion\n} from \"./chunk-I6E6CALJ.mjs\";\nimport \"./chunk-B4GODIOW.mjs\";\nimport \"./chunk-F6IJV2I2.mjs\";\nimport \"./chunk-QTCZFYFH.mjs\";\nimport \"./chunk-ENV6YVOM.mjs\";\nimport \"./chunk-PIR2H25N.mjs\";\nimport \"./chunk-RLY2SL5E.mjs\";\nimport \"./chunk-C5LYZZZ5.mjs\";\nimport \"./chunk-2ZKVRTBW.mjs\";\nimport \"./chunk-FO3VP56P.mjs\";\nimport \"./chunk-YS65UGPC.mjs\";\nimport \"./chunk-F6PXCY3N.mjs\";\nimport \"./chunk-3OHH43G6.mjs\";\nimport \"./chunk-G2H6MAK7.mjs\";\nimport \"./chunk-GRT22PE5.mjs\";\nimport \"./chunk-32IQRUVY.mjs\";\nimport \"./chunk-FNYASI54.mjs\";\nimport \"./chunk-FVC7M755.mjs\";\nimport \"./chunk-ZJ3OFMHB.mjs\";\nimport \"./chunk-PNU5HPGY.mjs\";\nimport \"./chunk-V2LANK5S.mjs\";\nimport \"./chunk-QZ6PT4QV.mjs\";\nimport \"./chunk-QL4XKIVL.mjs\";\nimport \"./chunk-6I62UDJA.mjs\";\nimport \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-RPUOO7AV.mjs\";\n\n// src/routes/tax-regions/tax-region-tax-override-create/tax-region-tax-override-create.tsx\nimport { useParams } from \"react-router-dom\";\n\n// src/routes/tax-regions/tax-region-tax-override-create/components/tax-region-override-create-form/tax-region-tax-override-create.tsx\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport {\n  Button,\n  Divider,\n  Heading,\n  Hint,\n  Input,\n  Label,\n  Select,\n  Text,\n  clx,\n  toast\n} from \"@medusajs/ui\";\nimport { useFieldArray, useForm, useWatch } from \"react-hook-form\";\nimport { z } from \"zod\";\nimport { MagnifyingGlass } from \"@medusajs/icons\";\nimport { useTranslation } from \"react-i18next\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar TaxRegionCreateTaxOverrideSchema = z.object({\n  name: z.string().min(1),\n  code: z.string().min(1),\n  rate: z.object({\n    float: z.number().optional(),\n    value: z.string().optional()\n  }).optional(),\n  is_combinable: z.boolean().optional(),\n  enabled_rules: z.object({\n    product: z.boolean(),\n    product_type: z.boolean()\n    // product_collection: z.boolean(),\n    // product_tag: z.boolean(),\n    // customer_group: z.boolean(),\n  }),\n  product: z.array(TaxRateRuleReferenceSchema).optional(),\n  product_type: z.array(TaxRateRuleReferenceSchema).optional()\n  // product_collection: z.array(TaxRateRuleReferenceSchema).optional(),\n  // product_tag: z.array(TaxRateRuleReferenceSchema).optional(),\n  // customer_group: z.array(TaxRateRuleReferenceSchema).optional(),\n});\nvar STACKED_MODAL_ID = \"tr\";\nvar getStackedModalId = (type) => `${STACKED_MODAL_ID}-${type}`;\nvar TaxRegionCreateTaxOverrideForm = ({\n  taxRegion\n}) => {\n  const { t } = useTranslation();\n  const { handleSuccess } = useRouteModal();\n  const { setIsOpen } = useStackedModal();\n  const form = useForm({\n    defaultValues: {\n      name: \"\",\n      code: \"\",\n      is_combinable: false,\n      rate: {\n        value: \"\"\n      },\n      enabled_rules: {\n        product: true,\n        product_type: false\n        // product_collection: false,\n        // product_tag: false,\n        // customer_group: false,\n      },\n      product: [],\n      product_type: []\n      // product_collection: [],\n      // product_tag: [],\n      // customer_group: [],\n    },\n    resolver: zodResolver(TaxRegionCreateTaxOverrideSchema)\n  });\n  const { mutateAsync, isPending } = useCreateTaxRate();\n  const handleSubmit = form.handleSubmit(async (values) => {\n    const {\n      product,\n      product_type\n      // customer_group,\n      // product_collection,\n      // product_tag,\n    } = values;\n    const productRules = createTaxRulePayload({\n      reference_type: \"product\" /* PRODUCT */,\n      references: product || []\n    });\n    const productTypeRules = createTaxRulePayload({\n      reference_type: \"product_type\" /* PRODUCT_TYPE */,\n      references: product_type || []\n    });\n    const rules = [\n      productRules,\n      productTypeRules\n      // customerGroupRules,\n      // productCollectionRules,\n      // productTagRules,\n    ].filter((rule) => Boolean(rule)).flatMap((r) => r);\n    mutateAsync(\n      {\n        name: values.name,\n        tax_region_id: taxRegion.id,\n        rate: values.rate?.float,\n        code: values.code,\n        is_combinable: values.is_combinable,\n        rules,\n        is_default: false\n      },\n      {\n        onSuccess: () => {\n          handleSuccess();\n        },\n        onError: (error) => {\n          toast.error(error.message);\n        }\n      }\n    );\n  });\n  const products = useFieldArray({\n    control: form.control,\n    name: \"product\" /* PRODUCT */\n  });\n  const productTypes = useFieldArray({\n    control: form.control,\n    name: \"product_type\" /* PRODUCT_TYPE */\n  });\n  const getControls = (type) => {\n    switch (type) {\n      case \"product\" /* PRODUCT */:\n        return products;\n      case \"product_type\" /* PRODUCT_TYPE */:\n        return productTypes;\n    }\n  };\n  const referenceTypeOptions = [\n    {\n      value: \"product\" /* PRODUCT */,\n      label: t(\"taxRegions.fields.targets.options.product\")\n    },\n    {\n      value: \"product_type\" /* PRODUCT_TYPE */,\n      label: t(\"taxRegions.fields.targets.options.productType\")\n    }\n    // {\n    //   value: TaxRateRuleReferenceType.PRODUCT_COLLECTION,\n    //   label: t(\"taxRegions.fields.targets.options.productCollection\"),\n    // },\n    // {\n    //   value: TaxRateRuleReferenceType.PRODUCT_TAG,\n    //   label: t(\"taxRegions.fields.targets.options.productTag\"),\n    // },\n    // {\n    //   value: TaxRateRuleReferenceType.CUSTOMER_GROUP,\n    //   label: t(\"taxRegions.fields.targets.options.customerGroup\"),\n    // },\n  ];\n  const searchPlaceholders = {\n    [\"product\" /* PRODUCT */]: t(\n      \"taxRegions.fields.targets.placeholders.product\"\n    ),\n    [\"product_type\" /* PRODUCT_TYPE */]: t(\n      \"taxRegions.fields.targets.placeholders.productType\"\n    )\n    // [TaxRateRuleReferenceType.PRODUCT_COLLECTION]: t(\n    //   \"taxRegions.fields.targets.placeholders.productCollection\"\n    // ),\n    // [TaxRateRuleReferenceType.PRODUCT_TAG]: t(\n    //   \"taxRegions.fields.targets.placeholders.productTag\"\n    // ),\n    // [TaxRateRuleReferenceType.CUSTOMER_GROUP]: t(\n    //   \"taxRegions.fields.targets.placeholders.customerGroup\"\n    // ),\n  };\n  const getFieldHandler = (type) => {\n    const { fields, remove, append } = getControls(type);\n    const modalId = getStackedModalId(type);\n    return (references) => {\n      if (!references.length) {\n        form.setValue(type, [], {\n          shouldDirty: true\n        });\n        setIsOpen(modalId, false);\n        return;\n      }\n      const newIds = references.map((reference) => reference.value);\n      const fieldsToAdd = references.filter(\n        (reference) => !fields.some((field) => field.value === reference.value)\n      );\n      for (const field of fields) {\n        if (!newIds.includes(field.value)) {\n          remove(fields.indexOf(field));\n        }\n      }\n      append(fieldsToAdd);\n      setIsOpen(modalId, false);\n    };\n  };\n  const displayOrder = /* @__PURE__ */ new Set([\n    \"product\" /* PRODUCT */\n  ]);\n  const disableRule = (type) => {\n    form.setValue(type, [], {\n      shouldDirty: true\n    });\n    form.setValue(`enabled_rules.${type}`, false, {\n      shouldDirty: true\n    });\n    displayOrder.delete(type);\n  };\n  const enableRule = (type) => {\n    form.setValue(`enabled_rules.${type}`, true, {\n      shouldDirty: true\n    });\n    form.setValue(type, [], {\n      shouldDirty: true\n    });\n    displayOrder.add(type);\n  };\n  const watchedEnabledRules = useWatch({\n    control: form.control,\n    name: \"enabled_rules\"\n  });\n  const addRule = () => {\n    const firstDisabledRule = Object.keys(watchedEnabledRules).find(\n      (key) => !watchedEnabledRules[key]\n    );\n    if (firstDisabledRule) {\n      enableRule(firstDisabledRule);\n    }\n  };\n  const visibleRuleTypes = referenceTypeOptions.filter((option) => watchedEnabledRules[option.value]).sort((a, b) => {\n    const orderArray = Array.from(displayOrder);\n    return orderArray.indexOf(b.value) - orderArray.indexOf(a.value);\n  });\n  const getAvailableRuleTypes = (type) => {\n    return referenceTypeOptions.filter((option) => {\n      return !visibleRuleTypes.some(\n        (visibleOption) => visibleOption.value === option.value\n      ) || option.value === type;\n    });\n  };\n  const showAddButton = Object.values(watchedEnabledRules).some(\n    (value) => !value\n  );\n  return /* @__PURE__ */ jsx(RouteFocusModal.Form, { form, children: /* @__PURE__ */ jsxs(\n    KeyboundForm,\n    {\n      onSubmit: handleSubmit,\n      className: \"flex h-full flex-col overflow-hidden\",\n      children: [\n        /* @__PURE__ */ jsx(RouteFocusModal.Header, {}),\n        /* @__PURE__ */ jsx(RouteFocusModal.Body, { className: \"flex flex-1 flex-col overflow-hidden\", children: /* @__PURE__ */ jsx(\"div\", { className: \"flex flex-1 flex-col items-center overflow-y-auto\", children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex w-full max-w-[720px] flex-col gap-y-8 px-2 py-16\", children: [\n          /* @__PURE__ */ jsxs(\"div\", { children: [\n            /* @__PURE__ */ jsx(RouteFocusModal.Title, { asChild: true, children: /* @__PURE__ */ jsx(Heading, { children: t(\"taxRegions.taxOverrides.create.header\") }) }),\n            /* @__PURE__ */ jsx(RouteFocusModal.Description, { asChild: true, children: /* @__PURE__ */ jsx(Text, { size: \"small\", className: \"text-ui-fg-subtle\", children: t(\"taxRegions.taxOverrides.create.hint\") }) })\n          ] }),\n          /* @__PURE__ */ jsx(\"div\", { className: \"flex flex-col gap-4\", children: /* @__PURE__ */ jsx(\"div\", { className: \"flex flex-col gap-y-4\", children: /* @__PURE__ */ jsxs(\"div\", { className: \"grid grid-cols-1 gap-4 md:grid-cols-2\", children: [\n            /* @__PURE__ */ jsx(\n              Form.Field,\n              {\n                control: form.control,\n                name: \"name\",\n                render: ({ field }) => {\n                  return /* @__PURE__ */ jsxs(Form.Item, { children: [\n                    /* @__PURE__ */ jsx(Form.Label, { children: t(\"fields.name\") }),\n                    /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(Input, { ...field }) }),\n                    /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n                  ] });\n                }\n              }\n            ),\n            /* @__PURE__ */ jsx(\n              Form.Field,\n              {\n                control: form.control,\n                name: \"rate\",\n                render: ({ field: { value, onChange, ...field } }) => {\n                  return /* @__PURE__ */ jsxs(Form.Item, { children: [\n                    /* @__PURE__ */ jsx(Form.Label, { children: t(\"taxRegions.fields.taxRate\") }),\n                    /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(\n                      PercentageInput,\n                      {\n                        ...field,\n                        placeholder: \"0.00\",\n                        value: value?.value,\n                        onValueChange: (value2, _name, values) => onChange({\n                          value: value2,\n                          float: values?.float\n                        })\n                      }\n                    ) }),\n                    /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n                  ] });\n                }\n              }\n            ),\n            /* @__PURE__ */ jsx(\n              Form.Field,\n              {\n                control: form.control,\n                name: \"code\",\n                render: ({ field }) => {\n                  return /* @__PURE__ */ jsxs(Form.Item, { children: [\n                    /* @__PURE__ */ jsx(Form.Label, { children: t(\"taxRegions.fields.taxCode\") }),\n                    /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(Input, { ...field }) }),\n                    /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n                  ] });\n                }\n              }\n            )\n          ] }) }) }),\n          /* @__PURE__ */ jsx(\n            SwitchBox,\n            {\n              control: form.control,\n              name: \"is_combinable\",\n              label: t(\"taxRegions.fields.isCombinable.label\"),\n              description: t(\"taxRegions.fields.isCombinable.hint\")\n            }\n          ),\n          /* @__PURE__ */ jsxs(\"div\", { className: \"flex flex-col gap-y-3\", children: [\n            /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center justify-between gap-x-4\", children: [\n              /* @__PURE__ */ jsxs(\"div\", { className: \"flex flex-col\", children: [\n                /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center gap-x-1\", children: [\n                  /* @__PURE__ */ jsx(\n                    Label,\n                    {\n                      id: \"tax_region_rules_label\",\n                      htmlFor: \"tax_region_rules\",\n                      children: t(\"taxRegions.fields.targets.label\")\n                    }\n                  ),\n                  /* @__PURE__ */ jsxs(\n                    Text,\n                    {\n                      size: \"small\",\n                      leading: \"compact\",\n                      className: \"text-ui-fg-muted\",\n                      children: [\n                        \"(\",\n                        t(\"fields.optional\"),\n                        \")\"\n                      ]\n                    }\n                  )\n                ] }),\n                /* @__PURE__ */ jsx(\n                  Hint,\n                  {\n                    id: \"tax_region_rules_description\",\n                    className: \"text-pretty\",\n                    children: t(\"taxRegions.fields.targets.hint\")\n                  }\n                )\n              ] }),\n              showAddButton && /* @__PURE__ */ jsx(\n                Button,\n                {\n                  onClick: addRule,\n                  type: \"button\",\n                  size: \"small\",\n                  variant: \"transparent\",\n                  className: \"text-ui-fg-interactive hover:text-ui-fg-interactive-hover flex-shrink-0\",\n                  children: t(\"taxRegions.fields.targets.action\")\n                }\n              )\n            ] }),\n            /* @__PURE__ */ jsx(\n              \"div\",\n              {\n                id: \"tax_region_rules\",\n                \"aria-labelledby\": \"tax_region_rules_label\",\n                \"aria-describedby\": \"tax_region_rules_description\",\n                role: \"application\",\n                className: \"flex flex-col gap-y-3\",\n                children: visibleRuleTypes.map((ruleType, index) => {\n                  const type = ruleType.value;\n                  const label = ruleType.label;\n                  const isLast = index === visibleRuleTypes.length - 1;\n                  const searchPlaceholder = searchPlaceholders[type];\n                  const options = getAvailableRuleTypes(type);\n                  const { fields, remove } = getControls(type);\n                  const handler = getFieldHandler(type);\n                  const modalId = getStackedModalId(type);\n                  const handleChangeType = (value) => {\n                    disableRule(type);\n                    enableRule(value);\n                  };\n                  return /* @__PURE__ */ jsx(\"div\", { children: /* @__PURE__ */ jsx(\n                    Form.Field,\n                    {\n                      control: form.control,\n                      name: ruleType.value,\n                      render: ({\n                        field: {\n                          value: _value,\n                          onChange: _onChange,\n                          ...field\n                        }\n                      }) => {\n                        return /* @__PURE__ */ jsxs(Form.Item, { className: \"space-y-0\", children: [\n                          /* @__PURE__ */ jsx(Form.Label, { className: \"sr-only\", children: label }),\n                          /* @__PURE__ */ jsxs(\n                            \"div\",\n                            {\n                              className: clx(\n                                \"bg-ui-bg-component shadow-elevation-card-rest transition-fg grid gap-1.5 rounded-xl py-1.5\",\n                                \"aria-[invalid='true']:shadow-borders-error\"\n                              ),\n                              role: \"application\",\n                              ...field,\n                              children: [\n                                /* @__PURE__ */ jsxs(\"div\", { className: \"text-ui-fg-subtle grid gap-1.5 px-1.5 md:grid-cols-2\", children: [\n                                  isLast ? /* @__PURE__ */ jsxs(\n                                    Select,\n                                    {\n                                      value: type,\n                                      onValueChange: handleChangeType,\n                                      children: [\n                                        /* @__PURE__ */ jsx(Select.Trigger, { className: \"bg-ui-bg-field-component hover:bg-ui-bg-field-component-hover\", children: /* @__PURE__ */ jsx(Select.Value, {}) }),\n                                        /* @__PURE__ */ jsx(Select.Content, { children: options.map((option) => {\n                                          return /* @__PURE__ */ jsx(\n                                            Select.Item,\n                                            {\n                                              value: option.value,\n                                              children: option.label\n                                            },\n                                            option.value\n                                          );\n                                        }) })\n                                      ]\n                                    }\n                                  ) : /* @__PURE__ */ jsx(\"div\", { className: \"bg-ui-bg-field shadow-borders-base txt-compact-small rounded-md px-2 py-1.5\", children: label }),\n                                  /* @__PURE__ */ jsx(\"div\", { className: \"bg-ui-bg-field shadow-borders-base txt-compact-small rounded-md px-2 py-1.5\", children: t(\n                                    \"taxRegions.fields.targets.operators.in\"\n                                  ) })\n                                ] }),\n                                /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center gap-1.5 px-1.5\", children: [\n                                  /* @__PURE__ */ jsxs(StackedFocusModal, { id: modalId, children: [\n                                    /* @__PURE__ */ jsx(StackedFocusModal.Trigger, { asChild: true, children: /* @__PURE__ */ jsxs(\n                                      \"button\",\n                                      {\n                                        type: \"button\",\n                                        className: \"bg-ui-bg-field-component hover:bg-ui-bg-field-component-hover shadow-borders-base txt-compact-small text-ui-fg-muted transition-fg focus-visible:shadow-borders-interactive-with-active flex flex-1 items-center gap-x-2 rounded-md px-2 py-1.5 outline-none\",\n                                        children: [\n                                          /* @__PURE__ */ jsx(MagnifyingGlass, {}),\n                                          searchPlaceholder\n                                        ]\n                                      }\n                                    ) }),\n                                    /* @__PURE__ */ jsx(StackedFocusModal.Trigger, { asChild: true, children: /* @__PURE__ */ jsx(Button, { variant: \"secondary\", children: t(\"actions.browse\") }) }),\n                                    /* @__PURE__ */ jsxs(StackedFocusModal.Content, { children: [\n                                      /* @__PURE__ */ jsxs(StackedFocusModal.Header, { children: [\n                                        /* @__PURE__ */ jsx(StackedFocusModal.Title, { asChild: true, children: /* @__PURE__ */ jsx(Heading, { className: \"sr-only\", children: t(\n                                          \"taxRegions.fields.targets.modal.header\"\n                                        ) }) }),\n                                        /* @__PURE__ */ jsx(StackedFocusModal.Description, { className: \"sr-only\", children: t(\n                                          \"taxRegions.fields.targets.hint\"\n                                        ) })\n                                      ] }),\n                                      /* @__PURE__ */ jsx(\n                                        TargetForm,\n                                        {\n                                          type: \"focus\",\n                                          referenceType: type,\n                                          state: fields,\n                                          setState: handler\n                                        }\n                                      )\n                                    ] })\n                                  ] }),\n                                  /* @__PURE__ */ jsx(\n                                    Button,\n                                    {\n                                      variant: \"secondary\",\n                                      onClick: () => disableRule(type),\n                                      type: \"button\",\n                                      children: t(\"actions.delete\")\n                                    }\n                                  )\n                                ] }),\n                                fields.length > 0 ? /* @__PURE__ */ jsxs(\"div\", { className: \"flex flex-col gap-y-1.5\", children: [\n                                  /* @__PURE__ */ jsx(Divider, { variant: \"dashed\" }),\n                                  /* @__PURE__ */ jsx(\"div\", { className: \"flex flex-col gap-y-1.5 px-1.5\", children: fields.map((field2, index2) => {\n                                    return /* @__PURE__ */ jsx(\n                                      TargetItem,\n                                      {\n                                        index: index2,\n                                        label: field2.label,\n                                        onRemove: remove\n                                      },\n                                      field2.id\n                                    );\n                                  }) })\n                                ] }) : null\n                              ]\n                            }\n                          ),\n                          /* @__PURE__ */ jsx(Form.ErrorMessage, { className: \"mt-2\" })\n                        ] });\n                      }\n                    }\n                  ) }, type);\n                })\n              }\n            )\n          ] })\n        ] }) }) }),\n        /* @__PURE__ */ jsx(RouteFocusModal.Footer, { children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center justify-end gap-x-2\", children: [\n          /* @__PURE__ */ jsx(RouteFocusModal.Close, { asChild: true, children: /* @__PURE__ */ jsx(Button, { size: \"small\", variant: \"secondary\", children: t(\"actions.cancel\") }) }),\n          /* @__PURE__ */ jsx(Button, { size: \"small\", type: \"submit\", isLoading: isPending, children: t(\"actions.save\") })\n        ] }) })\n      ]\n    }\n  ) });\n};\n\n// src/routes/tax-regions/tax-region-tax-override-create/tax-region-tax-override-create.tsx\nimport { jsx as jsx2 } from \"react/jsx-runtime\";\nvar TaxRegionCreateTaxOverride = () => {\n  const { id, province_id } = useParams();\n  const { tax_region, isPending, isError, error } = useTaxRegion(\n    province_id || id\n  );\n  const ready = !isPending && !!tax_region;\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsx2(RouteFocusModal, { children: ready && /* @__PURE__ */ jsx2(TaxRegionCreateTaxOverrideForm, { taxRegion: tax_region }) });\n};\nexport {\n  TaxRegionCreateTaxOverride as Component\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwIA,yBAA0B;AAif1B,IAAAA,sBAA4B;AAhf5B,IAAI,mCAAmC,EAAE,OAAO;AAAA,EAC9C,MAAM,EAAE,OAAO,EAAE,IAAI,CAAC;AAAA,EACtB,MAAM,EAAE,OAAO,EAAE,IAAI,CAAC;AAAA,EACtB,MAAM,EAAE,OAAO;AAAA,IACb,OAAO,EAAE,OAAO,EAAE,SAAS;AAAA,IAC3B,OAAO,EAAE,OAAO,EAAE,SAAS;AAAA,EAC7B,CAAC,EAAE,SAAS;AAAA,EACZ,eAAe,EAAE,QAAQ,EAAE,SAAS;AAAA,EACpC,eAAe,EAAE,OAAO;AAAA,IACtB,SAAS,EAAE,QAAQ;AAAA,IACnB,cAAc,EAAE,QAAQ;AAAA;AAAA;AAAA;AAAA,EAI1B,CAAC;AAAA,EACD,SAAS,EAAE,MAAM,0BAA0B,EAAE,SAAS;AAAA,EACtD,cAAc,EAAE,MAAM,0BAA0B,EAAE,SAAS;AAAA;AAAA;AAAA;AAI7D,CAAC;AACD,IAAI,mBAAmB;AACvB,IAAI,oBAAoB,CAAC,SAAS,GAAG,gBAAgB,IAAI,IAAI;AAC7D,IAAI,iCAAiC,CAAC;AAAA,EACpC;AACF,MAAM;AACJ,QAAM,EAAE,GAAAC,GAAE,IAAI,eAAe;AAC7B,QAAM,EAAE,cAAc,IAAI,cAAc;AACxC,QAAM,EAAE,UAAU,IAAI,gBAAgB;AACtC,QAAM,OAAO,QAAQ;AAAA,IACnB,eAAe;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,eAAe;AAAA,MACf,MAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,MACA,eAAe;AAAA,QACb,SAAS;AAAA,QACT,cAAc;AAAA;AAAA;AAAA;AAAA,MAIhB;AAAA,MACA,SAAS,CAAC;AAAA,MACV,cAAc,CAAC;AAAA;AAAA;AAAA;AAAA,IAIjB;AAAA,IACA,UAAU,EAAY,gCAAgC;AAAA,EACxD,CAAC;AACD,QAAM,EAAE,aAAa,UAAU,IAAI,iBAAiB;AACpD,QAAM,eAAe,KAAK,aAAa,OAAO,WAAW;AA9L3D;AA+LI,UAAM;AAAA,MACJ;AAAA,MACA;AAAA;AAAA;AAAA;AAAA,IAIF,IAAI;AACJ,UAAM,eAAe,qBAAqB;AAAA,MACxC,gBAAgB;AAAA,MAChB,YAAY,WAAW,CAAC;AAAA,IAC1B,CAAC;AACD,UAAM,mBAAmB,qBAAqB;AAAA,MAC5C,gBAAgB;AAAA,MAChB,YAAY,gBAAgB,CAAC;AAAA,IAC/B,CAAC;AACD,UAAM,QAAQ;AAAA,MACZ;AAAA,MACA;AAAA;AAAA;AAAA;AAAA,IAIF,EAAE,OAAO,CAAC,SAAS,QAAQ,IAAI,CAAC,EAAE,QAAQ,CAAC,MAAM,CAAC;AAClD;AAAA,MACE;AAAA,QACE,MAAM,OAAO;AAAA,QACb,eAAe,UAAU;AAAA,QACzB,OAAM,YAAO,SAAP,mBAAa;AAAA,QACnB,MAAM,OAAO;AAAA,QACb,eAAe,OAAO;AAAA,QACtB;AAAA,QACA,YAAY;AAAA,MACd;AAAA,MACA;AAAA,QACE,WAAW,MAAM;AACf,wBAAc;AAAA,QAChB;AAAA,QACA,SAAS,CAAC,UAAU;AAClB,gBAAM,MAAM,MAAM,OAAO;AAAA,QAC3B;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AACD,QAAM,WAAW,cAAc;AAAA,IAC7B,SAAS,KAAK;AAAA,IACd,MAAM;AAAA;AAAA,EACR,CAAC;AACD,QAAM,eAAe,cAAc;AAAA,IACjC,SAAS,KAAK;AAAA,IACd,MAAM;AAAA;AAAA,EACR,CAAC;AACD,QAAM,cAAc,CAAC,SAAS;AAC5B,YAAQ,MAAM;AAAA,MACZ,KAAK;AACH,eAAO;AAAA,MACT,KAAK;AACH,eAAO;AAAA,IACX;AAAA,EACF;AACA,QAAM,uBAAuB;AAAA,IAC3B;AAAA,MACE,OAAO;AAAA,MACP,OAAOA,GAAE,2CAA2C;AAAA,IACtD;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP,OAAOA,GAAE,+CAA+C;AAAA,IAC1D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaF;AACA,QAAM,qBAAqB;AAAA,IACzB;AAAA,MAAC;AAAA;AAAA,IAAuB,GAAGA;AAAA,MACzB;AAAA,IACF;AAAA,IACA;AAAA,MAAC;AAAA;AAAA,IAAiC,GAAGA;AAAA,MACnC;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUF;AACA,QAAM,kBAAkB,CAAC,SAAS;AAChC,UAAM,EAAE,QAAQ,QAAQ,OAAO,IAAI,YAAY,IAAI;AACnD,UAAM,UAAU,kBAAkB,IAAI;AACtC,WAAO,CAAC,eAAe;AACrB,UAAI,CAAC,WAAW,QAAQ;AACtB,aAAK,SAAS,MAAM,CAAC,GAAG;AAAA,UACtB,aAAa;AAAA,QACf,CAAC;AACD,kBAAU,SAAS,KAAK;AACxB;AAAA,MACF;AACA,YAAM,SAAS,WAAW,IAAI,CAAC,cAAc,UAAU,KAAK;AAC5D,YAAM,cAAc,WAAW;AAAA,QAC7B,CAAC,cAAc,CAAC,OAAO,KAAK,CAAC,UAAU,MAAM,UAAU,UAAU,KAAK;AAAA,MACxE;AACA,iBAAW,SAAS,QAAQ;AAC1B,YAAI,CAAC,OAAO,SAAS,MAAM,KAAK,GAAG;AACjC,iBAAO,OAAO,QAAQ,KAAK,CAAC;AAAA,QAC9B;AAAA,MACF;AACA,aAAO,WAAW;AAClB,gBAAU,SAAS,KAAK;AAAA,IAC1B;AAAA,EACF;AACA,QAAM,eAA+B,oBAAI,IAAI;AAAA,IAC3C;AAAA;AAAA,EACF,CAAC;AACD,QAAM,cAAc,CAAC,SAAS;AAC5B,SAAK,SAAS,MAAM,CAAC,GAAG;AAAA,MACtB,aAAa;AAAA,IACf,CAAC;AACD,SAAK,SAAS,iBAAiB,IAAI,IAAI,OAAO;AAAA,MAC5C,aAAa;AAAA,IACf,CAAC;AACD,iBAAa,OAAO,IAAI;AAAA,EAC1B;AACA,QAAM,aAAa,CAAC,SAAS;AAC3B,SAAK,SAAS,iBAAiB,IAAI,IAAI,MAAM;AAAA,MAC3C,aAAa;AAAA,IACf,CAAC;AACD,SAAK,SAAS,MAAM,CAAC,GAAG;AAAA,MACtB,aAAa;AAAA,IACf,CAAC;AACD,iBAAa,IAAI,IAAI;AAAA,EACvB;AACA,QAAM,sBAAsB,SAAS;AAAA,IACnC,SAAS,KAAK;AAAA,IACd,MAAM;AAAA,EACR,CAAC;AACD,QAAM,UAAU,MAAM;AACpB,UAAM,oBAAoB,OAAO,KAAK,mBAAmB,EAAE;AAAA,MACzD,CAAC,QAAQ,CAAC,oBAAoB,GAAG;AAAA,IACnC;AACA,QAAI,mBAAmB;AACrB,iBAAW,iBAAiB;AAAA,IAC9B;AAAA,EACF;AACA,QAAM,mBAAmB,qBAAqB,OAAO,CAAC,WAAW,oBAAoB,OAAO,KAAK,CAAC,EAAE,KAAK,CAAC,GAAG,MAAM;AACjH,UAAM,aAAa,MAAM,KAAK,YAAY;AAC1C,WAAO,WAAW,QAAQ,EAAE,KAAK,IAAI,WAAW,QAAQ,EAAE,KAAK;AAAA,EACjE,CAAC;AACD,QAAM,wBAAwB,CAAC,SAAS;AACtC,WAAO,qBAAqB,OAAO,CAAC,WAAW;AAC7C,aAAO,CAAC,iBAAiB;AAAA,QACvB,CAAC,kBAAkB,cAAc,UAAU,OAAO;AAAA,MACpD,KAAK,OAAO,UAAU;AAAA,IACxB,CAAC;AAAA,EACH;AACA,QAAM,gBAAgB,OAAO,OAAO,mBAAmB,EAAE;AAAA,IACvD,CAAC,UAAU,CAAC;AAAA,EACd;AACA,aAAuB,wBAAI,gBAAgB,MAAM,EAAE,MAAM,cAA0B;AAAA,IACjF;AAAA,IACA;AAAA,MACE,UAAU;AAAA,MACV,WAAW;AAAA,MACX,UAAU;AAAA,YACQ,wBAAI,gBAAgB,QAAQ,CAAC,CAAC;AAAA,YAC9B,wBAAI,gBAAgB,MAAM,EAAE,WAAW,wCAAwC,cAA0B,wBAAI,OAAO,EAAE,WAAW,qDAAqD,cAA0B,yBAAK,OAAO,EAAE,WAAW,yDAAyD,UAAU;AAAA,cAC1S,yBAAK,OAAO,EAAE,UAAU;AAAA,gBACtB,wBAAI,gBAAgB,OAAO,EAAE,SAAS,MAAM,cAA0B,wBAAI,SAAS,EAAE,UAAUA,GAAE,uCAAuC,EAAE,CAAC,EAAE,CAAC;AAAA,gBAC9I,wBAAI,gBAAgB,aAAa,EAAE,SAAS,MAAM,cAA0B,wBAAI,MAAM,EAAE,MAAM,SAAS,WAAW,qBAAqB,UAAUA,GAAE,qCAAqC,EAAE,CAAC,EAAE,CAAC;AAAA,UAChN,EAAE,CAAC;AAAA,cACa,wBAAI,OAAO,EAAE,WAAW,uBAAuB,cAA0B,wBAAI,OAAO,EAAE,WAAW,yBAAyB,cAA0B,yBAAK,OAAO,EAAE,WAAW,yCAAyC,UAAU;AAAA,gBAC9N;AAAA,cACd,KAAK;AAAA,cACL;AAAA,gBACE,SAAS,KAAK;AAAA,gBACd,MAAM;AAAA,gBACN,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,6BAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,wBACjC,wBAAI,KAAK,OAAO,EAAE,UAAUA,GAAE,aAAa,EAAE,CAAC;AAAA,wBAC9C,wBAAI,KAAK,SAAS,EAAE,cAA0B,wBAAI,OAAO,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC;AAAA,wBACxE,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,kBAC3C,EAAE,CAAC;AAAA,gBACL;AAAA,cACF;AAAA,YACF;AAAA,gBACgB;AAAA,cACd,KAAK;AAAA,cACL;AAAA,gBACE,SAAS,KAAK;AAAA,gBACd,MAAM;AAAA,gBACN,QAAQ,CAAC,EAAE,OAAO,EAAE,OAAO,UAAU,GAAG,MAAM,EAAE,MAAM;AACpD,6BAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,wBACjC,wBAAI,KAAK,OAAO,EAAE,UAAUA,GAAE,2BAA2B,EAAE,CAAC;AAAA,wBAC5D,wBAAI,KAAK,SAAS,EAAE,cAA0B;AAAA,sBAC5D;AAAA,sBACA;AAAA,wBACE,GAAG;AAAA,wBACH,aAAa;AAAA,wBACb,OAAO,+BAAO;AAAA,wBACd,eAAe,CAAC,QAAQ,OAAO,WAAW,SAAS;AAAA,0BACjD,OAAO;AAAA,0BACP,OAAO,iCAAQ;AAAA,wBACjB,CAAC;AAAA,sBACH;AAAA,oBACF,EAAE,CAAC;AAAA,wBACa,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,kBAC3C,EAAE,CAAC;AAAA,gBACL;AAAA,cACF;AAAA,YACF;AAAA,gBACgB;AAAA,cACd,KAAK;AAAA,cACL;AAAA,gBACE,SAAS,KAAK;AAAA,gBACd,MAAM;AAAA,gBACN,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,6BAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,wBACjC,wBAAI,KAAK,OAAO,EAAE,UAAUA,GAAE,2BAA2B,EAAE,CAAC;AAAA,wBAC5D,wBAAI,KAAK,SAAS,EAAE,cAA0B,wBAAI,OAAO,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC;AAAA,wBACxE,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,kBAC3C,EAAE,CAAC;AAAA,gBACL;AAAA,cACF;AAAA,YACF;AAAA,UACF,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;AAAA,cACO;AAAA,YACd;AAAA,YACA;AAAA,cACE,SAAS,KAAK;AAAA,cACd,MAAM;AAAA,cACN,OAAOA,GAAE,sCAAsC;AAAA,cAC/C,aAAaA,GAAE,qCAAqC;AAAA,YACtD;AAAA,UACF;AAAA,cACgB,yBAAK,OAAO,EAAE,WAAW,yBAAyB,UAAU;AAAA,gBAC1D,yBAAK,OAAO,EAAE,WAAW,6CAA6C,UAAU;AAAA,kBAC9E,yBAAK,OAAO,EAAE,WAAW,iBAAiB,UAAU;AAAA,oBAClD,yBAAK,OAAO,EAAE,WAAW,6BAA6B,UAAU;AAAA,sBAC9D;AAAA,oBACd;AAAA,oBACA;AAAA,sBACE,IAAI;AAAA,sBACJ,SAAS;AAAA,sBACT,UAAUA,GAAE,iCAAiC;AAAA,oBAC/C;AAAA,kBACF;AAAA,sBACgB;AAAA,oBACd;AAAA,oBACA;AAAA,sBACE,MAAM;AAAA,sBACN,SAAS;AAAA,sBACT,WAAW;AAAA,sBACX,UAAU;AAAA,wBACR;AAAA,wBACAA,GAAE,iBAAiB;AAAA,wBACnB;AAAA,sBACF;AAAA,oBACF;AAAA,kBACF;AAAA,gBACF,EAAE,CAAC;AAAA,oBACa;AAAA,kBACd;AAAA,kBACA;AAAA,oBACE,IAAI;AAAA,oBACJ,WAAW;AAAA,oBACX,UAAUA,GAAE,gCAAgC;AAAA,kBAC9C;AAAA,gBACF;AAAA,cACF,EAAE,CAAC;AAAA,cACH,qBAAiC;AAAA,gBAC/B;AAAA,gBACA;AAAA,kBACE,SAAS;AAAA,kBACT,MAAM;AAAA,kBACN,MAAM;AAAA,kBACN,SAAS;AAAA,kBACT,WAAW;AAAA,kBACX,UAAUA,GAAE,kCAAkC;AAAA,gBAChD;AAAA,cACF;AAAA,YACF,EAAE,CAAC;AAAA,gBACa;AAAA,cACd;AAAA,cACA;AAAA,gBACE,IAAI;AAAA,gBACJ,mBAAmB;AAAA,gBACnB,oBAAoB;AAAA,gBACpB,MAAM;AAAA,gBACN,WAAW;AAAA,gBACX,UAAU,iBAAiB,IAAI,CAAC,UAAU,UAAU;AAClD,wBAAM,OAAO,SAAS;AACtB,wBAAM,QAAQ,SAAS;AACvB,wBAAM,SAAS,UAAU,iBAAiB,SAAS;AACnD,wBAAM,oBAAoB,mBAAmB,IAAI;AACjD,wBAAM,UAAU,sBAAsB,IAAI;AAC1C,wBAAM,EAAE,QAAQ,OAAO,IAAI,YAAY,IAAI;AAC3C,wBAAM,UAAU,gBAAgB,IAAI;AACpC,wBAAM,UAAU,kBAAkB,IAAI;AACtC,wBAAM,mBAAmB,CAAC,UAAU;AAClC,gCAAY,IAAI;AAChB,+BAAW,KAAK;AAAA,kBAClB;AACA,6BAAuB,wBAAI,OAAO,EAAE,cAA0B;AAAA,oBAC5D,KAAK;AAAA,oBACL;AAAA,sBACE,SAAS,KAAK;AAAA,sBACd,MAAM,SAAS;AAAA,sBACf,QAAQ,CAAC;AAAA,wBACP,OAAO;AAAA,0BACL,OAAO;AAAA,0BACP,UAAU;AAAA,0BACV,GAAG;AAAA,wBACL;AAAA,sBACF,MAAM;AACJ,mCAAuB,yBAAK,KAAK,MAAM,EAAE,WAAW,aAAa,UAAU;AAAA,8BACzD,wBAAI,KAAK,OAAO,EAAE,WAAW,WAAW,UAAU,MAAM,CAAC;AAAA,8BACzD;AAAA,4BACd;AAAA,4BACA;AAAA,8BACE,WAAW;AAAA,gCACT;AAAA,gCACA;AAAA,8BACF;AAAA,8BACA,MAAM;AAAA,8BACN,GAAG;AAAA,8BACH,UAAU;AAAA,oCACQ,yBAAK,OAAO,EAAE,WAAW,wDAAwD,UAAU;AAAA,kCACzG,aAAyB;AAAA,oCACvB;AAAA,oCACA;AAAA,sCACE,OAAO;AAAA,sCACP,eAAe;AAAA,sCACf,UAAU;AAAA,4CACQ,wBAAI,OAAO,SAAS,EAAE,WAAW,iEAAiE,cAA0B,wBAAI,OAAO,OAAO,CAAC,CAAC,EAAE,CAAC;AAAA,4CACnJ,wBAAI,OAAO,SAAS,EAAE,UAAU,QAAQ,IAAI,CAAC,WAAW;AACtE,qDAAuB;AAAA,4CACrB,OAAO;AAAA,4CACP;AAAA,8CACE,OAAO,OAAO;AAAA,8CACd,UAAU,OAAO;AAAA,4CACnB;AAAA,4CACA,OAAO;AAAA,0CACT;AAAA,wCACF,CAAC,EAAE,CAAC;AAAA,sCACN;AAAA,oCACF;AAAA,kCACF,QAAoB,wBAAI,OAAO,EAAE,WAAW,+EAA+E,UAAU,MAAM,CAAC;AAAA,sCAC5H,wBAAI,OAAO,EAAE,WAAW,+EAA+E,UAAUA;AAAA,oCAC/H;AAAA,kCACF,EAAE,CAAC;AAAA,gCACL,EAAE,CAAC;AAAA,oCACa,yBAAK,OAAO,EAAE,WAAW,oCAAoC,UAAU;AAAA,sCACrE,yBAAK,mBAAmB,EAAE,IAAI,SAAS,UAAU;AAAA,wCAC/C,wBAAI,kBAAkB,SAAS,EAAE,SAAS,MAAM,cAA0B;AAAA,sCACxF;AAAA,sCACA;AAAA,wCACE,MAAM;AAAA,wCACN,WAAW;AAAA,wCACX,UAAU;AAAA,8CACQ,wBAAI,iBAAiB,CAAC,CAAC;AAAA,0CACvC;AAAA,wCACF;AAAA,sCACF;AAAA,oCACF,EAAE,CAAC;AAAA,wCACa,wBAAI,kBAAkB,SAAS,EAAE,SAAS,MAAM,cAA0B,wBAAI,QAAQ,EAAE,SAAS,aAAa,UAAUA,GAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;AAAA,wCAChJ,yBAAK,kBAAkB,SAAS,EAAE,UAAU;AAAA,0CAC1C,yBAAK,kBAAkB,QAAQ,EAAE,UAAU;AAAA,4CACzC,wBAAI,kBAAkB,OAAO,EAAE,SAAS,MAAM,cAA0B,wBAAI,SAAS,EAAE,WAAW,WAAW,UAAUA;AAAA,0CACrI;AAAA,wCACF,EAAE,CAAC,EAAE,CAAC;AAAA,4CACU,wBAAI,kBAAkB,aAAa,EAAE,WAAW,WAAW,UAAUA;AAAA,0CACnF;AAAA,wCACF,EAAE,CAAC;AAAA,sCACL,EAAE,CAAC;AAAA,0CACa;AAAA,wCACd;AAAA,wCACA;AAAA,0CACE,MAAM;AAAA,0CACN,eAAe;AAAA,0CACf,OAAO;AAAA,0CACP,UAAU;AAAA,wCACZ;AAAA,sCACF;AAAA,oCACF,EAAE,CAAC;AAAA,kCACL,EAAE,CAAC;AAAA,sCACa;AAAA,oCACd;AAAA,oCACA;AAAA,sCACE,SAAS;AAAA,sCACT,SAAS,MAAM,YAAY,IAAI;AAAA,sCAC/B,MAAM;AAAA,sCACN,UAAUA,GAAE,gBAAgB;AAAA,oCAC9B;AAAA,kCACF;AAAA,gCACF,EAAE,CAAC;AAAA,gCACH,OAAO,SAAS,QAAoB,yBAAK,OAAO,EAAE,WAAW,2BAA2B,UAAU;AAAA,sCAChF,wBAAI,SAAS,EAAE,SAAS,SAAS,CAAC;AAAA,sCAClC,wBAAI,OAAO,EAAE,WAAW,kCAAkC,UAAU,OAAO,IAAI,CAAC,QAAQ,WAAW;AACjH,+CAAuB;AAAA,sCACrB;AAAA,sCACA;AAAA,wCACE,OAAO;AAAA,wCACP,OAAO,OAAO;AAAA,wCACd,UAAU;AAAA,sCACZ;AAAA,sCACA,OAAO;AAAA,oCACT;AAAA,kCACF,CAAC,EAAE,CAAC;AAAA,gCACN,EAAE,CAAC,IAAI;AAAA,8BACT;AAAA,4BACF;AAAA,0BACF;AAAA,8BACgB,wBAAI,KAAK,cAAc,EAAE,WAAW,OAAO,CAAC;AAAA,wBAC9D,EAAE,CAAC;AAAA,sBACL;AAAA,oBACF;AAAA,kBACF,EAAE,GAAG,IAAI;AAAA,gBACX,CAAC;AAAA,cACH;AAAA,YACF;AAAA,UACF,EAAE,CAAC;AAAA,QACL,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;AAAA,YACO,wBAAI,gBAAgB,QAAQ,EAAE,cAA0B,yBAAK,OAAO,EAAE,WAAW,yCAAyC,UAAU;AAAA,cAClI,wBAAI,gBAAgB,OAAO,EAAE,SAAS,MAAM,cAA0B,wBAAI,QAAQ,EAAE,MAAM,SAAS,SAAS,aAAa,UAAUA,GAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;AAAA,cAC3J,wBAAI,QAAQ,EAAE,MAAM,SAAS,MAAM,UAAU,WAAW,WAAW,UAAUA,GAAE,cAAc,EAAE,CAAC;AAAA,QAClH,EAAE,CAAC,EAAE,CAAC;AAAA,MACR;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;AAIA,IAAI,6BAA6B,MAAM;AACrC,QAAM,EAAE,IAAI,YAAY,IAAI,UAAU;AACtC,QAAM,EAAE,YAAY,WAAW,SAAS,MAAM,IAAI;AAAA,IAChD,eAAe;AAAA,EACjB;AACA,QAAM,QAAQ,CAAC,aAAa,CAAC,CAAC;AAC9B,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,oBAAAC,KAAK,iBAAiB,EAAE,UAAU,aAAyB,oBAAAA,KAAK,gCAAgC,EAAE,WAAW,WAAW,CAAC,EAAE,CAAC;AACrJ;", "names": ["import_jsx_runtime", "t", "jsx2"]}