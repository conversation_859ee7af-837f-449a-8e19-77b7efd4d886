{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-B6ZOPCPA.mjs"], "sourcesContent": ["import {\n  countries,\n  getCountryByIso2\n} from \"./chunk-DG7J63J2.mjs\";\n\n// src/lib/addresses.ts\nvar isSameAddress = (a, b) => {\n  if (!a || !b) {\n    return false;\n  }\n  return a.first_name === b.first_name && a.last_name === b.last_name && a.address_1 === b.address_1 && a.address_2 === b.address_2 && a.city === b.city && a.postal_code === b.postal_code && a.province === b.province && a.country_code === b.country_code;\n};\nvar getFormattedAddress = ({\n  address\n}) => {\n  if (!address) {\n    return [];\n  }\n  const {\n    first_name,\n    last_name,\n    company,\n    address_1,\n    address_2,\n    city,\n    postal_code,\n    province,\n    country,\n    country_code\n  } = address;\n  const name = [first_name, last_name].filter(Boolean).join(\" \");\n  const formattedAddress = [];\n  if (name) {\n    formattedAddress.push(name);\n  }\n  if (company) {\n    formattedAddress.push(company);\n  }\n  if (address_1) {\n    formattedAddress.push(address_1);\n  }\n  if (address_2) {\n    formattedAddress.push(address_2);\n  }\n  const cityProvincePostal = [city, province, postal_code].filter(Boolean).join(\" \");\n  if (cityProvincePostal) {\n    formattedAddress.push(cityProvincePostal);\n  }\n  if (country) {\n    formattedAddress.push(country.display_name);\n  } else if (country_code) {\n    const country2 = getCountryByIso2(country_code);\n    if (country2) {\n      formattedAddress.push(country2.display_name);\n    } else {\n      formattedAddress.push(country_code.toUpperCase());\n    }\n  }\n  return formattedAddress;\n};\nvar getFormattedCountry = (countryCode) => {\n  if (!countryCode) {\n    return \"\";\n  }\n  const country = countries.find((c) => c.iso_2 === countryCode);\n  return country ? country.display_name : countryCode;\n};\n\nexport {\n  isSameAddress,\n  getFormattedAddress,\n  getFormattedCountry\n};\n"], "mappings": ";;;;;;AAMA,IAAI,gBAAgB,CAAC,GAAG,MAAM;AAC5B,MAAI,CAAC,KAAK,CAAC,GAAG;AACZ,WAAO;AAAA,EACT;AACA,SAAO,EAAE,eAAe,EAAE,cAAc,EAAE,cAAc,EAAE,aAAa,EAAE,cAAc,EAAE,aAAa,EAAE,cAAc,EAAE,aAAa,EAAE,SAAS,EAAE,QAAQ,EAAE,gBAAgB,EAAE,eAAe,EAAE,aAAa,EAAE,YAAY,EAAE,iBAAiB,EAAE;AACjP;AACA,IAAI,sBAAsB,CAAC;AAAA,EACzB;AACF,MAAM;AACJ,MAAI,CAAC,SAAS;AACZ,WAAO,CAAC;AAAA,EACV;AACA,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,OAAO,CAAC,YAAY,SAAS,EAAE,OAAO,OAAO,EAAE,KAAK,GAAG;AAC7D,QAAM,mBAAmB,CAAC;AAC1B,MAAI,MAAM;AACR,qBAAiB,KAAK,IAAI;AAAA,EAC5B;AACA,MAAI,SAAS;AACX,qBAAiB,KAAK,OAAO;AAAA,EAC/B;AACA,MAAI,WAAW;AACb,qBAAiB,KAAK,SAAS;AAAA,EACjC;AACA,MAAI,WAAW;AACb,qBAAiB,KAAK,SAAS;AAAA,EACjC;AACA,QAAM,qBAAqB,CAAC,MAAM,UAAU,WAAW,EAAE,OAAO,OAAO,EAAE,KAAK,GAAG;AACjF,MAAI,oBAAoB;AACtB,qBAAiB,KAAK,kBAAkB;AAAA,EAC1C;AACA,MAAI,SAAS;AACX,qBAAiB,KAAK,QAAQ,YAAY;AAAA,EAC5C,WAAW,cAAc;AACvB,UAAM,WAAW,iBAAiB,YAAY;AAC9C,QAAI,UAAU;AACZ,uBAAiB,KAAK,SAAS,YAAY;AAAA,IAC7C,OAAO;AACL,uBAAiB,KAAK,aAAa,YAAY,CAAC;AAAA,IAClD;AAAA,EACF;AACA,SAAO;AACT;AACA,IAAI,sBAAsB,CAAC,gBAAgB;AACzC,MAAI,CAAC,aAAa;AAChB,WAAO;AAAA,EACT;AACA,QAAM,UAAU,UAAU,KAAK,CAAC,MAAM,EAAE,UAAU,WAAW;AAC7D,SAAO,UAAU,QAAQ,eAAe;AAC1C;", "names": []}