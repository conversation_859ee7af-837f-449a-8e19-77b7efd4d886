{"version": 3, "sources": ["../../@dnd-kit/utilities/src/hooks/useCombinedRefs.ts", "../../@dnd-kit/utilities/src/execution-context/canUseDOM.ts", "../../@dnd-kit/utilities/src/type-guards/isWindow.ts", "../../@dnd-kit/utilities/src/type-guards/isNode.ts", "../../@dnd-kit/utilities/src/execution-context/getWindow.ts", "../../@dnd-kit/utilities/src/type-guards/isDocument.ts", "../../@dnd-kit/utilities/src/type-guards/isHTMLElement.ts", "../../@dnd-kit/utilities/src/type-guards/isSVGElement.ts", "../../@dnd-kit/utilities/src/execution-context/getOwnerDocument.ts", "../../@dnd-kit/utilities/src/hooks/useIsomorphicLayoutEffect.ts", "../../@dnd-kit/utilities/src/hooks/useEvent.ts", "../../@dnd-kit/utilities/src/hooks/useInterval.ts", "../../@dnd-kit/utilities/src/hooks/useLatestValue.ts", "../../@dnd-kit/utilities/src/hooks/useLazyMemo.ts", "../../@dnd-kit/utilities/src/hooks/useNodeRef.ts", "../../@dnd-kit/utilities/src/hooks/usePrevious.ts", "../../@dnd-kit/utilities/src/hooks/useUniqueId.ts", "../../@dnd-kit/utilities/src/adjustment.ts", "../../@dnd-kit/utilities/src/event/hasViewportRelativeCoordinates.ts", "../../@dnd-kit/utilities/src/event/isKeyboardEvent.ts", "../../@dnd-kit/utilities/src/event/isTouchEvent.ts", "../../@dnd-kit/utilities/src/coordinates/getEventCoordinates.ts", "../../@dnd-kit/utilities/src/css.ts", "../../@dnd-kit/utilities/src/focus/findFirstFocusableNode.ts", "../../@dnd-kit/accessibility/src/components/HiddenText/HiddenText.tsx", "../../@dnd-kit/accessibility/src/components/LiveRegion/LiveRegion.tsx", "../../@dnd-kit/accessibility/src/hooks/useAnnouncement.ts", "../../@dnd-kit/core/src/components/DndMonitor/context.ts", "../../@dnd-kit/core/src/components/DndMonitor/useDndMonitor.ts", "../../@dnd-kit/core/src/components/DndMonitor/useDndMonitorProvider.tsx", "../../@dnd-kit/core/src/components/Accessibility/defaults.ts", "../../@dnd-kit/core/src/components/Accessibility/Accessibility.tsx", "../../@dnd-kit/core/src/store/actions.ts", "../../@dnd-kit/core/src/utilities/other/noop.ts", "../../@dnd-kit/core/src/sensors/useSensor.ts", "../../@dnd-kit/core/src/sensors/useSensors.ts", "../../@dnd-kit/core/src/utilities/coordinates/constants.ts", "../../@dnd-kit/core/src/utilities/coordinates/distanceBetweenPoints.ts", "../../@dnd-kit/core/src/utilities/coordinates/getRelativeTransformOrigin.ts", "../../@dnd-kit/core/src/utilities/algorithms/helpers.ts", "../../@dnd-kit/core/src/utilities/algorithms/closestCenter.ts", "../../@dnd-kit/core/src/utilities/algorithms/closestCorners.ts", "../../@dnd-kit/core/src/utilities/algorithms/rectIntersection.ts", "../../@dnd-kit/core/src/utilities/algorithms/pointerWithin.ts", "../../@dnd-kit/core/src/utilities/rect/adjustScale.ts", "../../@dnd-kit/core/src/utilities/rect/getRectDelta.ts", "../../@dnd-kit/core/src/utilities/rect/rectAdjustment.ts", "../../@dnd-kit/core/src/utilities/transform/parseTransform.ts", "../../@dnd-kit/core/src/utilities/transform/inverseTransform.ts", "../../@dnd-kit/core/src/utilities/rect/getRect.ts", "../../@dnd-kit/core/src/utilities/rect/getWindowClientRect.ts", "../../@dnd-kit/core/src/utilities/scroll/isFixed.ts", "../../@dnd-kit/core/src/utilities/scroll/isScrollable.ts", "../../@dnd-kit/core/src/utilities/scroll/getScrollableAncestors.ts", "../../@dnd-kit/core/src/utilities/scroll/getScrollableElement.ts", "../../@dnd-kit/core/src/utilities/scroll/getScrollCoordinates.ts", "../../@dnd-kit/core/src/types/direction.ts", "../../@dnd-kit/core/src/utilities/scroll/documentScrollingElement.ts", "../../@dnd-kit/core/src/utilities/scroll/getScrollPosition.ts", "../../@dnd-kit/core/src/utilities/scroll/getScrollDirectionAndSpeed.ts", "../../@dnd-kit/core/src/utilities/scroll/getScrollElementRect.ts", "../../@dnd-kit/core/src/utilities/scroll/getScrollOffsets.ts", "../../@dnd-kit/core/src/utilities/scroll/scrollIntoViewIfNeeded.ts", "../../@dnd-kit/core/src/utilities/rect/Rect.ts", "../../@dnd-kit/core/src/sensors/utilities/Listeners.ts", "../../@dnd-kit/core/src/sensors/utilities/getEventListenerTarget.ts", "../../@dnd-kit/core/src/sensors/utilities/hasExceededDistance.ts", "../../@dnd-kit/core/src/sensors/events.ts", "../../@dnd-kit/core/src/sensors/keyboard/types.ts", "../../@dnd-kit/core/src/sensors/keyboard/defaults.ts", "../../@dnd-kit/core/src/sensors/keyboard/KeyboardSensor.ts", "../../@dnd-kit/core/src/sensors/pointer/AbstractPointerSensor.ts", "../../@dnd-kit/core/src/sensors/pointer/PointerSensor.ts", "../../@dnd-kit/core/src/sensors/mouse/MouseSensor.ts", "../../@dnd-kit/core/src/sensors/touch/TouchSensor.ts", "../../@dnd-kit/core/src/hooks/utilities/useAutoScroller.ts", "../../@dnd-kit/core/src/hooks/utilities/useCachedNode.ts", "../../@dnd-kit/core/src/hooks/utilities/useCombineActivators.ts", "../../@dnd-kit/core/src/hooks/utilities/useDroppableMeasuring.ts", "../../@dnd-kit/core/src/hooks/utilities/useInitialValue.ts", "../../@dnd-kit/core/src/hooks/utilities/useInitialRect.ts", "../../@dnd-kit/core/src/hooks/utilities/useMutationObserver.ts", "../../@dnd-kit/core/src/hooks/utilities/useResizeObserver.ts", "../../@dnd-kit/core/src/hooks/utilities/useRect.ts", "../../@dnd-kit/core/src/hooks/utilities/useRectDelta.ts", "../../@dnd-kit/core/src/hooks/utilities/useScrollableAncestors.ts", "../../@dnd-kit/core/src/hooks/utilities/useScrollOffsets.ts", "../../@dnd-kit/core/src/hooks/utilities/useScrollOffsetsDelta.ts", "../../@dnd-kit/core/src/hooks/utilities/useSensorSetup.ts", "../../@dnd-kit/core/src/hooks/utilities/useSyntheticListeners.ts", "../../@dnd-kit/core/src/hooks/utilities/useWindowRect.ts", "../../@dnd-kit/core/src/hooks/utilities/useRects.ts", "../../@dnd-kit/core/src/utilities/nodes/getMeasurableNode.ts", "../../@dnd-kit/core/src/hooks/utilities/useDragOverlayMeasuring.ts", "../../@dnd-kit/core/src/components/DndContext/defaults.ts", "../../@dnd-kit/core/src/store/constructors.ts", "../../@dnd-kit/core/src/store/context.ts", "../../@dnd-kit/core/src/store/reducer.ts", "../../@dnd-kit/core/src/components/Accessibility/components/RestoreFocus.tsx", "../../@dnd-kit/core/src/modifiers/applyModifiers.ts", "../../@dnd-kit/core/src/components/DndContext/hooks/useMeasuringConfiguration.ts", "../../@dnd-kit/core/src/components/DndContext/hooks/useLayoutShiftScrollCompensation.ts", "../../@dnd-kit/core/src/components/DndContext/DndContext.tsx", "../../@dnd-kit/core/src/hooks/useDraggable.ts", "../../@dnd-kit/core/src/hooks/useDndContext.ts", "../../@dnd-kit/core/src/hooks/useDroppable.ts", "../../@dnd-kit/core/src/components/DragOverlay/components/AnimationManager/AnimationManager.tsx", "../../@dnd-kit/core/src/components/DragOverlay/components/NullifiedContextProvider/NullifiedContextProvider.tsx", "../../@dnd-kit/core/src/components/DragOverlay/components/PositionedOverlay/PositionedOverlay.tsx", "../../@dnd-kit/core/src/components/DragOverlay/hooks/useDropAnimation.ts", "../../@dnd-kit/core/src/components/DragOverlay/hooks/useKey.ts", "../../@dnd-kit/core/src/components/DragOverlay/DragOverlay.tsx", "../../@dnd-kit/sortable/src/utilities/arrayMove.ts", "../../@dnd-kit/sortable/src/utilities/arraySwap.ts", "../../@dnd-kit/sortable/src/utilities/getSortedRects.ts", "../../@dnd-kit/sortable/src/utilities/isValidIndex.ts", "../../@dnd-kit/sortable/src/utilities/itemsEqual.ts", "../../@dnd-kit/sortable/src/utilities/normalizeDisabled.ts", "../../@dnd-kit/sortable/src/strategies/horizontalListSorting.ts", "../../@dnd-kit/sortable/src/strategies/rectSorting.ts", "../../@dnd-kit/sortable/src/strategies/rectSwapping.ts", "../../@dnd-kit/sortable/src/strategies/verticalListSorting.ts", "../../@dnd-kit/sortable/src/components/SortableContext.tsx", "../../@dnd-kit/sortable/src/hooks/defaults.ts", "../../@dnd-kit/sortable/src/hooks/utilities/useDerivedTransform.ts", "../../@dnd-kit/sortable/src/hooks/useSortable.ts", "../../@dnd-kit/sortable/src/types/type-guard.ts", "../../@dnd-kit/sortable/src/sensors/keyboard/sortableKeyboardCoordinates.ts"], "sourcesContent": ["import {useMemo} from 'react';\n\nexport function useCombinedRefs<T>(\n  ...refs: ((node: T) => void)[]\n): (node: T) => void {\n  return useMemo(\n    () => (node: T) => {\n      refs.forEach((ref) => ref(node));\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    refs\n  );\n}\n", "// https://github.com/facebook/react/blob/master/packages/shared/ExecutionEnvironment.js\nexport const canUseDOM =\n  typeof window !== 'undefined' &&\n  typeof window.document !== 'undefined' &&\n  typeof window.document.createElement !== 'undefined';\n", "export function isWindow(element: Object): element is typeof window {\n  const elementString = Object.prototype.toString.call(element);\n  return (\n    elementString === '[object Window]' ||\n    // In Electron context the Window object serializes to [object global]\n    elementString === '[object global]'\n  );\n}\n", "export function isNode(node: Object): node is Node {\n  return 'nodeType' in node;\n}\n", "import {isWindow} from '../type-guards/isWindow';\nimport {isNode} from '../type-guards/isNode';\n\nexport function getWindow(target: Event['target']): typeof window {\n  if (!target) {\n    return window;\n  }\n\n  if (isWindow(target)) {\n    return target;\n  }\n\n  if (!isNode(target)) {\n    return window;\n  }\n\n  return target.ownerDocument?.defaultView ?? window;\n}\n", "import {getWindow} from '../execution-context/getWindow';\n\nexport function isDocument(node: Node): node is Document {\n  const {Document} = getWindow(node);\n\n  return node instanceof Document;\n}\n", "import {getWindow} from '../execution-context/getWindow';\n\nimport {isWindow} from './isWindow';\n\nexport function isHTMLElement(node: Node | Window): node is HTMLElement {\n  if (isWindow(node)) {\n    return false;\n  }\n\n  return node instanceof getWindow(node).HTMLElement;\n}\n", "import {getWindow} from '../execution-context/getWindow';\n\nexport function isSVGElement(node: Node): node is SVGElement {\n  return node instanceof getWindow(node).SVGElement;\n}\n", "import {\n  isWindow,\n  isHTMLElement,\n  isDocument,\n  isNode,\n  isSVGElement,\n} from '../type-guards';\n\nexport function getOwnerDocument(target: Event['target']): Document {\n  if (!target) {\n    return document;\n  }\n\n  if (isWindow(target)) {\n    return target.document;\n  }\n\n  if (!isNode(target)) {\n    return document;\n  }\n\n  if (isDocument(target)) {\n    return target;\n  }\n\n  if (isHTMLElement(target) || isSVGElement(target)) {\n    return target.ownerDocument;\n  }\n\n  return document;\n}\n", "import {useEffect, useLayoutEffect} from 'react';\n\nimport {canUseDOM} from '../execution-context';\n\n/**\n * A hook that resolves to useEffect on the server and useLayoutEffect on the client\n * @param callback {function} Callback function that is invoked when the dependencies of the hook change\n */\nexport const useIsomorphicLayoutEffect = canUseDOM\n  ? useLayoutEffect\n  : useEffect;\n", "import {useCallback, useRef} from 'react';\n\nimport {useIsomorphicLayoutEffect} from './useIsomorphicLayoutEffect';\n\nexport function useEvent<T extends Function>(handler: T | undefined) {\n  const handlerRef = useRef<T | undefined>(handler);\n\n  useIsomorphicLayoutEffect(() => {\n    handlerRef.current = handler;\n  });\n\n  return useCallback(function (...args: any) {\n    return handlerRef.current?.(...args);\n  }, []);\n}\n", "import {useCallback, useRef} from 'react';\n\nexport function useInterval() {\n  const intervalRef = useRef<number | null>(null);\n\n  const set = useCallback((listener: Function, duration: number) => {\n    intervalRef.current = setInterval(listener, duration);\n  }, []);\n\n  const clear = useCallback(() => {\n    if (intervalRef.current !== null) {\n      clearInterval(intervalRef.current);\n      intervalRef.current = null;\n    }\n  }, []);\n\n  return [set, clear] as const;\n}\n", "import {useRef} from 'react';\nimport type {DependencyList} from 'react';\n\nimport {useIsomorphicLayoutEffect} from './useIsomorphicLayoutEffect';\n\nexport function useLatestValue<T extends any>(\n  value: T,\n  dependencies: DependencyList = [value]\n) {\n  const valueRef = useRef<T>(value);\n\n  useIsomorphicLayoutEffect(() => {\n    if (valueRef.current !== value) {\n      valueRef.current = value;\n    }\n  }, dependencies);\n\n  return valueRef;\n}\n", "import {useMemo, useRef} from 'react';\n\nexport function useLazyMemo<T>(\n  callback: (prevValue: T | undefined) => T,\n  dependencies: any[]\n) {\n  const valueRef = useRef<T>();\n\n  return useMemo(\n    () => {\n      const newValue = callback(valueRef.current);\n      valueRef.current = newValue;\n\n      return newValue;\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [...dependencies]\n  );\n}\n", "import {useRef, useCallback} from 'react';\n\nimport {useEvent} from './useEvent';\n\nexport function useNodeRef(\n  onChange?: (\n    newElement: HTMLElement | null,\n    previousElement: HTMLElement | null\n  ) => void\n) {\n  const onChangeHandler = useEvent(onChange);\n  const node = useRef<HTMLElement | null>(null);\n  const setNodeRef = useCallback(\n    (element: HTMLElement | null) => {\n      if (element !== node.current) {\n        onChangeHandler?.(element, node.current);\n      }\n\n      node.current = element;\n    },\n    //eslint-disable-next-line\n    []\n  );\n\n  return [node, setNodeRef] as const;\n}\n", "import {useRef, useEffect} from 'react';\n\nexport function usePrevious<T>(value: T) {\n  const ref = useRef<T>();\n\n  useEffect(() => {\n    ref.current = value;\n  }, [value]);\n\n  return ref.current;\n}\n", "import {useMemo} from 'react';\n\nlet ids: Record<string, number> = {};\n\nexport function useUniqueId(prefix: string, value?: string) {\n  return useMemo(() => {\n    if (value) {\n      return value;\n    }\n\n    const id = ids[prefix] == null ? 0 : ids[prefix] + 1;\n    ids[prefix] = id;\n\n    return `${prefix}-${id}`;\n  }, [prefix, value]);\n}\n", "function createAdjustmentFn(modifier: number) {\n  return <T extends Record<U, number>, U extends string>(\n    object: T,\n    ...adjustments: Partial<T>[]\n  ): T => {\n    return adjustments.reduce<T>(\n      (accumulator, adjustment) => {\n        const entries = Object.entries(adjustment) as [U, number][];\n\n        for (const [key, valueAdjustment] of entries) {\n          const value = accumulator[key];\n\n          if (value != null) {\n            accumulator[key] = (value + modifier * valueAdjustment) as T[U];\n          }\n        }\n\n        return accumulator;\n      },\n      {\n        ...object,\n      }\n    );\n  };\n}\n\nexport const add = createAdjustmentFn(1);\nexport const subtract = createAdjustmentFn(-1);\n", "export function hasViewportRelativeCoordinates(\n  event: Event\n): event is Event & Pick<PointerEvent, 'clientX' | 'clientY'> {\n  return 'clientX' in event && 'clientY' in event;\n}\n", "import {getWindow} from '../execution-context';\n\nexport function isKeyboardEvent(\n  event: Event | undefined | null\n): event is KeyboardEvent {\n  if (!event) {\n    return false;\n  }\n\n  const {KeyboardEvent} = getWindow(event.target);\n\n  return KeyboardEvent && event instanceof KeyboardEvent;\n}\n", "import {getWindow} from '../execution-context';\n\nexport function isTouchEvent(\n  event: Event | undefined | null\n): event is TouchEvent {\n  if (!event) {\n    return false;\n  }\n\n  const {TouchEvent} = getWindow(event.target);\n\n  return TouchEvent && event instanceof TouchEvent;\n}\n", "import type {Coordinates} from './types';\nimport {isTouchEvent, hasViewportRelativeCoordinates} from '../event';\n\n/**\n * Returns the normalized x and y coordinates for mouse and touch events.\n */\nexport function getEventCoordinates(event: Event): Coordinates | null {\n  if (isTouchEvent(event)) {\n    if (event.touches && event.touches.length) {\n      const {clientX: x, clientY: y} = event.touches[0];\n\n      return {\n        x,\n        y,\n      };\n    } else if (event.changedTouches && event.changedTouches.length) {\n      const {clientX: x, clientY: y} = event.changedTouches[0];\n\n      return {\n        x,\n        y,\n      };\n    }\n  }\n\n  if (hasViewportRelativeCoordinates(event)) {\n    return {\n      x: event.clientX,\n      y: event.clientY,\n    };\n  }\n\n  return null;\n}\n", "export type Transform = {\n  x: number;\n  y: number;\n  scaleX: number;\n  scaleY: number;\n};\n\nexport interface Transition {\n  property: string;\n  easing: string;\n  duration: number;\n}\n\nexport const CSS = Object.freeze({\n  Translate: {\n    toString(transform: Transform | null) {\n      if (!transform) {\n        return;\n      }\n\n      const {x, y} = transform;\n\n      return `translate3d(${x ? Math.round(x) : 0}px, ${\n        y ? Math.round(y) : 0\n      }px, 0)`;\n    },\n  },\n  Scale: {\n    toString(transform: Transform | null) {\n      if (!transform) {\n        return;\n      }\n\n      const {scaleX, scaleY} = transform;\n\n      return `scaleX(${scaleX}) scaleY(${scaleY})`;\n    },\n  },\n  Transform: {\n    toString(transform: Transform | null) {\n      if (!transform) {\n        return;\n      }\n\n      return [\n        CSS.Translate.toString(transform),\n        CSS.Scale.toString(transform),\n      ].join(' ');\n    },\n  },\n  Transition: {\n    toString({property, duration, easing}: Transition) {\n      return `${property} ${duration}ms ${easing}`;\n    },\n  },\n});\n", "const SELECTOR =\n  'a,frame,iframe,input:not([type=hidden]):not(:disabled),select:not(:disabled),textarea:not(:disabled),button:not(:disabled),*[tabindex]';\n\nexport function findFirstFocusableNode(\n  element: HTMLElement\n): HTMLElement | null {\n  if (element.matches(SELECTOR)) {\n    return element;\n  }\n\n  return element.querySelector(SELECTOR);\n}\n", "import React from 'react';\n\ninterface Props {\n  id: string;\n  value: string;\n}\n\nconst hiddenStyles: React.CSSProperties = {\n  display: 'none',\n};\n\nexport function HiddenText({id, value}: Props) {\n  return (\n    <div id={id} style={hiddenStyles}>\n      {value}\n    </div>\n  );\n}\n", "import React from 'react';\n\nexport interface Props {\n  id: string;\n  announcement: string;\n  ariaLiveType?: \"polite\" | \"assertive\" | \"off\";\n}\n\nexport function LiveRegion({id, announcement, ariaLiveType = \"assertive\"}: Props) {\n  // Hide element visually but keep it readable by screen readers\n  const visuallyHidden: React.CSSProperties = {\n    position: 'fixed',\n    top: 0,\n    left: 0,\n    width: 1,\n    height: 1,\n    margin: -1,\n    border: 0,\n    padding: 0,\n    overflow: 'hidden',\n    clip: 'rect(0 0 0 0)',\n    clipPath: 'inset(100%)',\n    whiteSpace: 'nowrap',\n  };\n  \n  return (\n    <div\n      id={id}\n      style={visuallyHidden}\n      role=\"status\"\n      aria-live={ariaLiveType}\n      aria-atomic\n    >\n      {announcement}\n    </div>\n  );\n}\n", "import {useCallback, useState} from 'react';\n\nexport function useAnnouncement() {\n  const [announcement, setAnnouncement] = useState('');\n  const announce = useCallback((value: string | undefined) => {\n    if (value != null) {\n      setAnnouncement(value);\n    }\n  }, []);\n\n  return {announce, announcement} as const;\n}\n", "import {createContext} from 'react';\n\nimport type {RegisterListener} from './types';\n\nexport const DndMonitorContext = createContext<RegisterListener | null>(null);\n", "import {useContext, useEffect} from 'react';\n\nimport {DndMonitorContext} from './context';\nimport type {DndMonitorListener} from './types';\n\nexport function useDndMonitor(listener: DndMonitorListener) {\n  const registerListener = useContext(DndMonitorContext);\n\n  useEffect(() => {\n    if (!registerListener) {\n      throw new Error(\n        'useDndMonitor must be used within a children of <DndContext>'\n      );\n    }\n\n    const unsubscribe = registerListener(listener);\n\n    return unsubscribe;\n  }, [listener, registerListener]);\n}\n", "import {useCallback, useState} from 'react';\n\nimport type {DndMonitorListener, DndMonitorEvent} from './types';\n\nexport function useDndMonitorProvider() {\n  const [listeners] = useState(() => new Set<DndMonitorListener>());\n\n  const registerListener = useCallback(\n    (listener) => {\n      listeners.add(listener);\n      return () => listeners.delete(listener);\n    },\n    [listeners]\n  );\n\n  const dispatch = useCallback(\n    ({type, event}: DndMonitorEvent) => {\n      listeners.forEach((listener) => listener[type]?.(event as any));\n    },\n    [listeners]\n  );\n\n  return [dispatch, registerListener] as const;\n}\n", "import type {Announcements, ScreenReaderInstructions} from './types';\n\nexport const defaultScreenReaderInstructions: ScreenReaderInstructions = {\n  draggable: `\n    To pick up a draggable item, press the space bar.\n    While dragging, use the arrow keys to move the item.\n    Press space again to drop the item in its new position, or press escape to cancel.\n  `,\n};\n\nexport const defaultAnnouncements: Announcements = {\n  onDragStart({active}) {\n    return `Picked up draggable item ${active.id}.`;\n  },\n  onDragOver({active, over}) {\n    if (over) {\n      return `Draggable item ${active.id} was moved over droppable area ${over.id}.`;\n    }\n\n    return `Draggable item ${active.id} is no longer over a droppable area.`;\n  },\n  onDragEnd({active, over}) {\n    if (over) {\n      return `Draggable item ${active.id} was dropped over droppable area ${over.id}`;\n    }\n\n    return `Draggable item ${active.id} was dropped.`;\n  },\n  onDragCancel({active}) {\n    return `Dragging was cancelled. Draggable item ${active.id} was dropped.`;\n  },\n};\n", "import React, {useEffect, useMemo, useState} from 'react';\nimport {createPortal} from 'react-dom';\nimport {useUniqueId} from '@dnd-kit/utilities';\nimport {HiddenText, LiveRegion, useAnnouncement} from '@dnd-kit/accessibility';\n\nimport {DndMonitorListener, useDndMonitor} from '../DndMonitor';\n\nimport type {Announcements, ScreenReaderInstructions} from './types';\nimport {\n  defaultAnnouncements,\n  defaultScreenReaderInstructions,\n} from './defaults';\n\ninterface Props {\n  announcements?: Announcements;\n  container?: Element;\n  screenReaderInstructions?: ScreenReaderInstructions;\n  hiddenTextDescribedById: string;\n}\n\nexport function Accessibility({\n  announcements = defaultAnnouncements,\n  container,\n  hiddenTextDescribedById,\n  screenReaderInstructions = defaultScreenReaderInstructions,\n}: Props) {\n  const {announce, announcement} = useAnnouncement();\n  const liveRegionId = useUniqueId(`DndLiveRegion`);\n  const [mounted, setMounted] = useState(false);\n\n  useEffect(() => {\n    setMounted(true);\n  }, []);\n\n  useDndMonitor(\n    useMemo<DndMonitorListener>(\n      () => ({\n        onDragStart({active}) {\n          announce(announcements.onDragStart({active}));\n        },\n        onDragMove({active, over}) {\n          if (announcements.onDragMove) {\n            announce(announcements.onDragMove({active, over}));\n          }\n        },\n        onDragOver({active, over}) {\n          announce(announcements.onDragOver({active, over}));\n        },\n        onDragEnd({active, over}) {\n          announce(announcements.onDragEnd({active, over}));\n        },\n        onDragCancel({active, over}) {\n          announce(announcements.onDragCancel({active, over}));\n        },\n      }),\n      [announce, announcements]\n    )\n  );\n\n  if (!mounted) {\n    return null;\n  }\n\n  const markup = (\n    <>\n      <HiddenText\n        id={hiddenTextDescribedById}\n        value={screenReaderInstructions.draggable}\n      />\n      <LiveRegion id={liveRegionId} announcement={announcement} />\n    </>\n  );\n\n  return container ? createPortal(markup, container) : markup;\n}\n", "import type {Coordinates, UniqueIdentifier} from '../types';\nimport type {DroppableContainer} from './types';\n\nexport enum Action {\n  DragStart = 'dragStart',\n  DragMove = 'dragMove',\n  DragEnd = 'dragEnd',\n  DragCancel = 'dragCancel',\n  DragOver = 'dragOver',\n  RegisterDroppable = 'registerDroppable',\n  SetDroppableDisabled = 'setDroppableDisabled',\n  UnregisterDroppable = 'unregisterDroppable',\n}\n\nexport type Actions =\n  | {\n      type: Action.DragStart;\n      active: UniqueIdentifier;\n      initialCoordinates: Coordinates;\n    }\n  | {type: Action.DragMove; coordinates: Coordinates}\n  | {type: Action.DragEnd}\n  | {type: Action.DragCancel}\n  | {\n      type: Action.RegisterDroppable;\n      element: DroppableContainer;\n    }\n  | {\n      type: Action.SetDroppableDisabled;\n      id: UniqueIdentifier;\n      key: UniqueIdentifier;\n      disabled: boolean;\n    }\n  | {\n      type: Action.UnregisterDroppable;\n      id: UniqueIdentifier;\n      key: UniqueIdentifier;\n    };\n", "export function noop(..._args: any) {}\n", "import {useMemo} from 'react';\n\nimport type {Sensor, SensorDescriptor, SensorOptions} from './types';\n\nexport function useSensor<T extends SensorOptions>(\n  sensor: Sensor<T>,\n  options?: T\n): SensorDescriptor<T> {\n  return useMemo(\n    () => ({\n      sensor,\n      options: options ?? ({} as T),\n    }),\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [sensor, options]\n  );\n}\n", "import {useMemo} from 'react';\n\nimport type {SensorDescriptor, SensorOptions} from './types';\n\nexport function useSensors(\n  ...sensors: (SensorDescriptor<any> | undefined | null)[]\n): SensorDescriptor<SensorOptions>[] {\n  return useMemo(\n    () =>\n      [...sensors].filter(\n        (sensor): sensor is SensorDescriptor<any> => sensor != null\n      ),\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [...sensors]\n  );\n}\n", "import type {Coordinates} from '../../types';\n\nexport const defaultCoordinates: Coordinates = Object.freeze({\n  x: 0,\n  y: 0,\n});\n", "import type {Coordinates} from '../../types';\n\n/**\n * Returns the distance between two points\n */\nexport function distanceBetween(p1: Coordinates, p2: Coordinates) {\n  return Math.sqrt(Math.pow(p1.x - p2.x, 2) + Math.pow(p1.y - p2.y, 2));\n}\n", "import {getEventCoordinates} from '@dnd-kit/utilities';\nimport type {ClientRect} from '../../types';\n\nexport function getRelativeTransformOrigin(\n  event: MouseEvent | TouchEvent | KeyboardEvent,\n  rect: ClientRect\n) {\n  const eventCoordinates = getEventCoordinates(event);\n\n  if (!eventCoordinates) {\n    return '0 0';\n  }\n\n  const transformOrigin = {\n    x: ((eventCoordinates.x - rect.left) / rect.width) * 100,\n    y: ((eventCoordinates.y - rect.top) / rect.height) * 100,\n  };\n\n  return `${transformOrigin.x}% ${transformOrigin.y}%`;\n}\n", "/* eslint-disable no-redeclare */\nimport type {ClientRect} from '../../types';\n\nimport type {Collision, CollisionDescriptor} from './types';\n\n/**\n * Sort collisions from smallest to greatest value\n */\nexport function sortCollisionsAsc(\n  {data: {value: a}}: CollisionDescriptor,\n  {data: {value: b}}: CollisionDescriptor\n) {\n  return a - b;\n}\n\n/**\n * Sort collisions from greatest to smallest value\n */\nexport function sortCollisionsDesc(\n  {data: {value: a}}: CollisionDescriptor,\n  {data: {value: b}}: CollisionDescriptor\n) {\n  return b - a;\n}\n\n/**\n * Returns the coordinates of the corners of a given rectangle:\n * [TopLeft {x, y}, TopRight {x, y}, BottomLeft {x, y}, BottomRight {x, y}]\n */\nexport function cornersOfRectangle({left, top, height, width}: ClientRect) {\n  return [\n    {\n      x: left,\n      y: top,\n    },\n    {\n      x: left + width,\n      y: top,\n    },\n    {\n      x: left,\n      y: top + height,\n    },\n    {\n      x: left + width,\n      y: top + height,\n    },\n  ];\n}\n\n/**\n * Returns the first collision, or null if there isn't one.\n * If a property is specified, returns the specified property of the first collision.\n */\nexport function getFirstCollision(\n  collisions: Collision[] | null | undefined\n): Collision | null;\nexport function getFirstCollision<T extends keyof Collision>(\n  collisions: Collision[] | null | undefined,\n  property: T\n): Collision[T] | null;\nexport function getFirstCollision(\n  collisions: Collision[] | null | undefined,\n  property?: keyof Collision\n) {\n  if (!collisions || collisions.length === 0) {\n    return null;\n  }\n\n  const [firstCollision] = collisions;\n\n  return property ? firstCollision[property] : firstCollision;\n}\n", "import {distanceBetween} from '../coordinates';\nimport type {Coordinates, ClientRect} from '../../types';\n\nimport type {CollisionDescriptor, CollisionDetection} from './types';\nimport {sortCollisionsAsc} from './helpers';\n\n/**\n * Returns the coordinates of the center of a given ClientRect\n */\nfunction centerOfRectangle(\n  rect: ClientRect,\n  left = rect.left,\n  top = rect.top\n): Coordinates {\n  return {\n    x: left + rect.width * 0.5,\n    y: top + rect.height * 0.5,\n  };\n}\n\n/**\n * Returns the closest rectangles from an array of rectangles to the center of a given\n * rectangle.\n */\nexport const closestCenter: CollisionDetection = ({\n  collisionRect,\n  droppableRects,\n  droppableContainers,\n}) => {\n  const centerRect = centerOfRectangle(\n    collisionRect,\n    collisionRect.left,\n    collisionRect.top\n  );\n  const collisions: CollisionDescriptor[] = [];\n\n  for (const droppableContainer of droppableContainers) {\n    const {id} = droppableContainer;\n    const rect = droppableRects.get(id);\n\n    if (rect) {\n      const distBetween = distanceBetween(centerOfRectangle(rect), centerRect);\n\n      collisions.push({id, data: {droppableContainer, value: distBetween}});\n    }\n  }\n\n  return collisions.sort(sortCollisionsAsc);\n};\n", "import {distanceBetween} from '../coordinates';\n\nimport type {CollisionDescriptor, CollisionDetection} from './types';\nimport {cornersOfRectangle, sortCollisionsAsc} from './helpers';\n\n/**\n * Returns the closest rectangles from an array of rectangles to the corners of\n * another rectangle.\n */\nexport const closestCorners: CollisionDetection = ({\n  collisionRect,\n  droppableRects,\n  droppableContainers,\n}) => {\n  const corners = cornersOfRectangle(collisionRect);\n  const collisions: CollisionDescriptor[] = [];\n\n  for (const droppableContainer of droppableContainers) {\n    const {id} = droppableContainer;\n    const rect = droppableRects.get(id);\n\n    if (rect) {\n      const rectCorners = cornersOfRectangle(rect);\n      const distances = corners.reduce((accumulator, corner, index) => {\n        return accumulator + distanceBetween(rectCorners[index], corner);\n      }, 0);\n      const effectiveDistance = Number((distances / 4).toFixed(4));\n\n      collisions.push({\n        id,\n        data: {droppableContainer, value: effectiveDistance},\n      });\n    }\n  }\n\n  return collisions.sort(sortCollisionsAsc);\n};\n", "import type {ClientRect} from '../../types';\n\nimport type {CollisionDescriptor, CollisionDetection} from './types';\nimport {sortCollisionsDesc} from './helpers';\n\n/**\n * Returns the intersecting rectangle area between two rectangles\n */\nexport function getIntersectionRatio(\n  entry: ClientRect,\n  target: ClientRect\n): number {\n  const top = Math.max(target.top, entry.top);\n  const left = Math.max(target.left, entry.left);\n  const right = Math.min(target.left + target.width, entry.left + entry.width);\n  const bottom = Math.min(target.top + target.height, entry.top + entry.height);\n  const width = right - left;\n  const height = bottom - top;\n\n  if (left < right && top < bottom) {\n    const targetArea = target.width * target.height;\n    const entryArea = entry.width * entry.height;\n    const intersectionArea = width * height;\n    const intersectionRatio =\n      intersectionArea / (targetArea + entryArea - intersectionArea);\n\n    return Number(intersectionRatio.toFixed(4));\n  }\n\n  // Rectangles do not overlap, or overlap has an area of zero (edge/corner overlap)\n  return 0;\n}\n\n/**\n * Returns the rectangles that has the greatest intersection area with a given\n * rectangle in an array of rectangles.\n */\nexport const rectIntersection: CollisionDetection = ({\n  collisionRect,\n  droppableRects,\n  droppableContainers,\n}) => {\n  const collisions: CollisionDescriptor[] = [];\n\n  for (const droppableContainer of droppableContainers) {\n    const {id} = droppableContainer;\n    const rect = droppableRects.get(id);\n\n    if (rect) {\n      const intersectionRatio = getIntersectionRatio(rect, collisionRect);\n\n      if (intersectionRatio > 0) {\n        collisions.push({\n          id,\n          data: {droppableContainer, value: intersectionRatio},\n        });\n      }\n    }\n  }\n\n  return collisions.sort(sortCollisionsDesc);\n};\n", "import type {Coordinates, ClientRect} from '../../types';\nimport {distanceBetween} from '../coordinates';\n\nimport type {CollisionDescriptor, CollisionDetection} from './types';\nimport {cornersOfRectangle, sortCollisionsAsc} from './helpers';\n\n/**\n * Check if a given point is contained within a bounding rectangle\n */\nfunction isPointWithinRect(point: Coordinates, rect: ClientRect): boolean {\n  const {top, left, bottom, right} = rect;\n\n  return (\n    top <= point.y && point.y <= bottom && left <= point.x && point.x <= right\n  );\n}\n\n/**\n * Returns the rectangles that the pointer is hovering over\n */\nexport const pointerWithin: CollisionDetection = ({\n  droppableContainers,\n  droppableRects,\n  pointerCoordinates,\n}) => {\n  if (!pointerCoordinates) {\n    return [];\n  }\n\n  const collisions: CollisionDescriptor[] = [];\n\n  for (const droppableContainer of droppableContainers) {\n    const {id} = droppableContainer;\n    const rect = droppableRects.get(id);\n\n    if (rect && isPointWithinRect(pointerCoordinates, rect)) {\n      /* There may be more than a single rectangle intersecting\n       * with the pointer coordinates. In order to sort the\n       * colliding rectangles, we measure the distance between\n       * the pointer and the corners of the intersecting rectangle\n       */\n      const corners = cornersOfRectangle(rect);\n      const distances = corners.reduce((accumulator, corner) => {\n        return accumulator + distanceBetween(pointerCoordinates, corner);\n      }, 0);\n      const effectiveDistance = Number((distances / 4).toFixed(4));\n\n      collisions.push({\n        id,\n        data: {droppableContainer, value: effectiveDistance},\n      });\n    }\n  }\n\n  return collisions.sort(sortCollisionsAsc);\n};\n", "import type {Transform} from '@dnd-kit/utilities';\nimport type {ClientRect} from '../../types';\n\nexport function adjustScale(\n  transform: Transform,\n  rect1: ClientRect | null,\n  rect2: ClientRect | null\n): Transform {\n  return {\n    ...transform,\n    scaleX: rect1 && rect2 ? rect1.width / rect2.width : 1,\n    scaleY: rect1 && rect2 ? rect1.height / rect2.height : 1,\n  };\n}\n", "import type {Coordinates, ClientRect} from '../../types';\nimport {defaultCoordinates} from '../coordinates';\n\nexport function getRectDelta(\n  rect1: ClientRect | null,\n  rect2: ClientRect | null\n): Coordinates {\n  return rect1 && rect2\n    ? {\n        x: rect1.left - rect2.left,\n        y: rect1.top - rect2.top,\n      }\n    : defaultCoordinates;\n}\n", "import type {Coordinates, ClientRect} from '../../types';\n\nexport function createRectAdjustmentFn(modifier: number) {\n  return function adjustClientRect(\n    rect: ClientRect,\n    ...adjustments: Coordinates[]\n  ): ClientRect {\n    return adjustments.reduce<ClientRect>(\n      (acc, adjustment) => ({\n        ...acc,\n        top: acc.top + modifier * adjustment.y,\n        bottom: acc.bottom + modifier * adjustment.y,\n        left: acc.left + modifier * adjustment.x,\n        right: acc.right + modifier * adjustment.x,\n      }),\n      {...rect}\n    );\n  };\n}\n\nexport const getAdjustedRect = createRectAdjustmentFn(1);\n", "import type {Transform} from '@dnd-kit/utilities';\n\nexport function parseTransform(transform: string): Transform | null {\n  if (transform.startsWith('matrix3d(')) {\n    const transformArray = transform.slice(9, -1).split(/, /);\n\n    return {\n      x: +transformArray[12],\n      y: +transformArray[13],\n      scaleX: +transformArray[0],\n      scaleY: +transformArray[5],\n    };\n  } else if (transform.startsWith('matrix(')) {\n    const transformArray = transform.slice(7, -1).split(/, /);\n\n    return {\n      x: +transformArray[4],\n      y: +transformArray[5],\n      scaleX: +transformArray[0],\n      scaleY: +transformArray[3],\n    };\n  }\n\n  return null;\n}\n", "import type {ClientRect} from '../../types';\n\nimport {parseTransform} from './parseTransform';\n\nexport function inverseTransform(\n  rect: ClientRect,\n  transform: string,\n  transformOrigin: string\n): ClientRect {\n  const parsedTransform = parseTransform(transform);\n\n  if (!parsedTransform) {\n    return rect;\n  }\n\n  const {scaleX, scaleY, x: translateX, y: translateY} = parsedTransform;\n\n  const x = rect.left - translateX - (1 - scaleX) * parseFloat(transformOrigin);\n  const y =\n    rect.top -\n    translateY -\n    (1 - scaleY) *\n      parseFloat(transformOrigin.slice(transformOrigin.indexOf(' ') + 1));\n  const w = scaleX ? rect.width / scaleX : rect.width;\n  const h = scaleY ? rect.height / scaleY : rect.height;\n\n  return {\n    width: w,\n    height: h,\n    top: y,\n    right: x + w,\n    bottom: y + h,\n    left: x,\n  };\n}\n", "import {getWindow} from '@dnd-kit/utilities';\n\nimport type {ClientRect} from '../../types';\nimport {inverseTransform} from '../transform';\n\ninterface Options {\n  ignoreTransform?: boolean;\n}\n\nconst defaultOptions: Options = {ignoreTransform: false};\n\n/**\n * Returns the bounding client rect of an element relative to the viewport.\n */\nexport function getClientRect(\n  element: Element,\n  options: Options = defaultOptions\n) {\n  let rect: ClientRect = element.getBoundingClientRect();\n\n  if (options.ignoreTransform) {\n    const {transform, transformOrigin} =\n      getWindow(element).getComputedStyle(element);\n\n    if (transform) {\n      rect = inverseTransform(rect, transform, transformOrigin);\n    }\n  }\n\n  const {top, left, width, height, bottom, right} = rect;\n\n  return {\n    top,\n    left,\n    width,\n    height,\n    bottom,\n    right,\n  };\n}\n\n/**\n * Returns the bounding client rect of an element relative to the viewport.\n *\n * @remarks\n * The ClientRect returned by this method does not take into account transforms\n * applied to the element it measures.\n *\n */\nexport function getTransformAgnosticClientRect(element: Element): ClientRect {\n  return getClientRect(element, {ignoreTransform: true});\n}\n", "import type {ClientRect} from '../../types';\n\nexport function getWindowClientRect(element: typeof window): ClientRect {\n  const width = element.innerWidth;\n  const height = element.innerHeight;\n\n  return {\n    top: 0,\n    left: 0,\n    right: width,\n    bottom: height,\n    width,\n    height,\n  };\n}\n", "import {getWindow} from '@dnd-kit/utilities';\n\nexport function isFixed(\n  node: HTMLElement,\n  computedStyle: CSSStyleDeclaration = getWindow(node).getComputedStyle(node)\n): boolean {\n  return computedStyle.position === 'fixed';\n}\n", "import {getWindow} from '@dnd-kit/utilities';\n\nexport function isScrollable(\n  element: HTMLElement,\n  computedStyle: CSSStyleDeclaration = getWindow(element).getComputedStyle(\n    element\n  )\n): boolean {\n  const overflowRegex = /(auto|scroll|overlay)/;\n  const properties = ['overflow', 'overflowX', 'overflowY'];\n\n  return properties.some((property) => {\n    const value = computedStyle[property as keyof CSSStyleDeclaration];\n\n    return typeof value === 'string' ? overflowRegex.test(value) : false;\n  });\n}\n", "import {\n  getWindow,\n  isDocument,\n  isHTMLElement,\n  isSVGElement,\n} from '@dnd-kit/utilities';\n\nimport {isFixed} from './isFixed';\nimport {isScrollable} from './isScrollable';\n\nexport function getScrollableAncestors(\n  element: Node | null,\n  limit?: number\n): Element[] {\n  const scrollParents: Element[] = [];\n\n  function findScrollableAncestors(node: Node | null): Element[] {\n    if (limit != null && scrollParents.length >= limit) {\n      return scrollParents;\n    }\n\n    if (!node) {\n      return scrollParents;\n    }\n\n    if (\n      isDocument(node) &&\n      node.scrollingElement != null &&\n      !scrollParents.includes(node.scrollingElement)\n    ) {\n      scrollParents.push(node.scrollingElement);\n\n      return scrollParents;\n    }\n\n    if (!isHTMLElement(node) || isSVGElement(node)) {\n      return scrollParents;\n    }\n\n    if (scrollParents.includes(node)) {\n      return scrollParents;\n    }\n\n    const computedStyle = getWindow(element).getComputedStyle(node);\n\n    if (node !== element) {\n      if (isScrollable(node, computedStyle)) {\n        scrollParents.push(node);\n      }\n    }\n\n    if (isFixed(node, computedStyle)) {\n      return scrollParents;\n    }\n\n    return findScrollableAncestors(node.parentNode);\n  }\n\n  if (!element) {\n    return scrollParents;\n  }\n\n  return findScrollableAncestors(element);\n}\n\nexport function getFirstScrollableAncestor(node: Node | null): Element | null {\n  const [firstScrollableAncestor] = getScrollableAncestors(node, 1);\n\n  return firstScrollableAncestor ?? null;\n}\n", "import {\n  canUseDOM,\n  isHTMLElement,\n  isDocument,\n  getOwnerDocument,\n  isNode,\n  isWindow,\n} from '@dnd-kit/utilities';\n\nexport function getScrollableElement(element: EventTarget | null) {\n  if (!canUseDOM || !element) {\n    return null;\n  }\n\n  if (isWindow(element)) {\n    return element;\n  }\n\n  if (!isNode(element)) {\n    return null;\n  }\n\n  if (\n    isDocument(element) ||\n    element === getOwnerDocument(element).scrollingElement\n  ) {\n    return window;\n  }\n\n  if (isHTMLElement(element)) {\n    return element;\n  }\n\n  return null;\n}\n", "import {isWindow} from '@dnd-kit/utilities';\n\nimport type {Coordinates} from '../../types';\n\nexport function getScrollXCoordinate(element: Element | typeof window): number {\n  if (isWindow(element)) {\n    return element.scrollX;\n  }\n\n  return element.scrollLeft;\n}\n\nexport function getScrollYCoordinate(element: Element | typeof window): number {\n  if (isWindow(element)) {\n    return element.scrollY;\n  }\n\n  return element.scrollTop;\n}\n\nexport function getScrollCoordinates(\n  element: Element | typeof window\n): Coordinates {\n  return {\n    x: getScrollXCoordinate(element),\n    y: getScrollYCoordinate(element),\n  };\n}\n", "export enum Direction {\n  Forward = 1,\n  Backward = -1,\n}\n", "import {canUseDOM} from '@dnd-kit/utilities';\n\nexport function isDocumentScrollingElement(element: Element | null) {\n  if (!canUseDOM || !element) {\n    return false;\n  }\n\n  return element === document.scrollingElement;\n}\n", "import {isDocumentScrollingElement} from './documentScrollingElement';\n\nexport function getScrollPosition(scrollingContainer: Element) {\n  const minScroll = {\n    x: 0,\n    y: 0,\n  };\n  const dimensions = isDocumentScrollingElement(scrollingContainer)\n    ? {\n        height: window.innerHeight,\n        width: window.innerWidth,\n      }\n    : {\n        height: scrollingContainer.clientHeight,\n        width: scrollingContainer.clientWidth,\n      };\n  const maxScroll = {\n    x: scrollingContainer.scrollWidth - dimensions.width,\n    y: scrollingContainer.scrollHeight - dimensions.height,\n  };\n\n  const isTop = scrollingContainer.scrollTop <= minScroll.y;\n  const isLeft = scrollingContainer.scrollLeft <= minScroll.x;\n  const isBottom = scrollingContainer.scrollTop >= maxScroll.y;\n  const isRight = scrollingContainer.scrollLeft >= maxScroll.x;\n\n  return {\n    isTop,\n    isLeft,\n    isBottom,\n    isRight,\n    maxScroll,\n    minScroll,\n  };\n}\n", "import {Direction, ClientRect} from '../../types';\nimport {getScrollPosition} from './getScrollPosition';\n\ninterface PositionalCoordinates\n  extends Pick<ClientRect, 'top' | 'left' | 'right' | 'bottom'> {}\n\nconst defaultThreshold = {\n  x: 0.2,\n  y: 0.2,\n};\n\nexport function getScrollDirectionAndSpeed(\n  scrollContainer: Element,\n  scrollContainerRect: ClientRect,\n  {top, left, right, bottom}: PositionalCoordinates,\n  acceleration = 10,\n  thresholdPercentage = defaultThreshold\n) {\n  const {isTop, isBottom, isLeft, isRight} = getScrollPosition(scrollContainer);\n\n  const direction = {\n    x: 0,\n    y: 0,\n  };\n  const speed = {\n    x: 0,\n    y: 0,\n  };\n  const threshold = {\n    height: scrollContainerRect.height * thresholdPercentage.y,\n    width: scrollContainerRect.width * thresholdPercentage.x,\n  };\n\n  if (!isTop && top <= scrollContainerRect.top + threshold.height) {\n    // Scroll Up\n    direction.y = Direction.Backward;\n    speed.y =\n      acceleration *\n      Math.abs(\n        (scrollContainerRect.top + threshold.height - top) / threshold.height\n      );\n  } else if (\n    !isBottom &&\n    bottom >= scrollContainerRect.bottom - threshold.height\n  ) {\n    // Scroll Down\n    direction.y = Direction.Forward;\n    speed.y =\n      acceleration *\n      Math.abs(\n        (scrollContainerRect.bottom - threshold.height - bottom) /\n          threshold.height\n      );\n  }\n\n  if (!isRight && right >= scrollContainerRect.right - threshold.width) {\n    // Scroll Right\n    direction.x = Direction.Forward;\n    speed.x =\n      acceleration *\n      Math.abs(\n        (scrollContainerRect.right - threshold.width - right) / threshold.width\n      );\n  } else if (!isLeft && left <= scrollContainerRect.left + threshold.width) {\n    // Scroll Left\n    direction.x = Direction.Backward;\n    speed.x =\n      acceleration *\n      Math.abs(\n        (scrollContainerRect.left + threshold.width - left) / threshold.width\n      );\n  }\n\n  return {\n    direction,\n    speed,\n  };\n}\n", "export function getScrollElementRect(element: Element) {\n  if (element === document.scrollingElement) {\n    const {innerWidth, innerHeight} = window;\n\n    return {\n      top: 0,\n      left: 0,\n      right: innerWidth,\n      bottom: innerHeight,\n      width: innerWidth,\n      height: innerHeight,\n    };\n  }\n\n  const {top, left, right, bottom} = element.getBoundingClientRect();\n\n  return {\n    top,\n    left,\n    right,\n    bottom,\n    width: element.clientWidth,\n    height: element.clientHeight,\n  };\n}\n", "import {add} from '@dnd-kit/utilities';\n\nimport type {Coordinates} from '../../types';\nimport {\n  getScrollCoordinates,\n  getScrollXCoordinate,\n  getScrollYCoordinate,\n} from './getScrollCoordinates';\nimport {defaultCoordinates} from '../coordinates';\n\nexport function getScrollOffsets(scrollableAncestors: Element[]): Coordinates {\n  return scrollableAncestors.reduce<Coordinates>((acc, node) => {\n    return add(acc, getScrollCoordinates(node));\n  }, defaultCoordinates);\n}\n\nexport function getScrollXOffset(scrollableAncestors: Element[]): number {\n  return scrollableAncestors.reduce<number>((acc, node) => {\n    return acc + getScrollXCoordinate(node);\n  }, 0);\n}\n\nexport function getScrollYOffset(scrollableAncestors: Element[]): number {\n  return scrollableAncestors.reduce<number>((acc, node) => {\n    return acc + getScrollYCoordinate(node);\n  }, 0);\n}\n", "import type {ClientRect} from '../../types';\nimport {getClientRect} from '../rect/getRect';\nimport {getFirstScrollableAncestor} from './getScrollableAncestors';\n\nexport function scrollIntoViewIfNeeded(\n  element: HTMLElement | null | undefined,\n  measure: (node: HTMLElement) => ClientRect = getClientRect\n) {\n  if (!element) {\n    return;\n  }\n\n  const {top, left, bottom, right} = measure(element);\n  const firstScrollableAncestor = getFirstScrollableAncestor(element);\n\n  if (!firstScrollableAncestor) {\n    return;\n  }\n\n  if (\n    bottom <= 0 ||\n    right <= 0 ||\n    top >= window.innerHeight ||\n    left >= window.innerWidth\n  ) {\n    element.scrollIntoView({\n      block: 'center',\n      inline: 'center',\n    });\n  }\n}\n", "import type {ClientRect} from '../../types/rect';\nimport {\n  getScrollableAncestors,\n  getScrollOffsets,\n  getScrollXOffset,\n  getScrollYOffset,\n} from '../scroll';\n\nconst properties = [\n  ['x', ['left', 'right'], getScrollXOffset],\n  ['y', ['top', 'bottom'], getScrollYOffset],\n] as const;\n\nexport class Rect {\n  constructor(rect: ClientRect, element: Element) {\n    const scrollableAncestors = getScrollableAncestors(element);\n    const scrollOffsets = getScrollOffsets(scrollableAncestors);\n\n    this.rect = {...rect};\n    this.width = rect.width;\n    this.height = rect.height;\n\n    for (const [axis, keys, getScrollOffset] of properties) {\n      for (const key of keys) {\n        Object.defineProperty(this, key, {\n          get: () => {\n            const currentOffsets = getScrollOffset(scrollableAncestors);\n            const scrollOffsetsDeltla = scrollOffsets[axis] - currentOffsets;\n\n            return this.rect[key] + scrollOffsetsDeltla;\n          },\n          enumerable: true,\n        });\n      }\n    }\n\n    Object.defineProperty(this, 'rect', {enumerable: false});\n  }\n\n  private rect: ClientRect;\n\n  public width: number;\n\n  public height: number;\n\n  // The below properties are set by the `Object.defineProperty` calls in the constructor\n  // @ts-ignore\n  public top: number;\n  // @ts-ignore\n  public bottom: number;\n  // @ts-ignore\n  public right: number;\n  // @ts-ignore\n  public left: number;\n}\n", "export class Listeners {\n  private listeners: [\n    string,\n    EventListenerOrEventListenerObject,\n    AddEventListenerOptions | boolean | undefined\n  ][] = [];\n\n  constructor(private target: EventTarget | null) {}\n\n  public add<T extends Event>(\n    eventName: string,\n    handler: (event: T) => void,\n    options?: AddEventListenerOptions | boolean\n  ) {\n    this.target?.addEventListener(eventName, handler as EventListener, options);\n    this.listeners.push([eventName, handler as EventListener, options]);\n  }\n\n  public removeAll = () => {\n    this.listeners.forEach((listener) =>\n      this.target?.removeEventListener(...listener)\n    );\n  };\n}\n", "import {getOwnerDocument, getWindow} from '@dnd-kit/utilities';\n\nexport function getEventListenerTarget(\n  target: EventTarget | null\n): EventTarget | Document {\n  // If the `event.target` element is removed from the document events will still be targeted\n  // at it, and hence won't always bubble up to the window or document anymore.\n  // If there is any risk of an element being removed while it is being dragged,\n  // the best practice is to attach the event listeners directly to the target.\n  // https://developer.mozilla.org/en-US/docs/Web/API/EventTarget\n\n  const {EventTarget} = getWindow(target);\n\n  return target instanceof EventTarget ? target : getOwnerDocument(target);\n}\n", "import type {Coordinates, DistanceMeasurement} from '../../types';\n\nexport function hasExceededDistance(\n  delta: Coordinates,\n  measurement: DistanceMeasurement\n): boolean {\n  const dx = Math.abs(delta.x);\n  const dy = Math.abs(delta.y);\n\n  if (typeof measurement === 'number') {\n    return Math.sqrt(dx ** 2 + dy ** 2) > measurement;\n  }\n\n  if ('x' in measurement && 'y' in measurement) {\n    return dx > measurement.x && dy > measurement.y;\n  }\n\n  if ('x' in measurement) {\n    return dx > measurement.x;\n  }\n\n  if ('y' in measurement) {\n    return dy > measurement.y;\n  }\n\n  return false;\n}\n", "export enum EventName {\n  Click = 'click',\n  DragStart = 'dragstart',\n  Keydown = 'keydown',\n  ContextMenu = 'contextmenu',\n  Resize = 'resize',\n  SelectionChange = 'selectionchange',\n  VisibilityChange = 'visibilitychange',\n}\n\nexport function preventDefault(event: Event) {\n  event.preventDefault();\n}\n\nexport function stopPropagation(event: Event) {\n  event.stopPropagation();\n}\n", "import type {Coordinates, UniqueIdentifier} from '../../types';\nimport type {SensorContext} from '../types';\n\nexport enum KeyboardCode {\n  Space = 'Space',\n  Down = 'ArrowDown',\n  Right = 'ArrowRight',\n  Left = 'ArrowLeft',\n  Up = 'ArrowUp',\n  Esc = 'Escape',\n  Enter = 'Enter',\n  Tab = 'Tab',\n}\n\nexport type KeyboardCodes = {\n  start: KeyboardEvent['code'][];\n  cancel: KeyboardEvent['code'][];\n  end: KeyboardEvent['code'][];\n};\n\nexport type KeyboardCoordinateGetter = (\n  event: KeyboardEvent,\n  args: {\n    active: UniqueIdentifier;\n    currentCoordinates: Coordinates;\n    context: SensorContext;\n  }\n) => Coordinates | void;\n", "import {KeyboardCoordinateGetter, KeyboardCode, KeyboardCodes} from './types';\n\nexport const defaultKeyboardCodes: KeyboardCodes = {\n  start: [KeyboardCode.Space, KeyboardCode.Enter],\n  cancel: [KeyboardCode.Esc],\n  end: [KeyboardCode.Space, KeyboardCode.Enter, KeyboardCode.Tab],\n};\n\nexport const defaultKeyboardCoordinateGetter: KeyboardCoordinateGetter = (\n  event,\n  {currentCoordinates}\n) => {\n  switch (event.code) {\n    case KeyboardCode.Right:\n      return {\n        ...currentCoordinates,\n        x: currentCoordinates.x + 25,\n      };\n    case KeyboardCode.Left:\n      return {\n        ...currentCoordinates,\n        x: currentCoordinates.x - 25,\n      };\n    case KeyboardCode.Down:\n      return {\n        ...currentCoordinates,\n        y: currentCoordinates.y + 25,\n      };\n    case KeyboardCode.Up:\n      return {\n        ...currentCoordinates,\n        y: currentCoordinates.y - 25,\n      };\n  }\n\n  return undefined;\n};\n", "import {\n  add as getAdjustedCoordinates,\n  subtract as getCoordinates<PERSON><PERSON><PERSON>,\n  getOwnerDocument,\n  getWindow,\n  isKeyboardEvent,\n} from '@dnd-kit/utilities';\n\nimport type {Coordinates} from '../../types';\nimport {\n  defaultCoordinates,\n  getScrollPosition,\n  getScrollElementRect,\n} from '../../utilities';\nimport {scrollIntoViewIfNeeded} from '../../utilities/scroll';\nimport {EventName} from '../events';\nimport {Listeners} from '../utilities';\nimport type {\n  Activators,\n  SensorInstance,\n  SensorProps,\n  SensorOptions,\n} from '../types';\n\nimport {KeyboardCoordinateGetter, KeyboardCode, KeyboardCodes} from './types';\nimport {\n  defaultKeyboardCodes,\n  defaultKeyboardCoordinateGetter,\n} from './defaults';\n\nexport interface KeyboardSensorOptions extends SensorOptions {\n  keyboardCodes?: KeyboardCodes;\n  coordinateGetter?: KeyboardCoordinateGetter;\n  scrollBehavior?: ScrollBehavior;\n  onActivation?({event}: {event: KeyboardEvent}): void;\n}\n\nexport type KeyboardSensorProps = SensorProps<KeyboardSensorOptions>;\n\nexport class KeyboardSensor implements SensorInstance {\n  public autoScrollEnabled = false;\n  private referenceCoordinates: Coordinates | undefined;\n  private listeners: Listeners;\n  private windowListeners: Listeners;\n\n  constructor(private props: KeyboardSensorProps) {\n    const {\n      event: {target},\n    } = props;\n\n    this.props = props;\n    this.listeners = new Listeners(getOwnerDocument(target));\n    this.windowListeners = new Listeners(getWindow(target));\n    this.handleKeyDown = this.handleKeyDown.bind(this);\n    this.handleCancel = this.handleCancel.bind(this);\n\n    this.attach();\n  }\n\n  private attach() {\n    this.handleStart();\n\n    this.windowListeners.add(EventName.Resize, this.handleCancel);\n    this.windowListeners.add(EventName.VisibilityChange, this.handleCancel);\n\n    setTimeout(() => this.listeners.add(EventName.Keydown, this.handleKeyDown));\n  }\n\n  private handleStart() {\n    const {activeNode, onStart} = this.props;\n    const node = activeNode.node.current;\n\n    if (node) {\n      scrollIntoViewIfNeeded(node);\n    }\n\n    onStart(defaultCoordinates);\n  }\n\n  private handleKeyDown(event: Event) {\n    if (isKeyboardEvent(event)) {\n      const {active, context, options} = this.props;\n      const {\n        keyboardCodes = defaultKeyboardCodes,\n        coordinateGetter = defaultKeyboardCoordinateGetter,\n        scrollBehavior = 'smooth',\n      } = options;\n      const {code} = event;\n\n      if (keyboardCodes.end.includes(code)) {\n        this.handleEnd(event);\n        return;\n      }\n\n      if (keyboardCodes.cancel.includes(code)) {\n        this.handleCancel(event);\n        return;\n      }\n\n      const {collisionRect} = context.current;\n      const currentCoordinates = collisionRect\n        ? {x: collisionRect.left, y: collisionRect.top}\n        : defaultCoordinates;\n\n      if (!this.referenceCoordinates) {\n        this.referenceCoordinates = currentCoordinates;\n      }\n\n      const newCoordinates = coordinateGetter(event, {\n        active,\n        context: context.current,\n        currentCoordinates,\n      });\n\n      if (newCoordinates) {\n        const coordinatesDelta = getCoordinatesDelta(\n          newCoordinates,\n          currentCoordinates\n        );\n        const scrollDelta = {\n          x: 0,\n          y: 0,\n        };\n        const {scrollableAncestors} = context.current;\n\n        for (const scrollContainer of scrollableAncestors) {\n          const direction = event.code;\n          const {isTop, isRight, isLeft, isBottom, maxScroll, minScroll} =\n            getScrollPosition(scrollContainer);\n          const scrollElementRect = getScrollElementRect(scrollContainer);\n\n          const clampedCoordinates = {\n            x: Math.min(\n              direction === KeyboardCode.Right\n                ? scrollElementRect.right - scrollElementRect.width / 2\n                : scrollElementRect.right,\n              Math.max(\n                direction === KeyboardCode.Right\n                  ? scrollElementRect.left\n                  : scrollElementRect.left + scrollElementRect.width / 2,\n                newCoordinates.x\n              )\n            ),\n            y: Math.min(\n              direction === KeyboardCode.Down\n                ? scrollElementRect.bottom - scrollElementRect.height / 2\n                : scrollElementRect.bottom,\n              Math.max(\n                direction === KeyboardCode.Down\n                  ? scrollElementRect.top\n                  : scrollElementRect.top + scrollElementRect.height / 2,\n                newCoordinates.y\n              )\n            ),\n          };\n\n          const canScrollX =\n            (direction === KeyboardCode.Right && !isRight) ||\n            (direction === KeyboardCode.Left && !isLeft);\n          const canScrollY =\n            (direction === KeyboardCode.Down && !isBottom) ||\n            (direction === KeyboardCode.Up && !isTop);\n\n          if (canScrollX && clampedCoordinates.x !== newCoordinates.x) {\n            const newScrollCoordinates =\n              scrollContainer.scrollLeft + coordinatesDelta.x;\n            const canScrollToNewCoordinates =\n              (direction === KeyboardCode.Right &&\n                newScrollCoordinates <= maxScroll.x) ||\n              (direction === KeyboardCode.Left &&\n                newScrollCoordinates >= minScroll.x);\n\n            if (canScrollToNewCoordinates && !coordinatesDelta.y) {\n              // We don't need to update coordinates, the scroll adjustment alone will trigger\n              // logic to auto-detect the new container we are over\n              scrollContainer.scrollTo({\n                left: newScrollCoordinates,\n                behavior: scrollBehavior,\n              });\n              return;\n            }\n\n            if (canScrollToNewCoordinates) {\n              scrollDelta.x = scrollContainer.scrollLeft - newScrollCoordinates;\n            } else {\n              scrollDelta.x =\n                direction === KeyboardCode.Right\n                  ? scrollContainer.scrollLeft - maxScroll.x\n                  : scrollContainer.scrollLeft - minScroll.x;\n            }\n\n            if (scrollDelta.x) {\n              scrollContainer.scrollBy({\n                left: -scrollDelta.x,\n                behavior: scrollBehavior,\n              });\n            }\n            break;\n          } else if (canScrollY && clampedCoordinates.y !== newCoordinates.y) {\n            const newScrollCoordinates =\n              scrollContainer.scrollTop + coordinatesDelta.y;\n            const canScrollToNewCoordinates =\n              (direction === KeyboardCode.Down &&\n                newScrollCoordinates <= maxScroll.y) ||\n              (direction === KeyboardCode.Up &&\n                newScrollCoordinates >= minScroll.y);\n\n            if (canScrollToNewCoordinates && !coordinatesDelta.x) {\n              // We don't need to update coordinates, the scroll adjustment alone will trigger\n              // logic to auto-detect the new container we are over\n              scrollContainer.scrollTo({\n                top: newScrollCoordinates,\n                behavior: scrollBehavior,\n              });\n              return;\n            }\n\n            if (canScrollToNewCoordinates) {\n              scrollDelta.y = scrollContainer.scrollTop - newScrollCoordinates;\n            } else {\n              scrollDelta.y =\n                direction === KeyboardCode.Down\n                  ? scrollContainer.scrollTop - maxScroll.y\n                  : scrollContainer.scrollTop - minScroll.y;\n            }\n\n            if (scrollDelta.y) {\n              scrollContainer.scrollBy({\n                top: -scrollDelta.y,\n                behavior: scrollBehavior,\n              });\n            }\n\n            break;\n          }\n        }\n\n        this.handleMove(\n          event,\n          getAdjustedCoordinates(\n            getCoordinatesDelta(newCoordinates, this.referenceCoordinates),\n            scrollDelta\n          )\n        );\n      }\n    }\n  }\n\n  private handleMove(event: Event, coordinates: Coordinates) {\n    const {onMove} = this.props;\n\n    event.preventDefault();\n    onMove(coordinates);\n  }\n\n  private handleEnd(event: Event) {\n    const {onEnd} = this.props;\n\n    event.preventDefault();\n    this.detach();\n    onEnd();\n  }\n\n  private handleCancel(event: Event) {\n    const {onCancel} = this.props;\n\n    event.preventDefault();\n    this.detach();\n    onCancel();\n  }\n\n  private detach() {\n    this.listeners.removeAll();\n    this.windowListeners.removeAll();\n  }\n\n  static activators: Activators<KeyboardSensorOptions> = [\n    {\n      eventName: 'onKeyDown' as const,\n      handler: (\n        event: React.KeyboardEvent,\n        {keyboardCodes = defaultKeyboardCodes, onActivation},\n        {active}\n      ) => {\n        const {code} = event.nativeEvent;\n\n        if (keyboardCodes.start.includes(code)) {\n          const activator = active.activatorNode.current;\n\n          if (activator && event.target !== activator) {\n            return false;\n          }\n\n          event.preventDefault();\n\n          onActivation?.({event: event.nativeEvent});\n\n          return true;\n        }\n\n        return false;\n      },\n    },\n  ];\n}\n", "import {\n  subtract as getCoordina<PERSON><PERSON><PERSON><PERSON>,\n  getEventCoordinates,\n  getOwnerDocument,\n  getWindow,\n} from '@dnd-kit/utilities';\n\nimport {defaultCoordinates} from '../../utilities';\nimport {\n  getEventListenerTarget,\n  hasExceededDistance,\n  Listeners,\n} from '../utilities';\nimport {EventName, preventDefault, stopPropagation} from '../events';\nimport {KeyboardCode} from '../keyboard';\nimport type {SensorInstance, SensorProps, SensorOptions} from '../types';\nimport type {Coordinates, DistanceMeasurement} from '../../types';\n\ninterface DistanceConstraint {\n  distance: DistanceMeasurement;\n  tolerance?: DistanceMeasurement;\n}\n\ninterface DelayConstraint {\n  delay: number;\n  tolerance: DistanceMeasurement;\n}\n\ninterface EventDescriptor {\n  name: keyof DocumentEventMap;\n  passive?: boolean;\n}\n\nexport interface PointerEventHandlers {\n  cancel?: EventDescriptor;\n  move: EventDescriptor;\n  end: EventDescriptor;\n}\n\nexport type PointerActivationConstraint =\n  | DelayConstraint\n  | DistanceConstraint\n  | (DelayConstraint & DistanceConstraint);\n\nfunction isDistanceConstraint(\n  constraint: PointerActivationConstraint\n): constraint is PointerActivationConstraint & DistanceConstraint {\n  return Boolean(constraint && 'distance' in constraint);\n}\n\nfunction isDelayConstraint(\n  constraint: PointerActivationConstraint\n): constraint is DelayConstraint {\n  return Boolean(constraint && 'delay' in constraint);\n}\n\nexport interface AbstractPointerSensorOptions extends SensorOptions {\n  activationConstraint?: PointerActivationConstraint;\n  bypassActivationConstraint?(\n    props: Pick<AbstractPointerSensorProps, 'activeNode' | 'event' | 'options'>\n  ): boolean;\n  onActivation?({event}: {event: Event}): void;\n}\n\nexport type AbstractPointerSensorProps =\n  SensorProps<AbstractPointerSensorOptions>;\n\nexport class AbstractPointerSensor implements SensorInstance {\n  public autoScrollEnabled = true;\n  private document: Document;\n  private activated: boolean = false;\n  private initialCoordinates: Coordinates;\n  private timeoutId: NodeJS.Timeout | null = null;\n  private listeners: Listeners;\n  private documentListeners: Listeners;\n  private windowListeners: Listeners;\n\n  constructor(\n    private props: AbstractPointerSensorProps,\n    private events: PointerEventHandlers,\n    listenerTarget = getEventListenerTarget(props.event.target)\n  ) {\n    const {event} = props;\n    const {target} = event;\n\n    this.props = props;\n    this.events = events;\n    this.document = getOwnerDocument(target);\n    this.documentListeners = new Listeners(this.document);\n    this.listeners = new Listeners(listenerTarget);\n    this.windowListeners = new Listeners(getWindow(target));\n    this.initialCoordinates = getEventCoordinates(event) ?? defaultCoordinates;\n    this.handleStart = this.handleStart.bind(this);\n    this.handleMove = this.handleMove.bind(this);\n    this.handleEnd = this.handleEnd.bind(this);\n    this.handleCancel = this.handleCancel.bind(this);\n    this.handleKeydown = this.handleKeydown.bind(this);\n    this.removeTextSelection = this.removeTextSelection.bind(this);\n\n    this.attach();\n  }\n\n  private attach() {\n    const {\n      events,\n      props: {\n        options: {activationConstraint, bypassActivationConstraint},\n      },\n    } = this;\n\n    this.listeners.add(events.move.name, this.handleMove, {passive: false});\n    this.listeners.add(events.end.name, this.handleEnd);\n\n    if (events.cancel) {\n      this.listeners.add(events.cancel.name, this.handleCancel);\n    }\n\n    this.windowListeners.add(EventName.Resize, this.handleCancel);\n    this.windowListeners.add(EventName.DragStart, preventDefault);\n    this.windowListeners.add(EventName.VisibilityChange, this.handleCancel);\n    this.windowListeners.add(EventName.ContextMenu, preventDefault);\n    this.documentListeners.add(EventName.Keydown, this.handleKeydown);\n\n    if (activationConstraint) {\n      if (\n        bypassActivationConstraint?.({\n          event: this.props.event,\n          activeNode: this.props.activeNode,\n          options: this.props.options,\n        })\n      ) {\n        return this.handleStart();\n      }\n\n      if (isDelayConstraint(activationConstraint)) {\n        this.timeoutId = setTimeout(\n          this.handleStart,\n          activationConstraint.delay\n        );\n        this.handlePending(activationConstraint);\n        return;\n      }\n\n      if (isDistanceConstraint(activationConstraint)) {\n        this.handlePending(activationConstraint);\n        return;\n      }\n    }\n\n    this.handleStart();\n  }\n\n  private detach() {\n    this.listeners.removeAll();\n    this.windowListeners.removeAll();\n\n    // Wait until the next event loop before removing document listeners\n    // This is necessary because we listen for `click` and `selection` events on the document\n    setTimeout(this.documentListeners.removeAll, 50);\n\n    if (this.timeoutId !== null) {\n      clearTimeout(this.timeoutId);\n      this.timeoutId = null;\n    }\n  }\n\n  private handlePending(\n    constraint: PointerActivationConstraint,\n    offset?: Coordinates | undefined\n  ): void {\n    const {active, onPending} = this.props;\n    onPending(active, constraint, this.initialCoordinates, offset);\n  }\n\n  private handleStart() {\n    const {initialCoordinates} = this;\n    const {onStart} = this.props;\n\n    if (initialCoordinates) {\n      this.activated = true;\n\n      // Stop propagation of click events once activation constraints are met\n      this.documentListeners.add(EventName.Click, stopPropagation, {\n        capture: true,\n      });\n\n      // Remove any text selection from the document\n      this.removeTextSelection();\n\n      // Prevent further text selection while dragging\n      this.documentListeners.add(\n        EventName.SelectionChange,\n        this.removeTextSelection\n      );\n\n      onStart(initialCoordinates);\n    }\n  }\n\n  private handleMove(event: Event) {\n    const {activated, initialCoordinates, props} = this;\n    const {\n      onMove,\n      options: {activationConstraint},\n    } = props;\n\n    if (!initialCoordinates) {\n      return;\n    }\n\n    const coordinates = getEventCoordinates(event) ?? defaultCoordinates;\n    const delta = getCoordinatesDelta(initialCoordinates, coordinates);\n\n    // Constraint validation\n    if (!activated && activationConstraint) {\n      if (isDistanceConstraint(activationConstraint)) {\n        if (\n          activationConstraint.tolerance != null &&\n          hasExceededDistance(delta, activationConstraint.tolerance)\n        ) {\n          return this.handleCancel();\n        }\n\n        if (hasExceededDistance(delta, activationConstraint.distance)) {\n          return this.handleStart();\n        }\n      }\n\n      if (isDelayConstraint(activationConstraint)) {\n        if (hasExceededDistance(delta, activationConstraint.tolerance)) {\n          return this.handleCancel();\n        }\n      }\n\n      this.handlePending(activationConstraint, delta);\n      return;\n    }\n\n    if (event.cancelable) {\n      event.preventDefault();\n    }\n\n    onMove(coordinates);\n  }\n\n  private handleEnd() {\n    const {onAbort, onEnd} = this.props;\n\n    this.detach();\n    if (!this.activated) {\n      onAbort(this.props.active);\n    }\n    onEnd();\n  }\n\n  private handleCancel() {\n    const {onAbort, onCancel} = this.props;\n\n    this.detach();\n    if (!this.activated) {\n      onAbort(this.props.active);\n    }\n    onCancel();\n  }\n\n  private handleKeydown(event: KeyboardEvent) {\n    if (event.code === KeyboardCode.Esc) {\n      this.handleCancel();\n    }\n  }\n\n  private removeTextSelection() {\n    this.document.getSelection()?.removeAllRanges();\n  }\n}\n", "import type {PointerEvent} from 'react';\nimport {getOwnerDocument} from '@dnd-kit/utilities';\n\nimport type {SensorProps} from '../types';\nimport {\n  AbstractPointerSensor,\n  AbstractPointerSensorOptions,\n  PointerEventHandlers,\n} from './AbstractPointerSensor';\n\nconst events: PointerEventHandlers = {\n  cancel: {name: 'pointercancel'},\n  move: {name: 'pointermove'},\n  end: {name: 'pointerup'},\n};\n\nexport interface PointerSensorOptions extends AbstractPointerSensorOptions {}\n\nexport type PointerSensorProps = SensorProps<PointerSensorOptions>;\n\nexport class PointerSensor extends AbstractPointerSensor {\n  constructor(props: PointerSensorProps) {\n    const {event} = props;\n    // Pointer events stop firing if the target is unmounted while dragging\n    // Therefore we attach listeners to the owner document instead\n    const listenerTarget = getOwnerDocument(event.target);\n\n    super(props, events, listenerTarget);\n  }\n\n  static activators = [\n    {\n      eventName: 'onPointerDown' as const,\n      handler: (\n        {nativeEvent: event}: PointerEvent,\n        {onActivation}: PointerSensorOptions\n      ) => {\n        if (!event.isPrimary || event.button !== 0) {\n          return false;\n        }\n\n        onActivation?.({event});\n\n        return true;\n      },\n    },\n  ];\n}\n", "import type {MouseEvent} from 'react';\nimport {getOwnerDocument} from '@dnd-kit/utilities';\n\nimport type {SensorProps} from '../types';\nimport {\n  AbstractPointerSensor,\n  PointerEventHandlers,\n  AbstractPointerSensorOptions,\n} from '../pointer';\n\nconst events: PointerEventHandlers = {\n  move: {name: 'mousemove'},\n  end: {name: 'mouseup'},\n};\n\nenum MouseButton {\n  RightClick = 2,\n}\n\nexport interface MouseSensorOptions extends AbstractPointerSensorOptions {}\n\nexport type MouseSensorProps = SensorProps<MouseSensorOptions>;\n\nexport class MouseSensor extends AbstractPointerSensor {\n  constructor(props: MouseSensorProps) {\n    super(props, events, getOwnerDocument(props.event.target));\n  }\n\n  static activators = [\n    {\n      eventName: 'onMouseDown' as const,\n      handler: (\n        {nativeEvent: event}: MouseEvent,\n        {onActivation}: MouseSensorOptions\n      ) => {\n        if (event.button === MouseButton.RightClick) {\n          return false;\n        }\n\n        onActivation?.({event});\n\n        return true;\n      },\n    },\n  ];\n}\n", "import type {TouchEvent} from 'react';\n\nimport {\n  AbstractPointerSensor,\n  PointerSensorProps,\n  PointerEventHandlers,\n  PointerSensorOptions,\n} from '../pointer';\nimport type {SensorProps} from '../types';\n\nconst events: PointerEventHandlers = {\n  cancel: {name: 'touchcancel'},\n  move: {name: 'touchmove'},\n  end: {name: 'touchend'},\n};\n\nexport interface TouchSensorOptions extends PointerSensorOptions {}\n\nexport type TouchSensorProps = SensorProps<TouchSensorOptions>;\n\nexport class TouchSensor extends AbstractPointerSensor {\n  constructor(props: PointerSensorProps) {\n    super(props, events);\n  }\n\n  static activators = [\n    {\n      eventName: 'onTouchStart' as const,\n      handler: (\n        {nativeEvent: event}: TouchEvent,\n        {onActivation}: TouchSensorOptions\n      ) => {\n        const {touches} = event;\n\n        if (touches.length > 1) {\n          return false;\n        }\n\n        onActivation?.({event});\n\n        return true;\n      },\n    },\n  ];\n\n  static setup() {\n    // Adding a non-capture and non-passive `touchmove` listener in order\n    // to force `event.preventDefault()` calls to work in dynamically added\n    // touchmove event handlers. This is required for iOS Safari.\n    window.addEventListener(events.move.name, noop, {\n      capture: false,\n      passive: false,\n    });\n\n    return function teardown() {\n      window.removeEventListener(events.move.name, noop);\n    };\n\n    // We create a new handler because the teardown function of another sensor\n    // could remove our event listener if we use a referentially equal listener.\n    function noop() {}\n  }\n}\n", "import {useCallback, useEffect, useMemo, useRef} from 'react';\nimport {useInterval, useLazyMemo, usePrevious} from '@dnd-kit/utilities';\n\nimport {getScrollDirectionAndSpeed} from '../../utilities';\nimport {Direction} from '../../types';\nimport type {Coordinates, ClientRect} from '../../types';\n\nexport type ScrollAncestorSortingFn = (ancestors: Element[]) => Element[];\n\nexport enum AutoScrollActivator {\n  Pointer,\n  DraggableRect,\n}\n\nexport interface Options {\n  acceleration?: number;\n  activator?: AutoScrollActivator;\n  canScroll?: CanScroll;\n  enabled?: boolean;\n  interval?: number;\n  layoutShiftCompensation?:\n    | boolean\n    | {\n        x: boolean;\n        y: boolean;\n      };\n  order?: TraversalOrder;\n  threshold?: {\n    x: number;\n    y: number;\n  };\n}\n\ninterface Arguments extends Options {\n  draggingRect: ClientRect | null;\n  enabled: boolean;\n  pointerCoordinates: Coordinates | null;\n  scrollableAncestors: Element[];\n  scrollableAncestorRects: ClientRect[];\n  delta: Coordinates;\n}\n\nexport type CanScroll = (element: Element) => boolean;\n\nexport enum TraversalOrder {\n  TreeOrder,\n  ReversedTreeOrder,\n}\n\ninterface ScrollDirection {\n  x: 0 | Direction;\n  y: 0 | Direction;\n}\n\nexport function useAutoScroller({\n  acceleration,\n  activator = AutoScrollActivator.Pointer,\n  canScroll,\n  draggingRect,\n  enabled,\n  interval = 5,\n  order = TraversalOrder.TreeOrder,\n  pointerCoordinates,\n  scrollableAncestors,\n  scrollableAncestorRects,\n  delta,\n  threshold,\n}: Arguments) {\n  const scrollIntent = useScrollIntent({delta, disabled: !enabled});\n  const [setAutoScrollInterval, clearAutoScrollInterval] = useInterval();\n  const scrollSpeed = useRef<Coordinates>({x: 0, y: 0});\n  const scrollDirection = useRef<ScrollDirection>({x: 0, y: 0});\n  const rect = useMemo(() => {\n    switch (activator) {\n      case AutoScrollActivator.Pointer:\n        return pointerCoordinates\n          ? {\n              top: pointerCoordinates.y,\n              bottom: pointerCoordinates.y,\n              left: pointerCoordinates.x,\n              right: pointerCoordinates.x,\n            }\n          : null;\n      case AutoScrollActivator.DraggableRect:\n        return draggingRect;\n    }\n  }, [activator, draggingRect, pointerCoordinates]);\n  const scrollContainerRef = useRef<Element | null>(null);\n  const autoScroll = useCallback(() => {\n    const scrollContainer = scrollContainerRef.current;\n\n    if (!scrollContainer) {\n      return;\n    }\n\n    const scrollLeft = scrollSpeed.current.x * scrollDirection.current.x;\n    const scrollTop = scrollSpeed.current.y * scrollDirection.current.y;\n\n    scrollContainer.scrollBy(scrollLeft, scrollTop);\n  }, []);\n  const sortedScrollableAncestors = useMemo(\n    () =>\n      order === TraversalOrder.TreeOrder\n        ? [...scrollableAncestors].reverse()\n        : scrollableAncestors,\n    [order, scrollableAncestors]\n  );\n\n  useEffect(\n    () => {\n      if (!enabled || !scrollableAncestors.length || !rect) {\n        clearAutoScrollInterval();\n        return;\n      }\n\n      for (const scrollContainer of sortedScrollableAncestors) {\n        if (canScroll?.(scrollContainer) === false) {\n          continue;\n        }\n\n        const index = scrollableAncestors.indexOf(scrollContainer);\n        const scrollContainerRect = scrollableAncestorRects[index];\n\n        if (!scrollContainerRect) {\n          continue;\n        }\n\n        const {direction, speed} = getScrollDirectionAndSpeed(\n          scrollContainer,\n          scrollContainerRect,\n          rect,\n          acceleration,\n          threshold\n        );\n\n        for (const axis of ['x', 'y'] as const) {\n          if (!scrollIntent[axis][direction[axis] as Direction]) {\n            speed[axis] = 0;\n            direction[axis] = 0;\n          }\n        }\n\n        if (speed.x > 0 || speed.y > 0) {\n          clearAutoScrollInterval();\n\n          scrollContainerRef.current = scrollContainer;\n          setAutoScrollInterval(autoScroll, interval);\n\n          scrollSpeed.current = speed;\n          scrollDirection.current = direction;\n\n          return;\n        }\n      }\n\n      scrollSpeed.current = {x: 0, y: 0};\n      scrollDirection.current = {x: 0, y: 0};\n      clearAutoScrollInterval();\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [\n      acceleration,\n      autoScroll,\n      canScroll,\n      clearAutoScrollInterval,\n      enabled,\n      interval,\n      // eslint-disable-next-line react-hooks/exhaustive-deps\n      JSON.stringify(rect),\n      // eslint-disable-next-line react-hooks/exhaustive-deps\n      JSON.stringify(scrollIntent),\n      setAutoScrollInterval,\n      scrollableAncestors,\n      sortedScrollableAncestors,\n      scrollableAncestorRects,\n      // eslint-disable-next-line react-hooks/exhaustive-deps\n      JSON.stringify(threshold),\n    ]\n  );\n}\n\ninterface ScrollIntent {\n  x: Record<Direction, boolean>;\n  y: Record<Direction, boolean>;\n}\n\nconst defaultScrollIntent: ScrollIntent = {\n  x: {[Direction.Backward]: false, [Direction.Forward]: false},\n  y: {[Direction.Backward]: false, [Direction.Forward]: false},\n};\n\nfunction useScrollIntent({\n  delta,\n  disabled,\n}: {\n  delta: Coordinates;\n  disabled: boolean;\n}): ScrollIntent {\n  const previousDelta = usePrevious(delta);\n\n  return useLazyMemo<ScrollIntent>(\n    (previousIntent) => {\n      if (disabled || !previousDelta || !previousIntent) {\n        // Reset scroll intent tracking when auto-scrolling is disabled\n        return defaultScrollIntent;\n      }\n\n      const direction = {\n        x: Math.sign(delta.x - previousDelta.x),\n        y: Math.sign(delta.y - previousDelta.y),\n      };\n\n      // Keep track of the user intent to scroll in each direction for both axis\n      return {\n        x: {\n          [Direction.Backward]:\n            previousIntent.x[Direction.Backward] || direction.x === -1,\n          [Direction.Forward]:\n            previousIntent.x[Direction.Forward] || direction.x === 1,\n        },\n        y: {\n          [Direction.Backward]:\n            previousIntent.y[Direction.Backward] || direction.y === -1,\n          [Direction.Forward]:\n            previousIntent.y[Direction.Forward] || direction.y === 1,\n        },\n      };\n    },\n    [disabled, delta, previousDelta]\n  );\n}\n", "import {useLazyMemo} from '@dnd-kit/utilities';\n\nimport type {DraggableNode, DraggableNodes} from '../../store';\nimport type {UniqueIdentifier} from '../../types';\n\nexport function useCachedNode(\n  draggableNodes: DraggableNodes,\n  id: UniqueIdentifier | null\n): DraggableNode['node']['current'] {\n  const draggableNode = id != null ? draggableNodes.get(id) : undefined;\n  const node = draggableNode ? draggableNode.node.current : null;\n\n  return useLazyMemo(\n    (cachedNode) => {\n      if (id == null) {\n        return null;\n      }\n\n      // In some cases, the draggable node can unmount while dragging\n      // This is the case for virtualized lists. In those situations,\n      // we fall back to the last known value for that node.\n      return node ?? cachedNode ?? null;\n    },\n    [node, id]\n  );\n}\n", "import {useMemo} from 'react';\n\nimport type {SensorActivatorFunction, SensorDescriptor} from '../../sensors';\nimport type {\n  SyntheticListener,\n  SyntheticListeners,\n} from './useSyntheticListeners';\n\nexport function useCombineActivators(\n  sensors: SensorDescriptor<any>[],\n  getSyntheticHandler: (\n    handler: SensorActivatorFunction<any>,\n    sensor: SensorDescriptor<any>\n  ) => SyntheticListener['handler']\n): SyntheticListeners {\n  return useMemo(\n    () =>\n      sensors.reduce<SyntheticListeners>((accumulator, sensor) => {\n        const {sensor: Sensor} = sensor;\n\n        const sensorActivators = Sensor.activators.map((activator) => ({\n          eventName: activator.eventName,\n          handler: getSyntheticHandler(activator.handler, sensor),\n        }));\n\n        return [...accumulator, ...sensorActivators];\n      }, []),\n    [sensors, getSyntheticHandler]\n  );\n}\n", "import {useCallback, useEffect, useRef, useState} from 'react';\nimport {useLatestValue, useLazyMemo} from '@dnd-kit/utilities';\n\nimport {Rect} from '../../utilities/rect';\nimport type {DroppableContainer, RectMap} from '../../store/types';\nimport type {ClientRect, UniqueIdentifier} from '../../types';\n\ninterface Arguments {\n  dragging: boolean;\n  dependencies: any[];\n  config: DroppableMeasuring;\n}\n\nexport enum MeasuringStrategy {\n  Always,\n  BeforeDragging,\n  WhileDragging,\n}\n\nexport enum MeasuringFrequency {\n  Optimized = 'optimized',\n}\n\ntype MeasuringFunction = (element: HTMLElement) => ClientRect;\n\nexport interface DroppableMeasuring {\n  measure: MeasuringFunction;\n  strategy: MeasuringStrategy;\n  frequency: MeasuringFrequency | number;\n}\n\nconst defaultValue: RectMap = new Map();\n\nexport function useDroppableMeasuring(\n  containers: DroppableContainer[],\n  {dragging, dependencies, config}: Arguments\n) {\n  const [queue, setQueue] = useState<UniqueIdentifier[] | null>(null);\n  const {frequency, measure, strategy} = config;\n  const containersRef = useRef(containers);\n  const disabled = isDisabled();\n  const disabledRef = useLatestValue(disabled);\n  const measureDroppableContainers = useCallback(\n    (ids: UniqueIdentifier[] = []) => {\n      if (disabledRef.current) {\n        return;\n      }\n\n      setQueue((value) => {\n        if (value === null) {\n          return ids;\n        }\n\n        return value.concat(ids.filter((id) => !value.includes(id)));\n      });\n    },\n    [disabledRef]\n  );\n  const timeoutId = useRef<NodeJS.Timeout | null>(null);\n  const droppableRects = useLazyMemo<RectMap>(\n    (previousValue) => {\n      if (disabled && !dragging) {\n        return defaultValue;\n      }\n\n      if (\n        !previousValue ||\n        previousValue === defaultValue ||\n        containersRef.current !== containers ||\n        queue != null\n      ) {\n        const map: RectMap = new Map();\n\n        for (let container of containers) {\n          if (!container) {\n            continue;\n          }\n\n          if (\n            queue &&\n            queue.length > 0 &&\n            !queue.includes(container.id) &&\n            container.rect.current\n          ) {\n            // This container does not need to be re-measured\n            map.set(container.id, container.rect.current);\n            continue;\n          }\n\n          const node = container.node.current;\n          const rect = node ? new Rect(measure(node), node) : null;\n\n          container.rect.current = rect;\n\n          if (rect) {\n            map.set(container.id, rect);\n          }\n        }\n\n        return map;\n      }\n\n      return previousValue;\n    },\n    [containers, queue, dragging, disabled, measure]\n  );\n\n  useEffect(() => {\n    containersRef.current = containers;\n  }, [containers]);\n\n  useEffect(\n    () => {\n      if (disabled) {\n        return;\n      }\n\n      measureDroppableContainers();\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [dragging, disabled]\n  );\n\n  useEffect(\n    () => {\n      if (queue && queue.length > 0) {\n        setQueue(null);\n      }\n    },\n    //eslint-disable-next-line react-hooks/exhaustive-deps\n    [JSON.stringify(queue)]\n  );\n\n  useEffect(\n    () => {\n      if (\n        disabled ||\n        typeof frequency !== 'number' ||\n        timeoutId.current !== null\n      ) {\n        return;\n      }\n\n      timeoutId.current = setTimeout(() => {\n        measureDroppableContainers();\n        timeoutId.current = null;\n      }, frequency);\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [frequency, disabled, measureDroppableContainers, ...dependencies]\n  );\n\n  return {\n    droppableRects,\n    measureDroppableContainers,\n    measuringScheduled: queue != null,\n  };\n\n  function isDisabled() {\n    switch (strategy) {\n      case MeasuringStrategy.Always:\n        return false;\n      case MeasuringStrategy.BeforeDragging:\n        return dragging;\n      default:\n        return !dragging;\n    }\n  }\n}\n", "import {useLazyMemo} from '@dnd-kit/utilities';\n\ntype AnyFunction = (...args: any) => any;\n\nexport function useInitialValue<\n  T,\n  U extends AnyFunction | undefined = undefined\n>(\n  value: T | null,\n  computeFn?: U\n): U extends AnyFunction ? ReturnType<U> | null : T | null {\n  return useLazyMemo(\n    (previousValue) => {\n      if (!value) {\n        return null;\n      }\n\n      if (previousValue) {\n        return previousValue;\n      }\n\n      return typeof computeFn === 'function' ? computeFn(value) : value;\n    },\n    [computeFn, value]\n  );\n}\n", "import type {ClientRect} from '../../types';\nimport {useInitialValue} from './useInitialValue';\n\nexport function useInitialRect(\n  node: HTMLElement | null,\n  measure: (node: HTMLElement) => ClientRect\n) {\n  return useInitialValue(node, measure);\n}\n", "import {useEffect, useMemo} from 'react';\nimport {useEvent} from '@dnd-kit/utilities';\n\ninterface Arguments {\n  callback: MutationCallback;\n  disabled?: boolean;\n}\n\n/**\n * Returns a new MutationObserver instance.\n * If `MutationObserver` is undefined in the execution environment, returns `undefined`.\n */\nexport function useMutationObserver({callback, disabled}: Arguments) {\n  const handleMutations = useEvent(callback);\n  const mutationObserver = useMemo(() => {\n    if (\n      disabled ||\n      typeof window === 'undefined' ||\n      typeof window.MutationObserver === 'undefined'\n    ) {\n      return undefined;\n    }\n\n    const {MutationObserver} = window;\n\n    return new MutationObserver(handleMutations);\n  }, [handleMutations, disabled]);\n\n  useEffect(() => {\n    return () => mutationObserver?.disconnect();\n  }, [mutationObserver]);\n\n  return mutationObserver;\n}\n", "import {useEffect, useMemo} from 'react';\nimport {useEvent} from '@dnd-kit/utilities';\n\ninterface Arguments {\n  callback: ResizeObserverCallback;\n  disabled?: boolean;\n}\n\n/**\n * Returns a new ResizeObserver instance bound to the `onResize` callback.\n * If `ResizeObserver` is undefined in the execution environment, returns `undefined`.\n */\nexport function useResizeObserver({callback, disabled}: Arguments) {\n  const handleResize = useEvent(callback);\n  const resizeObserver = useMemo(\n    () => {\n      if (\n        disabled ||\n        typeof window === 'undefined' ||\n        typeof window.ResizeObserver === 'undefined'\n      ) {\n        return undefined;\n      }\n\n      const {ResizeObserver} = window;\n\n      return new ResizeObserver(handleResize);\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [disabled]\n  );\n\n  useEffect(() => {\n    return () => resizeObserver?.disconnect();\n  }, [resizeObserver]);\n\n  return resizeObserver;\n}\n", "import {useState} from 'react';\nimport {useIsomorphicLayoutEffect} from '@dnd-kit/utilities';\n\nimport type {ClientRect} from '../../types';\nimport {getClientRect, Rect} from '../../utilities';\n\nimport {useMutationObserver} from './useMutationObserver';\nimport {useResizeObserver} from './useResizeObserver';\n\nfunction defaultMeasure(element: HTMLElement) {\n  return new Rect(getClientRect(element), element);\n}\n\nexport function useRect(\n  element: HTMLElement | null,\n  measure: (element: HTMLElement) => ClientRect = defaultMeasure,\n  fallbackRect?: ClientRect | null\n) {\n  const [rect, setRect] = useState<ClientRect | null>(null);\n\n  function measureRect() {\n    setRect((currentRect): ClientRect | null => {\n      if (!element) {\n        return null;\n      }\n  \n      if (element.isConnected === false) {\n        // Fall back to last rect we measured if the element is\n        // no longer connected to the DOM.\n        return currentRect ?? fallbackRect ?? null;\n      }\n  \n      const newRect = measure(element);\n  \n      if (JSON.stringify(currentRect) === JSON.stringify(newRect)) {\n        return currentRect;\n      }\n  \n      return newRect;\n    });\n  }\n  \n  const mutationObserver = useMutationObserver({\n    callback(records) {\n      if (!element) {\n        return;\n      }\n\n      for (const record of records) {\n        const {type, target} = record;\n\n        if (\n          type === 'childList' &&\n          target instanceof HTMLElement &&\n          target.contains(element)\n        ) {\n          measureRect();\n          break;\n        }\n      }\n    },\n  });\n  const resizeObserver = useResizeObserver({callback: measureRect});\n\n  useIsomorphicLayoutEffect(() => {\n    measureRect();\n\n    if (element) {\n      resizeObserver?.observe(element);\n      mutationObserver?.observe(document.body, {\n        childList: true,\n        subtree: true,\n      });\n    } else {\n      resizeObserver?.disconnect();\n      mutationObserver?.disconnect();\n    }\n  }, [element]);\n\n  return rect;\n}\n", "import type {ClientRect} from '../../types';\nimport {getRectDelta} from '../../utilities';\n\nimport {useInitialValue} from './useInitialValue';\n\nexport function useRectDelta(rect: ClientRect | null) {\n  const initialRect = useInitialValue(rect);\n\n  return getRectDelta(rect, initialRect);\n}\n", "import {useEffect, useRef} from 'react';\nimport {useLazyMemo} from '@dnd-kit/utilities';\n\nimport {getScrollableAncestors} from '../../utilities';\n\nconst defaultValue: Element[] = [];\n\nexport function useScrollableAncestors(node: HTMLElement | null) {\n  const previousNode = useRef(node);\n\n  const ancestors = useLazyMemo<Element[]>(\n    (previousValue) => {\n      if (!node) {\n        return defaultValue;\n      }\n\n      if (\n        previousValue &&\n        previousValue !== defaultValue &&\n        node &&\n        previousNode.current &&\n        node.parentNode === previousNode.current.parentNode\n      ) {\n        return previousValue;\n      }\n\n      return getScrollableAncestors(node);\n    },\n    [node]\n  );\n\n  useEffect(() => {\n    previousNode.current = node;\n  }, [node]);\n\n  return ancestors;\n}\n", "import {useState, useCallback, useMemo, useRef, useEffect} from 'react';\nimport {add} from '@dnd-kit/utilities';\n\nimport {\n  defaultCoordinates,\n  getScrollableElement,\n  getScrollCoordinates,\n  getScrollOffsets,\n} from '../../utilities';\nimport type {Coordinates} from '../../types';\n\ntype ScrollCoordinates = Map<HTMLElement | Window, Coordinates>;\n\nexport function useScrollOffsets(elements: Element[]): Coordinates {\n  const [\n    scrollCoordinates,\n    setScrollCoordinates,\n  ] = useState<ScrollCoordinates | null>(null);\n  const prevElements = useRef(elements);\n\n  // To-do: Throttle the handleScroll callback\n  const handleScroll = useCallback((event: Event) => {\n    const scrollingElement = getScrollableElement(event.target);\n\n    if (!scrollingElement) {\n      return;\n    }\n\n    setScrollCoordinates((scrollCoordinates) => {\n      if (!scrollCoordinates) {\n        return null;\n      }\n\n      scrollCoordinates.set(\n        scrollingElement,\n        getScrollCoordinates(scrollingElement)\n      );\n\n      return new Map(scrollCoordinates);\n    });\n  }, []);\n\n  useEffect(() => {\n    const previousElements = prevElements.current;\n\n    if (elements !== previousElements) {\n      cleanup(previousElements);\n\n      const entries = elements\n        .map((element) => {\n          const scrollableElement = getScrollableElement(element);\n\n          if (scrollableElement) {\n            scrollableElement.addEventListener('scroll', handleScroll, {\n              passive: true,\n            });\n\n            return [\n              scrollableElement,\n              getScrollCoordinates(scrollableElement),\n            ] as const;\n          }\n\n          return null;\n        })\n        .filter(\n          (\n            entry\n          ): entry is [\n            HTMLElement | (Window & typeof globalThis),\n            Coordinates\n          ] => entry != null\n        );\n\n      setScrollCoordinates(entries.length ? new Map(entries) : null);\n\n      prevElements.current = elements;\n    }\n\n    return () => {\n      cleanup(elements);\n      cleanup(previousElements);\n    };\n\n    function cleanup(elements: Element[]) {\n      elements.forEach((element) => {\n        const scrollableElement = getScrollableElement(element);\n\n        scrollableElement?.removeEventListener('scroll', handleScroll);\n      });\n    }\n  }, [handleScroll, elements]);\n\n  return useMemo(() => {\n    if (elements.length) {\n      return scrollCoordinates\n        ? Array.from(scrollCoordinates.values()).reduce(\n            (acc, coordinates) => add(acc, coordinates),\n            defaultCoordinates\n          )\n        : getScrollOffsets(elements);\n    }\n\n    return defaultCoordinates;\n  }, [elements, scrollCoordinates]);\n}\n", "import {useEffect, useRef} from 'react';\nimport {Coordinates, subtract} from '@dnd-kit/utilities';\n\nimport {defaultCoordinates} from '../../utilities';\n\nexport function useScrollOffsetsDelta(\n  scrollOffsets: Coordinates,\n  dependencies: any[] = []\n) {\n  const initialScrollOffsets = useRef<Coordinates | null>(null);\n\n  useEffect(\n    () => {\n      initialScrollOffsets.current = null;\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    dependencies\n  );\n\n  useEffect(() => {\n    const hasScrollOffsets = scrollOffsets !== defaultCoordinates;\n\n    if (hasScrollOffsets && !initialScrollOffsets.current) {\n      initialScrollOffsets.current = scrollOffsets;\n    }\n\n    if (!hasScrollOffsets && initialScrollOffsets.current) {\n      initialScrollOffsets.current = null;\n    }\n  }, [scrollOffsets]);\n\n  return initialScrollOffsets.current\n    ? subtract(scrollOffsets, initialScrollOffsets.current)\n    : defaultCoordinates;\n}\n", "import {useEffect} from 'react';\nimport {canUseDOM} from '@dnd-kit/utilities';\n\nimport type {SensorDescriptor} from '../../sensors';\n\nexport function useSensorSetup(sensors: SensorDescriptor<any>[]) {\n  useEffect(\n    () => {\n      if (!canUseDOM) {\n        return;\n      }\n\n      const teardownFns = sensors.map(({sensor}) => sensor.setup?.());\n\n      return () => {\n        for (const teardown of teardownFns) {\n          teardown?.();\n        }\n      };\n    },\n    // TO-DO: Sensors length could theoretically change which would not be a valid dependency\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    sensors.map(({sensor}) => sensor)\n  );\n}\n", "import {useMemo} from 'react';\n\nimport type {SyntheticEventName, UniqueIdentifier} from '../../types';\n\nexport type SyntheticListener = {\n  eventName: SyntheticEventName;\n  handler: (event: React.SyntheticEvent, id: UniqueIdentifier) => void;\n};\n\nexport type SyntheticListeners = SyntheticListener[];\n\nexport type SyntheticListenerMap = Record<string, Function>;\n\nexport function useSyntheticListeners(\n  listeners: SyntheticListeners,\n  id: UniqueIdentifier\n): SyntheticListenerMap {\n  return useMemo(() => {\n    return listeners.reduce<SyntheticListenerMap>(\n      (acc, {eventName, handler}) => {\n        acc[eventName] = (event: React.SyntheticEvent) => {\n          handler(event, id);\n        };\n\n        return acc;\n      },\n      {} as SyntheticListenerMap\n    );\n  }, [listeners, id]);\n}\n", "import {useMemo} from 'react';\n\nimport {getWindowClientRect} from '../../utilities/rect';\n\nexport function useWindowRect(element: typeof window | null) {\n  return useMemo(() => (element ? getWindowClientRect(element) : null), [\n    element,\n  ]);\n}\n", "import {useState} from 'react';\nimport {getWindow, useIsomorphicLayoutEffect} from '@dnd-kit/utilities';\n\nimport type {ClientRect} from '../../types';\nimport {Rect, getClientRect} from '../../utilities/rect';\nimport {isDocumentScrollingElement} from '../../utilities';\n\nimport {useResizeObserver} from './useResizeObserver';\nimport {useWindowRect} from './useWindowRect';\n\nconst defaultValue: Rect[] = [];\n\nexport function useRects(\n  elements: Element[],\n  measure: (element: Element) => ClientRect = getClientRect\n): ClientRect[] {\n  const [firstElement] = elements;\n  const windowRect = useWindowRect(\n    firstElement ? getWindow(firstElement) : null\n  );\n  const [rects, setRects] = useState<ClientRect[]>(defaultValue);\n\n  function measureRects() {\n    setRects(() => {\n      if (!elements.length) {\n        return defaultValue;\n      }\n\n      return elements.map((element) =>\n        isDocumentScrollingElement(element)\n          ? (windowRect as ClientRect)\n          : new Rect(measure(element), element)\n      );\n    });\n  }\n\n  const resizeObserver = useResizeObserver({callback: measureRects});\n\n  useIsomorphicLayoutEffect(() => {\n    resizeObserver?.disconnect();\n    measureRects();\n    elements.forEach((element) => resizeObserver?.observe(element));\n  }, [elements]);\n\n  return rects;\n}\n", "import {isHTMLElement} from '@dnd-kit/utilities';\n\nexport function getMeasurableNode(\n  node: HTMLElement | undefined | null\n): HTMLElement | null {\n  if (!node) {\n    return null;\n  }\n\n  if (node.children.length > 1) {\n    return node;\n  }\n  const firstChild = node.children[0];\n\n  return isHTMLElement(firstChild) ? firstChild : node;\n}\n", "import {useMemo, useCallback, useState} from 'react';\nimport {isHTMLElement, useNodeRef} from '@dnd-kit/utilities';\n\nimport {useResizeObserver} from './useResizeObserver';\nimport {getMeasurableNode} from '../../utilities/nodes';\nimport type {PublicContextDescriptor} from '../../store';\nimport type {ClientRect} from '../../types';\n\ninterface Arguments {\n  measure(element: HTMLElement): ClientRect;\n}\n\nexport function useDragOverlayMeasuring({\n  measure,\n}: Arguments): PublicContextDescriptor['dragOverlay'] {\n  const [rect, setRect] = useState<ClientRect | null>(null);\n  const handleResize = useCallback(\n    (entries: ResizeObserverEntry[]) => {\n      for (const {target} of entries) {\n        if (isHTMLElement(target)) {\n          setRect((rect) => {\n            const newRect = measure(target);\n\n            return rect\n              ? {...rect, width: newRect.width, height: newRect.height}\n              : newRect;\n          });\n          break;\n        }\n      }\n    },\n    [measure]\n  );\n  const resizeObserver = useResizeObserver({callback: handleResize});\n  const handleNodeChange = useCallback(\n    (element) => {\n      const node = getMeasurableNode(element);\n\n      resizeObserver?.disconnect();\n\n      if (node) {\n        resizeObserver?.observe(node);\n      }\n\n      setRect(node ? measure(node) : null);\n    },\n    [measure, resizeObserver]\n  );\n  const [nodeRef, setRef] = useNodeRef(handleNodeChange);\n\n  return useMemo(\n    () => ({\n      nodeRef,\n      rect,\n      setRef,\n    }),\n    [rect, nodeRef, setRef]\n  );\n}\n", "import type {DeepRequired} from '@dnd-kit/utilities';\n\nimport type {DataRef} from '../../store/types';\nimport {KeyboardSensor, PointerSensor} from '../../sensors';\nimport {MeasuringStrategy, MeasuringFrequency} from '../../hooks/utilities';\nimport {\n  getClientRect,\n  getTransformAgnosticClientRect,\n} from '../../utilities/rect';\n\nimport type {MeasuringConfiguration} from './types';\n\nexport const defaultSensors = [\n  {sensor: PointerSensor, options: {}},\n  {sensor: KeyboardSensor, options: {}},\n];\n\nexport const defaultData: DataRef = {current: {}};\n\nexport const defaultMeasuringConfiguration: DeepRequired<MeasuringConfiguration> = {\n  draggable: {\n    measure: getTransformAgnosticClientRect,\n  },\n  droppable: {\n    measure: getTransformAgnosticClientRect,\n    strategy: MeasuringStrategy.WhileDragging,\n    frequency: MeasuringFrequency.Optimized,\n  },\n  dragOverlay: {\n    measure: getClientRect,\n  },\n};\n", "import type {UniqueIdentifier} from '../types';\nimport type {DroppableContainer} from './types';\n\ntype Identifier = UniqueIdentifier | null | undefined;\n\nexport class DroppableContainersMap extends Map<\n  UniqueIdentifier,\n  DroppableContainer\n> {\n  get(id: Identifier) {\n    return id != null ? super.get(id) ?? undefined : undefined;\n  }\n\n  toArray(): DroppableContainer[] {\n    return Array.from(this.values());\n  }\n\n  getEnabled(): DroppableContainer[] {\n    return this.toArray().filter(({disabled}) => !disabled);\n  }\n\n  getNodeFor(id: Identifier) {\n    return this.get(id)?.node.current ?? undefined;\n  }\n}\n", "import {createContext} from 'react';\n\nimport {noop} from '../utilities/other';\nimport {defaultMeasuringConfiguration} from '../components/DndContext/defaults';\nimport {DroppableContainersMap} from './constructors';\nimport type {InternalContextDescriptor, PublicContextDescriptor} from './types';\n\nexport const defaultPublicContext: PublicContextDescriptor = {\n  activatorEvent: null,\n  active: null,\n  activeNode: null,\n  activeNodeRect: null,\n  collisions: null,\n  containerNodeRect: null,\n  draggableNodes: new Map(),\n  droppableRects: new Map(),\n  droppableContainers: new DroppableContainersMap(),\n  over: null,\n  dragOverlay: {\n    nodeRef: {\n      current: null,\n    },\n    rect: null,\n    setRef: noop,\n  },\n  scrollableAncestors: [],\n  scrollableAncestorRects: [],\n  measuringConfiguration: defaultMeasuringConfiguration,\n  measureDroppableContainers: noop,\n  windowRect: null,\n  measuringScheduled: false,\n};\n\nexport const defaultInternalContext: InternalContextDescriptor = {\n  activatorEvent: null,\n  activators: [],\n  active: null,\n  activeNodeRect: null,\n  ariaDescribedById: {\n    draggable: '',\n  },\n  dispatch: noop,\n  draggableNodes: new Map(),\n  over: null,\n  measureDroppableContainers: noop,\n};\n\nexport const InternalContext = createContext<InternalContextDescriptor>(\n  defaultInternalContext\n);\n\nexport const PublicContext = createContext<PublicContextDescriptor>(\n  defaultPublicContext\n);\n", "import {Action, Actions} from './actions';\nimport {DroppableContainersMap} from './constructors';\nimport type {State} from './types';\n\nexport function getInitialState(): State {\n  return {\n    draggable: {\n      active: null,\n      initialCoordinates: {x: 0, y: 0},\n      nodes: new Map(),\n      translate: {x: 0, y: 0},\n    },\n    droppable: {\n      containers: new DroppableContainersMap(),\n    },\n  };\n}\n\nexport function reducer(state: State, action: Actions): State {\n  switch (action.type) {\n    case Action.DragStart:\n      return {\n        ...state,\n        draggable: {\n          ...state.draggable,\n          initialCoordinates: action.initialCoordinates,\n          active: action.active,\n        },\n      };\n    case Action.DragMove:\n      if (state.draggable.active == null) {\n        return state;\n      }\n\n      return {\n        ...state,\n        draggable: {\n          ...state.draggable,\n          translate: {\n            x: action.coordinates.x - state.draggable.initialCoordinates.x,\n            y: action.coordinates.y - state.draggable.initialCoordinates.y,\n          },\n        },\n      };\n    case Action.DragEnd:\n    case Action.DragCancel:\n      return {\n        ...state,\n        draggable: {\n          ...state.draggable,\n          active: null,\n          initialCoordinates: {x: 0, y: 0},\n          translate: {x: 0, y: 0},\n        },\n      };\n\n    case Action.RegisterDroppable: {\n      const {element} = action;\n      const {id} = element;\n      const containers = new DroppableContainersMap(state.droppable.containers);\n      containers.set(id, element);\n\n      return {\n        ...state,\n        droppable: {\n          ...state.droppable,\n          containers,\n        },\n      };\n    }\n\n    case Action.SetDroppableDisabled: {\n      const {id, key, disabled} = action;\n      const element = state.droppable.containers.get(id);\n\n      if (!element || key !== element.key) {\n        return state;\n      }\n\n      const containers = new DroppableContainersMap(state.droppable.containers);\n      containers.set(id, {\n        ...element,\n        disabled,\n      });\n\n      return {\n        ...state,\n        droppable: {\n          ...state.droppable,\n          containers,\n        },\n      };\n    }\n\n    case Action.UnregisterDroppable: {\n      const {id, key} = action;\n      const element = state.droppable.containers.get(id);\n\n      if (!element || key !== element.key) {\n        return state;\n      }\n\n      const containers = new DroppableContainersMap(state.droppable.containers);\n      containers.delete(id);\n\n      return {\n        ...state,\n        droppable: {\n          ...state.droppable,\n          containers,\n        },\n      };\n    }\n\n    default: {\n      return state;\n    }\n  }\n}\n", "import {useContext, useEffect} from 'react';\nimport {\n  findFirstFocusableNode,\n  isKeyboardEvent,\n  usePrevious,\n} from '@dnd-kit/utilities';\n\nimport {InternalContext} from '../../../store';\n\ninterface Props {\n  disabled: boolean;\n}\n\nexport function RestoreFocus({disabled}: Props) {\n  const {active, activatorEvent, draggableNodes} = useContext(InternalContext);\n  const previousActivatorEvent = usePrevious(activatorEvent);\n  const previousActiveId = usePrevious(active?.id);\n\n  // Restore keyboard focus on the activator node\n  useEffect(() => {\n    if (disabled) {\n      return;\n    }\n\n    if (!activatorEvent && previousActivatorEvent && previousActiveId != null) {\n      if (!isKeyboardEvent(previousActivatorEvent)) {\n        return;\n      }\n\n      if (document.activeElement === previousActivatorEvent.target) {\n        // No need to restore focus\n        return;\n      }\n\n      const draggableNode = draggableNodes.get(previousActiveId);\n\n      if (!draggableNode) {\n        return;\n      }\n\n      const {activatorNode, node} = draggableNode;\n\n      if (!activatorNode.current && !node.current) {\n        return;\n      }\n\n      requestAnimationFrame(() => {\n        for (const element of [activatorNode.current, node.current]) {\n          if (!element) {\n            continue;\n          }\n\n          const focusableNode = findFirstFocusableNode(element);\n\n          if (focusableNode) {\n            focusableNode.focus();\n            break;\n          }\n        }\n      });\n    }\n  }, [\n    activatorEvent,\n    disabled,\n    draggableNodes,\n    previousActiveId,\n    previousActivatorEvent,\n  ]);\n\n  return null;\n}\n", "import type {FirstArgument, Transform} from '@dnd-kit/utilities';\n\nimport type {Modifiers, Modifier} from './types';\n\nexport function applyModifiers(\n  modifiers: Modifiers | undefined,\n  {transform, ...args}: FirstArgument<Modifier>\n): Transform {\n  return modifiers?.length\n    ? modifiers.reduce<Transform>((accumulator, modifier) => {\n        return modifier({\n          transform: accumulator,\n          ...args,\n        });\n      }, transform)\n    : transform;\n}\n", "import {useMemo} from 'react';\nimport type {DeepRequired} from '@dnd-kit/utilities';\n\nimport {defaultMeasuringConfiguration} from '../defaults';\nimport type {MeasuringConfiguration} from '../types';\n\nexport function useMeasuringConfiguration(\n  config: MeasuringConfiguration | undefined\n): DeepRequired<MeasuringConfiguration> {\n  return useMemo(\n    () => ({\n      draggable: {\n        ...defaultMeasuringConfiguration.draggable,\n        ...config?.draggable,\n      },\n      droppable: {\n        ...defaultMeasuringConfiguration.droppable,\n        ...config?.droppable,\n      },\n      dragOverlay: {\n        ...defaultMeasuringConfiguration.dragOverlay,\n        ...config?.dragOverlay,\n      },\n    }),\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [config?.draggable, config?.droppable, config?.dragOverlay]\n  );\n}\n", "import {useRef} from 'react';\nimport {useIsomorphicLayoutEffect} from '@dnd-kit/utilities';\n\nimport {getRectDelta} from '../../../utilities/rect';\nimport {getFirstScrollableAncestor} from '../../../utilities/scroll';\nimport type {ClientRect} from '../../../types';\nimport type {DraggableNode} from '../../../store';\nimport type {MeasuringFunction} from '../types';\n\ninterface Options {\n  activeNode: DraggableNode | null | undefined;\n  config: boolean | {x: boolean; y: boolean} | undefined;\n  initialRect: ClientRect | null;\n  measure: MeasuringFunction;\n}\n\nexport function useLayoutShiftScrollCompensation({\n  activeNode,\n  measure,\n  initialRect,\n  config = true,\n}: Options) {\n  const initialized = useRef(false);\n  const {x, y} = typeof config === 'boolean' ? {x: config, y: config} : config;\n\n  useIsomorphicLayoutEffect(() => {\n    const disabled = !x && !y;\n\n    if (disabled || !activeNode) {\n      initialized.current = false;\n      return;\n    }\n\n    if (initialized.current || !initialRect) {\n      // Return early if layout shift scroll compensation was already attempted\n      // or if there is no initialRect to compare to.\n      return;\n    }\n\n    // Get the most up to date node ref for the active draggable\n    const node = activeNode?.node.current;\n\n    if (!node || node.isConnected === false) {\n      // Return early if there is no attached node ref or if the node is\n      // disconnected from the document.\n      return;\n    }\n\n    const rect = measure(node);\n    const rectDelta = getRectDelta(rect, initialRect);\n\n    if (!x) {\n      rectDelta.x = 0;\n    }\n\n    if (!y) {\n      rectDelta.y = 0;\n    }\n\n    // Only perform layout shift scroll compensation once\n    initialized.current = true;\n\n    if (Math.abs(rectDelta.x) > 0 || Math.abs(rectDelta.y) > 0) {\n      const firstScrollableAncestor = getFirstScrollableAncestor(node);\n\n      if (firstScrollableAncestor) {\n        firstScrollableAncestor.scrollBy({\n          top: rectDelta.y,\n          left: rectDelta.x,\n        });\n      }\n    }\n  }, [activeNode, x, y, initialRect, measure]);\n}\n", "import React, {\n  memo,\n  createContext,\n  useCallback,\n  useEffect,\n  useMemo,\n  useReducer,\n  useRef,\n  useState,\n} from 'react';\nimport {unstable_batchedUpdates} from 'react-dom';\nimport {\n  add,\n  getEventCoordinates,\n  getWindow,\n  useLatestValue,\n  useIsomorphicLayoutEffect,\n  useUniqueId,\n} from '@dnd-kit/utilities';\nimport type {Transform} from '@dnd-kit/utilities';\n\nimport {\n  Action,\n  PublicContext,\n  InternalContext,\n  PublicContextDescriptor,\n  InternalContextDescriptor,\n  getInitialState,\n  reducer,\n} from '../../store';\nimport {DndMonitorContext, useDndMonitorProvider} from '../DndMonitor';\nimport {\n  useAutoScroller,\n  useCachedNode,\n  useCombineActivators,\n  useDragOverlayMeasuring,\n  useDroppableMeasuring,\n  useInitialRect,\n  useRect,\n  useRectDelta,\n  useRects,\n  useScrollableAncestors,\n  useScrollOffsets,\n  useScrollOffsetsDelta,\n  useSensorSetup,\n  useWindowRect,\n} from '../../hooks/utilities';\nimport type {AutoScrollOptions, SyntheticListener} from '../../hooks/utilities';\nimport type {\n  Sensor,\n  SensorContext,\n  SensorDescriptor,\n  SensorActivatorFunction,\n  SensorInstance,\n} from '../../sensors';\nimport {\n  adjustScale,\n  CollisionDetection,\n  defaultCoordinates,\n  getAdjustedRect,\n  getFirstCollision,\n  rectIntersection,\n} from '../../utilities';\nimport {applyModifiers, Modifiers} from '../../modifiers';\nimport type {Active, Over} from '../../store/types';\nimport type {\n  DragStartEvent,\n  DragCancelEvent,\n  DragEndEvent,\n  DragMoveEvent,\n  DragOverEvent,\n  UniqueIdentifier,\n  DragPendingEvent,\n  DragAbortEvent,\n} from '../../types';\nimport {\n  Accessibility,\n  Announcements,\n  RestoreFocus,\n  ScreenReaderInstructions,\n} from '../Accessibility';\n\nimport {defaultData, defaultSensors} from './defaults';\nimport {\n  useLayoutShiftScrollCompensation,\n  useMeasuringConfiguration,\n} from './hooks';\nimport type {MeasuringConfiguration} from './types';\n\nexport interface Props {\n  id?: string;\n  accessibility?: {\n    announcements?: Announcements;\n    container?: Element;\n    restoreFocus?: boolean;\n    screenReaderInstructions?: ScreenReaderInstructions;\n  };\n  autoScroll?: boolean | AutoScrollOptions;\n  cancelDrop?: CancelDrop;\n  children?: React.ReactNode;\n  collisionDetection?: CollisionDetection;\n  measuring?: MeasuringConfiguration;\n  modifiers?: Modifiers;\n  sensors?: SensorDescriptor<any>[];\n  onDragAbort?(event: DragAbortEvent): void;\n  onDragPending?(event: DragPendingEvent): void;\n  onDragStart?(event: DragStartEvent): void;\n  onDragMove?(event: DragMoveEvent): void;\n  onDragOver?(event: DragOverEvent): void;\n  onDragEnd?(event: DragEndEvent): void;\n  onDragCancel?(event: DragCancelEvent): void;\n}\n\nexport interface CancelDropArguments extends DragEndEvent {}\n\nexport type CancelDrop = (\n  args: CancelDropArguments\n) => boolean | Promise<boolean>;\n\ninterface DndEvent extends Event {\n  dndKit?: {\n    capturedBy: Sensor<any>;\n  };\n}\n\nexport const ActiveDraggableContext = createContext<Transform>({\n  ...defaultCoordinates,\n  scaleX: 1,\n  scaleY: 1,\n});\n\nenum Status {\n  Uninitialized,\n  Initializing,\n  Initialized,\n}\n\nexport const DndContext = memo(function DndContext({\n  id,\n  accessibility,\n  autoScroll = true,\n  children,\n  sensors = defaultSensors,\n  collisionDetection = rectIntersection,\n  measuring,\n  modifiers,\n  ...props\n}: Props) {\n  const store = useReducer(reducer, undefined, getInitialState);\n  const [state, dispatch] = store;\n  const [dispatchMonitorEvent, registerMonitorListener] =\n    useDndMonitorProvider();\n  const [status, setStatus] = useState<Status>(Status.Uninitialized);\n  const isInitialized = status === Status.Initialized;\n  const {\n    draggable: {active: activeId, nodes: draggableNodes, translate},\n    droppable: {containers: droppableContainers},\n  } = state;\n  const node = activeId != null ? draggableNodes.get(activeId) : null;\n  const activeRects = useRef<Active['rect']['current']>({\n    initial: null,\n    translated: null,\n  });\n  const active = useMemo<Active | null>(\n    () =>\n      activeId != null\n        ? {\n            id: activeId,\n            // It's possible for the active node to unmount while dragging\n            data: node?.data ?? defaultData,\n            rect: activeRects,\n          }\n        : null,\n    [activeId, node]\n  );\n  const activeRef = useRef<UniqueIdentifier | null>(null);\n  const [activeSensor, setActiveSensor] = useState<SensorInstance | null>(null);\n  const [activatorEvent, setActivatorEvent] = useState<Event | null>(null);\n  const latestProps = useLatestValue(props, Object.values(props));\n  const draggableDescribedById = useUniqueId(`DndDescribedBy`, id);\n  const enabledDroppableContainers = useMemo(\n    () => droppableContainers.getEnabled(),\n    [droppableContainers]\n  );\n  const measuringConfiguration = useMeasuringConfiguration(measuring);\n  const {droppableRects, measureDroppableContainers, measuringScheduled} =\n    useDroppableMeasuring(enabledDroppableContainers, {\n      dragging: isInitialized,\n      dependencies: [translate.x, translate.y],\n      config: measuringConfiguration.droppable,\n    });\n  const activeNode = useCachedNode(draggableNodes, activeId);\n  const activationCoordinates = useMemo(\n    () => (activatorEvent ? getEventCoordinates(activatorEvent) : null),\n    [activatorEvent]\n  );\n  const autoScrollOptions = getAutoScrollerOptions();\n  const initialActiveNodeRect = useInitialRect(\n    activeNode,\n    measuringConfiguration.draggable.measure\n  );\n\n  useLayoutShiftScrollCompensation({\n    activeNode: activeId != null ? draggableNodes.get(activeId) : null,\n    config: autoScrollOptions.layoutShiftCompensation,\n    initialRect: initialActiveNodeRect,\n    measure: measuringConfiguration.draggable.measure,\n  });\n\n  const activeNodeRect = useRect(\n    activeNode,\n    measuringConfiguration.draggable.measure,\n    initialActiveNodeRect\n  );\n  const containerNodeRect = useRect(\n    activeNode ? activeNode.parentElement : null\n  );\n  const sensorContext = useRef<SensorContext>({\n    activatorEvent: null,\n    active: null,\n    activeNode,\n    collisionRect: null,\n    collisions: null,\n    droppableRects,\n    draggableNodes,\n    draggingNode: null,\n    draggingNodeRect: null,\n    droppableContainers,\n    over: null,\n    scrollableAncestors: [],\n    scrollAdjustedTranslate: null,\n  });\n  const overNode = droppableContainers.getNodeFor(\n    sensorContext.current.over?.id\n  );\n  const dragOverlay = useDragOverlayMeasuring({\n    measure: measuringConfiguration.dragOverlay.measure,\n  });\n\n  // Use the rect of the drag overlay if it is mounted\n  const draggingNode = dragOverlay.nodeRef.current ?? activeNode;\n  const draggingNodeRect = isInitialized\n    ? dragOverlay.rect ?? activeNodeRect\n    : null;\n  const usesDragOverlay = Boolean(\n    dragOverlay.nodeRef.current && dragOverlay.rect\n  );\n  // The delta between the previous and new position of the draggable node\n  // is only relevant when there is no drag overlay\n  const nodeRectDelta = useRectDelta(usesDragOverlay ? null : activeNodeRect);\n\n  // Get the window rect of the dragging node\n  const windowRect = useWindowRect(\n    draggingNode ? getWindow(draggingNode) : null\n  );\n\n  // Get scrollable ancestors of the dragging node\n  const scrollableAncestors = useScrollableAncestors(\n    isInitialized ? overNode ?? activeNode : null\n  );\n  const scrollableAncestorRects = useRects(scrollableAncestors);\n\n  // Apply modifiers\n  const modifiedTranslate = applyModifiers(modifiers, {\n    transform: {\n      x: translate.x - nodeRectDelta.x,\n      y: translate.y - nodeRectDelta.y,\n      scaleX: 1,\n      scaleY: 1,\n    },\n    activatorEvent,\n    active,\n    activeNodeRect,\n    containerNodeRect,\n    draggingNodeRect,\n    over: sensorContext.current.over,\n    overlayNodeRect: dragOverlay.rect,\n    scrollableAncestors,\n    scrollableAncestorRects,\n    windowRect,\n  });\n\n  const pointerCoordinates = activationCoordinates\n    ? add(activationCoordinates, translate)\n    : null;\n\n  const scrollOffsets = useScrollOffsets(scrollableAncestors);\n  // Represents the scroll delta since dragging was initiated\n  const scrollAdjustment = useScrollOffsetsDelta(scrollOffsets);\n  // Represents the scroll delta since the last time the active node rect was measured\n  const activeNodeScrollDelta = useScrollOffsetsDelta(scrollOffsets, [\n    activeNodeRect,\n  ]);\n\n  const scrollAdjustedTranslate = add(modifiedTranslate, scrollAdjustment);\n\n  const collisionRect = draggingNodeRect\n    ? getAdjustedRect(draggingNodeRect, modifiedTranslate)\n    : null;\n\n  const collisions =\n    active && collisionRect\n      ? collisionDetection({\n          active,\n          collisionRect,\n          droppableRects,\n          droppableContainers: enabledDroppableContainers,\n          pointerCoordinates,\n        })\n      : null;\n  const overId = getFirstCollision(collisions, 'id');\n  const [over, setOver] = useState<Over | null>(null);\n\n  // When there is no drag overlay used, we need to account for the\n  // window scroll delta\n  const appliedTranslate = usesDragOverlay\n    ? modifiedTranslate\n    : add(modifiedTranslate, activeNodeScrollDelta);\n\n  const transform = adjustScale(\n    appliedTranslate,\n    over?.rect ?? null,\n    activeNodeRect\n  );\n\n  const activeSensorRef = useRef<SensorInstance | null>(null);\n  const instantiateSensor = useCallback(\n    (\n      event: React.SyntheticEvent,\n      {sensor: Sensor, options}: SensorDescriptor<any>\n    ) => {\n      if (activeRef.current == null) {\n        return;\n      }\n\n      const activeNode = draggableNodes.get(activeRef.current);\n\n      if (!activeNode) {\n        return;\n      }\n\n      const activatorEvent = event.nativeEvent;\n\n      const sensorInstance = new Sensor({\n        active: activeRef.current,\n        activeNode,\n        event: activatorEvent,\n        options,\n        // Sensors need to be instantiated with refs for arguments that change over time\n        // otherwise they are frozen in time with the stale arguments\n        context: sensorContext,\n        onAbort(id) {\n          const draggableNode = draggableNodes.get(id);\n\n          if (!draggableNode) {\n            return;\n          }\n\n          const {onDragAbort} = latestProps.current;\n          const event: DragAbortEvent = {id};\n          onDragAbort?.(event);\n          dispatchMonitorEvent({type: 'onDragAbort', event});\n        },\n        onPending(id, constraint, initialCoordinates, offset) {\n          const draggableNode = draggableNodes.get(id);\n\n          if (!draggableNode) {\n            return;\n          }\n\n          const {onDragPending} = latestProps.current;\n          const event: DragPendingEvent = {\n            id,\n            constraint,\n            initialCoordinates,\n            offset,\n          };\n\n          onDragPending?.(event);\n          dispatchMonitorEvent({type: 'onDragPending', event});\n        },\n        onStart(initialCoordinates) {\n          const id = activeRef.current;\n\n          if (id == null) {\n            return;\n          }\n\n          const draggableNode = draggableNodes.get(id);\n\n          if (!draggableNode) {\n            return;\n          }\n\n          const {onDragStart} = latestProps.current;\n          const event: DragStartEvent = {\n            activatorEvent,\n            active: {id, data: draggableNode.data, rect: activeRects},\n          };\n\n          unstable_batchedUpdates(() => {\n            onDragStart?.(event);\n            setStatus(Status.Initializing);\n            dispatch({\n              type: Action.DragStart,\n              initialCoordinates,\n              active: id,\n            });\n            dispatchMonitorEvent({type: 'onDragStart', event});\n            setActiveSensor(activeSensorRef.current);\n            setActivatorEvent(activatorEvent);\n          });\n        },\n        onMove(coordinates) {\n          dispatch({\n            type: Action.DragMove,\n            coordinates,\n          });\n        },\n        onEnd: createHandler(Action.DragEnd),\n        onCancel: createHandler(Action.DragCancel),\n      });\n\n      activeSensorRef.current = sensorInstance;\n\n      function createHandler(type: Action.DragEnd | Action.DragCancel) {\n        return async function handler() {\n          const {active, collisions, over, scrollAdjustedTranslate} =\n            sensorContext.current;\n          let event: DragEndEvent | null = null;\n\n          if (active && scrollAdjustedTranslate) {\n            const {cancelDrop} = latestProps.current;\n\n            event = {\n              activatorEvent,\n              active: active,\n              collisions,\n              delta: scrollAdjustedTranslate,\n              over,\n            };\n\n            if (type === Action.DragEnd && typeof cancelDrop === 'function') {\n              const shouldCancel = await Promise.resolve(cancelDrop(event));\n\n              if (shouldCancel) {\n                type = Action.DragCancel;\n              }\n            }\n          }\n\n          activeRef.current = null;\n\n          unstable_batchedUpdates(() => {\n            dispatch({type});\n            setStatus(Status.Uninitialized);\n            setOver(null);\n            setActiveSensor(null);\n            setActivatorEvent(null);\n            activeSensorRef.current = null;\n\n            const eventName =\n              type === Action.DragEnd ? 'onDragEnd' : 'onDragCancel';\n\n            if (event) {\n              const handler = latestProps.current[eventName];\n\n              handler?.(event);\n              dispatchMonitorEvent({type: eventName, event});\n            }\n          });\n        };\n      }\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [draggableNodes]\n  );\n\n  const bindActivatorToSensorInstantiator = useCallback(\n    (\n      handler: SensorActivatorFunction<any>,\n      sensor: SensorDescriptor<any>\n    ): SyntheticListener['handler'] => {\n      return (event, active) => {\n        const nativeEvent = event.nativeEvent as DndEvent;\n        const activeDraggableNode = draggableNodes.get(active);\n\n        if (\n          // Another sensor is already instantiating\n          activeRef.current !== null ||\n          // No active draggable\n          !activeDraggableNode ||\n          // Event has already been captured\n          nativeEvent.dndKit ||\n          nativeEvent.defaultPrevented\n        ) {\n          return;\n        }\n\n        const activationContext = {\n          active: activeDraggableNode,\n        };\n        const shouldActivate = handler(\n          event,\n          sensor.options,\n          activationContext\n        );\n\n        if (shouldActivate === true) {\n          nativeEvent.dndKit = {\n            capturedBy: sensor.sensor,\n          };\n\n          activeRef.current = active;\n          instantiateSensor(event, sensor);\n        }\n      };\n    },\n    [draggableNodes, instantiateSensor]\n  );\n\n  const activators = useCombineActivators(\n    sensors,\n    bindActivatorToSensorInstantiator\n  );\n\n  useSensorSetup(sensors);\n\n  useIsomorphicLayoutEffect(() => {\n    if (activeNodeRect && status === Status.Initializing) {\n      setStatus(Status.Initialized);\n    }\n  }, [activeNodeRect, status]);\n\n  useEffect(\n    () => {\n      const {onDragMove} = latestProps.current;\n      const {active, activatorEvent, collisions, over} = sensorContext.current;\n\n      if (!active || !activatorEvent) {\n        return;\n      }\n\n      const event: DragMoveEvent = {\n        active,\n        activatorEvent,\n        collisions,\n        delta: {\n          x: scrollAdjustedTranslate.x,\n          y: scrollAdjustedTranslate.y,\n        },\n        over,\n      };\n\n      unstable_batchedUpdates(() => {\n        onDragMove?.(event);\n        dispatchMonitorEvent({type: 'onDragMove', event});\n      });\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [scrollAdjustedTranslate.x, scrollAdjustedTranslate.y]\n  );\n\n  useEffect(\n    () => {\n      const {\n        active,\n        activatorEvent,\n        collisions,\n        droppableContainers,\n        scrollAdjustedTranslate,\n      } = sensorContext.current;\n\n      if (\n        !active ||\n        activeRef.current == null ||\n        !activatorEvent ||\n        !scrollAdjustedTranslate\n      ) {\n        return;\n      }\n\n      const {onDragOver} = latestProps.current;\n      const overContainer = droppableContainers.get(overId);\n      const over =\n        overContainer && overContainer.rect.current\n          ? {\n              id: overContainer.id,\n              rect: overContainer.rect.current,\n              data: overContainer.data,\n              disabled: overContainer.disabled,\n            }\n          : null;\n      const event: DragOverEvent = {\n        active,\n        activatorEvent,\n        collisions,\n        delta: {\n          x: scrollAdjustedTranslate.x,\n          y: scrollAdjustedTranslate.y,\n        },\n        over,\n      };\n\n      unstable_batchedUpdates(() => {\n        setOver(over);\n        onDragOver?.(event);\n        dispatchMonitorEvent({type: 'onDragOver', event});\n      });\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [overId]\n  );\n\n  useIsomorphicLayoutEffect(() => {\n    sensorContext.current = {\n      activatorEvent,\n      active,\n      activeNode,\n      collisionRect,\n      collisions,\n      droppableRects,\n      draggableNodes,\n      draggingNode,\n      draggingNodeRect,\n      droppableContainers,\n      over,\n      scrollableAncestors,\n      scrollAdjustedTranslate,\n    };\n\n    activeRects.current = {\n      initial: draggingNodeRect,\n      translated: collisionRect,\n    };\n  }, [\n    active,\n    activeNode,\n    collisions,\n    collisionRect,\n    draggableNodes,\n    draggingNode,\n    draggingNodeRect,\n    droppableRects,\n    droppableContainers,\n    over,\n    scrollableAncestors,\n    scrollAdjustedTranslate,\n  ]);\n\n  useAutoScroller({\n    ...autoScrollOptions,\n    delta: translate,\n    draggingRect: collisionRect,\n    pointerCoordinates,\n    scrollableAncestors,\n    scrollableAncestorRects,\n  });\n\n  const publicContext = useMemo(() => {\n    const context: PublicContextDescriptor = {\n      active,\n      activeNode,\n      activeNodeRect,\n      activatorEvent,\n      collisions,\n      containerNodeRect,\n      dragOverlay,\n      draggableNodes,\n      droppableContainers,\n      droppableRects,\n      over,\n      measureDroppableContainers,\n      scrollableAncestors,\n      scrollableAncestorRects,\n      measuringConfiguration,\n      measuringScheduled,\n      windowRect,\n    };\n\n    return context;\n  }, [\n    active,\n    activeNode,\n    activeNodeRect,\n    activatorEvent,\n    collisions,\n    containerNodeRect,\n    dragOverlay,\n    draggableNodes,\n    droppableContainers,\n    droppableRects,\n    over,\n    measureDroppableContainers,\n    scrollableAncestors,\n    scrollableAncestorRects,\n    measuringConfiguration,\n    measuringScheduled,\n    windowRect,\n  ]);\n\n  const internalContext = useMemo(() => {\n    const context: InternalContextDescriptor = {\n      activatorEvent,\n      activators,\n      active,\n      activeNodeRect,\n      ariaDescribedById: {\n        draggable: draggableDescribedById,\n      },\n      dispatch,\n      draggableNodes,\n      over,\n      measureDroppableContainers,\n    };\n\n    return context;\n  }, [\n    activatorEvent,\n    activators,\n    active,\n    activeNodeRect,\n    dispatch,\n    draggableDescribedById,\n    draggableNodes,\n    over,\n    measureDroppableContainers,\n  ]);\n\n  return (\n    <DndMonitorContext.Provider value={registerMonitorListener}>\n      <InternalContext.Provider value={internalContext}>\n        <PublicContext.Provider value={publicContext}>\n          <ActiveDraggableContext.Provider value={transform}>\n            {children}\n          </ActiveDraggableContext.Provider>\n        </PublicContext.Provider>\n        <RestoreFocus disabled={accessibility?.restoreFocus === false} />\n      </InternalContext.Provider>\n      <Accessibility\n        {...accessibility}\n        hiddenTextDescribedById={draggableDescribedById}\n      />\n    </DndMonitorContext.Provider>\n  );\n\n  function getAutoScrollerOptions() {\n    const activeSensorDisablesAutoscroll =\n      activeSensor?.autoScrollEnabled === false;\n    const autoScrollGloballyDisabled =\n      typeof autoScroll === 'object'\n        ? autoScroll.enabled === false\n        : autoScroll === false;\n    const enabled =\n      isInitialized &&\n      !activeSensorDisablesAutoscroll &&\n      !autoScrollGloballyDisabled;\n\n    if (typeof autoScroll === 'object') {\n      return {\n        ...autoScroll,\n        enabled,\n      };\n    }\n\n    return {enabled};\n  }\n});\n", "import {createContext, useContext, useMemo} from 'react';\nimport {\n  Transform,\n  useNodeRef,\n  useIsomorphicLayoutEffect,\n  useLatestValue,\n  useUniqueId,\n} from '@dnd-kit/utilities';\n\nimport {InternalContext, Data} from '../store';\nimport type {UniqueIdentifier} from '../types';\nimport {ActiveDraggableContext} from '../components/DndContext';\nimport {useSyntheticListeners, SyntheticListenerMap} from './utilities';\n\nexport interface UseDraggableArguments {\n  id: UniqueIdentifier;\n  data?: Data;\n  disabled?: boolean;\n  attributes?: {\n    role?: string;\n    roleDescription?: string;\n    tabIndex?: number;\n  };\n}\n\nexport interface DraggableAttributes {\n  role: string;\n  tabIndex: number;\n  'aria-disabled': boolean;\n  'aria-pressed': boolean | undefined;\n  'aria-roledescription': string;\n  'aria-describedby': string;\n}\n\nexport type DraggableSyntheticListeners = SyntheticListenerMap | undefined;\n\nconst NullContext = createContext<any>(null);\n\nconst defaultRole = 'button';\n\nconst ID_PREFIX = 'Draggable';\n\nexport function useDraggable({\n  id,\n  data,\n  disabled = false,\n  attributes,\n}: UseDraggableArguments) {\n  const key = useUniqueId(ID_PREFIX);\n  const {\n    activators,\n    activatorEvent,\n    active,\n    activeNodeRect,\n    ariaDescribedById,\n    draggableNodes,\n    over,\n  } = useContext(InternalContext);\n  const {\n    role = defaultRole,\n    roleDescription = 'draggable',\n    tabIndex = 0,\n  } = attributes ?? {};\n  const isDragging = active?.id === id;\n  const transform: Transform | null = useContext(\n    isDragging ? ActiveDraggableContext : NullContext\n  );\n  const [node, setNodeRef] = useNodeRef();\n  const [activatorNode, setActivatorNodeRef] = useNodeRef();\n  const listeners = useSyntheticListeners(activators, id);\n  const dataRef = useLatestValue(data);\n\n  useIsomorphicLayoutEffect(\n    () => {\n      draggableNodes.set(id, {id, key, node, activatorNode, data: dataRef});\n\n      return () => {\n        const node = draggableNodes.get(id);\n\n        if (node && node.key === key) {\n          draggableNodes.delete(id);\n        }\n      };\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [draggableNodes, id]\n  );\n\n  const memoizedAttributes: DraggableAttributes = useMemo(\n    () => ({\n      role,\n      tabIndex,\n      'aria-disabled': disabled,\n      'aria-pressed': isDragging && role === defaultRole ? true : undefined,\n      'aria-roledescription': roleDescription,\n      'aria-describedby': ariaDescribedById.draggable,\n    }),\n    [\n      disabled,\n      role,\n      tabIndex,\n      isDragging,\n      roleDescription,\n      ariaDescribedById.draggable,\n    ]\n  );\n\n  return {\n    active,\n    activatorEvent,\n    activeNodeRect,\n    attributes: memoizedAttributes,\n    isDragging,\n    listeners: disabled ? undefined : listeners,\n    node,\n    over,\n    setNodeRef,\n    setActivatorNodeRef,\n    transform,\n  };\n}\n", "import {ContextType, useContext} from 'react';\nimport {PublicContext} from '../store';\n\nexport function useDndContext() {\n  return useContext(PublicContext);\n}\n\nexport type UseDndContextReturnValue = ContextType<typeof PublicContext>;\n", "import {useCallback, useContext, useEffect, useRef} from 'react';\nimport {useLatestValue, useNodeRef, useUniqueId} from '@dnd-kit/utilities';\n\nimport {InternalContext, Action, Data} from '../store';\nimport type {ClientRect, UniqueIdentifier} from '../types';\n\nimport {useResizeObserver} from './utilities';\n\ninterface ResizeObserverConfig {\n  /** Whether the ResizeObserver should be disabled entirely */\n  disabled?: boolean;\n  /** Resize events may affect the layout and position of other droppable containers.\n   * Specify an array of `UniqueIdentifier` of droppable containers that should also be re-measured\n   * when this droppable container resizes. Specifying an empty array re-measures all droppable containers.\n   */\n  updateMeasurementsFor?: UniqueIdentifier[];\n  /** Represents the debounce timeout between when resize events are observed and when elements are re-measured */\n  timeout?: number;\n}\n\nexport interface UseDroppableArguments {\n  id: UniqueIdentifier;\n  disabled?: boolean;\n  data?: Data;\n  resizeObserverConfig?: ResizeObserverConfig;\n}\n\nconst ID_PREFIX = 'Droppable';\n\nconst defaultResizeObserverConfig = {\n  timeout: 25,\n};\n\nexport function useDroppable({\n  data,\n  disabled = false,\n  id,\n  resizeObserverConfig,\n}: UseDroppableArguments) {\n  const key = useUniqueId(ID_PREFIX);\n  const {active, dispatch, over, measureDroppableContainers} =\n    useContext(InternalContext);\n  const previous = useRef({disabled});\n  const resizeObserverConnected = useRef(false);\n  const rect = useRef<ClientRect | null>(null);\n  const callbackId = useRef<NodeJS.Timeout | null>(null);\n  const {\n    disabled: resizeObserverDisabled,\n    updateMeasurementsFor,\n    timeout: resizeObserverTimeout,\n  } = {\n    ...defaultResizeObserverConfig,\n    ...resizeObserverConfig,\n  };\n  const ids = useLatestValue(updateMeasurementsFor ?? id);\n  const handleResize = useCallback(\n    () => {\n      if (!resizeObserverConnected.current) {\n        // ResizeObserver invokes the `handleResize` callback as soon as `observe` is called,\n        // assuming the element is rendered and displayed.\n        resizeObserverConnected.current = true;\n        return;\n      }\n\n      if (callbackId.current != null) {\n        clearTimeout(callbackId.current);\n      }\n\n      callbackId.current = setTimeout(() => {\n        measureDroppableContainers(\n          Array.isArray(ids.current) ? ids.current : [ids.current]\n        );\n        callbackId.current = null;\n      }, resizeObserverTimeout);\n    },\n    //eslint-disable-next-line react-hooks/exhaustive-deps\n    [resizeObserverTimeout]\n  );\n  const resizeObserver = useResizeObserver({\n    callback: handleResize,\n    disabled: resizeObserverDisabled || !active,\n  });\n  const handleNodeChange = useCallback(\n    (newElement: HTMLElement | null, previousElement: HTMLElement | null) => {\n      if (!resizeObserver) {\n        return;\n      }\n\n      if (previousElement) {\n        resizeObserver.unobserve(previousElement);\n        resizeObserverConnected.current = false;\n      }\n\n      if (newElement) {\n        resizeObserver.observe(newElement);\n      }\n    },\n    [resizeObserver]\n  );\n  const [nodeRef, setNodeRef] = useNodeRef(handleNodeChange);\n  const dataRef = useLatestValue(data);\n\n  useEffect(() => {\n    if (!resizeObserver || !nodeRef.current) {\n      return;\n    }\n\n    resizeObserver.disconnect();\n    resizeObserverConnected.current = false;\n    resizeObserver.observe(nodeRef.current);\n  }, [nodeRef, resizeObserver]);\n\n  useEffect(\n    () => {\n      dispatch({\n        type: Action.RegisterDroppable,\n        element: {\n          id,\n          key,\n          disabled,\n          node: nodeRef,\n          rect,\n          data: dataRef,\n        },\n      });\n\n      return () =>\n        dispatch({\n          type: Action.UnregisterDroppable,\n          key,\n          id,\n        });\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [id]\n  );\n\n  useEffect(() => {\n    if (disabled !== previous.current.disabled) {\n      dispatch({\n        type: Action.SetDroppableDisabled,\n        id,\n        key,\n        disabled,\n      });\n\n      previous.current.disabled = disabled;\n    }\n  }, [id, key, disabled, dispatch]);\n\n  return {\n    active,\n    rect,\n    isOver: over?.id === id,\n    node: nodeRef,\n    over,\n    setNodeRef,\n  };\n}\n", "import React, {cloneElement, useState} from 'react';\nimport {useIsomorphicLayoutEffect, usePrevious} from '@dnd-kit/utilities';\n\nimport type {UniqueIdentifier} from '../../../../types';\n\nexport type Animation = (\n  key: UniqueIdentifier,\n  node: HTMLElement\n) => Promise<void> | void;\n\nexport interface Props {\n  animation: Animation;\n  children: React.ReactElement | null;\n}\n\nexport function AnimationManager({animation, children}: Props) {\n  const [\n    clonedChildren,\n    setClonedChildren,\n  ] = useState<React.ReactElement | null>(null);\n  const [element, setElement] = useState<HTMLElement | null>(null);\n  const previousChildren = usePrevious(children);\n\n  if (!children && !clonedChildren && previousChildren) {\n    setClonedChildren(previousChildren);\n  }\n\n  useIsomorphicLayoutEffect(() => {\n    if (!element) {\n      return;\n    }\n\n    const key = clonedChildren?.key;\n    const id = clonedChildren?.props.id;\n\n    if (key == null || id == null) {\n      setClonedChildren(null);\n      return;\n    }\n\n    Promise.resolve(animation(id, element)).then(() => {\n      setClonedChildren(null);\n    });\n  }, [animation, clonedChildren, element]);\n\n  return (\n    <>\n      {children}\n      {clonedChildren ? cloneElement(clonedChildren, {ref: setElement}) : null}\n    </>\n  );\n}\n", "import React from 'react';\nimport type {Transform} from '@dnd-kit/utilities';\n\nimport {InternalContext, defaultInternalContext} from '../../../../store';\nimport {ActiveDraggableContext} from '../../../DndContext';\n\ninterface Props {\n  children: React.ReactNode;\n}\n\nconst defaultTransform: Transform = {\n  x: 0,\n  y: 0,\n  scaleX: 1,\n  scaleY: 1,\n};\n\nexport function NullifiedContextProvider({children}: Props) {\n  return (\n    <InternalContext.Provider value={defaultInternalContext}>\n      <ActiveDraggableContext.Provider value={defaultTransform}>\n        {children}\n      </ActiveDraggableContext.Provider>\n    </InternalContext.Provider>\n  );\n}\n", "import React, {forwardRef} from 'react';\nimport {CSS, isKeyboardEvent} from '@dnd-kit/utilities';\n\nimport type {Transform} from '@dnd-kit/utilities';\n\nimport {getRelativeTransformOrigin} from '../../../../utilities';\nimport type {ClientRect, UniqueIdentifier} from '../../../../types';\n\ntype TransitionGetter = (\n  activatorEvent: Event | null\n) => React.CSSProperties['transition'] | undefined;\n\nexport interface Props {\n  as: keyof JSX.IntrinsicElements;\n  activatorEvent: Event | null;\n  adjustScale?: boolean;\n  children?: React.ReactNode;\n  className?: string;\n  id: UniqueIdentifier;\n  rect: ClientRect | null;\n  style?: React.CSSProperties;\n  transition?: string | TransitionGetter;\n  transform: Transform;\n}\n\nconst baseStyles: React.CSSProperties = {\n  position: 'fixed',\n  touchAction: 'none',\n};\n\nconst defaultTransition: TransitionGetter = (activatorEvent) => {\n  const isKeyboardActivator = isKeyboardEvent(activatorEvent);\n\n  return isKeyboardActivator ? 'transform 250ms ease' : undefined;\n};\n\nexport const PositionedOverlay = forwardRef<HTMLElement, Props>(\n  (\n    {\n      as,\n      activatorEvent,\n      adjustScale,\n      children,\n      className,\n      rect,\n      style,\n      transform,\n      transition = defaultTransition,\n    },\n    ref\n  ) => {\n    if (!rect) {\n      return null;\n    }\n\n    const scaleAdjustedTransform = adjustScale\n      ? transform\n      : {\n          ...transform,\n          scaleX: 1,\n          scaleY: 1,\n        };\n    const styles: React.CSSProperties | undefined = {\n      ...baseStyles,\n      width: rect.width,\n      height: rect.height,\n      top: rect.top,\n      left: rect.left,\n      transform: CSS.Transform.toString(scaleAdjustedTransform),\n      transformOrigin:\n        adjustScale && activatorEvent\n          ? getRelativeTransformOrigin(\n              activatorEvent as MouseEvent | KeyboardEvent | TouchEvent,\n              rect\n            )\n          : undefined,\n      transition:\n        typeof transition === 'function'\n          ? transition(activatorEvent)\n          : transition,\n      ...style,\n    };\n\n    return React.createElement(\n      as,\n      {\n        className,\n        style: styles,\n        ref,\n      },\n      children\n    );\n  }\n);\n", "import {CSS, useEvent, getWindow} from '@dnd-kit/utilities';\nimport type {DeepRequired, Transform} from '@dnd-kit/utilities';\n\nimport type {\n  Active,\n  DraggableNode,\n  DraggableNodes,\n  DroppableContainers,\n} from '../../../store';\nimport type {ClientRect, UniqueIdentifier} from '../../../types';\nimport {getMeasurableNode} from '../../../utilities/nodes';\nimport {scrollIntoViewIfNeeded} from '../../../utilities/scroll';\nimport {parseTransform} from '../../../utilities/transform';\nimport type {MeasuringConfiguration} from '../../DndContext';\nimport type {Animation} from '../components';\n\ninterface SharedParameters {\n  active: {\n    id: UniqueIdentifier;\n    data: Active['data'];\n    node: HTMLElement;\n    rect: ClientRect;\n  };\n  dragOverlay: {\n    node: HTMLElement;\n    rect: ClientRect;\n  };\n  draggableNodes: DraggableNodes;\n  droppableContainers: DroppableContainers;\n  measuringConfiguration: DeepRequired<MeasuringConfiguration>;\n}\n\nexport interface KeyframeResolverParameters extends SharedParameters {\n  transform: {\n    initial: Transform;\n    final: Transform;\n  };\n}\n\nexport type KeyframeResolver = (\n  parameters: KeyframeResolverParameters\n) => Keyframe[];\n\nexport interface DropAnimationOptions {\n  keyframes?: KeyframeResolver;\n  duration?: number;\n  easing?: string;\n  sideEffects?: DropAnimationSideEffects | null;\n}\n\nexport type DropAnimation = DropAnimationFunction | DropAnimationOptions;\n\ninterface Arguments {\n  draggableNodes: DraggableNodes;\n  droppableContainers: DroppableContainers;\n  measuringConfiguration: DeepRequired<MeasuringConfiguration>;\n  config?: DropAnimation | null;\n}\n\nexport interface DropAnimationFunctionArguments extends SharedParameters {\n  transform: Transform;\n}\n\nexport type DropAnimationFunction = (\n  args: DropAnimationFunctionArguments\n) => Promise<void> | void;\n\ntype CleanupFunction = () => void;\n\nexport interface DropAnimationSideEffectsParameters extends SharedParameters {}\n\nexport type DropAnimationSideEffects = (\n  parameters: DropAnimationSideEffectsParameters\n) => CleanupFunction | void;\n\ntype ExtractStringProperties<T> = {\n  [K in keyof T]?: T[K] extends string ? string : never;\n};\n\ntype Styles = ExtractStringProperties<CSSStyleDeclaration>;\n\ninterface DefaultDropAnimationSideEffectsOptions {\n  className?: {\n    active?: string;\n    dragOverlay?: string;\n  };\n  styles?: {\n    active?: Styles;\n    dragOverlay?: Styles;\n  };\n}\n\nexport const defaultDropAnimationSideEffects = (\n  options: DefaultDropAnimationSideEffectsOptions\n): DropAnimationSideEffects => ({active, dragOverlay}) => {\n  const originalStyles: Record<string, string> = {};\n  const {styles, className} = options;\n\n  if (styles?.active) {\n    for (const [key, value] of Object.entries(styles.active)) {\n      if (value === undefined) {\n        continue;\n      }\n\n      originalStyles[key] = active.node.style.getPropertyValue(key);\n      active.node.style.setProperty(key, value);\n    }\n  }\n\n  if (styles?.dragOverlay) {\n    for (const [key, value] of Object.entries(styles.dragOverlay)) {\n      if (value === undefined) {\n        continue;\n      }\n\n      dragOverlay.node.style.setProperty(key, value);\n    }\n  }\n\n  if (className?.active) {\n    active.node.classList.add(className.active);\n  }\n\n  if (className?.dragOverlay) {\n    dragOverlay.node.classList.add(className.dragOverlay);\n  }\n\n  return function cleanup() {\n    for (const [key, value] of Object.entries(originalStyles)) {\n      active.node.style.setProperty(key, value);\n    }\n\n    if (className?.active) {\n      active.node.classList.remove(className.active);\n    }\n  };\n};\n\nconst defaultKeyframeResolver: KeyframeResolver = ({\n  transform: {initial, final},\n}) => [\n  {\n    transform: CSS.Transform.toString(initial),\n  },\n  {\n    transform: CSS.Transform.toString(final),\n  },\n];\n\nexport const defaultDropAnimationConfiguration: Required<DropAnimationOptions> = {\n  duration: 250,\n  easing: 'ease',\n  keyframes: defaultKeyframeResolver,\n  sideEffects: defaultDropAnimationSideEffects({\n    styles: {\n      active: {\n        opacity: '0',\n      },\n    },\n  }),\n};\n\nexport function useDropAnimation({\n  config,\n  draggableNodes,\n  droppableContainers,\n  measuringConfiguration,\n}: Arguments) {\n  return useEvent<Animation>((id, node) => {\n    if (config === null) {\n      return;\n    }\n\n    const activeDraggable: DraggableNode | undefined = draggableNodes.get(id);\n\n    if (!activeDraggable) {\n      return;\n    }\n\n    const activeNode = activeDraggable.node.current;\n\n    if (!activeNode) {\n      return;\n    }\n\n    const measurableNode = getMeasurableNode(node);\n\n    if (!measurableNode) {\n      return;\n    }\n    const {transform} = getWindow(node).getComputedStyle(node);\n    const parsedTransform = parseTransform(transform);\n\n    if (!parsedTransform) {\n      return;\n    }\n\n    const animation: DropAnimationFunction =\n      typeof config === 'function'\n        ? config\n        : createDefaultDropAnimation(config);\n\n    scrollIntoViewIfNeeded(\n      activeNode,\n      measuringConfiguration.draggable.measure\n    );\n\n    return animation({\n      active: {\n        id,\n        data: activeDraggable.data,\n        node: activeNode,\n        rect: measuringConfiguration.draggable.measure(activeNode),\n      },\n      draggableNodes,\n      dragOverlay: {\n        node,\n        rect: measuringConfiguration.dragOverlay.measure(measurableNode),\n      },\n      droppableContainers,\n      measuringConfiguration,\n      transform: parsedTransform,\n    });\n  });\n}\n\nfunction createDefaultDropAnimation(\n  options: DropAnimationOptions | undefined\n): DropAnimationFunction {\n  const {duration, easing, sideEffects, keyframes} = {\n    ...defaultDropAnimationConfiguration,\n    ...options,\n  };\n\n  return ({active, dragOverlay, transform, ...rest}) => {\n    if (!duration) {\n      // Do not animate if animation duration is zero.\n      return;\n    }\n\n    const delta = {\n      x: dragOverlay.rect.left - active.rect.left,\n      y: dragOverlay.rect.top - active.rect.top,\n    };\n\n    const scale = {\n      scaleX:\n        transform.scaleX !== 1\n          ? (active.rect.width * transform.scaleX) / dragOverlay.rect.width\n          : 1,\n      scaleY:\n        transform.scaleY !== 1\n          ? (active.rect.height * transform.scaleY) / dragOverlay.rect.height\n          : 1,\n    };\n    const finalTransform = {\n      x: transform.x - delta.x,\n      y: transform.y - delta.y,\n      ...scale,\n    };\n\n    const animationKeyframes = keyframes({\n      ...rest,\n      active,\n      dragOverlay,\n      transform: {initial: transform, final: finalTransform},\n    });\n\n    const [firstKeyframe] = animationKeyframes;\n    const lastKeyframe = animationKeyframes[animationKeyframes.length - 1];\n\n    if (JSON.stringify(firstKeyframe) === JSON.stringify(lastKeyframe)) {\n      // The start and end keyframes are the same, infer that there is no animation needed.\n      return;\n    }\n\n    const cleanup = sideEffects?.({active, dragOverlay, ...rest});\n    const animation = dragOverlay.node.animate(animationKeyframes, {\n      duration,\n      easing,\n      fill: 'forwards',\n    });\n\n    return new Promise((resolve) => {\n      animation.onfinish = () => {\n        cleanup?.();\n        resolve();\n      };\n    });\n  };\n}\n", "import {useMemo} from 'react';\n\nimport type {UniqueIdentifier} from '../../../types';\n\nlet key = 0;\n\nexport function useKey(id: UniqueIdentifier | undefined) {\n  return useMemo(() => {\n    if (id == null) {\n      return;\n    }\n\n    key++;\n    return key;\n  }, [id]);\n}\n", "import React, {useContext} from 'react';\n\nimport {applyModifiers, Modifiers} from '../../modifiers';\nimport {ActiveDraggableContext} from '../DndContext';\nimport {useDndContext} from '../../hooks';\nimport {useInitialValue} from '../../hooks/utilities';\n\nimport {\n  AnimationManager,\n  NullifiedContextProvider,\n  PositionedOverlay,\n} from './components';\nimport type {PositionedOverlayProps} from './components';\n\nimport {useDropAnimation, useKey} from './hooks';\nimport type {DropAnimation} from './hooks';\n\nexport interface Props\n  extends Pick<\n    PositionedOverlayProps,\n    'adjustScale' | 'children' | 'className' | 'style' | 'transition'\n  > {\n  dropAnimation?: DropAnimation | null | undefined;\n  modifiers?: Modifiers;\n  wrapperElement?: keyof JSX.IntrinsicElements;\n  zIndex?: number;\n}\n\nexport const DragOverlay = React.memo(\n  ({\n    adjustScale = false,\n    children,\n    dropAnimation: dropAnimationConfig,\n    style,\n    transition,\n    modifiers,\n    wrapperElement = 'div',\n    className,\n    zIndex = 999,\n  }: Props) => {\n    const {\n      activatorEvent,\n      active,\n      activeNodeRect,\n      containerNodeRect,\n      draggableNodes,\n      droppableContainers,\n      dragOverlay,\n      over,\n      measuringConfiguration,\n      scrollableAncestors,\n      scrollableAncestorRects,\n      windowRect,\n    } = useDndContext();\n    const transform = useContext(ActiveDraggableContext);\n    const key = useKey(active?.id);\n    const modifiedTransform = applyModifiers(modifiers, {\n      activatorEvent,\n      active,\n      activeNodeRect,\n      containerNodeRect,\n      draggingNodeRect: dragOverlay.rect,\n      over,\n      overlayNodeRect: dragOverlay.rect,\n      scrollableAncestors,\n      scrollableAncestorRects,\n      transform,\n      windowRect,\n    });\n    const initialRect = useInitialValue(activeNodeRect);\n    const dropAnimation = useDropAnimation({\n      config: dropAnimationConfig,\n      draggableNodes,\n      droppableContainers,\n      measuringConfiguration,\n    });\n    // We need to wait for the active node to be measured before connecting the drag overlay ref\n    // otherwise collisions can be computed against a mispositioned drag overlay\n    const ref = initialRect ? dragOverlay.setRef : undefined;\n\n    return (\n      <NullifiedContextProvider>\n        <AnimationManager animation={dropAnimation}>\n          {active && key ? (\n            <PositionedOverlay\n              key={key}\n              id={active.id}\n              ref={ref}\n              as={wrapperElement}\n              activatorEvent={activatorEvent}\n              adjustScale={adjustScale}\n              className={className}\n              transition={transition}\n              rect={initialRect}\n              style={{\n                zIndex,\n                ...style,\n              }}\n              transform={modifiedTransform}\n            >\n              {children}\n            </PositionedOverlay>\n          ) : null}\n        </AnimationManager>\n      </NullifiedContextProvider>\n    );\n  }\n);\n", "/**\n * Move an array item to a different position. Returns a new array with the item moved to the new position.\n */\nexport function arrayMove<T>(array: T[], from: number, to: number): T[] {\n  const newArray = array.slice();\n  newArray.splice(\n    to < 0 ? newArray.length + to : to,\n    0,\n    newArray.splice(from, 1)[0]\n  );\n\n  return newArray;\n}\n", "/**\n * Swap an array item to a different position. Returns a new array with the item swapped to the new position.\n */\nexport function arraySwap<T>(array: T[], from: number, to: number): T[] {\n  const newArray = array.slice();\n\n  newArray[from] = array[to];\n  newArray[to] = array[from];\n\n  return newArray;\n}\n", "import type {\n  ClientRect,\n  UniqueIdentifier,\n  UseDndContextReturnValue,\n} from '@dnd-kit/core';\n\nexport function getSortedRects(\n  items: UniqueIdentifier[],\n  rects: UseDndContextReturnValue['droppableRects']\n) {\n  return items.reduce<ClientRect[]>((accumulator, id, index) => {\n    const rect = rects.get(id);\n\n    if (rect) {\n      accumulator[index] = rect;\n    }\n\n    return accumulator;\n  }, Array(items.length));\n}\n", "export function isValidIndex(index: number | null): index is number {\n  return index !== null && index >= 0;\n}\n", "import type {UniqueIdentifier} from '@dnd-kit/core';\n\nexport function itemsEqual(a: UniqueIdentifier[], b: UniqueIdentifier[]) {\n  if (a === b) {\n    return true;\n  }\n\n  if (a.length !== b.length) {\n    return false;\n  }\n\n  for (let i = 0; i < a.length; i++) {\n    if (a[i] !== b[i]) {\n      return false;\n    }\n  }\n\n  return true;\n}\n", "import type {Disabled} from '../types';\n\nexport function normalizeDisabled(disabled: boolean | Disabled): Disabled {\n  if (typeof disabled === 'boolean') {\n    return {\n      draggable: disabled,\n      droppable: disabled,\n    };\n  }\n\n  return disabled;\n}\n", "import type {ClientRect} from '@dnd-kit/core';\nimport type {SortingStrategy} from '../types';\n\n// To-do: We should be calculating scale transformation\nconst defaultScale = {\n  scaleX: 1,\n  scaleY: 1,\n};\n\nexport const horizontalListSortingStrategy: SortingStrategy = ({\n  rects,\n  activeNodeRect: fallbackActiveRect,\n  activeIndex,\n  overIndex,\n  index,\n}) => {\n  const activeNodeRect = rects[activeIndex] ?? fallbackActiveRect;\n\n  if (!activeNodeRect) {\n    return null;\n  }\n\n  const itemGap = getItemGap(rects, index, activeIndex);\n\n  if (index === activeIndex) {\n    const newIndexRect = rects[overIndex];\n\n    if (!newIndexRect) {\n      return null;\n    }\n\n    return {\n      x:\n        activeIndex < overIndex\n          ? newIndexRect.left +\n            newIndexRect.width -\n            (activeNodeRect.left + activeNodeRect.width)\n          : newIndexRect.left - activeNodeRect.left,\n      y: 0,\n      ...defaultScale,\n    };\n  }\n\n  if (index > activeIndex && index <= overIndex) {\n    return {\n      x: -activeNodeRect.width - itemGap,\n      y: 0,\n      ...defaultScale,\n    };\n  }\n\n  if (index < activeIndex && index >= overIndex) {\n    return {\n      x: activeNodeRect.width + itemGap,\n      y: 0,\n      ...defaultScale,\n    };\n  }\n\n  return {\n    x: 0,\n    y: 0,\n    ...defaultScale,\n  };\n};\n\nfunction getItemGap(rects: ClientRect[], index: number, activeIndex: number) {\n  const currentRect: ClientRect | undefined = rects[index];\n  const previousRect: ClientRect | undefined = rects[index - 1];\n  const nextRect: ClientRect | undefined = rects[index + 1];\n\n  if (!currentRect || (!previousRect && !nextRect)) {\n    return 0;\n  }\n\n  if (activeIndex < index) {\n    return previousRect\n      ? currentRect.left - (previousRect.left + previousRect.width)\n      : nextRect.left - (currentRect.left + currentRect.width);\n  }\n\n  return nextRect\n    ? nextRect.left - (currentRect.left + currentRect.width)\n    : currentRect.left - (previousRect.left + previousRect.width);\n}\n", "import {arrayMove} from '../utilities';\nimport type {SortingStrategy} from '../types';\n\nexport const rectSortingStrategy: SortingStrategy = ({\n  rects,\n  activeIndex,\n  overIndex,\n  index,\n}) => {\n  const newRects = arrayMove(rects, overIndex, activeIndex);\n\n  const oldRect = rects[index];\n  const newRect = newRects[index];\n\n  if (!newRect || !oldRect) {\n    return null;\n  }\n\n  return {\n    x: newRect.left - oldRect.left,\n    y: newRect.top - oldRect.top,\n    scaleX: newRect.width / oldRect.width,\n    scaleY: newRect.height / oldRect.height,\n  };\n};\n", "import type {SortingStrategy} from '../types';\n\nexport const rectSwappingStrategy: SortingStrategy = ({\n  activeIndex,\n  index,\n  rects,\n  overIndex,\n}) => {\n  let oldRect;\n  let newRect;\n\n  if (index === activeIndex) {\n    oldRect = rects[index];\n    newRect = rects[overIndex];\n  }\n\n  if (index === overIndex) {\n    oldRect = rects[index];\n    newRect = rects[activeIndex];\n  }\n\n  if (!newRect || !oldRect) {\n    return null;\n  }\n\n  return {\n    x: newRect.left - oldRect.left,\n    y: newRect.top - oldRect.top,\n    scaleX: newRect.width / oldRect.width,\n    scaleY: newRect.height / oldRect.height,\n  };\n};\n", "import type {ClientRect} from '@dnd-kit/core';\nimport type {SortingStrategy} from '../types';\n\n// To-do: We should be calculating scale transformation\nconst defaultScale = {\n  scaleX: 1,\n  scaleY: 1,\n};\n\nexport const verticalListSortingStrategy: SortingStrategy = ({\n  activeIndex,\n  activeNodeRect: fallbackActiveRect,\n  index,\n  rects,\n  overIndex,\n}) => {\n  const activeNodeRect = rects[activeIndex] ?? fallbackActiveRect;\n\n  if (!activeNodeRect) {\n    return null;\n  }\n\n  if (index === activeIndex) {\n    const overIndexRect = rects[overIndex];\n\n    if (!overIndexRect) {\n      return null;\n    }\n\n    return {\n      x: 0,\n      y:\n        activeIndex < overIndex\n          ? overIndexRect.top +\n            overIndexRect.height -\n            (activeNodeRect.top + activeNodeRect.height)\n          : overIndexRect.top - activeNodeRect.top,\n      ...defaultScale,\n    };\n  }\n\n  const itemGap = getItemGap(rects, index, activeIndex);\n\n  if (index > activeIndex && index <= overIndex) {\n    return {\n      x: 0,\n      y: -activeNodeRect.height - itemGap,\n      ...defaultScale,\n    };\n  }\n\n  if (index < activeIndex && index >= overIndex) {\n    return {\n      x: 0,\n      y: activeNodeRect.height + itemGap,\n      ...defaultScale,\n    };\n  }\n\n  return {\n    x: 0,\n    y: 0,\n    ...defaultScale,\n  };\n};\n\nfunction getItemGap(\n  clientRects: ClientRect[],\n  index: number,\n  activeIndex: number\n) {\n  const currentRect: ClientRect | undefined = clientRects[index];\n  const previousRect: ClientRect | undefined = clientRects[index - 1];\n  const nextRect: ClientRect | undefined = clientRects[index + 1];\n\n  if (!currentRect) {\n    return 0;\n  }\n\n  if (activeIndex < index) {\n    return previousRect\n      ? currentRect.top - (previousRect.top + previousRect.height)\n      : nextRect\n      ? nextRect.top - (currentRect.top + currentRect.height)\n      : 0;\n  }\n\n  return nextRect\n    ? nextRect.top - (currentRect.top + currentRect.height)\n    : previousRect\n    ? currentRect.top - (previousRect.top + previousRect.height)\n    : 0;\n}\n", "import React, {useEffect, useMemo, useRef} from 'react';\nimport {useDndContext, ClientRect, UniqueIdentifier} from '@dnd-kit/core';\nimport {useIsomorphicLayoutEffect, useUniqueId} from '@dnd-kit/utilities';\n\nimport type {Disabled, SortingStrategy} from '../types';\nimport {getSortedRects, itemsEqual, normalizeDisabled} from '../utilities';\nimport {rectSortingStrategy} from '../strategies';\n\nexport interface Props {\n  children: React.ReactNode;\n  items: (UniqueIdentifier | {id: UniqueIdentifier})[];\n  strategy?: SortingStrategy;\n  id?: string;\n  disabled?: boolean | Disabled;\n}\n\nconst ID_PREFIX = 'Sortable';\n\ninterface ContextDescriptor {\n  activeIndex: number;\n  containerId: string;\n  disabled: Disabled;\n  disableTransforms: boolean;\n  items: UniqueIdentifier[];\n  overIndex: number;\n  useDragOverlay: boolean;\n  sortedRects: ClientRect[];\n  strategy: SortingStrategy;\n}\n\nexport const Context = React.createContext<ContextDescriptor>({\n  activeIndex: -1,\n  containerId: ID_PREFIX,\n  disableTransforms: false,\n  items: [],\n  overIndex: -1,\n  useDragOverlay: false,\n  sortedRects: [],\n  strategy: rectSortingStrategy,\n  disabled: {\n    draggable: false,\n    droppable: false,\n  },\n});\n\nexport function SortableContext({\n  children,\n  id,\n  items: userDefinedItems,\n  strategy = rectSortingStrategy,\n  disabled: disabledProp = false,\n}: Props) {\n  const {\n    active,\n    dragOverlay,\n    droppableRects,\n    over,\n    measureDroppableContainers,\n  } = useDndContext();\n  const containerId = useUniqueId(ID_PREFIX, id);\n  const useDragOverlay = Boolean(dragOverlay.rect !== null);\n  const items = useMemo<UniqueIdentifier[]>(\n    () =>\n      userDefinedItems.map((item) =>\n        typeof item === 'object' && 'id' in item ? item.id : item\n      ),\n    [userDefinedItems]\n  );\n  const isDragging = active != null;\n  const activeIndex = active ? items.indexOf(active.id) : -1;\n  const overIndex = over ? items.indexOf(over.id) : -1;\n  const previousItemsRef = useRef(items);\n  const itemsHaveChanged = !itemsEqual(items, previousItemsRef.current);\n  const disableTransforms =\n    (overIndex !== -1 && activeIndex === -1) || itemsHaveChanged;\n  const disabled = normalizeDisabled(disabledProp);\n\n  useIsomorphicLayoutEffect(() => {\n    if (itemsHaveChanged && isDragging) {\n      measureDroppableContainers(items);\n    }\n  }, [itemsHaveChanged, items, isDragging, measureDroppableContainers]);\n\n  useEffect(() => {\n    previousItemsRef.current = items;\n  }, [items]);\n\n  const contextValue = useMemo(\n    (): ContextDescriptor => ({\n      activeIndex,\n      containerId,\n      disabled,\n      disableTransforms,\n      items,\n      overIndex,\n      useDragOverlay,\n      sortedRects: getSortedRects(items, droppableRects),\n      strategy,\n    }),\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [\n      activeIndex,\n      containerId,\n      disabled.draggable,\n      disabled.droppable,\n      disableTransforms,\n      items,\n      overIndex,\n      droppableRects,\n      useDragOverlay,\n      strategy,\n    ]\n  );\n\n  return <Context.Provider value={contextValue}>{children}</Context.Provider>;\n}\n", "import {CSS} from '@dnd-kit/utilities';\n\nimport {arrayMove} from '../utilities';\n\nimport type {\n  AnimateLayoutChanges,\n  NewIndexGetter,\n  SortableTransition,\n} from './types';\n\nexport const defaultNewIndexGetter: NewIndexGetter = ({\n  id,\n  items,\n  activeIndex,\n  overIndex,\n}) => arrayMove(items, activeIndex, overIndex).indexOf(id);\n\nexport const defaultAnimateLayoutChanges: AnimateLayoutChanges = ({\n  containerId,\n  isSorting,\n  wasDragging,\n  index,\n  items,\n  newIndex,\n  previousItems,\n  previousContainerId,\n  transition,\n}) => {\n  if (!transition || !wasDragging) {\n    return false;\n  }\n\n  if (previousItems !== items && index === newIndex) {\n    return false;\n  }\n\n  if (isSorting) {\n    return true;\n  }\n\n  return newIndex !== index && containerId === previousContainerId;\n};\n\nexport const defaultTransition: SortableTransition = {\n  duration: 200,\n  easing: 'ease',\n};\n\nexport const transitionProperty = 'transform';\n\nexport const disabledTransition = CSS.Transition.toString({\n  property: transitionProperty,\n  duration: 0,\n  easing: 'linear',\n});\n\nexport const defaultAttributes = {\n  roleDescription: 'sortable',\n};\n", "import {useEffect, useRef, useState} from 'react';\nimport {getClientRect, ClientRect} from '@dnd-kit/core';\nimport {Transform, useIsomorphicLayoutEffect} from '@dnd-kit/utilities';\n\ninterface Arguments {\n  rect: React.MutableRefObject<ClientRect | null>;\n  disabled: boolean;\n  index: number;\n  node: React.MutableRefObject<HTMLElement | null>;\n}\n\n/*\n * When the index of an item changes while sorting,\n * we need to temporarily disable the transforms\n */\nexport function useDerivedTransform({disabled, index, node, rect}: Arguments) {\n  const [derivedTransform, setDerivedtransform] = useState<Transform | null>(\n    null\n  );\n  const previousIndex = useRef(index);\n\n  useIsomorphicLayoutEffect(() => {\n    if (!disabled && index !== previousIndex.current && node.current) {\n      const initial = rect.current;\n\n      if (initial) {\n        const current = getClientRect(node.current, {\n          ignoreTransform: true,\n        });\n\n        const delta = {\n          x: initial.left - current.left,\n          y: initial.top - current.top,\n          scaleX: initial.width / current.width,\n          scaleY: initial.height / current.height,\n        };\n\n        if (delta.x || delta.y) {\n          setDerivedtransform(delta);\n        }\n      }\n    }\n\n    if (index !== previousIndex.current) {\n      previousIndex.current = index;\n    }\n  }, [disabled, index, node, rect]);\n\n  useEffect(() => {\n    if (derivedTransform) {\n      setDerivedtransform(null);\n    }\n  }, [derivedTransform]);\n\n  return derivedTransform;\n}\n", "import {useContext, useEffect, useMemo, useRef} from 'react';\nimport {\n  useDraggable,\n  useDroppable,\n  UseDraggableArguments,\n  UseDroppableArguments,\n} from '@dnd-kit/core';\nimport type {Data} from '@dnd-kit/core';\nimport {CSS, isKeyboardEvent, useCombinedRefs} from '@dnd-kit/utilities';\n\nimport {Context} from '../components';\nimport type {Disabled, SortableData, SortingStrategy} from '../types';\nimport {isValidIndex} from '../utilities';\nimport {\n  defaultAnimateLayoutChanges,\n  defaultAttributes,\n  defaultNewIndexGetter,\n  defaultTransition,\n  disabledTransition,\n  transitionProperty,\n} from './defaults';\nimport type {\n  AnimateLayoutChanges,\n  NewIndexGetter,\n  SortableTransition,\n} from './types';\nimport {useDerivedTransform} from './utilities';\n\nexport interface Arguments\n  extends Omit<UseDraggableArguments, 'disabled'>,\n    Pick<UseDroppableArguments, 'resizeObserverConfig'> {\n  animateLayoutChanges?: AnimateLayoutChanges;\n  disabled?: boolean | Disabled;\n  getNewIndex?: NewIndexGetter;\n  strategy?: SortingStrategy;\n  transition?: SortableTransition | null;\n}\n\nexport function useSortable({\n  animateLayoutChanges = defaultAnimateLayoutChanges,\n  attributes: userDefinedAttributes,\n  disabled: localDisabled,\n  data: customData,\n  getNewIndex = defaultNewIndexGetter,\n  id,\n  strategy: localStrategy,\n  resizeObserverConfig,\n  transition = defaultTransition,\n}: Arguments) {\n  const {\n    items,\n    containerId,\n    activeIndex,\n    disabled: globalDisabled,\n    disableTransforms,\n    sortedRects,\n    overIndex,\n    useDragOverlay,\n    strategy: globalStrategy,\n  } = useContext(Context);\n  const disabled: Disabled = normalizeLocalDisabled(\n    localDisabled,\n    globalDisabled\n  );\n  const index = items.indexOf(id);\n  const data = useMemo<SortableData & Data>(\n    () => ({sortable: {containerId, index, items}, ...customData}),\n    [containerId, customData, index, items]\n  );\n  const itemsAfterCurrentSortable = useMemo(\n    () => items.slice(items.indexOf(id)),\n    [items, id]\n  );\n  const {\n    rect,\n    node,\n    isOver,\n    setNodeRef: setDroppableNodeRef,\n  } = useDroppable({\n    id,\n    data,\n    disabled: disabled.droppable,\n    resizeObserverConfig: {\n      updateMeasurementsFor: itemsAfterCurrentSortable,\n      ...resizeObserverConfig,\n    },\n  });\n  const {\n    active,\n    activatorEvent,\n    activeNodeRect,\n    attributes,\n    setNodeRef: setDraggableNodeRef,\n    listeners,\n    isDragging,\n    over,\n    setActivatorNodeRef,\n    transform,\n  } = useDraggable({\n    id,\n    data,\n    attributes: {\n      ...defaultAttributes,\n      ...userDefinedAttributes,\n    },\n    disabled: disabled.draggable,\n  });\n  const setNodeRef = useCombinedRefs(setDroppableNodeRef, setDraggableNodeRef);\n  const isSorting = Boolean(active);\n  const displaceItem =\n    isSorting &&\n    !disableTransforms &&\n    isValidIndex(activeIndex) &&\n    isValidIndex(overIndex);\n  const shouldDisplaceDragSource = !useDragOverlay && isDragging;\n  const dragSourceDisplacement =\n    shouldDisplaceDragSource && displaceItem ? transform : null;\n  const strategy = localStrategy ?? globalStrategy;\n  const finalTransform = displaceItem\n    ? dragSourceDisplacement ??\n      strategy({\n        rects: sortedRects,\n        activeNodeRect,\n        activeIndex,\n        overIndex,\n        index,\n      })\n    : null;\n  const newIndex =\n    isValidIndex(activeIndex) && isValidIndex(overIndex)\n      ? getNewIndex({id, items, activeIndex, overIndex})\n      : index;\n  const activeId = active?.id;\n  const previous = useRef({\n    activeId,\n    items,\n    newIndex,\n    containerId,\n  });\n  const itemsHaveChanged = items !== previous.current.items;\n  const shouldAnimateLayoutChanges = animateLayoutChanges({\n    active,\n    containerId,\n    isDragging,\n    isSorting,\n    id,\n    index,\n    items,\n    newIndex: previous.current.newIndex,\n    previousItems: previous.current.items,\n    previousContainerId: previous.current.containerId,\n    transition,\n    wasDragging: previous.current.activeId != null,\n  });\n\n  const derivedTransform = useDerivedTransform({\n    disabled: !shouldAnimateLayoutChanges,\n    index,\n    node,\n    rect,\n  });\n\n  useEffect(() => {\n    if (isSorting && previous.current.newIndex !== newIndex) {\n      previous.current.newIndex = newIndex;\n    }\n\n    if (containerId !== previous.current.containerId) {\n      previous.current.containerId = containerId;\n    }\n\n    if (items !== previous.current.items) {\n      previous.current.items = items;\n    }\n  }, [isSorting, newIndex, containerId, items]);\n\n  useEffect(() => {\n    if (activeId === previous.current.activeId) {\n      return;\n    }\n\n    if (activeId && !previous.current.activeId) {\n      previous.current.activeId = activeId;\n      return;\n    }\n\n    const timeoutId = setTimeout(() => {\n      previous.current.activeId = activeId;\n    }, 50);\n\n    return () => clearTimeout(timeoutId);\n  }, [activeId]);\n\n  return {\n    active,\n    activeIndex,\n    attributes,\n    data,\n    rect,\n    index,\n    newIndex,\n    items,\n    isOver,\n    isSorting,\n    isDragging,\n    listeners,\n    node,\n    overIndex,\n    over,\n    setNodeRef,\n    setActivatorNodeRef,\n    setDroppableNodeRef,\n    setDraggableNodeRef,\n    transform: derivedTransform ?? finalTransform,\n    transition: getTransition(),\n  };\n\n  function getTransition() {\n    if (\n      // Temporarily disable transitions for a single frame to set up derived transforms\n      derivedTransform ||\n      // Or to prevent items jumping to back to their \"new\" position when items change\n      (itemsHaveChanged && previous.current.newIndex === index)\n    ) {\n      return disabledTransition;\n    }\n\n    if (\n      (shouldDisplaceDragSource && !isKeyboardEvent(activatorEvent)) ||\n      !transition\n    ) {\n      return undefined;\n    }\n\n    if (isSorting || shouldAnimateLayoutChanges) {\n      return CSS.Transition.toString({\n        ...transition,\n        property: transitionProperty,\n      });\n    }\n\n    return undefined;\n  }\n}\n\nfunction normalizeLocalDisabled(\n  localDisabled: Arguments['disabled'],\n  globalDisabled: Disabled\n) {\n  if (typeof localDisabled === 'boolean') {\n    return {\n      draggable: localDisabled,\n      // Backwards compatibility\n      droppable: false,\n    };\n  }\n\n  return {\n    draggable: localDisabled?.draggable ?? globalDisabled.draggable,\n    droppable: localDisabled?.droppable ?? globalDisabled.droppable,\n  };\n}\n", "import type {\n  Active,\n  Data,\n  DroppableContainer,\n  DraggableNode,\n  Over,\n} from '@dnd-kit/core';\n\nimport type {SortableData} from './data';\n\nexport function hasSortableData<\n  T extends Active | Over | DraggableNode | DroppableContainer\n>(\n  entry: T | null | undefined\n): entry is T & {data: {current: Data<SortableData>}} {\n  if (!entry) {\n    return false;\n  }\n\n  const data = entry.data.current;\n\n  if (\n    data &&\n    'sortable' in data &&\n    typeof data.sortable === 'object' &&\n    'containerId' in data.sortable &&\n    'items' in data.sortable &&\n    'index' in data.sortable\n  ) {\n    return true;\n  }\n\n  return false;\n}\n", "import {\n  closestCorners,\n  getScrollableAncestors,\n  getFirstCollision,\n  KeyboardCode,\n  DroppableContainer,\n  KeyboardCoordinateGetter,\n} from '@dnd-kit/core';\nimport {subtract} from '@dnd-kit/utilities';\n\nimport {hasSortableData} from '../../types';\n\nconst directions: string[] = [\n  KeyboardCode.Down,\n  KeyboardCode.Right,\n  KeyboardCode.Up,\n  KeyboardCode.Left,\n];\n\nexport const sortableKeyboardCoordinates: KeyboardCoordinateGetter = (\n  event,\n  {\n    context: {\n      active,\n      collisionRect,\n      droppableRects,\n      droppableContainers,\n      over,\n      scrollableAncestors,\n    },\n  }\n) => {\n  if (directions.includes(event.code)) {\n    event.preventDefault();\n\n    if (!active || !collisionRect) {\n      return;\n    }\n\n    const filteredContainers: DroppableContainer[] = [];\n\n    droppableContainers.getEnabled().forEach((entry) => {\n      if (!entry || entry?.disabled) {\n        return;\n      }\n\n      const rect = droppableRects.get(entry.id);\n\n      if (!rect) {\n        return;\n      }\n\n      switch (event.code) {\n        case KeyboardCode.Down:\n          if (collisionRect.top < rect.top) {\n            filteredContainers.push(entry);\n          }\n          break;\n        case KeyboardCode.Up:\n          if (collisionRect.top > rect.top) {\n            filteredContainers.push(entry);\n          }\n          break;\n        case KeyboardCode.Left:\n          if (collisionRect.left > rect.left) {\n            filteredContainers.push(entry);\n          }\n          break;\n        case KeyboardCode.Right:\n          if (collisionRect.left < rect.left) {\n            filteredContainers.push(entry);\n          }\n          break;\n      }\n    });\n\n    const collisions = closestCorners({\n      active,\n      collisionRect: collisionRect,\n      droppableRects,\n      droppableContainers: filteredContainers,\n      pointerCoordinates: null,\n    });\n    let closestId = getFirstCollision(collisions, 'id');\n\n    if (closestId === over?.id && collisions.length > 1) {\n      closestId = collisions[1].id;\n    }\n\n    if (closestId != null) {\n      const activeDroppable = droppableContainers.get(active.id);\n      const newDroppable = droppableContainers.get(closestId);\n      const newRect = newDroppable ? droppableRects.get(newDroppable.id) : null;\n      const newNode = newDroppable?.node.current;\n\n      if (newNode && newRect && activeDroppable && newDroppable) {\n        const newScrollAncestors = getScrollableAncestors(newNode);\n        const hasDifferentScrollAncestors = newScrollAncestors.some(\n          (element, index) => scrollableAncestors[index] !== element\n        );\n        const hasSameContainer = isSameContainer(activeDroppable, newDroppable);\n        const isAfterActive = isAfter(activeDroppable, newDroppable);\n        const offset =\n          hasDifferentScrollAncestors || !hasSameContainer\n            ? {\n                x: 0,\n                y: 0,\n              }\n            : {\n                x: isAfterActive ? collisionRect.width - newRect.width : 0,\n                y: isAfterActive ? collisionRect.height - newRect.height : 0,\n              };\n        const rectCoordinates = {\n          x: newRect.left,\n          y: newRect.top,\n        };\n\n        const newCoordinates =\n          offset.x && offset.y\n            ? rectCoordinates\n            : subtract(rectCoordinates, offset);\n\n        return newCoordinates;\n      }\n    }\n  }\n\n  return undefined;\n};\n\nfunction isSameContainer(a: DroppableContainer, b: DroppableContainer) {\n  if (!hasSortableData(a) || !hasSortableData(b)) {\n    return false;\n  }\n\n  return (\n    a.data.current.sortable.containerId === b.data.current.sortable.containerId\n  );\n}\n\nfunction isAfter(a: DroppableContainer, b: DroppableContainer) {\n  if (!hasSortableData(a) || !hasSortableData(b)) {\n    return false;\n  }\n\n  if (!isSameContainer(a, b)) {\n    return false;\n  }\n\n  return a.data.current.sortable.index < b.data.current.sortable.index;\n}\n"], "mappings": ";;;;;;;;;;;;SAEgBA,kBAAAA;oCACXC,OAAAA,IAAAA,MAAAA,IAAAA,GAAAA,OAAAA,GAAAA,OAAAA,MAAAA,QAAAA;AAAAA,SAAAA,IAAAA,IAAAA,UAAAA,IAAAA;;AAEH,aAAOC;IACL,MAAOC,UAAD;AACJF,WAAKG,QAASC,SAAQA,IAAIF,IAAD,CAAzB;;;IAGFF;EALY;AAOf;ACXD,IAAaK,YACX,OAAOC,WAAW,eAClB,OAAOA,OAAOC,aAAa,eAC3B,OAAOD,OAAOC,SAASC,kBAAkB;SCJ3BC,SAASC,SAAAA;AACvB,QAAMC,gBAAgBC,OAAOC,UAAUC,SAASC,KAAKL,OAA/B;AACtB,SACEC,kBAAkB;EAElBA,kBAAkB;AAErB;SCPeK,OAAOd,MAAAA;AACrB,SAAO,cAAcA;AACtB;SCCee,UAAUC,QAAAA;;AACxB,MAAI,CAACA,QAAQ;AACX,WAAOZ;;AAGT,MAAIG,SAASS,MAAD,GAAU;AACpB,WAAOA;;AAGT,MAAI,CAACF,OAAOE,MAAD,GAAU;AACnB,WAAOZ;;AAGT,UAAA,yBAAA,yBAAOY,OAAOC,kBAAd,OAAA,SAAO,uBAAsBC,gBAA7B,OAAA,wBAA4Cd;AAC7C;SCfee,WAAWnB,MAAAA;AACzB,QAAM;IAACoB;MAAYL,UAAUf,IAAD;AAE5B,SAAOA,gBAAgBoB;AACxB;SCFeC,cAAcrB,MAAAA;AAC5B,MAAIO,SAASP,IAAD,GAAQ;AAClB,WAAO;;AAGT,SAAOA,gBAAgBe,UAAUf,IAAD,EAAOsB;AACxC;SCReC,aAAavB,MAAAA;AAC3B,SAAOA,gBAAgBe,UAAUf,IAAD,EAAOwB;AACxC;SCIeC,iBAAiBT,QAAAA;AAC/B,MAAI,CAACA,QAAQ;AACX,WAAOX;;AAGT,MAAIE,SAASS,MAAD,GAAU;AACpB,WAAOA,OAAOX;;AAGhB,MAAI,CAACS,OAAOE,MAAD,GAAU;AACnB,WAAOX;;AAGT,MAAIc,WAAWH,MAAD,GAAU;AACtB,WAAOA;;AAGT,MAAIK,cAAcL,MAAD,KAAYO,aAAaP,MAAD,GAAU;AACjD,WAAOA,OAAOC;;AAGhB,SAAOZ;AACR;ACtBD,IAAaqB,4BAA4BvB,YACrCwB,+BACAC;SCNYC,SAA6BC,SAAAA;AAC3C,QAAMC,iBAAaC,qBAAsBF,OAAhB;AAEzBJ,4BAA0B,MAAA;AACxBK,eAAWE,UAAUH;GADE;AAIzB,aAAOI,0BAAY,WAAA;sCAAaC,OAAAA,IAAAA,MAAAA,IAAAA,GAAAA,OAAAA,GAAAA,OAAAA,MAAAA,QAAAA;AAAAA,WAAAA,IAAAA,IAAAA,UAAAA,IAAAA;;AAC9B,WAAOJ,WAAWE,WAAlB,OAAA,SAAOF,WAAWE,QAAU,GAAGE,IAAxB;KACN,CAAA,CAFe;AAGnB;SCZeC,cAAAA;AACd,QAAMC,kBAAcL,qBAAsB,IAAhB;AAE1B,QAAMM,UAAMJ,0BAAY,CAACK,UAAoBC,aAArB;AACtBH,gBAAYJ,UAAUQ,YAAYF,UAAUC,QAAX;KAChC,CAAA,CAFoB;AAIvB,QAAME,YAAQR,0BAAY,MAAA;AACxB,QAAIG,YAAYJ,YAAY,MAAM;AAChCU,oBAAcN,YAAYJ,OAAb;AACbI,kBAAYJ,UAAU;;KAEvB,CAAA,CALsB;AAOzB,SAAO,CAACK,KAAKI,KAAN;AACR;SCZeE,eACdC,OACAC,cAAAA;MAAAA,iBAAAA,QAAAA;AAAAA,mBAA+B,CAACD,KAAD;;AAE/B,QAAME,eAAWf,qBAAUa,KAAJ;AAEvBnB,4BAA0B,MAAA;AACxB,QAAIqB,SAASd,YAAYY,OAAO;AAC9BE,eAASd,UAAUY;;KAEpBC,YAJsB;AAMzB,SAAOC;AACR;SChBeC,YACdC,UACAH,cAAAA;AAEA,QAAMC,eAAWf,qBAAM;AAEvB,aAAOjC;IACL,MAAA;AACE,YAAMmD,WAAWD,SAASF,SAASd,OAAV;AACzBc,eAASd,UAAUiB;AAEnB,aAAOA;;;IAGT,CAAC,GAAGJ,YAAJ;EARY;AAUf;SCdeK,WACdC,UAAAA;AAKA,QAAMC,kBAAkBxB,SAASuB,QAAD;AAChC,QAAMpD,WAAOgC,qBAA2B,IAArB;AACnB,QAAMsB,iBAAapB;IAChB1B,aAAD;AACE,UAAIA,YAAYR,KAAKiC,SAAS;AAC5BoB,2BAAe,OAAf,SAAAA,gBAAkB7C,SAASR,KAAKiC,OAAjB;;AAGjBjC,WAAKiC,UAAUzB;;;IAGjB,CAAA;EAT4B;AAY9B,SAAO,CAACR,MAAMsD,UAAP;AACR;SCvBeC,YAAeV,OAAAA;AAC7B,QAAM3C,UAAM8B,qBAAM;AAElBJ,8BAAU,MAAA;AACR1B,QAAI+B,UAAUY;KACb,CAACA,KAAD,CAFM;AAIT,SAAO3C,IAAI+B;AACZ;ACRD,IAAIuB,MAA8B,CAAA;AAElC,SAAgBC,YAAYC,QAAgBb,OAAAA;AAC1C,aAAO9C,sBAAQ,MAAA;AACb,QAAI8C,OAAO;AACT,aAAOA;;AAGT,UAAMc,KAAKH,IAAIE,MAAD,KAAY,OAAO,IAAIF,IAAIE,MAAD,IAAW;AACnDF,QAAIE,MAAD,IAAWC;AAEd,WAAUD,SAAV,MAAoBC;KACnB,CAACD,QAAQb,KAAT,CATW;AAUf;ACfD,SAASe,mBAAmBC,UAA5B;AACE,SAAO,SACLC,QADK;sCAEFC,cAAAA,IAAAA,MAAAA,OAAAA,IAAAA,OAAAA,IAAAA,CAAAA,GAAAA,OAAAA,GAAAA,OAAAA,MAAAA,QAAAA;AAAAA,kBAAAA,OAAAA,CAAAA,IAAAA,UAAAA,IAAAA;;AAEH,WAAOA,YAAYC,OACjB,CAACC,aAAaC,eAAd;AACE,YAAMC,UAAUzD,OAAOyD,QAAQD,UAAf;AAEhB,iBAAW,CAACE,MAAKC,eAAN,KAA0BF,SAAS;AAC5C,cAAMtB,QAAQoB,YAAYG,IAAD;AAEzB,YAAIvB,SAAS,MAAM;AACjBoB,sBAAYG,IAAD,IAASvB,QAAQgB,WAAWQ;;;AAI3C,aAAOJ;OAET;MACE,GAAGH;KAfA;;AAmBV;AAED,IAAaQ,MAAMV,mBAAmB,CAAD;AACrC,IAAaW,WAAWX,mBAAmB,EAAD;SC3B1BY,+BACdC,OAAAA;AAEA,SAAO,aAAaA,SAAS,aAAaA;AAC3C;SCFeC,gBACdD,OAAAA;AAEA,MAAI,CAACA,OAAO;AACV,WAAO;;AAGT,QAAM;IAACE;MAAiB5D,UAAU0D,MAAMzD,MAAP;AAEjC,SAAO2D,iBAAiBF,iBAAiBE;AAC1C;SCVeC,aACdH,OAAAA;AAEA,MAAI,CAACA,OAAO;AACV,WAAO;;AAGT,QAAM;IAACI;MAAc9D,UAAU0D,MAAMzD,MAAP;AAE9B,SAAO6D,cAAcJ,iBAAiBI;AACvC;ACND,SAAgBC,oBAAoBL,OAAAA;AAClC,MAAIG,aAAaH,KAAD,GAAS;AACvB,QAAIA,MAAMM,WAAWN,MAAMM,QAAQC,QAAQ;AACzC,YAAM;QAACC,SAASC;QAAGC,SAASC;UAAKX,MAAMM,QAAQ,CAAd;AAEjC,aAAO;QACLG;QACAE;;eAEOX,MAAMY,kBAAkBZ,MAAMY,eAAeL,QAAQ;AAC9D,YAAM;QAACC,SAASC;QAAGC,SAASC;UAAKX,MAAMY,eAAe,CAArB;AAEjC,aAAO;QACLH;QACAE;;;;AAKN,MAAIZ,+BAA+BC,KAAD,GAAS;AACzC,WAAO;MACLS,GAAGT,MAAMQ;MACTG,GAAGX,MAAMU;;;AAIb,SAAO;AACR;ICpBYG,MAAM5E,OAAO6E,OAAO;EAC/BC,WAAW;IACT5E,SAAS6E,WAAD;AACN,UAAI,CAACA,WAAW;AACd;;AAGF,YAAM;QAACP;QAAGE;UAAKK;AAEf,aAAA,kBAAsBP,IAAIQ,KAAKC,MAAMT,CAAX,IAAgB,KAA1C,UACEE,IAAIM,KAAKC,MAAMP,CAAX,IAAgB,KADtB;;;EAKJQ,OAAO;IACLhF,SAAS6E,WAAD;AACN,UAAI,CAACA,WAAW;AACd;;AAGF,YAAM;QAACI;QAAQC;UAAUL;AAEzB,aAAA,YAAiBI,SAAjB,cAAmCC,SAAnC;;;EAGJC,WAAW;IACTnF,SAAS6E,WAAD;AACN,UAAI,CAACA,WAAW;AACd;;AAGF,aAAO,CACLH,IAAIE,UAAU5E,SAAS6E,SAAvB,GACAH,IAAIM,MAAMhF,SAAS6E,SAAnB,CAFK,EAGLO,KAAK,GAHA;;;EAMXC,YAAY;IACVrF,SAAQ,MAAA;UAAC;QAACsF;QAAU1D;QAAU2D;;AAC5B,aAAUD,WAAV,MAAsB1D,WAAtB,QAAoC2D;;;AAvCT,CAAd;ACbnB,IAAMC,WACJ;AAEF,SAAgBC,uBACd7F,SAAAA;AAEA,MAAIA,QAAQ8F,QAAQF,QAAhB,GAA2B;AAC7B,WAAO5F;;AAGT,SAAOA,QAAQ+F,cAAcH,QAAtB;AACR;;;;;;;;ACJD,IAAMI,eAAoC;EACxCC,SAAS;AAD+B;SAI1BC,WAAAA,MAAAA;MAAW;IAACC;IAAIC;;AAC9B,SACEC,cAAAA,QAAAA,cAAA,OAAA;IAAKF;IAAQG,OAAON;KACjBI,KADH;AAIH;SCTeG,WAAAA,MAAAA;MAAW;IAACJ;IAAIK;IAAcC,eAAe;;AAE3D,QAAMC,iBAAsC;IAC1CC,UAAU;IACVC,KAAK;IACLC,MAAM;IACNC,OAAO;IACPC,QAAQ;IACRC,QAAQ;IACRC,QAAQ;IACRC,SAAS;IACTC,UAAU;IACVC,MAAM;IACNC,UAAU;IACVC,YAAY;;AAGd,SACEjB,cAAAA,QAAAA,cAAA,OAAA;IACEF;IACAG,OAAOI;IACPa,MAAK;iBACMd;;KAGVD,YAPH;AAUH;SClCegB,kBAAAA;AACd,QAAM,CAAChB,cAAciB,eAAf,QAAkCC,wBAAS,EAAD;AAChD,QAAMC,eAAWC,2BAAaxB,WAAD;AAC3B,QAAIA,SAAS,MAAM;AACjBqB,sBAAgBrB,KAAD;;KAEhB,CAAA,CAJyB;AAM5B,SAAO;IAACuB;IAAUnB;;AACnB;;;ACPM,IAAMqB,wBAAoBC,6BAAuC,IAA1B;SCC9BC,cAAcC,UAAAA;AAC5B,QAAMC,uBAAmBC,0BAAWL,iBAAD;AAEnCM,+BAAU,MAAA;AACR,QAAI,CAACF,kBAAkB;AACrB,YAAM,IAAIG,MACR,8DADI;;AAKR,UAAMC,cAAcJ,iBAAiBD,QAAD;AAEpC,WAAOK;KACN,CAACL,UAAUC,gBAAX,CAVM;AAWV;SCfeK,wBAAAA;AACd,QAAM,CAACC,SAAD,QAAcC,wBAAS,MAAM,oBAAIC,IAAJ,CAAP;AAE5B,QAAMR,uBAAmBS,2BACtBV,cAAD;AACEO,cAAUI,IAAIX,QAAd;AACA,WAAO,MAAMO,UAAUK,OAAOZ,QAAjB;KAEf,CAACO,SAAD,CALkC;AAQpC,QAAMM,eAAWH,2BACf,UAAA;QAAC;MAACI;MAAMC;;AACNR,cAAUS,QAAShB,cAAD;AAAA,UAAA;AAAA,cAAA,iBAAcA,SAASc,IAAD,MAAtB,OAAA,SAAc,eAAA,KAAAd,UAAiBe,KAAT;KAAxC;KAEF,CAACR,SAAD,CAJ0B;AAO5B,SAAO,CAACM,UAAUZ,gBAAX;AACR;ICrBYgB,kCAA4D;EACvEC,WAAS;AAD8D;AAQzE,IAAaC,uBAAsC;EACjDC,YAAW,MAAA;QAAC;MAACC;;AACX,WAAA,8BAAmCA,OAAOC,KAA1C;;EAEFC,WAAU,OAAA;QAAC;MAACF;MAAQG;;AAClB,QAAIA,MAAM;AACR,aAAA,oBAAyBH,OAAOC,KAAhC,oCAAoEE,KAAKF,KAAzE;;AAGF,WAAA,oBAAyBD,OAAOC,KAAhC;;EAEFG,UAAS,OAAA;QAAC;MAACJ;MAAQG;;AACjB,QAAIA,MAAM;AACR,aAAA,oBAAyBH,OAAOC,KAAhC,sCAAsEE,KAAKF;;AAG7E,WAAA,oBAAyBD,OAAOC,KAAhC;;EAEFI,aAAY,OAAA;QAAC;MAACL;;AACZ,WAAA,4CAAiDA,OAAOC,KAAxD;;AAnB+C;SCUnCK,cAAAA,MAAAA;MAAc;IAC5BC,gBAAgBT;IAChBU;IACAC;IACAC,2BAA2Bd;;AAE3B,QAAM;IAACe;IAAUC;MAAgBC,gBAAe;AAChD,QAAMC,eAAeC,YAAW,eAAA;AAChC,QAAM,CAACC,SAASC,UAAV,QAAwB9B,wBAAS,KAAD;AAEtCL,+BAAU,MAAA;AACRmC,eAAW,IAAD;KACT,CAAA,CAFM;AAITvC,oBACEwC,uBACE,OAAO;IACLnB,YAAW,OAAA;UAAC;QAACC;;AACXW,eAASJ,cAAcR,YAAY;QAACC;OAA3B,CAAD;;IAEVmB,WAAU,OAAA;UAAC;QAACnB;QAAQG;;AAClB,UAAII,cAAcY,YAAY;AAC5BR,iBAASJ,cAAcY,WAAW;UAACnB;UAAQG;SAAlC,CAAD;;;IAGZD,WAAU,OAAA;UAAC;QAACF;QAAQG;;AAClBQ,eAASJ,cAAcL,WAAW;QAACF;QAAQG;OAAlC,CAAD;;IAEVC,UAAS,OAAA;UAAC;QAACJ;QAAQG;;AACjBQ,eAASJ,cAAcH,UAAU;QAACJ;QAAQG;OAAjC,CAAD;;IAEVE,aAAY,OAAA;UAAC;QAACL;QAAQG;;AACpBQ,eAASJ,cAAcF,aAAa;QAACL;QAAQG;OAApC,CAAD;;MAGZ,CAACQ,UAAUJ,aAAX,CApBK,CADI;AAyBb,MAAI,CAACS,SAAS;AACZ,WAAO;;AAGT,QAAMI,SACJC,cAAAA,QAAAA,cAAA,cAAAA,QAAA,UAAA,MACEA,cAAAA,QAAAA,cAACC,YAAD;IACErB,IAAIQ;IACJc,OAAOb,yBAAyBb;GAFlC,GAIAwB,cAAAA,QAAAA,cAACG,YAAD;IAAYvB,IAAIa;IAAcF;GAA9B,CALF;AASF,SAAOJ,gBAAYiB,+BAAaL,QAAQZ,SAAT,IAAsBY;AACtD;ACvED,IAAYM;CAAZ,SAAYA,SAAAA;AACVA,EAAAA,QAAAA,WAAAA,IAAA;AACAA,EAAAA,QAAAA,UAAAA,IAAA;AACAA,EAAAA,QAAAA,SAAAA,IAAA;AACAA,EAAAA,QAAAA,YAAAA,IAAA;AACAA,EAAAA,QAAAA,UAAAA,IAAA;AACAA,EAAAA,QAAAA,mBAAAA,IAAA;AACAA,EAAAA,QAAAA,sBAAAA,IAAA;AACAA,EAAAA,QAAAA,qBAAAA,IAAA;AACD,GATWA,WAAAA,SAAM,CAAA,EAAlB;SCHgBC,OAAAA;AAAAA;SCIAC,UACdC,QACAC,SAAAA;AAEA,aAAOZ;IACL,OAAO;MACLW;MACAC,SAASA,WAAF,OAAEA,UAAY,CAAA;;;IAGvB,CAACD,QAAQC,OAAT;EANY;AAQf;SCZeC,aAAAA;oCACXC,UAAAA,IAAAA,MAAAA,IAAAA,GAAAA,OAAAA,GAAAA,OAAAA,MAAAA,QAAAA;AAAAA,YAAAA,IAAAA,IAAAA,UAAAA,IAAAA;;AAEH,aAAOd;IACL,MACE,CAAC,GAAGc,OAAJ,EAAaC,OACVJ,YAA4CA,UAAU,IADzD;;IAIF,CAAC,GAAGG,OAAJ;EANY;AAQf;ICbYE,qBAAkCC,OAAOC,OAAO;EAC3DC,GAAG;EACHC,GAAG;AAFwD,CAAd;ACG/C,SAAgBC,gBAAgBC,IAAiBC,IAAAA;AAC/C,SAAOC,KAAKC,KAAKD,KAAKE,IAAIJ,GAAGH,IAAII,GAAGJ,GAAG,CAAtB,IAA2BK,KAAKE,IAAIJ,GAAGF,IAAIG,GAAGH,GAAG,CAAtB,CAArC;AACR;SCJeO,2BACdnD,OACAoD,MAAAA;AAEA,QAAMC,mBAAmBC,oBAAoBtD,KAAD;AAE5C,MAAI,CAACqD,kBAAkB;AACrB,WAAO;;AAGT,QAAME,kBAAkB;IACtBZ,IAAKU,iBAAiBV,IAAIS,KAAKI,QAAQJ,KAAKK,QAAS;IACrDb,IAAKS,iBAAiBT,IAAIQ,KAAKM,OAAON,KAAKO,SAAU;;AAGvD,SAAUJ,gBAAgBZ,IAA1B,OAAgCY,gBAAgBX,IAAhD;AACD;ACXD,SAAgBgB,kBAAAA,MAAAA,OAAAA;MACd;IAACC,MAAM;MAAChC,OAAOiC;;;MACf;IAACD,MAAM;MAAChC,OAAOkC;;;AAEf,SAAOD,IAAIC;AACZ;AAKD,SAAgBC,mBAAAA,OAAAA,OAAAA;MACd;IAACH,MAAM;MAAChC,OAAOiC;;;MACf;IAACD,MAAM;MAAChC,OAAOkC;;;AAEf,SAAOA,IAAID;AACZ;AAMD,SAAgBG,mBAAAA,OAAAA;MAAmB;IAACT;IAAME;IAAKC;IAAQF;;AACrD,SAAO,CACL;IACEd,GAAGa;IACHZ,GAAGc;KAEL;IACEf,GAAGa,OAAOC;IACVb,GAAGc;KAEL;IACEf,GAAGa;IACHZ,GAAGc,MAAMC;KAEX;IACEhB,GAAGa,OAAOC;IACVb,GAAGc,MAAMC;GAfN;AAkBR;AAaD,SAAgBO,kBACdC,YACAC,UAAAA;AAEA,MAAI,CAACD,cAAcA,WAAWE,WAAW,GAAG;AAC1C,WAAO;;AAGT,QAAM,CAACC,cAAD,IAAmBH;AAEzB,SAAOC,WAAWE,eAAeF,QAAD,IAAaE;AAC9C;AC/DD,SAASC,kBACPnB,MACAI,MACAE,KAHF;MAEEF,SAAAA,QAAAA;AAAAA,WAAOJ,KAAKI;;MACZE,QAAAA,QAAAA;AAAAA,UAAMN,KAAKM;;AAEX,SAAO;IACLf,GAAGa,OAAOJ,KAAKK,QAAQ;IACvBb,GAAGc,MAAMN,KAAKO,SAAS;;AAE1B;AAMD,IAAaa,gBAAoC,UAAA;MAAC;IAChDC;IACAC;IACAC;;AAEA,QAAMC,aAAaL,kBACjBE,eACAA,cAAcjB,MACdiB,cAAcf,GAHoB;AAKpC,QAAMS,aAAoC,CAAA;AAE1C,aAAWU,sBAAsBF,qBAAqB;AACpD,UAAM;MAACpE;QAAMsE;AACb,UAAMzB,OAAOsB,eAAeI,IAAIvE,EAAnB;AAEb,QAAI6C,MAAM;AACR,YAAM2B,cAAclC,gBAAgB0B,kBAAkBnB,IAAD,GAAQwB,UAA1B;AAEnCT,iBAAWa,KAAK;QAACzE;QAAIsD,MAAM;UAACgB;UAAoBhD,OAAOkD;;OAAvD;;;AAIJ,SAAOZ,WAAWc,KAAKrB,iBAAhB;AACR;ACvCD,IAAasB,iBAAqC,UAAA;MAAC;IACjDT;IACAC;IACAC;;AAEA,QAAMQ,UAAUlB,mBAAmBQ,aAAD;AAClC,QAAMN,aAAoC,CAAA;AAE1C,aAAWU,sBAAsBF,qBAAqB;AACpD,UAAM;MAACpE;QAAMsE;AACb,UAAMzB,OAAOsB,eAAeI,IAAIvE,EAAnB;AAEb,QAAI6C,MAAM;AACR,YAAMgC,cAAcnB,mBAAmBb,IAAD;AACtC,YAAMiC,YAAYF,QAAQG,OAAO,CAACC,aAAaC,QAAQC,UAAtB;AAC/B,eAAOF,cAAc1C,gBAAgBuC,YAAYK,KAAD,GAASD,MAArB;SACnC,CAFe;AAGlB,YAAME,oBAAoBC,QAAQN,YAAY,GAAGO,QAAQ,CAAxB,CAAD;AAEhCzB,iBAAWa,KAAK;QACdzE;QACAsD,MAAM;UAACgB;UAAoBhD,OAAO6D;;OAFpC;;;AAOJ,SAAOvB,WAAWc,KAAKrB,iBAAhB;AACR;AC5BD,SAAgBiC,qBACdC,OACAC,QAAAA;AAEA,QAAMrC,MAAMV,KAAKgD,IAAID,OAAOrC,KAAKoC,MAAMpC,GAA3B;AACZ,QAAMF,OAAOR,KAAKgD,IAAID,OAAOvC,MAAMsC,MAAMtC,IAA5B;AACb,QAAMyC,QAAQjD,KAAKkD,IAAIH,OAAOvC,OAAOuC,OAAOtC,OAAOqC,MAAMtC,OAAOsC,MAAMrC,KAAxD;AACd,QAAM0C,SAASnD,KAAKkD,IAAIH,OAAOrC,MAAMqC,OAAOpC,QAAQmC,MAAMpC,MAAMoC,MAAMnC,MAAvD;AACf,QAAMF,QAAQwC,QAAQzC;AACtB,QAAMG,SAASwC,SAASzC;AAExB,MAAIF,OAAOyC,SAASvC,MAAMyC,QAAQ;AAChC,UAAMC,aAAaL,OAAOtC,QAAQsC,OAAOpC;AACzC,UAAM0C,YAAYP,MAAMrC,QAAQqC,MAAMnC;AACtC,UAAM2C,mBAAmB7C,QAAQE;AACjC,UAAM4C,oBACJD,oBAAoBF,aAAaC,YAAYC;AAE/C,WAAOX,OAAOY,kBAAkBX,QAAQ,CAA1B,CAAD;;AAIf,SAAO;AACR;AAMD,IAAaY,mBAAuC,UAAA;MAAC;IACnD/B;IACAC;IACAC;;AAEA,QAAMR,aAAoC,CAAA;AAE1C,aAAWU,sBAAsBF,qBAAqB;AACpD,UAAM;MAACpE;QAAMsE;AACb,UAAMzB,OAAOsB,eAAeI,IAAIvE,EAAnB;AAEb,QAAI6C,MAAM;AACR,YAAMmD,oBAAoBV,qBAAqBzC,MAAMqB,aAAP;AAE9C,UAAI8B,oBAAoB,GAAG;AACzBpC,mBAAWa,KAAK;UACdzE;UACAsD,MAAM;YAACgB;YAAoBhD,OAAO0E;;SAFpC;;;;AAQN,SAAOpC,WAAWc,KAAKjB,kBAAhB;AACR;SE1DeyC,YACdC,WACAC,OACAC,OAAAA;AAEA,SAAO;IACL,GAAGF;IACHG,QAAQF,SAASC,QAAQD,MAAMG,QAAQF,MAAME,QAAQ;IACrDC,QAAQJ,SAASC,QAAQD,MAAMK,SAASJ,MAAMI,SAAS;;AAE1D;SCVeC,aACdN,OACAC,OAAAA;AAEA,SAAOD,SAASC,QACZ;IACEM,GAAGP,MAAMQ,OAAOP,MAAMO;IACtBC,GAAGT,MAAMU,MAAMT,MAAMS;MAEvBC;AACL;SCXeC,uBAAuBC,UAAAA;AACrC,SAAO,SAASC,iBACdC,MADK;sCAEFC,cAAAA,IAAAA,MAAAA,OAAAA,IAAAA,OAAAA,IAAAA,CAAAA,GAAAA,OAAAA,GAAAA,OAAAA,MAAAA,QAAAA;AAAAA,kBAAAA,OAAAA,CAAAA,IAAAA,UAAAA,IAAAA;;AAEH,WAAOA,YAAYC,OACjB,CAACC,KAAKC,gBAAgB;MACpB,GAAGD;MACHR,KAAKQ,IAAIR,MAAMG,WAAWM,WAAWV;MACrCW,QAAQF,IAAIE,SAASP,WAAWM,WAAWV;MAC3CD,MAAMU,IAAIV,OAAOK,WAAWM,WAAWZ;MACvCc,OAAOH,IAAIG,QAAQR,WAAWM,WAAWZ;QAE3C;MAAC,GAAGQ;KARC;;AAWV;AAEM,IAAMO,kBAAkBV,uBAAuB,CAAD;SClBrCW,eAAexB,WAAAA;AAC7B,MAAIA,UAAUyB,WAAW,WAArB,GAAmC;AACrC,UAAMC,iBAAiB1B,UAAU2B,MAAM,GAAG,EAAnB,EAAuBC,MAAM,IAA7B;AAEvB,WAAO;MACLpB,GAAG,CAACkB,eAAe,EAAD;MAClBhB,GAAG,CAACgB,eAAe,EAAD;MAClBvB,QAAQ,CAACuB,eAAe,CAAD;MACvBrB,QAAQ,CAACqB,eAAe,CAAD;;aAEhB1B,UAAUyB,WAAW,SAArB,GAAiC;AAC1C,UAAMC,iBAAiB1B,UAAU2B,MAAM,GAAG,EAAnB,EAAuBC,MAAM,IAA7B;AAEvB,WAAO;MACLpB,GAAG,CAACkB,eAAe,CAAD;MAClBhB,GAAG,CAACgB,eAAe,CAAD;MAClBvB,QAAQ,CAACuB,eAAe,CAAD;MACvBrB,QAAQ,CAACqB,eAAe,CAAD;;;AAI3B,SAAO;AACR;SCpBeG,iBACdb,MACAhB,WACA8B,iBAAAA;AAEA,QAAMC,kBAAkBP,eAAexB,SAAD;AAEtC,MAAI,CAAC+B,iBAAiB;AACpB,WAAOf;;AAGT,QAAM;IAACb;IAAQE;IAAQG,GAAGwB;IAAYtB,GAAGuB;MAAcF;AAEvD,QAAMvB,IAAIQ,KAAKP,OAAOuB,cAAc,IAAI7B,UAAU+B,WAAWJ,eAAD;AAC5D,QAAMpB,IACJM,KAAKL,MACLsB,cACC,IAAI5B,UACH6B,WAAWJ,gBAAgBH,MAAMG,gBAAgBK,QAAQ,GAAxB,IAA+B,CAArD,CAAD;AACd,QAAMC,IAAIjC,SAASa,KAAKZ,QAAQD,SAASa,KAAKZ;AAC9C,QAAMiC,IAAIhC,SAASW,KAAKV,SAASD,SAASW,KAAKV;AAE/C,SAAO;IACLF,OAAOgC;IACP9B,QAAQ+B;IACR1B,KAAKD;IACLY,OAAOd,IAAI4B;IACXf,QAAQX,IAAI2B;IACZ5B,MAAMD;;AAET;ACzBD,IAAM8B,iBAA0B;EAACC,iBAAiB;AAAlB;AAKhC,SAAgBC,cACdC,SACAC,SAAAA;MAAAA,YAAAA,QAAAA;AAAAA,cAAmBJ;;AAEnB,MAAItB,OAAmByB,QAAQE,sBAAR;AAEvB,MAAID,QAAQH,iBAAiB;AAC3B,UAAM;MAACvC;MAAW8B;QAChBc,UAAUH,OAAD,EAAUI,iBAAiBJ,OAApC;AAEF,QAAIzC,WAAW;AACbgB,aAAOa,iBAAiBb,MAAMhB,WAAW8B,eAAlB;;;AAI3B,QAAM;IAACnB;IAAKF;IAAML;IAAOE;IAAQe;IAAQC;MAASN;AAElD,SAAO;IACLL;IACAF;IACAL;IACAE;IACAe;IACAC;;AAEH;AAUD,SAAgBwB,+BAA+BL,SAAAA;AAC7C,SAAOD,cAAcC,SAAS;IAACF,iBAAiB;GAA5B;AACrB;SCjDeQ,oBAAoBN,SAAAA;AAClC,QAAMrC,QAAQqC,QAAQO;AACtB,QAAM1C,SAASmC,QAAQQ;AAEvB,SAAO;IACLtC,KAAK;IACLF,MAAM;IACNa,OAAOlB;IACPiB,QAAQf;IACRF;IACAE;;AAEH;SCZe4C,QACdC,MACAC,eAAAA;MAAAA,kBAAAA,QAAAA;AAAAA,oBAAqCR,UAAUO,IAAD,EAAON,iBAAiBM,IAAjC;;AAErC,SAAOC,cAAcC,aAAa;AACnC;SCLeC,aACdb,SACAW,eAAAA;MAAAA,kBAAAA,QAAAA;AAAAA,oBAAqCR,UAAUH,OAAD,EAAUI,iBACtDJ,OADmC;;AAIrC,QAAMc,gBAAgB;AACtB,QAAMC,cAAa,CAAC,YAAY,aAAa,WAA1B;AAEnB,SAAOA,YAAWC,KAAMC,cAAD;AACrB,UAAMC,QAAQP,cAAcM,QAAD;AAE3B,WAAO,OAAOC,UAAU,WAAWJ,cAAcK,KAAKD,KAAnB,IAA4B;GAH1D;AAKR;SCNeE,uBACdpB,SACAqB,OAAAA;AAEA,QAAMC,gBAA2B,CAAA;AAEjC,WAASC,wBAAwBb,MAAjC;AACE,QAAIW,SAAS,QAAQC,cAAcE,UAAUH,OAAO;AAClD,aAAOC;;AAGT,QAAI,CAACZ,MAAM;AACT,aAAOY;;AAGT,QACEG,WAAWf,IAAD,KACVA,KAAKgB,oBAAoB,QACzB,CAACJ,cAAcK,SAASjB,KAAKgB,gBAA5B,GACD;AACAJ,oBAAcM,KAAKlB,KAAKgB,gBAAxB;AAEA,aAAOJ;;AAGT,QAAI,CAACO,cAAcnB,IAAD,KAAUoB,aAAapB,IAAD,GAAQ;AAC9C,aAAOY;;AAGT,QAAIA,cAAcK,SAASjB,IAAvB,GAA8B;AAChC,aAAOY;;AAGT,UAAMX,gBAAgBR,UAAUH,OAAD,EAAUI,iBAAiBM,IAApC;AAEtB,QAAIA,SAASV,SAAS;AACpB,UAAIa,aAAaH,MAAMC,aAAP,GAAuB;AACrCW,sBAAcM,KAAKlB,IAAnB;;;AAIJ,QAAID,QAAQC,MAAMC,aAAP,GAAuB;AAChC,aAAOW;;AAGT,WAAOC,wBAAwBb,KAAKqB,UAAN;;AAGhC,MAAI,CAAC/B,SAAS;AACZ,WAAOsB;;AAGT,SAAOC,wBAAwBvB,OAAD;AAC/B;AAED,SAAgBgC,2BAA2BtB,MAAAA;AACzC,QAAM,CAACuB,uBAAD,IAA4Bb,uBAAuBV,MAAM,CAAP;AAExD,SAAOuB,2BAAP,OAAOA,0BAA2B;AACnC;SC5DeC,qBAAqBlC,SAAAA;AACnC,MAAI,CAACmC,aAAa,CAACnC,SAAS;AAC1B,WAAO;;AAGT,MAAIoC,SAASpC,OAAD,GAAW;AACrB,WAAOA;;AAGT,MAAI,CAACqC,OAAOrC,OAAD,GAAW;AACpB,WAAO;;AAGT,MACEyB,WAAWzB,OAAD,KACVA,YAAYsC,iBAAiBtC,OAAD,EAAU0B,kBACtC;AACA,WAAOa;;AAGT,MAAIV,cAAc7B,OAAD,GAAW;AAC1B,WAAOA;;AAGT,SAAO;AACR;SC9BewC,qBAAqBxC,SAAAA;AACnC,MAAIoC,SAASpC,OAAD,GAAW;AACrB,WAAOA,QAAQyC;;AAGjB,SAAOzC,QAAQ0C;AAChB;AAED,SAAgBC,qBAAqB3C,SAAAA;AACnC,MAAIoC,SAASpC,OAAD,GAAW;AACrB,WAAOA,QAAQ4C;;AAGjB,SAAO5C,QAAQ6C;AAChB;AAED,SAAgBC,qBACd9C,SAAAA;AAEA,SAAO;IACLjC,GAAGyE,qBAAqBxC,OAAD;IACvB/B,GAAG0E,qBAAqB3C,OAAD;;AAE1B;AC3BD,IAAY+C;CAAZ,SAAYA,YAAAA;AACVA,EAAAA,WAAAA,WAAAA,SAAAA,IAAAA,CAAAA,IAAA;AACAA,EAAAA,WAAAA,WAAAA,UAAAA,IAAAA,EAAAA,IAAA;AACD,GAHWA,cAAAA,YAAS,CAAA,EAArB;SCEgBC,2BAA2BhD,SAAAA;AACzC,MAAI,CAACmC,aAAa,CAACnC,SAAS;AAC1B,WAAO;;AAGT,SAAOA,YAAYiD,SAASvB;AAC7B;SCNewB,kBAAkBC,oBAAAA;AAChC,QAAMC,YAAY;IAChBrF,GAAG;IACHE,GAAG;;AAEL,QAAMoF,aAAaL,2BAA2BG,kBAAD,IACzC;IACEtF,QAAQ0E,OAAO/B;IACf7C,OAAO4E,OAAOhC;MAEhB;IACE1C,QAAQsF,mBAAmBG;IAC3B3F,OAAOwF,mBAAmBI;;AAEhC,QAAMC,YAAY;IAChBzF,GAAGoF,mBAAmBM,cAAcJ,WAAW1F;IAC/CM,GAAGkF,mBAAmBO,eAAeL,WAAWxF;;AAGlD,QAAM8F,QAAQR,mBAAmBN,aAAaO,UAAUnF;AACxD,QAAM2F,SAAST,mBAAmBT,cAAcU,UAAUrF;AAC1D,QAAM8F,WAAWV,mBAAmBN,aAAaW,UAAUvF;AAC3D,QAAM6F,UAAUX,mBAAmBT,cAAcc,UAAUzF;AAE3D,SAAO;IACL4F;IACAC;IACAC;IACAC;IACAN;IACAJ;;AAEH;AC5BD,IAAMW,mBAAmB;EACvBhG,GAAG;EACHE,GAAG;AAFoB;AAKzB,SAAgB+F,2BACdC,iBACAC,qBAAAA,MAEAC,cACAC,qBAAAA;MAFA;IAAClG;IAAKF;IAAMa;IAAOD;;MACnBuF,iBAAAA,QAAAA;AAAAA,mBAAe;;MACfC,wBAAAA,QAAAA;AAAAA,0BAAsBL;;AAEtB,QAAM;IAACJ;IAAOE;IAAUD;IAAQE;MAAWZ,kBAAkBe,eAAD;AAE5D,QAAMI,YAAY;IAChBtG,GAAG;IACHE,GAAG;;AAEL,QAAMqG,QAAQ;IACZvG,GAAG;IACHE,GAAG;;AAEL,QAAMsG,YAAY;IAChB1G,QAAQqG,oBAAoBrG,SAASuG,oBAAoBnG;IACzDN,OAAOuG,oBAAoBvG,QAAQyG,oBAAoBrG;;AAGzD,MAAI,CAAC4F,SAASzF,OAAOgG,oBAAoBhG,MAAMqG,UAAU1G,QAAQ;AAE/DwG,cAAUpG,IAAI8E,UAAUyB;AACxBF,UAAMrG,IACJkG,eACAM,KAAKC,KACFR,oBAAoBhG,MAAMqG,UAAU1G,SAASK,OAAOqG,UAAU1G,MADjE;aAIF,CAACgG,YACDjF,UAAUsF,oBAAoBtF,SAAS2F,UAAU1G,QACjD;AAEAwG,cAAUpG,IAAI8E,UAAU4B;AACxBL,UAAMrG,IACJkG,eACAM,KAAKC,KACFR,oBAAoBtF,SAAS2F,UAAU1G,SAASe,UAC/C2F,UAAU1G,MAFd;;AAMJ,MAAI,CAACiG,WAAWjF,SAASqF,oBAAoBrF,QAAQ0F,UAAU5G,OAAO;AAEpE0G,cAAUtG,IAAIgF,UAAU4B;AACxBL,UAAMvG,IACJoG,eACAM,KAAKC,KACFR,oBAAoBrF,QAAQ0F,UAAU5G,QAAQkB,SAAS0F,UAAU5G,KADpE;aAGO,CAACiG,UAAU5F,QAAQkG,oBAAoBlG,OAAOuG,UAAU5G,OAAO;AAExE0G,cAAUtG,IAAIgF,UAAUyB;AACxBF,UAAMvG,IACJoG,eACAM,KAAKC,KACFR,oBAAoBlG,OAAOuG,UAAU5G,QAAQK,QAAQuG,UAAU5G,KADlE;;AAKJ,SAAO;IACL0G;IACAC;;AAEH;SC7EeM,qBAAqB5E,SAAAA;AACnC,MAAIA,YAAYiD,SAASvB,kBAAkB;AACzC,UAAM;MAACnB;MAAYC;QAAe+B;AAElC,WAAO;MACLrE,KAAK;MACLF,MAAM;MACNa,OAAO0B;MACP3B,QAAQ4B;MACR7C,OAAO4C;MACP1C,QAAQ2C;;;AAIZ,QAAM;IAACtC;IAAKF;IAAMa;IAAOD;MAAUoB,QAAQE,sBAAR;AAEnC,SAAO;IACLhC;IACAF;IACAa;IACAD;IACAjB,OAAOqC,QAAQuD;IACf1F,QAAQmC,QAAQsD;;AAEnB;SCdeuB,iBAAiBC,qBAAAA;AAC/B,SAAOA,oBAAoBrG,OAAoB,CAACC,KAAKgC,SAAN;AAC7C,WAAOqE,IAAIrG,KAAKoE,qBAAqBpC,IAAD,CAA1B;KACTvC,kBAFI;AAGR;AAED,SAAgB6G,iBAAiBF,qBAAAA;AAC/B,SAAOA,oBAAoBrG,OAAe,CAACC,KAAKgC,SAAN;AACxC,WAAOhC,MAAM8D,qBAAqB9B,IAAD;KAChC,CAFI;AAGR;AAED,SAAgBuE,iBAAiBH,qBAAAA;AAC/B,SAAOA,oBAAoBrG,OAAe,CAACC,KAAKgC,SAAN;AACxC,WAAOhC,MAAMiE,qBAAqBjC,IAAD;KAChC,CAFI;AAGR;SCtBewE,uBACdlF,SACAmF,SAAAA;MAAAA,YAAAA,QAAAA;AAAAA,cAA6CpF;;AAE7C,MAAI,CAACC,SAAS;AACZ;;AAGF,QAAM;IAAC9B;IAAKF;IAAMY;IAAQC;MAASsG,QAAQnF,OAAD;AAC1C,QAAMiC,0BAA0BD,2BAA2BhC,OAAD;AAE1D,MAAI,CAACiC,yBAAyB;AAC5B;;AAGF,MACErD,UAAU,KACVC,SAAS,KACTX,OAAOqE,OAAO/B,eACdxC,QAAQuE,OAAOhC,YACf;AACAP,YAAQoF,eAAe;MACrBC,OAAO;MACPC,QAAQ;KAFV;;AAKH;ACtBD,IAAMvE,aAAa,CACjB,CAAC,KAAK,CAAC,QAAQ,OAAT,GAAmBiE,gBAAzB,GACA,CAAC,KAAK,CAAC,OAAO,QAAR,GAAmBC,gBAAzB,CAFiB;AAKnB,IAAaM,OAAb,MAAaA;EACXC,YAAYjH,MAAkByB,SAAAA;SAyBtBzB,OAAAA;SAEDZ,QAAAA;SAEAE,SAAAA;SAIAK,MAAAA;SAEAU,SAAAA;SAEAC,QAAAA;SAEAb,OAAAA;AAtCL,UAAM8G,sBAAsB1D,uBAAuBpB,OAAD;AAClD,UAAMyF,gBAAgBZ,iBAAiBC,mBAAD;AAEtC,SAAKvG,OAAO;MAAC,GAAGA;;AAChB,SAAKZ,QAAQY,KAAKZ;AAClB,SAAKE,SAASU,KAAKV;AAEnB,eAAW,CAAC6H,MAAMC,MAAMC,eAAb,KAAiC7E,YAAY;AACtD,iBAAW8E,QAAOF,MAAM;AACtBG,eAAOC,eAAe,MAAMF,MAAK;UAC/BG,KAAK,MAAA;AACH,kBAAMC,iBAAiBL,gBAAgBd,mBAAD;AACtC,kBAAMoB,sBAAsBT,cAAcC,IAAD,IAASO;AAElD,mBAAO,KAAK1H,KAAKsH,IAAV,IAAiBK;;UAE1BC,YAAY;SAPd;;;AAYJL,WAAOC,eAAe,MAAM,QAAQ;MAACI,YAAY;KAAjD;;;ICpCSC,kBAAAA;EAOXZ,YAAoBa,QAAAA;SAAAA,SAAAA;SANZC,YAIF,CAAA;SAaCC,YAAY,MAAA;AACjB,WAAKD,UAAUE,QAASC,cAAD;AAAA,YAAA;AAAA,gBAAA,eACrB,KAAKJ,WADgB,OAAA,SACrB,aAAaK,oBAAoB,GAAGD,QAApC;OADF;;AAZkB,SAAA,SAAAJ;;EAEbtB,IACL4B,WACAC,SACA3G,SAHQ;;AAKR,KAAA,gBAAA,KAAKoG,WAAL,OAAA,SAAA,cAAaQ,iBAAiBF,WAAWC,SAA0B3G,OAAnE;AACA,SAAKqG,UAAU1E,KAAK,CAAC+E,WAAWC,SAA0B3G,OAAtC,CAApB;;;SCbY6G,uBACdT,QAAAA;AAQA,QAAM;IAACU;MAAe5G,UAAUkG,MAAD;AAE/B,SAAOA,kBAAkBU,cAAcV,SAAS/D,iBAAiB+D,MAAD;AACjE;SCZeW,oBACdC,OACAC,aAAAA;AAEA,QAAMC,KAAK1C,KAAKC,IAAIuC,MAAMlJ,CAAf;AACX,QAAMqJ,KAAK3C,KAAKC,IAAIuC,MAAMhJ,CAAf;AAEX,MAAI,OAAOiJ,gBAAgB,UAAU;AACnC,WAAOzC,KAAK4C,KAAKF,MAAM,IAAIC,MAAM,CAA1B,IAA+BF;;AAGxC,MAAI,OAAOA,eAAe,OAAOA,aAAa;AAC5C,WAAOC,KAAKD,YAAYnJ,KAAKqJ,KAAKF,YAAYjJ;;AAGhD,MAAI,OAAOiJ,aAAa;AACtB,WAAOC,KAAKD,YAAYnJ;;AAG1B,MAAI,OAAOmJ,aAAa;AACtB,WAAOE,KAAKF,YAAYjJ;;AAG1B,SAAO;AACR;AC1BD,IAAYqJ;CAAZ,SAAYA,YAAAA;AACVA,EAAAA,WAAAA,OAAAA,IAAA;AACAA,EAAAA,WAAAA,WAAAA,IAAA;AACAA,EAAAA,WAAAA,SAAAA,IAAA;AACAA,EAAAA,WAAAA,aAAAA,IAAA;AACAA,EAAAA,WAAAA,QAAAA,IAAA;AACAA,EAAAA,WAAAA,iBAAAA,IAAA;AACAA,EAAAA,WAAAA,kBAAAA,IAAA;AACD,GARWA,cAAAA,YAAS,CAAA,EAArB;AAUA,SAAgBC,eAAeC,OAAAA;AAC7BA,QAAMD,eAAN;AACD;AAED,SAAgBE,gBAAgBD,OAAAA;AAC9BA,QAAMC,gBAAN;AACD;ICbWC;CAAZ,SAAYA,eAAAA;AACVA,EAAAA,cAAAA,OAAAA,IAAA;AACAA,EAAAA,cAAAA,MAAAA,IAAA;AACAA,EAAAA,cAAAA,OAAAA,IAAA;AACAA,EAAAA,cAAAA,MAAAA,IAAA;AACAA,EAAAA,cAAAA,IAAAA,IAAA;AACAA,EAAAA,cAAAA,KAAAA,IAAA;AACAA,EAAAA,cAAAA,OAAAA,IAAA;AACAA,EAAAA,cAAAA,KAAAA,IAAA;AACD,GATWA,iBAAAA,eAAY,CAAA,EAAxB;ACDO,IAAMC,uBAAsC;EACjDC,OAAO,CAACF,aAAaG,OAAOH,aAAaI,KAAlC;EACPC,QAAQ,CAACL,aAAaM,GAAd;EACRC,KAAK,CAACP,aAAaG,OAAOH,aAAaI,OAAOJ,aAAaQ,GAAtD;AAH4C;AAMnD,IAAaC,kCAA4D,CACvEX,OADuE,SAAA;MAEvE;IAACY;;AAED,UAAQZ,MAAMa,MAAd;IACE,KAAKX,aAAaY;AAChB,aAAO;QACL,GAAGF;QACHrK,GAAGqK,mBAAmBrK,IAAI;;IAE9B,KAAK2J,aAAaa;AAChB,aAAO;QACL,GAAGH;QACHrK,GAAGqK,mBAAmBrK,IAAI;;IAE9B,KAAK2J,aAAac;AAChB,aAAO;QACL,GAAGJ;QACHnK,GAAGmK,mBAAmBnK,IAAI;;IAE9B,KAAKyJ,aAAae;AAChB,aAAO;QACL,GAAGL;QACHnK,GAAGmK,mBAAmBnK,IAAI;;;AAIhC,SAAOyK;AACR;ICGYC,uBAAAA;EAMXnD,YAAoBoD,OAAAA;SAAAA,QAAAA;SALbC,oBAAoB;SACnBC,uBAAAA;SACAxC,YAAAA;SACAyC,kBAAAA;AAEY,SAAA,QAAAH;AAClB,UAAM;MACJpB,OAAO;QAACnB;;QACNuC;AAEJ,SAAKA,QAAQA;AACb,SAAKtC,YAAY,IAAIF,UAAU9D,iBAAiB+D,MAAD,CAA9B;AACjB,SAAK0C,kBAAkB,IAAI3C,UAAUjG,UAAUkG,MAAD,CAAvB;AACvB,SAAK2C,gBAAgB,KAAKA,cAAcC,KAAK,IAAxB;AACrB,SAAKC,eAAe,KAAKA,aAAaD,KAAK,IAAvB;AAEpB,SAAKE,OAAL;;EAGMA,SAAM;AACZ,SAAKC,YAAL;AAEA,SAAKL,gBAAgBhE,IAAIuC,UAAU+B,QAAQ,KAAKH,YAAhD;AACA,SAAKH,gBAAgBhE,IAAIuC,UAAUgC,kBAAkB,KAAKJ,YAA1D;AAEAK,eAAW,MAAM,KAAKjD,UAAUvB,IAAIuC,UAAUkC,SAAS,KAAKR,aAA3C,CAAP;;EAGJI,cAAW;AACjB,UAAM;MAACK;MAAYC;QAAW,KAAKd;AACnC,UAAMlI,OAAO+I,WAAW/I,KAAKiJ;AAE7B,QAAIjJ,MAAM;AACRwE,6BAAuBxE,IAAD;;AAGxBgJ,YAAQvL,kBAAD;;EAGD6K,cAAcxB,OAAD;AACnB,QAAIoC,gBAAgBpC,KAAD,GAAS;AAC1B,YAAM;QAACqC;QAAQC;QAAS7J;UAAW,KAAK2I;AACxC,YAAM;QACJmB,gBAAgBpC;QAChBqC,mBAAmB7B;QACnB8B,iBAAiB;UACfhK;AACJ,YAAM;QAACoI;UAAQb;AAEf,UAAIuC,cAAc9B,IAAItG,SAAS0G,IAA3B,GAAkC;AACpC,aAAK6B,UAAU1C,KAAf;AACA;;AAGF,UAAIuC,cAAchC,OAAOpG,SAAS0G,IAA9B,GAAqC;AACvC,aAAKa,aAAa1B,KAAlB;AACA;;AAGF,YAAM;QAAC2C;UAAiBL,QAAQH;AAChC,YAAMvB,qBAAqB+B,gBACvB;QAACpM,GAAGoM,cAAcnM;QAAMC,GAAGkM,cAAcjM;UACzCC;AAEJ,UAAI,CAAC,KAAK2K,sBAAsB;AAC9B,aAAKA,uBAAuBV;;AAG9B,YAAMgC,iBAAiBJ,iBAAiBxC,OAAO;QAC7CqC;QACAC,SAASA,QAAQH;QACjBvB;OAHqC;AAMvC,UAAIgC,gBAAgB;AAClB,cAAMC,mBAAmBC,SACvBF,gBACAhC,kBAF0C;AAI5C,cAAMmC,cAAc;UAClBxM,GAAG;UACHE,GAAG;;AAEL,cAAM;UAAC6G;YAAuBgF,QAAQH;AAEtC,mBAAW1F,mBAAmBa,qBAAqB;AACjD,gBAAMT,YAAYmD,MAAMa;AACxB,gBAAM;YAAC1E;YAAOG;YAASF;YAAQC;YAAUL;YAAWJ;cAClDF,kBAAkBe,eAAD;AACnB,gBAAMuG,oBAAoB5F,qBAAqBX,eAAD;AAE9C,gBAAMwG,qBAAqB;YACzB1M,GAAG0G,KAAKiG,IACNrG,cAAcqD,aAAaY,QACvBkC,kBAAkB3L,QAAQ2L,kBAAkB7M,QAAQ,IACpD6M,kBAAkB3L,OACtB4F,KAAKkG,IACHtG,cAAcqD,aAAaY,QACvBkC,kBAAkBxM,OAClBwM,kBAAkBxM,OAAOwM,kBAAkB7M,QAAQ,GACvDyM,eAAerM,CAJjB,CAJC;YAWHE,GAAGwG,KAAKiG,IACNrG,cAAcqD,aAAac,OACvBgC,kBAAkB5L,SAAS4L,kBAAkB3M,SAAS,IACtD2M,kBAAkB5L,QACtB6F,KAAKkG,IACHtG,cAAcqD,aAAac,OACvBgC,kBAAkBtM,MAClBsM,kBAAkBtM,MAAMsM,kBAAkB3M,SAAS,GACvDuM,eAAenM,CAJjB,CAJC;;AAaL,gBAAM2M,aACHvG,cAAcqD,aAAaY,SAAS,CAACxE,WACrCO,cAAcqD,aAAaa,QAAQ,CAAC3E;AACvC,gBAAMiH,aACHxG,cAAcqD,aAAac,QAAQ,CAAC3E,YACpCQ,cAAcqD,aAAae,MAAM,CAAC9E;AAErC,cAAIiH,cAAcH,mBAAmB1M,MAAMqM,eAAerM,GAAG;AAC3D,kBAAM+M,uBACJ7G,gBAAgBvB,aAAa2H,iBAAiBtM;AAChD,kBAAMgN,4BACH1G,cAAcqD,aAAaY,SAC1BwC,wBAAwBtH,UAAUzF,KACnCsG,cAAcqD,aAAaa,QAC1BuC,wBAAwB1H,UAAUrF;AAEtC,gBAAIgN,6BAA6B,CAACV,iBAAiBpM,GAAG;AAGpDgG,8BAAgB+G,SAAS;gBACvBhN,MAAM8M;gBACNG,UAAUhB;eAFZ;AAIA;;AAGF,gBAAIc,2BAA2B;AAC7BR,0BAAYxM,IAAIkG,gBAAgBvB,aAAaoI;mBACxC;AACLP,0BAAYxM,IACVsG,cAAcqD,aAAaY,QACvBrE,gBAAgBvB,aAAac,UAAUzF,IACvCkG,gBAAgBvB,aAAaU,UAAUrF;;AAG/C,gBAAIwM,YAAYxM,GAAG;AACjBkG,8BAAgBiH,SAAS;gBACvBlN,MAAM,CAACuM,YAAYxM;gBACnBkN,UAAUhB;eAFZ;;AAKF;qBACSY,cAAcJ,mBAAmBxM,MAAMmM,eAAenM,GAAG;AAClE,kBAAM6M,uBACJ7G,gBAAgBpB,YAAYwH,iBAAiBpM;AAC/C,kBAAM8M,4BACH1G,cAAcqD,aAAac,QAC1BsC,wBAAwBtH,UAAUvF,KACnCoG,cAAcqD,aAAae,MAC1BqC,wBAAwB1H,UAAUnF;AAEtC,gBAAI8M,6BAA6B,CAACV,iBAAiBtM,GAAG;AAGpDkG,8BAAgB+G,SAAS;gBACvB9M,KAAK4M;gBACLG,UAAUhB;eAFZ;AAIA;;AAGF,gBAAIc,2BAA2B;AAC7BR,0BAAYtM,IAAIgG,gBAAgBpB,YAAYiI;mBACvC;AACLP,0BAAYtM,IACVoG,cAAcqD,aAAac,OACvBvE,gBAAgBpB,YAAYW,UAAUvF,IACtCgG,gBAAgBpB,YAAYO,UAAUnF;;AAG9C,gBAAIsM,YAAYtM,GAAG;AACjBgG,8BAAgBiH,SAAS;gBACvBhN,KAAK,CAACqM,YAAYtM;gBAClBgN,UAAUhB;eAFZ;;AAMF;;;AAIJ,aAAKkB,WACH3D,OACA4D,IACEd,SAAoBF,gBAAgB,KAAKtB,oBAAtB,GACnByB,WAFoB,CAFxB;;;;EAWEY,WAAW3D,OAAc6D,aAAf;AAChB,UAAM;MAACC;QAAU,KAAK1C;AAEtBpB,UAAMD,eAAN;AACA+D,WAAOD,WAAD;;EAGAnB,UAAU1C,OAAD;AACf,UAAM;MAAC+D;QAAS,KAAK3C;AAErBpB,UAAMD,eAAN;AACA,SAAKiE,OAAL;AACAD,UAAK;;EAGCrC,aAAa1B,OAAD;AAClB,UAAM;MAACiE;QAAY,KAAK7C;AAExBpB,UAAMD,eAAN;AACA,SAAKiE,OAAL;AACAC,aAAQ;;EAGFD,SAAM;AACZ,SAAKlF,UAAUC,UAAf;AACA,SAAKwC,gBAAgBxC,UAArB;;;AA1OSoC,eA6OJ+C,aAAgD,CACrD;EACE/E,WAAW;EACXC,SAAS,CACPY,OADO,MAAA,UAAA;QAEP;MAACuC,gBAAgBpC;MAAsBgE;;QACvC;MAAC9B;;AAED,UAAM;MAACxB;QAAQb,MAAMoE;AAErB,QAAI7B,cAAcnC,MAAMjG,SAAS0G,IAA7B,GAAoC;AACtC,YAAMwD,YAAYhC,OAAOiC,cAAcnC;AAEvC,UAAIkC,aAAarE,MAAMnB,WAAWwF,WAAW;AAC3C,eAAO;;AAGTrE,YAAMD,eAAN;AAEAoE,sBAAY,OAAZ,SAAAA,aAAe;QAACnE,OAAOA,MAAMoE;OAAjB;AAEZ,aAAO;;AAGT,WAAO;;AAvBX,CADqD;ACxOzD,SAASG,qBACPC,YADF;AAGE,SAAOC,QAAQD,cAAc,cAAcA,UAA7B;AACf;AAED,SAASE,kBACPF,YADF;AAGE,SAAOC,QAAQD,cAAc,WAAWA,UAA1B;AACf;AAaD,IAAaG,wBAAb,MAAaA;EAUX3G,YACUoD,OACAwD,SACRC,gBAAAA;;QAAAA,mBAAAA,QAAAA;AAAAA,uBAAiBvF,uBAAuB8B,MAAMpB,MAAMnB,MAAb;;SAF/BuC,QAAAA;SACAwD,SAAAA;SAXHvD,oBAAoB;SACnB5F,WAAAA;SACAqJ,YAAqB;SACrBC,qBAAAA;SACAC,YAAmC;SACnClG,YAAAA;SACAmG,oBAAAA;SACA1D,kBAAAA;AAGE,SAAA,QAAAH;AACA,SAAA,SAAAwD;AAGR,UAAM;MAAC5E;QAASoB;AAChB,UAAM;MAACvC;QAAUmB;AAEjB,SAAKoB,QAAQA;AACb,SAAKwD,SAASA;AACd,SAAKnJ,WAAWX,iBAAiB+D,MAAD;AAChC,SAAKoG,oBAAoB,IAAIrG,UAAU,KAAKnD,QAAnB;AACzB,SAAKqD,YAAY,IAAIF,UAAUiG,cAAd;AACjB,SAAKtD,kBAAkB,IAAI3C,UAAUjG,UAAUkG,MAAD,CAAvB;AACvB,SAAKkG,sBAAL,uBAA0BG,oBAAoBlF,KAAD,MAA7C,OAAA,uBAAwDrJ;AACxD,SAAKiL,cAAc,KAAKA,YAAYH,KAAK,IAAtB;AACnB,SAAKkC,aAAa,KAAKA,WAAWlC,KAAK,IAArB;AAClB,SAAKiB,YAAY,KAAKA,UAAUjB,KAAK,IAApB;AACjB,SAAKC,eAAe,KAAKA,aAAaD,KAAK,IAAvB;AACpB,SAAK0D,gBAAgB,KAAKA,cAAc1D,KAAK,IAAxB;AACrB,SAAK2D,sBAAsB,KAAKA,oBAAoB3D,KAAK,IAA9B;AAE3B,SAAKE,OAAL;;EAGMA,SAAM;AACZ,UAAM;MACJiD,QAAAA;MACAxD,OAAO;QACL3I,SAAS;UAAC4M;UAAsBC;;;QAEhC;AAEJ,SAAKxG,UAAUvB,IAAIqH,QAAOW,KAAKC,MAAM,KAAK7B,YAAY;MAAC8B,SAAS;KAAhE;AACA,SAAK3G,UAAUvB,IAAIqH,QAAOnE,IAAI+E,MAAM,KAAK9C,SAAzC;AAEA,QAAIkC,QAAOrE,QAAQ;AACjB,WAAKzB,UAAUvB,IAAIqH,QAAOrE,OAAOiF,MAAM,KAAK9D,YAA5C;;AAGF,SAAKH,gBAAgBhE,IAAIuC,UAAU+B,QAAQ,KAAKH,YAAhD;AACA,SAAKH,gBAAgBhE,IAAIuC,UAAU4F,WAAW3F,cAA9C;AACA,SAAKwB,gBAAgBhE,IAAIuC,UAAUgC,kBAAkB,KAAKJ,YAA1D;AACA,SAAKH,gBAAgBhE,IAAIuC,UAAU6F,aAAa5F,cAAhD;AACA,SAAKkF,kBAAkB1H,IAAIuC,UAAUkC,SAAS,KAAKmD,aAAnD;AAEA,QAAIE,sBAAsB;AACxB,UACEC,8BADF,QACEA,2BAA6B;QAC3BtF,OAAO,KAAKoB,MAAMpB;QAClBiC,YAAY,KAAKb,MAAMa;QACvBxJ,SAAS,KAAK2I,MAAM3I;OAHI,GAK1B;AACA,eAAO,KAAKmJ,YAAL;;AAGT,UAAI8C,kBAAkBW,oBAAD,GAAwB;AAC3C,aAAKL,YAAYjD,WACf,KAAKH,aACLyD,qBAAqBO,KAFI;AAI3B,aAAKC,cAAcR,oBAAnB;AACA;;AAGF,UAAId,qBAAqBc,oBAAD,GAAwB;AAC9C,aAAKQ,cAAcR,oBAAnB;AACA;;;AAIJ,SAAKzD,YAAL;;EAGMoC,SAAM;AACZ,SAAKlF,UAAUC,UAAf;AACA,SAAKwC,gBAAgBxC,UAArB;AAIAgD,eAAW,KAAKkD,kBAAkBlG,WAAW,EAAnC;AAEV,QAAI,KAAKiG,cAAc,MAAM;AAC3Bc,mBAAa,KAAKd,SAAN;AACZ,WAAKA,YAAY;;;EAIba,cACNrB,YACAuB,QAFmB;AAInB,UAAM;MAAC1D;MAAQ2D;QAAa,KAAK5E;AACjC4E,cAAU3D,QAAQmC,YAAY,KAAKO,oBAAoBgB,MAA9C;;EAGHnE,cAAW;AACjB,UAAM;MAACmD;QAAsB;AAC7B,UAAM;MAAC7C;QAAW,KAAKd;AAEvB,QAAI2D,oBAAoB;AACtB,WAAKD,YAAY;AAGjB,WAAKG,kBAAkB1H,IAAIuC,UAAUmG,OAAOhG,iBAAiB;QAC3DiG,SAAS;OADX;AAKA,WAAKd,oBAAL;AAGA,WAAKH,kBAAkB1H,IACrBuC,UAAUqG,iBACV,KAAKf,mBAFP;AAKAlD,cAAQ6C,kBAAD;;;EAIHpB,WAAW3D,OAAD;;AAChB,UAAM;MAAC8E;MAAWC;MAAoB3D;QAAS;AAC/C,UAAM;MACJ0C;MACArL,SAAS;QAAC4M;;QACRjE;AAEJ,QAAI,CAAC2D,oBAAoB;AACvB;;AAGF,UAAMlB,eAAW,wBAAGqB,oBAAoBlF,KAAD,MAAtB,OAAA,wBAAiCrJ;AAClD,UAAM8I,QAAQqD,SAAoBiC,oBAAoBlB,WAArB;AAGjC,QAAI,CAACiB,aAAaO,sBAAsB;AACtC,UAAId,qBAAqBc,oBAAD,GAAwB;AAC9C,YACEA,qBAAqBe,aAAa,QAClC5G,oBAAoBC,OAAO4F,qBAAqBe,SAA7B,GACnB;AACA,iBAAO,KAAK1E,aAAL;;AAGT,YAAIlC,oBAAoBC,OAAO4F,qBAAqBgB,QAA7B,GAAwC;AAC7D,iBAAO,KAAKzE,YAAL;;;AAIX,UAAI8C,kBAAkBW,oBAAD,GAAwB;AAC3C,YAAI7F,oBAAoBC,OAAO4F,qBAAqBe,SAA7B,GAAyC;AAC9D,iBAAO,KAAK1E,aAAL;;;AAIX,WAAKmE,cAAcR,sBAAsB5F,KAAzC;AACA;;AAGF,QAAIO,MAAMsG,YAAY;AACpBtG,YAAMD,eAAN;;AAGF+D,WAAOD,WAAD;;EAGAnB,YAAS;AACf,UAAM;MAAC6D;MAASxC;QAAS,KAAK3C;AAE9B,SAAK4C,OAAL;AACA,QAAI,CAAC,KAAKc,WAAW;AACnByB,cAAQ,KAAKnF,MAAMiB,MAAZ;;AAET0B,UAAK;;EAGCrC,eAAY;AAClB,UAAM;MAAC6E;MAAStC;QAAY,KAAK7C;AAEjC,SAAK4C,OAAL;AACA,QAAI,CAAC,KAAKc,WAAW;AACnByB,cAAQ,KAAKnF,MAAMiB,MAAZ;;AAET4B,aAAQ;;EAGFkB,cAAcnF,OAAD;AACnB,QAAIA,MAAMa,SAASX,aAAaM,KAAK;AACnC,WAAKkB,aAAL;;;EAII0D,sBAAmB;;AACzB,KAAA,wBAAA,KAAK3J,SAAS+K,aAAd,MAAA,OAAA,SAAA,sBAA8BC,gBAA9B;;;ACtQJ,IAAM7B,SAA+B;EACnCrE,QAAQ;IAACiF,MAAM;;EACfD,MAAM;IAACC,MAAM;;EACb/E,KAAK;IAAC+E,MAAM;;AAHuB;AAUrC,IAAakB,gBAAb,cAAmC/B,sBAAAA;EACjC3G,YAAYoD,OAAAA;AACV,UAAM;MAACpB;QAASoB;AAGhB,UAAMyD,iBAAiB/J,iBAAiBkF,MAAMnB,MAAP;AAEvC,UAAMuC,OAAOwD,QAAQC,cAArB;;;AAPS6B,cAUJxC,aAAa,CAClB;EACE/E,WAAW;EACXC,SAAS,CAAA,MAAA,UAAA;QACP;MAACgF,aAAapE;;QACd;MAACmE;;AAED,QAAI,CAACnE,MAAM2G,aAAa3G,MAAM4G,WAAW,GAAG;AAC1C,aAAO;;AAGTzC,oBAAY,OAAZ,SAAAA,aAAe;MAACnE;KAAJ;AAEZ,WAAO;;AAZX,CADkB;ACpBtB,IAAM4E,WAA+B;EACnCW,MAAM;IAACC,MAAM;;EACb/E,KAAK;IAAC+E,MAAM;;AAFuB;AAKrC,IAAKqB;CAAL,SAAKA,cAAAA;AACHA,EAAAA,aAAAA,aAAAA,YAAAA,IAAAA,CAAAA,IAAA;AACD,GAFIA,gBAAAA,cAAW,CAAA,EAAhB;AAQA,IAAaC,cAAb,cAAiCnC,sBAAAA;EAC/B3G,YAAYoD,OAAAA;AACV,UAAMA,OAAOwD,UAAQ9J,iBAAiBsG,MAAMpB,MAAMnB,MAAb,CAArC;;;AAFSiI,YAKJ5C,aAAa,CAClB;EACE/E,WAAW;EACXC,SAAS,CAAA,MAAA,UAAA;QACP;MAACgF,aAAapE;;QACd;MAACmE;;AAED,QAAInE,MAAM4G,WAAWC,YAAYE,YAAY;AAC3C,aAAO;;AAGT5C,oBAAY,OAAZ,SAAAA,aAAe;MAACnE;KAAJ;AAEZ,WAAO;;AAZX,CADkB;AClBtB,IAAM4E,WAA+B;EACnCrE,QAAQ;IAACiF,MAAM;;EACfD,MAAM;IAACC,MAAM;;EACb/E,KAAK;IAAC+E,MAAM;;AAHuB;AAUrC,IAAawB,cAAb,cAAiCrC,sBAAAA;EAC/B3G,YAAYoD,OAAAA;AACV,UAAMA,OAAOwD,QAAb;;EAuBU,OAALqC,QAAK;AAIVlM,WAAOsE,iBAAiBuF,SAAOW,KAAKC,MAAM0B,OAAM;MAC9ChB,SAAS;MACTT,SAAS;KAFX;AAKA,WAAO,SAAS0B,WAAT;AACLpM,aAAOmE,oBAAoB0F,SAAOW,KAAKC,MAAM0B,KAA7C;;AAKF,aAASA,QAAT;IAAA;;;AAxCSF,YAKJ9C,aAAa,CAClB;EACE/E,WAAW;EACXC,SAAS,CAAA,MAAA,UAAA;QACP;MAACgF,aAAapE;;QACd;MAACmE;;AAED,UAAM;MAACiD;QAAWpH;AAElB,QAAIoH,QAAQpN,SAAS,GAAG;AACtB,aAAO;;AAGTmK,oBAAY,OAAZ,SAAAA,aAAe;MAACnE;KAAJ;AAEZ,WAAO;;AAdX,CADkB;IChBVqH;CAAZ,SAAYA,sBAAAA;AACVA,EAAAA,qBAAAA,qBAAAA,SAAAA,IAAAA,CAAAA,IAAA;AACAA,EAAAA,qBAAAA,qBAAAA,eAAAA,IAAAA,CAAAA,IAAA;AACD,GAHWA,wBAAAA,sBAAmB,CAAA,EAA/B;AAmCA,IAAYC;CAAZ,SAAYA,iBAAAA;AACVA,EAAAA,gBAAAA,gBAAAA,WAAAA,IAAAA,CAAAA,IAAA;AACAA,EAAAA,gBAAAA,gBAAAA,mBAAAA,IAAAA,CAAAA,IAAA;AACD,GAHWA,mBAAAA,iBAAc,CAAA,EAA1B;AAUA,SAAgBC,gBAAAA,MAAAA;MAAgB;IAC9B5K;IACA0H,YAAYgD,oBAAoBG;IAChCC;IACAC;IACAC;IACAC,WAAW;IACXC,QAAQP,eAAeQ;IACvBC;IACAzK;IACA0K;IACAvI;IACA1C;;AAEA,QAAMkL,eAAeC,gBAAgB;IAACzI;IAAO0I,UAAU,CAACR;GAApB;AACpC,QAAM,CAACS,uBAAuBC,uBAAxB,IAAmDC,YAAW;AACpE,QAAMC,kBAAcC,sBAAoB;IAACjS,GAAG;IAAGE,GAAG;GAAxB;AAC1B,QAAMgS,sBAAkBD,sBAAwB;IAACjS,GAAG;IAAGE,GAAG;GAA5B;AAC9B,QAAMM,WAAO2R,uBAAQ,MAAA;AACnB,YAAQrE,WAAR;MACE,KAAKgD,oBAAoBG;AACvB,eAAOO,qBACH;UACErR,KAAKqR,mBAAmBtR;UACxBW,QAAQ2Q,mBAAmBtR;UAC3BD,MAAMuR,mBAAmBxR;UACzBc,OAAO0Q,mBAAmBxR;YAE5B;MACN,KAAK8Q,oBAAoBsB;AACvB,eAAOjB;;KAEV,CAACrD,WAAWqD,cAAcK,kBAA1B,CAdiB;AAepB,QAAMa,yBAAqBJ,sBAAuB,IAAjB;AACjC,QAAMK,iBAAaC,2BAAY,MAAA;AAC7B,UAAMrM,kBAAkBmM,mBAAmBzG;AAE3C,QAAI,CAAC1F,iBAAiB;AACpB;;AAGF,UAAMvB,aAAaqN,YAAYpG,QAAQ5L,IAAIkS,gBAAgBtG,QAAQ5L;AACnE,UAAM8E,YAAYkN,YAAYpG,QAAQ1L,IAAIgS,gBAAgBtG,QAAQ1L;AAElEgG,oBAAgBiH,SAASxI,YAAYG,SAArC;KACC,CAAA,CAX2B;AAY9B,QAAM0N,gCAA4BL,uBAChC,MACEb,UAAUP,eAAeQ,YACrB,CAAC,GAAGxK,mBAAJ,EAAyB0L,QAAzB,IACA1L,qBACN,CAACuK,OAAOvK,mBAAR,CALuC;AAQzC2L;IACE,MAAA;AACE,UAAI,CAACtB,WAAW,CAACrK,oBAAoBtD,UAAU,CAACjD,MAAM;AACpDsR,gCAAuB;AACvB;;AAGF,iBAAW5L,mBAAmBsM,2BAA2B;AACvD,aAAItB,aAAS,OAAT,SAAAA,UAAYhL,eAAH,OAAwB,OAAO;AAC1C;;AAGF,cAAMyM,QAAQ5L,oBAAoBpF,QAAQuE,eAA5B;AACd,cAAMC,sBAAsBsL,wBAAwBkB,KAAD;AAEnD,YAAI,CAACxM,qBAAqB;AACxB;;AAGF,cAAM;UAACG;UAAWC;YAASN,2BACzBC,iBACAC,qBACA3F,MACA4F,cACAI,SALmD;AAQrD,mBAAWmB,QAAQ,CAAC,KAAK,GAAN,GAAqB;AACtC,cAAI,CAAC+J,aAAa/J,IAAD,EAAOrB,UAAUqB,IAAD,CAA5B,GAAkD;AACrDpB,kBAAMoB,IAAD,IAAS;AACdrB,sBAAUqB,IAAD,IAAS;;;AAItB,YAAIpB,MAAMvG,IAAI,KAAKuG,MAAMrG,IAAI,GAAG;AAC9B4R,kCAAuB;AAEvBO,6BAAmBzG,UAAU1F;AAC7B2L,gCAAsBS,YAAYjB,QAAb;AAErBW,sBAAYpG,UAAUrF;AACtB2L,0BAAgBtG,UAAUtF;AAE1B;;;AAIJ0L,kBAAYpG,UAAU;QAAC5L,GAAG;QAAGE,GAAG;;AAChCgS,sBAAgBtG,UAAU;QAAC5L,GAAG;QAAGE,GAAG;;AACpC4R,8BAAuB;;;IAGzB;MACE1L;MACAkM;MACApB;MACAY;MACAV;MACAC;;MAEAuB,KAAKC,UAAUrS,IAAf;;MAEAoS,KAAKC,UAAUnB,YAAf;MACAG;MACA9K;MACAyL;MACAf;;MAEAmB,KAAKC,UAAUrM,SAAf;IAhBF;EApDO;AAuEV;AAOD,IAAMsM,sBAAoC;EACxC9S,GAAG;IAAC,CAACgF,UAAUyB,QAAX,GAAsB;IAAO,CAACzB,UAAU4B,OAAX,GAAqB;;EACtD1G,GAAG;IAAC,CAAC8E,UAAUyB,QAAX,GAAsB;IAAO,CAACzB,UAAU4B,OAAX,GAAqB;;AAFd;AAK1C,SAAS+K,gBAAT,OAAA;MAAyB;IACvBzI;IACA0I;;AAKA,QAAMmB,gBAAgBC,YAAY9J,KAAD;AAEjC,SAAO+J,YACJC,oBAAD;AACE,QAAItB,YAAY,CAACmB,iBAAiB,CAACG,gBAAgB;AAEjD,aAAOJ;;AAGT,UAAMxM,YAAY;MAChBtG,GAAG0G,KAAKyM,KAAKjK,MAAMlJ,IAAI+S,cAAc/S,CAAlC;MACHE,GAAGwG,KAAKyM,KAAKjK,MAAMhJ,IAAI6S,cAAc7S,CAAlC;;AAIL,WAAO;MACLF,GAAG;QACD,CAACgF,UAAUyB,QAAX,GACEyM,eAAelT,EAAEgF,UAAUyB,QAA3B,KAAwCH,UAAUtG,MAAM;QAC1D,CAACgF,UAAU4B,OAAX,GACEsM,eAAelT,EAAEgF,UAAU4B,OAA3B,KAAuCN,UAAUtG,MAAM;;MAE3DE,GAAG;QACD,CAAC8E,UAAUyB,QAAX,GACEyM,eAAehT,EAAE8E,UAAUyB,QAA3B,KAAwCH,UAAUpG,MAAM;QAC1D,CAAC8E,UAAU4B,OAAX,GACEsM,eAAehT,EAAE8E,UAAU4B,OAA3B,KAAuCN,UAAUpG,MAAM;;;KAI/D,CAAC0R,UAAU1I,OAAO6J,aAAlB,CA5BgB;AA8BnB;SCjOeK,cACdC,gBACAC,IAAAA;AAEA,QAAMC,gBAAgBD,MAAM,OAAOD,eAAepL,IAAIqL,EAAnB,IAAyB3I;AAC5D,QAAMhI,OAAO4Q,gBAAgBA,cAAc5Q,KAAKiJ,UAAU;AAE1D,SAAOqH,YACJO,gBAAD;;AACE,QAAIF,MAAM,MAAM;AACd,aAAO;;AAMT,YAAA,OAAO3Q,QAAP,OAAOA,OAAQ6Q,eAAf,OAAA,OAA6B;KAE/B,CAAC7Q,MAAM2Q,EAAP,CAXgB;AAanB;SCjBeG,qBACdC,SACAC,qBAAAA;AAKA,aAAOxB,uBACL,MACEuB,QAAQhT,OAA2B,CAACkT,aAAaC,WAAd;AACjC,UAAM;MAACA,QAAQC;QAAUD;AAEzB,UAAME,mBAAmBD,OAAOnG,WAAWqG,IAAKlG,gBAAe;MAC7DlF,WAAWkF,UAAUlF;MACrBC,SAAS8K,oBAAoB7F,UAAUjF,SAASgL,MAApB;MAFL;AAKzB,WAAO,CAAC,GAAGD,aAAa,GAAGG,gBAApB;KACN,CAAA,CATH,GAUF,CAACL,SAASC,mBAAV,CAZY;AAcf;IChBWM;CAAZ,SAAYA,oBAAAA;AACVA,EAAAA,mBAAAA,mBAAAA,QAAAA,IAAAA,CAAAA,IAAA;AACAA,EAAAA,mBAAAA,mBAAAA,gBAAAA,IAAAA,CAAAA,IAAA;AACAA,EAAAA,mBAAAA,mBAAAA,eAAAA,IAAAA,CAAAA,IAAA;AACD,GAJWA,sBAAAA,oBAAiB,CAAA,EAA7B;AAMA,IAAYC;CAAZ,SAAYA,qBAAAA;AACVA,EAAAA,oBAAAA,WAAAA,IAAA;AACD,GAFWA,uBAAAA,qBAAkB,CAAA,EAA9B;AAYA,IAAMC,eAAwB,oBAAIC,IAAJ;AAE9B,SAAgBC,sBACdC,YAAAA,MAAAA;MACA;IAACC;IAAUC;IAAcC;;AAEzB,QAAM,CAACC,OAAOC,QAAR,QAAoBC,wBAAoC,IAA5B;AAClC,QAAM;IAACC;IAAWzN;IAAS0N;MAAYL;AACvC,QAAMM,oBAAgB9C,sBAAOqC,UAAD;AAC5B,QAAM1C,WAAWoD,WAAU;AAC3B,QAAMC,cAAcC,eAAetD,QAAD;AAClC,QAAMuD,iCAA6B5C,2BACjC,SAAC6C,MAAD;QAACA,SAAAA,QAAAA;AAAAA,MAAAA,OAA0B,CAAA;;AACzB,QAAIH,YAAYrJ,SAAS;AACvB;;AAGF+I,aAAUxR,WAAD;AACP,UAAIA,UAAU,MAAM;AAClB,eAAOiS;;AAGT,aAAOjS,MAAMkS,OAAOD,KAAIE,OAAQhC,QAAO,CAACnQ,MAAMS,SAAS0P,EAAf,CAApB,CAAb;KALD;KAQV,CAAC2B,WAAD,CAd4C;AAgB9C,QAAMxG,gBAAYwD,sBAA8B,IAAxB;AACxB,QAAMsD,iBAAiBtC,YACpBuC,mBAAD;AACE,QAAI5D,YAAY,CAAC2C,UAAU;AACzB,aAAOJ;;AAGT,QACE,CAACqB,iBACDA,kBAAkBrB,gBAClBY,cAAcnJ,YAAY0I,cAC1BI,SAAS,MACT;AACA,YAAMV,MAAe,oBAAII,IAAJ;AAErB,eAASqB,aAAanB,YAAY;AAChC,YAAI,CAACmB,WAAW;AACd;;AAGF,YACEf,SACAA,MAAMjR,SAAS,KACf,CAACiR,MAAM9Q,SAAS6R,UAAUnC,EAAzB,KACDmC,UAAUjV,KAAKoL,SACf;AAEAoI,cAAI0B,IAAID,UAAUnC,IAAImC,UAAUjV,KAAKoL,OAArC;AACA;;AAGF,cAAMjJ,OAAO8S,UAAU9S,KAAKiJ;AAC5B,cAAMpL,OAAOmC,OAAO,IAAI6E,KAAKJ,QAAQzE,IAAD,GAAQA,IAAxB,IAAgC;AAEpD8S,kBAAUjV,KAAKoL,UAAUpL;AAEzB,YAAIA,MAAM;AACRwT,cAAI0B,IAAID,UAAUnC,IAAI9S,IAAtB;;;AAIJ,aAAOwT;;AAGT,WAAOwB;KAET,CAAClB,YAAYI,OAAOH,UAAU3C,UAAUxK,OAAxC,CA7CgC;AAgDlCsL,+BAAU,MAAA;AACRqC,kBAAcnJ,UAAU0I;KACvB,CAACA,UAAD,CAFM;AAIT5B;IACE,MAAA;AACE,UAAId,UAAU;AACZ;;AAGFuD,iCAA0B;;;IAG5B,CAACZ,UAAU3C,QAAX;EATO;AAYTc;IACE,MAAA;AACE,UAAIgC,SAASA,MAAMjR,SAAS,GAAG;AAC7BkR,iBAAS,IAAD;;;;IAIZ,CAAC/B,KAAKC,UAAU6B,KAAf,CAAD;EAPO;AAUThC;IACE,MAAA;AACE,UACEd,YACA,OAAOiD,cAAc,YACrBpG,UAAU7C,YAAY,MACtB;AACA;;AAGF6C,gBAAU7C,UAAUJ,WAAW,MAAA;AAC7B2J,mCAA0B;AAC1B1G,kBAAU7C,UAAU;SACnBiJ,SAH2B;;;IAMhC,CAACA,WAAWjD,UAAUuD,4BAA4B,GAAGX,YAArD;EAhBO;AAmBT,SAAO;IACLe;IACAJ;IACAQ,oBAAoBjB,SAAS;;AAG/B,WAASM,aAAT;AACE,YAAQF,UAAR;MACE,KAAKb,kBAAkB2B;AACrB,eAAO;MACT,KAAK3B,kBAAkB4B;AACrB,eAAOtB;MACT;AACE,eAAO,CAACA;;;AAGf;SCpKeuB,gBAId3S,OACA4S,WAAAA;AAEA,SAAO9C,YACJuC,mBAAD;AACE,QAAI,CAACrS,OAAO;AACV,aAAO;;AAGT,QAAIqS,eAAe;AACjB,aAAOA;;AAGT,WAAO,OAAOO,cAAc,aAAaA,UAAU5S,KAAD,IAAUA;KAE9D,CAAC4S,WAAW5S,KAAZ,CAZgB;AAcnB;SCtBe6S,eACdrT,MACAyE,SAAAA;AAEA,SAAO0O,gBAAgBnT,MAAMyE,OAAP;AACvB;ACID,SAAgB6O,oBAAAA,MAAAA;MAAoB;IAACC;IAAUtE;;AAC7C,QAAMuE,kBAAkBC,SAASF,QAAD;AAChC,QAAMG,uBAAmBlE,uBAAQ,MAAA;AAC/B,QACEP,YACA,OAAOpN,WAAW,eAClB,OAAOA,OAAO8R,qBAAqB,aACnC;AACA,aAAO3L;;AAGT,UAAM;MAAC2L;QAAoB9R;AAE3B,WAAO,IAAI8R,iBAAiBH,eAArB;KACN,CAACA,iBAAiBvE,QAAlB,CAZ6B;AAchCc,+BAAU,MAAA;AACR,WAAO,MAAM2D,oBAAN,OAAA,SAAMA,iBAAkBE,WAAlB;KACZ,CAACF,gBAAD,CAFM;AAIT,SAAOA;AACR;ACrBD,SAAgBG,kBAAAA,MAAAA;MAAkB;IAACN;IAAUtE;;AAC3C,QAAM6E,eAAeL,SAASF,QAAD;AAC7B,QAAMQ,qBAAiBvE;IACrB,MAAA;AACE,UACEP,YACA,OAAOpN,WAAW,eAClB,OAAOA,OAAOmS,mBAAmB,aACjC;AACA,eAAOhM;;AAGT,YAAM;QAACgM;UAAkBnS;AAEzB,aAAO,IAAImS,eAAeF,YAAnB;;;IAGT,CAAC7E,QAAD;EAf4B;AAkB9Bc,+BAAU,MAAA;AACR,WAAO,MAAMgE,kBAAN,OAAA,SAAMA,eAAgBH,WAAhB;KACZ,CAACG,cAAD,CAFM;AAIT,SAAOA;AACR;AC5BD,SAASE,eAAe3U,SAAxB;AACE,SAAO,IAAIuF,KAAKxF,cAAcC,OAAD,GAAWA,OAAjC;AACR;AAED,SAAgB4U,QACd5U,SACAmF,SACA0P,cAAAA;MADA1P,YAAAA,QAAAA;AAAAA,cAAgDwP;;AAGhD,QAAM,CAACpW,MAAMuW,OAAP,QAAkBnC,wBAA4B,IAApB;AAEhC,WAASoC,cAAT;AACED,YAASE,iBAAD;AACN,UAAI,CAAChV,SAAS;AACZ,eAAO;;AAGT,UAAIA,QAAQiV,gBAAgB,OAAO;AAAA,YAAA;AAGjC,gBAAA,OAAOD,eAAP,OAAOA,cAAeH,iBAAtB,OAAA,OAAsC;;AAGxC,YAAMK,UAAU/P,QAAQnF,OAAD;AAEvB,UAAI2Q,KAAKC,UAAUoE,WAAf,MAAgCrE,KAAKC,UAAUsE,OAAf,GAAyB;AAC3D,eAAOF;;AAGT,aAAOE;KAjBF;;AAqBT,QAAMd,mBAAmBJ,oBAAoB;IAC3CC,SAASkB,SAAD;AACN,UAAI,CAACnV,SAAS;AACZ;;AAGF,iBAAWoV,UAAUD,SAAS;AAC5B,cAAM;UAACE;UAAMhP;YAAU+O;AAEvB,YACEC,SAAS,eACThP,kBAAkBiP,eAClBjP,OAAOkP,SAASvV,OAAhB,GACA;AACA+U,sBAAW;AACX;;;;GAfoC;AAoB5C,QAAMN,iBAAiBF,kBAAkB;IAACN,UAAUc;GAAZ;AAExCS,4BAA0B,MAAA;AACxBT,gBAAW;AAEX,QAAI/U,SAAS;AACXyU,wBAAc,OAAd,SAAAA,eAAgBgB,QAAQzV,OAAxB;AACAoU,0BAAgB,OAAhB,SAAAA,iBAAkBqB,QAAQxS,SAASyS,MAAM;QACvCC,WAAW;QACXC,SAAS;OAFX;WAIK;AACLnB,wBAAc,OAAd,SAAAA,eAAgBH,WAAhB;AACAF,0BAAgB,OAAhB,SAAAA,iBAAkBE,WAAlB;;KAED,CAACtU,OAAD,CAbsB;AAezB,SAAOzB;AACR;SC3EesX,aAAatX,MAAAA;AAC3B,QAAMuX,cAAcjC,gBAAgBtV,IAAD;AAEnC,SAAOT,aAAaS,MAAMuX,WAAP;AACpB;ACJD,IAAM5D,iBAA0B,CAAA;AAEhC,SAAgB6D,uBAAuBrV,MAAAA;AACrC,QAAMsV,mBAAehG,sBAAOtP,IAAD;AAE3B,QAAMuV,YAAYjF,YACfuC,mBAAD;AACE,QAAI,CAAC7S,MAAM;AACT,aAAOwR;;AAGT,QACEqB,iBACAA,kBAAkBrB,kBAClBxR,QACAsV,aAAarM,WACbjJ,KAAKqB,eAAeiU,aAAarM,QAAQ5H,YACzC;AACA,aAAOwR;;AAGT,WAAOnS,uBAAuBV,IAAD;KAE/B,CAACA,IAAD,CAlB2B;AAqB7B+P,+BAAU,MAAA;AACRuF,iBAAarM,UAAUjJ;KACtB,CAACA,IAAD,CAFM;AAIT,SAAOuV;AACR;SCvBeC,iBAAiBC,UAAAA;AAC/B,QAAM,CACJC,mBACAC,oBAFI,QAGF1D,wBAAmC,IAA3B;AACZ,QAAM2D,mBAAetG,sBAAOmG,QAAD;AAG3B,QAAMI,mBAAejG,2BAAa9I,WAAD;AAC/B,UAAM9F,mBAAmBQ,qBAAqBsF,MAAMnB,MAAP;AAE7C,QAAI,CAAC3E,kBAAkB;AACrB;;AAGF2U,yBAAsBD,CAAAA,uBAAD;AACnB,UAAI,CAACA,oBAAmB;AACtB,eAAO;;AAGTA,MAAAA,mBAAkB3C,IAChB/R,kBACAoB,qBAAqBpB,gBAAD,CAFtB;AAKA,aAAO,IAAIyQ,IAAIiE,kBAAR;KAVW;KAYnB,CAAA,CAnB6B;AAqBhC3F,+BAAU,MAAA;AACR,UAAM+F,mBAAmBF,aAAa3M;AAEtC,QAAIwM,aAAaK,kBAAkB;AACjCC,cAAQD,gBAAD;AAEP,YAAME,UAAUP,SACbpE,IAAK/R,aAAD;AACH,cAAM2W,oBAAoBzU,qBAAqBlC,OAAD;AAE9C,YAAI2W,mBAAmB;AACrBA,4BAAkB9P,iBAAiB,UAAU0P,cAAc;YACzDtJ,SAAS;WADX;AAIA,iBAAO,CACL0J,mBACA7T,qBAAqB6T,iBAAD,CAFf;;AAMT,eAAO;OAfK,EAiBbtD,OAEGuD,WAIGA,SAAS,IAvBF;AA0BhBP,2BAAqBK,QAAQlV,SAAS,IAAI2Q,IAAIuE,OAAR,IAAmB,IAArC;AAEpBJ,mBAAa3M,UAAUwM;;AAGzB,WAAO,MAAA;AACLM,cAAQN,QAAD;AACPM,cAAQD,gBAAD;;AAGT,aAASC,QAAQN,WAAjB;AACEA,MAAAA,UAAS3P,QAASxG,aAAD;AACf,cAAM2W,oBAAoBzU,qBAAqBlC,OAAD;AAE9C2W,6BAAiB,OAAjB,SAAAA,kBAAmBjQ,oBAAoB,UAAU6P,YAAjD;OAHF;;KAMD,CAACA,cAAcJ,QAAf,CAjDM;AAmDT,aAAOjG,uBAAQ,MAAA;AACb,QAAIiG,SAAS3U,QAAQ;AACnB,aAAO4U,oBACHS,MAAMC,KAAKV,kBAAkBW,OAAlB,CAAX,EAAuCtY,OACrC,CAACC,KAAK2M,gBAAgBtG,IAAIrG,KAAK2M,WAAN,GACzBlN,kBAFF,IAIA0G,iBAAiBsR,QAAD;;AAGtB,WAAOhY;KACN,CAACgY,UAAUC,iBAAX,CAXW;AAYf;SCpGeY,sBACdvR,eACA8M,cAAAA;MAAAA,iBAAAA,QAAAA;AAAAA,mBAAsB,CAAA;;AAEtB,QAAM0E,2BAAuBjH,sBAA2B,IAArB;AAEnCS;IACE,MAAA;AACEwG,2BAAqBtN,UAAU;;;IAGjC4I;EALO;AAQT9B,+BAAU,MAAA;AACR,UAAMyG,mBAAmBzR,kBAAkBtH;AAE3C,QAAI+Y,oBAAoB,CAACD,qBAAqBtN,SAAS;AACrDsN,2BAAqBtN,UAAUlE;;AAGjC,QAAI,CAACyR,oBAAoBD,qBAAqBtN,SAAS;AACrDsN,2BAAqBtN,UAAU;;KAEhC,CAAClE,aAAD,CAVM;AAYT,SAAOwR,qBAAqBtN,UACxBwN,SAAS1R,eAAewR,qBAAqBtN,OAArC,IACRxL;AACL;SC7BeiZ,eAAe3F,SAAAA;AAC7BhB;IACE,MAAA;AACE,UAAI,CAACtO,WAAW;AACd;;AAGF,YAAMkV,cAAc5F,QAAQM,IAAI,UAAA;AAAA,YAAC;UAACH;YAAF;AAAA,eAAcA,OAAOnD,SAArB,OAAA,SAAcmD,OAAOnD,MAAP;OAA1B;AAEpB,aAAO,MAAA;AACL,mBAAWE,YAAY0I,aAAa;AAClC1I,sBAAQ,OAAR,SAAAA,SAAQ;;;;;;IAMd8C,QAAQM,IAAI,WAAA;AAAA,UAAC;QAACH;UAAF;AAAA,aAAcA;KAA1B;EAhBO;AAkBV;SCXe0F,sBACdhR,WACA+K,IAAAA;AAEA,aAAOnB,uBAAQ,MAAA;AACb,WAAO5J,UAAU7H,OACf,CAACC,KAAD,SAAA;UAAM;QAACiI;QAAWC;;AAChBlI,UAAIiI,SAAD,IAAea,WAAD;AACfZ,gBAAQY,OAAO6J,EAAR;;AAGT,aAAO3S;OAET,CAAA,CARK;KAUN,CAAC4H,WAAW+K,EAAZ,CAXW;AAYf;SCzBekG,cAAcvX,SAAAA;AAC5B,aAAOkQ,uBAAQ,MAAOlQ,UAAUM,oBAAoBN,OAAD,IAAY,MAAO,CACpEA,OADoE,CAAxD;AAGf;ACED,IAAMkS,iBAAuB,CAAA;AAE7B,SAAgBsF,SACdrB,UACAhR,SAAAA;MAAAA,YAAAA,QAAAA;AAAAA,cAA4CpF;;AAE5C,QAAM,CAAC0X,YAAD,IAAiBtB;AACvB,QAAMuB,aAAaH,cACjBE,eAAetX,UAAUsX,YAAD,IAAiB,IADX;AAGhC,QAAM,CAACE,OAAOC,QAAR,QAAoBjF,wBAAuBT,cAAf;AAElC,WAAS2F,eAAT;AACED,aAAS,MAAA;AACP,UAAI,CAACzB,SAAS3U,QAAQ;AACpB,eAAO0Q;;AAGT,aAAOiE,SAASpE,IAAK/R,aACnBgD,2BAA2BhD,OAAD,IACrB0X,aACD,IAAInS,KAAKJ,QAAQnF,OAAD,GAAWA,OAA3B,CAHC;KALD;;AAaV,QAAMyU,iBAAiBF,kBAAkB;IAACN,UAAU4D;GAAZ;AAExCrC,4BAA0B,MAAA;AACxBf,sBAAc,OAAd,SAAAA,eAAgBH,WAAhB;AACAuD,iBAAY;AACZ1B,aAAS3P,QAASxG,aAAYyU,kBAAb,OAAA,SAAaA,eAAgBgB,QAAQzV,OAAxB,CAA9B;KACC,CAACmW,QAAD,CAJsB;AAMzB,SAAOwB;AACR;SC3CeG,kBACdpX,MAAAA;AAEA,MAAI,CAACA,MAAM;AACT,WAAO;;AAGT,MAAIA,KAAKqX,SAASvW,SAAS,GAAG;AAC5B,WAAOd;;AAET,QAAMsX,aAAatX,KAAKqX,SAAS,CAAd;AAEnB,SAAOlW,cAAcmW,UAAD,IAAeA,aAAatX;AACjD;SCHeuX,wBAAAA,MAAAA;MAAwB;IACtC9S;;AAEA,QAAM,CAAC5G,MAAMuW,OAAP,QAAkBnC,wBAA4B,IAApB;AAChC,QAAM6B,mBAAelE,2BAClBoG,aAAD;AACE,eAAW;MAACrQ;SAAWqQ,SAAS;AAC9B,UAAI7U,cAAcwE,MAAD,GAAU;AACzByO,gBAASvW,CAAAA,UAAD;AACN,gBAAM2W,UAAU/P,QAAQkB,MAAD;AAEvB,iBAAO9H,QACH;YAAC,GAAGA;YAAMZ,OAAOuX,QAAQvX;YAAOE,QAAQqX,QAAQrX;cAChDqX;SALC;AAOP;;;KAIN,CAAC/P,OAAD,CAf8B;AAiBhC,QAAMsP,iBAAiBF,kBAAkB;IAACN,UAAUO;GAAZ;AACxC,QAAM0D,uBAAmB5H,2BACtBtQ,aAAD;AACE,UAAMU,OAAOoX,kBAAkB9X,OAAD;AAE9ByU,sBAAc,OAAd,SAAAA,eAAgBH,WAAhB;AAEA,QAAI5T,MAAM;AACR+T,wBAAc,OAAd,SAAAA,eAAgBgB,QAAQ/U,IAAxB;;AAGFoU,YAAQpU,OAAOyE,QAAQzE,IAAD,IAAS,IAAxB;KAET,CAACyE,SAASsP,cAAV,CAZkC;AAcpC,QAAM,CAAC0D,SAASC,MAAV,IAAoBC,WAAWH,gBAAD;AAEpC,aAAOhI,uBACL,OAAO;IACLiI;IACA5Z;IACA6Z;MAEF,CAAC7Z,MAAM4Z,SAASC,MAAhB,CANY;AAQf;AC9CM,IAAME,iBAAiB,CAC5B;EAAC1G,QAAQ1D;EAAejO,SAAS,CAAA;AAAjC,GACA;EAAC2R,QAAQjJ;EAAgB1I,SAAS,CAAA;AAAlC,CAF4B;AAKvB,IAAMsY,cAAuB;EAAC5O,SAAS,CAAA;AAAV;AAE7B,IAAM6O,gCAAsE;EACjFC,WAAW;IACTtT,SAAS9E;;EAEXqY,WAAW;IACTvT,SAAS9E;IACTwS,UAAUb,kBAAkB2G;IAC5B/F,WAAWX,mBAAmB2G;;EAEhCC,aAAa;IACX1T,SAASpF;;AAVsE;ICdtE+Y,uCAA+B3G,IAAAA;EAI1CnM,IAAIqL,IAAD;;AACD,WAAOA,MAAM,QAAN,aAAa,MAAMrL,IAAIqL,EAAV,MAAb,OAAA,aAA8B3I,SAAYA;;EAGnDqQ,UAAO;AACL,WAAOlC,MAAMC,KAAK,KAAKC,OAAL,CAAX;;EAGTiC,aAAU;AACR,WAAO,KAAKD,QAAL,EAAe1F,OAAO,UAAA;AAAA,UAAC;QAAC1D;UAAF;AAAA,aAAgB,CAACA;KAAvC;;EAGTsJ,WAAW5H,IAAD;;AACR,YAAA,yBAAA,YAAO,KAAKrL,IAAIqL,EAAT,MAAP,OAAA,SAAO,UAAc3Q,KAAKiJ,YAA1B,OAAA,wBAAqCjB;;;ACflC,IAAMwQ,uBAAgD;EAC3DC,gBAAgB;EAChBtP,QAAQ;EACRJ,YAAY;EACZ2P,gBAAgB;EAChBC,YAAY;EACZC,mBAAmB;EACnBlI,gBAAgB,oBAAIe,IAAJ;EAChBmB,gBAAgB,oBAAInB,IAAJ;EAChBoH,qBAAqB,IAAIT,uBAAJ;EACrBU,MAAM;EACNX,aAAa;IACXV,SAAS;MACPxO,SAAS;;IAEXpL,MAAM;IACN6Z,QAAQ1J;;EAEV5J,qBAAqB,CAAA;EACrB0K,yBAAyB,CAAA;EACzBiK,wBAAwBjB;EACxBtF,4BAA4BxE;EAC5BgJ,YAAY;EACZhE,oBAAoB;AAvBuC;AA0BtD,IAAMgG,yBAAoD;EAC/DP,gBAAgB;EAChBzN,YAAY,CAAA;EACZ7B,QAAQ;EACRuP,gBAAgB;EAChBO,mBAAmB;IACjBlB,WAAW;;EAEbmB,UAAUlL;EACV0C,gBAAgB,oBAAIe,IAAJ;EAChBqH,MAAM;EACNtG,4BAA4BxE;AAXmC;AAc1D,IAAMmL,sBAAkBC,6BAC7BJ,sBAD0C;AAIrC,IAAMK,oBAAgBD,6BAC3BZ,oBADwC;SC/C1Bc,kBAAAA;AACd,SAAO;IACLvB,WAAW;MACT5O,QAAQ;MACR0C,oBAAoB;QAACxO,GAAG;QAAGE,GAAG;;MAC9Bgc,OAAO,oBAAI9H,IAAJ;MACP+H,WAAW;QAACnc,GAAG;QAAGE,GAAG;;;IAEvBya,WAAW;MACTrG,YAAY,IAAIyG,uBAAJ;;;AAGjB;AAED,SAAgBqB,QAAQC,OAAcC,QAAAA;AACpC,UAAQA,OAAOhF,MAAf;IACE,KAAKiF,OAAOpN;AACV,aAAO;QACL,GAAGkN;QACH3B,WAAW;UACT,GAAG2B,MAAM3B;UACTlM,oBAAoB8N,OAAO9N;UAC3B1C,QAAQwQ,OAAOxQ;;;IAGrB,KAAKyQ,OAAOC;AACV,UAAIH,MAAM3B,UAAU5O,UAAU,MAAM;AAClC,eAAOuQ;;AAGT,aAAO;QACL,GAAGA;QACH3B,WAAW;UACT,GAAG2B,MAAM3B;UACTyB,WAAW;YACTnc,GAAGsc,OAAOhP,YAAYtN,IAAIqc,MAAM3B,UAAUlM,mBAAmBxO;YAC7DE,GAAGoc,OAAOhP,YAAYpN,IAAImc,MAAM3B,UAAUlM,mBAAmBtO;;;;IAIrE,KAAKqc,OAAOE;IACZ,KAAKF,OAAOG;AACV,aAAO;QACL,GAAGL;QACH3B,WAAW;UACT,GAAG2B,MAAM3B;UACT5O,QAAQ;UACR0C,oBAAoB;YAACxO,GAAG;YAAGE,GAAG;;UAC9Bic,WAAW;YAACnc,GAAG;YAAGE,GAAG;;;;IAI3B,KAAKqc,OAAOI,mBAAmB;AAC7B,YAAM;QAAC1a;UAAWqa;AAClB,YAAM;QAAChJ;UAAMrR;AACb,YAAMqS,aAAa,IAAIyG,uBAAuBsB,MAAM1B,UAAUrG,UAA3C;AACnBA,iBAAWoB,IAAIpC,IAAIrR,OAAnB;AAEA,aAAO;QACL,GAAGoa;QACH1B,WAAW;UACT,GAAG0B,MAAM1B;UACTrG;;;;IAKN,KAAKiI,OAAOK,sBAAsB;AAChC,YAAM;QAACtJ;QAAIxL,KAAAA;QAAK8J;UAAY0K;AAC5B,YAAMra,UAAUoa,MAAM1B,UAAUrG,WAAWrM,IAAIqL,EAA/B;AAEhB,UAAI,CAACrR,WAAW6F,SAAQ7F,QAAQ6F,KAAK;AACnC,eAAOuU;;AAGT,YAAM/H,aAAa,IAAIyG,uBAAuBsB,MAAM1B,UAAUrG,UAA3C;AACnBA,iBAAWoB,IAAIpC,IAAI;QACjB,GAAGrR;QACH2P;OAFF;AAKA,aAAO;QACL,GAAGyK;QACH1B,WAAW;UACT,GAAG0B,MAAM1B;UACTrG;;;;IAKN,KAAKiI,OAAOM,qBAAqB;AAC/B,YAAM;QAACvJ;QAAIxL,KAAAA;UAAOwU;AAClB,YAAMra,UAAUoa,MAAM1B,UAAUrG,WAAWrM,IAAIqL,EAA/B;AAEhB,UAAI,CAACrR,WAAW6F,SAAQ7F,QAAQ6F,KAAK;AACnC,eAAOuU;;AAGT,YAAM/H,aAAa,IAAIyG,uBAAuBsB,MAAM1B,UAAUrG,UAA3C;AACnBA,iBAAWwI,OAAOxJ,EAAlB;AAEA,aAAO;QACL,GAAG+I;QACH1B,WAAW;UACT,GAAG0B,MAAM1B;UACTrG;;;;IAKN,SAAS;AACP,aAAO+H;;;AAGZ;SCzGeU,aAAAA,MAAAA;MAAa;IAACnL;;AAC5B,QAAM;IAAC9F;IAAQsP;IAAgB/H;UAAkB2J,0BAAWlB,eAAD;AAC3D,QAAMmB,yBAAyBjK,YAAYoI,cAAD;AAC1C,QAAM8B,mBAAmBlK,YAAYlH,UAAD,OAAA,SAACA,OAAQwH,EAAT;AAGpCZ,+BAAU,MAAA;AACR,QAAId,UAAU;AACZ;;AAGF,QAAI,CAACwJ,kBAAkB6B,0BAA0BC,oBAAoB,MAAM;AACzE,UAAI,CAACrR,gBAAgBoR,sBAAD,GAA0B;AAC5C;;AAGF,UAAI/X,SAASiY,kBAAkBF,uBAAuB3U,QAAQ;AAE5D;;AAGF,YAAMiL,gBAAgBF,eAAepL,IAAIiV,gBAAnB;AAEtB,UAAI,CAAC3J,eAAe;AAClB;;AAGF,YAAM;QAACxF;QAAepL;UAAQ4Q;AAE9B,UAAI,CAACxF,cAAcnC,WAAW,CAACjJ,KAAKiJ,SAAS;AAC3C;;AAGFwR,4BAAsB,MAAA;AACpB,mBAAWnb,WAAW,CAAC8L,cAAcnC,SAASjJ,KAAKiJ,OAA7B,GAAuC;AAC3D,cAAI,CAAC3J,SAAS;AACZ;;AAGF,gBAAMob,gBAAgBC,uBAAuBrb,OAAD;AAE5C,cAAIob,eAAe;AACjBA,0BAAcE,MAAd;AACA;;;OAVe;;KAetB,CACDnC,gBACAxJ,UACAyB,gBACA6J,kBACAD,sBALC,CA1CM;AAkDT,SAAO;AACR;SClEeO,eACdC,WAAAA,MAAAA;MACA;IAACje;IAAW,GAAGke;;AAEf,SAAOD,aAAS,QAATA,UAAWha,SACdga,UAAU/c,OAAkB,CAACkT,aAAatT,aAAd;AAC1B,WAAOA,SAAS;MACdd,WAAWoU;MACX,GAAG8J;KAFU;KAIdle,SALH,IAMAA;AACL;SCVeme,0BACdlJ,QAAAA;AAEA,aAAOtC;IACL,OAAO;MACLuI,WAAW;QACT,GAAGD,8BAA8BC;QACjC,GAAGjG,UAAH,OAAA,SAAGA,OAAQiG;;MAEbC,WAAW;QACT,GAAGF,8BAA8BE;QACjC,GAAGlG,UAAH,OAAA,SAAGA,OAAQkG;;MAEbG,aAAa;QACX,GAAGL,8BAA8BK;QACjC,GAAGrG,UAAH,OAAA,SAAGA,OAAQqG;;;;IAIf,CAACrG,UAAD,OAAA,SAACA,OAAQiG,WAAWjG,UAApB,OAAA,SAAoBA,OAAQkG,WAAWlG,UAAvC,OAAA,SAAuCA,OAAQqG,WAA/C;EAhBY;AAkBf;SCXe8C,iCAAAA,MAAAA;MAAiC;IAC/ClS;IACAtE;IACA2Q;IACAtD,SAAS;;AAET,QAAMoJ,kBAAc5L,sBAAO,KAAD;AAC1B,QAAM;IAACjS;IAAGE;MAAK,OAAOuU,WAAW,YAAY;IAACzU,GAAGyU;IAAQvU,GAAGuU;MAAUA;AAEtEgD,4BAA0B,MAAA;AACxB,UAAM7F,WAAW,CAAC5R,KAAK,CAACE;AAExB,QAAI0R,YAAY,CAAClG,YAAY;AAC3BmS,kBAAYjS,UAAU;AACtB;;AAGF,QAAIiS,YAAYjS,WAAW,CAACmM,aAAa;AAGvC;;AAIF,UAAMpV,OAAO+I,cAAH,OAAA,SAAGA,WAAY/I,KAAKiJ;AAE9B,QAAI,CAACjJ,QAAQA,KAAKuU,gBAAgB,OAAO;AAGvC;;AAGF,UAAM1W,OAAO4G,QAAQzE,IAAD;AACpB,UAAMmb,YAAY/d,aAAaS,MAAMuX,WAAP;AAE9B,QAAI,CAAC/X,GAAG;AACN8d,gBAAU9d,IAAI;;AAGhB,QAAI,CAACE,GAAG;AACN4d,gBAAU5d,IAAI;;AAIhB2d,gBAAYjS,UAAU;AAEtB,QAAIlF,KAAKC,IAAImX,UAAU9d,CAAnB,IAAwB,KAAK0G,KAAKC,IAAImX,UAAU5d,CAAnB,IAAwB,GAAG;AAC1D,YAAMgE,0BAA0BD,2BAA2BtB,IAAD;AAE1D,UAAIuB,yBAAyB;AAC3BA,gCAAwBiJ,SAAS;UAC/BhN,KAAK2d,UAAU5d;UACfD,MAAM6d,UAAU9d;SAFlB;;;KAMH,CAAC0L,YAAY1L,GAAGE,GAAG6X,aAAa3Q,OAAhC,CA/CsB;AAgD1B;ACoDM,IAAM2W,6BAAyBhC,6BAAyB;EAC7D,GAAG3b;EACHT,QAAQ;EACRE,QAAQ;AAHqD,CAAZ;AAMnD,IAAKme;CAAL,SAAKA,SAAAA;AACHA,EAAAA,QAAAA,QAAAA,eAAAA,IAAAA,CAAAA,IAAA;AACAA,EAAAA,QAAAA,QAAAA,cAAAA,IAAAA,CAAAA,IAAA;AACAA,EAAAA,QAAAA,QAAAA,aAAAA,IAAAA,CAAAA,IAAA;AACD,GAJIA,WAAAA,SAAM,CAAA,EAAX;AAMA,IAAaC,iBAAaC,oBAAK,SAASD,YAAT,MAAA;;MAAoB;IACjD3K;IACA6K;IACA7L,aAAa;IACb0H;IACAtG,UAAU6G;IACV6D,qBAAqBC;IACrBC;IACAb;IACA,GAAG5S;;AAEH,QAAM0T,YAAQC,0BAAWpC,SAASzR,QAAWsR,eAArB;AACxB,QAAM,CAACI,OAAOR,QAAR,IAAoB0C;AAC1B,QAAM,CAACE,sBAAsBC,uBAAvB,IACJC,sBAAqB;AACvB,QAAM,CAACC,QAAQC,SAAT,QAAsBjK,wBAAiBoJ,OAAOc,aAAhB;AACpC,QAAMC,gBAAgBH,WAAWZ,OAAOgB;AACxC,QAAM;IACJtE,WAAW;MAAC5O,QAAQmT;MAAU/C,OAAO7I;MAAgB8I;;IACrDxB,WAAW;MAACrG,YAAYkH;;MACtBa;AACJ,QAAM1Z,OAAOsc,YAAY,OAAO5L,eAAepL,IAAIgX,QAAnB,IAA+B;AAC/D,QAAMC,kBAAcjN,sBAAkC;IACpDkN,SAAS;IACTC,YAAY;GAFY;AAI1B,QAAMtT,aAASqG,uBACb,MAAA;AAAA,QAAA;AAAA,WACE8M,YAAY,OACR;MACE3L,IAAI2L;;MAEJI,OAAI,aAAE1c,QAAF,OAAA,SAAEA,KAAM0c,SAAR,OAAA,aAAgB7E;MACpBha,MAAM0e;QAER;KACN,CAACD,UAAUtc,IAAX,CAVoB;AAYtB,QAAM2c,gBAAYrN,sBAAgC,IAA1B;AACxB,QAAM,CAACsN,cAAcC,eAAf,QAAkC5K,wBAAgC,IAAxB;AAChD,QAAM,CAACwG,gBAAgBqE,iBAAjB,QAAsC7K,wBAAuB,IAAf;AACpD,QAAM8K,cAAcxK,eAAerK,OAAO9C,OAAOiR,OAAOnO,KAAd,CAAR;AAClC,QAAM8U,yBAAyBC,YAAW,kBAAmBtM,EAAnB;AAC1C,QAAMuM,iCAA6B1N,uBACjC,MAAMqJ,oBAAoBP,WAApB,GACN,CAACO,mBAAD,CAFwC;AAI1C,QAAME,yBAAyBiC,0BAA0BW,SAAD;AACxD,QAAM;IAAC/I;IAAgBJ;IAA4BQ;MACjDtB,sBAAsBwL,4BAA4B;IAChDtL,UAAUwK;IACVvK,cAAc,CAAC2H,UAAUnc,GAAGmc,UAAUjc,CAAxB;IACduU,QAAQiH,uBAAuBf;GAHZ;AAKvB,QAAMjP,aAAa0H,cAAcC,gBAAgB4L,QAAjB;AAChC,QAAMa,4BAAwB3N,uBAC5B,MAAOiJ,iBAAiBzM,oBAAoByM,cAAD,IAAmB,MAC9D,CAACA,cAAD,CAFmC;AAIrC,QAAM2E,oBAAoBC,uBAAsB;AAChD,QAAMC,wBAAwBjK,eAC5BtK,YACAgQ,uBAAuBhB,UAAUtT,OAFS;AAK5CwW,mCAAiC;IAC/BlS,YAAYuT,YAAY,OAAO5L,eAAepL,IAAIgX,QAAnB,IAA+B;IAC9DxK,QAAQsL,kBAAkBG;IAC1BnI,aAAakI;IACb7Y,SAASsU,uBAAuBhB,UAAUtT;GAJZ;AAOhC,QAAMiU,iBAAiBxE,QACrBnL,YACAgQ,uBAAuBhB,UAAUtT,SACjC6Y,qBAH4B;AAK9B,QAAM1E,oBAAoB1E,QACxBnL,aAAaA,WAAWyU,gBAAgB,IADT;AAGjC,QAAMC,oBAAgBnO,sBAAsB;IAC1CmJ,gBAAgB;IAChBtP,QAAQ;IACRJ;IACAU,eAAe;IACfkP,YAAY;IACZ/F;IACAlC;IACAgN,cAAc;IACdC,kBAAkB;IAClB9E;IACAC,MAAM;IACN1U,qBAAqB,CAAA;IACrBwZ,yBAAyB;GAbC;AAe5B,QAAMC,WAAWhF,oBAAoBN,YAApB,wBACfkF,cAAcxU,QAAQ6P,SADP,OAAA,SACf,sBAA4BnI,EADb;AAGjB,QAAMwH,cAAcZ,wBAAwB;IAC1C9S,SAASsU,uBAAuBZ,YAAY1T;GADH;AAK3C,QAAMiZ,gBAAY,wBAAGvF,YAAYV,QAAQxO,YAAvB,OAAA,wBAAkCF;AACpD,QAAM4U,mBAAmBvB,iBAAa,oBAClCjE,YAAYta,SADsB,OAAA,oBACd6a,iBACpB;AACJ,QAAMoF,kBAAkBvS,QACtB4M,YAAYV,QAAQxO,WAAWkP,YAAYta,IADd;AAK/B,QAAMkgB,gBAAgB5I,aAAa2I,kBAAkB,OAAOpF,cAA1B;AAGlC,QAAM1B,aAAaH,cACjB6G,eAAeje,UAAUie,YAAD,IAAiB,IADX;AAKhC,QAAMtZ,sBAAsBiR,uBAC1B+G,gBAAgByB,YAAH,OAAGA,WAAY9U,aAAa,IADO;AAGlD,QAAM+F,0BAA0BgI,SAAS1S,mBAAD;AAGxC,QAAM4Z,oBAAoBnD,eAAeC,WAAW;IAClDje,WAAW;MACTQ,GAAGmc,UAAUnc,IAAI0gB,cAAc1gB;MAC/BE,GAAGic,UAAUjc,IAAIwgB,cAAcxgB;MAC/BP,QAAQ;MACRE,QAAQ;;IAEVub;IACAtP;IACAuP;IACAE;IACA+E;IACA7E,MAAM2E,cAAcxU,QAAQ6P;IAC5BmF,iBAAiB9F,YAAYta;IAC7BuG;IACA0K;IACAkI;GAhBsC;AAmBxC,QAAMnI,qBAAqBsO,wBACvB9Y,IAAI8Y,uBAAuB3D,SAAxB,IACH;AAEJ,QAAMzU,gBAAgByQ,iBAAiBpR,mBAAD;AAEtC,QAAM8Z,mBAAmB5H,sBAAsBvR,aAAD;AAE9C,QAAMoZ,wBAAwB7H,sBAAsBvR,eAAe,CACjE2T,cADiE,CAAhB;AAInD,QAAMkF,0BAA0BvZ,IAAI2Z,mBAAmBE,gBAApB;AAEnC,QAAMzU,gBAAgBkU,mBAClBvf,gBAAgBuf,kBAAkBK,iBAAnB,IACf;AAEJ,QAAMrF,aACJxP,UAAUM,gBACNgS,mBAAmB;IACjBtS;IACAM;IACAmJ;IACAiG,qBAAqBqE;IACrBrO;GALgB,IAOlB;AACN,QAAMuP,SAASC,kBAAkB1F,YAAY,IAAb;AAChC,QAAM,CAACG,MAAMwF,OAAP,QAAkBrM,wBAAsB,IAAd;AAIhC,QAAMsM,mBAAmBT,kBACrBE,oBACA3Z,IAAI2Z,mBAAmBG,qBAApB;AAEP,QAAMthB,YAAYD,YAChB2hB,mBAD2B,aAE3BzF,QAF2B,OAAA,SAE3BA,KAAMjb,SAFqB,OAAA,aAEb,MACd6a,cAH2B;AAM7B,QAAM8F,sBAAkBlP,sBAA8B,IAAxB;AAC9B,QAAMmP,wBAAoB7O;IACxB,CACE9I,OADF,UAAA;UAEE;QAACoK,QAAQC;QAAQ5R;;AAEjB,UAAIod,UAAU1T,WAAW,MAAM;AAC7B;;AAGF,YAAMF,cAAa2H,eAAepL,IAAIqX,UAAU1T,OAA7B;AAEnB,UAAI,CAACF,aAAY;AACf;;AAGF,YAAM0P,kBAAiB3R,MAAMoE;AAE7B,YAAMwT,iBAAiB,IAAIvN,OAAO;QAChChI,QAAQwT,UAAU1T;QAClBF,YAAAA;QACAjC,OAAO2R;QACPlZ;;;QAGA6J,SAASqU;QACTpQ,QAAQsD,KAAD;AACL,gBAAMC,gBAAgBF,eAAepL,IAAIqL,GAAnB;AAEtB,cAAI,CAACC,eAAe;AAClB;;AAGF,gBAAM;YAAC+N;cAAe5B,YAAY9T;AAClC,gBAAMnC,SAAwB;YAAC6J,IAAAA;;AAC/BgO,yBAAW,OAAX,SAAAA,YAAc7X,MAAH;AACXgV,+BAAqB;YAACnH,MAAM;YAAe7N,OAAAA;WAAvB;;QAEtBgG,UAAU6D,KAAIrF,YAAYO,oBAAoBgB,QAArC;AACP,gBAAM+D,gBAAgBF,eAAepL,IAAIqL,GAAnB;AAEtB,cAAI,CAACC,eAAe;AAClB;;AAGF,gBAAM;YAACgO;cAAiB7B,YAAY9T;AACpC,gBAAMnC,SAA0B;YAC9B6J,IAAAA;YACArF;YACAO;YACAgB;;AAGF+R,2BAAa,OAAb,SAAAA,cAAgB9X,MAAH;AACbgV,+BAAqB;YAACnH,MAAM;YAAiB7N,OAAAA;WAAzB;;QAEtBkC,QAAQ6C,oBAAD;AACL,gBAAM8E,MAAKgM,UAAU1T;AAErB,cAAI0H,OAAM,MAAM;AACd;;AAGF,gBAAMC,gBAAgBF,eAAepL,IAAIqL,GAAnB;AAEtB,cAAI,CAACC,eAAe;AAClB;;AAGF,gBAAM;YAACiO;cAAe9B,YAAY9T;AAClC,gBAAMnC,SAAwB;YAC5B2R,gBAAAA;YACAtP,QAAQ;cAACwH,IAAAA;cAAI+L,MAAM9L,cAAc8L;cAAM7e,MAAM0e;;;AAG/CuC,wDAAwB,MAAA;AACtBD,2BAAW,OAAX,SAAAA,YAAc/X,MAAH;AACXoV,sBAAUb,OAAO0D,YAAR;AACT7F,qBAAS;cACPvE,MAAMiF,OAAOpN;cACbX;cACA1C,QAAQwH;aAHF;AAKRmL,iCAAqB;cAACnH,MAAM;cAAe7N,OAAAA;aAAvB;AACpB+V,4BAAgB2B,gBAAgBvV,OAAjB;AACf6T,8BAAkBrE,eAAD;WAVI;;QAazB7N,OAAOD,aAAD;AACJuO,mBAAS;YACPvE,MAAMiF,OAAOC;YACblP;WAFM;;QAKVE,OAAOmU,cAAcpF,OAAOE,OAAR;QACpB/O,UAAUiU,cAAcpF,OAAOG,UAAR;OA7EF;AAgFvByE,sBAAgBvV,UAAUyV;AAE1B,eAASM,cAAcrK,MAAvB;AACE,eAAO,eAAezO,UAAf;AACL,gBAAM;YAACiD,QAAAA;YAAQwP,YAAAA;YAAYG,MAAAA;YAAM8E,yBAAAA;cAC/BH,cAAcxU;AAChB,cAAInC,SAA6B;AAEjC,cAAIqC,WAAUyU,0BAAyB;AACrC,kBAAM;cAACqB;gBAAclC,YAAY9T;AAEjCnC,YAAAA,SAAQ;cACN2R,gBAAAA;cACAtP,QAAQA;cACRwP,YAAAA;cACApS,OAAOqX;cACP9E,MAAAA;;AAGF,gBAAInE,SAASiF,OAAOE,WAAW,OAAOmF,eAAe,YAAY;AAC/D,oBAAMC,eAAe,MAAMC,QAAQC,QAAQH,WAAWnY,MAAD,CAA1B;AAE3B,kBAAIoY,cAAc;AAChBvK,uBAAOiF,OAAOG;;;;AAKpB4C,oBAAU1T,UAAU;AAEpB6V,wDAAwB,MAAA;AACtB5F,qBAAS;cAACvE;aAAF;AACRuH,sBAAUb,OAAOc,aAAR;AACTmC,oBAAQ,IAAD;AACPzB,4BAAgB,IAAD;AACfC,8BAAkB,IAAD;AACjB0B,4BAAgBvV,UAAU;AAE1B,kBAAMhD,YACJ0O,SAASiF,OAAOE,UAAU,cAAc;AAE1C,gBAAIhT,QAAO;AACT,oBAAMZ,WAAU6W,YAAY9T,QAAQhD,SAApB;AAEhBC,cAAAA,YAAO,OAAP,SAAAA,SAAUY,MAAH;AACPgV,mCAAqB;gBAACnH,MAAM1O;gBAAWa,OAAAA;eAAnB;;WAfD;;;;;IAsB7B,CAAC4J,cAAD;EArJmC;AAwJrC,QAAM2O,wCAAoCzP,2BACxC,CACE1J,SACAgL,WAFF;AAIE,WAAO,CAACpK,OAAOqC,YAAR;AACL,YAAM+B,cAAcpE,MAAMoE;AAC1B,YAAMoU,sBAAsB5O,eAAepL,IAAI6D,OAAnB;AAE5B;;QAEEwT,UAAU1T,YAAY;QAEtB,CAACqW;QAEDpU,YAAYqU,UACZrU,YAAYsU;QACZ;AACA;;AAGF,YAAMC,oBAAoB;QACxBtW,QAAQmW;;AAEV,YAAMI,iBAAiBxZ,QACrBY,OACAoK,OAAO3R,SACPkgB,iBAH4B;AAM9B,UAAIC,mBAAmB,MAAM;AAC3BxU,oBAAYqU,SAAS;UACnBI,YAAYzO,OAAOA;;AAGrByL,kBAAU1T,UAAUE;AACpBsV,0BAAkB3X,OAAOoK,MAAR;;;KAIvB,CAACR,gBAAgB+N,iBAAjB,CAxCmD;AA2CrD,QAAMzT,aAAa8F,qBACjBC,SACAsO,iCAFqC;AAKvC3I,iBAAe3F,OAAD;AAEd+D,4BAA0B,MAAA;AACxB,QAAI4D,kBAAkBuD,WAAWZ,OAAO0D,cAAc;AACpD7C,gBAAUb,OAAOgB,WAAR;;KAEV,CAAC3D,gBAAgBuD,MAAjB,CAJsB;AAMzBlM;IACE,MAAA;AACE,YAAM;QAAC6P;UAAc7C,YAAY9T;AACjC,YAAM;QAACE,QAAAA;QAAQsP,gBAAAA;QAAgBE,YAAAA;QAAYG,MAAAA;UAAQ2E,cAAcxU;AAEjE,UAAI,CAACE,WAAU,CAACsP,iBAAgB;AAC9B;;AAGF,YAAM3R,QAAuB;QAC3BqC,QAAAA;QACAsP,gBAAAA;QACAE,YAAAA;QACApS,OAAO;UACLlJ,GAAGugB,wBAAwBvgB;UAC3BE,GAAGqgB,wBAAwBrgB;;QAE7Bub,MAAAA;;AAGFgG,oDAAwB,MAAA;AACtBc,sBAAU,OAAV,SAAAA,WAAa9Y,KAAH;AACVgV,6BAAqB;UAACnH,MAAM;UAAc7N;SAAtB;OAFC;;;IAMzB,CAAC8W,wBAAwBvgB,GAAGugB,wBAAwBrgB,CAApD;EA1BO;AA6BTwS;IACE,MAAA;AACE,YAAM;QACJ5G,QAAAA;QACAsP,gBAAAA;QACAE,YAAAA;QACAE,qBAAAA;QACA+E,yBAAAA;UACEH,cAAcxU;AAElB,UACE,CAACE,WACDwT,UAAU1T,WAAW,QACrB,CAACwP,mBACD,CAACmF,0BACD;AACA;;AAGF,YAAM;QAACiC;UAAc9C,YAAY9T;AACjC,YAAM6W,gBAAgBjH,qBAAoBvT,IAAI8Y,MAAxB;AACtB,YAAMtF,QACJgH,iBAAiBA,cAAcjiB,KAAKoL,UAChC;QACE0H,IAAImP,cAAcnP;QAClB9S,MAAMiiB,cAAcjiB,KAAKoL;QACzByT,MAAMoD,cAAcpD;QACpBzN,UAAU6Q,cAAc7Q;UAE1B;AACN,YAAMnI,QAAuB;QAC3BqC,QAAAA;QACAsP,gBAAAA;QACAE,YAAAA;QACApS,OAAO;UACLlJ,GAAGugB,yBAAwBvgB;UAC3BE,GAAGqgB,yBAAwBrgB;;QAE7Bub,MAAAA;;AAGFgG,oDAAwB,MAAA;AACtBR,gBAAQxF,KAAD;AACP+G,sBAAU,OAAV,SAAAA,WAAa/Y,KAAH;AACVgV,6BAAqB;UAACnH,MAAM;UAAc7N;SAAtB;OAHC;;;IAOzB,CAACsX,MAAD;EAhDO;AAmDTtJ,4BAA0B,MAAA;AACxB2I,kBAAcxU,UAAU;MACtBwP;MACAtP;MACAJ;MACAU;MACAkP;MACA/F;MACAlC;MACAgN;MACAC;MACA9E;MACAC;MACA1U;MACAwZ;;AAGFrB,gBAAYtT,UAAU;MACpBuT,SAASmB;MACTlB,YAAYhT;;KAEb,CACDN,QACAJ,YACA4P,YACAlP,eACAiH,gBACAgN,cACAC,kBACA/K,gBACAiG,qBACAC,MACA1U,qBACAwZ,uBAZC,CArBsB;AAoCzBvP,kBAAgB;IACd,GAAG+O;IACH7W,OAAOiT;IACPhL,cAAc/E;IACdoF;IACAzK;IACA0K;GANa;AASf,QAAMiR,oBAAgBvQ,uBAAQ,MAAA;AAC5B,UAAMpG,UAAmC;MACvCD;MACAJ;MACA2P;MACAD;MACAE;MACAC;MACAT;MACAzH;MACAmI;MACAjG;MACAkG;MACAtG;MACApO;MACA0K;MACAiK;MACA/F;MACAgE;;AAGF,WAAO5N;KACN,CACDD,QACAJ,YACA2P,gBACAD,gBACAE,YACAC,mBACAT,aACAzH,gBACAmI,qBACAjG,gBACAkG,MACAtG,4BACApO,qBACA0K,yBACAiK,wBACA/F,oBACAgE,UAjBC,CAtB0B;AA0C7B,QAAMgJ,sBAAkBxQ,uBAAQ,MAAA;AAC9B,UAAMpG,UAAqC;MACzCqP;MACAzN;MACA7B;MACAuP;MACAO,mBAAmB;QACjBlB,WAAWiF;;MAEb9D;MACAxI;MACAoI;MACAtG;;AAGF,WAAOpJ;KACN,CACDqP,gBACAzN,YACA7B,QACAuP,gBACAQ,UACA8D,wBACAtM,gBACAoI,MACAtG,0BATC,CAhB4B;AA4B/B,SACEyN,cAAAA,QAAAA,cAACC,kBAAkBC,UAAnB;IAA4B3f,OAAOub;KACjCkE,cAAAA,QAAAA,cAAC9G,gBAAgBgH,UAAjB;IAA0B3f,OAAOwf;KAC/BC,cAAAA,QAAAA,cAAC5G,cAAc8G,UAAf;IAAwB3f,OAAOuf;KAC7BE,cAAAA,QAAAA,cAAC7E,uBAAuB+E,UAAxB;IAAiC3f,OAAO3D;KACrCwa,QADH,CADF,GAKA4I,cAAAA,QAAAA,cAAC7F,cAAD;IAAcnL,WAAUuM,iBAAa,OAAb,SAAAA,cAAe4E,kBAAiB;GAAxD,CANF,GAQAH,cAAAA,QAAAA,cAACI,eAAD;IAAA,GACM7E;IACJ8E,yBAAyBtD;GAF3B,CATF;AAgBF,WAASK,yBAAT;AACE,UAAMkD,kCACJ3D,gBAAY,OAAZ,SAAAA,aAAczU,uBAAsB;AACtC,UAAMqY,6BACJ,OAAO7Q,eAAe,WAClBA,WAAWlB,YAAY,QACvBkB,eAAe;AACrB,UAAMlB,UACJ2N,iBACA,CAACmE,kCACD,CAACC;AAEH,QAAI,OAAO7Q,eAAe,UAAU;AAClC,aAAO;QACL,GAAGA;QACHlB;;;AAIJ,WAAO;MAACA;;;AAEX,CAtnB6B;ACrG9B,IAAMgS,kBAAcrH,6BAAmB,IAAN;AAEjC,IAAMsH,cAAc;AAEpB,IAAMC,YAAY;AAElB,SAAgBC,aAAAA,MAAAA;MAAa;IAC3BjQ;IACA+L;IACAzN,WAAW;IACX4R;;AAEA,QAAM1b,OAAM8X,YAAY0D,SAAD;AACvB,QAAM;IACJ3V;IACAyN;IACAtP;IACAuP;IACAO;IACAvI;IACAoI;UACEuB,0BAAWlB,eAAD;AACd,QAAM;IACJ2H,OAAOJ;IACPK,kBAAkB;IAClBC,WAAW;MACTH,cAJE,OAIFA,aAAc,CAAA;AAClB,QAAMI,cAAa9X,UAAM,OAAN,SAAAA,OAAQwH,QAAOA;AAClC,QAAM9T,gBAA8Bwd,0BAClC4G,aAAa7F,yBAAyBqF,WADM;AAG9C,QAAM,CAACzgB,MAAMkhB,UAAP,IAAqBvJ,WAAU;AACrC,QAAM,CAACvM,eAAe+V,mBAAhB,IAAuCxJ,WAAU;AACvD,QAAM/R,YAAYgR,sBAAsB5L,YAAY2F,EAAb;AACvC,QAAMyQ,UAAU7O,eAAemK,IAAD;AAE9B5H;IACE,MAAA;AACEpE,qBAAeqC,IAAIpC,IAAI;QAACA;QAAIxL,KAAAA;QAAKnF;QAAMoL;QAAesR,MAAM0E;OAA5D;AAEA,aAAO,MAAA;AACL,cAAMphB,QAAO0Q,eAAepL,IAAIqL,EAAnB;AAEb,YAAI3Q,SAAQA,MAAKmF,QAAQA,MAAK;AAC5BuL,yBAAeyJ,OAAOxJ,EAAtB;;;;;IAKN,CAACD,gBAAgBC,EAAjB;EAbuB;AAgBzB,QAAM0Q,yBAA0C7R,uBAC9C,OAAO;IACLsR;IACAE;IACA,iBAAiB/R;IACjB,gBAAgBgS,cAAcH,SAASJ,cAAc,OAAO1Y;IAC5D,wBAAwB+Y;IACxB,oBAAoB9H,kBAAkBlB;MAExC,CACE9I,UACA6R,MACAE,UACAC,YACAF,iBACA9H,kBAAkBlB,SANpB,CATqD;AAmBvD,SAAO;IACL5O;IACAsP;IACAC;IACAmI,YAAYQ;IACZJ;IACArb,WAAWqJ,WAAWjH,SAAYpC;IAClC5F;IACA8Y;IACAoI;IACAC;IACAtkB;;AAEH;SCrHeykB,gBAAAA;AACd,aAAOjH,0BAAWhB,aAAD;AAClB;ACsBD,IAAMsH,cAAY;AAElB,IAAMY,8BAA8B;EAClCC,SAAS;AADyB;AAIpC,SAAgBC,aAAAA,MAAAA;MAAa;IAC3B/E;IACAzN,WAAW;IACX0B;IACA+Q;;AAEA,QAAMvc,OAAM8X,YAAY0D,WAAD;AACvB,QAAM;IAACxX;IAAQ+P;IAAUJ;IAAMtG;UAC7B6H,0BAAWlB,eAAD;AACZ,QAAMwI,eAAWrS,sBAAO;IAACL;GAAF;AACvB,QAAM2S,8BAA0BtS,sBAAO,KAAD;AACtC,QAAMzR,WAAOyR,sBAA0B,IAApB;AACnB,QAAMuS,iBAAavS,sBAA8B,IAAxB;AACzB,QAAM;IACJL,UAAU6S;IACVC;IACAP,SAASQ;MACP;IACF,GAAGT;IACH,GAAGG;;AAEL,QAAMjP,OAAMF,eAAewP,yBAAD,OAACA,wBAAyBpR,EAA1B;AAC1B,QAAMmD,mBAAelE;IACnB,MAAA;AACE,UAAI,CAACgS,wBAAwB3Y,SAAS;AAGpC2Y,gCAAwB3Y,UAAU;AAClC;;AAGF,UAAI4Y,WAAW5Y,WAAW,MAAM;AAC9B2D,qBAAaiV,WAAW5Y,OAAZ;;AAGd4Y,iBAAW5Y,UAAUJ,WAAW,MAAA;AAC9B2J,mCACE2D,MAAM8L,QAAQxP,KAAIxJ,OAAlB,IAA6BwJ,KAAIxJ,UAAU,CAACwJ,KAAIxJ,OAAL,CADnB;AAG1B4Y,mBAAW5Y,UAAU;SACpB+Y,qBAL4B;;;IAQjC,CAACA,qBAAD;EArB8B;AAuBhC,QAAMjO,iBAAiBF,kBAAkB;IACvCN,UAAUO;IACV7E,UAAU6S,0BAA0B,CAAC3Y;GAFC;AAIxC,QAAMqO,uBAAmB5H,2BACvB,CAACsS,YAAgCC,oBAAjC;AACE,QAAI,CAACpO,gBAAgB;AACnB;;AAGF,QAAIoO,iBAAiB;AACnBpO,qBAAeqO,UAAUD,eAAzB;AACAP,8BAAwB3Y,UAAU;;AAGpC,QAAIiZ,YAAY;AACdnO,qBAAegB,QAAQmN,UAAvB;;KAGJ,CAACnO,cAAD,CAfkC;AAiBpC,QAAM,CAAC0D,SAASyJ,UAAV,IAAwBvJ,WAAWH,gBAAD;AACxC,QAAM4J,UAAU7O,eAAemK,IAAD;AAE9B3M,+BAAU,MAAA;AACR,QAAI,CAACgE,kBAAkB,CAAC0D,QAAQxO,SAAS;AACvC;;AAGF8K,mBAAeH,WAAf;AACAgO,4BAAwB3Y,UAAU;AAClC8K,mBAAegB,QAAQ0C,QAAQxO,OAA/B;KACC,CAACwO,SAAS1D,cAAV,CARM;AAUThE;IACE,MAAA;AACEmJ,eAAS;QACPvE,MAAMiF,OAAOI;QACb1a,SAAS;UACPqR;UACAxL,KAAAA;UACA8J;UACAjP,MAAMyX;UACN5Z;UACA6e,MAAM0E;;OARF;AAYR,aAAO,MACLlI,SAAS;QACPvE,MAAMiF,OAAOM;QACb/U,KAAAA;QACAwL;OAHM;;;IAOZ,CAACA,EAAD;EAtBO;AAyBTZ,+BAAU,MAAA;AACR,QAAId,aAAa0S,SAAS1Y,QAAQgG,UAAU;AAC1CiK,eAAS;QACPvE,MAAMiF,OAAOK;QACbtJ;QACAxL,KAAAA;QACA8J;OAJM;AAOR0S,eAAS1Y,QAAQgG,WAAWA;;KAE7B,CAAC0B,IAAIxL,MAAK8J,UAAUiK,QAApB,CAXM;AAaT,SAAO;IACL/P;IACAtL;IACAwkB,SAAQvJ,QAAI,OAAJ,SAAAA,KAAMnI,QAAOA;IACrB3Q,MAAMyX;IACNqB;IACAoI;;AAEH;SC/IeoB,iBAAAA,MAAAA;MAAiB;IAACC;IAAWlL;;AAC3C,QAAM,CACJmL,gBACAC,iBAFI,QAGFxQ,wBAAoC,IAA5B;AACZ,QAAM,CAAC3S,SAASojB,UAAV,QAAwBzQ,wBAA6B,IAArB;AACtC,QAAM0Q,mBAAmBtS,YAAYgH,QAAD;AAEpC,MAAI,CAACA,YAAY,CAACmL,kBAAkBG,kBAAkB;AACpDF,sBAAkBE,gBAAD;;AAGnB7N,4BAA0B,MAAA;AACxB,QAAI,CAACxV,SAAS;AACZ;;AAGF,UAAM6F,OAAMqd,kBAAH,OAAA,SAAGA,eAAgBrd;AAC5B,UAAMwL,KAAK6R,kBAAH,OAAA,SAAGA,eAAgBta,MAAMyI;AAEjC,QAAIxL,QAAO,QAAQwL,MAAM,MAAM;AAC7B8R,wBAAkB,IAAD;AACjB;;AAGFtD,YAAQC,QAAQmD,UAAU5R,IAAIrR,OAAL,CAAzB,EAAwCsjB,KAAK,MAAA;AAC3CH,wBAAkB,IAAD;KADnB;KAGC,CAACF,WAAWC,gBAAgBljB,OAA5B,CAhBsB;AAkBzB,SACE2gB,cAAAA,QAAAA,cAAA,cAAAA,QAAA,UAAA,MACG5I,UACAmL,qBAAiBK,4BAAaL,gBAAgB;IAACM,KAAKJ;GAAvB,IAAsC,IAFtE;AAKH;ACzCD,IAAMK,mBAA8B;EAClC1lB,GAAG;EACHE,GAAG;EACHP,QAAQ;EACRE,QAAQ;AAJ0B;AAOpC,SAAgB8lB,yBAAAA,MAAAA;MAAyB;IAAC3L;;AACxC,SACE4I,cAAAA,QAAAA,cAAC9G,gBAAgBgH,UAAjB;IAA0B3f,OAAOwY;KAC/BiH,cAAAA,QAAAA,cAAC7E,uBAAuB+E,UAAxB;IAAiC3f,OAAOuiB;KACrC1L,QADH,CADF;AAMH;ACAD,IAAM4L,aAAkC;EACtC/iB,UAAU;EACVgjB,aAAa;AAFyB;AAKxC,IAAMC,oBAAuC1K,oBAAD;AAC1C,QAAM2K,sBAAsBla,gBAAgBuP,cAAD;AAE3C,SAAO2K,sBAAsB,yBAAyBpb;AACvD;AAEM,IAAMqb,wBAAoBC,0BAC/B,CAAA,MAYER,QAZF;MACE;IACES;IACA9K;IACA7b,aAAAA;IACAya;IACAmM;IACA3lB;IACA4lB;IACA5mB;IACA6mB,aAAaP;;AAIf,MAAI,CAACtlB,MAAM;AACT,WAAO;;AAGT,QAAM8lB,yBAAyB/mB,eAC3BC,YACA;IACE,GAAGA;IACHG,QAAQ;IACRE,QAAQ;;AAEd,QAAM0mB,SAA0C;IAC9C,GAAGX;IACHhmB,OAAOY,KAAKZ;IACZE,QAAQU,KAAKV;IACbK,KAAKK,KAAKL;IACVF,MAAMO,KAAKP;IACXT,WAAWgnB,IAAIC,UAAUC,SAASJ,sBAAvB;IACXhlB,iBACE/B,gBAAe6b,iBACXuL,2BACEvL,gBACA5a,IAFwB,IAI1BmK;IACN0b,YACE,OAAOA,eAAe,aAClBA,WAAWjL,cAAD,IACViL;IACN,GAAGD;;AAGL,SAAOxD,cAAAA,QAAMgE,cACXV,IACA;IACEC;IACAC,OAAOG;IACPd;KAEFzL,QAPK;AASR,CAxDwC;ICwD9B6M,kCACX3kB,aAC6B,UAAA;MAAC;IAAC4J;IAAQgP;;AACvC,QAAMgM,iBAAyC,CAAA;AAC/C,QAAM;IAACP;IAAQJ;MAAajkB;AAE5B,MAAIqkB,UAAJ,QAAIA,OAAQza,QAAQ;AAClB,eAAW,CAAChE,MAAK3E,KAAN,KAAgB4E,OAAO4Q,QAAQ4N,OAAOza,MAAtB,GAA+B;AACxD,UAAI3I,UAAUwH,QAAW;AACvB;;AAGFmc,qBAAehf,IAAD,IAAQgE,OAAOnJ,KAAKyjB,MAAMW,iBAAiBjf,IAAnC;AACtBgE,aAAOnJ,KAAKyjB,MAAMY,YAAYlf,MAAK3E,KAAnC;;;AAIJ,MAAIojB,UAAJ,QAAIA,OAAQzL,aAAa;AACvB,eAAW,CAAChT,MAAK3E,KAAN,KAAgB4E,OAAO4Q,QAAQ4N,OAAOzL,WAAtB,GAAoC;AAC7D,UAAI3X,UAAUwH,QAAW;AACvB;;AAGFmQ,kBAAYnY,KAAKyjB,MAAMY,YAAYlf,MAAK3E,KAAxC;;;AAIJ,MAAIgjB,aAAJ,QAAIA,UAAWra,QAAQ;AACrBA,WAAOnJ,KAAKskB,UAAUjgB,IAAImf,UAAUra,MAApC;;AAGF,MAAIqa,aAAJ,QAAIA,UAAWrL,aAAa;AAC1BA,gBAAYnY,KAAKskB,UAAUjgB,IAAImf,UAAUrL,WAAzC;;AAGF,SAAO,SAASpC,UAAT;AACL,eAAW,CAAC5Q,MAAK3E,KAAN,KAAgB4E,OAAO4Q,QAAQmO,cAAf,GAAgC;AACzDhb,aAAOnJ,KAAKyjB,MAAMY,YAAYlf,MAAK3E,KAAnC;;AAGF,QAAIgjB,aAAJ,QAAIA,UAAWra,QAAQ;AACrBA,aAAOnJ,KAAKskB,UAAUC,OAAOf,UAAUra,MAAvC;;;AAGL;AAED,IAAMqb,0BAA4C,WAAA;AAAA,MAAC;IACjD3nB,WAAW;MAAC2f;MAASiI;;MAD2B;AAAA,SAE5C,CACJ;IACE5nB,WAAWgnB,IAAIC,UAAUC,SAASvH,OAAvB;KAEb;IACE3f,WAAWgnB,IAAIC,UAAUC,SAASU,KAAvB;GALT;AAF4C;AAWlD,IAAaC,oCAAoE;EAC/EC,UAAU;EACVC,QAAQ;EACRC,WAAWL;EACXM,aAAaZ,gCAAgC;IAC3CN,QAAQ;MACNza,QAAQ;QACN4b,SAAS;;;GAH6B;AAJmC;AAajF,SAAgBC,iBAAAA,OAAAA;MAAiB;IAC/BlT;IACApB;IACAmI;IACAE;;AAEA,SAAOtF,SAAoB,CAAC9C,IAAI3Q,SAAL;AACzB,QAAI8R,WAAW,MAAM;AACnB;;AAGF,UAAMmT,kBAA6CvU,eAAepL,IAAIqL,EAAnB;AAEnD,QAAI,CAACsU,iBAAiB;AACpB;;AAGF,UAAMlc,aAAakc,gBAAgBjlB,KAAKiJ;AAExC,QAAI,CAACF,YAAY;AACf;;AAGF,UAAMmc,iBAAiB9N,kBAAkBpX,IAAD;AAExC,QAAI,CAACklB,gBAAgB;AACnB;;AAEF,UAAM;MAACroB;QAAa4C,UAAUO,IAAD,EAAON,iBAAiBM,IAAjC;AACpB,UAAMpB,kBAAkBP,eAAexB,SAAD;AAEtC,QAAI,CAAC+B,iBAAiB;AACpB;;AAGF,UAAM2jB,YACJ,OAAOzQ,WAAW,aACdA,SACAqT,2BAA2BrT,MAAD;AAEhCtN,2BACEuE,YACAgQ,uBAAuBhB,UAAUtT,OAFb;AAKtB,WAAO8d,UAAU;MACfpZ,QAAQ;QACNwH;QACA+L,MAAMuI,gBAAgBvI;QACtB1c,MAAM+I;QACNlL,MAAMkb,uBAAuBhB,UAAUtT,QAAQsE,UAAzC;;MAER2H;MACAyH,aAAa;QACXnY;QACAnC,MAAMkb,uBAAuBZ,YAAY1T,QAAQygB,cAA3C;;MAERrM;MACAE;MACAlc,WAAW+B;KAdG;GAvCH;AAwDhB;AAED,SAASumB,2BACP5lB,SADF;AAGE,QAAM;IAAColB;IAAUC;IAAQE;IAAaD;MAAa;IACjD,GAAGH;IACH,GAAGnlB;;AAGL,SAAO,WAAA;QAAC;MAAC4J;MAAQgP;MAAatb;MAAW,GAAGuoB;;AAC1C,QAAI,CAACT,UAAU;AAEb;;AAGF,UAAMpe,QAAQ;MACZlJ,GAAG8a,YAAYta,KAAKP,OAAO6L,OAAOtL,KAAKP;MACvCC,GAAG4a,YAAYta,KAAKL,MAAM2L,OAAOtL,KAAKL;;AAGxC,UAAM6nB,QAAQ;MACZroB,QACEH,UAAUG,WAAW,IAChBmM,OAAOtL,KAAKZ,QAAQJ,UAAUG,SAAUmb,YAAYta,KAAKZ,QAC1D;MACNC,QACEL,UAAUK,WAAW,IAChBiM,OAAOtL,KAAKV,SAASN,UAAUK,SAAUib,YAAYta,KAAKV,SAC3D;;AAER,UAAMmoB,iBAAiB;MACrBjoB,GAAGR,UAAUQ,IAAIkJ,MAAMlJ;MACvBE,GAAGV,UAAUU,IAAIgJ,MAAMhJ;MACvB,GAAG8nB;;AAGL,UAAME,qBAAqBV,UAAU;MACnC,GAAGO;MACHjc;MACAgP;MACAtb,WAAW;QAAC2f,SAAS3f;QAAW4nB,OAAOa;;KAJL;AAOpC,UAAM,CAACE,aAAD,IAAkBD;AACxB,UAAME,eAAeF,mBAAmBA,mBAAmBzkB,SAAS,CAA7B;AAEvC,QAAImP,KAAKC,UAAUsV,aAAf,MAAkCvV,KAAKC,UAAUuV,YAAf,GAA8B;AAElE;;AAGF,UAAM1P,UAAU+O,eAAH,OAAA,SAAGA,YAAc;MAAC3b;MAAQgP;MAAa,GAAGiN;KAA5B;AAC3B,UAAM7C,YAAYpK,YAAYnY,KAAK0lB,QAAQH,oBAAoB;MAC7DZ;MACAC;MACAe,MAAM;KAHU;AAMlB,WAAO,IAAIxG,QAASC,aAAD;AACjBmD,gBAAUqD,WAAW,MAAA;AACnB7P,mBAAO,OAAP,SAAAA,QAAO;AACPqJ,gBAAO;;KAHJ;;AAOV;AC9RD,IAAIja,MAAM;AAEV,SAAgB0gB,OAAOlV,IAAAA;AACrB,aAAOnB,uBAAQ,MAAA;AACb,QAAImB,MAAM,MAAM;AACd;;AAGFxL;AACA,WAAOA;KACN,CAACwL,EAAD,CAPW;AAQf;ICaYmV,cAAc7F,cAAAA,QAAM1E,KAC/B,UAAA;MAAC;IACC3e,aAAAA,eAAc;IACdya;IACA0O,eAAeC;IACfvC;IACAC;IACA5I;IACAmL,iBAAiB;IACjBzC;IACA0C,SAAS;;AAET,QAAM;IACJzN;IACAtP;IACAuP;IACAE;IACAlI;IACAmI;IACAV;IACAW;IACAC;IACA3U;IACA0K;IACAkI;MACEsK,cAAa;AACjB,QAAMzkB,gBAAYwd,0BAAWe,sBAAD;AAC5B,QAAMjW,OAAM0gB,OAAO1c,UAAD,OAAA,SAACA,OAAQwH,EAAT;AAClB,QAAMwV,oBAAoBtL,eAAeC,WAAW;IAClDrC;IACAtP;IACAuP;IACAE;IACA+E,kBAAkBxF,YAAYta;IAC9Bib;IACAmF,iBAAiB9F,YAAYta;IAC7BuG;IACA0K;IACAjS;IACAma;GAXsC;AAaxC,QAAM5B,cAAcjC,gBAAgBuF,cAAD;AACnC,QAAMqN,gBAAgBf,iBAAiB;IACrClT,QAAQkU;IACRtV;IACAmI;IACAE;GAJoC;AAQtC,QAAM+J,MAAM1N,cAAc+C,YAAYT,SAAS1P;AAE/C,SACEiY,cAAAA,QAAAA,cAAC+C,0BAAD,MACE/C,cAAAA,QAAAA,cAACqC,kBAAD;IAAkBC,WAAWwD;KAC1B5c,UAAUhE,OACT8a,cAAAA,QAAAA,cAACoD,mBAAD;IACEle,KAAKA;IACLwL,IAAIxH,OAAOwH;IACXmS;IACAS,IAAI0C;IACJxN;IACA7b,aAAaA;IACb4mB;IACAE;IACA7lB,MAAMuX;IACNqO,OAAO;MACLyC;MACA,GAAGzC;;IAEL5mB,WAAWspB;KAEV9O,QAhBH,IAkBE,IApBN,CADF;AAyBH,CA9EwB;;;;SCzBX+O,UAAaC,OAAYC,MAAcC,IAAAA;AACrD,QAAMC,WAAWH,MAAMI,MAAN;AACjBD,WAASE,OACPH,KAAK,IAAIC,SAASG,SAASJ,KAAKA,IAChC,GACAC,SAASE,OAAOJ,MAAM,CAAtB,EAAyB,CAAzB,CAHF;AAMA,SAAOE;AACR;SENeI,eACdC,OACAC,OAAAA;AAEA,SAAOD,MAAME,OAAqB,CAACC,aAAaC,IAAIC,UAAlB;AAChC,UAAMC,OAAOL,MAAMM,IAAIH,EAAV;AAEb,QAAIE,MAAM;AACRH,kBAAYE,KAAD,IAAUC;;AAGvB,WAAOH;KACNK,MAAMR,MAAMS,MAAP,CARD;AASR;SCnBeC,aAAaL,OAAAA;AAC3B,SAAOA,UAAU,QAAQA,SAAS;AACnC;SCAeM,WAAWC,GAAuBC,GAAAA;AAChD,MAAID,MAAMC,GAAG;AACX,WAAO;;AAGT,MAAID,EAAEH,WAAWI,EAAEJ,QAAQ;AACzB,WAAO;;AAGT,WAASK,IAAI,GAAGA,IAAIF,EAAEH,QAAQK,KAAK;AACjC,QAAIF,EAAEE,CAAD,MAAQD,EAAEC,CAAD,GAAK;AACjB,aAAO;;;AAIX,SAAO;AACR;SChBeC,kBAAkBC,UAAAA;AAChC,MAAI,OAAOA,aAAa,WAAW;AACjC,WAAO;MACLC,WAAWD;MACXE,WAAWF;;;AAIf,SAAOA;AACR;IERYG,sBAAuC,UAAA;MAAC;IACnDC;IACAC;IACAC;IACAC;;AAEA,QAAMC,WAAWC,UAAUL,OAAOE,WAAWD,WAAnB;AAE1B,QAAMK,UAAUN,MAAMG,KAAD;AACrB,QAAMI,UAAUH,SAASD,KAAD;AAExB,MAAI,CAACI,WAAW,CAACD,SAAS;AACxB,WAAO;;AAGT,SAAO;IACLE,GAAGD,QAAQE,OAAOH,QAAQG;IAC1BC,GAAGH,QAAQI,MAAML,QAAQK;IACzBC,QAAQL,QAAQM,QAAQP,QAAQO;IAChCC,QAAQP,QAAQQ,SAAST,QAAQS;;AAEpC;AEpBD,IAAMC,iBAAe;EACnBC,QAAQ;EACRC,QAAQ;AAFW;AAKrB,IAAaC,8BAA+C,UAAA;;MAAC;IAC3DC;IACAC,gBAAgBC;IAChBC;IACAC;IACAC;;AAEA,QAAMJ,kBAAc,qBAAGG,MAAMJ,WAAD,MAAR,OAAA,qBAAyBE;AAE7C,MAAI,CAACD,gBAAgB;AACnB,WAAO;;AAGT,MAAIE,UAAUH,aAAa;AACzB,UAAMM,gBAAgBF,MAAMC,SAAD;AAE3B,QAAI,CAACC,eAAe;AAClB,aAAO;;AAGT,WAAO;MACLC,GAAG;MACHC,GACER,cAAcK,YACVC,cAAcG,MACdH,cAAcI,UACbT,eAAeQ,MAAMR,eAAeS,UACrCJ,cAAcG,MAAMR,eAAeQ;MACzC,GAAGb;;;AAIP,QAAMe,UAAUC,aAAWR,OAAOD,OAAOH,WAAf;AAE1B,MAAIG,QAAQH,eAAeG,SAASE,WAAW;AAC7C,WAAO;MACLE,GAAG;MACHC,GAAG,CAACP,eAAeS,SAASC;MAC5B,GAAGf;;;AAIP,MAAIO,QAAQH,eAAeG,SAASE,WAAW;AAC7C,WAAO;MACLE,GAAG;MACHC,GAAGP,eAAeS,SAASC;MAC3B,GAAGf;;;AAIP,SAAO;IACLW,GAAG;IACHC,GAAG;IACH,GAAGZ;;AAEN;AAED,SAASgB,aACPC,aACAV,OACAH,aAHF;AAKE,QAAMc,cAAsCD,YAAYV,KAAD;AACvD,QAAMY,eAAuCF,YAAYV,QAAQ,CAAT;AACxD,QAAMa,WAAmCH,YAAYV,QAAQ,CAAT;AAEpD,MAAI,CAACW,aAAa;AAChB,WAAO;;AAGT,MAAId,cAAcG,OAAO;AACvB,WAAOY,eACHD,YAAYL,OAAOM,aAAaN,MAAMM,aAAaL,UACnDM,WACAA,SAASP,OAAOK,YAAYL,MAAMK,YAAYJ,UAC9C;;AAGN,SAAOM,WACHA,SAASP,OAAOK,YAAYL,MAAMK,YAAYJ,UAC9CK,eACAD,YAAYL,OAAOM,aAAaN,MAAMM,aAAaL,UACnD;AACL;AC5ED,IAAMO,aAAY;AAcX,IAAMC,UAAUC,cAAAA,QAAMC,cAAiC;EAC5DpB,aAAa;EACbqB,aAAaJ;EACbK,mBAAmB;EACnBC,OAAO,CAAA;EACPlB,WAAW;EACXmB,gBAAgB;EAChBC,aAAa,CAAA;EACbC,UAAUC;EACVC,UAAU;IACRC,WAAW;IACXC,WAAW;;AAX+C,CAAvC;AAevB,SAAgBC,gBAAAA,MAAAA;MAAgB;IAC9BC;IACAC;IACAV,OAAOW;IACPR,WAAWC;IACXC,UAAUO,eAAe;;AAEzB,QAAM;IACJC;IACAC;IACAC;IACAC;IACAC;MACEC,cAAa;AACjB,QAAMpB,cAAcqB,YAAYzB,YAAWgB,EAAZ;AAC/B,QAAMT,iBAAiBmB,QAAQN,YAAYO,SAAS,IAAtB;AAC9B,QAAMrB,YAAQsB,uBACZ,MACEX,iBAAiBY,IAAKC,UACpB,OAAOA,SAAS,YAAY,QAAQA,OAAOA,KAAKd,KAAKc,IADvD,GAGF,CAACb,gBAAD,CALmB;AAOrB,QAAMc,aAAaZ,UAAU;AAC7B,QAAMpC,cAAcoC,SAASb,MAAM0B,QAAQb,OAAOH,EAArB,IAA2B;AACxD,QAAM5B,YAAYkC,OAAOhB,MAAM0B,QAAQV,KAAKN,EAAnB,IAAyB;AAClD,QAAMiB,uBAAmBC,sBAAO5B,KAAD;AAC/B,QAAM6B,mBAAmB,CAACC,WAAW9B,OAAO2B,iBAAiBI,OAAzB;AACpC,QAAMhC,oBACHjB,cAAc,MAAML,gBAAgB,MAAOoD;AAC9C,QAAMxB,WAAW2B,kBAAkBpB,YAAD;AAElCqB,4BAA0B,MAAA;AACxB,QAAIJ,oBAAoBJ,YAAY;AAClCR,iCAA2BjB,KAAD;;KAE3B,CAAC6B,kBAAkB7B,OAAOyB,YAAYR,0BAAtC,CAJsB;AAMzBiB,+BAAU,MAAA;AACRP,qBAAiBI,UAAU/B;KAC1B,CAACA,KAAD,CAFM;AAIT,QAAMmC,mBAAeb;IACnB,OAA0B;MACxB7C;MACAqB;MACAO;MACAN;MACAC;MACAlB;MACAmB;MACAC,aAAakC,eAAepC,OAAOe,cAAR;MAC3BZ;;;IAGF,CACE1B,aACAqB,aACAO,SAASC,WACTD,SAASE,WACTR,mBACAC,OACAlB,WACAiC,gBACAd,gBACAE,QAVF;EAb0B;AA2B5B,SAAOP,cAAAA,QAAAA,cAACD,QAAQ0C,UAAT;IAAkBC,OAAOH;KAAe1B,QAAxC;AACR;ICzGY8B,wBAAwC,UAAA;AAAA,MAAC;IACpD7B;IACAV;IACAvB;IACAK;MAJmD;AAAA,SAK/C0D,UAAUxC,OAAOvB,aAAaK,SAArB,EAAgC4C,QAAQhB,EAAjD;AAL+C;AAOrD,IAAa+B,8BAAoD,WAAA;MAAC;IAChE3C;IACA4C;IACAC;IACA/D;IACAoB;IACA4C;IACAC;IACAC;IACAC;;AAEA,MAAI,CAACA,cAAc,CAACJ,aAAa;AAC/B,WAAO;;AAGT,MAAIE,kBAAkB7C,SAASpB,UAAUgE,UAAU;AACjD,WAAO;;AAGT,MAAIF,WAAW;AACb,WAAO;;AAGT,SAAOE,aAAahE,SAASkB,gBAAgBgD;AAC9C;AAEM,IAAME,qBAAwC;EACnDC,UAAU;EACVC,QAAQ;AAF2C;AAK9C,IAAMC,qBAAqB;AAE3B,IAAMC,qBAAqBC,IAAIC,WAAWC,SAAS;EACxDC,UAAUL;EACVF,UAAU;EACVC,QAAQ;AAHgD,CAAxB;AAM3B,IAAMO,oBAAoB;EAC/BC,iBAAiB;AADc;ACzCjC,SAAgBC,oBAAAA,MAAAA;MAAoB;IAACtD;IAAUzB;IAAOgF;IAAMvC;;AAC1D,QAAM,CAACwC,kBAAkBC,mBAAnB,QAA0CC,wBAC9C,IADsD;AAGxD,QAAMC,oBAAgBpC,sBAAOhD,KAAD;AAE5BqD,4BAA0B,MAAA;AACxB,QAAI,CAAC5B,YAAYzB,UAAUoF,cAAcjC,WAAW6B,KAAK7B,SAAS;AAChE,YAAMkC,UAAU5C,KAAKU;AAErB,UAAIkC,SAAS;AACX,cAAMlC,UAAUmC,cAAcN,KAAK7B,SAAS;UAC1CoC,iBAAiB;SADU;AAI7B,cAAMC,QAAQ;UACZpF,GAAGiF,QAAQI,OAAOtC,QAAQsC;UAC1BpF,GAAGgF,QAAQ/E,MAAM6C,QAAQ7C;UACzBZ,QAAQ2F,QAAQK,QAAQvC,QAAQuC;UAChC/F,QAAQ0F,QAAQ9E,SAAS4C,QAAQ5C;;AAGnC,YAAIiF,MAAMpF,KAAKoF,MAAMnF,GAAG;AACtB6E,8BAAoBM,KAAD;;;;AAKzB,QAAIxF,UAAUoF,cAAcjC,SAAS;AACnCiC,oBAAcjC,UAAUnD;;KAEzB,CAACyB,UAAUzB,OAAOgF,MAAMvC,IAAxB,CAzBsB;AA2BzBa,+BAAU,MAAA;AACR,QAAI2B,kBAAkB;AACpBC,0BAAoB,IAAD;;KAEpB,CAACD,gBAAD,CAJM;AAMT,SAAOA;AACR;SCjBeU,YAAAA,MAAAA;MAAY;IAC1BC,uBAAuB/B;IACvBgC,YAAYC;IACZrE,UAAUsE;IACVC,MAAMC;IACNC,cAAcvC;IACd7B;IACAP,UAAU4E;IACVC;IACAjC,aAAaC;;AAEb,QAAM;IACJhD;IACAF;IACArB;IACA4B,UAAU4E;IACVlF;IACAG;IACApB;IACAmB;IACAE,UAAU+E;UACRC,0BAAWxF,OAAD;AACd,QAAMU,WAAqB+E,uBACzBT,eACAM,cAF+C;AAIjD,QAAMrG,QAAQoB,MAAM0B,QAAQhB,EAAd;AACd,QAAMkE,WAAOtD,uBACX,OAAO;IAAC+D,UAAU;MAACvF;MAAalB;MAAOoB;;IAAQ,GAAG6E;MAClD,CAAC/E,aAAa+E,YAAYjG,OAAOoB,KAAjC,CAFkB;AAIpB,QAAMsF,gCAA4BhE,uBAChC,MAAMtB,MAAMuF,MAAMvF,MAAM0B,QAAQhB,EAAd,CAAZ,GACN,CAACV,OAAOU,EAAR,CAFuC;AAIzC,QAAM;IACJW;IACAuC;IACA4B;IACAC,YAAYC;MACVC,aAAa;IACfjF;IACAkE;IACAvE,UAAUA,SAASE;IACnByE,sBAAsB;MACpBY,uBAAuBN;MACvB,GAAGN;;GANS;AAShB,QAAM;IACJnE;IACAgF;IACAnH;IACA+F;IACAgB,YAAYK;IACZC;IACAtE;IACAT;IACAgF;IACAC;MACEC,aAAa;IACfxF;IACAkE;IACAH,YAAY;MACV,GAAGhB;MACH,GAAGiB;;IAELrE,UAAUA,SAASC;GAPL;AAShB,QAAMmF,aAAaU,gBAAgBT,qBAAqBI,mBAAtB;AAClC,QAAMpD,YAAYtB,QAAQP,MAAD;AACzB,QAAMuF,eACJ1D,aACA,CAAC3C,qBACDsG,aAAa5H,WAAD,KACZ4H,aAAavH,SAAD;AACd,QAAMwH,2BAA2B,CAACrG,kBAAkBwB;AACpD,QAAM8E,yBACJD,4BAA4BF,eAAeH,YAAY;AACzD,QAAM9F,WAAW4E,iBAAH,OAAGA,gBAAiBG;AAClC,QAAMsB,iBAAiBJ,eACnBG,0BAD+B,OAC/BA,yBACApG,SAAS;IACPtB,OAAOqB;IACPxB;IACAD;IACAK;IACAF;GALM,IAOR;AACJ,QAAMgE,WACJyD,aAAa5H,WAAD,KAAiB4H,aAAavH,SAAD,IACrCgG,YAAY;IAACpE;IAAIV;IAAOvB;IAAaK;GAA1B,IACXF;AACN,QAAM6H,WAAW5F,UAAH,OAAA,SAAGA,OAAQH;AACzB,QAAMgG,eAAW9E,sBAAO;IACtB6E;IACAzG;IACA4C;IACA9C;GAJqB;AAMvB,QAAM+B,mBAAmB7B,UAAU0G,SAAS3E,QAAQ/B;AACpD,QAAM2G,6BAA6BnC,qBAAqB;IACtD3D;IACAf;IACA2B;IACAiB;IACAhC;IACA9B;IACAoB;IACA4C,UAAU8D,SAAS3E,QAAQa;IAC3BC,eAAe6D,SAAS3E,QAAQ/B;IAChC8C,qBAAqB4D,SAAS3E,QAAQjC;IACtCiD;IACAJ,aAAa+D,SAAS3E,QAAQ0E,YAAY;GAZW;AAevD,QAAM5C,mBAAmBF,oBAAoB;IAC3CtD,UAAU,CAACsG;IACX/H;IACAgF;IACAvC;GAJ0C;AAO5Ca,+BAAU,MAAA;AACR,QAAIQ,aAAagE,SAAS3E,QAAQa,aAAaA,UAAU;AACvD8D,eAAS3E,QAAQa,WAAWA;;AAG9B,QAAI9C,gBAAgB4G,SAAS3E,QAAQjC,aAAa;AAChD4G,eAAS3E,QAAQjC,cAAcA;;AAGjC,QAAIE,UAAU0G,SAAS3E,QAAQ/B,OAAO;AACpC0G,eAAS3E,QAAQ/B,QAAQA;;KAE1B,CAAC0C,WAAWE,UAAU9C,aAAaE,KAAnC,CAZM;AAcTkC,+BAAU,MAAA;AACR,QAAIuE,aAAaC,SAAS3E,QAAQ0E,UAAU;AAC1C;;AAGF,QAAIA,YAAY,CAACC,SAAS3E,QAAQ0E,UAAU;AAC1CC,eAAS3E,QAAQ0E,WAAWA;AAC5B;;AAGF,UAAMG,YAAYC,WAAW,MAAA;AAC3BH,eAAS3E,QAAQ0E,WAAWA;OAC3B,EAFyB;AAI5B,WAAO,MAAMK,aAAaF,SAAD;KACxB,CAACH,QAAD,CAfM;AAiBT,SAAO;IACL5F;IACApC;IACAgG;IACAG;IACAvD;IACAzC;IACAgE;IACA5C;IACAwF;IACA9C;IACAjB;IACAsE;IACAnC;IACA9E;IACAkC;IACAyE;IACAO;IACAN;IACAI;IACAG,WAAWpC,oBAAF,OAAEA,mBAAoB2C;IAC/BzD,YAAYgE,cAAa;;AAG3B,WAASA,gBAAT;AACE;;MAEElD;MAEChC,oBAAoB6E,SAAS3E,QAAQa,aAAahE;MACnD;AACA,aAAOwE;;AAGT,QACGkD,4BAA4B,CAACU,gBAAgBnB,cAAD,KAC7C,CAAC9C,YACD;AACA,aAAOkE;;AAGT,QAAIvE,aAAaiE,4BAA4B;AAC3C,aAAOtD,IAAIC,WAAWC,SAAS;QAC7B,GAAGR;QACHS,UAAUL;OAFL;;AAMT,WAAO8D;;AAEV;AAED,SAAS7B,uBACPT,eACAM,gBAFF;;AAIE,MAAI,OAAON,kBAAkB,WAAW;AACtC,WAAO;MACLrE,WAAWqE;;MAEXpE,WAAW;;;AAIf,SAAO;IACLD,YAAS,wBAAEqE,iBAAF,OAAA,SAAEA,cAAerE,cAAjB,OAAA,wBAA8B2E,eAAe3E;IACtDC,YAAS,wBAAEoE,iBAAF,OAAA,SAAEA,cAAepE,cAAjB,OAAA,wBAA8B0E,eAAe1E;;AAEzD;SC3Pe2G,gBAGdC,OAAAA;AAEA,MAAI,CAACA,OAAO;AACV,WAAO;;AAGT,QAAMvC,OAAOuC,MAAMvC,KAAK7C;AAExB,MACE6C,QACA,cAAcA,QACd,OAAOA,KAAKS,aAAa,YACzB,iBAAiBT,KAAKS,YACtB,WAAWT,KAAKS,YAChB,WAAWT,KAAKS,UAChB;AACA,WAAO;;AAGT,SAAO;AACR;ACrBD,IAAM+B,aAAuB,CAC3BC,aAAaC,MACbD,aAAaE,OACbF,aAAaG,IACbH,aAAaI,IAJc;AAO7B,IAAaC,8BAAwD,CACnEC,OADmE,SAAA;MAEnE;IACEC,SAAS;MACP/G;MACAgH;MACA9G;MACA+G;MACA9G;MACA+G;;;AAIJ,MAAIX,WAAWY,SAASL,MAAMM,IAA1B,GAAiC;AACnCN,UAAMO,eAAN;AAEA,QAAI,CAACrH,UAAU,CAACgH,eAAe;AAC7B;;AAGF,UAAMM,qBAA2C,CAAA;AAEjDL,wBAAoBM,WAApB,EAAiCC,QAASlB,WAAD;AACvC,UAAI,CAACA,SAASA,SAAV,QAAUA,MAAO9G,UAAU;AAC7B;;AAGF,YAAMgB,OAAON,eAAeuH,IAAInB,MAAMzG,EAAzB;AAEb,UAAI,CAACW,MAAM;AACT;;AAGF,cAAQsG,MAAMM,MAAd;QACE,KAAKZ,aAAaC;AAChB,cAAIO,cAAc3I,MAAMmC,KAAKnC,KAAK;AAChCiJ,+BAAmBI,KAAKpB,KAAxB;;AAEF;QACF,KAAKE,aAAaG;AAChB,cAAIK,cAAc3I,MAAMmC,KAAKnC,KAAK;AAChCiJ,+BAAmBI,KAAKpB,KAAxB;;AAEF;QACF,KAAKE,aAAaI;AAChB,cAAII,cAAcxD,OAAOhD,KAAKgD,MAAM;AAClC8D,+BAAmBI,KAAKpB,KAAxB;;AAEF;QACF,KAAKE,aAAaE;AAChB,cAAIM,cAAcxD,OAAOhD,KAAKgD,MAAM;AAClC8D,+BAAmBI,KAAKpB,KAAxB;;AAEF;;KA/BN;AAmCA,UAAMqB,aAAaC,eAAe;MAChC5H;MACAgH;MACA9G;MACA+G,qBAAqBK;MACrBO,oBAAoB;KALW;AAOjC,QAAIC,YAAYC,kBAAkBJ,YAAY,IAAb;AAEjC,QAAIG,eAAc3H,QAAL,OAAA,SAAKA,KAAMN,OAAM8H,WAAWK,SAAS,GAAG;AACnDF,kBAAYH,WAAW,CAAD,EAAI9H;;AAG5B,QAAIiI,aAAa,MAAM;AACrB,YAAMG,kBAAkBhB,oBAAoBQ,IAAIzH,OAAOH,EAA/B;AACxB,YAAMqI,eAAejB,oBAAoBQ,IAAIK,SAAxB;AACrB,YAAMK,UAAUD,eAAehI,eAAeuH,IAAIS,aAAarI,EAAhC,IAAsC;AACrE,YAAMuI,UAAUF,gBAAH,OAAA,SAAGA,aAAcnF,KAAK7B;AAEnC,UAAIkH,WAAWD,WAAWF,mBAAmBC,cAAc;AACzD,cAAMG,qBAAqBC,uBAAuBF,OAAD;AACjD,cAAMG,8BAA8BF,mBAAmBG,KACrD,CAACC,SAAS1K,UAAUmJ,oBAAoBnJ,KAAD,MAAY0K,OADjB;AAGpC,cAAMC,mBAAmBC,gBAAgBV,iBAAiBC,YAAlB;AACxC,cAAMU,gBAAgBC,QAAQZ,iBAAiBC,YAAlB;AAC7B,cAAMY,SACJP,+BAA+B,CAACG,mBAC5B;UACEvK,GAAG;UACHC,GAAG;YAEL;UACED,GAAGyK,gBAAgB5B,cAAcvD,QAAQ0E,QAAQ1E,QAAQ;UACzDrF,GAAGwK,gBAAgB5B,cAAc1I,SAAS6J,QAAQ7J,SAAS;;AAEnE,cAAMyK,kBAAkB;UACtB5K,GAAGgK,QAAQ3E;UACXpF,GAAG+J,QAAQ9J;;AAGb,cAAM2K,iBACJF,OAAO3K,KAAK2K,OAAO1K,IACf2K,kBACAE,SAASF,iBAAiBD,MAAlB;AAEd,eAAOE;;;;AAKb,SAAO5C;AACR;AAED,SAASuC,gBAAgBO,GAAuBC,GAAhD;AACE,MAAI,CAAC9C,gBAAgB6C,CAAD,KAAO,CAAC7C,gBAAgB8C,CAAD,GAAK;AAC9C,WAAO;;AAGT,SACED,EAAEnF,KAAK7C,QAAQsD,SAASvF,gBAAgBkK,EAAEpF,KAAK7C,QAAQsD,SAASvF;AAEnE;AAED,SAAS4J,QAAQK,GAAuBC,GAAxC;AACE,MAAI,CAAC9C,gBAAgB6C,CAAD,KAAO,CAAC7C,gBAAgB8C,CAAD,GAAK;AAC9C,WAAO;;AAGT,MAAI,CAACR,gBAAgBO,GAAGC,CAAJ,GAAQ;AAC1B,WAAO;;AAGT,SAAOD,EAAEnF,KAAK7C,QAAQsD,SAASzG,QAAQoL,EAAEpF,KAAK7C,QAAQsD,SAASzG;AAChE;", "names": ["useCombinedRefs", "refs", "useMemo", "node", "for<PERSON>ach", "ref", "canUseDOM", "window", "document", "createElement", "isWindow", "element", "elementString", "Object", "prototype", "toString", "call", "isNode", "getWindow", "target", "ownerDocument", "defaultView", "isDocument", "Document", "isHTMLElement", "HTMLElement", "isSVGElement", "SVGElement", "getOwnerDocument", "useIsomorphicLayoutEffect", "useLayoutEffect", "useEffect", "useEvent", "handler", "handler<PERSON>ef", "useRef", "current", "useCallback", "args", "useInterval", "intervalRef", "set", "listener", "duration", "setInterval", "clear", "clearInterval", "useLatestValue", "value", "dependencies", "valueRef", "useLazyMemo", "callback", "newValue", "useNodeRef", "onChange", "onChangeHandler", "setNodeRef", "usePrevious", "ids", "useUniqueId", "prefix", "id", "createAdjustmentFn", "modifier", "object", "adjustments", "reduce", "accumulator", "adjustment", "entries", "key", "valueAdjustment", "add", "subtract", "hasViewportRelativeCoordinates", "event", "isKeyboardEvent", "KeyboardEvent", "isTouchEvent", "TouchEvent", "getEventCoordinates", "touches", "length", "clientX", "x", "clientY", "y", "changedTouches", "CSS", "freeze", "Translate", "transform", "Math", "round", "Scale", "scaleX", "scaleY", "Transform", "join", "Transition", "property", "easing", "SELECTOR", "findFirstFocusableNode", "matches", "querySelector", "hiddenStyles", "display", "HiddenText", "id", "value", "React", "style", "LiveRegion", "announcement", "ariaLiveType", "visuallyHidden", "position", "top", "left", "width", "height", "margin", "border", "padding", "overflow", "clip", "clipPath", "whiteSpace", "role", "useAnnouncement", "setAnnouncement", "useState", "announce", "useCallback", "DndMonitorContext", "createContext", "useDndMonitor", "listener", "registerListener", "useContext", "useEffect", "Error", "unsubscribe", "useDndMonitorProvider", "listeners", "useState", "Set", "useCallback", "add", "delete", "dispatch", "type", "event", "for<PERSON>ach", "defaultScreenReaderInstructions", "draggable", "defaultAnnouncements", "onDragStart", "active", "id", "onDragOver", "over", "onDragEnd", "onDragCancel", "Accessibility", "announcements", "container", "hiddenTextDescribedById", "screenReaderInstructions", "announce", "announcement", "useAnnouncement", "liveRegionId", "useUniqueId", "mounted", "setMounted", "useMemo", "onDragMove", "markup", "React", "HiddenText", "value", "LiveRegion", "createPortal", "Action", "noop", "useSensor", "sensor", "options", "useSensors", "sensors", "filter", "defaultCoordinates", "Object", "freeze", "x", "y", "distanceBetween", "p1", "p2", "Math", "sqrt", "pow", "getRelativeTransformOrigin", "rect", "eventCoordinates", "getEventCoordinates", "transform<PERSON><PERSON>in", "left", "width", "top", "height", "sortCollisionsAsc", "data", "a", "b", "sortCollisionsDesc", "cornersOfRectangle", "getFirstCollision", "collisions", "property", "length", "firstCollision", "centerOfRectangle", "closestCenter", "collisionRect", "droppableRects", "droppableContainers", "centerRect", "droppableContainer", "get", "distBetween", "push", "sort", "closestCorners", "corners", "rectCorners", "distances", "reduce", "accumulator", "corner", "index", "effectiveDistance", "Number", "toFixed", "getIntersectionRatio", "entry", "target", "max", "right", "min", "bottom", "targetArea", "entryArea", "intersectionArea", "intersectionRatio", "rectIntersection", "adjustScale", "transform", "rect1", "rect2", "scaleX", "width", "scaleY", "height", "getRectDelta", "x", "left", "y", "top", "defaultCoordinates", "createRectAdjustmentFn", "modifier", "adjustClientRect", "rect", "adjustments", "reduce", "acc", "adjustment", "bottom", "right", "getAdjustedRect", "parseTransform", "startsWith", "transformArray", "slice", "split", "inverseTransform", "transform<PERSON><PERSON>in", "parsedTransform", "translateX", "translateY", "parseFloat", "indexOf", "w", "h", "defaultOptions", "ignoreTransform", "getClientRect", "element", "options", "getBoundingClientRect", "getWindow", "getComputedStyle", "getTransformAgnosticClientRect", "getWindowClientRect", "innerWidth", "innerHeight", "isFixed", "node", "computedStyle", "position", "isScrollable", "overflowRegex", "properties", "some", "property", "value", "test", "getScrollableAncestors", "limit", "scrollParents", "findScrollableAncestors", "length", "isDocument", "scrollingElement", "includes", "push", "isHTMLElement", "isSVGElement", "parentNode", "getFirstScrollableAncestor", "firstScrollableAncestor", "getScrollableElement", "canUseDOM", "isWindow", "isNode", "getOwnerDocument", "window", "getScrollXCoordinate", "scrollX", "scrollLeft", "getScrollYCoordinate", "scrollY", "scrollTop", "getScrollCoordinates", "Direction", "isDocumentScrollingElement", "document", "getScrollPosition", "scrollingContainer", "minScroll", "dimensions", "clientHeight", "clientWidth", "maxScroll", "scrollWidth", "scrollHeight", "isTop", "isLeft", "isBottom", "isRight", "defaultThreshold", "getScrollDirectionAndSpeed", "scrollContainer", "scrollContainerRect", "acceleration", "thresholdPercentage", "direction", "speed", "threshold", "Backward", "Math", "abs", "Forward", "getScrollElementRect", "getScrollOffsets", "scrollableAncestors", "add", "getScrollXOffset", "getScrollYOffset", "scrollIntoViewIfNeeded", "measure", "scrollIntoView", "block", "inline", "Rect", "constructor", "scrollOffsets", "axis", "keys", "getScrollOffset", "key", "Object", "defineProperty", "get", "currentOffsets", "scrollOffsetsDeltla", "enumerable", "Listeners", "target", "listeners", "removeAll", "for<PERSON>ach", "listener", "removeEventListener", "eventName", "handler", "addEventListener", "getEventListenerTarget", "EventTarget", "hasExceededDistance", "delta", "measurement", "dx", "dy", "sqrt", "EventName", "preventDefault", "event", "stopPropagation", "KeyboardCode", "defaultKeyboardCodes", "start", "Space", "Enter", "cancel", "Esc", "end", "Tab", "defaultKeyboardCoordinateGetter", "currentCoordinates", "code", "Right", "Left", "Down", "Up", "undefined", "KeyboardSensor", "props", "autoScrollEnabled", "referenceCoordinates", "windowListeners", "handleKeyDown", "bind", "handleCancel", "attach", "handleStart", "Resize", "VisibilityChange", "setTimeout", "Keydown", "activeNode", "onStart", "current", "isKeyboardEvent", "active", "context", "keyboardCodes", "coordinateGetter", "scroll<PERSON>eh<PERSON>or", "handleEnd", "collisionRect", "newCoordinates", "coordinates<PERSON><PERSON><PERSON>", "getCoordinatesDelta", "scrollDelta", "scrollElementRect", "clampedCoordinates", "min", "max", "canScrollX", "canScrollY", "newScrollCoordinates", "canScrollToNewCoordinates", "scrollTo", "behavior", "scrollBy", "handleMove", "getAdjustedCoordinates", "coordinates", "onMove", "onEnd", "detach", "onCancel", "activators", "onActivation", "nativeEvent", "activator", "activatorNode", "isDistanceConstraint", "constraint", "Boolean", "isDelayConstraint", "AbstractPointerSensor", "events", "<PERSON><PERSON><PERSON><PERSON>", "activated", "initialCoordinates", "timeoutId", "documentListeners", "getEventCoordinates", "handleKeydown", "removeTextSelection", "activationConstraint", "bypassActivationConstraint", "move", "name", "passive", "DragStart", "ContextMenu", "delay", "handlePending", "clearTimeout", "offset", "onPending", "Click", "capture", "SelectionChange", "tolerance", "distance", "cancelable", "onAbort", "getSelection", "removeAllRanges", "PointerSensor", "isPrimary", "button", "MouseB<PERSON>on", "MouseSensor", "RightClick", "TouchSensor", "setup", "noop", "teardown", "touches", "AutoScrollActivator", "TraversalOrder", "useAutoScroller", "Pointer", "canScroll", "draggingRect", "enabled", "interval", "order", "TreeOrder", "pointerCoordinates", "scrollableAncestorRects", "scrollIntent", "useScrollIntent", "disabled", "setAutoScrollInterval", "clearAutoScrollInterval", "useInterval", "scrollSpeed", "useRef", "scrollDirection", "useMemo", "DraggableRect", "scrollContainerRef", "autoScroll", "useCallback", "sortedScrollableAncestors", "reverse", "useEffect", "index", "JSON", "stringify", "defaultScrollIntent", "previousDel<PERSON>", "usePrevious", "useLazyMemo", "previousIntent", "sign", "useCachedNode", "draggableNodes", "id", "draggableNode", "cachedNode", "useCombineActivators", "sensors", "getSyntheticHandler", "accumulator", "sensor", "Sensor", "sensorActivators", "map", "MeasuringStrategy", "MeasuringFrequency", "defaultValue", "Map", "useDroppableMeasuring", "containers", "dragging", "dependencies", "config", "queue", "setQueue", "useState", "frequency", "strategy", "containersRef", "isDisabled", "disabledRef", "useLatestValue", "measureDroppableContainers", "ids", "concat", "filter", "droppableRects", "previousValue", "container", "set", "measuringScheduled", "Always", "BeforeDragging", "useInitialValue", "computeFn", "useInitialRect", "useMutationObserver", "callback", "handleMutations", "useEvent", "mutationObserver", "MutationObserver", "disconnect", "useResizeObserver", "handleResize", "resizeObserver", "ResizeObserver", "defaultMeasure", "useRect", "fallbackRect", "setRect", "measureRect", "currentRect", "isConnected", "newRect", "records", "record", "type", "HTMLElement", "contains", "useIsomorphicLayoutEffect", "observe", "body", "childList", "subtree", "useRectDelta", "initialRect", "useScrollableAncestors", "previousNode", "ancestors", "useScrollOffsets", "elements", "scrollCoordinates", "setScrollCoordinates", "prevElements", "handleScroll", "previousElements", "cleanup", "entries", "scrollableElement", "entry", "Array", "from", "values", "useScrollOffsetsDelta", "initialScrollOffsets", "hasScrollOffsets", "subtract", "useSensorSetup", "teardownFns", "useSyntheticListeners", "useWindowRect", "useRects", "firstElement", "windowRect", "rects", "setRects", "measureRects", "getMeasurableNode", "children", "<PERSON><PERSON><PERSON><PERSON>", "useDragOverlayMeasuring", "handleNodeChange", "nodeRef", "setRef", "useNodeRef", "defaultSensors", "defaultData", "defaultMeasuringConfiguration", "draggable", "droppable", "WhileDragging", "Optimized", "dragOverlay", "DroppableContainersMap", "toArray", "getEnabled", "getNodeFor", "defaultPublicContext", "activatorEvent", "activeNodeRect", "collisions", "containerNodeRect", "droppableContainers", "over", "measuringConfiguration", "defaultInternalContext", "ariaDescribedById", "dispatch", "InternalContext", "createContext", "PublicContext", "getInitialState", "nodes", "translate", "reducer", "state", "action", "Action", "<PERSON><PERSON><PERSON><PERSON>", "DragEnd", "DragCancel", "RegisterDroppable", "SetDroppableDisabled", "UnregisterDroppable", "delete", "RestoreFocus", "useContext", "previousActivatorEvent", "previousActiveId", "activeElement", "requestAnimationFrame", "focusableNode", "findFirstFocusableNode", "focus", "applyModifiers", "modifiers", "args", "useMeasuringConfiguration", "useLayoutShiftScrollCompensation", "initialized", "rectD<PERSON><PERSON>", "ActiveDraggableContext", "Status", "DndContext", "memo", "accessibility", "collisionDetection", "rectIntersection", "measuring", "store", "useReducer", "dispatchMonitorEvent", "registerMonitorListener", "useDndMonitorProvider", "status", "setStatus", "Uninitialized", "isInitialized", "Initialized", "activeId", "activeRects", "initial", "translated", "data", "activeRef", "activeSensor", "setActiveSensor", "setActivatorEvent", "latestProps", "draggableDescribedById", "useUniqueId", "enabledDroppableContainers", "activationCoordinates", "autoScrollOptions", "getAutoScrollerOptions", "initialActiveNodeRect", "layoutShiftCompensation", "parentElement", "sensorContext", "draggingNode", "draggingNodeRect", "scrollAdjustedTranslate", "overNode", "usesDragOverlay", "nodeRectDelta", "modifiedTranslate", "overlayNodeRect", "scrollAdjustment", "activeNodeScrollDelta", "overId", "getFirstCollision", "setOver", "appliedTranslate", "activeSensorRef", "instantiateSensor", "sensorInstance", "onDragAbort", "onDragPending", "onDragStart", "unstable_batchedUpdates", "Initializing", "createHandler", "cancelDrop", "shouldCancel", "Promise", "resolve", "bindActivatorToSensorInstantiator", "activeDraggableNode", "dndKit", "defaultPrevented", "activationContext", "shouldActivate", "capturedBy", "onDragMove", "onDragOver", "over<PERSON><PERSON><PERSON>", "publicContext", "internalContext", "React", "DndMonitorContext", "Provider", "restoreFocus", "Accessibility", "hiddenTextDescribedById", "activeSensorDisablesAutoscroll", "autoScrollGloballyDisabled", "NullContext", "defaultRole", "ID_PREFIX", "useDraggable", "attributes", "role", "roleDescription", "tabIndex", "isDragging", "setNodeRef", "setActivatorNodeRef", "dataRef", "memoizedAttributes", "useDndContext", "defaultResizeObserverConfig", "timeout", "useDroppable", "resizeObserverConfig", "previous", "resizeObserverConnected", "callbackId", "resizeObserverDisabled", "updateMeasurementsFor", "resizeObserverTimeout", "isArray", "newElement", "previousElement", "unobserve", "isOver", "AnimationManager", "animation", "cloned<PERSON><PERSON><PERSON><PERSON>", "setClonedChildren", "setElement", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "then", "cloneElement", "ref", "defaultTransform", "NullifiedContextProvider", "baseStyles", "touchAction", "defaultTransition", "isKeyboardActivator", "PositionedOverlay", "forwardRef", "as", "className", "style", "transition", "scaleAdjustedTransform", "styles", "CSS", "Transform", "toString", "getRelativeTransformOrigin", "createElement", "defaultDropAnimationSideEffects", "originalStyles", "getPropertyValue", "setProperty", "classList", "remove", "defaultKeyframeResolver", "final", "defaultDropAnimationConfiguration", "duration", "easing", "keyframes", "sideEffects", "opacity", "useDropAnimation", "activeDraggable", "measurableNode", "createDefaultDropAnimation", "rest", "scale", "finalTransform", "animationKeyframes", "firstKeyframe", "lastKeyframe", "animate", "fill", "onfinish", "useKey", "DragOverlay", "dropAnimation", "dropAnimationConfig", "wrapperElement", "zIndex", "modifiedTransform", "arrayMove", "array", "from", "to", "newArray", "slice", "splice", "length", "getSortedRects", "items", "rects", "reduce", "accumulator", "id", "index", "rect", "get", "Array", "length", "isValidIndex", "itemsEqual", "a", "b", "i", "normalizeDisabled", "disabled", "draggable", "droppable", "rectSortingStrategy", "rects", "activeIndex", "overIndex", "index", "newRects", "arrayMove", "oldRect", "newRect", "x", "left", "y", "top", "scaleX", "width", "scaleY", "height", "defaultScale", "scaleX", "scaleY", "verticalListSortingStrategy", "activeIndex", "activeNodeRect", "fallbackActiveRect", "index", "rects", "overIndex", "overIndexRect", "x", "y", "top", "height", "itemGap", "getItemGap", "clientRects", "currentRect", "previousRect", "nextRect", "ID_PREFIX", "Context", "React", "createContext", "containerId", "disableTransforms", "items", "useDragOverlay", "sortedRects", "strategy", "rectSortingStrategy", "disabled", "draggable", "droppable", "SortableContext", "children", "id", "userDefinedItems", "disabledProp", "active", "dragOverlay", "droppableRects", "over", "measureDroppableContainers", "useDndContext", "useUniqueId", "Boolean", "rect", "useMemo", "map", "item", "isDragging", "indexOf", "previousItemsRef", "useRef", "itemsHaveChanged", "itemsEqual", "current", "normalizeDisabled", "useIsomorphicLayoutEffect", "useEffect", "contextValue", "getSortedRects", "Provider", "value", "defaultNewIndexGetter", "arrayMove", "defaultAnimateLayoutChanges", "isSorting", "wasDragging", "newIndex", "previousItems", "previousContainerId", "transition", "defaultTransition", "duration", "easing", "transitionProperty", "disabledTransition", "CSS", "Transition", "toString", "property", "defaultAttributes", "roleDescription", "useDerivedTransform", "node", "derivedTransform", "setDerivedtransform", "useState", "previousIndex", "initial", "getClientRect", "ignoreTransform", "delta", "left", "width", "useSortable", "animateLayoutChanges", "attributes", "userDefinedAttributes", "localDisabled", "data", "customData", "getNewIndex", "localStrategy", "resizeObserverConfig", "globalDisabled", "globalStrategy", "useContext", "normalizeLocalDisabled", "sortable", "itemsAfterCurrentSortable", "slice", "isOver", "setNodeRef", "setDroppableNodeRef", "useDroppable", "updateMeasurementsFor", "activatorEvent", "setDraggableNodeRef", "listeners", "setActivatorNodeRef", "transform", "useDraggable", "useCombinedRefs", "displaceItem", "isValidIndex", "shouldDisplaceDragSource", "dragSourceDisplacement", "finalTransform", "activeId", "previous", "shouldAnimateLayoutChanges", "timeoutId", "setTimeout", "clearTimeout", "getTransition", "isKeyboardEvent", "undefined", "hasSortableData", "entry", "directions", "KeyboardCode", "Down", "Right", "Up", "Left", "sortableKeyboardCoordinates", "event", "context", "collisionRect", "droppableContainers", "scrollableAncestors", "includes", "code", "preventDefault", "filteredContainers", "getEnabled", "for<PERSON>ach", "get", "push", "collisions", "closestCorners", "pointerCoordinates", "closestId", "getFirstCollision", "length", "activeDroppable", "newDroppable", "newRect", "newNode", "newScrollAncestors", "getScrollableAncestors", "hasDifferentScrollAncestors", "some", "element", "hasSameContainer", "isSameContainer", "isAfterActive", "isAfter", "offset", "rectCoordinates", "newCoordinates", "subtract", "a", "b"]}