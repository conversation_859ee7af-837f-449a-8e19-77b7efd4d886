{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-PDWBYQOW.mjs"], "sourcesContent": ["import {\n  currencies\n} from \"./chunk-MWVM4TYO.mjs\";\n\n// src/lib/money-amount-helpers.ts\nvar getDecimalDigits = (currency) => {\n  return currencies[currency.toUpperCase()]?.decimal_digits ?? 0;\n};\nvar getLocaleAmount = (amount, currencyCode) => {\n  const formatter = new Intl.NumberFormat([], {\n    style: \"currency\",\n    currencyDisplay: \"narrowSymbol\",\n    currency: currencyCode\n  });\n  return formatter.format(amount);\n};\nvar getNativeSymbol = (currencyCode) => {\n  const formatted = new Intl.NumberFormat([], {\n    style: \"currency\",\n    currency: currencyCode,\n    currencyDisplay: \"narrowSymbol\"\n  }).format(0);\n  return formatted.replace(/\\d/g, \"\").replace(/[.,]/g, \"\").trim();\n};\nvar getStylizedAmount = (amount, currencyCode) => {\n  const symbol = getNativeSymbol(currencyCode);\n  const decimalDigits = getDecimalDigits(currencyCode);\n  const lessThanRoundingPrecission = isAmountLessThenRoundingError(\n    amount,\n    currencyCode\n  );\n  const total = amount.toLocaleString(void 0, {\n    minimumFractionDigits: decimalDigits,\n    maximumFractionDigits: decimalDigits,\n    signDisplay: lessThanRoundingPrecission ? \"exceptZero\" : \"auto\"\n  });\n  return `${symbol} ${total} ${currencyCode.toUpperCase()}`;\n};\nvar isAmountLessThenRoundingError = (amount, currencyCode) => {\n  const decimalDigits = getDecimalDigits(currencyCode);\n  return Math.abs(amount) < 1 / 10 ** decimalDigits / 2;\n};\n\nexport {\n  getLocaleAmount,\n  getStylizedAmount,\n  isAmountLessThenRoundingError\n};\n"], "mappings": ";;;;;AAKA,IAAI,mBAAmB,CAAC,aAAa;AALrC;AAME,WAAO,gBAAW,SAAS,YAAY,CAAC,MAAjC,mBAAoC,mBAAkB;AAC/D;AACA,IAAI,kBAAkB,CAAC,QAAQ,iBAAiB;AAC9C,QAAM,YAAY,IAAI,KAAK,aAAa,CAAC,GAAG;AAAA,IAC1C,OAAO;AAAA,IACP,iBAAiB;AAAA,IACjB,UAAU;AAAA,EACZ,CAAC;AACD,SAAO,UAAU,OAAO,MAAM;AAChC;AACA,IAAI,kBAAkB,CAAC,iBAAiB;AACtC,QAAM,YAAY,IAAI,KAAK,aAAa,CAAC,GAAG;AAAA,IAC1C,OAAO;AAAA,IACP,UAAU;AAAA,IACV,iBAAiB;AAAA,EACnB,CAAC,EAAE,OAAO,CAAC;AACX,SAAO,UAAU,QAAQ,OAAO,EAAE,EAAE,QAAQ,SAAS,EAAE,EAAE,KAAK;AAChE;AACA,IAAI,oBAAoB,CAAC,QAAQ,iBAAiB;AAChD,QAAM,SAAS,gBAAgB,YAAY;AAC3C,QAAM,gBAAgB,iBAAiB,YAAY;AACnD,QAAM,6BAA6B;AAAA,IACjC;AAAA,IACA;AAAA,EACF;AACA,QAAM,QAAQ,OAAO,eAAe,QAAQ;AAAA,IAC1C,uBAAuB;AAAA,IACvB,uBAAuB;AAAA,IACvB,aAAa,6BAA6B,eAAe;AAAA,EAC3D,CAAC;AACD,SAAO,GAAG,MAAM,IAAI,KAAK,IAAI,aAAa,YAAY,CAAC;AACzD;AACA,IAAI,gCAAgC,CAAC,QAAQ,iBAAiB;AAC5D,QAAM,gBAAgB,iBAAiB,YAAY;AACnD,SAAO,KAAK,IAAI,MAAM,IAAI,IAAI,MAAM,gBAAgB;AACtD;", "names": []}