{"version": 3, "sources": ["../../@medusajs/dashboard/dist/product-variant-metadata-543SLWNL.mjs"], "sourcesContent": ["import {\n  MetadataForm\n} from \"./chunk-S2UEWRDA.mjs\";\nimport \"./chunk-IUCDCPJU.mjs\";\nimport \"./chunk-6HTZNHPT.mjs\";\nimport \"./chunk-4TC5YS65.mjs\";\nimport \"./chunk-LPEUYMRK.mjs\";\nimport \"./chunk-OC7BQLYI.mjs\";\nimport \"./chunk-OBQI23QM.mjs\";\nimport \"./chunk-Z5UDPQIH.mjs\";\nimport \"./chunk-KOSCMAIC.mjs\";\nimport \"./chunk-X6DSNTTX.mjs\";\nimport \"./chunk-I6E6CALJ.mjs\";\nimport \"./chunk-B4GODIOW.mjs\";\nimport \"./chunk-F6IJV2I2.mjs\";\nimport \"./chunk-QTCZFYFH.mjs\";\nimport \"./chunk-ENV6YVOM.mjs\";\nimport \"./chunk-PIR2H25N.mjs\";\nimport \"./chunk-RLY2SL5E.mjs\";\nimport \"./chunk-C5LYZZZ5.mjs\";\nimport \"./chunk-2ZKVRTBW.mjs\";\nimport \"./chunk-FO3VP56P.mjs\";\nimport \"./chunk-YS65UGPC.mjs\";\nimport \"./chunk-F6PXCY3N.mjs\";\nimport \"./chunk-3OHH43G6.mjs\";\nimport \"./chunk-G2H6MAK7.mjs\";\nimport \"./chunk-GRT22PE5.mjs\";\nimport \"./chunk-32IQRUVY.mjs\";\nimport \"./chunk-FNYASI54.mjs\";\nimport \"./chunk-FVC7M755.mjs\";\nimport \"./chunk-ZJ3OFMHB.mjs\";\nimport \"./chunk-PNU5HPGY.mjs\";\nimport \"./chunk-V2LANK5S.mjs\";\nimport \"./chunk-QZ6PT4QV.mjs\";\nimport \"./chunk-QL4XKIVL.mjs\";\nimport {\n  useProductVariant,\n  useUpdateProductVariant\n} from \"./chunk-6I62UDJA.mjs\";\nimport \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-RPUOO7AV.mjs\";\n\n// src/routes/product-variants/product-variant-metadata/product-variant-metadata.tsx\nimport { useParams } from \"react-router-dom\";\nimport { jsx } from \"react/jsx-runtime\";\nvar ProductVariantMetadata = () => {\n  const { id, variant_id } = useParams();\n  const { variant, isPending, isError, error } = useProductVariant(\n    id,\n    variant_id\n  );\n  const { mutateAsync, isPending: isMutating } = useUpdateProductVariant(\n    id,\n    variant_id\n  );\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsx(\n    MetadataForm,\n    {\n      metadata: variant?.metadata,\n      hook: mutateAsync,\n      isPending,\n      isMutating\n    }\n  );\n};\nexport {\n  ProductVariantMetadata as Component\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8CA,yBAAoB;AACpB,IAAI,yBAAyB,MAAM;AACjC,QAAM,EAAE,IAAI,WAAW,IAAI,UAAU;AACrC,QAAM,EAAE,SAAS,WAAW,SAAS,MAAM,IAAI;AAAA,IAC7C;AAAA,IACA;AAAA,EACF;AACA,QAAM,EAAE,aAAa,WAAW,WAAW,IAAI;AAAA,IAC7C;AAAA,IACA;AAAA,EACF;AACA,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB;AAAA,IACrB;AAAA,IACA;AAAA,MACE,UAAU,mCAAS;AAAA,MACnB,MAAM;AAAA,MACN;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACF;", "names": []}