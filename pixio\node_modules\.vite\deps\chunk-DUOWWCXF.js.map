{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-5CCKT6WV.mjs"], "sourcesContent": ["import {\n  ordersQueryKeys\n} from \"./chunk-FNYASI54.mjs\";\nimport {\n  reservationItemsQueryKeys\n} from \"./chunk-FVC7M755.mjs\";\nimport {\n  inventoryItemsQueryKeys\n} from \"./chunk-6I62UDJA.mjs\";\nimport {\n  queryClient\n} from \"./chunk-FXYH54JP.mjs\";\nimport {\n  sdk\n} from \"./chunk-DEQUVHHE.mjs\";\n\n// src/hooks/api/order-edits.tsx\nimport { useMutation } from \"@tanstack/react-query\";\nvar useCreateOrderEdit = (orderId, options) => {\n  return useMutation({\n    mutationFn: (payload) => sdk.admin.orderEdit.initiateRequest(payload),\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries({\n        queryKey: ordersQueryKeys.details()\n      });\n      queryClient.invalidateQueries({\n        queryKey: ordersQueryKeys.preview(orderId)\n      });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\nvar useRequestOrderEdit = (id, options) => {\n  return useMutation({\n    mutationFn: () => sdk.admin.orderEdit.request(id),\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries({\n        queryKey: ordersQueryKeys.details()\n      });\n      queryClient.invalidateQueries({\n        queryKey: ordersQueryKeys.preview(id)\n      });\n      queryClient.invalidateQueries({\n        queryKey: ordersQueryKeys.changes(id)\n      });\n      queryClient.invalidateQueries({\n        queryKey: ordersQueryKeys.lineItems(id)\n      });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\nvar useConfirmOrderEdit = (id, options) => {\n  return useMutation({\n    mutationFn: () => sdk.admin.orderEdit.confirm(id),\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries({\n        queryKey: ordersQueryKeys.details()\n      });\n      queryClient.invalidateQueries({\n        queryKey: ordersQueryKeys.preview(id)\n      });\n      queryClient.invalidateQueries({\n        queryKey: ordersQueryKeys.changes(id)\n      });\n      queryClient.invalidateQueries({\n        queryKey: ordersQueryKeys.lineItems(id)\n      });\n      queryClient.invalidateQueries({\n        queryKey: reservationItemsQueryKeys.lists()\n      });\n      queryClient.invalidateQueries({\n        queryKey: inventoryItemsQueryKeys.lists()\n      });\n      queryClient.invalidateQueries({\n        queryKey: inventoryItemsQueryKeys.details()\n      });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\nvar useCancelOrderEdit = (orderId, options) => {\n  return useMutation({\n    mutationFn: () => sdk.admin.orderEdit.cancelRequest(orderId),\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries({\n        queryKey: ordersQueryKeys.details()\n      });\n      queryClient.invalidateQueries({\n        queryKey: ordersQueryKeys.preview(orderId)\n      });\n      queryClient.invalidateQueries({\n        queryKey: ordersQueryKeys.changes(orderId)\n      });\n      queryClient.invalidateQueries({\n        queryKey: ordersQueryKeys.lineItems(orderId)\n      });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\nvar useAddOrderEditItems = (id, options) => {\n  return useMutation({\n    mutationFn: (payload) => sdk.admin.orderEdit.addItems(id, payload),\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries({\n        queryKey: ordersQueryKeys.preview(id)\n      });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\nvar useUpdateOrderEditOriginalItem = (id, options) => {\n  return useMutation({\n    mutationFn: ({\n      itemId,\n      ...payload\n    }) => {\n      return sdk.admin.orderEdit.updateOriginalItem(id, itemId, payload);\n    },\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries({\n        queryKey: ordersQueryKeys.preview(id)\n      });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\nvar useUpdateOrderEditAddedItem = (id, options) => {\n  return useMutation({\n    mutationFn: ({\n      actionId,\n      ...payload\n    }) => {\n      return sdk.admin.orderEdit.updateAddedItem(id, actionId, payload);\n    },\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries({\n        queryKey: ordersQueryKeys.preview(id)\n      });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\nvar useRemoveOrderEditItem = (id, options) => {\n  return useMutation({\n    mutationFn: (actionId) => sdk.admin.orderEdit.removeAddedItem(id, actionId),\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries({\n        queryKey: ordersQueryKeys.preview(id)\n      });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\n\nexport {\n  useCreateOrderEdit,\n  useRequestOrderEdit,\n  useConfirmOrderEdit,\n  useCancelOrderEdit,\n  useAddOrderEditItems,\n  useUpdateOrderEditOriginalItem,\n  useUpdateOrderEditAddedItem,\n  useRemoveOrderEditItem\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAkBA,IAAI,qBAAqB,CAAC,SAAS,YAAY;AAC7C,SAAO,YAAY;AAAA,IACjB,YAAY,CAAC,YAAY,IAAI,MAAM,UAAU,gBAAgB,OAAO;AAAA,IACpE,WAAW,CAAC,MAAM,WAAW,YAAY;AArB7C;AAsBM,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,gBAAgB,QAAQ;AAAA,MACpC,CAAC;AACD,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,gBAAgB,QAAQ,OAAO;AAAA,MAC3C,CAAC;AACD,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;AACA,IAAI,sBAAsB,CAAC,IAAI,YAAY;AACzC,SAAO,YAAY;AAAA,IACjB,YAAY,MAAM,IAAI,MAAM,UAAU,QAAQ,EAAE;AAAA,IAChD,WAAW,CAAC,MAAM,WAAW,YAAY;AApC7C;AAqCM,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,gBAAgB,QAAQ;AAAA,MACpC,CAAC;AACD,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,gBAAgB,QAAQ,EAAE;AAAA,MACtC,CAAC;AACD,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,gBAAgB,QAAQ,EAAE;AAAA,MACtC,CAAC;AACD,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,gBAAgB,UAAU,EAAE;AAAA,MACxC,CAAC;AACD,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;AACA,IAAI,sBAAsB,CAAC,IAAI,YAAY;AACzC,SAAO,YAAY;AAAA,IACjB,YAAY,MAAM,IAAI,MAAM,UAAU,QAAQ,EAAE;AAAA,IAChD,WAAW,CAAC,MAAM,WAAW,YAAY;AAzD7C;AA0DM,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,gBAAgB,QAAQ;AAAA,MACpC,CAAC;AACD,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,gBAAgB,QAAQ,EAAE;AAAA,MACtC,CAAC;AACD,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,gBAAgB,QAAQ,EAAE;AAAA,MACtC,CAAC;AACD,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,gBAAgB,UAAU,EAAE;AAAA,MACxC,CAAC;AACD,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,0BAA0B,MAAM;AAAA,MAC5C,CAAC;AACD,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,wBAAwB,MAAM;AAAA,MAC1C,CAAC;AACD,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,wBAAwB,QAAQ;AAAA,MAC5C,CAAC;AACD,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;AACA,IAAI,qBAAqB,CAAC,SAAS,YAAY;AAC7C,SAAO,YAAY;AAAA,IACjB,YAAY,MAAM,IAAI,MAAM,UAAU,cAAc,OAAO;AAAA,IAC3D,WAAW,CAAC,MAAM,WAAW,YAAY;AAvF7C;AAwFM,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,gBAAgB,QAAQ;AAAA,MACpC,CAAC;AACD,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,gBAAgB,QAAQ,OAAO;AAAA,MAC3C,CAAC;AACD,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,gBAAgB,QAAQ,OAAO;AAAA,MAC3C,CAAC;AACD,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,gBAAgB,UAAU,OAAO;AAAA,MAC7C,CAAC;AACD,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;AACA,IAAI,uBAAuB,CAAC,IAAI,YAAY;AAC1C,SAAO,YAAY;AAAA,IACjB,YAAY,CAAC,YAAY,IAAI,MAAM,UAAU,SAAS,IAAI,OAAO;AAAA,IACjE,WAAW,CAAC,MAAM,WAAW,YAAY;AA5G7C;AA6GM,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,gBAAgB,QAAQ,EAAE;AAAA,MACtC,CAAC;AACD,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;AACA,IAAI,iCAAiC,CAAC,IAAI,YAAY;AACpD,SAAO,YAAY;AAAA,IACjB,YAAY,CAAC;AAAA,MACX;AAAA,MACA,GAAG;AAAA,IACL,MAAM;AACJ,aAAO,IAAI,MAAM,UAAU,mBAAmB,IAAI,QAAQ,OAAO;AAAA,IACnE;AAAA,IACA,WAAW,CAAC,MAAM,WAAW,YAAY;AA7H7C;AA8HM,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,gBAAgB,QAAQ,EAAE;AAAA,MACtC,CAAC;AACD,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;AACA,IAAI,8BAA8B,CAAC,IAAI,YAAY;AACjD,SAAO,YAAY;AAAA,IACjB,YAAY,CAAC;AAAA,MACX;AAAA,MACA,GAAG;AAAA,IACL,MAAM;AACJ,aAAO,IAAI,MAAM,UAAU,gBAAgB,IAAI,UAAU,OAAO;AAAA,IAClE;AAAA,IACA,WAAW,CAAC,MAAM,WAAW,YAAY;AA9I7C;AA+IM,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,gBAAgB,QAAQ,EAAE;AAAA,MACtC,CAAC;AACD,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;AACA,IAAI,yBAAyB,CAAC,IAAI,YAAY;AAC5C,SAAO,YAAY;AAAA,IACjB,YAAY,CAAC,aAAa,IAAI,MAAM,UAAU,gBAAgB,IAAI,QAAQ;AAAA,IAC1E,WAAW,CAAC,MAAM,WAAW,YAAY;AA1J7C;AA2JM,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,gBAAgB,QAAQ,EAAE;AAAA,MACtC,CAAC;AACD,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;", "names": []}