{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-G2J2T2QU.mjs"], "sourcesContent": ["import {\n  castNumber\n} from \"./chunk-6GU6IDUA.mjs\";\n\n// src/routes/price-lists/common/utils.ts\nimport { json } from \"react-router-dom\";\nvar getValues = (priceList) => {\n  const startsAt = priceList.starts_at;\n  const endsAt = priceList.ends_at;\n  const isExpired = endsAt ? new Date(endsAt) < /* @__PURE__ */ new Date() : false;\n  const isScheduled = startsAt ? new Date(startsAt) > /* @__PURE__ */ new Date() : false;\n  const isDraft = priceList.status === \"draft\" /* DRAFT */;\n  return {\n    isExpired,\n    isScheduled,\n    isDraft\n  };\n};\nvar getPriceListStatus = (t, priceList) => {\n  const { isExpired, isScheduled, isDraft } = getValues(priceList);\n  let text = t(\"priceLists.fields.status.options.active\");\n  let color = \"green\";\n  let status = \"active\" /* ACTIVE */;\n  if (isDraft) {\n    color = \"grey\";\n    text = t(\"priceLists.fields.status.options.draft\");\n    status = \"draft\" /* DRAFT */;\n  }\n  if (isExpired) {\n    color = \"red\";\n    text = t(\"priceLists.fields.status.options.expired\");\n    status = \"expired\" /* EXPIRED */;\n  }\n  if (isScheduled) {\n    color = \"orange\";\n    text = t(\"priceLists.fields.status.options.scheduled\");\n    status = \"scheduled\" /* SCHEDULED */;\n  }\n  return {\n    color,\n    text,\n    status\n  };\n};\nvar isProductRow = (row) => {\n  return \"variants\" in row;\n};\nvar extractPricesFromVariants = (variantId, variant, regions) => {\n  const extractPriceDetails = (price, priceType, id) => {\n    const currencyCode = priceType === \"currency\" ? id : regions.find((r) => r.id === id)?.currency_code;\n    if (!currencyCode) {\n      throw json({ message: \"Currency code not found\" }, 400);\n    }\n    return {\n      amount: castNumber(price.amount),\n      ...priceType === \"region\" ? { rules: { region_id: id } } : {},\n      currency_code: currencyCode,\n      variant_id: variantId\n    };\n  };\n  const currencyPrices = Object.entries(variant.currency_prices || {}).flatMap(\n    ([currencyCode, currencyPrice]) => {\n      return currencyPrice?.amount ? [extractPriceDetails(currencyPrice, \"currency\", currencyCode)] : [];\n    }\n  );\n  const regionPrices = Object.entries(variant.region_prices || {}).flatMap(\n    ([regionId, regionPrice]) => {\n      return regionPrice?.amount ? [extractPriceDetails(regionPrice, \"region\", regionId)] : [];\n    }\n  );\n  return [...currencyPrices, ...regionPrices];\n};\nvar exctractPricesFromProducts = (products, regions) => {\n  return Object.values(products).flatMap(\n    ({ variants }) => Object.entries(variants).flatMap(\n      ([variantId, variant]) => extractPricesFromVariants(variantId, variant, regions)\n    )\n  );\n};\n\nexport {\n  getPriceListStatus,\n  isProductRow,\n  exctractPricesFromProducts\n};\n"], "mappings": ";;;;;;;;AAMA,IAAI,YAAY,CAAC,cAAc;AAC7B,QAAM,WAAW,UAAU;AAC3B,QAAM,SAAS,UAAU;AACzB,QAAM,YAAY,SAAS,IAAI,KAAK,MAAM,IAAoB,oBAAI,KAAK,IAAI;AAC3E,QAAM,cAAc,WAAW,IAAI,KAAK,QAAQ,IAAoB,oBAAI,KAAK,IAAI;AACjF,QAAM,UAAU,UAAU,WAAW;AACrC,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AACA,IAAI,qBAAqB,CAAC,GAAG,cAAc;AACzC,QAAM,EAAE,WAAW,aAAa,QAAQ,IAAI,UAAU,SAAS;AAC/D,MAAI,OAAO,EAAE,yCAAyC;AACtD,MAAI,QAAQ;AACZ,MAAI,SAAS;AACb,MAAI,SAAS;AACX,YAAQ;AACR,WAAO,EAAE,wCAAwC;AACjD,aAAS;AAAA,EACX;AACA,MAAI,WAAW;AACb,YAAQ;AACR,WAAO,EAAE,0CAA0C;AACnD,aAAS;AAAA,EACX;AACA,MAAI,aAAa;AACf,YAAQ;AACR,WAAO,EAAE,4CAA4C;AACrD,aAAS;AAAA,EACX;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AACA,IAAI,eAAe,CAAC,QAAQ;AAC1B,SAAO,cAAc;AACvB;AACA,IAAI,4BAA4B,CAAC,WAAW,SAAS,YAAY;AAC/D,QAAM,sBAAsB,CAAC,OAAO,WAAW,OAAO;AAhDxD;AAiDI,UAAM,eAAe,cAAc,aAAa,MAAK,aAAQ,KAAK,CAAC,MAAM,EAAE,OAAO,EAAE,MAA/B,mBAAkC;AACvF,QAAI,CAAC,cAAc;AACjB,YAAM,KAAK,EAAE,SAAS,0BAA0B,GAAG,GAAG;AAAA,IACxD;AACA,WAAO;AAAA,MACL,QAAQ,WAAW,MAAM,MAAM;AAAA,MAC/B,GAAG,cAAc,WAAW,EAAE,OAAO,EAAE,WAAW,GAAG,EAAE,IAAI,CAAC;AAAA,MAC5D,eAAe;AAAA,MACf,YAAY;AAAA,IACd;AAAA,EACF;AACA,QAAM,iBAAiB,OAAO,QAAQ,QAAQ,mBAAmB,CAAC,CAAC,EAAE;AAAA,IACnE,CAAC,CAAC,cAAc,aAAa,MAAM;AACjC,cAAO,+CAAe,UAAS,CAAC,oBAAoB,eAAe,YAAY,YAAY,CAAC,IAAI,CAAC;AAAA,IACnG;AAAA,EACF;AACA,QAAM,eAAe,OAAO,QAAQ,QAAQ,iBAAiB,CAAC,CAAC,EAAE;AAAA,IAC/D,CAAC,CAAC,UAAU,WAAW,MAAM;AAC3B,cAAO,2CAAa,UAAS,CAAC,oBAAoB,aAAa,UAAU,QAAQ,CAAC,IAAI,CAAC;AAAA,IACzF;AAAA,EACF;AACA,SAAO,CAAC,GAAG,gBAAgB,GAAG,YAAY;AAC5C;AACA,IAAI,6BAA6B,CAAC,UAAU,YAAY;AACtD,SAAO,OAAO,OAAO,QAAQ,EAAE;AAAA,IAC7B,CAAC,EAAE,SAAS,MAAM,OAAO,QAAQ,QAAQ,EAAE;AAAA,MACzC,CAAC,CAAC,WAAW,OAAO,MAAM,0BAA0B,WAAW,SAAS,OAAO;AAAA,IACjF;AAAA,EACF;AACF;", "names": []}