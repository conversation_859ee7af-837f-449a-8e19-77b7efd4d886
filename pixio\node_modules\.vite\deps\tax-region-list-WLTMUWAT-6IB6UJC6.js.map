{"version": 3, "sources": ["../../@medusajs/dashboard/dist/tax-region-list-WLTMUWAT.mjs"], "sourcesContent": ["import {\n  TaxRegionTable,\n  useTaxRegionTable\n} from \"./chunk-ATYH24XU.mjs\";\nimport \"./chunk-4FUGAJJD.mjs\";\nimport \"./chunk-THZJC662.mjs\";\nimport {\n  useTaxRegionTableQuery\n} from \"./chunk-RIV7FKGN.mjs\";\nimport \"./chunk-AOFGTNG6.mjs\";\nimport \"./chunk-EMIHDNB7.mjs\";\nimport \"./chunk-EQTBJSBZ.mjs\";\nimport {\n  SingleColumnPage\n} from \"./chunk-2RQLKDBF.mjs\";\nimport \"./chunk-C76H5USB.mjs\";\nimport \"./chunk-DG7J63J2.mjs\";\nimport {\n  useExtension\n} from \"./chunk-C5P5PL3E.mjs\";\nimport \"./chunk-LPEUYMRK.mjs\";\nimport \"./chunk-3NJTXRIY.mjs\";\nimport \"./chunk-OC7BQLYI.mjs\";\nimport \"./chunk-X6DSNTTX.mjs\";\nimport {\n  useTaxRegions\n} from \"./chunk-I6E6CALJ.mjs\";\nimport \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-RPUOO7AV.mjs\";\n\n// src/routes/tax-regions/tax-region-list/components/tax-region-list-view/tax-region-list-view.tsx\nimport { Container, Heading, Text } from \"@medusajs/ui\";\nimport { keepPreviousData } from \"@tanstack/react-query\";\nimport { useTranslation } from \"react-i18next\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar PAGE_SIZE = 20;\nvar TaxRegionListView = () => {\n  const { t } = useTranslation();\n  const { searchParams, raw } = useTaxRegionTableQuery({\n    pageSize: PAGE_SIZE\n  });\n  const { tax_regions, count, isPending, isError, error } = useTaxRegions(\n    {\n      ...searchParams,\n      order: \"country_code\",\n      parent_id: \"null\"\n    },\n    {\n      placeholderData: keepPreviousData\n    }\n  );\n  const { table } = useTaxRegionTable({\n    count,\n    data: tax_regions,\n    pageSize: PAGE_SIZE\n  });\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsx(Container, { className: \"divide-y p-0\", children: /* @__PURE__ */ jsxs(\n    TaxRegionTable,\n    {\n      action: {\n        to: \"create\",\n        label: t(\"actions.create\")\n      },\n      isPending,\n      queryObject: raw,\n      table,\n      count,\n      children: [\n        /* @__PURE__ */ jsx(Heading, { children: t(\"taxes.domain\") }),\n        /* @__PURE__ */ jsx(Text, { size: \"small\", className: \"text-ui-fg-subtle text-pretty\", children: t(\"taxRegions.list.hint\") })\n      ]\n    }\n  ) });\n};\n\n// src/routes/tax-regions/tax-region-list/tax-region-list.tsx\nimport { jsx as jsx2 } from \"react/jsx-runtime\";\nvar TaxRegionsList = () => {\n  const { getWidgets } = useExtension();\n  return /* @__PURE__ */ jsx2(\n    SingleColumnPage,\n    {\n      widgets: {\n        before: getWidgets(\"tax.list.before\"),\n        after: getWidgets(\"tax.list.after\")\n      },\n      hasOutlet: true,\n      children: /* @__PURE__ */ jsx2(TaxRegionListView, {})\n    }\n  );\n};\nexport {\n  TaxRegionsList as Component\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoCA,yBAA0B;AA6C1B,IAAAA,sBAA4B;AA5C5B,IAAI,YAAY;AAChB,IAAI,oBAAoB,MAAM;AAC5B,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,QAAM,EAAE,cAAc,IAAI,IAAI,uBAAuB;AAAA,IACnD,UAAU;AAAA,EACZ,CAAC;AACD,QAAM,EAAE,aAAa,OAAO,WAAW,SAAS,MAAM,IAAI;AAAA,IACxD;AAAA,MACE,GAAG;AAAA,MACH,OAAO;AAAA,MACP,WAAW;AAAA,IACb;AAAA,IACA;AAAA,MACE,iBAAiB;AAAA,IACnB;AAAA,EACF;AACA,QAAM,EAAE,MAAM,IAAI,kBAAkB;AAAA,IAClC;AAAA,IACA,MAAM;AAAA,IACN,UAAU;AAAA,EACZ,CAAC;AACD,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,wBAAI,WAAW,EAAE,WAAW,gBAAgB,cAA0B;AAAA,IAC3F;AAAA,IACA;AAAA,MACE,QAAQ;AAAA,QACN,IAAI;AAAA,QACJ,OAAO,EAAE,gBAAgB;AAAA,MAC3B;AAAA,MACA;AAAA,MACA,aAAa;AAAA,MACb;AAAA,MACA;AAAA,MACA,UAAU;AAAA,YACQ,wBAAI,SAAS,EAAE,UAAU,EAAE,cAAc,EAAE,CAAC;AAAA,YAC5C,wBAAI,MAAM,EAAE,MAAM,SAAS,WAAW,iCAAiC,UAAU,EAAE,sBAAsB,EAAE,CAAC;AAAA,MAC9H;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;AAIA,IAAI,iBAAiB,MAAM;AACzB,QAAM,EAAE,WAAW,IAAI,aAAa;AACpC,aAAuB,oBAAAC;AAAA,IACrB;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP,QAAQ,WAAW,iBAAiB;AAAA,QACpC,OAAO,WAAW,gBAAgB;AAAA,MACpC;AAAA,MACA,WAAW;AAAA,MACX,cAA0B,oBAAAA,KAAK,mBAAmB,CAAC,CAAC;AAAA,IACtD;AAAA,EACF;AACF;", "names": ["import_jsx_runtime", "jsx2"]}