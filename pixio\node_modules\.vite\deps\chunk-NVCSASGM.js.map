{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-IQBAUTU5.mjs"], "sourcesContent": ["import {\n  Thumbnail\n} from \"./chunk-MNXC6Q4F.mjs\";\n\n// src/components/table/table-cells/product/product-cell/product-cell.tsx\nimport { useTranslation } from \"react-i18next\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar ProductCell = ({ product }) => {\n  return /* @__PURE__ */ jsxs(\"div\", { className: \"flex h-full w-full max-w-[250px] items-center gap-x-3 overflow-hidden\", children: [\n    /* @__PURE__ */ jsx(\"div\", { className: \"w-fit flex-shrink-0\", children: /* @__PURE__ */ jsx(Thumbnail, { src: product.thumbnail }) }),\n    /* @__PURE__ */ jsx(\"span\", { title: product.title, className: \"truncate\", children: product.title })\n  ] });\n};\nvar ProductHeader = () => {\n  const { t } = useTranslation();\n  return /* @__PURE__ */ jsx(\"div\", { className: \"flex h-full w-full items-center\", children: /* @__PURE__ */ jsx(\"span\", { children: t(\"fields.product\") }) });\n};\n\nexport {\n  ProductCell,\n  ProductHeader\n};\n"], "mappings": ";;;;;;;;;;;;;;AAMA,yBAA0B;AAC1B,IAAI,cAAc,CAAC,EAAE,QAAQ,MAAM;AACjC,aAAuB,yBAAK,OAAO,EAAE,WAAW,yEAAyE,UAAU;AAAA,QACjH,wBAAI,OAAO,EAAE,WAAW,uBAAuB,cAA0B,wBAAI,WAAW,EAAE,KAAK,QAAQ,UAAU,CAAC,EAAE,CAAC;AAAA,QACrH,wBAAI,QAAQ,EAAE,OAAO,QAAQ,OAAO,WAAW,YAAY,UAAU,QAAQ,MAAM,CAAC;AAAA,EACtG,EAAE,CAAC;AACL;AACA,IAAI,gBAAgB,MAAM;AACxB,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,aAAuB,wBAAI,OAAO,EAAE,WAAW,mCAAmC,cAA0B,wBAAI,QAAQ,EAAE,UAAU,EAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;AAC9J;", "names": []}