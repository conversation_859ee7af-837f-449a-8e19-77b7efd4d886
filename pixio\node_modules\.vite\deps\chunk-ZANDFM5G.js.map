{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-BTU5QU35.mjs"], "sourcesContent": ["import {\n  useDate\n} from \"./chunk-KSV3NQOT.mjs\";\n\n// src/components/common/date-range-display/date-range-display.tsx\nimport { Text, clx } from \"@medusajs/ui\";\nimport { useTranslation } from \"react-i18next\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar DateRangeDisplay = ({\n  startsAt,\n  endsAt,\n  showTime = false\n}) => {\n  const startDate = startsAt ? new Date(startsAt) : null;\n  const endDate = endsAt ? new Date(endsAt) : null;\n  const { t } = useTranslation();\n  const { getFullDate } = useDate();\n  return /* @__PURE__ */ jsxs(\"div\", { className: \"grid gap-3 md:grid-cols-2\", children: [\n    /* @__PURE__ */ jsxs(\"div\", { className: \"shadow-elevation-card-rest bg-ui-bg-component text-ui-fg-subtle flex items-center gap-x-3 rounded-md px-3 py-1.5\", children: [\n      /* @__PURE__ */ jsx(Bar, { date: startDate }),\n      /* @__PURE__ */ jsxs(\"div\", { children: [\n        /* @__PURE__ */ jsx(Text, { weight: \"plus\", size: \"small\", children: t(\"fields.startDate\") }),\n        /* @__PURE__ */ jsx(Text, { size: \"small\", className: \"tabular-nums\", children: startDate ? getFullDate({\n          date: startDate,\n          includeTime: showTime\n        }) : \"-\" })\n      ] })\n    ] }),\n    /* @__PURE__ */ jsxs(\"div\", { className: \"shadow-elevation-card-rest bg-ui-bg-component text-ui-fg-subtle flex items-center gap-x-3 rounded-md px-3 py-1.5\", children: [\n      /* @__PURE__ */ jsx(Bar, { date: endDate }),\n      /* @__PURE__ */ jsxs(\"div\", { children: [\n        /* @__PURE__ */ jsx(Text, { size: \"small\", weight: \"plus\", children: t(\"fields.endDate\") }),\n        /* @__PURE__ */ jsx(Text, { size: \"small\", className: \"tabular-nums\", children: endDate ? getFullDate({\n          date: endDate,\n          includeTime: showTime\n        }) : \"-\" })\n      ] })\n    ] })\n  ] });\n};\nvar Bar = ({ date }) => {\n  const now = /* @__PURE__ */ new Date();\n  const isDateInFuture = date && date > now;\n  return /* @__PURE__ */ jsx(\n    \"div\",\n    {\n      className: clx(\"bg-ui-tag-neutral-icon h-8 w-1 rounded-full\", {\n        \"bg-ui-tag-orange-icon\": isDateInFuture\n      })\n    }\n  );\n};\n\nexport {\n  DateRangeDisplay\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAOA,yBAA0B;AAC1B,IAAI,mBAAmB,CAAC;AAAA,EACtB;AAAA,EACA;AAAA,EACA,WAAW;AACb,MAAM;AACJ,QAAM,YAAY,WAAW,IAAI,KAAK,QAAQ,IAAI;AAClD,QAAM,UAAU,SAAS,IAAI,KAAK,MAAM,IAAI;AAC5C,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,QAAM,EAAE,YAAY,IAAI,QAAQ;AAChC,aAAuB,yBAAK,OAAO,EAAE,WAAW,6BAA6B,UAAU;AAAA,QACrE,yBAAK,OAAO,EAAE,WAAW,oHAAoH,UAAU;AAAA,UACrJ,wBAAI,KAAK,EAAE,MAAM,UAAU,CAAC;AAAA,UAC5B,yBAAK,OAAO,EAAE,UAAU;AAAA,YACtB,wBAAI,MAAM,EAAE,QAAQ,QAAQ,MAAM,SAAS,UAAU,EAAE,kBAAkB,EAAE,CAAC;AAAA,YAC5E,wBAAI,MAAM,EAAE,MAAM,SAAS,WAAW,gBAAgB,UAAU,YAAY,YAAY;AAAA,UACtG,MAAM;AAAA,UACN,aAAa;AAAA,QACf,CAAC,IAAI,IAAI,CAAC;AAAA,MACZ,EAAE,CAAC;AAAA,IACL,EAAE,CAAC;AAAA,QACa,yBAAK,OAAO,EAAE,WAAW,oHAAoH,UAAU;AAAA,UACrJ,wBAAI,KAAK,EAAE,MAAM,QAAQ,CAAC;AAAA,UAC1B,yBAAK,OAAO,EAAE,UAAU;AAAA,YACtB,wBAAI,MAAM,EAAE,MAAM,SAAS,QAAQ,QAAQ,UAAU,EAAE,gBAAgB,EAAE,CAAC;AAAA,YAC1E,wBAAI,MAAM,EAAE,MAAM,SAAS,WAAW,gBAAgB,UAAU,UAAU,YAAY;AAAA,UACpG,MAAM;AAAA,UACN,aAAa;AAAA,QACf,CAAC,IAAI,IAAI,CAAC;AAAA,MACZ,EAAE,CAAC;AAAA,IACL,EAAE,CAAC;AAAA,EACL,EAAE,CAAC;AACL;AACA,IAAI,MAAM,CAAC,EAAE,KAAK,MAAM;AACtB,QAAM,MAAsB,oBAAI,KAAK;AACrC,QAAM,iBAAiB,QAAQ,OAAO;AACtC,aAAuB;AAAA,IACrB;AAAA,IACA;AAAA,MACE,WAAW,IAAI,+CAA+C;AAAA,QAC5D,yBAAyB;AAAA,MAC3B,CAAC;AAAA,IACH;AAAA,EACF;AACF;", "names": []}