{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-YIZSVS2R.mjs"], "sourcesContent": ["// src/hooks/use-combobox-data.tsx\nimport {\n  keepPreviousData,\n  useInfiniteQuery,\n  useQuery\n} from \"@tanstack/react-query\";\n\n// src/hooks/use-debounced-search.tsx\nimport debounce from \"lodash/debounce\";\nimport { useCallback, useEffect, useState } from \"react\";\nvar useDebouncedSearch = () => {\n  const [searchValue, onSearchValueChange] = useState(\"\");\n  const [debouncedQuery, setDebouncedQuery] = useState(\"\");\n  const debouncedUpdate = useCallback(\n    debounce((query) => setDebouncedQuery(query), 300),\n    []\n  );\n  useEffect(() => {\n    debouncedUpdate(searchValue);\n    return () => debouncedUpdate.cancel();\n  }, [searchValue, debouncedUpdate]);\n  return {\n    searchValue,\n    onSearchValueChange,\n    query: debouncedQuery || void 0\n  };\n};\n\n// src/hooks/use-combobox-data.tsx\nvar useComboboxData = ({\n  queryKey,\n  queryFn,\n  getOptions,\n  defaultValue,\n  defaultValueKey,\n  pageSize = 10,\n  enabled = true\n}) => {\n  const { searchValue, onSearchValueChange, query } = useDebouncedSearch();\n  const queryInitialDataBy = defaultValueKey || \"id\";\n  const { data: initialData } = useQuery({\n    queryKey,\n    queryFn: async () => {\n      return queryFn({\n        [queryInitialDataBy]: defaultValue,\n        limit: Array.isArray(defaultValue) ? defaultValue.length : 1\n      });\n    },\n    enabled: !!defaultValue && enabled\n  });\n  const { data, ...rest } = useInfiniteQuery({\n    queryKey: [...queryKey, query],\n    queryFn: async ({ pageParam = 0 }) => {\n      return await queryFn({\n        q: query,\n        limit: pageSize,\n        offset: pageParam\n      });\n    },\n    initialPageParam: 0,\n    getNextPageParam: (lastPage) => {\n      const moreItemsExist = lastPage.count > lastPage.offset + lastPage.limit;\n      return moreItemsExist ? lastPage.offset + lastPage.limit : void 0;\n    },\n    placeholderData: keepPreviousData,\n    enabled\n  });\n  const options = data?.pages.flatMap((page) => getOptions(page)) ?? [];\n  const defaultOptions = initialData ? getOptions(initialData) : [];\n  const disabled = !rest.isPending && !options.length && !searchValue || !enabled;\n  if (defaultValue && defaultOptions.length && !searchValue) {\n    defaultOptions.forEach((option) => {\n      if (!options.find((o) => o.value === option.value)) {\n        options.unshift(option);\n      }\n    });\n  }\n  return {\n    options,\n    searchValue,\n    onSearchValueChange,\n    disabled,\n    ...rest\n  };\n};\n\nexport {\n  useDebouncedSearch,\n  useComboboxData\n};\n"], "mappings": ";;;;;;;;;;;;;;;;AAQA,sBAAqB;AACrB,mBAAiD;AACjD,IAAI,qBAAqB,MAAM;AAC7B,QAAM,CAAC,aAAa,mBAAmB,QAAI,uBAAS,EAAE;AACtD,QAAM,CAAC,gBAAgB,iBAAiB,QAAI,uBAAS,EAAE;AACvD,QAAM,sBAAkB;AAAA,QACtB,gBAAAA,SAAS,CAAC,UAAU,kBAAkB,KAAK,GAAG,GAAG;AAAA,IACjD,CAAC;AAAA,EACH;AACA,8BAAU,MAAM;AACd,oBAAgB,WAAW;AAC3B,WAAO,MAAM,gBAAgB,OAAO;AAAA,EACtC,GAAG,CAAC,aAAa,eAAe,CAAC;AACjC,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,OAAO,kBAAkB;AAAA,EAC3B;AACF;AAGA,IAAI,kBAAkB,CAAC;AAAA,EACrB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,WAAW;AAAA,EACX,UAAU;AACZ,MAAM;AACJ,QAAM,EAAE,aAAa,qBAAqB,MAAM,IAAI,mBAAmB;AACvE,QAAM,qBAAqB,mBAAmB;AAC9C,QAAM,EAAE,MAAM,YAAY,IAAI,SAAS;AAAA,IACrC;AAAA,IACA,SAAS,YAAY;AACnB,aAAO,QAAQ;AAAA,QACb,CAAC,kBAAkB,GAAG;AAAA,QACtB,OAAO,MAAM,QAAQ,YAAY,IAAI,aAAa,SAAS;AAAA,MAC7D,CAAC;AAAA,IACH;AAAA,IACA,SAAS,CAAC,CAAC,gBAAgB;AAAA,EAC7B,CAAC;AACD,QAAM,EAAE,MAAM,GAAG,KAAK,IAAI,iBAAiB;AAAA,IACzC,UAAU,CAAC,GAAG,UAAU,KAAK;AAAA,IAC7B,SAAS,OAAO,EAAE,YAAY,EAAE,MAAM;AACpC,aAAO,MAAM,QAAQ;AAAA,QACnB,GAAG;AAAA,QACH,OAAO;AAAA,QACP,QAAQ;AAAA,MACV,CAAC;AAAA,IACH;AAAA,IACA,kBAAkB;AAAA,IAClB,kBAAkB,CAAC,aAAa;AAC9B,YAAM,iBAAiB,SAAS,QAAQ,SAAS,SAAS,SAAS;AACnE,aAAO,iBAAiB,SAAS,SAAS,SAAS,QAAQ;AAAA,IAC7D;AAAA,IACA,iBAAiB;AAAA,IACjB;AAAA,EACF,CAAC;AACD,QAAM,WAAU,6BAAM,MAAM,QAAQ,CAAC,SAAS,WAAW,IAAI,OAAM,CAAC;AACpE,QAAM,iBAAiB,cAAc,WAAW,WAAW,IAAI,CAAC;AAChE,QAAM,WAAW,CAAC,KAAK,aAAa,CAAC,QAAQ,UAAU,CAAC,eAAe,CAAC;AACxE,MAAI,gBAAgB,eAAe,UAAU,CAAC,aAAa;AACzD,mBAAe,QAAQ,CAAC,WAAW;AACjC,UAAI,CAAC,QAAQ,KAAK,CAAC,MAAM,EAAE,UAAU,OAAO,KAAK,GAAG;AAClD,gBAAQ,QAAQ,MAAM;AAAA,MACxB;AAAA,IACF,CAAC;AAAA,EACH;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACL;AACF;", "names": ["debounce"]}