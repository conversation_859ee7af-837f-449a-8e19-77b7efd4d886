{"version": 3, "sources": ["../../@medusajs/dashboard/dist/reservation-detail-27UGTUXR.mjs"], "sourcesContent": ["import {\n  InventoryItemGeneralSection\n} from \"./chunk-Q5DI5VYN.mjs\";\nimport \"./chunk-YOYOJU5D.mjs\";\nimport {\n  SectionRow\n} from \"./chunk-LFLGEXIG.mjs\";\nimport {\n  TwoColumnPage\n} from \"./chunk-2RQLKDBF.mjs\";\nimport {\n  useExtension\n} from \"./chunk-C5P5PL3E.mjs\";\nimport {\n  TwoColumnPageSkeleton\n} from \"./chunk-LPEUYMRK.mjs\";\nimport {\n  ActionMenu\n} from \"./chunk-3NJTXRIY.mjs\";\nimport \"./chunk-OC7BQLYI.mjs\";\nimport \"./chunk-Z5UDPQIH.mjs\";\nimport \"./chunk-KOSCMAIC.mjs\";\nimport \"./chunk-X6DSNTTX.mjs\";\nimport \"./chunk-I6E6CALJ.mjs\";\nimport \"./chunk-B4GODIOW.mjs\";\nimport \"./chunk-F6IJV2I2.mjs\";\nimport \"./chunk-QTCZFYFH.mjs\";\nimport \"./chunk-ENV6YVOM.mjs\";\nimport \"./chunk-PIR2H25N.mjs\";\nimport \"./chunk-RLY2SL5E.mjs\";\nimport \"./chunk-C5LYZZZ5.mjs\";\nimport \"./chunk-2ZKVRTBW.mjs\";\nimport \"./chunk-FO3VP56P.mjs\";\nimport \"./chunk-YS65UGPC.mjs\";\nimport \"./chunk-F6PXCY3N.mjs\";\nimport \"./chunk-3OHH43G6.mjs\";\nimport \"./chunk-G2H6MAK7.mjs\";\nimport \"./chunk-GRT22PE5.mjs\";\nimport {\n  useStockLocation\n} from \"./chunk-32IQRUVY.mjs\";\nimport \"./chunk-FNYASI54.mjs\";\nimport {\n  reservationItemsQueryKeys,\n  useReservationItem\n} from \"./chunk-FVC7M755.mjs\";\nimport \"./chunk-ZJ3OFMHB.mjs\";\nimport \"./chunk-PNU5HPGY.mjs\";\nimport \"./chunk-V2LANK5S.mjs\";\nimport \"./chunk-QZ6PT4QV.mjs\";\nimport \"./chunk-QL4XKIVL.mjs\";\nimport {\n  useInventoryItem\n} from \"./chunk-6I62UDJA.mjs\";\nimport {\n  queryClient\n} from \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport {\n  sdk\n} from \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-RPUOO7AV.mjs\";\n\n// src/routes/reservations/reservation-detail/breadcrumb.tsx\nimport { jsx } from \"react/jsx-runtime\";\nvar ReservationDetailBreadcrumb = (props) => {\n  const { id } = props.params || {};\n  const { reservation } = useReservationItem(id, void 0, {\n    initialData: props.data,\n    enabled: Boolean(id)\n  });\n  if (!reservation) {\n    return null;\n  }\n  const display = reservation?.inventory_item?.title ?? reservation?.inventory_item?.sku ?? reservation.id;\n  return /* @__PURE__ */ jsx(\"span\", { children: display });\n};\n\n// src/routes/reservations/reservation-detail/loader.ts\nvar reservationDetailQuery = (id) => ({\n  queryKey: reservationItemsQueryKeys.detail(id),\n  queryFn: async () => sdk.admin.reservation.retrieve(id)\n});\nvar reservationItemLoader = async ({ params }) => {\n  const id = params.id;\n  const query = reservationDetailQuery(id);\n  return queryClient.ensureQueryData(query);\n};\n\n// src/routes/reservations/reservation-detail/reservation-detail.tsx\nimport { useLoaderData, useParams } from \"react-router-dom\";\n\n// src/routes/reservations/reservation-detail/components/reservation-general-section/reservation-general-section.tsx\nimport { Container, Heading } from \"@medusajs/ui\";\nimport { PencilSquare } from \"@medusajs/icons\";\nimport { useTranslation } from \"react-i18next\";\nimport { jsx as jsx2, jsxs } from \"react/jsx-runtime\";\nvar ReservationGeneralSection = ({\n  reservation\n}) => {\n  const { t } = useTranslation();\n  const { inventory_item: inventoryItem, isPending: isLoadingInventoryItem } = useInventoryItem(reservation.inventory_item_id);\n  const { stock_location: location, isPending: isLoadingLocation } = useStockLocation(reservation.location_id);\n  if (isLoadingInventoryItem || !inventoryItem || isLoadingLocation || !location) {\n    return /* @__PURE__ */ jsx2(\"div\", { children: \"Loading...\" });\n  }\n  const locationLevel = inventoryItem.location_levels.find(\n    (l) => l.location_id === reservation.location_id\n  );\n  return /* @__PURE__ */ jsxs(Container, { className: \"divide-y p-0\", children: [\n    /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center justify-between px-6 py-4\", children: [\n      /* @__PURE__ */ jsx2(Heading, { children: t(\"inventory.reservation.header\", {\n        itemName: inventoryItem.title ?? inventoryItem.sku\n      }) }),\n      /* @__PURE__ */ jsx2(\n        ActionMenu,\n        {\n          groups: [\n            {\n              actions: [\n                {\n                  icon: /* @__PURE__ */ jsx2(PencilSquare, {}),\n                  label: t(\"actions.edit\"),\n                  to: `edit`\n                }\n              ]\n            }\n          ]\n        }\n      )\n    ] }),\n    /* @__PURE__ */ jsx2(\n      SectionRow,\n      {\n        title: t(\"inventory.reservation.lineItemId\"),\n        value: reservation.line_item_id\n      }\n    ),\n    /* @__PURE__ */ jsx2(\n      SectionRow,\n      {\n        title: t(\"inventory.reservation.description\"),\n        value: reservation.description\n      }\n    ),\n    /* @__PURE__ */ jsx2(\n      SectionRow,\n      {\n        title: t(\"inventory.reservation.location\"),\n        value: location?.name\n      }\n    ),\n    /* @__PURE__ */ jsx2(\n      SectionRow,\n      {\n        title: t(\"inventory.reservation.inStockAtLocation\"),\n        value: locationLevel?.stocked_quantity\n      }\n    ),\n    /* @__PURE__ */ jsx2(\n      SectionRow,\n      {\n        title: t(\"inventory.reservation.availableAtLocation\"),\n        value: locationLevel?.available_quantity\n      }\n    ),\n    /* @__PURE__ */ jsx2(\n      SectionRow,\n      {\n        title: t(\"inventory.reservation.reservedAtLocation\"),\n        value: locationLevel?.reserved_quantity\n      }\n    )\n  ] });\n};\n\n// src/routes/reservations/reservation-detail/reservation-detail.tsx\nimport { jsx as jsx3, jsxs as jsxs2 } from \"react/jsx-runtime\";\nvar ReservationDetail = () => {\n  const { id } = useParams();\n  const initialData = useLoaderData();\n  const { reservation, isLoading, isError, error } = useReservationItem(\n    id,\n    void 0,\n    {\n      initialData\n    }\n  );\n  const { inventory_item } = useInventoryItem(\n    reservation?.inventory_item?.id,\n    void 0,\n    { enabled: !!reservation?.inventory_item?.id }\n  );\n  const { getWidgets } = useExtension();\n  if (isLoading || !reservation) {\n    return /* @__PURE__ */ jsx3(\n      TwoColumnPageSkeleton,\n      {\n        mainSections: 1,\n        sidebarSections: 1,\n        showJSON: true,\n        showMetadata: true\n      }\n    );\n  }\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsxs2(\n    TwoColumnPage,\n    {\n      widgets: {\n        before: getWidgets(\"reservation.details.before\"),\n        after: getWidgets(\"reservation.details.after\"),\n        sideBefore: getWidgets(\"reservation.details.side.before\"),\n        sideAfter: getWidgets(\"reservation.details.side.after\")\n      },\n      data: reservation,\n      showJSON: true,\n      showMetadata: true,\n      children: [\n        /* @__PURE__ */ jsx3(TwoColumnPage.Main, { children: /* @__PURE__ */ jsx3(ReservationGeneralSection, { reservation }) }),\n        /* @__PURE__ */ jsx3(TwoColumnPage.Sidebar, { children: inventory_item && /* @__PURE__ */ jsx3(InventoryItemGeneralSection, { inventoryItem: inventory_item }) })\n      ]\n    }\n  );\n};\nexport {\n  ReservationDetailBreadcrumb as Breadcrumb,\n  ReservationDetail as Component,\n  reservationItemLoader as loader\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgEA,yBAAoB;AAgCpB,IAAAA,sBAAkC;AAiFlC,IAAAA,sBAA2C;AAhH3C,IAAI,8BAA8B,CAAC,UAAU;AAjE7C;AAkEE,QAAM,EAAE,GAAG,IAAI,MAAM,UAAU,CAAC;AAChC,QAAM,EAAE,YAAY,IAAI,mBAAmB,IAAI,QAAQ;AAAA,IACrD,aAAa,MAAM;AAAA,IACnB,SAAS,QAAQ,EAAE;AAAA,EACrB,CAAC;AACD,MAAI,CAAC,aAAa;AAChB,WAAO;AAAA,EACT;AACA,QAAM,YAAU,gDAAa,mBAAb,mBAA6B,YAAS,gDAAa,mBAAb,mBAA6B,QAAO,YAAY;AACtG,aAAuB,wBAAI,QAAQ,EAAE,UAAU,QAAQ,CAAC;AAC1D;AAGA,IAAI,yBAAyB,CAAC,QAAQ;AAAA,EACpC,UAAU,0BAA0B,OAAO,EAAE;AAAA,EAC7C,SAAS,YAAY,IAAI,MAAM,YAAY,SAAS,EAAE;AACxD;AACA,IAAI,wBAAwB,OAAO,EAAE,OAAO,MAAM;AAChD,QAAM,KAAK,OAAO;AAClB,QAAM,QAAQ,uBAAuB,EAAE;AACvC,SAAO,YAAY,gBAAgB,KAAK;AAC1C;AAUA,IAAI,4BAA4B,CAAC;AAAA,EAC/B;AACF,MAAM;AACJ,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,QAAM,EAAE,gBAAgB,eAAe,WAAW,uBAAuB,IAAI,iBAAiB,YAAY,iBAAiB;AAC3H,QAAM,EAAE,gBAAgB,UAAU,WAAW,kBAAkB,IAAI,iBAAiB,YAAY,WAAW;AAC3G,MAAI,0BAA0B,CAAC,iBAAiB,qBAAqB,CAAC,UAAU;AAC9E,eAAuB,oBAAAC,KAAK,OAAO,EAAE,UAAU,aAAa,CAAC;AAAA,EAC/D;AACA,QAAM,gBAAgB,cAAc,gBAAgB;AAAA,IAClD,CAAC,MAAM,EAAE,gBAAgB,YAAY;AAAA,EACvC;AACA,aAAuB,0BAAK,WAAW,EAAE,WAAW,gBAAgB,UAAU;AAAA,QAC5D,0BAAK,OAAO,EAAE,WAAW,+CAA+C,UAAU;AAAA,UAChF,oBAAAA,KAAK,SAAS,EAAE,UAAU,EAAE,gCAAgC;AAAA,QAC1E,UAAU,cAAc,SAAS,cAAc;AAAA,MACjD,CAAC,EAAE,CAAC;AAAA,UACY,oBAAAA;AAAA,QACd;AAAA,QACA;AAAA,UACE,QAAQ;AAAA,YACN;AAAA,cACE,SAAS;AAAA,gBACP;AAAA,kBACE,UAAsB,oBAAAA,KAAK,cAAc,CAAC,CAAC;AAAA,kBAC3C,OAAO,EAAE,cAAc;AAAA,kBACvB,IAAI;AAAA,gBACN;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF,EAAE,CAAC;AAAA,QACa,oBAAAA;AAAA,MACd;AAAA,MACA;AAAA,QACE,OAAO,EAAE,kCAAkC;AAAA,QAC3C,OAAO,YAAY;AAAA,MACrB;AAAA,IACF;AAAA,QACgB,oBAAAA;AAAA,MACd;AAAA,MACA;AAAA,QACE,OAAO,EAAE,mCAAmC;AAAA,QAC5C,OAAO,YAAY;AAAA,MACrB;AAAA,IACF;AAAA,QACgB,oBAAAA;AAAA,MACd;AAAA,MACA;AAAA,QACE,OAAO,EAAE,gCAAgC;AAAA,QACzC,OAAO,qCAAU;AAAA,MACnB;AAAA,IACF;AAAA,QACgB,oBAAAA;AAAA,MACd;AAAA,MACA;AAAA,QACE,OAAO,EAAE,yCAAyC;AAAA,QAClD,OAAO,+CAAe;AAAA,MACxB;AAAA,IACF;AAAA,QACgB,oBAAAA;AAAA,MACd;AAAA,MACA;AAAA,QACE,OAAO,EAAE,2CAA2C;AAAA,QACpD,OAAO,+CAAe;AAAA,MACxB;AAAA,IACF;AAAA,QACgB,oBAAAA;AAAA,MACd;AAAA,MACA;AAAA,QACE,OAAO,EAAE,0CAA0C;AAAA,QACnD,OAAO,+CAAe;AAAA,MACxB;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;AAIA,IAAI,oBAAoB,MAAM;AAlL9B;AAmLE,QAAM,EAAE,GAAG,IAAI,UAAU;AACzB,QAAM,cAAc,cAAc;AAClC,QAAM,EAAE,aAAa,WAAW,SAAS,MAAM,IAAI;AAAA,IACjD;AAAA,IACA;AAAA,IACA;AAAA,MACE;AAAA,IACF;AAAA,EACF;AACA,QAAM,EAAE,eAAe,IAAI;AAAA,KACzB,gDAAa,mBAAb,mBAA6B;AAAA,IAC7B;AAAA,IACA,EAAE,SAAS,CAAC,GAAC,gDAAa,mBAAb,mBAA6B,IAAG;AAAA,EAC/C;AACA,QAAM,EAAE,WAAW,IAAI,aAAa;AACpC,MAAI,aAAa,CAAC,aAAa;AAC7B,eAAuB,oBAAAC;AAAA,MACrB;AAAA,MACA;AAAA,QACE,cAAc;AAAA,QACd,iBAAiB;AAAA,QACjB,UAAU;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,EACF;AACA,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,oBAAAC;AAAA,IACrB;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP,QAAQ,WAAW,4BAA4B;AAAA,QAC/C,OAAO,WAAW,2BAA2B;AAAA,QAC7C,YAAY,WAAW,iCAAiC;AAAA,QACxD,WAAW,WAAW,gCAAgC;AAAA,MACxD;AAAA,MACA,MAAM;AAAA,MACN,UAAU;AAAA,MACV,cAAc;AAAA,MACd,UAAU;AAAA,YACQ,oBAAAD,KAAK,cAAc,MAAM,EAAE,cAA0B,oBAAAA,KAAK,2BAA2B,EAAE,YAAY,CAAC,EAAE,CAAC;AAAA,YACvG,oBAAAA,KAAK,cAAc,SAAS,EAAE,UAAU,sBAAkC,oBAAAA,KAAK,6BAA6B,EAAE,eAAe,eAAe,CAAC,EAAE,CAAC;AAAA,MAClK;AAAA,IACF;AAAA,EACF;AACF;", "names": ["import_jsx_runtime", "jsx2", "jsx3", "jsxs2"]}