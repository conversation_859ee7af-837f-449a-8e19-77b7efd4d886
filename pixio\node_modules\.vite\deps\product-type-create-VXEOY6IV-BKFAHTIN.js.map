{"version": 3, "sources": ["../../@medusajs/dashboard/dist/product-type-create-VXEOY6IV.mjs"], "sourcesContent": ["import \"./chunk-IUCDCPJU.mjs\";\nimport {\n  KeyboundForm\n} from \"./chunk-6HTZNHPT.mjs\";\nimport {\n  RouteFocusModal,\n  useRouteModal\n} from \"./chunk-4TC5YS65.mjs\";\nimport {\n  Form\n} from \"./chunk-OBQI23QM.mjs\";\nimport {\n  useCreateProductType\n} from \"./chunk-B4GODIOW.mjs\";\nimport \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-RPUOO7AV.mjs\";\n\n// src/routes/product-types/product-type-create/components/create-product-type-form/create-product-type-form.tsx\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { Button, Heading, Input, Text, toast } from \"@medusajs/ui\";\nimport { useForm } from \"react-hook-form\";\nimport { useTranslation } from \"react-i18next\";\nimport { z } from \"zod\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar CreateProductTypeSchema = z.object({\n  value: z.string().min(1)\n});\nvar CreateProductTypeForm = () => {\n  const { t } = useTranslation();\n  const { handleSuccess } = useRouteModal();\n  const form = useForm({\n    defaultValues: {\n      value: \"\"\n    },\n    resolver: zodResolver(CreateProductTypeSchema)\n  });\n  const { mutateAsync, isPending } = useCreateProductType();\n  const handleSubmit = form.handleSubmit(\n    async (values) => {\n      await mutateAsync(values, {\n        onSuccess: ({ product_type }) => {\n          toast.success(\n            t(\"productTypes.create.successToast\", {\n              value: product_type.value.trim()\n            })\n          );\n          handleSuccess(`/settings/product-types/${product_type.id}`);\n        },\n        onError: (e) => {\n          toast.error(e.message);\n        }\n      });\n    }\n  );\n  return /* @__PURE__ */ jsx(RouteFocusModal.Form, { form, children: /* @__PURE__ */ jsxs(KeyboundForm, { onSubmit: handleSubmit, className: \"flex h-full flex-col\", children: [\n    /* @__PURE__ */ jsx(RouteFocusModal.Body, { className: \"flex flex-col items-center overflow-auto p-16\", children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex w-full max-w-[720px] flex-col gap-y-8\", children: [\n      /* @__PURE__ */ jsxs(\"div\", { children: [\n        /* @__PURE__ */ jsx(Heading, { children: t(\"productTypes.create.header\") }),\n        /* @__PURE__ */ jsx(Text, { size: \"small\", className: \"text-ui-fg-subtle\", children: t(\"productTypes.create.hint\") })\n      ] }),\n      /* @__PURE__ */ jsx(\"div\", { className: \"grid grid-cols-1 gap-4 md:grid-cols-2\", children: /* @__PURE__ */ jsx(\n        Form.Field,\n        {\n          control: form.control,\n          name: \"value\",\n          render: ({ field }) => {\n            return /* @__PURE__ */ jsxs(Form.Item, { children: [\n              /* @__PURE__ */ jsx(Form.Label, { children: t(\"productTypes.fields.value\") }),\n              /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(Input, { ...field }) }),\n              /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n            ] });\n          }\n        }\n      ) })\n    ] }) }),\n    /* @__PURE__ */ jsx(RouteFocusModal.Footer, { children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center justify-end gap-x-2\", children: [\n      /* @__PURE__ */ jsx(RouteFocusModal.Close, { asChild: true, children: /* @__PURE__ */ jsx(Button, { size: \"small\", variant: \"secondary\", children: t(\"actions.cancel\") }) }),\n      /* @__PURE__ */ jsx(\n        Button,\n        {\n          size: \"small\",\n          variant: \"primary\",\n          type: \"submit\",\n          isLoading: isPending,\n          children: t(\"actions.create\")\n        }\n      )\n    ] }) })\n  ] }) });\n};\n\n// src/routes/product-types/product-type-create/product-type-create.tsx\nimport { jsx as jsx2 } from \"react/jsx-runtime\";\nvar ProductTypeCreate = () => {\n  return /* @__PURE__ */ jsx2(RouteFocusModal, { children: /* @__PURE__ */ jsx2(CreateProductTypeForm, {}) });\n};\nexport {\n  ProductTypeCreate as Component\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyBA,yBAA0B;AAqE1B,IAAAA,sBAA4B;AApE5B,IAAI,0BAA0B,EAAE,OAAO;AAAA,EACrC,OAAO,EAAE,OAAO,EAAE,IAAI,CAAC;AACzB,CAAC;AACD,IAAI,wBAAwB,MAAM;AAChC,QAAM,EAAE,GAAAC,GAAE,IAAI,eAAe;AAC7B,QAAM,EAAE,cAAc,IAAI,cAAc;AACxC,QAAM,OAAO,QAAQ;AAAA,IACnB,eAAe;AAAA,MACb,OAAO;AAAA,IACT;AAAA,IACA,UAAU,EAAY,uBAAuB;AAAA,EAC/C,CAAC;AACD,QAAM,EAAE,aAAa,UAAU,IAAI,qBAAqB;AACxD,QAAM,eAAe,KAAK;AAAA,IACxB,OAAO,WAAW;AAChB,YAAM,YAAY,QAAQ;AAAA,QACxB,WAAW,CAAC,EAAE,aAAa,MAAM;AAC/B,gBAAM;AAAA,YACJA,GAAE,oCAAoC;AAAA,cACpC,OAAO,aAAa,MAAM,KAAK;AAAA,YACjC,CAAC;AAAA,UACH;AACA,wBAAc,2BAA2B,aAAa,EAAE,EAAE;AAAA,QAC5D;AAAA,QACA,SAAS,CAAC,MAAM;AACd,gBAAM,MAAM,EAAE,OAAO;AAAA,QACvB;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AACA,aAAuB,wBAAI,gBAAgB,MAAM,EAAE,MAAM,cAA0B,yBAAK,cAAc,EAAE,UAAU,cAAc,WAAW,wBAAwB,UAAU;AAAA,QAC3J,wBAAI,gBAAgB,MAAM,EAAE,WAAW,iDAAiD,cAA0B,yBAAK,OAAO,EAAE,WAAW,8CAA8C,UAAU;AAAA,UACjM,yBAAK,OAAO,EAAE,UAAU;AAAA,YACtB,wBAAI,SAAS,EAAE,UAAUA,GAAE,4BAA4B,EAAE,CAAC;AAAA,YAC1D,wBAAI,MAAM,EAAE,MAAM,SAAS,WAAW,qBAAqB,UAAUA,GAAE,0BAA0B,EAAE,CAAC;AAAA,MACtH,EAAE,CAAC;AAAA,UACa,wBAAI,OAAO,EAAE,WAAW,yCAAyC,cAA0B;AAAA,QACzG,KAAK;AAAA,QACL;AAAA,UACE,SAAS,KAAK;AAAA,UACd,MAAM;AAAA,UACN,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,uBAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,kBACjC,wBAAI,KAAK,OAAO,EAAE,UAAUA,GAAE,2BAA2B,EAAE,CAAC;AAAA,kBAC5D,wBAAI,KAAK,SAAS,EAAE,cAA0B,wBAAI,OAAO,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC;AAAA,kBACxE,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,YAC3C,EAAE,CAAC;AAAA,UACL;AAAA,QACF;AAAA,MACF,EAAE,CAAC;AAAA,IACL,EAAE,CAAC,EAAE,CAAC;AAAA,QACU,wBAAI,gBAAgB,QAAQ,EAAE,cAA0B,yBAAK,OAAO,EAAE,WAAW,yCAAyC,UAAU;AAAA,UAClI,wBAAI,gBAAgB,OAAO,EAAE,SAAS,MAAM,cAA0B,wBAAI,QAAQ,EAAE,MAAM,SAAS,SAAS,aAAa,UAAUA,GAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;AAAA,UAC3J;AAAA,QACd;AAAA,QACA;AAAA,UACE,MAAM;AAAA,UACN,SAAS;AAAA,UACT,MAAM;AAAA,UACN,WAAW;AAAA,UACX,UAAUA,GAAE,gBAAgB;AAAA,QAC9B;AAAA,MACF;AAAA,IACF,EAAE,CAAC,EAAE,CAAC;AAAA,EACR,EAAE,CAAC,EAAE,CAAC;AACR;AAIA,IAAI,oBAAoB,MAAM;AAC5B,aAAuB,oBAAAC,KAAK,iBAAiB,EAAE,cAA0B,oBAAAA,KAAK,uBAAuB,CAAC,CAAC,EAAE,CAAC;AAC5G;", "names": ["import_jsx_runtime", "t", "jsx2"]}