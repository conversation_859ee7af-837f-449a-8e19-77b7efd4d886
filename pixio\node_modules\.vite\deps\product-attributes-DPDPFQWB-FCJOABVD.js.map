{"version": 3, "sources": ["../../@medusajs/dashboard/dist/product-attributes-DPDPFQWB.mjs"], "sourcesContent": ["import {\n  CountrySelect\n} from \"./chunk-SCBXRJPV.mjs\";\nimport \"./chunk-IUCDCPJU.mjs\";\nimport {\n  KeyboundForm\n} from \"./chunk-6HTZNHPT.mjs\";\nimport {\n  RouteDrawer,\n  useRouteModal\n} from \"./chunk-4TC5YS65.mjs\";\nimport {\n  PRODUCT_DETAIL_FIELDS\n} from \"./chunk-KAGQQERZ.mjs\";\nimport {\n  FormExtensionZone,\n  useExtendableForm\n} from \"./chunk-I7Q3WDNZ.mjs\";\nimport \"./chunk-NQIC7ZFS.mjs\";\nimport \"./chunk-ONB3JEHR.mjs\";\nimport \"./chunk-4GQOUCX6.mjs\";\nimport \"./chunk-2VTICXJR.mjs\";\nimport \"./chunk-D3YQN7HV.mjs\";\nimport \"./chunk-DG7J63J2.mjs\";\nimport \"./chunk-6BUUHFWE.mjs\";\nimport \"./chunk-MNXC6Q4F.mjs\";\nimport {\n  useExtension\n} from \"./chunk-C5P5PL3E.mjs\";\nimport \"./chunk-LPEUYMRK.mjs\";\nimport \"./chunk-XKXNQ2KV.mjs\";\nimport \"./chunk-3NJTXRIY.mjs\";\nimport \"./chunk-OC7BQLYI.mjs\";\nimport \"./chunk-NYXYHZE6.mjs\";\nimport {\n  Form\n} from \"./chunk-OBQI23QM.mjs\";\nimport \"./chunk-Z5UDPQIH.mjs\";\nimport \"./chunk-KOSCMAIC.mjs\";\nimport \"./chunk-X6DSNTTX.mjs\";\nimport \"./chunk-I6E6CALJ.mjs\";\nimport \"./chunk-B4GODIOW.mjs\";\nimport \"./chunk-F6IJV2I2.mjs\";\nimport \"./chunk-QTCZFYFH.mjs\";\nimport \"./chunk-ENV6YVOM.mjs\";\nimport \"./chunk-PIR2H25N.mjs\";\nimport \"./chunk-RLY2SL5E.mjs\";\nimport \"./chunk-C5LYZZZ5.mjs\";\nimport \"./chunk-2ZKVRTBW.mjs\";\nimport \"./chunk-FO3VP56P.mjs\";\nimport \"./chunk-YS65UGPC.mjs\";\nimport \"./chunk-F6PXCY3N.mjs\";\nimport \"./chunk-3OHH43G6.mjs\";\nimport \"./chunk-G2H6MAK7.mjs\";\nimport \"./chunk-GRT22PE5.mjs\";\nimport \"./chunk-32IQRUVY.mjs\";\nimport \"./chunk-FNYASI54.mjs\";\nimport \"./chunk-FVC7M755.mjs\";\nimport \"./chunk-ZJ3OFMHB.mjs\";\nimport \"./chunk-PNU5HPGY.mjs\";\nimport \"./chunk-V2LANK5S.mjs\";\nimport \"./chunk-QZ6PT4QV.mjs\";\nimport \"./chunk-QL4XKIVL.mjs\";\nimport {\n  useProduct,\n  useUpdateProduct\n} from \"./chunk-6I62UDJA.mjs\";\nimport \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-RPUOO7AV.mjs\";\n\n// src/routes/products/product-attributes/product-attributes.tsx\nimport { Heading } from \"@medusajs/ui\";\nimport { useTranslation as useTranslation2 } from \"react-i18next\";\nimport { useParams } from \"react-router-dom\";\n\n// src/routes/products/product-attributes/components/product-attributes-form/product-attributes-form.tsx\nimport { Button, Input } from \"@medusajs/ui\";\nimport { useTranslation } from \"react-i18next\";\nimport * as zod from \"zod\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar dimension = zod.union([zod.string(), zod.number()]).transform((value) => {\n  if (value === \"\") {\n    return null;\n  }\n  return Number(value);\n}).optional().nullable();\nvar ProductAttributesSchema = zod.object({\n  weight: dimension,\n  length: dimension,\n  width: dimension,\n  height: dimension,\n  mid_code: zod.string().optional(),\n  hs_code: zod.string().optional(),\n  origin_country: zod.string().optional()\n});\nvar ProductAttributesForm = ({\n  product\n}) => {\n  const { t } = useTranslation();\n  const { handleSuccess } = useRouteModal();\n  const { getFormConfigs, getFormFields } = useExtension();\n  const configs = getFormConfigs(\"product\", \"attributes\");\n  const fields = getFormFields(\"product\", \"attributes\");\n  const form = useExtendableForm({\n    defaultValues: {\n      height: product.height ? product.height : null,\n      width: product.width ? product.width : null,\n      length: product.length ? product.length : null,\n      weight: product.weight ? product.weight : null,\n      mid_code: product.mid_code || \"\",\n      hs_code: product.hs_code || \"\",\n      origin_country: product.origin_country || \"\"\n    },\n    schema: ProductAttributesSchema,\n    configs,\n    data: product\n  });\n  const { mutateAsync, isPending } = useUpdateProduct(product.id);\n  const handleSubmit = form.handleSubmit(async (data) => {\n    await mutateAsync(\n      {\n        weight: data.weight ? data.weight : void 0,\n        length: data.length ? data.length : void 0,\n        width: data.width ? data.width : void 0,\n        height: data.height ? data.height : void 0,\n        mid_code: data.mid_code,\n        hs_code: data.hs_code,\n        origin_country: data.origin_country\n      },\n      {\n        onSuccess: () => {\n          handleSuccess();\n        }\n      }\n    );\n  });\n  return /* @__PURE__ */ jsx(RouteDrawer.Form, { form, children: /* @__PURE__ */ jsxs(KeyboundForm, { onSubmit: handleSubmit, className: \"flex h-full flex-col\", children: [\n    /* @__PURE__ */ jsx(RouteDrawer.Body, { children: /* @__PURE__ */ jsx(\"div\", { className: \"flex h-full flex-col gap-y-8\", children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex flex-col gap-y-4\", children: [\n      /* @__PURE__ */ jsx(\n        Form.Field,\n        {\n          control: form.control,\n          name: \"width\",\n          render: ({ field: { onChange, value, ...field } }) => {\n            return /* @__PURE__ */ jsxs(Form.Item, { children: [\n              /* @__PURE__ */ jsx(Form.Label, { children: t(\"fields.width\") }),\n              /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(\n                Input,\n                {\n                  type: \"number\",\n                  min: 0,\n                  value: value || \"\",\n                  onChange: (e) => {\n                    const value2 = e.target.value;\n                    if (value2 === \"\") {\n                      onChange(null);\n                    } else {\n                      onChange(parseFloat(value2));\n                    }\n                  },\n                  ...field\n                }\n              ) }),\n              /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n            ] });\n          }\n        }\n      ),\n      /* @__PURE__ */ jsx(\n        Form.Field,\n        {\n          control: form.control,\n          name: \"height\",\n          render: ({ field: { onChange, value, ...field } }) => {\n            return /* @__PURE__ */ jsxs(Form.Item, { children: [\n              /* @__PURE__ */ jsx(Form.Label, { children: t(\"fields.height\") }),\n              /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(\n                Input,\n                {\n                  type: \"number\",\n                  min: 0,\n                  value: value || \"\",\n                  onChange: (e) => {\n                    const value2 = e.target.value;\n                    if (value2 === \"\") {\n                      onChange(null);\n                    } else {\n                      onChange(Number(value2));\n                    }\n                  },\n                  ...field\n                }\n              ) }),\n              /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n            ] });\n          }\n        }\n      ),\n      /* @__PURE__ */ jsx(\n        Form.Field,\n        {\n          control: form.control,\n          name: \"length\",\n          render: ({ field: { onChange, value, ...field } }) => {\n            return /* @__PURE__ */ jsxs(Form.Item, { children: [\n              /* @__PURE__ */ jsx(Form.Label, { children: t(\"fields.length\") }),\n              /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(\n                Input,\n                {\n                  type: \"number\",\n                  min: 0,\n                  value: value || \"\",\n                  onChange: (e) => {\n                    const value2 = e.target.value;\n                    if (value2 === \"\") {\n                      onChange(null);\n                    } else {\n                      onChange(Number(value2));\n                    }\n                  },\n                  ...field\n                }\n              ) }),\n              /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n            ] });\n          }\n        }\n      ),\n      /* @__PURE__ */ jsx(\n        Form.Field,\n        {\n          control: form.control,\n          name: \"weight\",\n          render: ({ field: { onChange, value, ...field } }) => {\n            return /* @__PURE__ */ jsxs(Form.Item, { children: [\n              /* @__PURE__ */ jsx(Form.Label, { children: t(\"fields.weight\") }),\n              /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(\n                Input,\n                {\n                  type: \"number\",\n                  min: 0,\n                  value: value || \"\",\n                  onChange: (e) => {\n                    const value2 = e.target.value;\n                    if (value2 === \"\") {\n                      onChange(null);\n                    } else {\n                      onChange(Number(value2));\n                    }\n                  },\n                  ...field\n                }\n              ) }),\n              /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n            ] });\n          }\n        }\n      ),\n      /* @__PURE__ */ jsx(\n        Form.Field,\n        {\n          control: form.control,\n          name: \"mid_code\",\n          render: ({ field }) => {\n            return /* @__PURE__ */ jsxs(Form.Item, { children: [\n              /* @__PURE__ */ jsx(Form.Label, { children: t(\"fields.midCode\") }),\n              /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(Input, { ...field }) }),\n              /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n            ] });\n          }\n        }\n      ),\n      /* @__PURE__ */ jsx(\n        Form.Field,\n        {\n          control: form.control,\n          name: \"hs_code\",\n          render: ({ field }) => {\n            return /* @__PURE__ */ jsxs(Form.Item, { children: [\n              /* @__PURE__ */ jsx(Form.Label, { children: t(\"fields.hsCode\") }),\n              /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(Input, { ...field }) }),\n              /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n            ] });\n          }\n        }\n      ),\n      /* @__PURE__ */ jsx(\n        Form.Field,\n        {\n          control: form.control,\n          name: \"origin_country\",\n          render: ({ field }) => {\n            return /* @__PURE__ */ jsxs(Form.Item, { children: [\n              /* @__PURE__ */ jsx(Form.Label, { children: t(\"fields.countryOfOrigin\") }),\n              /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(CountrySelect, { ...field }) }),\n              /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n            ] });\n          }\n        }\n      ),\n      /* @__PURE__ */ jsx(FormExtensionZone, { fields, form })\n    ] }) }) }),\n    /* @__PURE__ */ jsx(RouteDrawer.Footer, { children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center justify-end gap-x-2\", children: [\n      /* @__PURE__ */ jsx(RouteDrawer.Close, { asChild: true, children: /* @__PURE__ */ jsx(Button, { size: \"small\", variant: \"secondary\", children: t(\"actions.cancel\") }) }),\n      /* @__PURE__ */ jsx(Button, { size: \"small\", type: \"submit\", isLoading: isPending, children: t(\"actions.save\") })\n    ] }) })\n  ] }) });\n};\n\n// src/routes/products/product-attributes/product-attributes.tsx\nimport { jsx as jsx2, jsxs as jsxs2 } from \"react/jsx-runtime\";\nvar ProductAttributes = () => {\n  const { id } = useParams();\n  const { t } = useTranslation2();\n  const { product, isLoading, isError, error } = useProduct(id, {\n    fields: PRODUCT_DETAIL_FIELDS\n  });\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsxs2(RouteDrawer, { children: [\n    /* @__PURE__ */ jsx2(RouteDrawer.Header, { children: /* @__PURE__ */ jsx2(RouteDrawer.Title, { asChild: true, children: /* @__PURE__ */ jsx2(Heading, { children: t(\"products.editAttributes\") }) }) }),\n    !isLoading && product && /* @__PURE__ */ jsx2(ProductAttributesForm, { product })\n  ] });\n};\nexport {\n  ProductAttributes as Component\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiFA,yBAA0B;AAuO1B,IAAAA,sBAA2C;AAtO3C,IAAI,YAAgB,UAAM,CAAK,WAAO,GAAO,WAAO,CAAC,CAAC,EAAE,UAAU,CAAC,UAAU;AAC3E,MAAI,UAAU,IAAI;AAChB,WAAO;AAAA,EACT;AACA,SAAO,OAAO,KAAK;AACrB,CAAC,EAAE,SAAS,EAAE,SAAS;AACvB,IAAI,0BAA8B,WAAO;AAAA,EACvC,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,UAAc,WAAO,EAAE,SAAS;AAAA,EAChC,SAAa,WAAO,EAAE,SAAS;AAAA,EAC/B,gBAAoB,WAAO,EAAE,SAAS;AACxC,CAAC;AACD,IAAI,wBAAwB,CAAC;AAAA,EAC3B;AACF,MAAM;AACJ,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,QAAM,EAAE,cAAc,IAAI,cAAc;AACxC,QAAM,EAAE,gBAAgB,cAAc,IAAI,aAAa;AACvD,QAAM,UAAU,eAAe,WAAW,YAAY;AACtD,QAAM,SAAS,cAAc,WAAW,YAAY;AACpD,QAAM,OAAO,kBAAkB;AAAA,IAC7B,eAAe;AAAA,MACb,QAAQ,QAAQ,SAAS,QAAQ,SAAS;AAAA,MAC1C,OAAO,QAAQ,QAAQ,QAAQ,QAAQ;AAAA,MACvC,QAAQ,QAAQ,SAAS,QAAQ,SAAS;AAAA,MAC1C,QAAQ,QAAQ,SAAS,QAAQ,SAAS;AAAA,MAC1C,UAAU,QAAQ,YAAY;AAAA,MAC9B,SAAS,QAAQ,WAAW;AAAA,MAC5B,gBAAgB,QAAQ,kBAAkB;AAAA,IAC5C;AAAA,IACA,QAAQ;AAAA,IACR;AAAA,IACA,MAAM;AAAA,EACR,CAAC;AACD,QAAM,EAAE,aAAa,UAAU,IAAI,iBAAiB,QAAQ,EAAE;AAC9D,QAAM,eAAe,KAAK,aAAa,OAAO,SAAS;AACrD,UAAM;AAAA,MACJ;AAAA,QACE,QAAQ,KAAK,SAAS,KAAK,SAAS;AAAA,QACpC,QAAQ,KAAK,SAAS,KAAK,SAAS;AAAA,QACpC,OAAO,KAAK,QAAQ,KAAK,QAAQ;AAAA,QACjC,QAAQ,KAAK,SAAS,KAAK,SAAS;AAAA,QACpC,UAAU,KAAK;AAAA,QACf,SAAS,KAAK;AAAA,QACd,gBAAgB,KAAK;AAAA,MACvB;AAAA,MACA;AAAA,QACE,WAAW,MAAM;AACf,wBAAc;AAAA,QAChB;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AACD,aAAuB,wBAAI,YAAY,MAAM,EAAE,MAAM,cAA0B,yBAAK,cAAc,EAAE,UAAU,cAAc,WAAW,wBAAwB,UAAU;AAAA,QACvJ,wBAAI,YAAY,MAAM,EAAE,cAA0B,wBAAI,OAAO,EAAE,WAAW,gCAAgC,cAA0B,yBAAK,OAAO,EAAE,WAAW,yBAAyB,UAAU;AAAA,UAC9L;AAAA,QACd,KAAK;AAAA,QACL;AAAA,UACE,SAAS,KAAK;AAAA,UACd,MAAM;AAAA,UACN,QAAQ,CAAC,EAAE,OAAO,EAAE,UAAU,OAAO,GAAG,MAAM,EAAE,MAAM;AACpD,uBAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,kBACjC,wBAAI,KAAK,OAAO,EAAE,UAAU,EAAE,cAAc,EAAE,CAAC;AAAA,kBAC/C,wBAAI,KAAK,SAAS,EAAE,cAA0B;AAAA,gBAC5D;AAAA,gBACA;AAAA,kBACE,MAAM;AAAA,kBACN,KAAK;AAAA,kBACL,OAAO,SAAS;AAAA,kBAChB,UAAU,CAAC,MAAM;AACf,0BAAM,SAAS,EAAE,OAAO;AACxB,wBAAI,WAAW,IAAI;AACjB,+BAAS,IAAI;AAAA,oBACf,OAAO;AACL,+BAAS,WAAW,MAAM,CAAC;AAAA,oBAC7B;AAAA,kBACF;AAAA,kBACA,GAAG;AAAA,gBACL;AAAA,cACF,EAAE,CAAC;AAAA,kBACa,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,YAC3C,EAAE,CAAC;AAAA,UACL;AAAA,QACF;AAAA,MACF;AAAA,UACgB;AAAA,QACd,KAAK;AAAA,QACL;AAAA,UACE,SAAS,KAAK;AAAA,UACd,MAAM;AAAA,UACN,QAAQ,CAAC,EAAE,OAAO,EAAE,UAAU,OAAO,GAAG,MAAM,EAAE,MAAM;AACpD,uBAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,kBACjC,wBAAI,KAAK,OAAO,EAAE,UAAU,EAAE,eAAe,EAAE,CAAC;AAAA,kBAChD,wBAAI,KAAK,SAAS,EAAE,cAA0B;AAAA,gBAC5D;AAAA,gBACA;AAAA,kBACE,MAAM;AAAA,kBACN,KAAK;AAAA,kBACL,OAAO,SAAS;AAAA,kBAChB,UAAU,CAAC,MAAM;AACf,0BAAM,SAAS,EAAE,OAAO;AACxB,wBAAI,WAAW,IAAI;AACjB,+BAAS,IAAI;AAAA,oBACf,OAAO;AACL,+BAAS,OAAO,MAAM,CAAC;AAAA,oBACzB;AAAA,kBACF;AAAA,kBACA,GAAG;AAAA,gBACL;AAAA,cACF,EAAE,CAAC;AAAA,kBACa,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,YAC3C,EAAE,CAAC;AAAA,UACL;AAAA,QACF;AAAA,MACF;AAAA,UACgB;AAAA,QACd,KAAK;AAAA,QACL;AAAA,UACE,SAAS,KAAK;AAAA,UACd,MAAM;AAAA,UACN,QAAQ,CAAC,EAAE,OAAO,EAAE,UAAU,OAAO,GAAG,MAAM,EAAE,MAAM;AACpD,uBAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,kBACjC,wBAAI,KAAK,OAAO,EAAE,UAAU,EAAE,eAAe,EAAE,CAAC;AAAA,kBAChD,wBAAI,KAAK,SAAS,EAAE,cAA0B;AAAA,gBAC5D;AAAA,gBACA;AAAA,kBACE,MAAM;AAAA,kBACN,KAAK;AAAA,kBACL,OAAO,SAAS;AAAA,kBAChB,UAAU,CAAC,MAAM;AACf,0BAAM,SAAS,EAAE,OAAO;AACxB,wBAAI,WAAW,IAAI;AACjB,+BAAS,IAAI;AAAA,oBACf,OAAO;AACL,+BAAS,OAAO,MAAM,CAAC;AAAA,oBACzB;AAAA,kBACF;AAAA,kBACA,GAAG;AAAA,gBACL;AAAA,cACF,EAAE,CAAC;AAAA,kBACa,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,YAC3C,EAAE,CAAC;AAAA,UACL;AAAA,QACF;AAAA,MACF;AAAA,UACgB;AAAA,QACd,KAAK;AAAA,QACL;AAAA,UACE,SAAS,KAAK;AAAA,UACd,MAAM;AAAA,UACN,QAAQ,CAAC,EAAE,OAAO,EAAE,UAAU,OAAO,GAAG,MAAM,EAAE,MAAM;AACpD,uBAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,kBACjC,wBAAI,KAAK,OAAO,EAAE,UAAU,EAAE,eAAe,EAAE,CAAC;AAAA,kBAChD,wBAAI,KAAK,SAAS,EAAE,cAA0B;AAAA,gBAC5D;AAAA,gBACA;AAAA,kBACE,MAAM;AAAA,kBACN,KAAK;AAAA,kBACL,OAAO,SAAS;AAAA,kBAChB,UAAU,CAAC,MAAM;AACf,0BAAM,SAAS,EAAE,OAAO;AACxB,wBAAI,WAAW,IAAI;AACjB,+BAAS,IAAI;AAAA,oBACf,OAAO;AACL,+BAAS,OAAO,MAAM,CAAC;AAAA,oBACzB;AAAA,kBACF;AAAA,kBACA,GAAG;AAAA,gBACL;AAAA,cACF,EAAE,CAAC;AAAA,kBACa,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,YAC3C,EAAE,CAAC;AAAA,UACL;AAAA,QACF;AAAA,MACF;AAAA,UACgB;AAAA,QACd,KAAK;AAAA,QACL;AAAA,UACE,SAAS,KAAK;AAAA,UACd,MAAM;AAAA,UACN,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,uBAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,kBACjC,wBAAI,KAAK,OAAO,EAAE,UAAU,EAAE,gBAAgB,EAAE,CAAC;AAAA,kBACjD,wBAAI,KAAK,SAAS,EAAE,cAA0B,wBAAI,OAAO,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC;AAAA,kBACxE,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,YAC3C,EAAE,CAAC;AAAA,UACL;AAAA,QACF;AAAA,MACF;AAAA,UACgB;AAAA,QACd,KAAK;AAAA,QACL;AAAA,UACE,SAAS,KAAK;AAAA,UACd,MAAM;AAAA,UACN,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,uBAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,kBACjC,wBAAI,KAAK,OAAO,EAAE,UAAU,EAAE,eAAe,EAAE,CAAC;AAAA,kBAChD,wBAAI,KAAK,SAAS,EAAE,cAA0B,wBAAI,OAAO,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC;AAAA,kBACxE,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,YAC3C,EAAE,CAAC;AAAA,UACL;AAAA,QACF;AAAA,MACF;AAAA,UACgB;AAAA,QACd,KAAK;AAAA,QACL;AAAA,UACE,SAAS,KAAK;AAAA,UACd,MAAM;AAAA,UACN,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,uBAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,kBACjC,wBAAI,KAAK,OAAO,EAAE,UAAU,EAAE,wBAAwB,EAAE,CAAC;AAAA,kBACzD,wBAAI,KAAK,SAAS,EAAE,cAA0B,wBAAI,eAAe,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC;AAAA,kBAChF,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,YAC3C,EAAE,CAAC;AAAA,UACL;AAAA,QACF;AAAA,MACF;AAAA,UACgB,wBAAI,mBAAmB,EAAE,QAAQ,KAAK,CAAC;AAAA,IACzD,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;AAAA,QACO,wBAAI,YAAY,QAAQ,EAAE,cAA0B,yBAAK,OAAO,EAAE,WAAW,yCAAyC,UAAU;AAAA,UAC9H,wBAAI,YAAY,OAAO,EAAE,SAAS,MAAM,cAA0B,wBAAI,QAAQ,EAAE,MAAM,SAAS,SAAS,aAAa,UAAU,EAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;AAAA,UACvJ,wBAAI,QAAQ,EAAE,MAAM,SAAS,MAAM,UAAU,WAAW,WAAW,UAAU,EAAE,cAAc,EAAE,CAAC;AAAA,IAClH,EAAE,CAAC,EAAE,CAAC;AAAA,EACR,EAAE,CAAC,EAAE,CAAC;AACR;AAIA,IAAI,oBAAoB,MAAM;AAC5B,QAAM,EAAE,GAAG,IAAI,UAAU;AACzB,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,QAAM,EAAE,SAAS,WAAW,SAAS,MAAM,IAAI,WAAW,IAAI;AAAA,IAC5D,QAAQ;AAAA,EACV,CAAC;AACD,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,oBAAAC,MAAM,aAAa,EAAE,UAAU;AAAA,QACpC,oBAAAC,KAAK,YAAY,QAAQ,EAAE,cAA0B,oBAAAA,KAAK,YAAY,OAAO,EAAE,SAAS,MAAM,cAA0B,oBAAAA,KAAK,SAAS,EAAE,UAAU,EAAE,yBAAyB,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;AAAA,IACtM,CAAC,aAAa,eAA2B,oBAAAA,KAAK,uBAAuB,EAAE,QAAQ,CAAC;AAAA,EAClF,EAAE,CAAC;AACL;", "names": ["import_jsx_runtime", "jsxs2", "jsx2"]}