{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-OV5NMSY6.mjs"], "sourcesContent": ["// src/lib/format-currency.ts\nvar formatCurrency = (amount, currency) => {\n  return new Intl.NumberFormat(\"en-US\", {\n    style: \"currency\",\n    currency,\n    signDisplay: \"auto\"\n  }).format(amount);\n};\n\nexport {\n  formatCurrency\n};\n"], "mappings": ";AACA,IAAI,iBAAiB,CAAC,QAAQ,aAAa;AACzC,SAAO,IAAI,KAAK,aAAa,SAAS;AAAA,IACpC,OAAO;AAAA,IACP;AAAA,IACA,aAAa;AAAA,EACf,CAAC,EAAE,OAAO,MAAM;AAClB;", "names": []}