import {
  Thumbnail
} from "./chunk-6GQUHAET.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/chunk-IQBAUTU5.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var ProductCell = ({ product }) => {
  return (0, import_jsx_runtime.jsxs)("div", { className: "flex h-full w-full max-w-[250px] items-center gap-x-3 overflow-hidden", children: [
    (0, import_jsx_runtime.jsx)("div", { className: "w-fit flex-shrink-0", children: (0, import_jsx_runtime.jsx)(Thumbnail, { src: product.thumbnail }) }),
    (0, import_jsx_runtime.jsx)("span", { title: product.title, className: "truncate", children: product.title })
  ] });
};
var ProductHeader = () => {
  const { t } = useTranslation();
  return (0, import_jsx_runtime.jsx)("div", { className: "flex h-full w-full items-center", children: (0, import_jsx_runtime.jsx)("span", { children: t("fields.product") }) });
};

export {
  ProductCell,
  ProductHeader
};
//# sourceMappingURL=chunk-NVCSASGM.js.map
