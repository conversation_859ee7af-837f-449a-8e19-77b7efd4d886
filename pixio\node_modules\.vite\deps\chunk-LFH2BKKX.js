import {
  CurrencyInput,
  Input,
  Text,
  clx
} from "./chunk-YP2LLXWB.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/chunk-YRY2CZ6I.mjs
var import_react = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var DeprecatedPercentageInput = (0, import_react.forwardRef)(({ min = 0, max = 100, step = 1e-4, ...props }, ref) => {
  return (0, import_jsx_runtime.jsxs)("div", { className: "relative", children: [
    (0, import_jsx_runtime.jsx)("div", { className: "absolute inset-y-0 left-0 z-10 flex w-8 items-center justify-center border-r", children: (0, import_jsx_runtime.jsx)(
      Text,
      {
        className: "text-ui-fg-muted",
        size: "small",
        leading: "compact",
        weight: "plus",
        children: "%"
      }
    ) }),
    (0, import_jsx_runtime.jsx)(
      Input,
      {
        ref,
        type: "number",
        min,
        max,
        step,
        ...props,
        className: "pl-10"
      }
    )
  ] });
});
DeprecatedPercentageInput.displayName = "PercentageInput";
var PercentageInput = (0, import_react.forwardRef)(({ min = 0, decimalScale = 2, className, ...props }, ref) => {
  return (0, import_jsx_runtime.jsxs)("div", { className: "relative", children: [
    (0, import_jsx_runtime.jsx)(
      CurrencyInput,
      {
        ref,
        min,
        autoComplete: "off",
        decimalScale,
        decimalsLimit: decimalScale,
        ...props,
        className: clx(
          "caret-ui-fg-base bg-ui-bg-field shadow-buttons-neutral transition-fg txt-compact-small flex w-full select-none appearance-none items-center justify-between rounded-md px-2 py-1.5 pr-10 text-right outline-none",
          "placeholder:text-ui-fg-muted text-ui-fg-base",
          "hover:bg-ui-bg-field-hover",
          "focus-visible:shadow-borders-interactive-with-active data-[state=open]:!shadow-borders-interactive-with-active",
          "aria-[invalid=true]:border-ui-border-error aria-[invalid=true]:shadow-borders-error",
          "invalid::border-ui-border-error invalid:shadow-borders-error",
          "disabled:!bg-ui-bg-disabled disabled:!text-ui-fg-disabled",
          className
        )
      }
    ),
    (0, import_jsx_runtime.jsx)("div", { className: "absolute inset-y-0 right-0 z-10 flex w-8 items-center justify-center border-l", children: (0, import_jsx_runtime.jsx)(
      Text,
      {
        className: "text-ui-fg-muted",
        size: "small",
        leading: "compact",
        weight: "plus",
        children: "%"
      }
    ) })
  ] });
});
PercentageInput.displayName = "PercentageInput";

export {
  DeprecatedPercentageInput,
  PercentageInput
};
//# sourceMappingURL=chunk-LFH2BKKX.js.map
