{"version": 3, "sources": ["../../@medusajs/dashboard/dist/order-list-KTYCJ4NW.mjs"], "sourcesContent": ["import {\n  useOrderTableColumns\n} from \"./chunk-RORIX3PU.mjs\";\nimport {\n  useOrderTableQuery\n} from \"./chunk-XMAWMECC.mjs\";\nimport \"./chunk-5HNRTDDS.mjs\";\nimport \"./chunk-NNBHHXXN.mjs\";\nimport \"./chunk-7DXVXBSA.mjs\";\nimport \"./chunk-PDWBYQOW.mjs\";\nimport \"./chunk-ADOCJB6L.mjs\";\nimport \"./chunk-P3UUX2T6.mjs\";\nimport {\n  _DataTable,\n  useDataTable\n} from \"./chunk-UE6PO4FK.mjs\";\nimport \"./chunk-YEDAFXMB.mjs\";\nimport \"./chunk-AOFGTNG6.mjs\";\nimport \"./chunk-EMIHDNB7.mjs\";\nimport \"./chunk-MWVM4TYO.mjs\";\nimport {\n  useOrderTableFilters\n} from \"./chunk-FVK4ZYYM.mjs\";\nimport \"./chunk-SXYXTC2L.mjs\";\nimport \"./chunk-M3VFKDXJ.mjs\";\nimport {\n  SingleColumnPage\n} from \"./chunk-2RQLKDBF.mjs\";\nimport \"./chunk-C76H5USB.mjs\";\nimport \"./chunk-KSV3NQOT.mjs\";\nimport {\n  useExtension\n} from \"./chunk-C5P5PL3E.mjs\";\nimport \"./chunk-LPEUYMRK.mjs\";\nimport \"./chunk-NYXYHZE6.mjs\";\nimport {\n  useOrders\n} from \"./chunk-FNYASI54.mjs\";\nimport \"./chunk-FVC7M755.mjs\";\nimport \"./chunk-PNU5HPGY.mjs\";\nimport \"./chunk-QZ6PT4QV.mjs\";\nimport \"./chunk-QL4XKIVL.mjs\";\nimport \"./chunk-6I62UDJA.mjs\";\nimport \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-RPUOO7AV.mjs\";\n\n// src/routes/orders/order-list/components/order-list-table/order-list-table.tsx\nimport { Container, Heading } from \"@medusajs/ui\";\nimport { keepPreviousData } from \"@tanstack/react-query\";\nimport { useTranslation } from \"react-i18next\";\n\n// src/routes/orders/order-list/const.ts\nvar DEFAULT_PROPERTIES = [\n  \"id\",\n  \"status\",\n  \"created_at\",\n  \"email\",\n  \"display_id\",\n  \"payment_status\",\n  \"fulfillment_status\",\n  \"total\",\n  \"currency_code\"\n];\nvar DEFAULT_RELATIONS = [\"*customer\", \"*sales_channel\"];\nvar DEFAULT_FIELDS = `${DEFAULT_PROPERTIES.join(\n  \",\"\n)},${DEFAULT_RELATIONS.join(\",\")}`;\n\n// src/routes/orders/order-list/components/order-list-table/order-list-table.tsx\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar PAGE_SIZE = 20;\nvar OrderListTable = () => {\n  const { t } = useTranslation();\n  const { searchParams, raw } = useOrderTableQuery({\n    pageSize: PAGE_SIZE\n  });\n  const { orders, count, isError, error, isLoading } = useOrders(\n    {\n      fields: DEFAULT_FIELDS,\n      ...searchParams\n    },\n    {\n      placeholderData: keepPreviousData\n    }\n  );\n  const filters = useOrderTableFilters();\n  const columns = useOrderTableColumns({});\n  const { table } = useDataTable({\n    data: orders ?? [],\n    columns,\n    enablePagination: true,\n    count,\n    pageSize: PAGE_SIZE\n  });\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsxs(Container, { className: \"divide-y p-0\", children: [\n    /* @__PURE__ */ jsx(\"div\", { className: \"flex items-center justify-between px-6 py-4\", children: /* @__PURE__ */ jsx(Heading, { children: t(\"orders.domain\") }) }),\n    /* @__PURE__ */ jsx(\n      _DataTable,\n      {\n        columns,\n        table,\n        pagination: true,\n        navigateTo: (row) => `/orders/${row.original.id}`,\n        filters,\n        count,\n        search: true,\n        isLoading,\n        pageSize: PAGE_SIZE,\n        orderBy: [\n          { key: \"display_id\", label: t(\"orders.fields.displayId\") },\n          { key: \"created_at\", label: t(\"fields.createdAt\") },\n          { key: \"updated_at\", label: t(\"fields.updatedAt\") }\n        ],\n        queryObject: raw,\n        noRecords: {\n          message: t(\"orders.list.noRecordsMessage\")\n        }\n      }\n    )\n  ] });\n};\n\n// src/routes/orders/order-list/order-list.tsx\nimport { jsx as jsx2 } from \"react/jsx-runtime\";\nvar OrderList = () => {\n  const { getWidgets } = useExtension();\n  return /* @__PURE__ */ jsx2(\n    SingleColumnPage,\n    {\n      widgets: {\n        after: getWidgets(\"order.list.after\"),\n        before: getWidgets(\"order.list.before\")\n      },\n      hasOutlet: false,\n      children: /* @__PURE__ */ jsx2(OrderListTable, {})\n    }\n  );\n};\nexport {\n  OrderList as Component\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuEA,yBAA0B;AAyD1B,IAAAA,sBAA4B;AA1E5B,IAAI,qBAAqB;AAAA,EACvB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,IAAI,oBAAoB,CAAC,aAAa,gBAAgB;AACtD,IAAI,iBAAiB,GAAG,mBAAmB;AAAA,EACzC;AACF,CAAC,IAAI,kBAAkB,KAAK,GAAG,CAAC;AAIhC,IAAI,YAAY;AAChB,IAAI,iBAAiB,MAAM;AACzB,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,QAAM,EAAE,cAAc,IAAI,IAAI,mBAAmB;AAAA,IAC/C,UAAU;AAAA,EACZ,CAAC;AACD,QAAM,EAAE,QAAQ,OAAO,SAAS,OAAO,UAAU,IAAI;AAAA,IACnD;AAAA,MACE,QAAQ;AAAA,MACR,GAAG;AAAA,IACL;AAAA,IACA;AAAA,MACE,iBAAiB;AAAA,IACnB;AAAA,EACF;AACA,QAAM,UAAU,qBAAqB;AACrC,QAAM,UAAU,qBAAqB,CAAC,CAAC;AACvC,QAAM,EAAE,MAAM,IAAI,aAAa;AAAA,IAC7B,MAAM,UAAU,CAAC;AAAA,IACjB;AAAA,IACA,kBAAkB;AAAA,IAClB;AAAA,IACA,UAAU;AAAA,EACZ,CAAC;AACD,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,yBAAK,WAAW,EAAE,WAAW,gBAAgB,UAAU;AAAA,QAC5D,wBAAI,OAAO,EAAE,WAAW,+CAA+C,cAA0B,wBAAI,SAAS,EAAE,UAAU,EAAE,eAAe,EAAE,CAAC,EAAE,CAAC;AAAA,QACjJ;AAAA,MACd;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,QACA,YAAY;AAAA,QACZ,YAAY,CAAC,QAAQ,WAAW,IAAI,SAAS,EAAE;AAAA,QAC/C;AAAA,QACA;AAAA,QACA,QAAQ;AAAA,QACR;AAAA,QACA,UAAU;AAAA,QACV,SAAS;AAAA,UACP,EAAE,KAAK,cAAc,OAAO,EAAE,yBAAyB,EAAE;AAAA,UACzD,EAAE,KAAK,cAAc,OAAO,EAAE,kBAAkB,EAAE;AAAA,UAClD,EAAE,KAAK,cAAc,OAAO,EAAE,kBAAkB,EAAE;AAAA,QACpD;AAAA,QACA,aAAa;AAAA,QACb,WAAW;AAAA,UACT,SAAS,EAAE,8BAA8B;AAAA,QAC3C;AAAA,MACF;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;AAIA,IAAI,YAAY,MAAM;AACpB,QAAM,EAAE,WAAW,IAAI,aAAa;AACpC,aAAuB,oBAAAC;AAAA,IACrB;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP,OAAO,WAAW,kBAAkB;AAAA,QACpC,QAAQ,WAAW,mBAAmB;AAAA,MACxC;AAAA,MACA,WAAW;AAAA,MACX,cAA0B,oBAAAA,KAAK,gBAAgB,CAAC,CAAC;AAAA,IACnD;AAAA,EACF;AACF;", "names": ["import_jsx_runtime", "jsx2"]}