import {
  ITEM_TOTAL_ATTRIBUTE
} from "./chunk-S4XCFSZC.js";
import {
  getLocaleAmount
} from "./chunk-UDMOPZAP.js";
import {
  DataGrid,
  DataGridCellContainer,
  IncludesTaxTooltip,
  createDataGridHelper,
  useCombinedRefs,
  useDataGridCell,
  useDataGridCellError
} from "./chunk-ZOK7LZDT.js";
import {
  currencies
} from "./chunk-H3DTEG3J.js";
import {
  castNumber
} from "./chunk-EZLR4STK.js";
import {
  KeyboundForm
} from "./chunk-DP54EP6X.js";
import {
  StackedFocusModal
} from "./chunk-MVVOBQIC.js";
import {
  t as t2
} from "./chunk-WHQIBI5S.js";
import {
  z
} from "./chunk-4XXECALA.js";
import {
  t
} from "./chunk-MPXR7HT5.js";
import {
  Controller,
  Form,
  useFieldArray,
  useForm,
  useFormContext,
  useWatch
} from "./chunk-XXJU43CK.js";
import {
  Trans,
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import {
  ArrowsPointingOut,
  Badge,
  Button,
  CircleSliders,
  CurrencyInput,
  CurrencyInput2,
  Divider,
  Heading,
  IconButton,
  InformationCircleSolid,
  Label,
  Plus,
  Text,
  Tooltip,
  TriangleDownMini,
  XMark,
  XMarkMini,
  clx,
  dist_exports2 as dist_exports,
  formatValue
} from "./chunk-YP2LLXWB.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/chunk-VTDYXPA4.mjs
var import_react = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_react2 = __toESM(require_react(), 1);
var import_react3 = __toESM(require_react(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var import_react4 = __toESM(require_react(), 1);
var import_react5 = __toESM(require_react(), 1);
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime4 = __toESM(require_jsx_runtime(), 1);
var createPriceRule = (attribute, operator, value) => {
  const rule = {
    attribute,
    operator,
    value: castNumber(value)
  };
  return rule;
};
var buildShippingOptionPriceRules = (rule) => {
  const conditions = [
    { value: rule.gte, operator: "gte" },
    { value: rule.lte, operator: "lte" },
    { value: rule.gt, operator: "gt" },
    { value: rule.lt, operator: "lt" },
    { value: rule.eq, operator: "eq" }
  ];
  const conditionsWithValues = conditions.filter(({ value }) => value);
  return conditionsWithValues.map(
    ({ operator, value }) => createPriceRule(ITEM_TOTAL_ATTRIBUTE, operator, value)
  );
};
var ConditionalPriceSchema = z.object({
  amount: z.union([z.string(), z.number()]),
  gte: z.union([z.string(), z.number()]).nullish(),
  lte: z.union([z.string(), z.number()]).nullish(),
  lt: z.number().nullish(),
  gt: z.number().nullish(),
  eq: z.number().nullish()
}).refine((data) => data.amount !== "", {
  message: t(
    "stockLocations.shippingOptions.conditionalPrices.errors.amountRequired"
  ),
  path: ["amount"]
}).refine(
  (data) => {
    const hasEqLtGt = data.eq !== void 0 || data.lt !== void 0 || data.gt !== void 0;
    if (hasEqLtGt) {
      return true;
    }
    return data.gte !== void 0 && data.gte !== "" || data.lte !== void 0 && data.lte !== "";
  },
  {
    message: t(
      "stockLocations.shippingOptions.conditionalPrices.errors.minOrMaxRequired"
    ),
    path: ["gte"]
  }
).refine(
  (data) => {
    if (data.gte != null && data.gte !== "" && data.lte != null && data.lte !== "") {
      const gte = castNumber(data.gte);
      const lte = castNumber(data.lte);
      return gte <= lte;
    }
    return true;
  },
  {
    message: t(
      "stockLocations.shippingOptions.conditionalPrices.errors.minGreaterThanMax"
    ),
    path: ["gte"]
  }
);
var UpdateConditionalPriceSchema = ConditionalPriceSchema.and(
  z.object({
    id: z.string().optional()
  })
);
function refineDuplicates(data, ctx) {
  const prices = data.prices;
  for (let i = 0; i < prices.length; i++) {
    for (let j = i + 1; j < prices.length; j++) {
      const price1 = prices[i];
      const price2 = prices[j];
      if (price1.amount === "" || price2.amount === "") {
        continue;
      }
      const price1Amount = castNumber(price1.amount);
      const price2Amount = castNumber(price2.amount);
      if (price1Amount === price2Amount) {
        addDuplicateAmountError(ctx, j);
      }
      const conditions = [
        { value: price1.gte, type: "gte" },
        { value: price1.lte, type: "lte" },
        { value: price1.eq, type: "eq" },
        { value: price1.lt, type: "lt" },
        { value: price1.gt, type: "gt" }
      ];
      conditions.forEach((condition1) => {
        if (!condition1.value && condition1.value !== 0) {
          return;
        }
        const conditions2 = [
          { value: price2.gte, type: "gte" },
          { value: price2.lte, type: "lte" },
          { value: price2.eq, type: "eq" },
          { value: price2.lt, type: "lt" },
          { value: price2.gt, type: "gt" }
        ];
        conditions2.forEach((condition2) => {
          if (!condition2.value && condition2.value !== 0) {
            return;
          }
          const condition1Value = castNumber(
            condition1.value
          );
          const condition2Value = castNumber(
            condition2.value
          );
          if (condition1Value === condition2Value) {
            addOverlappingConditionError(ctx, j, condition2.type);
          }
        });
      });
    }
  }
}
var CondtionalPriceRuleSchema = z.object({
  prices: z.array(ConditionalPriceSchema)
}).superRefine(refineDuplicates);
var UpdateConditionalPriceRuleSchema = z.object({
  prices: z.array(UpdateConditionalPriceSchema)
}).superRefine(refineDuplicates);
var addDuplicateAmountError = (ctx, index) => {
  ctx.addIssue({
    code: z.ZodIssueCode.custom,
    message: t(
      "stockLocations.shippingOptions.conditionalPrices.errors.duplicateAmount"
    ),
    path: ["prices", index, "amount"]
  });
};
var addOverlappingConditionError = (ctx, index, type) => {
  ctx.addIssue({
    code: z.ZodIssueCode.custom,
    message: t(
      "stockLocations.shippingOptions.conditionalPrices.errors.overlappingConditions"
    ),
    path: ["prices", index, type]
  });
};
var ShippingOptionPriceContext = (0, import_react.createContext)(null);
var ShippingOptionPriceProvider = ({
  children,
  onOpenConditionalPricesModal,
  onCloseConditionalPricesModal
}) => {
  return (0, import_jsx_runtime.jsx)(
    ShippingOptionPriceContext.Provider,
    {
      value: { onOpenConditionalPricesModal, onCloseConditionalPricesModal },
      children
    }
  );
};
var useShippingOptionPrice = () => {
  const context = (0, import_react2.useContext)(ShippingOptionPriceContext);
  if (!context) {
    throw new Error(
      "useShippingOptionPrice must be used within a ShippingOptionPriceProvider"
    );
  }
  return context;
};
var getCustomShippingOptionPriceFieldName = (field, type) => {
  const prefix = type === "region" ? "region_prices" : "currency_prices";
  const customPrefix = type === "region" ? "conditional_region_prices" : "conditional_currency_prices";
  const name = field.replace(
    prefix,
    customPrefix
  );
  return name;
};
var RULE_ITEM_PREFIX = "rule-item";
var getRuleValue = (index) => `${RULE_ITEM_PREFIX}-${index}`;
var ConditionalPriceForm = ({
  info,
  variant
}) => {
  const { t: t22 } = useTranslation();
  const { getValues, setValue: setFormValue } = useFormContext();
  const { onCloseConditionalPricesModal } = useShippingOptionPrice();
  const [value, setValue] = (0, import_react3.useState)([getRuleValue(0)]);
  const { field, type, currency, name: header } = info;
  const name = getCustomShippingOptionPriceFieldName(field, type);
  const conditionalPriceForm = useForm({
    defaultValues: {
      prices: getValues(name) || [
        {
          amount: "",
          gte: "",
          lte: null
        }
      ]
    },
    resolver: t2(
      variant === "create" ? CondtionalPriceRuleSchema : UpdateConditionalPriceRuleSchema
    )
  });
  const { fields, append, remove } = useFieldArray({
    control: conditionalPriceForm.control,
    name: "prices"
  });
  const handleAdd = () => {
    append({
      amount: "",
      gte: "",
      lte: null
    });
    setValue([...value, getRuleValue(fields.length)]);
  };
  const handleRemove = (index) => {
    remove(index);
  };
  const handleOnSubmit = conditionalPriceForm.handleSubmit(
    (values) => {
      setFormValue(name, values.prices, {
        shouldDirty: true,
        shouldValidate: true,
        shouldTouch: true
      });
      onCloseConditionalPricesModal();
    },
    (e) => {
      const indexesWithErrors = Object.keys(e.prices || {});
      setValue((prev) => {
        const values = new Set(prev);
        indexesWithErrors.forEach((index) => {
          values.add(getRuleValue(Number(index)));
        });
        return Array.from(values);
      });
    }
  );
  const handleOnKeyDown = (event) => {
    if (event.key === "Enter" && (event.metaKey || event.ctrlKey)) {
      console.log("Fired");
      event.preventDefault();
      event.stopPropagation();
      handleOnSubmit();
    }
  };
  return (0, import_jsx_runtime2.jsx)(Form, { ...conditionalPriceForm, children: (0, import_jsx_runtime2.jsx)(
    KeyboundForm,
    {
      onSubmit: handleOnSubmit,
      onKeyDown: handleOnKeyDown,
      className: "flex h-full flex-col",
      children: (0, import_jsx_runtime2.jsxs)(StackedFocusModal.Content, { children: [
        (0, import_jsx_runtime2.jsx)(StackedFocusModal.Header, {}),
        (0, import_jsx_runtime2.jsx)(StackedFocusModal.Body, { className: "size-full overflow-hidden", children: (0, import_jsx_runtime2.jsx)("div", { className: "flex size-full flex-1 flex-col items-center overflow-y-auto", children: (0, import_jsx_runtime2.jsx)("div", { className: "flex w-full max-w-[720px] flex-col gap-y-8 px-6 py-16", children: (0, import_jsx_runtime2.jsxs)("div", { className: "flex w-full flex-col gap-y-6", children: [
          (0, import_jsx_runtime2.jsxs)("div", { children: [
            (0, import_jsx_runtime2.jsx)(StackedFocusModal.Title, { asChild: true, children: (0, import_jsx_runtime2.jsx)(Heading, { children: t22(
              "stockLocations.shippingOptions.conditionalPrices.header",
              {
                name: header
              }
            ) }) }),
            (0, import_jsx_runtime2.jsx)(StackedFocusModal.Description, { asChild: true, children: (0, import_jsx_runtime2.jsx)(Text, { size: "small", className: "text-ui-fg-subtle", children: t22(
              "stockLocations.shippingOptions.conditionalPrices.description"
            ) }) })
          ] }),
          (0, import_jsx_runtime2.jsx)(ConditionalPriceList, { value, onValueChange: setValue, children: fields.map((field2, index) => (0, import_jsx_runtime2.jsx)(
            ConditionalPriceItem,
            {
              index,
              onRemove: handleRemove,
              currency,
              control: conditionalPriceForm.control
            },
            field2.id
          )) }),
          (0, import_jsx_runtime2.jsx)("div", { className: "flex items-center justify-end", children: (0, import_jsx_runtime2.jsx)(
            Button,
            {
              variant: "secondary",
              size: "small",
              type: "button",
              onClick: handleAdd,
              children: t22(
                "stockLocations.shippingOptions.conditionalPrices.actions.addPrice"
              )
            }
          ) })
        ] }) }) }) }),
        (0, import_jsx_runtime2.jsx)(StackedFocusModal.Footer, { children: (0, import_jsx_runtime2.jsxs)("div", { className: "flex items-center justify-end gap-2", children: [
          (0, import_jsx_runtime2.jsx)(StackedFocusModal.Close, { asChild: true, children: (0, import_jsx_runtime2.jsx)(Button, { variant: "secondary", size: "small", type: "button", children: t22("actions.cancel") }) }),
          (0, import_jsx_runtime2.jsx)(Button, { size: "small", type: "button", onClick: handleOnSubmit, children: t22("actions.save") })
        ] }) })
      ] })
    }
  ) });
};
var ConditionalPriceList = ({
  children,
  value,
  onValueChange
}) => {
  return (0, import_jsx_runtime2.jsx)(
    dist_exports.Root,
    {
      type: "multiple",
      defaultValue: [getRuleValue(0)],
      value,
      onValueChange,
      className: "flex flex-col gap-y-3",
      children
    }
  );
};
var ConditionalPriceItem = ({
  index,
  currency,
  onRemove,
  control
}) => {
  const { t: t22 } = useTranslation();
  const handleRemove = (e) => {
    e.stopPropagation();
    onRemove(index);
  };
  return (0, import_jsx_runtime2.jsxs)(
    dist_exports.Item,
    {
      value: getRuleValue(index),
      className: clx(
        "bg-ui-bg-component shadow-elevation-card-rest rounded-lg"
      ),
      children: [
        (0, import_jsx_runtime2.jsx)(dist_exports.Trigger, { asChild: true, children: (0, import_jsx_runtime2.jsxs)("div", { className: "group/trigger flex w-full cursor-pointer items-start justify-between gap-x-2 p-3", children: [
          (0, import_jsx_runtime2.jsxs)("div", { className: "flex flex-1 flex-wrap items-center justify-between gap-2", children: [
            (0, import_jsx_runtime2.jsx)("div", { className: "flex h-7 items-center", children: (0, import_jsx_runtime2.jsx)(
              AmountDisplay,
              {
                index,
                currency,
                control
              }
            ) }),
            (0, import_jsx_runtime2.jsx)("div", { className: "flex min-h-7 items-center", children: (0, import_jsx_runtime2.jsx)(
              ConditionDisplay,
              {
                index,
                currency,
                control
              }
            ) })
          ] }),
          (0, import_jsx_runtime2.jsxs)("div", { className: "flex items-center gap-x-2", children: [
            (0, import_jsx_runtime2.jsx)(
              IconButton,
              {
                size: "small",
                variant: "transparent",
                className: "text-ui-fg-muted hover:text-ui-fg-subtle focus-visible:text-ui-fg-subtle",
                onClick: handleRemove,
                children: (0, import_jsx_runtime2.jsx)(XMarkMini, {})
              }
            ),
            (0, import_jsx_runtime2.jsx)(
              IconButton,
              {
                size: "small",
                variant: "transparent",
                className: "text-ui-fg-muted hover:text-ui-fg-subtle focus-visible:text-ui-fg-subtle",
                children: (0, import_jsx_runtime2.jsx)(TriangleDownMini, { className: "transition-transform group-data-[state=open]/trigger:rotate-180" })
              }
            )
          ] })
        ] }) }),
        (0, import_jsx_runtime2.jsxs)(dist_exports.Content, { className: "text-ui-fg-subtle", children: [
          (0, import_jsx_runtime2.jsx)(Divider, { variant: "dashed" }),
          (0, import_jsx_runtime2.jsx)(
            Form.Field,
            {
              control,
              name: `prices.${index}.amount`,
              render: ({ field: { value, onChange, ...props } }) => {
                return (0, import_jsx_runtime2.jsx)(Form.Item, { children: (0, import_jsx_runtime2.jsxs)("div", { className: "grid grid-cols-2 items-start gap-x-2 p-3", children: [
                  (0, import_jsx_runtime2.jsx)("div", { className: "flex h-8 items-center", children: (0, import_jsx_runtime2.jsx)(Form.Label, { children: t22(
                    "stockLocations.shippingOptions.conditionalPrices.rules.amount"
                  ) }) }),
                  (0, import_jsx_runtime2.jsxs)("div", { className: "flex flex-col gap-y-1", children: [
                    (0, import_jsx_runtime2.jsx)(Form.Control, { children: (0, import_jsx_runtime2.jsx)(
                      CurrencyInput2,
                      {
                        className: "bg-ui-bg-field-component hover:bg-ui-bg-field-component-hover focus-visible:bg-ui-bg-field-component-hover",
                        placeholder: formatValue({
                          value: "0",
                          decimalScale: currency.decimal_digits
                        }),
                        decimalScale: currency.decimal_digits,
                        symbol: currency.symbol_native,
                        code: currency.code,
                        value,
                        onValueChange: (_value, _name, values) => onChange((values == null ? void 0 : values.value) ? values == null ? void 0 : values.value : ""),
                        autoFocus: true,
                        ...props
                      }
                    ) }),
                    (0, import_jsx_runtime2.jsx)(Form.ErrorMessage, {})
                  ] })
                ] }) });
              }
            }
          ),
          (0, import_jsx_runtime2.jsx)(Divider, { variant: "dashed" }),
          (0, import_jsx_runtime2.jsx)(
            Form.Field,
            {
              control,
              name: `prices.${index}.gte`,
              render: ({ field }) => {
                return (0, import_jsx_runtime2.jsx)(
                  OperatorInput,
                  {
                    field,
                    label: t22(
                      "stockLocations.shippingOptions.conditionalPrices.rules.gte"
                    ),
                    currency,
                    placeholder: "1000"
                  }
                );
              }
            }
          ),
          (0, import_jsx_runtime2.jsx)(Divider, { variant: "dashed" }),
          (0, import_jsx_runtime2.jsx)(
            Form.Field,
            {
              control,
              name: `prices.${index}.lte`,
              render: ({ field }) => {
                return (0, import_jsx_runtime2.jsx)(
                  OperatorInput,
                  {
                    field,
                    label: t22(
                      "stockLocations.shippingOptions.conditionalPrices.rules.lte"
                    ),
                    currency,
                    placeholder: "1000"
                  }
                );
              }
            }
          ),
          (0, import_jsx_runtime2.jsx)(
            ReadOnlyConditions,
            {
              index,
              control,
              currency
            }
          )
        ] })
      ]
    }
  );
};
var OperatorInput = ({
  field,
  label,
  currency,
  placeholder
}) => {
  const innerRef = (0, import_react3.useRef)(null);
  const { value, onChange, ref, ...props } = field;
  const refs = useCombinedRefs(innerRef, ref);
  const action = () => {
    if (value === null) {
      onChange("");
      requestAnimationFrame(() => {
        var _a;
        (_a = innerRef.current) == null ? void 0 : _a.focus();
      });
      return;
    }
    onChange(null);
  };
  const isNull = value === null;
  return (0, import_jsx_runtime2.jsx)(Form.Item, { children: (0, import_jsx_runtime2.jsxs)("div", { className: "grid grid-cols-2 items-start gap-x-2 p-3", children: [
    (0, import_jsx_runtime2.jsxs)("div", { className: "flex h-8 items-center gap-x-1", children: [
      (0, import_jsx_runtime2.jsx)(IconButton, { size: "2xsmall", variant: "transparent", onClick: action, children: isNull ? (0, import_jsx_runtime2.jsx)(Plus, {}) : (0, import_jsx_runtime2.jsx)(XMark, {}) }),
      (0, import_jsx_runtime2.jsx)(Form.Label, { children: label })
    ] }),
    !isNull && (0, import_jsx_runtime2.jsxs)("div", { className: "flex flex-col gap-y-1", children: [
      (0, import_jsx_runtime2.jsx)(Form.Control, { children: (0, import_jsx_runtime2.jsx)(
        CurrencyInput2,
        {
          className: "bg-ui-bg-field-component hover:bg-ui-bg-field-component-hover focus-visible:bg-ui-bg-field-component-hover",
          placeholder: formatValue({
            value: placeholder,
            decimalScale: currency.decimal_digits
          }),
          decimalScale: currency.decimal_digits,
          symbol: currency.symbol_native,
          code: currency.code,
          value,
          ref: refs,
          onValueChange: (_value, _name, values) => onChange((values == null ? void 0 : values.value) ? values == null ? void 0 : values.value : ""),
          ...props
        }
      ) }),
      (0, import_jsx_runtime2.jsx)(Form.ErrorMessage, {})
    ] })
  ] }) });
};
var ReadOnlyConditions = ({
  index,
  control,
  currency
}) => {
  const { t: t22 } = useTranslation();
  const item = useWatch({
    control,
    name: `prices.${index}`
  });
  if (item.eq == null && item.gt == null && item.lt == null) {
    return null;
  }
  return (0, import_jsx_runtime2.jsxs)("div", { children: [
    (0, import_jsx_runtime2.jsx)(Divider, { variant: "dashed" }),
    (0, import_jsx_runtime2.jsxs)("div", { className: "flex items-center gap-x-1 px-3 pt-3", children: [
      (0, import_jsx_runtime2.jsx)(Text, { size: "small", leading: "compact", weight: "plus", children: t22(
        "stockLocations.shippingOptions.conditionalPrices.customRules.label"
      ) }),
      (0, import_jsx_runtime2.jsx)(
        Tooltip,
        {
          content: t22(
            "stockLocations.shippingOptions.conditionalPrices.customRules.tooltip"
          ),
          children: (0, import_jsx_runtime2.jsx)(InformationCircleSolid, { className: "text-ui-fg-muted" })
        }
      )
    ] }),
    (0, import_jsx_runtime2.jsxs)("div", { children: [
      item.eq != null && (0, import_jsx_runtime2.jsxs)("div", { className: "grid grid-cols-2 items-start gap-x-2 p-3", children: [
        (0, import_jsx_runtime2.jsx)("div", { className: "flex h-8 items-center", children: (0, import_jsx_runtime2.jsx)(Label, { weight: "plus", size: "small", children: t22(
          "stockLocations.shippingOptions.conditionalPrices.customRules.eq"
        ) }) }),
        (0, import_jsx_runtime2.jsx)(
          CurrencyInput2,
          {
            className: "bg-ui-bg-field-component hover:bg-ui-bg-field-component-hover focus-visible:bg-ui-bg-field-component-hover",
            symbol: currency.symbol_native,
            code: currency.code,
            value: item.eq,
            disabled: true
          }
        )
      ] }),
      item.gt != null && (0, import_jsx_runtime2.jsxs)(import_react3.Fragment, { children: [
        (0, import_jsx_runtime2.jsx)(Divider, { variant: "dashed" }),
        (0, import_jsx_runtime2.jsxs)("div", { className: "grid grid-cols-2 items-start gap-x-2 p-3", children: [
          (0, import_jsx_runtime2.jsx)("div", { className: "flex h-8 items-center", children: (0, import_jsx_runtime2.jsx)(Label, { weight: "plus", size: "small", children: t22(
            "stockLocations.shippingOptions.conditionalPrices.customRules.gt"
          ) }) }),
          (0, import_jsx_runtime2.jsx)(
            CurrencyInput2,
            {
              className: "bg-ui-bg-field-component hover:bg-ui-bg-field-component-hover focus-visible:bg-ui-bg-field-component-hover",
              symbol: currency.symbol_native,
              code: currency.code,
              value: item.gt,
              disabled: true
            }
          )
        ] })
      ] }),
      item.lt != null && (0, import_jsx_runtime2.jsxs)(import_react3.Fragment, { children: [
        (0, import_jsx_runtime2.jsx)(Divider, { variant: "dashed" }),
        (0, import_jsx_runtime2.jsxs)("div", { className: "grid grid-cols-2 items-start gap-x-2 p-3", children: [
          (0, import_jsx_runtime2.jsx)("div", { className: "flex h-8 items-center", children: (0, import_jsx_runtime2.jsx)(Label, { weight: "plus", size: "small", children: t22(
            "stockLocations.shippingOptions.conditionalPrices.customRules.lt"
          ) }) }),
          (0, import_jsx_runtime2.jsx)(
            CurrencyInput2,
            {
              className: "bg-ui-bg-field-component hover:bg-ui-bg-field-component-hover focus-visible:bg-ui-bg-field-component-hover",
              symbol: currency.symbol_native,
              code: currency.code,
              value: item.lt,
              disabled: true
            }
          )
        ] })
      ] })
    ] })
  ] });
};
var AmountDisplay = ({
  index,
  currency,
  control
}) => {
  const amount = useWatch({
    control,
    name: `prices.${index}.amount`
  });
  if (amount === "" || amount === void 0) {
    return (0, import_jsx_runtime2.jsx)(Text, { size: "small", weight: "plus", children: "-" });
  }
  const castAmount = castNumber(amount);
  return (0, import_jsx_runtime2.jsx)(Text, { size: "small", weight: "plus", children: getLocaleAmount(castAmount, currency.code) });
};
var ConditionContainer = ({ children }) => (0, import_jsx_runtime2.jsx)("div", { className: "text-ui-fg-subtle txt-small flex flex-wrap items-center gap-1.5", children });
var ConditionDisplay = ({
  index,
  currency,
  control
}) => {
  const { t: t22, i18n } = useTranslation();
  const gte = useWatch({
    control,
    name: `prices.${index}.gte`
  });
  const lte = useWatch({
    control,
    name: `prices.${index}.lte`
  });
  const renderCondition = () => {
    const castGte = gte ? castNumber(gte) : void 0;
    const castLte = lte ? castNumber(lte) : void 0;
    if (!castGte && !castLte) {
      return null;
    }
    if (castGte && !castLte) {
      return (0, import_jsx_runtime2.jsx)(ConditionContainer, { children: (0, import_jsx_runtime2.jsx)(
        Trans,
        {
          i18n,
          i18nKey: "stockLocations.shippingOptions.conditionalPrices.summaries.greaterThan",
          components: [
            (0, import_jsx_runtime2.jsx)(Badge, { size: "2xsmall" }, "attribute"),
            (0, import_jsx_runtime2.jsx)(Badge, { size: "2xsmall" }, "gte")
          ],
          values: {
            attribute: t22(
              "stockLocations.shippingOptions.conditionalPrices.attributes.cartItemTotal"
            ),
            gte: getLocaleAmount(castGte, currency.code)
          }
        }
      ) });
    }
    if (!castGte && castLte) {
      return (0, import_jsx_runtime2.jsx)(ConditionContainer, { children: (0, import_jsx_runtime2.jsx)(
        Trans,
        {
          i18n,
          i18nKey: "stockLocations.shippingOptions.conditionalPrices.summaries.lessThan",
          components: [
            (0, import_jsx_runtime2.jsx)(Badge, { size: "2xsmall" }, "attribute"),
            (0, import_jsx_runtime2.jsx)(Badge, { size: "2xsmall" }, "lte")
          ],
          values: {
            attribute: t22(
              "stockLocations.shippingOptions.conditionalPrices.attributes.cartItemTotal"
            ),
            lte: getLocaleAmount(castLte, currency.code)
          }
        }
      ) });
    }
    if (castGte && castLte) {
      return (0, import_jsx_runtime2.jsx)(ConditionContainer, { children: (0, import_jsx_runtime2.jsx)(
        Trans,
        {
          i18n,
          i18nKey: "stockLocations.shippingOptions.conditionalPrices.summaries.range",
          components: [
            (0, import_jsx_runtime2.jsx)(Badge, { size: "2xsmall" }, "attribute"),
            (0, import_jsx_runtime2.jsx)(Badge, { size: "2xsmall" }, "gte"),
            (0, import_jsx_runtime2.jsx)(Badge, { size: "2xsmall" }, "lte")
          ],
          values: {
            attribute: t22(
              "stockLocations.shippingOptions.conditionalPrices.attributes.cartItemTotal"
            ),
            gte: getLocaleAmount(castGte, currency.code),
            lte: getLocaleAmount(castLte, currency.code)
          }
        }
      ) });
    }
    return null;
  };
  return renderCondition();
};
var ShippingOptionPriceCell = ({
  context,
  code,
  header,
  type
}) => {
  const [symbolWidth, setSymbolWidth] = (0, import_react5.useState)(0);
  const measuredRef = (0, import_react5.useCallback)((node) => {
    if (node) {
      const width = node.offsetWidth;
      setSymbolWidth(width);
    }
  }, []);
  const { field, control, renderProps } = useDataGridCell({
    context
  });
  const errorProps = useDataGridCellError({ context });
  const { container, input } = renderProps;
  const { isAnchor } = container;
  const currency = currencies[code.toUpperCase()];
  return (0, import_jsx_runtime3.jsx)(
    Controller,
    {
      control,
      name: field,
      render: ({ field: props }) => {
        return (0, import_jsx_runtime3.jsx)(
          DataGridCellContainer,
          {
            ...container,
            ...errorProps,
            outerComponent: (0, import_jsx_runtime3.jsx)(
              OuterComponent,
              {
                header,
                isAnchor,
                field,
                control,
                symbolWidth,
                type,
                currency
              }
            ),
            children: (0, import_jsx_runtime3.jsx)(
              Inner,
              {
                field: props,
                inputProps: input,
                currencyInfo: currency,
                onMeasureSymbol: measuredRef
              }
            )
          }
        );
      }
    }
  );
};
var OuterComponent = ({
  isAnchor,
  header,
  field,
  control,
  symbolWidth,
  type,
  currency
}) => {
  const { onOpenConditionalPricesModal } = useShippingOptionPrice();
  const buttonRef = (0, import_react5.useRef)(null);
  const name = getCustomShippingOptionPriceFieldName(field, type);
  const price = useWatch({ control, name });
  (0, import_react5.useEffect)(() => {
    const handleKeyDown = (e) => {
      var _a;
      if (isAnchor && (e.metaKey || e.ctrlKey) && e.key.toLowerCase() === "b") {
        e.preventDefault();
        (_a = buttonRef.current) == null ? void 0 : _a.click();
      }
    };
    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, [isAnchor]);
  return (0, import_jsx_runtime3.jsxs)(
    "div",
    {
      className: "absolute inset-y-0 z-[3] flex w-fit items-center justify-center",
      style: {
        left: symbolWidth ? `${symbolWidth + 16 + 4}px` : void 0
      },
      children: [
        (price == null ? void 0 : price.length) > 0 && !isAnchor && (0, import_jsx_runtime3.jsx)("div", { className: "flex size-[15px] items-center justify-center group-hover/container:hidden", children: (0, import_jsx_runtime3.jsx)(CircleSliders, { className: "text-ui-fg-interactive" }) }),
        (0, import_jsx_runtime3.jsx)(
          "button",
          {
            ref: buttonRef,
            type: "button",
            className: clx(
              "hover:text-ui-fg-subtle text-ui-fg-muted transition-fg hidden size-[15px] items-center justify-center rounded-md bg-transparent group-hover/container:flex",
              { flex: isAnchor }
            ),
            onClick: () => onOpenConditionalPricesModal({
              type,
              field,
              currency,
              name: header
            }),
            children: (0, import_jsx_runtime3.jsx)(ArrowsPointingOut, {})
          }
        )
      ]
    }
  );
};
var Inner = ({
  field,
  onMeasureSymbol,
  inputProps,
  currencyInfo
}) => {
  const { value, onChange: _, onBlur, ref, ...rest } = field;
  const {
    ref: inputRef,
    onBlur: onInputBlur,
    onFocus,
    onChange,
    ...attributes
  } = inputProps;
  const formatter = (0, import_react5.useCallback)(
    (value2) => {
      const ensuredValue = typeof value2 === "number" ? value2.toString() : value2 || "";
      return formatValue({
        value: ensuredValue,
        decimalScale: currencyInfo.decimal_digits,
        disableGroupSeparators: true,
        decimalSeparator: "."
      });
    },
    [currencyInfo]
  );
  const [localValue, setLocalValue] = (0, import_react5.useState)(value || "");
  const handleValueChange = (value2, _name, _values) => {
    if (!value2) {
      setLocalValue("");
      return;
    }
    setLocalValue(value2);
  };
  (0, import_react5.useEffect)(() => {
    let update = value;
    if (!isNaN(Number(value))) {
      update = formatter(update);
    }
    setLocalValue(update);
  }, [value, formatter]);
  const combinedRed = useCombinedRefs(inputRef, ref);
  return (0, import_jsx_runtime3.jsxs)("div", { className: "relative flex size-full items-center", children: [
    (0, import_jsx_runtime3.jsx)(
      "span",
      {
        className: "txt-compact-small text-ui-fg-muted pointer-events-none absolute left-0 w-fit min-w-4",
        "aria-hidden": true,
        ref: onMeasureSymbol,
        children: currencyInfo.symbol_native
      }
    ),
    (0, import_jsx_runtime3.jsx)(
      CurrencyInput,
      {
        ...rest,
        ...attributes,
        ref: combinedRed,
        className: "txt-compact-small w-full flex-1 cursor-default appearance-none bg-transparent pl-[60px] text-right outline-none",
        value: localValue || void 0,
        onValueChange: handleValueChange,
        formatValueOnBlur: true,
        onBlur: () => {
          onBlur();
          onInputBlur();
          onChange(localValue, value);
        },
        onFocus,
        decimalScale: currencyInfo.decimal_digits,
        decimalsLimit: currencyInfo.decimal_digits,
        autoComplete: "off",
        tabIndex: -1
      }
    )
  ] });
};
var columnHelper = createDataGridHelper();
var useShippingOptionPriceColumns = ({
  name,
  currencies: currencies2 = [],
  regions = [],
  pricePreferences = []
}) => {
  const { t: t22 } = useTranslation();
  return (0, import_react4.useMemo)(() => {
    return [
      columnHelper.column({
        id: "name",
        name: t22("fields.name"),
        disableHiding: true,
        header: t22("fields.name"),
        cell: (context) => {
          return (0, import_jsx_runtime4.jsx)(DataGrid.ReadonlyCell, { context, children: name });
        }
      }),
      ...createDataGridPriceColumns({
        currencies: currencies2,
        regions,
        pricePreferences,
        getFieldName: (context, value) => {
          var _a;
          if ((_a = context.column.id) == null ? void 0 : _a.startsWith("currency_prices")) {
            return `currency_prices.${value}`;
          }
          return `region_prices.${value}`;
        },
        t: t22
      })
    ];
  }, [t22, currencies2, regions, pricePreferences, name]);
};
var createDataGridPriceColumns = ({
  currencies: currencies2,
  regions,
  pricePreferences,
  getFieldName,
  t: t22
}) => {
  const columnHelper2 = createDataGridHelper();
  return [
    ...(currencies2 == null ? void 0 : currencies2.map((currency) => {
      const preference = pricePreferences == null ? void 0 : pricePreferences.find(
        (p) => p.attribute === "currency_code" && p.value === currency
      );
      const translatedCurrencyName = t22("fields.priceTemplate", {
        regionOrCurrency: currency.toUpperCase()
      });
      return columnHelper2.column({
        id: `currency_prices.${currency}`,
        name: t22("fields.priceTemplate", {
          regionOrCurrency: currency.toUpperCase()
        }),
        field: (context) => {
          return getFieldName(context, currency);
        },
        type: "number",
        header: () => (0, import_jsx_runtime4.jsxs)("div", { className: "flex w-full items-center justify-between gap-3", children: [
          (0, import_jsx_runtime4.jsx)("span", { className: "truncate", title: translatedCurrencyName, children: translatedCurrencyName }),
          (0, import_jsx_runtime4.jsx)(IncludesTaxTooltip, { includesTax: preference == null ? void 0 : preference.is_tax_inclusive })
        ] }),
        cell: (context) => {
          return (0, import_jsx_runtime4.jsx)(
            ShippingOptionPriceCell,
            {
              type: "currency",
              header: translatedCurrencyName,
              code: currency,
              context
            }
          );
        }
      });
    })) ?? [],
    ...(regions == null ? void 0 : regions.map((region) => {
      const preference = pricePreferences == null ? void 0 : pricePreferences.find(
        (p) => p.attribute === "region_id" && p.value === region.id
      );
      const translatedRegionName = t22("fields.priceTemplate", {
        regionOrCurrency: region.name
      });
      return columnHelper2.column({
        id: `region_prices.${region.id}`,
        name: t22("fields.priceTemplate", {
          regionOrCurrency: region.name
        }),
        field: (context) => {
          return getFieldName(context, region.id);
        },
        type: "number",
        header: () => (0, import_jsx_runtime4.jsxs)("div", { className: "flex w-full items-center justify-between gap-3", children: [
          (0, import_jsx_runtime4.jsx)("span", { className: "truncate", title: translatedRegionName, children: translatedRegionName }),
          (0, import_jsx_runtime4.jsx)(IncludesTaxTooltip, { includesTax: preference == null ? void 0 : preference.is_tax_inclusive })
        ] }),
        cell: (context) => {
          const currency = currencies2 == null ? void 0 : currencies2.find((c) => c === region.currency_code);
          if (!currency) {
            return null;
          }
          return (0, import_jsx_runtime4.jsx)(
            ShippingOptionPriceCell,
            {
              type: "region",
              header: translatedRegionName,
              code: region.currency_code,
              context
            }
          );
        }
      });
    })) ?? []
  ];
};

export {
  buildShippingOptionPriceRules,
  ConditionalPriceSchema,
  UpdateConditionalPriceSchema,
  ShippingOptionPriceProvider,
  ConditionalPriceForm,
  useShippingOptionPriceColumns
};
//# sourceMappingURL=chunk-WP4KKMSI.js.map
