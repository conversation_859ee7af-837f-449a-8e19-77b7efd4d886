import {
  AddCampaignPromotionForm
} from "./chunk-QJ4H2UHV.js";
import "./chunk-ENAZYTQU.js";
import "./chunk-H3DTEG3J.js";
import "./chunk-EGRHWZRV.js";
import "./chunk-DP54EP6X.js";
import {
  RouteDrawer
} from "./chunk-MVVOBQIC.js";
import "./chunk-WHQIBI5S.js";
import "./chunk-4XXECALA.js";
import "./chunk-XXJU43CK.js";
import {
  useCampaigns,
  usePromotion
} from "./chunk-TZWW72YW.js";
import "./chunk-AAFHKNJG.js";
import "./chunk-3JKGO5XL.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import "./chunk-JFCIYOH4.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-5GF3RGIE.js";
import {
  useParams
} from "./chunk-T7YBVUWZ.js";
import {
  Heading
} from "./chunk-YP2LLXWB.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-ZSQ362B4.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/promotion-add-campaign-4MRPKION.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var PromotionAddCampaign = () => {
  var _a, _b;
  const { id } = useParams();
  const { t } = useTranslation();
  const { promotion, isPending, isError, error } = usePromotion(id);
  let campaignQuery = {};
  if ((_a = promotion == null ? void 0 : promotion.application_method) == null ? void 0 : _a.currency_code) {
    campaignQuery = {
      budget: {
        currency_code: (_b = promotion == null ? void 0 : promotion.application_method) == null ? void 0 : _b.currency_code
      }
    };
  }
  const {
    campaigns,
    isPending: areCampaignsLoading,
    isError: isCampaignError,
    error: campaignError
  } = useCampaigns(campaignQuery);
  if (isError || isCampaignError) {
    throw error || campaignError;
  }
  return (0, import_jsx_runtime.jsxs)(RouteDrawer, { children: [
    (0, import_jsx_runtime.jsx)(RouteDrawer.Header, { children: (0, import_jsx_runtime.jsx)(Heading, { children: t("promotions.campaign.edit.header") }) }),
    !isPending && !areCampaignsLoading && promotion && campaigns && (0, import_jsx_runtime.jsx)(AddCampaignPromotionForm, { promotion, campaigns })
  ] });
};
export {
  PromotionAddCampaign as Component
};
//# sourceMappingURL=promotion-add-campaign-4MRPKION-EUNNQFK7.js.map
