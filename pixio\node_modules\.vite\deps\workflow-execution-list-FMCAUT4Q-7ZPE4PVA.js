import {
  getTransactionState,
  getTransactionStateColor
} from "./chunk-MFHP2J6Q.js";
import {
  StatusCell
} from "./chunk-OVCKROM5.js";
import "./chunk-EGRHWZRV.js";
import {
  _DataTable,
  useDataTable
} from "./chunk-VCX3BVQR.js";
import "./chunk-VCBFQV64.js";
import "./chunk-QLJZR2JY.js";
import "./chunk-X3TOWPPJ.js";
import {
  SingleColumnPage
} from "./chunk-3LNIL4XX.js";
import {
  useQueryParams
} from "./chunk-32T72GVU.js";
import "./chunk-MX43XOWY.js";
import "./chunk-QX6SXRUW.js";
import "./chunk-7ANVLPZR.js";
import "./chunk-2E2FUO6N.js";
import {
  useExtension
} from "./chunk-FTD3ZWHZ.js";
import "./chunk-YXT43UJF.js";
import "./chunk-DL4QDYPT.js";
import "./chunk-NV2N3EWM.js";
import "./chunk-BF7OBKIN.js";
import "./chunk-7UAYECTW.js";
import "./chunk-Y3NYV3NU.js";
import "./chunk-MPXR7HT5.js";
import {
  useWorkflowExecutions
} from "./chunk-3R6LYIXM.js";
import "./chunk-CUPZIPFX.js";
import "./chunk-JFCIYOH4.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-5GF3RGIE.js";
import "./chunk-T7YBVUWZ.js";
import {
  Badge,
  Container,
  Heading,
  Text,
  createColumnHelper
} from "./chunk-YP2LLXWB.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-ZSQ362B4.js";
import {
  keepPreviousData
} from "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/workflow-execution-list-FMCAUT4Q.mjs
var import_react = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var columnHelper = createColumnHelper();
var useWorkflowExecutionTableColumns = () => {
  const { t } = useTranslation();
  return (0, import_react.useMemo)(
    () => [
      columnHelper.accessor("transaction_id", {
        header: t("workflowExecutions.transactionIdLabel"),
        cell: ({ getValue }) => (0, import_jsx_runtime.jsx)(Badge, { size: "2xsmall", children: getValue() })
      }),
      columnHelper.accessor("state", {
        header: t("fields.state"),
        cell: ({ getValue }) => {
          const state = getValue();
          const color = getTransactionStateColor(state);
          const translatedState = getTransactionState(t, state);
          return (0, import_jsx_runtime.jsx)(StatusCell, { color, children: (0, import_jsx_runtime.jsx)("span", { className: "capitalize", children: translatedState }) });
        }
      }),
      columnHelper.accessor("execution", {
        header: t("workflowExecutions.progressLabel"),
        cell: ({ getValue }) => {
          var _a;
          const steps = (_a = getValue()) == null ? void 0 : _a.steps;
          if (!steps) {
            return "0 of 0 steps";
          }
          const actionableSteps = Object.values(steps).filter(
            (step) => step.id !== ROOT_PREFIX
          );
          const completedSteps = actionableSteps.filter(
            (step) => step.invoke.state === "done"
            /* DONE */
          );
          return t("workflowExecutions.stepsCompletedLabel", {
            completed: completedSteps.length,
            count: actionableSteps.length
          });
        }
      })
    ],
    [t]
  );
};
var ROOT_PREFIX = "_root";
var useWorkflowExecutionTableQuery = ({
  pageSize = 20,
  prefix
}) => {
  const raw = useQueryParams(["q", "offset"], prefix);
  const { offset, ...rest } = raw;
  const searchParams = {
    limit: pageSize,
    offset: offset ? parseInt(offset) : 0,
    ...rest
  };
  return {
    searchParams,
    raw
  };
};
var PAGE_SIZE = 20;
var WorkflowExecutionListTable = () => {
  const { t } = useTranslation();
  const { searchParams, raw } = useWorkflowExecutionTableQuery({
    pageSize: PAGE_SIZE
  });
  const { workflow_executions, count, isLoading, isError, error } = useWorkflowExecutions(
    {
      ...searchParams
    },
    {
      placeholderData: keepPreviousData
    }
  );
  const columns = useWorkflowExecutionTableColumns();
  const { table } = useDataTable({
    data: workflow_executions || [],
    columns,
    count,
    pageSize: PAGE_SIZE,
    enablePagination: true,
    getRowId: (row) => row.id
  });
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime2.jsxs)(Container, { className: "divide-y p-0", children: [
    (0, import_jsx_runtime2.jsx)("div", { className: "flex items-center justify-between px-6 py-4", children: (0, import_jsx_runtime2.jsxs)("div", { children: [
      (0, import_jsx_runtime2.jsx)(Heading, { children: t("workflowExecutions.domain") }),
      (0, import_jsx_runtime2.jsx)(Text, { className: "text-ui-fg-subtle", size: "small", children: t(`workflowExecutions.subtitle`) })
    ] }) }),
    (0, import_jsx_runtime2.jsx)(
      _DataTable,
      {
        table,
        columns,
        count,
        isLoading,
        pageSize: PAGE_SIZE,
        navigateTo: (row) => `${row.id}`,
        search: true,
        pagination: true,
        queryObject: raw,
        noRecords: {
          message: t("workflowExecutions.list.noRecordsMessage")
        }
      }
    )
  ] });
};
var WorkflowExcecutionList = () => {
  const { getWidgets } = useExtension();
  return (0, import_jsx_runtime3.jsx)(
    SingleColumnPage,
    {
      widgets: {
        after: getWidgets("workflow.list.after"),
        before: getWidgets("workflow.list.before")
      },
      hasOutlet: false,
      children: (0, import_jsx_runtime3.jsx)(WorkflowExecutionListTable, {})
    }
  );
};
export {
  WorkflowExcecutionList as Component
};
//# sourceMappingURL=workflow-execution-list-FMCAUT4Q-7ZPE4PVA.js.map
