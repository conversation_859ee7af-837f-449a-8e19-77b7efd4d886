import {
  HandleInput
} from "./chunk-ABBBWI4J.js";
import "./chunk-EGRHWZRV.js";
import {
  KeyboundForm
} from "./chunk-DP54EP6X.js";
import {
  RouteFocusModal,
  useRouteModal
} from "./chunk-MVVOBQIC.js";
import {
  t
} from "./chunk-WHQIBI5S.js";
import {
  objectType,
  stringType
} from "./chunk-4XXECALA.js";
import {
  Form,
  useForm
} from "./chunk-XXJU43CK.js";
import {
  useCreateCollection
} from "./chunk-YL3TYBFR.js";
import "./chunk-AJYMIHLQ.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import "./chunk-JFCIYOH4.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-5GF3RGIE.js";
import "./chunk-T7YBVUWZ.js";
import {
  Button,
  Heading,
  Input,
  Text,
  toast
} from "./chunk-YP2LLXWB.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-ZSQ362B4.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/collection-create-6MZU3JJO.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var CreateCollectionSchema = objectType({
  title: stringType().min(1),
  handle: stringType().optional()
});
var CreateCollectionForm = () => {
  const { t: t2 } = useTranslation();
  const { handleSuccess } = useRouteModal();
  const form = useForm({
    defaultValues: {
      title: "",
      handle: ""
    },
    resolver: t(CreateCollectionSchema)
  });
  const { mutateAsync, isPending } = useCreateCollection();
  const handleSubmit = form.handleSubmit(async (data) => {
    await mutateAsync(data, {
      onSuccess: ({ collection }) => {
        handleSuccess(`/collections/${collection.id}`);
        toast.success(t2("collections.createSuccess"));
      },
      onError: (error) => {
        toast.error(error.message);
      }
    });
  });
  return (0, import_jsx_runtime.jsx)(RouteFocusModal.Form, { form, children: (0, import_jsx_runtime.jsxs)(KeyboundForm, { onSubmit: handleSubmit, children: [
    (0, import_jsx_runtime.jsx)(RouteFocusModal.Header, { children: (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center justify-end gap-x-2", children: [
      (0, import_jsx_runtime.jsx)(RouteFocusModal.Close, { asChild: true, children: (0, import_jsx_runtime.jsx)(Button, { size: "small", variant: "secondary", children: t2("actions.cancel") }) }),
      (0, import_jsx_runtime.jsx)(
        Button,
        {
          size: "small",
          variant: "primary",
          type: "submit",
          isLoading: isPending,
          children: t2("actions.create")
        }
      )
    ] }) }),
    (0, import_jsx_runtime.jsx)(RouteFocusModal.Body, { className: "flex flex-col items-center p-16", children: (0, import_jsx_runtime.jsxs)("div", { className: "flex w-full max-w-[720px] flex-col gap-y-8", children: [
      (0, import_jsx_runtime.jsxs)("div", { children: [
        (0, import_jsx_runtime.jsx)(Heading, { children: t2("collections.createCollection") }),
        (0, import_jsx_runtime.jsx)(Text, { size: "small", className: "text-ui-fg-subtle", children: t2("collections.createCollectionHint") })
      ] }),
      (0, import_jsx_runtime.jsxs)("div", { className: "grid grid-cols-1 gap-4 md:grid-cols-2", children: [
        (0, import_jsx_runtime.jsx)(
          Form.Field,
          {
            control: form.control,
            name: "title",
            render: ({ field }) => {
              return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("fields.title") }),
                (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(Input, { autoComplete: "off", ...field }) }),
                (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
              ] });
            }
          }
        ),
        (0, import_jsx_runtime.jsx)(
          Form.Field,
          {
            control: form.control,
            name: "handle",
            render: ({ field }) => {
              return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                (0, import_jsx_runtime.jsx)(
                  Form.Label,
                  {
                    optional: true,
                    tooltip: t2("collections.handleTooltip"),
                    children: t2("fields.handle")
                  }
                ),
                (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(HandleInput, { ...field }) }),
                (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
              ] });
            }
          }
        )
      ] })
    ] }) })
  ] }) });
};
var CollectionCreate = () => {
  return (0, import_jsx_runtime2.jsx)(RouteFocusModal, { children: (0, import_jsx_runtime2.jsx)(CreateCollectionForm, {}) });
};
export {
  CollectionCreate as Component
};
//# sourceMappingURL=collection-create-6MZU3JJO-XW5FZPKK.js.map
