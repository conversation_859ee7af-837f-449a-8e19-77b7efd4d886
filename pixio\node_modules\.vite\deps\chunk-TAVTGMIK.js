import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/chunk-F6ZOHZVB.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var VisuallyHidden = ({ children }) => {
  return (0, import_jsx_runtime.jsx)("span", { className: "sr-only", children });
};

export {
  VisuallyHidden
};
//# sourceMappingURL=chunk-TAVTGMIK.js.map
