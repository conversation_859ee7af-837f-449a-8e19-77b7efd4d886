{"version": 3, "sources": ["../../@medusajs/dashboard/dist/location-service-zone-manage-areas-2EFMSKBL.mjs"], "sourcesContent": ["import {\n  GeoZoneForm\n} from \"./chunk-6DMXHN2X.mjs\";\nimport \"./chunk-NOAFLTPV.mjs\";\nimport {\n  GEO_ZONE_STACKED_MODAL_ID\n} from \"./chunk-PYIO3TDQ.mjs\";\nimport \"./chunk-LQTHYS2Z.mjs\";\nimport \"./chunk-UE6PO4FK.mjs\";\nimport \"./chunk-YEDAFXMB.mjs\";\nimport \"./chunk-AOFGTNG6.mjs\";\nimport \"./chunk-EMIHDNB7.mjs\";\nimport \"./chunk-X5VECN6S.mjs\";\nimport \"./chunk-SXYXTC2L.mjs\";\nimport \"./chunk-M3VFKDXJ.mjs\";\nimport \"./chunk-C76H5USB.mjs\";\nimport \"./chunk-KSV3NQOT.mjs\";\nimport \"./chunk-IUCDCPJU.mjs\";\nimport {\n  KeyboundForm\n} from \"./chunk-6HTZNHPT.mjs\";\nimport {\n  RouteFocusModal,\n  StackedFocusModal,\n  useRouteModal\n} from \"./chunk-4TC5YS65.mjs\";\nimport {\n  countries\n} from \"./chunk-DG7J63J2.mjs\";\nimport \"./chunk-LPEUYMRK.mjs\";\nimport \"./chunk-NYXYHZE6.mjs\";\nimport \"./chunk-OBQI23QM.mjs\";\nimport {\n  useUpdateFulfillmentSetServiceZone\n} from \"./chunk-ENV6YVOM.mjs\";\nimport \"./chunk-GRT22PE5.mjs\";\nimport {\n  useStockLocation\n} from \"./chunk-32IQRUVY.mjs\";\nimport \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-RPUOO7AV.mjs\";\n\n// src/routes/locations/location-service-zone-manage-areas/location-service-zone-manage-areas.tsx\nimport { json, useParams } from \"react-router-dom\";\n\n// src/routes/locations/location-service-zone-manage-areas/components/edit-region-areas-form/edit-service-zone-areas-form.tsx\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { Button, Heading, toast } from \"@medusajs/ui\";\nimport { useForm } from \"react-hook-form\";\nimport { useTranslation } from \"react-i18next\";\nimport { z } from \"zod\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar EditeServiceZoneSchema = z.object({\n  countries: z.array(z.object({ iso_2: z.string().min(2), display_name: z.string() })).min(1)\n});\nfunction EditServiceZoneAreasForm({\n  fulfillmentSetId,\n  locationId,\n  zone\n}) {\n  const { t } = useTranslation();\n  const { handleSuccess } = useRouteModal();\n  const form = useForm({\n    defaultValues: {\n      countries: zone.geo_zones.map((z2) => {\n        const country = countries.find((c) => c.iso_2 === z2.country_code);\n        return {\n          iso_2: z2.country_code,\n          display_name: country?.display_name || z2.country_code.toUpperCase()\n        };\n      })\n    },\n    resolver: zodResolver(EditeServiceZoneSchema)\n  });\n  const { mutateAsync: editServiceZone, isPending: isLoading } = useUpdateFulfillmentSetServiceZone(fulfillmentSetId, zone.id);\n  const handleSubmit = form.handleSubmit(async (data) => {\n    await editServiceZone(\n      {\n        geo_zones: data.countries.map(({ iso_2 }) => ({\n          country_code: iso_2,\n          type: \"country\"\n        }))\n      },\n      {\n        onSuccess: () => {\n          toast.success(\n            t(\"stockLocations.serviceZones.manageAreas.successToast\", {\n              name: zone.name\n            })\n          );\n          handleSuccess(`/settings/locations/${locationId}`);\n        },\n        onError: (e) => {\n          toast.error(e.message);\n        }\n      }\n    );\n  });\n  return /* @__PURE__ */ jsx(RouteFocusModal.Form, { form, children: /* @__PURE__ */ jsxs(\n    KeyboundForm,\n    {\n      className: \"flex h-full flex-col overflow-hidden\",\n      onSubmit: handleSubmit,\n      children: [\n        /* @__PURE__ */ jsx(RouteFocusModal.Header, {}),\n        /* @__PURE__ */ jsx(RouteFocusModal.Body, { className: \"flex flex-1 flex-col overflow-auto\", children: /* @__PURE__ */ jsxs(StackedFocusModal, { id: GEO_ZONE_STACKED_MODAL_ID, children: [\n          /* @__PURE__ */ jsx(\"div\", { className: \"flex flex-col items-center p-16\", children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex w-full max-w-[720px] flex-col gap-y-8\", children: [\n            /* @__PURE__ */ jsx(Heading, { children: t(\"stockLocations.serviceZones.manageAreas.header\", {\n              name: zone.name\n            }) }),\n            /* @__PURE__ */ jsx(GeoZoneForm, { form })\n          ] }) }),\n          /* @__PURE__ */ jsx(GeoZoneForm.AreaDrawer, { form })\n        ] }) }),\n        /* @__PURE__ */ jsx(RouteFocusModal.Footer, { children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center justify-end gap-x-2\", children: [\n          /* @__PURE__ */ jsx(RouteFocusModal.Close, { asChild: true, children: /* @__PURE__ */ jsx(Button, { variant: \"secondary\", size: \"small\", children: t(\"actions.cancel\") }) }),\n          /* @__PURE__ */ jsx(Button, { type: \"submit\", size: \"small\", isLoading, children: t(\"actions.save\") })\n        ] }) })\n      ]\n    }\n  ) });\n}\n\n// src/routes/locations/location-service-zone-manage-areas/location-service-zone-manage-areas.tsx\nimport { jsx as jsx2 } from \"react/jsx-runtime\";\nvar LocationServiceZoneManageAreas = () => {\n  const { location_id, fset_id, zone_id } = useParams();\n  const { stock_location, isPending, isFetching, isError, error } = useStockLocation(location_id, {\n    fields: \"*fulfillment_sets.service_zones.geo_zones,fulfillment_sets.service_zones.name\"\n  });\n  const zone = stock_location?.fulfillment_sets?.find((f) => f.id === fset_id)?.service_zones.find((z2) => z2.id === zone_id);\n  if (!isPending && !isFetching && !zone) {\n    throw json(\n      { message: `Service zone with ID ${zone_id} was not found` },\n      404\n    );\n  }\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsx2(RouteFocusModal, { prev: `/settings/locations/${location_id}`, children: zone && /* @__PURE__ */ jsx2(\n    EditServiceZoneAreasForm,\n    {\n      zone,\n      fulfillmentSetId: fset_id,\n      locationId: location_id\n    }\n  ) });\n};\nexport {\n  LocationServiceZoneManageAreas as Component\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqDA,yBAA0B;AAyE1B,IAAAA,sBAA4B;AAxE5B,IAAI,yBAAyB,EAAE,OAAO;AAAA,EACpC,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,CAAC,GAAG,cAAc,EAAE,OAAO,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC;AAC5F,CAAC;AACD,SAAS,yBAAyB;AAAA,EAChC;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,QAAM,EAAE,GAAAC,GAAE,IAAI,eAAe;AAC7B,QAAM,EAAE,cAAc,IAAI,cAAc;AACxC,QAAM,OAAO,QAAQ;AAAA,IACnB,eAAe;AAAA,MACb,WAAW,KAAK,UAAU,IAAI,CAAC,OAAO;AACpC,cAAM,UAAU,UAAU,KAAK,CAAC,MAAM,EAAE,UAAU,GAAG,YAAY;AACjE,eAAO;AAAA,UACL,OAAO,GAAG;AAAA,UACV,eAAc,mCAAS,iBAAgB,GAAG,aAAa,YAAY;AAAA,QACrE;AAAA,MACF,CAAC;AAAA,IACH;AAAA,IACA,UAAU,EAAY,sBAAsB;AAAA,EAC9C,CAAC;AACD,QAAM,EAAE,aAAa,iBAAiB,WAAW,UAAU,IAAI,mCAAmC,kBAAkB,KAAK,EAAE;AAC3H,QAAM,eAAe,KAAK,aAAa,OAAO,SAAS;AACrD,UAAM;AAAA,MACJ;AAAA,QACE,WAAW,KAAK,UAAU,IAAI,CAAC,EAAE,MAAM,OAAO;AAAA,UAC5C,cAAc;AAAA,UACd,MAAM;AAAA,QACR,EAAE;AAAA,MACJ;AAAA,MACA;AAAA,QACE,WAAW,MAAM;AACf,gBAAM;AAAA,YACJA,GAAE,wDAAwD;AAAA,cACxD,MAAM,KAAK;AAAA,YACb,CAAC;AAAA,UACH;AACA,wBAAc,uBAAuB,UAAU,EAAE;AAAA,QACnD;AAAA,QACA,SAAS,CAAC,MAAM;AACd,gBAAM,MAAM,EAAE,OAAO;AAAA,QACvB;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AACD,aAAuB,wBAAI,gBAAgB,MAAM,EAAE,MAAM,cAA0B;AAAA,IACjF;AAAA,IACA;AAAA,MACE,WAAW;AAAA,MACX,UAAU;AAAA,MACV,UAAU;AAAA,YACQ,wBAAI,gBAAgB,QAAQ,CAAC,CAAC;AAAA,YAC9B,wBAAI,gBAAgB,MAAM,EAAE,WAAW,sCAAsC,cAA0B,yBAAK,mBAAmB,EAAE,IAAI,2BAA2B,UAAU;AAAA,cACxK,wBAAI,OAAO,EAAE,WAAW,mCAAmC,cAA0B,yBAAK,OAAO,EAAE,WAAW,8CAA8C,UAAU;AAAA,gBACpK,wBAAI,SAAS,EAAE,UAAUA,GAAE,kDAAkD;AAAA,cAC3F,MAAM,KAAK;AAAA,YACb,CAAC,EAAE,CAAC;AAAA,gBACY,wBAAI,aAAa,EAAE,KAAK,CAAC;AAAA,UAC3C,EAAE,CAAC,EAAE,CAAC;AAAA,cACU,wBAAI,YAAY,YAAY,EAAE,KAAK,CAAC;AAAA,QACtD,EAAE,CAAC,EAAE,CAAC;AAAA,YACU,wBAAI,gBAAgB,QAAQ,EAAE,cAA0B,yBAAK,OAAO,EAAE,WAAW,yCAAyC,UAAU;AAAA,cAClI,wBAAI,gBAAgB,OAAO,EAAE,SAAS,MAAM,cAA0B,wBAAI,QAAQ,EAAE,SAAS,aAAa,MAAM,SAAS,UAAUA,GAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;AAAA,cAC3J,wBAAI,QAAQ,EAAE,MAAM,UAAU,MAAM,SAAS,WAAW,UAAUA,GAAE,cAAc,EAAE,CAAC;AAAA,QACvG,EAAE,CAAC,EAAE,CAAC;AAAA,MACR;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;AAIA,IAAI,iCAAiC,MAAM;AA/H3C;AAgIE,QAAM,EAAE,aAAa,SAAS,QAAQ,IAAI,UAAU;AACpD,QAAM,EAAE,gBAAgB,WAAW,YAAY,SAAS,MAAM,IAAI,iBAAiB,aAAa;AAAA,IAC9F,QAAQ;AAAA,EACV,CAAC;AACD,QAAM,QAAO,4DAAgB,qBAAhB,mBAAkC,KAAK,CAAC,MAAM,EAAE,OAAO,aAAvD,mBAAiE,cAAc,KAAK,CAAC,OAAO,GAAG,OAAO;AACnH,MAAI,CAAC,aAAa,CAAC,cAAc,CAAC,MAAM;AACtC,UAAM;AAAA,MACJ,EAAE,SAAS,wBAAwB,OAAO,iBAAiB;AAAA,MAC3D;AAAA,IACF;AAAA,EACF;AACA,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,oBAAAC,KAAK,iBAAiB,EAAE,MAAM,uBAAuB,WAAW,IAAI,UAAU,YAAwB,oBAAAA;AAAA,IAC3H;AAAA,IACA;AAAA,MACE;AAAA,MACA,kBAAkB;AAAA,MAClB,YAAY;AAAA,IACd;AAAA,EACF,EAAE,CAAC;AACL;", "names": ["import_jsx_runtime", "t", "jsx2"]}