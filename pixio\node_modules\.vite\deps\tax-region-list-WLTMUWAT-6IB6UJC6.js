import {
  TaxRegionTable,
  useTaxRegionTable
} from "./chunk-473JZMR2.js";
import "./chunk-K7VPWFD7.js";
import "./chunk-KL72CHML.js";
import {
  useTaxRegionTableQuery
} from "./chunk-GEC36FCE.js";
import "./chunk-QF476XOZ.js";
import "./chunk-D2VV3NDE.js";
import "./chunk-QLJZR2JY.js";
import "./chunk-X3TOWPPJ.js";
import {
  SingleColumnPage
} from "./chunk-3LNIL4XX.js";
import "./chunk-32T72GVU.js";
import "./chunk-HPGXK5DQ.js";
import {
  useExtension
} from "./chunk-FTD3ZWHZ.js";
import "./chunk-YXT43UJF.js";
import "./chunk-CFRQOB2M.js";
import "./chunk-66DVUN72.js";
import "./chunk-3A5TVVNI.js";
import {
  useTaxRegions
} from "./chunk-EZ62MM7J.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import "./chunk-JFCIYOH4.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-5GF3RGIE.js";
import "./chunk-T7YBVUWZ.js";
import {
  Container,
  Heading,
  Text
} from "./chunk-YP2LLXWB.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-ZSQ362B4.js";
import {
  keepPreviousData
} from "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/tax-region-list-WLTMUWAT.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var PAGE_SIZE = 20;
var TaxRegionListView = () => {
  const { t } = useTranslation();
  const { searchParams, raw } = useTaxRegionTableQuery({
    pageSize: PAGE_SIZE
  });
  const { tax_regions, count, isPending, isError, error } = useTaxRegions(
    {
      ...searchParams,
      order: "country_code",
      parent_id: "null"
    },
    {
      placeholderData: keepPreviousData
    }
  );
  const { table } = useTaxRegionTable({
    count,
    data: tax_regions,
    pageSize: PAGE_SIZE
  });
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime.jsx)(Container, { className: "divide-y p-0", children: (0, import_jsx_runtime.jsxs)(
    TaxRegionTable,
    {
      action: {
        to: "create",
        label: t("actions.create")
      },
      isPending,
      queryObject: raw,
      table,
      count,
      children: [
        (0, import_jsx_runtime.jsx)(Heading, { children: t("taxes.domain") }),
        (0, import_jsx_runtime.jsx)(Text, { size: "small", className: "text-ui-fg-subtle text-pretty", children: t("taxRegions.list.hint") })
      ]
    }
  ) });
};
var TaxRegionsList = () => {
  const { getWidgets } = useExtension();
  return (0, import_jsx_runtime2.jsx)(
    SingleColumnPage,
    {
      widgets: {
        before: getWidgets("tax.list.before"),
        after: getWidgets("tax.list.after")
      },
      hasOutlet: true,
      children: (0, import_jsx_runtime2.jsx)(TaxRegionListView, {})
    }
  );
};
export {
  TaxRegionsList as Component
};
//# sourceMappingURL=tax-region-list-WLTMUWAT-6IB6UJC6.js.map
