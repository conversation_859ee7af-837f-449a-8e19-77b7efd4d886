import {
  DataTableSearch
} from "./chunk-VCBFQV64.js";
import {
  DataTableOrderBy
} from "./chunk-QLJZR2JY.js";
import {
  NoRecords,
  NoResults
} from "./chunk-X3TOWPPJ.js";
import {
  DataTableFilter
} from "./chunk-MX43XOWY.js";
import {
  TableSkeleton
} from "./chunk-YXT43UJF.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import {
  Link,
  useSearchParams
} from "./chunk-T7YBVUWZ.js";
import {
  CommandBar,
  Table,
  clx,
  flexRender,
  getCoreRowModel,
  getExpandedRowModel,
  getPaginationRowModel,
  useReactTable
} from "./chunk-YP2LLXWB.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/chunk-UE6PO4FK.mjs
var import_react = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_react2 = __toESM(require_react(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var import_react3 = __toESM(require_react(), 1);
var DataTableQuery = ({
  search,
  orderBy,
  filters,
  prefix
}) => {
  return (search || orderBy || filters || prefix) && (0, import_jsx_runtime.jsxs)("div", { className: "flex items-start justify-between gap-x-4 px-6 py-4", children: [
    (0, import_jsx_runtime.jsx)("div", { className: "w-full max-w-[60%]", children: filters && filters.length > 0 && (0, import_jsx_runtime.jsx)(DataTableFilter, { filters, prefix }) }),
    (0, import_jsx_runtime.jsxs)("div", { className: "flex shrink-0 items-center gap-x-2", children: [
      search && (0, import_jsx_runtime.jsx)(
        DataTableSearch,
        {
          prefix,
          autofocus: search === "autofocus"
        }
      ),
      orderBy && (0, import_jsx_runtime.jsx)(DataTableOrderBy, { keys: orderBy, prefix })
    ] })
  ] });
};
var DataTableRoot = ({
  table,
  columns,
  pagination,
  navigateTo,
  commands,
  count = 0,
  noResults = false,
  noHeader = false,
  layout = "fit"
}) => {
  const { t } = useTranslation();
  const [showStickyBorder, setShowStickyBorder] = (0, import_react2.useState)(false);
  const scrollableRef = (0, import_react2.useRef)(null);
  const hasSelect = columns.find((c) => c.id === "select");
  const hasActions = columns.find((c) => c.id === "actions");
  const hasCommandBar = commands && commands.length > 0;
  const rowSelection = table.getState().rowSelection;
  const { pageIndex, pageSize } = table.getState().pagination;
  const colCount = columns.length - (hasSelect ? 1 : 0) - (hasActions ? 1 : 0);
  const colWidth = 100 / colCount;
  const handleHorizontalScroll = (e) => {
    const scrollLeft = e.currentTarget.scrollLeft;
    if (scrollLeft > 0) {
      setShowStickyBorder(true);
    } else {
      setShowStickyBorder(false);
    }
  };
  const handleAction = async (action) => {
    await action(rowSelection).then(() => {
      table.resetRowSelection();
    });
  };
  (0, import_react2.useEffect)(() => {
    var _a;
    (_a = scrollableRef.current) == null ? void 0 : _a.scroll({ top: 0, left: 0 });
  }, [pageIndex]);
  return (0, import_jsx_runtime2.jsxs)(
    "div",
    {
      className: clx("flex w-full flex-col overflow-hidden", {
        "flex flex-1 flex-col": layout === "fill"
      }),
      children: [
        (0, import_jsx_runtime2.jsx)(
          "div",
          {
            ref: scrollableRef,
            onScroll: handleHorizontalScroll,
            className: clx("w-full", {
              "min-h-0 flex-grow overflow-auto": layout === "fill",
              "overflow-x-auto": layout === "fit"
            }),
            children: !noResults ? (0, import_jsx_runtime2.jsxs)(Table, { className: "relative w-full", children: [
              !noHeader && (0, import_jsx_runtime2.jsx)(Table.Header, { className: "border-t-0", children: table.getHeaderGroups().map((headerGroup) => {
                return (0, import_jsx_runtime2.jsx)(
                  Table.Row,
                  {
                    className: clx({
                      "relative border-b-0 [&_th:last-of-type]:w-[1%] [&_th:last-of-type]:whitespace-nowrap": hasActions,
                      "[&_th:first-of-type]:w-[1%] [&_th:first-of-type]:whitespace-nowrap": hasSelect
                    }),
                    children: headerGroup.headers.map((header, index) => {
                      const isActionHeader = header.id === "actions";
                      const isSelectHeader = header.id === "select";
                      const isSpecialHeader = isActionHeader || isSelectHeader;
                      const firstHeader = headerGroup.headers.findIndex(
                        (h) => h.id !== "select"
                      );
                      const isFirstHeader = firstHeader !== -1 ? header.id === headerGroup.headers[firstHeader].id : index === 0;
                      const isStickyHeader = isSelectHeader || isFirstHeader;
                      return (0, import_jsx_runtime2.jsx)(
                        Table.HeaderCell,
                        {
                          "data-table-header-id": header.id,
                          style: {
                            width: !isSpecialHeader ? `${colWidth}%` : void 0
                          },
                          className: clx({
                            "bg-ui-bg-subtle sticky left-0 after:absolute after:inset-y-0 after:right-0 after:h-full after:w-px after:bg-transparent after:content-['']": isStickyHeader,
                            "left-[68px]": isStickyHeader && hasSelect && !isSelectHeader,
                            "after:bg-ui-border-base": showStickyBorder && isStickyHeader && !isSpecialHeader
                          }),
                          children: flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )
                        },
                        header.id
                      );
                    })
                  },
                  headerGroup.id
                );
              }) }),
              (0, import_jsx_runtime2.jsx)(Table.Body, { className: "border-b-0", children: table.getRowModel().rows.map((row) => {
                const to = navigateTo ? navigateTo(row) : void 0;
                const isRowDisabled = hasSelect && !row.getCanSelect();
                const isOdd = row.depth % 2 !== 0;
                const cells = row.getVisibleCells();
                return (0, import_jsx_runtime2.jsx)(
                  Table.Row,
                  {
                    "data-selected": row.getIsSelected(),
                    className: clx(
                      "transition-fg group/row group relative [&_td:last-of-type]:w-[1%] [&_td:last-of-type]:whitespace-nowrap",
                      "has-[[data-row-link]:focus-visible]:bg-ui-bg-base-hover",
                      {
                        "bg-ui-bg-subtle hover:bg-ui-bg-subtle-hover": isOdd,
                        "cursor-pointer": !!to,
                        "bg-ui-bg-highlight hover:bg-ui-bg-highlight-hover": row.getIsSelected(),
                        "!bg-ui-bg-disabled !hover:bg-ui-bg-disabled": isRowDisabled
                      }
                    ),
                    children: cells.map((cell, index) => {
                      const visibleCells = row.getVisibleCells();
                      const isSelectCell = cell.column.id === "select";
                      const firstCell = visibleCells.findIndex(
                        (h) => h.column.id !== "select"
                      );
                      const isFirstCell = firstCell !== -1 ? cell.column.id === visibleCells[firstCell].column.id : index === 0;
                      const isStickyCell = isSelectCell || isFirstCell;
                      const depthOffset = row.depth > 0 && isFirstCell ? row.depth * 14 + 24 : void 0;
                      const hasLeftOffset = isStickyCell && hasSelect && !isSelectCell;
                      const Inner = flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      );
                      const isTabableLink = isFirstCell && !!to;
                      const shouldRenderAsLink = !!to && !isSelectCell;
                      return (0, import_jsx_runtime2.jsx)(
                        Table.Cell,
                        {
                          className: clx({
                            "!pl-0 !pr-0": shouldRenderAsLink,
                            "bg-ui-bg-base group-data-[selected=true]/row:bg-ui-bg-highlight group-data-[selected=true]/row:group-hover/row:bg-ui-bg-highlight-hover group-hover/row:bg-ui-bg-base-hover transition-fg group-has-[[data-row-link]:focus-visible]:bg-ui-bg-base-hover sticky left-0 after:absolute after:inset-y-0 after:right-0 after:h-full after:w-px after:bg-transparent after:content-['']": isStickyCell,
                            "bg-ui-bg-subtle group-hover/row:bg-ui-bg-subtle-hover": isOdd && isStickyCell,
                            "left-[68px]": hasLeftOffset,
                            "after:bg-ui-border-base": showStickyBorder && isStickyCell && !isSelectCell,
                            "!bg-ui-bg-disabled !hover:bg-ui-bg-disabled": isRowDisabled
                          }),
                          style: {
                            paddingLeft: depthOffset ? `${depthOffset}px` : void 0
                          },
                          children: shouldRenderAsLink ? (0, import_jsx_runtime2.jsx)(
                            Link,
                            {
                              to,
                              className: "size-full outline-none",
                              "data-row-link": true,
                              tabIndex: isTabableLink ? 0 : -1,
                              children: (0, import_jsx_runtime2.jsx)(
                                "div",
                                {
                                  className: clx(
                                    "flex size-full items-center pr-6",
                                    {
                                      "pl-6": isTabableLink && !hasLeftOffset
                                    }
                                  ),
                                  children: Inner
                                }
                              )
                            }
                          ) : Inner
                        },
                        cell.id
                      );
                    })
                  },
                  row.id
                );
              }) })
            ] }) : (0, import_jsx_runtime2.jsx)("div", { className: clx({ "border-b": layout === "fit" }), children: (0, import_jsx_runtime2.jsx)(NoResults, {}) })
          }
        ),
        pagination && (0, import_jsx_runtime2.jsx)("div", { className: clx({ "border-t": layout === "fill" }), children: (0, import_jsx_runtime2.jsx)(
          Pagination,
          {
            canNextPage: table.getCanNextPage(),
            canPreviousPage: table.getCanPreviousPage(),
            nextPage: table.nextPage,
            previousPage: table.previousPage,
            count,
            pageIndex,
            pageCount: table.getPageCount(),
            pageSize
          }
        ) }),
        hasCommandBar && (0, import_jsx_runtime2.jsx)(CommandBar, { open: !!Object.keys(rowSelection).length, children: (0, import_jsx_runtime2.jsxs)(CommandBar.Bar, { children: [
          (0, import_jsx_runtime2.jsx)(CommandBar.Value, { children: t("general.countSelected", {
            count: Object.keys(rowSelection).length
          }) }),
          (0, import_jsx_runtime2.jsx)(CommandBar.Seperator, {}),
          commands == null ? void 0 : commands.map((command, index) => {
            return (0, import_jsx_runtime2.jsxs)(import_react2.Fragment, { children: [
              (0, import_jsx_runtime2.jsx)(
                CommandBar.Command,
                {
                  label: command.label,
                  shortcut: command.shortcut,
                  action: () => handleAction(command.action)
                }
              ),
              index < commands.length - 1 && (0, import_jsx_runtime2.jsx)(CommandBar.Seperator, {})
            ] }, index);
          })
        ] }) })
      ]
    }
  );
};
var Pagination = (props) => {
  const { t } = useTranslation();
  const translations = {
    of: t("general.of"),
    results: t("general.results"),
    pages: t("general.pages"),
    prev: t("general.prev"),
    next: t("general.next")
  };
  return (0, import_jsx_runtime2.jsx)(
    Table.Pagination,
    {
      className: "flex-shrink-0",
      ...props,
      translations
    }
  );
};
var MemoizedDataTableQuery = (0, import_react.memo)(DataTableQuery);
var _DataTable = ({
  table,
  columns,
  pagination,
  navigateTo,
  commands,
  count = 0,
  search = false,
  orderBy,
  filters,
  prefix,
  queryObject = {},
  pageSize,
  isLoading = false,
  noHeader = false,
  layout = "fit",
  noRecords: noRecordsProps = {}
}) => {
  if (isLoading) {
    return (0, import_jsx_runtime3.jsx)(
      TableSkeleton,
      {
        layout,
        rowCount: pageSize,
        search: !!search,
        filters: !!(filters == null ? void 0 : filters.length),
        orderBy: !!(orderBy == null ? void 0 : orderBy.length),
        pagination: !!pagination
      }
    );
  }
  const noQuery = Object.values(queryObject).filter((v) => Boolean(v)).length === 0;
  const noResults = !isLoading && count === 0 && !noQuery;
  const noRecords = !isLoading && count === 0 && noQuery;
  if (noRecords) {
    return (0, import_jsx_runtime3.jsx)(
      NoRecords,
      {
        className: clx({
          "flex h-full flex-col overflow-hidden": layout === "fill"
        }),
        ...noRecordsProps
      }
    );
  }
  return (0, import_jsx_runtime3.jsxs)(
    "div",
    {
      className: clx("divide-y", {
        "flex h-full flex-col overflow-hidden": layout === "fill"
      }),
      children: [
        (0, import_jsx_runtime3.jsx)(
          MemoizedDataTableQuery,
          {
            search,
            orderBy,
            filters,
            prefix
          }
        ),
        (0, import_jsx_runtime3.jsx)(
          DataTableRoot,
          {
            table,
            count,
            columns,
            pagination: true,
            navigateTo,
            commands,
            noResults,
            noHeader,
            layout
          }
        )
      ]
    }
  );
};
var useDataTable = ({
  data = [],
  columns,
  count = 0,
  pageSize: _pageSize = 20,
  enablePagination = true,
  enableRowSelection = false,
  enableExpandableRows = false,
  rowSelection: _rowSelection,
  getSubRows,
  getRowId,
  meta,
  prefix
}) => {
  const [searchParams, setSearchParams] = useSearchParams();
  const offsetKey = `${prefix ? `${prefix}_` : ""}offset`;
  const offset = searchParams.get(offsetKey);
  const [{ pageIndex, pageSize }, setPagination] = (0, import_react3.useState)({
    pageIndex: offset ? Math.ceil(Number(offset) / _pageSize) : 0,
    pageSize: _pageSize
  });
  const pagination = (0, import_react3.useMemo)(
    () => ({
      pageIndex,
      pageSize
    }),
    [pageIndex, pageSize]
  );
  const [localRowSelection, setLocalRowSelection] = (0, import_react3.useState)({});
  const rowSelection = (_rowSelection == null ? void 0 : _rowSelection.state) ?? localRowSelection;
  const setRowSelection = (_rowSelection == null ? void 0 : _rowSelection.updater) ?? setLocalRowSelection;
  (0, import_react3.useEffect)(() => {
    if (!enablePagination) {
      return;
    }
    const index = offset ? Math.ceil(Number(offset) / _pageSize) : 0;
    if (index === pageIndex) {
      return;
    }
    setPagination((prev) => ({
      ...prev,
      pageIndex: index
    }));
  }, [offset, enablePagination, _pageSize, pageIndex]);
  const onPaginationChange = (updater) => {
    const state = updater(pagination);
    const { pageIndex: pageIndex2, pageSize: pageSize2 } = state;
    setSearchParams((prev) => {
      if (!pageIndex2) {
        prev.delete(offsetKey);
        return prev;
      }
      const newSearch = new URLSearchParams(prev);
      newSearch.set(offsetKey, String(pageIndex2 * pageSize2));
      return newSearch;
    });
    setPagination(state);
    return state;
  };
  const table = useReactTable({
    data,
    columns,
    state: {
      rowSelection,
      // We always pass a selection state to the table even if it's not enabled
      pagination: enablePagination ? pagination : void 0
    },
    pageCount: Math.ceil((count ?? 0) / pageSize),
    enableRowSelection,
    getRowId,
    getSubRows,
    onRowSelectionChange: enableRowSelection ? setRowSelection : void 0,
    onPaginationChange: enablePagination ? onPaginationChange : void 0,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: enablePagination ? getPaginationRowModel() : void 0,
    getExpandedRowModel: enableExpandableRows ? getExpandedRowModel() : void 0,
    manualPagination: enablePagination ? true : void 0,
    meta
  });
  return { table };
};

export {
  _DataTable,
  useDataTable
};
//# sourceMappingURL=chunk-VCX3BVQR.js.map
