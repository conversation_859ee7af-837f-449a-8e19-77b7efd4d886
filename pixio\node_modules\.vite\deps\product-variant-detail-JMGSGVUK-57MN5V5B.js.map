{"version": 3, "sources": ["../../@medusajs/dashboard/dist/product-variant-detail-JMGSGVUK.mjs"], "sourcesContent": ["import {\n  LinkButton\n} from \"./chunk-6WKBBTKM.mjs\";\nimport {\n  getLocaleAmount\n} from \"./chunk-PDWBYQOW.mjs\";\nimport {\n  VARIANT_DETAIL_FIELDS\n} from \"./chunk-EUTK2A3J.mjs\";\nimport \"./chunk-LQTHYS2Z.mjs\";\nimport {\n  PlaceholderCell\n} from \"./chunk-P3UUX2T6.mjs\";\nimport {\n  _DataTable,\n  useDataTable\n} from \"./chunk-UE6PO4FK.mjs\";\nimport \"./chunk-YEDAFXMB.mjs\";\nimport \"./chunk-AOFGTNG6.mjs\";\nimport {\n  NoRecords\n} from \"./chunk-EMIHDNB7.mjs\";\nimport \"./chunk-MWVM4TYO.mjs\";\nimport \"./chunk-SXYXTC2L.mjs\";\nimport \"./chunk-M3VFKDXJ.mjs\";\nimport \"./chunk-YOYOJU5D.mjs\";\nimport {\n  SectionRow\n} from \"./chunk-LFLGEXIG.mjs\";\nimport {\n  TwoColumnPage\n} from \"./chunk-2RQLKDBF.mjs\";\nimport \"./chunk-KSV3NQOT.mjs\";\nimport {\n  useExtension\n} from \"./chunk-C5P5PL3E.mjs\";\nimport {\n  TwoColumnPageSkeleton\n} from \"./chunk-LPEUYMRK.mjs\";\nimport {\n  ActionMenu\n} from \"./chunk-3NJTXRIY.mjs\";\nimport \"./chunk-OC7BQLYI.mjs\";\nimport \"./chunk-NYXYHZE6.mjs\";\nimport \"./chunk-Z5UDPQIH.mjs\";\nimport \"./chunk-KOSCMAIC.mjs\";\nimport \"./chunk-X6DSNTTX.mjs\";\nimport \"./chunk-I6E6CALJ.mjs\";\nimport \"./chunk-B4GODIOW.mjs\";\nimport \"./chunk-F6IJV2I2.mjs\";\nimport \"./chunk-QTCZFYFH.mjs\";\nimport \"./chunk-ENV6YVOM.mjs\";\nimport \"./chunk-PIR2H25N.mjs\";\nimport \"./chunk-RLY2SL5E.mjs\";\nimport \"./chunk-C5LYZZZ5.mjs\";\nimport \"./chunk-2ZKVRTBW.mjs\";\nimport \"./chunk-FO3VP56P.mjs\";\nimport \"./chunk-YS65UGPC.mjs\";\nimport \"./chunk-F6PXCY3N.mjs\";\nimport \"./chunk-3OHH43G6.mjs\";\nimport \"./chunk-G2H6MAK7.mjs\";\nimport \"./chunk-GRT22PE5.mjs\";\nimport \"./chunk-32IQRUVY.mjs\";\nimport \"./chunk-FNYASI54.mjs\";\nimport \"./chunk-FVC7M755.mjs\";\nimport \"./chunk-ZJ3OFMHB.mjs\";\nimport \"./chunk-PNU5HPGY.mjs\";\nimport \"./chunk-V2LANK5S.mjs\";\nimport \"./chunk-QZ6PT4QV.mjs\";\nimport \"./chunk-QL4XKIVL.mjs\";\nimport {\n  useDeleteVariant,\n  useProductVariant,\n  variantsQueryKeys\n} from \"./chunk-6I62UDJA.mjs\";\nimport {\n  queryClient\n} from \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport {\n  sdk\n} from \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-RPUOO7AV.mjs\";\n\n// src/routes/product-variants/product-variant-detail/breadcrumb.tsx\nimport { jsx } from \"react/jsx-runtime\";\nvar ProductVariantDetailBreadcrumb = (props) => {\n  const { id, variant_id } = props.params || {};\n  const { variant } = useProductVariant(\n    id,\n    variant_id,\n    {\n      fields: VARIANT_DETAIL_FIELDS\n    },\n    {\n      initialData: props.data,\n      enabled: Boolean(id) && Boolean(variant_id)\n    }\n  );\n  if (!variant) {\n    return null;\n  }\n  return /* @__PURE__ */ jsx(\"span\", { children: variant.title });\n};\n\n// src/routes/product-variants/product-variant-detail/loader.ts\nvar variantDetailQuery = (productId, variantId) => ({\n  queryKey: variantsQueryKeys.detail(variantId, {\n    fields: VARIANT_DETAIL_FIELDS\n  }),\n  queryFn: async () => sdk.admin.product.retrieveVariant(productId, variantId, {\n    fields: VARIANT_DETAIL_FIELDS\n  })\n});\nvar variantLoader = async ({ params }) => {\n  const productId = params.id;\n  const variantId = params.variant_id;\n  const query = variantDetailQuery(productId, variantId);\n  return queryClient.ensureQueryData(query);\n};\n\n// src/routes/product-variants/product-variant-detail/product-variant-detail.tsx\nimport { useLoaderData, useParams } from \"react-router-dom\";\n\n// src/routes/product-variants/product-variant-detail/components/variant-general-section/variant-general-section.tsx\nimport { Component, PencilSquare, Trash } from \"@medusajs/icons\";\nimport { Badge, Container, Heading, usePrompt } from \"@medusajs/ui\";\nimport { useTranslation } from \"react-i18next\";\nimport { useNavigate } from \"react-router-dom\";\nimport { jsx as jsx2, jsxs } from \"react/jsx-runtime\";\nfunction VariantGeneralSection({ variant }) {\n  const { t } = useTranslation();\n  const prompt = usePrompt();\n  const navigate = useNavigate();\n  const hasInventoryKit = variant.inventory?.length > 1;\n  const { mutateAsync } = useDeleteVariant(variant.product_id, variant.id);\n  const handleDelete = async () => {\n    const res = await prompt({\n      title: t(\"general.areYouSure\"),\n      description: t(\"products.variant.deleteWarning\", {\n        title: variant.title\n      }),\n      confirmText: t(\"actions.delete\"),\n      cancelText: t(\"actions.cancel\")\n    });\n    if (!res) {\n      return;\n    }\n    await mutateAsync(void 0, {\n      onSuccess: () => {\n        navigate(\"..\", { replace: true });\n      }\n    });\n  };\n  return /* @__PURE__ */ jsxs(Container, { className: \"divide-y p-0\", children: [\n    /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center justify-between px-6 py-4\", children: [\n      /* @__PURE__ */ jsxs(\"div\", { children: [\n        /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center gap-2\", children: [\n          /* @__PURE__ */ jsx2(Heading, { children: variant.title }),\n          hasInventoryKit && /* @__PURE__ */ jsx2(\"span\", { className: \"text-ui-fg-muted font-normal\", children: /* @__PURE__ */ jsx2(Component, {}) })\n        ] }),\n        /* @__PURE__ */ jsx2(\"span\", { className: \"text-ui-fg-subtle txt-small mt-2\", children: t(\"labels.productVariant\") })\n      ] }),\n      /* @__PURE__ */ jsx2(\"div\", { className: \"flex items-center gap-x-4\", children: /* @__PURE__ */ jsx2(\n        ActionMenu,\n        {\n          groups: [\n            {\n              actions: [\n                {\n                  label: t(\"actions.edit\"),\n                  to: \"edit\",\n                  icon: /* @__PURE__ */ jsx2(PencilSquare, {})\n                }\n              ]\n            },\n            {\n              actions: [\n                {\n                  label: t(\"actions.delete\"),\n                  onClick: handleDelete,\n                  icon: /* @__PURE__ */ jsx2(Trash, {})\n                }\n              ]\n            }\n          ]\n        }\n      ) })\n    ] }),\n    /* @__PURE__ */ jsx2(SectionRow, { title: t(\"fields.sku\"), value: variant.sku }),\n    variant.options?.map((o) => /* @__PURE__ */ jsx2(\n      SectionRow,\n      {\n        title: o.option?.title,\n        value: /* @__PURE__ */ jsx2(Badge, { size: \"2xsmall\", children: o.value })\n      },\n      o.id\n    ))\n  ] });\n}\n\n// src/routes/product-variants/product-variant-detail/components/variant-inventory-section/variant-inventory-section.tsx\nimport { useTranslation as useTranslation4 } from \"react-i18next\";\nimport { Buildings as Buildings2, Component as Component2 } from \"@medusajs/icons\";\nimport { Container as Container2, Heading as Heading2 } from \"@medusajs/ui\";\n\n// src/routes/product-variants/product-variant-detail/components/variant-inventory-section/inventory-actions.tsx\nimport { useTranslation as useTranslation2 } from \"react-i18next\";\nimport { Buildings } from \"@medusajs/icons\";\nimport { jsx as jsx3 } from \"react/jsx-runtime\";\nvar InventoryActions = ({ item }) => {\n  const { t } = useTranslation2();\n  return /* @__PURE__ */ jsx3(\n    ActionMenu,\n    {\n      groups: [\n        {\n          actions: [\n            {\n              icon: /* @__PURE__ */ jsx3(Buildings, {}),\n              label: t(\"products.variant.inventory.navigateToItem\"),\n              to: `/inventory/${item.id}`\n            }\n          ]\n        }\n      ]\n    }\n  );\n};\n\n// src/routes/product-variants/product-variant-detail/components/variant-inventory-section/use-inventory-table-columns.tsx\nimport { createColumnHelper } from \"@tanstack/react-table\";\nimport { useMemo } from \"react\";\nimport { useTranslation as useTranslation3 } from \"react-i18next\";\nimport { jsx as jsx4 } from \"react/jsx-runtime\";\nvar columnHelper = createColumnHelper();\nvar useInventoryTableColumns = () => {\n  const { t } = useTranslation3();\n  return useMemo(\n    () => [\n      columnHelper.accessor(\"title\", {\n        header: t(\"fields.title\"),\n        cell: ({ getValue }) => {\n          const title = getValue();\n          if (!title) {\n            return /* @__PURE__ */ jsx4(PlaceholderCell, {});\n          }\n          return /* @__PURE__ */ jsx4(\"div\", { className: \"flex size-full items-center overflow-hidden\", children: /* @__PURE__ */ jsx4(\"span\", { className: \"truncate\", children: title }) });\n        }\n      }),\n      columnHelper.accessor(\"sku\", {\n        header: t(\"fields.sku\"),\n        cell: ({ getValue }) => {\n          const sku = getValue();\n          if (!sku) {\n            return /* @__PURE__ */ jsx4(PlaceholderCell, {});\n          }\n          return /* @__PURE__ */ jsx4(\"div\", { className: \"flex size-full items-center overflow-hidden\", children: /* @__PURE__ */ jsx4(\"span\", { className: \"truncate\", children: sku }) });\n        }\n      }),\n      columnHelper.accessor(\"required_quantity\", {\n        header: t(\"fields.requiredQuantity\"),\n        cell: ({ getValue }) => {\n          const quantity = getValue();\n          if (Number.isNaN(quantity)) {\n            return /* @__PURE__ */ jsx4(PlaceholderCell, {});\n          }\n          return /* @__PURE__ */ jsx4(\"div\", { className: \"flex size-full items-center overflow-hidden\", children: /* @__PURE__ */ jsx4(\"span\", { className: \"truncate\", children: quantity }) });\n        }\n      }),\n      columnHelper.display({\n        id: \"inventory_quantity\",\n        header: t(\"fields.inventory\"),\n        cell: ({ getValue, row: { original: inventory } }) => {\n          if (!inventory.location_levels?.length) {\n            return /* @__PURE__ */ jsx4(PlaceholderCell, {});\n          }\n          let quantity = 0;\n          let locations = 0;\n          inventory.location_levels.forEach((level) => {\n            quantity += level.available_quantity;\n            locations += 1;\n          });\n          return /* @__PURE__ */ jsx4(\"div\", { className: \"flex size-full items-center overflow-hidden\", children: /* @__PURE__ */ jsx4(\"span\", { className: \"truncate\", children: t(\"products.variant.tableItem\", {\n            availableCount: quantity,\n            locationCount: locations,\n            count: locations\n          }) }) });\n        }\n      }),\n      columnHelper.display({\n        id: \"actions\",\n        cell: ({ row }) => /* @__PURE__ */ jsx4(InventoryActions, { item: row.original })\n      })\n    ],\n    [t]\n  );\n};\n\n// src/routes/product-variants/product-variant-detail/components/variant-inventory-section/variant-inventory-section.tsx\nimport { jsx as jsx5, jsxs as jsxs2 } from \"react/jsx-runtime\";\nvar PAGE_SIZE = 20;\nfunction VariantInventorySection({\n  inventoryItems\n}) {\n  const { t } = useTranslation4();\n  const columns = useInventoryTableColumns();\n  const { table } = useDataTable({\n    data: inventoryItems ?? [],\n    columns,\n    count: inventoryItems.length,\n    enablePagination: true,\n    getRowId: (row) => row.id,\n    pageSize: PAGE_SIZE\n  });\n  const hasKit = inventoryItems.length > 1;\n  return /* @__PURE__ */ jsxs2(Container2, { className: \"divide-y p-0\", children: [\n    /* @__PURE__ */ jsxs2(\"div\", { className: \"flex items-center justify-between px-6 py-4\", children: [\n      /* @__PURE__ */ jsx5(\"div\", { className: \"flex items-center gap-2\", children: /* @__PURE__ */ jsx5(Heading2, { level: \"h2\", children: t(\"fields.inventoryItems\") }) }),\n      /* @__PURE__ */ jsx5(\"div\", { className: \"flex items-center gap-x-4\", children: /* @__PURE__ */ jsx5(\n        ActionMenu,\n        {\n          groups: [\n            {\n              actions: [\n                {\n                  label: t(\n                    hasKit ? \"products.variant.inventory.manageKit\" : \"products.variant.inventory.manageItems\"\n                  ),\n                  to: \"manage-items\",\n                  icon: hasKit ? /* @__PURE__ */ jsx5(Component2, {}) : /* @__PURE__ */ jsx5(Buildings2, {})\n                }\n              ]\n            }\n          ]\n        }\n      ) })\n    ] }),\n    /* @__PURE__ */ jsx5(\n      _DataTable,\n      {\n        table,\n        columns,\n        pageSize: PAGE_SIZE,\n        count: inventoryItems.length,\n        navigateTo: (row) => `/inventory/${row.id}`\n      }\n    )\n  ] });\n}\nfunction InventorySectionPlaceholder() {\n  const { t } = useTranslation4();\n  return /* @__PURE__ */ jsx5(Container2, { className: \"divide-y p-0\", children: /* @__PURE__ */ jsxs2(\"div\", { className: \"flex items-center justify-between px-6 py-4\", children: [\n    /* @__PURE__ */ jsxs2(\"div\", { className: \"flex flex-col gap-1\", children: [\n      /* @__PURE__ */ jsx5(Heading2, { level: \"h2\", children: t(\"fields.inventoryItems\") }),\n      /* @__PURE__ */ jsx5(\"span\", { className: \"txt-small text-ui-fg-subtle\", children: t(\"products.variant.inventory.notManagedDesc\") })\n    ] }),\n    /* @__PURE__ */ jsx5(\"div\", { className: \"flex items-center gap-x-4\", children: /* @__PURE__ */ jsx5(LinkButton, { to: \"edit\", children: t(\"products.variant.edit.header\") }) })\n  ] }) });\n}\n\n// src/routes/product-variants/product-variant-detail/components/variant-prices-section/variant-prices-section.tsx\nimport { useState } from \"react\";\nimport { useTranslation as useTranslation5 } from \"react-i18next\";\nimport { CurrencyDollar } from \"@medusajs/icons\";\nimport { Button, Container as Container3, Heading as Heading3 } from \"@medusajs/ui\";\nimport { jsx as jsx6, jsxs as jsxs3 } from \"react/jsx-runtime\";\nfunction VariantPricesSection({ variant }) {\n  const { t } = useTranslation5();\n  const prices = variant.prices?.filter((p) => !Object.keys(p.rules || {}).length).sort((p1, p2) => p1.currency_code?.localeCompare(p2.currency_code));\n  const hasPrices = !!prices?.length;\n  const [pageSize, setPageSize] = useState(3);\n  const displayPrices = prices?.slice(0, pageSize);\n  const onShowMore = () => {\n    setPageSize(pageSize + 3);\n  };\n  return /* @__PURE__ */ jsxs3(Container3, { className: \"flex flex-col divide-y p-0\", children: [\n    /* @__PURE__ */ jsxs3(\"div\", { className: \"flex items-center justify-between px-6 py-4\", children: [\n      /* @__PURE__ */ jsx6(Heading3, { level: \"h2\", children: t(\"labels.prices\") }),\n      /* @__PURE__ */ jsx6(\n        ActionMenu,\n        {\n          groups: [\n            {\n              actions: [\n                {\n                  label: t(\"actions.edit\"),\n                  to: `/products/${variant.product_id}/variants/${variant.id}/prices`,\n                  icon: /* @__PURE__ */ jsx6(CurrencyDollar, {})\n                }\n              ]\n            }\n          ]\n        }\n      )\n    ] }),\n    !hasPrices && /* @__PURE__ */ jsx6(NoRecords, { className: \"h-60\" }),\n    displayPrices?.map((price) => {\n      return /* @__PURE__ */ jsxs3(\n        \"div\",\n        {\n          className: \"txt-small text-ui-fg-subtle flex justify-between px-6 py-4\",\n          children: [\n            /* @__PURE__ */ jsx6(\"span\", { className: \"font-medium\", children: price.currency_code.toUpperCase() }),\n            /* @__PURE__ */ jsx6(\"span\", { children: getLocaleAmount(price.amount, price.currency_code) })\n          ]\n        },\n        price.id\n      );\n    }),\n    hasPrices && /* @__PURE__ */ jsxs3(\"div\", { className: \"txt-small text-ui-fg-subtle flex items-center justify-between px-6 py-4\", children: [\n      /* @__PURE__ */ jsx6(\"span\", { className: \"font-medium\", children: t(\"products.variant.pricesPagination\", {\n        total: prices.length,\n        current: Math.min(pageSize, prices.length)\n      }) }),\n      /* @__PURE__ */ jsx6(\n        Button,\n        {\n          onClick: onShowMore,\n          disabled: pageSize >= prices.length,\n          className: \"-mr-3 text-blue-500\",\n          variant: \"transparent\",\n          children: t(\"actions.showMore\")\n        }\n      )\n    ] })\n  ] });\n}\n\n// src/routes/product-variants/product-variant-detail/product-variant-detail.tsx\nimport { jsx as jsx7, jsxs as jsxs4 } from \"react/jsx-runtime\";\nvar ProductVariantDetail = () => {\n  const initialData = useLoaderData();\n  const { id, variant_id } = useParams();\n  const { variant, isLoading, isError, error } = useProductVariant(\n    id,\n    variant_id,\n    { fields: VARIANT_DETAIL_FIELDS },\n    {\n      initialData\n    }\n  );\n  const { getWidgets } = useExtension();\n  if (isLoading || !variant) {\n    return /* @__PURE__ */ jsx7(\n      TwoColumnPageSkeleton,\n      {\n        mainSections: 2,\n        sidebarSections: 1,\n        showJSON: true,\n        showMetadata: true\n      }\n    );\n  }\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsxs4(\n    TwoColumnPage,\n    {\n      data: variant,\n      hasOutlet: true,\n      showJSON: true,\n      showMetadata: true,\n      widgets: {\n        after: getWidgets(\"product_variant.details.after\"),\n        before: getWidgets(\"product_variant.details.before\"),\n        sideAfter: getWidgets(\"product_variant.details.side.after\"),\n        sideBefore: getWidgets(\"product_variant.details.side.before\")\n      },\n      children: [\n        /* @__PURE__ */ jsxs4(TwoColumnPage.Main, { children: [\n          /* @__PURE__ */ jsx7(VariantGeneralSection, { variant }),\n          !variant.manage_inventory ? /* @__PURE__ */ jsx7(InventorySectionPlaceholder, {}) : /* @__PURE__ */ jsx7(\n            VariantInventorySection,\n            {\n              inventoryItems: variant.inventory_items.map((i) => {\n                return {\n                  ...i.inventory,\n                  required_quantity: i.required_quantity,\n                  variant\n                };\n              })\n            }\n          )\n        ] }),\n        /* @__PURE__ */ jsx7(TwoColumnPage.Sidebar, { children: /* @__PURE__ */ jsx7(VariantPricesSection, { variant }) })\n      ]\n    }\n  );\n};\nexport {\n  ProductVariantDetailBreadcrumb as Breadcrumb,\n  ProductVariantDetail as Component,\n  variantLoader as loader\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqFA,yBAAoB;AA4CpB,IAAAA,sBAAkC;AAgFlC,IAAAC,sBAA4B;AAuB5B,mBAAwB;AAExB,IAAAC,sBAA4B;AAkE5B,IAAAA,sBAA2C;AA8D3C,IAAAC,gBAAyB;AAIzB,IAAAC,sBAA2C;AAgE3C,IAAAA,sBAA2C;AAxV3C,IAAI,iCAAiC,CAAC,UAAU;AAC9C,QAAM,EAAE,IAAI,WAAW,IAAI,MAAM,UAAU,CAAC;AAC5C,QAAM,EAAE,QAAQ,IAAI;AAAA,IAClB;AAAA,IACA;AAAA,IACA;AAAA,MACE,QAAQ;AAAA,IACV;AAAA,IACA;AAAA,MACE,aAAa,MAAM;AAAA,MACnB,SAAS,QAAQ,EAAE,KAAK,QAAQ,UAAU;AAAA,IAC5C;AAAA,EACF;AACA,MAAI,CAAC,SAAS;AACZ,WAAO;AAAA,EACT;AACA,aAAuB,wBAAI,QAAQ,EAAE,UAAU,QAAQ,MAAM,CAAC;AAChE;AAGA,IAAI,qBAAqB,CAAC,WAAW,eAAe;AAAA,EAClD,UAAU,kBAAkB,OAAO,WAAW;AAAA,IAC5C,QAAQ;AAAA,EACV,CAAC;AAAA,EACD,SAAS,YAAY,IAAI,MAAM,QAAQ,gBAAgB,WAAW,WAAW;AAAA,IAC3E,QAAQ;AAAA,EACV,CAAC;AACH;AACA,IAAI,gBAAgB,OAAO,EAAE,OAAO,MAAM;AACxC,QAAM,YAAY,OAAO;AACzB,QAAM,YAAY,OAAO;AACzB,QAAM,QAAQ,mBAAmB,WAAW,SAAS;AACrD,SAAO,YAAY,gBAAgB,KAAK;AAC1C;AAWA,SAAS,sBAAsB,EAAE,QAAQ,GAAG;AAlI5C;AAmIE,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,QAAM,SAAS,UAAU;AACzB,QAAM,WAAW,YAAY;AAC7B,QAAM,oBAAkB,aAAQ,cAAR,mBAAmB,UAAS;AACpD,QAAM,EAAE,YAAY,IAAI,iBAAiB,QAAQ,YAAY,QAAQ,EAAE;AACvE,QAAM,eAAe,YAAY;AAC/B,UAAM,MAAM,MAAM,OAAO;AAAA,MACvB,OAAO,EAAE,oBAAoB;AAAA,MAC7B,aAAa,EAAE,kCAAkC;AAAA,QAC/C,OAAO,QAAQ;AAAA,MACjB,CAAC;AAAA,MACD,aAAa,EAAE,gBAAgB;AAAA,MAC/B,YAAY,EAAE,gBAAgB;AAAA,IAChC,CAAC;AACD,QAAI,CAAC,KAAK;AACR;AAAA,IACF;AACA,UAAM,YAAY,QAAQ;AAAA,MACxB,WAAW,MAAM;AACf,iBAAS,MAAM,EAAE,SAAS,KAAK,CAAC;AAAA,MAClC;AAAA,IACF,CAAC;AAAA,EACH;AACA,aAAuB,0BAAK,WAAW,EAAE,WAAW,gBAAgB,UAAU;AAAA,QAC5D,0BAAK,OAAO,EAAE,WAAW,+CAA+C,UAAU;AAAA,UAChF,0BAAK,OAAO,EAAE,UAAU;AAAA,YACtB,0BAAK,OAAO,EAAE,WAAW,2BAA2B,UAAU;AAAA,cAC5D,oBAAAC,KAAK,SAAS,EAAE,UAAU,QAAQ,MAAM,CAAC;AAAA,UACzD,uBAAmC,oBAAAA,KAAK,QAAQ,EAAE,WAAW,gCAAgC,cAA0B,oBAAAA,KAAK,WAAW,CAAC,CAAC,EAAE,CAAC;AAAA,QAC9I,EAAE,CAAC;AAAA,YACa,oBAAAA,KAAK,QAAQ,EAAE,WAAW,oCAAoC,UAAU,EAAE,uBAAuB,EAAE,CAAC;AAAA,MACtH,EAAE,CAAC;AAAA,UACa,oBAAAA,KAAK,OAAO,EAAE,WAAW,6BAA6B,cAA0B,oBAAAA;AAAA,QAC9F;AAAA,QACA;AAAA,UACE,QAAQ;AAAA,YACN;AAAA,cACE,SAAS;AAAA,gBACP;AAAA,kBACE,OAAO,EAAE,cAAc;AAAA,kBACvB,IAAI;AAAA,kBACJ,UAAsB,oBAAAA,KAAK,cAAc,CAAC,CAAC;AAAA,gBAC7C;AAAA,cACF;AAAA,YACF;AAAA,YACA;AAAA,cACE,SAAS;AAAA,gBACP;AAAA,kBACE,OAAO,EAAE,gBAAgB;AAAA,kBACzB,SAAS;AAAA,kBACT,UAAsB,oBAAAA,KAAK,OAAO,CAAC,CAAC;AAAA,gBACtC;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF,EAAE,CAAC;AAAA,IACL,EAAE,CAAC;AAAA,QACa,oBAAAA,KAAK,YAAY,EAAE,OAAO,EAAE,YAAY,GAAG,OAAO,QAAQ,IAAI,CAAC;AAAA,KAC/E,aAAQ,YAAR,mBAAiB,IAAI,CAAC,MAAG;AA9L7B,UAAAC;AA8LgD,qCAAAD;AAAA,QAC1C;AAAA,QACA;AAAA,UACE,QAAOC,MAAA,EAAE,WAAF,gBAAAA,IAAU;AAAA,UACjB,WAAuB,oBAAAD,KAAK,OAAO,EAAE,MAAM,WAAW,UAAU,EAAE,MAAM,CAAC;AAAA,QAC3E;AAAA,QACA,EAAE;AAAA,MACJ;AAAA;AAAA,EACF,EAAE,CAAC;AACL;AAWA,IAAI,mBAAmB,CAAC,EAAE,KAAK,MAAM;AACnC,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,aAAuB,oBAAAE;AAAA,IACrB;AAAA,IACA;AAAA,MACE,QAAQ;AAAA,QACN;AAAA,UACE,SAAS;AAAA,YACP;AAAA,cACE,UAAsB,oBAAAA,KAAK,WAAW,CAAC,CAAC;AAAA,cACxC,OAAO,EAAE,2CAA2C;AAAA,cACpD,IAAI,cAAc,KAAK,EAAE;AAAA,YAC3B;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAOA,IAAI,eAAe,mBAAmB;AACtC,IAAI,2BAA2B,MAAM;AACnC,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,aAAO;AAAA,IACL,MAAM;AAAA,MACJ,aAAa,SAAS,SAAS;AAAA,QAC7B,QAAQ,EAAE,cAAc;AAAA,QACxB,MAAM,CAAC,EAAE,SAAS,MAAM;AACtB,gBAAM,QAAQ,SAAS;AACvB,cAAI,CAAC,OAAO;AACV,uBAAuB,oBAAAC,KAAK,iBAAiB,CAAC,CAAC;AAAA,UACjD;AACA,qBAAuB,oBAAAA,KAAK,OAAO,EAAE,WAAW,+CAA+C,cAA0B,oBAAAA,KAAK,QAAQ,EAAE,WAAW,YAAY,UAAU,MAAM,CAAC,EAAE,CAAC;AAAA,QACrL;AAAA,MACF,CAAC;AAAA,MACD,aAAa,SAAS,OAAO;AAAA,QAC3B,QAAQ,EAAE,YAAY;AAAA,QACtB,MAAM,CAAC,EAAE,SAAS,MAAM;AACtB,gBAAM,MAAM,SAAS;AACrB,cAAI,CAAC,KAAK;AACR,uBAAuB,oBAAAA,KAAK,iBAAiB,CAAC,CAAC;AAAA,UACjD;AACA,qBAAuB,oBAAAA,KAAK,OAAO,EAAE,WAAW,+CAA+C,cAA0B,oBAAAA,KAAK,QAAQ,EAAE,WAAW,YAAY,UAAU,IAAI,CAAC,EAAE,CAAC;AAAA,QACnL;AAAA,MACF,CAAC;AAAA,MACD,aAAa,SAAS,qBAAqB;AAAA,QACzC,QAAQ,EAAE,yBAAyB;AAAA,QACnC,MAAM,CAAC,EAAE,SAAS,MAAM;AACtB,gBAAM,WAAW,SAAS;AAC1B,cAAI,OAAO,MAAM,QAAQ,GAAG;AAC1B,uBAAuB,oBAAAA,KAAK,iBAAiB,CAAC,CAAC;AAAA,UACjD;AACA,qBAAuB,oBAAAA,KAAK,OAAO,EAAE,WAAW,+CAA+C,cAA0B,oBAAAA,KAAK,QAAQ,EAAE,WAAW,YAAY,UAAU,SAAS,CAAC,EAAE,CAAC;AAAA,QACxL;AAAA,MACF,CAAC;AAAA,MACD,aAAa,QAAQ;AAAA,QACnB,IAAI;AAAA,QACJ,QAAQ,EAAE,kBAAkB;AAAA,QAC5B,MAAM,CAAC,EAAE,UAAU,KAAK,EAAE,UAAU,UAAU,EAAE,MAAM;AAjR9D;AAkRU,cAAI,GAAC,eAAU,oBAAV,mBAA2B,SAAQ;AACtC,uBAAuB,oBAAAA,KAAK,iBAAiB,CAAC,CAAC;AAAA,UACjD;AACA,cAAI,WAAW;AACf,cAAI,YAAY;AAChB,oBAAU,gBAAgB,QAAQ,CAAC,UAAU;AAC3C,wBAAY,MAAM;AAClB,yBAAa;AAAA,UACf,CAAC;AACD,qBAAuB,oBAAAA,KAAK,OAAO,EAAE,WAAW,+CAA+C,cAA0B,oBAAAA,KAAK,QAAQ,EAAE,WAAW,YAAY,UAAU,EAAE,8BAA8B;AAAA,YACvM,gBAAgB;AAAA,YAChB,eAAe;AAAA,YACf,OAAO;AAAA,UACT,CAAC,EAAE,CAAC,EAAE,CAAC;AAAA,QACT;AAAA,MACF,CAAC;AAAA,MACD,aAAa,QAAQ;AAAA,QACnB,IAAI;AAAA,QACJ,MAAM,CAAC,EAAE,IAAI,UAAsB,oBAAAA,KAAK,kBAAkB,EAAE,MAAM,IAAI,SAAS,CAAC;AAAA,MAClF,CAAC;AAAA,IACH;AAAA,IACA,CAAC,CAAC;AAAA,EACJ;AACF;AAIA,IAAI,YAAY;AAChB,SAAS,wBAAwB;AAAA,EAC/B;AACF,GAAG;AACD,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,QAAM,UAAU,yBAAyB;AACzC,QAAM,EAAE,MAAM,IAAI,aAAa;AAAA,IAC7B,MAAM,kBAAkB,CAAC;AAAA,IACzB;AAAA,IACA,OAAO,eAAe;AAAA,IACtB,kBAAkB;AAAA,IAClB,UAAU,CAAC,QAAQ,IAAI;AAAA,IACvB,UAAU;AAAA,EACZ,CAAC;AACD,QAAM,SAAS,eAAe,SAAS;AACvC,aAAuB,oBAAAC,MAAM,WAAY,EAAE,WAAW,gBAAgB,UAAU;AAAA,QAC9D,oBAAAA,MAAM,OAAO,EAAE,WAAW,+CAA+C,UAAU;AAAA,UACjF,oBAAAC,KAAK,OAAO,EAAE,WAAW,2BAA2B,cAA0B,oBAAAA,KAAK,SAAU,EAAE,OAAO,MAAM,UAAU,EAAE,uBAAuB,EAAE,CAAC,EAAE,CAAC;AAAA,UACrJ,oBAAAA,KAAK,OAAO,EAAE,WAAW,6BAA6B,cAA0B,oBAAAA;AAAA,QAC9F;AAAA,QACA;AAAA,UACE,QAAQ;AAAA,YACN;AAAA,cACE,SAAS;AAAA,gBACP;AAAA,kBACE,OAAO;AAAA,oBACL,SAAS,yCAAyC;AAAA,kBACpD;AAAA,kBACA,IAAI;AAAA,kBACJ,MAAM,aAAyB,oBAAAA,KAAK,WAAY,CAAC,CAAC,QAAoB,oBAAAA,KAAK,WAAY,CAAC,CAAC;AAAA,gBAC3F;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF,EAAE,CAAC;AAAA,IACL,EAAE,CAAC;AAAA,QACa,oBAAAA;AAAA,MACd;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,QACA,UAAU;AAAA,QACV,OAAO,eAAe;AAAA,QACtB,YAAY,CAAC,QAAQ,cAAc,IAAI,EAAE;AAAA,MAC3C;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;AACA,SAAS,8BAA8B;AACrC,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,aAAuB,oBAAAA,KAAK,WAAY,EAAE,WAAW,gBAAgB,cAA0B,oBAAAD,MAAM,OAAO,EAAE,WAAW,+CAA+C,UAAU;AAAA,QAChK,oBAAAA,MAAM,OAAO,EAAE,WAAW,uBAAuB,UAAU;AAAA,UACzD,oBAAAC,KAAK,SAAU,EAAE,OAAO,MAAM,UAAU,EAAE,uBAAuB,EAAE,CAAC;AAAA,UACpE,oBAAAA,KAAK,QAAQ,EAAE,WAAW,+BAA+B,UAAU,EAAE,2CAA2C,EAAE,CAAC;AAAA,IACrI,EAAE,CAAC;AAAA,QACa,oBAAAA,KAAK,OAAO,EAAE,WAAW,6BAA6B,cAA0B,oBAAAA,KAAK,YAAY,EAAE,IAAI,QAAQ,UAAU,EAAE,8BAA8B,EAAE,CAAC,EAAE,CAAC;AAAA,EACjL,EAAE,CAAC,EAAE,CAAC;AACR;AAQA,SAAS,qBAAqB,EAAE,QAAQ,GAAG;AA/W3C;AAgXE,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,QAAM,UAAS,aAAQ,WAAR,mBAAgB,OAAO,CAAC,MAAM,CAAC,OAAO,KAAK,EAAE,SAAS,CAAC,CAAC,EAAE,QAAQ,KAAK,CAAC,IAAI,OAAI;AAjXjG,QAAAJ;AAiXoG,YAAAA,MAAA,GAAG,kBAAH,gBAAAA,IAAkB,cAAc,GAAG;AAAA;AACrI,QAAM,YAAY,CAAC,EAAC,iCAAQ;AAC5B,QAAM,CAAC,UAAU,WAAW,QAAI,wBAAS,CAAC;AAC1C,QAAM,gBAAgB,iCAAQ,MAAM,GAAG;AACvC,QAAM,aAAa,MAAM;AACvB,gBAAY,WAAW,CAAC;AAAA,EAC1B;AACA,aAAuB,oBAAAK,MAAM,WAAY,EAAE,WAAW,8BAA8B,UAAU;AAAA,QAC5E,oBAAAA,MAAM,OAAO,EAAE,WAAW,+CAA+C,UAAU;AAAA,UACjF,oBAAAC,KAAK,SAAU,EAAE,OAAO,MAAM,UAAU,EAAE,eAAe,EAAE,CAAC;AAAA,UAC5D,oBAAAA;AAAA,QACd;AAAA,QACA;AAAA,UACE,QAAQ;AAAA,YACN;AAAA,cACE,SAAS;AAAA,gBACP;AAAA,kBACE,OAAO,EAAE,cAAc;AAAA,kBACvB,IAAI,aAAa,QAAQ,UAAU,aAAa,QAAQ,EAAE;AAAA,kBAC1D,UAAsB,oBAAAA,KAAK,gBAAgB,CAAC,CAAC;AAAA,gBAC/C;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF,EAAE,CAAC;AAAA,IACH,CAAC,iBAA6B,oBAAAA,KAAK,WAAW,EAAE,WAAW,OAAO,CAAC;AAAA,IACnE,+CAAe,IAAI,CAAC,UAAU;AAC5B,iBAAuB,oBAAAD;AAAA,QACrB;AAAA,QACA;AAAA,UACE,WAAW;AAAA,UACX,UAAU;AAAA,gBACQ,oBAAAC,KAAK,QAAQ,EAAE,WAAW,eAAe,UAAU,MAAM,cAAc,YAAY,EAAE,CAAC;AAAA,gBACtF,oBAAAA,KAAK,QAAQ,EAAE,UAAU,gBAAgB,MAAM,QAAQ,MAAM,aAAa,EAAE,CAAC;AAAA,UAC/F;AAAA,QACF;AAAA,QACA,MAAM;AAAA,MACR;AAAA,IACF;AAAA,IACA,iBAA6B,oBAAAD,MAAM,OAAO,EAAE,WAAW,2EAA2E,UAAU;AAAA,UAC1H,oBAAAC,KAAK,QAAQ,EAAE,WAAW,eAAe,UAAU,EAAE,qCAAqC;AAAA,QACxG,OAAO,OAAO;AAAA,QACd,SAAS,KAAK,IAAI,UAAU,OAAO,MAAM;AAAA,MAC3C,CAAC,EAAE,CAAC;AAAA,UACY,oBAAAA;AAAA,QACd;AAAA,QACA;AAAA,UACE,SAAS;AAAA,UACT,UAAU,YAAY,OAAO;AAAA,UAC7B,WAAW;AAAA,UACX,SAAS;AAAA,UACT,UAAU,EAAE,kBAAkB;AAAA,QAChC;AAAA,MACF;AAAA,IACF,EAAE,CAAC;AAAA,EACL,EAAE,CAAC;AACL;AAIA,IAAI,uBAAuB,MAAM;AAC/B,QAAM,cAAc,cAAc;AAClC,QAAM,EAAE,IAAI,WAAW,IAAI,UAAU;AACrC,QAAM,EAAE,SAAS,WAAW,SAAS,MAAM,IAAI;AAAA,IAC7C;AAAA,IACA;AAAA,IACA,EAAE,QAAQ,sBAAsB;AAAA,IAChC;AAAA,MACE;AAAA,IACF;AAAA,EACF;AACA,QAAM,EAAE,WAAW,IAAI,aAAa;AACpC,MAAI,aAAa,CAAC,SAAS;AACzB,eAAuB,oBAAAC;AAAA,MACrB;AAAA,MACA;AAAA,QACE,cAAc;AAAA,QACd,iBAAiB;AAAA,QACjB,UAAU;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,EACF;AACA,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,oBAAAC;AAAA,IACrB;AAAA,IACA;AAAA,MACE,MAAM;AAAA,MACN,WAAW;AAAA,MACX,UAAU;AAAA,MACV,cAAc;AAAA,MACd,SAAS;AAAA,QACP,OAAO,WAAW,+BAA+B;AAAA,QACjD,QAAQ,WAAW,gCAAgC;AAAA,QACnD,WAAW,WAAW,oCAAoC;AAAA,QAC1D,YAAY,WAAW,qCAAqC;AAAA,MAC9D;AAAA,MACA,UAAU;AAAA,YACQ,oBAAAA,MAAM,cAAc,MAAM,EAAE,UAAU;AAAA,cACpC,oBAAAD,KAAK,uBAAuB,EAAE,QAAQ,CAAC;AAAA,UACvD,CAAC,QAAQ,uBAAmC,oBAAAA,KAAK,6BAA6B,CAAC,CAAC,QAAoB,oBAAAA;AAAA,YAClG;AAAA,YACA;AAAA,cACE,gBAAgB,QAAQ,gBAAgB,IAAI,CAAC,MAAM;AACjD,uBAAO;AAAA,kBACL,GAAG,EAAE;AAAA,kBACL,mBAAmB,EAAE;AAAA,kBACrB;AAAA,gBACF;AAAA,cACF,CAAC;AAAA,YACH;AAAA,UACF;AAAA,QACF,EAAE,CAAC;AAAA,YACa,oBAAAA,KAAK,cAAc,SAAS,EAAE,cAA0B,oBAAAA,KAAK,sBAAsB,EAAE,QAAQ,CAAC,EAAE,CAAC;AAAA,MACnH;AAAA,IACF;AAAA,EACF;AACF;", "names": ["import_jsx_runtime", "import_jsx_runtime", "import_jsx_runtime", "import_react", "import_jsx_runtime", "jsx2", "_a", "jsx3", "jsx4", "jsxs2", "jsx5", "jsxs3", "jsx6", "jsx7", "jsxs4"]}