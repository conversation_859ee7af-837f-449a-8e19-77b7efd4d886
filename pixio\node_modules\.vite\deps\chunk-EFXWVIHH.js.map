{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-XDJ7OMBR.mjs"], "sourcesContent": ["// src/components/inputs/chip-input/chip-input.tsx\nimport { XMarkMini } from \"@medusajs/icons\";\nimport { Badge, clx } from \"@medusajs/ui\";\nimport { AnimatePresence, motion } from \"motion/react\";\nimport {\n  forwardRef,\n  useImperativeHandle,\n  useRef,\n  useState\n} from \"react\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar ChipInput = forwardRef(\n  ({\n    value,\n    onChange,\n    onBlur,\n    disabled,\n    name,\n    showRemove = true,\n    variant = \"base\",\n    allowDuplicates = false,\n    placeholder,\n    className\n  }, ref) => {\n    const innerRef = useRef(null);\n    const isControlledRef = useRef(typeof value !== \"undefined\");\n    const isControlled = isControlledRef.current;\n    const [uncontrolledValue, setUncontrolledValue] = useState([]);\n    useImperativeHandle(\n      ref,\n      () => innerRef.current\n    );\n    const [duplicateIndex, setDuplicateIndex] = useState(null);\n    const chips = isControlled ? value : uncontrolledValue;\n    const handleAddChip = (chip) => {\n      const cleanValue = chip.trim();\n      if (!cleanValue) {\n        return;\n      }\n      if (!allowDuplicates && chips.includes(cleanValue)) {\n        setDuplicateIndex(chips.indexOf(cleanValue));\n        setTimeout(() => {\n          setDuplicateIndex(null);\n        }, 300);\n        return;\n      }\n      onChange?.([...chips, cleanValue]);\n      if (!isControlled) {\n        setUncontrolledValue([...chips, cleanValue]);\n      }\n    };\n    const handleRemoveChip = (chip) => {\n      onChange?.(chips.filter((v) => v !== chip));\n      if (!isControlled) {\n        setUncontrolledValue(chips.filter((v) => v !== chip));\n      }\n    };\n    const handleBlur = (e) => {\n      onBlur?.();\n      if (e.target.value) {\n        handleAddChip(e.target.value);\n        e.target.value = \"\";\n      }\n    };\n    const handleKeyDown = (e) => {\n      if (e.key === \"Enter\" || e.key === \",\") {\n        e.preventDefault();\n        if (!innerRef.current?.value) {\n          return;\n        }\n        handleAddChip(innerRef.current?.value ?? \"\");\n        innerRef.current.value = \"\";\n        innerRef.current?.focus();\n      }\n      if (e.key === \"Backspace\" && innerRef.current?.value === \"\") {\n        handleRemoveChip(chips[chips.length - 1]);\n      }\n    };\n    const shake = {\n      x: [0, -2, 2, -2, 2, 0],\n      transition: { duration: 0.3 }\n    };\n    return /* @__PURE__ */ jsxs(\n      \"div\",\n      {\n        className: clx(\n          \"shadow-borders-base flex min-h-8 flex-wrap items-center gap-1 rounded-md px-2 py-1.5\",\n          \"transition-fg focus-within:shadow-borders-interactive-with-active\",\n          \"has-[input:disabled]:bg-ui-bg-disabled has-[input:disabled]:text-ui-fg-disabled has-[input:disabled]:cursor-not-allowed\",\n          {\n            \"bg-ui-bg-field-component hover:bg-ui-bg-field-component-hover\": variant === \"contrast\",\n            \"bg-ui-bg-field hover:bg-ui-bg-field-hover\": variant === \"base\"\n          },\n          className\n        ),\n        tabIndex: -1,\n        onClick: () => innerRef.current?.focus(),\n        children: [\n          chips.map((v, index) => {\n            return /* @__PURE__ */ jsx(AnimatePresence, { children: /* @__PURE__ */ jsx(\n              Badge,\n              {\n                size: \"2xsmall\",\n                className: clx(\"gap-x-0.5 pl-1.5 pr-1.5\", {\n                  \"transition-fg pr-1\": showRemove,\n                  \"shadow-borders-focus\": index === duplicateIndex\n                }),\n                asChild: true,\n                children: /* @__PURE__ */ jsxs(\n                  motion.div,\n                  {\n                    animate: index === duplicateIndex ? shake : void 0,\n                    children: [\n                      v,\n                      showRemove && /* @__PURE__ */ jsx(\n                        \"button\",\n                        {\n                          tabIndex: -1,\n                          type: \"button\",\n                          onClick: () => handleRemoveChip(v),\n                          className: clx(\n                            \"text-ui-fg-subtle transition-fg outline-none\"\n                          ),\n                          children: /* @__PURE__ */ jsx(XMarkMini, {})\n                        }\n                      )\n                    ]\n                  }\n                )\n              }\n            ) }, `${v}-${index}`);\n          }),\n          /* @__PURE__ */ jsx(\n            \"input\",\n            {\n              className: clx(\n                \"caret-ui-fg-base text-ui-fg-base txt-compact-small flex-1 appearance-none bg-transparent\",\n                \"disabled:text-ui-fg-disabled disabled:cursor-not-allowed\",\n                \"focus:outline-none\",\n                \"placeholder:text-ui-fg-muted\"\n              ),\n              onKeyDown: handleKeyDown,\n              onBlur: handleBlur,\n              disabled,\n              name,\n              ref: innerRef,\n              placeholder: chips.length === 0 ? placeholder : void 0,\n              autoComplete: \"off\"\n            }\n          )\n        ]\n      }\n    );\n  }\n);\nChipInput.displayName = \"ChipInput\";\n\nexport {\n  ChipInput\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAIA,IAAAA,gBAKO;AACP,yBAA0B;AAC1B,IAAI,gBAAY;AAAA,EACd,CAAC;AAAA,IACC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,aAAa;AAAA,IACb,UAAU;AAAA,IACV,kBAAkB;AAAA,IAClB;AAAA,IACA;AAAA,EACF,GAAG,QAAQ;AACT,UAAM,eAAW,sBAAO,IAAI;AAC5B,UAAM,sBAAkB,sBAAO,OAAO,UAAU,WAAW;AAC3D,UAAM,eAAe,gBAAgB;AACrC,UAAM,CAAC,mBAAmB,oBAAoB,QAAI,wBAAS,CAAC,CAAC;AAC7D;AAAA,MACE;AAAA,MACA,MAAM,SAAS;AAAA,IACjB;AACA,UAAM,CAAC,gBAAgB,iBAAiB,QAAI,wBAAS,IAAI;AACzD,UAAM,QAAQ,eAAe,QAAQ;AACrC,UAAM,gBAAgB,CAAC,SAAS;AAC9B,YAAM,aAAa,KAAK,KAAK;AAC7B,UAAI,CAAC,YAAY;AACf;AAAA,MACF;AACA,UAAI,CAAC,mBAAmB,MAAM,SAAS,UAAU,GAAG;AAClD,0BAAkB,MAAM,QAAQ,UAAU,CAAC;AAC3C,mBAAW,MAAM;AACf,4BAAkB,IAAI;AAAA,QACxB,GAAG,GAAG;AACN;AAAA,MACF;AACA,2CAAW,CAAC,GAAG,OAAO,UAAU;AAChC,UAAI,CAAC,cAAc;AACjB,6BAAqB,CAAC,GAAG,OAAO,UAAU,CAAC;AAAA,MAC7C;AAAA,IACF;AACA,UAAM,mBAAmB,CAAC,SAAS;AACjC,2CAAW,MAAM,OAAO,CAAC,MAAM,MAAM,IAAI;AACzC,UAAI,CAAC,cAAc;AACjB,6BAAqB,MAAM,OAAO,CAAC,MAAM,MAAM,IAAI,CAAC;AAAA,MACtD;AAAA,IACF;AACA,UAAM,aAAa,CAAC,MAAM;AACxB;AACA,UAAI,EAAE,OAAO,OAAO;AAClB,sBAAc,EAAE,OAAO,KAAK;AAC5B,UAAE,OAAO,QAAQ;AAAA,MACnB;AAAA,IACF;AACA,UAAM,gBAAgB,CAAC,MAAM;AAhEjC;AAiEM,UAAI,EAAE,QAAQ,WAAW,EAAE,QAAQ,KAAK;AACtC,UAAE,eAAe;AACjB,YAAI,GAAC,cAAS,YAAT,mBAAkB,QAAO;AAC5B;AAAA,QACF;AACA,wBAAc,cAAS,YAAT,mBAAkB,UAAS,EAAE;AAC3C,iBAAS,QAAQ,QAAQ;AACzB,uBAAS,YAAT,mBAAkB;AAAA,MACpB;AACA,UAAI,EAAE,QAAQ,iBAAe,cAAS,YAAT,mBAAkB,WAAU,IAAI;AAC3D,yBAAiB,MAAM,MAAM,SAAS,CAAC,CAAC;AAAA,MAC1C;AAAA,IACF;AACA,UAAM,QAAQ;AAAA,MACZ,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,GAAG,CAAC;AAAA,MACtB,YAAY,EAAE,UAAU,IAAI;AAAA,IAC9B;AACA,eAAuB;AAAA,MACrB;AAAA,MACA;AAAA,QACE,WAAW;AAAA,UACT;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,YACE,iEAAiE,YAAY;AAAA,YAC7E,6CAA6C,YAAY;AAAA,UAC3D;AAAA,UACA;AAAA,QACF;AAAA,QACA,UAAU;AAAA,QACV,SAAS,MAAG;AAhGpB;AAgGuB,gCAAS,YAAT,mBAAkB;AAAA;AAAA,QACjC,UAAU;AAAA,UACR,MAAM,IAAI,CAAC,GAAG,UAAU;AACtB,uBAAuB,wBAAI,iBAAiB,EAAE,cAA0B;AAAA,cACtE;AAAA,cACA;AAAA,gBACE,MAAM;AAAA,gBACN,WAAW,IAAI,2BAA2B;AAAA,kBACxC,sBAAsB;AAAA,kBACtB,wBAAwB,UAAU;AAAA,gBACpC,CAAC;AAAA,gBACD,SAAS;AAAA,gBACT,cAA0B;AAAA,kBACxB,OAAO;AAAA,kBACP;AAAA,oBACE,SAAS,UAAU,iBAAiB,QAAQ;AAAA,oBAC5C,UAAU;AAAA,sBACR;AAAA,sBACA,kBAA8B;AAAA,wBAC5B;AAAA,wBACA;AAAA,0BACE,UAAU;AAAA,0BACV,MAAM;AAAA,0BACN,SAAS,MAAM,iBAAiB,CAAC;AAAA,0BACjC,WAAW;AAAA,4BACT;AAAA,0BACF;AAAA,0BACA,cAA0B,wBAAI,WAAW,CAAC,CAAC;AAAA,wBAC7C;AAAA,sBACF;AAAA,oBACF;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAAA,YACF,EAAE,GAAG,GAAG,CAAC,IAAI,KAAK,EAAE;AAAA,UACtB,CAAC;AAAA,cACe;AAAA,YACd;AAAA,YACA;AAAA,cACE,WAAW;AAAA,gBACT;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,cACF;AAAA,cACA,WAAW;AAAA,cACX,QAAQ;AAAA,cACR;AAAA,cACA;AAAA,cACA,KAAK;AAAA,cACL,aAAa,MAAM,WAAW,IAAI,cAAc;AAAA,cAChD,cAAc;AAAA,YAChB;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AACA,UAAU,cAAc;", "names": ["import_react"]}