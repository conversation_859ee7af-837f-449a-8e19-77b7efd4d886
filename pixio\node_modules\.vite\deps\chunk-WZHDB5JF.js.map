{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-QJ6SBVJ2.mjs"], "sourcesContent": ["// src/routes/orders/order-create-claim/components/claim-create-form/item-placeholder.tsx\nimport { jsx } from \"react/jsx-runtime\";\nvar ItemPlaceholder = () => {\n  return /* @__PURE__ */ jsx(\n    \"div\",\n    {\n      style: {\n        background: \"repeating-linear-gradient(-45deg, rgb(212, 212, 216, 0.15), rgb(212, 212, 216,.15) 10px, transparent 10px, transparent 20px)\"\n      },\n      className: \"bg-ui-bg-field mt-4 block h-[56px] w-full rounded-lg border border-dashed\"\n    }\n  );\n};\n\nexport {\n  ItemPlaceholder\n};\n"], "mappings": ";;;;;;;;AACA,yBAAoB;AACpB,IAAI,kBAAkB,MAAM;AAC1B,aAAuB;AAAA,IACrB;AAAA,IACA;AAAA,MACE,OAAO;AAAA,QACL,YAAY;AAAA,MACd;AAAA,MACA,WAAW;AAAA,IACb;AAAA,EACF;AACF;", "names": []}