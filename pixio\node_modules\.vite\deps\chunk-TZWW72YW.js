import {
  queryClient
} from "./chunk-VMLNCWLE.js";
import {
  queryKeysFactory
} from "./chunk-CUPZIPFX.js";
import {
  sdk
} from "./chunk-JFCIYOH4.js";
import {
  useMutation,
  useQuery
} from "./chunk-R35JBZ3G.js";

// node_modules/@medusajs/dashboard/dist/chunk-G2H6MAK7.mjs
var REGIONS_QUERY_KEY = "campaigns";
var campaignsQueryKeys = queryKeysFactory(REGIONS_QUERY_KEY);
var useCampaign = (id, query, options) => {
  const { data, ...rest } = useQuery({
    queryKey: campaignsQueryKeys.detail(id),
    queryFn: async () => sdk.admin.campaign.retrieve(id, query),
    ...options
  });
  return { ...data, ...rest };
};
var useCampaigns = (query, options) => {
  const { data, ...rest } = useQuery({
    queryFn: () => sdk.admin.campaign.list(query),
    queryKey: campaignsQueryKeys.list(query),
    ...options
  });
  return { ...data, ...rest };
};
var useCreateCampaign = (options) => {
  return useMutation({
    mutationFn: (payload) => sdk.admin.campaign.create(payload),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({ queryKey: campaignsQueryKeys.lists() });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useUpdateCampaign = (id, options) => {
  return useMutation({
    mutationFn: (payload) => sdk.admin.campaign.update(id, payload),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({ queryKey: campaignsQueryKeys.lists() });
      queryClient.invalidateQueries({ queryKey: campaignsQueryKeys.details() });
      queryClient.invalidateQueries({ queryKey: promotionsQueryKeys.lists() });
      queryClient.invalidateQueries({ queryKey: promotionsQueryKeys.details() });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useDeleteCampaign = (id, options) => {
  return useMutation({
    mutationFn: () => sdk.admin.campaign.delete(id),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({ queryKey: campaignsQueryKeys.lists() });
      queryClient.invalidateQueries({ queryKey: campaignsQueryKeys.details() });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useAddOrRemoveCampaignPromotions = (id, options) => {
  return useMutation({
    mutationFn: (payload) => sdk.admin.campaign.batchPromotions(id, payload),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({ queryKey: campaignsQueryKeys.details() });
      queryClient.invalidateQueries({ queryKey: promotionsQueryKeys.lists() });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var PROMOTIONS_QUERY_KEY = "promotions";
var promotionsQueryKeys = {
  ...queryKeysFactory(PROMOTIONS_QUERY_KEY),
  // TODO: handle invalidations properly
  listRules: (id, ruleType, query) => [PROMOTIONS_QUERY_KEY, id, ruleType, query],
  listRuleAttributes: (ruleType, promotionType) => [
    PROMOTIONS_QUERY_KEY,
    ruleType,
    promotionType
  ],
  listRuleValues: (ruleType, ruleValue, query) => [PROMOTIONS_QUERY_KEY, ruleType, ruleValue, query]
};
var usePromotion = (id, options) => {
  const { data, ...rest } = useQuery({
    queryKey: promotionsQueryKeys.detail(id),
    queryFn: async () => sdk.admin.promotion.retrieve(id),
    ...options
  });
  return { ...data, ...rest };
};
var usePromotionRules = (id, ruleType, query, options) => {
  const { data, ...rest } = useQuery({
    queryKey: promotionsQueryKeys.listRules(id, ruleType, query),
    queryFn: async () => sdk.admin.promotion.listRules(id, ruleType, query),
    ...options
  });
  return { ...data, ...rest };
};
var usePromotions = (query, options) => {
  const { data, ...rest } = useQuery({
    queryKey: promotionsQueryKeys.list(query),
    queryFn: async () => sdk.admin.promotion.list(query),
    ...options
  });
  return { ...data, ...rest };
};
var usePromotionRuleAttributes = (ruleType, promotionType, options) => {
  const { data, ...rest } = useQuery({
    queryKey: promotionsQueryKeys.listRuleAttributes(ruleType, promotionType),
    queryFn: async () => sdk.admin.promotion.listRuleAttributes(ruleType, promotionType),
    ...options
  });
  return { ...data, ...rest };
};
var useDeletePromotion = (id, options) => {
  return useMutation({
    mutationFn: () => sdk.admin.promotion.delete(id),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({ queryKey: promotionsQueryKeys.lists() });
      queryClient.invalidateQueries({
        queryKey: promotionsQueryKeys.detail(id)
      });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useCreatePromotion = (options) => {
  return useMutation({
    mutationFn: (payload) => sdk.admin.promotion.create(payload),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({ queryKey: promotionsQueryKeys.lists() });
      queryClient.invalidateQueries({ queryKey: campaignsQueryKeys.lists() });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useUpdatePromotion = (id, options) => {
  return useMutation({
    mutationFn: (payload) => sdk.admin.promotion.update(id, payload),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({ queryKey: promotionsQueryKeys.all });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var usePromotionAddRules = (id, ruleType, options) => {
  return useMutation({
    mutationFn: (payload) => sdk.admin.promotion.addRules(id, ruleType, payload),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({ queryKey: promotionsQueryKeys.all });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var usePromotionRemoveRules = (id, ruleType, options) => {
  return useMutation({
    mutationFn: (payload) => sdk.admin.promotion.removeRules(id, ruleType, payload),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({ queryKey: promotionsQueryKeys.all });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var usePromotionUpdateRules = (id, ruleType, options) => {
  return useMutation({
    mutationFn: (payload) => sdk.admin.promotion.updateRules(id, ruleType, payload),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({ queryKey: promotionsQueryKeys.all });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};

export {
  campaignsQueryKeys,
  useCampaign,
  useCampaigns,
  useCreateCampaign,
  useUpdateCampaign,
  useDeleteCampaign,
  useAddOrRemoveCampaignPromotions,
  promotionsQueryKeys,
  usePromotion,
  usePromotionRules,
  usePromotions,
  usePromotionRuleAttributes,
  useDeletePromotion,
  useCreatePromotion,
  useUpdatePromotion,
  usePromotionAddRules,
  usePromotionRemoveRules,
  usePromotionUpdateRules
};
//# sourceMappingURL=chunk-TZWW72YW.js.map
