{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-BFAYZKJV.mjs"], "sourcesContent": ["import {\n  DateCell\n} from \"./chunk-5HNRTDDS.mjs\";\nimport {\n  TextCell,\n  TextHeader\n} from \"./chunk-MSDRGCRR.mjs\";\nimport {\n  PlaceholderCell\n} from \"./chunk-P3UUX2T6.mjs\";\n\n// src/hooks/table/columns/use-campaign-table-columns.tsx\nimport { createColumnHelper } from \"@tanstack/react-table\";\nimport { useMemo } from \"react\";\nimport { useTranslation as useTranslation3 } from \"react-i18next\";\n\n// src/components/table/table-cells/sales-channel/description-cell/description-cell.tsx\nimport { useTranslation } from \"react-i18next\";\nimport { jsx } from \"react/jsx-runtime\";\nvar DescriptionCell = ({ description }) => {\n  if (!description) {\n    return /* @__PURE__ */ jsx(PlaceholderCell, {});\n  }\n  return /* @__PURE__ */ jsx(\"div\", { className: \"flex h-full w-full items-center overflow-hidden\", children: /* @__PURE__ */ jsx(\"span\", { className: \"truncate\", children: description }) });\n};\nvar DescriptionHeader = () => {\n  const { t } = useTranslation();\n  return /* @__PURE__ */ jsx(\"div\", { className: \"flex h-full w-full items-center\", children: /* @__PURE__ */ jsx(\"span\", { className: \"truncate\", children: t(\"fields.description\") }) });\n};\n\n// src/components/table/table-cells/sales-channel/name-cell/name-cell.tsx\nimport { useTranslation as useTranslation2 } from \"react-i18next\";\nimport { jsx as jsx2 } from \"react/jsx-runtime\";\nvar NameCell = ({ name }) => {\n  if (!name) {\n    return /* @__PURE__ */ jsx2(PlaceholderCell, {});\n  }\n  return /* @__PURE__ */ jsx2(\"div\", { className: \"flex h-full w-full items-center overflow-hidden\", children: /* @__PURE__ */ jsx2(\"span\", { className: \"truncate\", children: name }) });\n};\nvar NameHeader = () => {\n  const { t } = useTranslation2();\n  return /* @__PURE__ */ jsx2(\"div\", { className: \"flex h-full w-full items-center\", children: /* @__PURE__ */ jsx2(\"span\", { className: \"truncate\", children: t(\"fields.name\") }) });\n};\n\n// src/hooks/table/columns/use-campaign-table-columns.tsx\nimport { jsx as jsx3 } from \"react/jsx-runtime\";\nvar columnHelper = createColumnHelper();\nvar useCampaignTableColumns = () => {\n  const { t } = useTranslation3();\n  return useMemo(\n    () => [\n      columnHelper.accessor(\"name\", {\n        header: () => /* @__PURE__ */ jsx3(NameHeader, {}),\n        cell: ({ getValue }) => /* @__PURE__ */ jsx3(NameCell, { name: getValue() })\n      }),\n      columnHelper.accessor(\"description\", {\n        header: () => /* @__PURE__ */ jsx3(DescriptionHeader, {}),\n        cell: ({ getValue }) => /* @__PURE__ */ jsx3(DescriptionCell, { description: getValue() })\n      }),\n      columnHelper.accessor(\"campaign_identifier\", {\n        header: () => /* @__PURE__ */ jsx3(TextHeader, { text: t(\"campaigns.fields.identifier\") }),\n        cell: ({ getValue }) => {\n          const value = getValue();\n          return /* @__PURE__ */ jsx3(TextCell, { text: value });\n        }\n      }),\n      columnHelper.accessor(\"starts_at\", {\n        header: () => /* @__PURE__ */ jsx3(TextHeader, { text: t(\"campaigns.fields.start_date\") }),\n        cell: ({ getValue }) => {\n          const value = getValue();\n          if (!value) {\n            return;\n          }\n          const date = new Date(value);\n          return /* @__PURE__ */ jsx3(DateCell, { date });\n        }\n      }),\n      columnHelper.accessor(\"ends_at\", {\n        header: () => /* @__PURE__ */ jsx3(TextHeader, { text: t(\"campaigns.fields.end_date\") }),\n        cell: ({ getValue }) => {\n          const value = getValue();\n          if (!value) {\n            return;\n          }\n          const date = new Date(value);\n          return /* @__PURE__ */ jsx3(DateCell, { date });\n        }\n      })\n    ],\n    [t]\n  );\n};\n\nexport {\n  useCampaignTableColumns\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAaA,mBAAwB;AAKxB,yBAAoB;AAcpB,IAAAA,sBAA4B;AAa5B,IAAAA,sBAA4B;AA1B5B,IAAI,kBAAkB,CAAC,EAAE,YAAY,MAAM;AACzC,MAAI,CAAC,aAAa;AAChB,eAAuB,wBAAI,iBAAiB,CAAC,CAAC;AAAA,EAChD;AACA,aAAuB,wBAAI,OAAO,EAAE,WAAW,mDAAmD,cAA0B,wBAAI,QAAQ,EAAE,WAAW,YAAY,UAAU,YAAY,CAAC,EAAE,CAAC;AAC7L;AACA,IAAI,oBAAoB,MAAM;AAC5B,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,aAAuB,wBAAI,OAAO,EAAE,WAAW,mCAAmC,cAA0B,wBAAI,QAAQ,EAAE,WAAW,YAAY,UAAU,EAAE,oBAAoB,EAAE,CAAC,EAAE,CAAC;AACzL;AAKA,IAAI,WAAW,CAAC,EAAE,KAAK,MAAM;AAC3B,MAAI,CAAC,MAAM;AACT,eAAuB,oBAAAC,KAAK,iBAAiB,CAAC,CAAC;AAAA,EACjD;AACA,aAAuB,oBAAAA,KAAK,OAAO,EAAE,WAAW,mDAAmD,cAA0B,oBAAAA,KAAK,QAAQ,EAAE,WAAW,YAAY,UAAU,KAAK,CAAC,EAAE,CAAC;AACxL;AACA,IAAI,aAAa,MAAM;AACrB,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,aAAuB,oBAAAA,KAAK,OAAO,EAAE,WAAW,mCAAmC,cAA0B,oBAAAA,KAAK,QAAQ,EAAE,WAAW,YAAY,UAAU,EAAE,aAAa,EAAE,CAAC,EAAE,CAAC;AACpL;AAIA,IAAI,eAAe,mBAAmB;AACtC,IAAI,0BAA0B,MAAM;AAClC,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,aAAO;AAAA,IACL,MAAM;AAAA,MACJ,aAAa,SAAS,QAAQ;AAAA,QAC5B,QAAQ,UAAsB,oBAAAC,KAAK,YAAY,CAAC,CAAC;AAAA,QACjD,MAAM,CAAC,EAAE,SAAS,UAAsB,oBAAAA,KAAK,UAAU,EAAE,MAAM,SAAS,EAAE,CAAC;AAAA,MAC7E,CAAC;AAAA,MACD,aAAa,SAAS,eAAe;AAAA,QACnC,QAAQ,UAAsB,oBAAAA,KAAK,mBAAmB,CAAC,CAAC;AAAA,QACxD,MAAM,CAAC,EAAE,SAAS,UAAsB,oBAAAA,KAAK,iBAAiB,EAAE,aAAa,SAAS,EAAE,CAAC;AAAA,MAC3F,CAAC;AAAA,MACD,aAAa,SAAS,uBAAuB;AAAA,QAC3C,QAAQ,UAAsB,oBAAAA,KAAK,YAAY,EAAE,MAAM,EAAE,6BAA6B,EAAE,CAAC;AAAA,QACzF,MAAM,CAAC,EAAE,SAAS,MAAM;AACtB,gBAAM,QAAQ,SAAS;AACvB,qBAAuB,oBAAAA,KAAK,UAAU,EAAE,MAAM,MAAM,CAAC;AAAA,QACvD;AAAA,MACF,CAAC;AAAA,MACD,aAAa,SAAS,aAAa;AAAA,QACjC,QAAQ,UAAsB,oBAAAA,KAAK,YAAY,EAAE,MAAM,EAAE,6BAA6B,EAAE,CAAC;AAAA,QACzF,MAAM,CAAC,EAAE,SAAS,MAAM;AACtB,gBAAM,QAAQ,SAAS;AACvB,cAAI,CAAC,OAAO;AACV;AAAA,UACF;AACA,gBAAM,OAAO,IAAI,KAAK,KAAK;AAC3B,qBAAuB,oBAAAA,KAAK,UAAU,EAAE,KAAK,CAAC;AAAA,QAChD;AAAA,MACF,CAAC;AAAA,MACD,aAAa,SAAS,WAAW;AAAA,QAC/B,QAAQ,UAAsB,oBAAAA,KAAK,YAAY,EAAE,MAAM,EAAE,2BAA2B,EAAE,CAAC;AAAA,QACvF,MAAM,CAAC,EAAE,SAAS,MAAM;AACtB,gBAAM,QAAQ,SAAS;AACvB,cAAI,CAAC,OAAO;AACV;AAAA,UACF;AACA,gBAAM,OAAO,IAAI,KAAK,KAAK;AAC3B,qBAAuB,oBAAAA,KAAK,UAAU,EAAE,KAAK,CAAC;AAAA,QAChD;AAAA,MACF,CAAC;AAAA,IACH;AAAA,IACA,CAAC,CAAC;AAAA,EACJ;AACF;", "names": ["import_jsx_runtime", "jsx2", "jsx3"]}