import {
  currencies,
  getCurrencySymbol
} from "./chunk-H3DTEG3J.js";
import {
  Form,
  useWatch
} from "./chunk-XXJU43CK.js";
import {
  useStore
} from "./chunk-AAFHKNJG.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import {
  CurrencyInput2 as CurrencyInput,
  DatePicker,
  Heading,
  Input,
  RadioGroup,
  Select,
  Text,
  Textarea
} from "./chunk-YP2LLXWB.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/chunk-TDDYKNA2.mjs
var import_react = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var CreateCampaignFormFields = ({ form, fieldScope = "" }) => {
  var _a;
  const { t } = useTranslation();
  const { store } = useStore();
  const watchValueType = useWatch({
    control: form.control,
    name: `${fieldScope}budget.type`
  });
  const isTypeSpend = watchValueType === "spend";
  const currencyValue = useWatch({
    control: form.control,
    name: `${fieldScope}budget.currency_code`
  });
  const promotionCurrencyValue = useWatch({
    control: form.control,
    name: `application_method.currency_code`
  });
  const currency = currencyValue || promotionCurrencyValue;
  (0, import_react.useEffect)(() => {
    form.setValue(`${fieldScope}budget.limit`, null);
    if (isTypeSpend) {
      form.setValue(`campaign.budget.currency_code`, promotionCurrencyValue);
    }
    if (watchValueType === "usage") {
      form.setValue(`campaign.budget.currency_code`, null);
    }
  }, [watchValueType]);
  if (promotionCurrencyValue) {
    const formCampaignBudget = (_a = form.getValues().campaign) == null ? void 0 : _a.budget;
    const formCampaignCurrency = formCampaignBudget == null ? void 0 : formCampaignBudget.currency_code;
    if ((formCampaignBudget == null ? void 0 : formCampaignBudget.type) === "spend" && formCampaignCurrency !== promotionCurrencyValue) {
      form.setValue("campaign.budget.currency_code", promotionCurrencyValue);
    }
  }
  return (0, import_jsx_runtime.jsxs)("div", { className: "flex w-full max-w-[720px] flex-col gap-y-8", children: [
    (0, import_jsx_runtime.jsxs)("div", { children: [
      (0, import_jsx_runtime.jsx)(Heading, { children: t("campaigns.create.header") }),
      (0, import_jsx_runtime.jsx)(Text, { size: "small", className: "text-ui-fg-subtle", children: t("campaigns.create.hint") })
    ] }),
    (0, import_jsx_runtime.jsxs)("div", { className: "flex flex-col gap-y-4", children: [
      (0, import_jsx_runtime.jsxs)("div", { className: "grid grid-cols-1 gap-4 md:grid-cols-2", children: [
        (0, import_jsx_runtime.jsx)(
          Form.Field,
          {
            control: form.control,
            name: `${fieldScope}name`,
            render: ({ field }) => {
              return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                (0, import_jsx_runtime.jsx)(Form.Label, { children: t("fields.name") }),
                (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(Input, { ...field }) }),
                (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
              ] });
            }
          }
        ),
        (0, import_jsx_runtime.jsx)(
          Form.Field,
          {
            control: form.control,
            name: `${fieldScope}campaign_identifier`,
            render: ({ field }) => {
              return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                (0, import_jsx_runtime.jsx)(Form.Label, { children: t("campaigns.fields.identifier") }),
                (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(Input, { ...field }) }),
                (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
              ] });
            }
          }
        )
      ] }),
      (0, import_jsx_runtime.jsx)(
        Form.Field,
        {
          control: form.control,
          name: `${fieldScope}description`,
          render: ({ field }) => {
            return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
              (0, import_jsx_runtime.jsx)(Form.Label, { optional: true, children: t("fields.description") }),
              (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(Textarea, { ...field }) }),
              (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
            ] });
          }
        }
      )
    ] }),
    (0, import_jsx_runtime.jsxs)("div", { className: "grid grid-cols-1 gap-4 md:grid-cols-2", children: [
      (0, import_jsx_runtime.jsx)(
        Form.Field,
        {
          control: form.control,
          name: `${fieldScope}starts_at`,
          render: ({ field }) => {
            return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
              (0, import_jsx_runtime.jsx)(Form.Label, { optional: true, children: t("campaigns.fields.start_date") }),
              (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(
                DatePicker,
                {
                  granularity: "minute",
                  shouldCloseOnSelect: false,
                  ...field
                }
              ) }),
              (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
            ] });
          }
        }
      ),
      (0, import_jsx_runtime.jsx)(
        Form.Field,
        {
          control: form.control,
          name: `${fieldScope}ends_at`,
          render: ({ field }) => {
            return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
              (0, import_jsx_runtime.jsx)(Form.Label, { optional: true, children: t("campaigns.fields.end_date") }),
              (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(
                DatePicker,
                {
                  granularity: "minute",
                  shouldCloseOnSelect: false,
                  ...field
                }
              ) }),
              (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
            ] });
          }
        }
      )
    ] }),
    (0, import_jsx_runtime.jsxs)("div", { children: [
      (0, import_jsx_runtime.jsx)(Heading, { children: t("campaigns.budget.create.header") }),
      (0, import_jsx_runtime.jsx)(Text, { size: "small", className: "text-ui-fg-subtle", children: t("campaigns.budget.create.hint") })
    ] }),
    (0, import_jsx_runtime.jsx)(
      Form.Field,
      {
        control: form.control,
        name: `${fieldScope}budget.type`,
        render: ({ field }) => {
          return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
            (0, import_jsx_runtime.jsx)(
              Form.Label,
              {
                tooltip: (fieldScope == null ? void 0 : fieldScope.length) && !currency ? t("promotions.tooltips.campaignType") : void 0,
                children: t("campaigns.budget.fields.type")
              }
            ),
            (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsxs)(
              RadioGroup,
              {
                className: "flex gap-y-3",
                ...field,
                onValueChange: field.onChange,
                children: [
                  (0, import_jsx_runtime.jsx)(
                    RadioGroup.ChoiceBox,
                    {
                      value: "usage",
                      label: t("campaigns.budget.type.usage.title"),
                      description: t("campaigns.budget.type.usage.description")
                    }
                  ),
                  (0, import_jsx_runtime.jsx)(
                    RadioGroup.ChoiceBox,
                    {
                      value: "spend",
                      label: t("campaigns.budget.type.spend.title"),
                      description: t("campaigns.budget.type.spend.description"),
                      disabled: (fieldScope == null ? void 0 : fieldScope.length) ? !currency : false
                    }
                  )
                ]
              }
            ) }),
            (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
          ] });
        }
      }
    ),
    (0, import_jsx_runtime.jsxs)("div", { className: "grid grid-cols-1 gap-4 md:grid-cols-2", children: [
      isTypeSpend && (0, import_jsx_runtime.jsx)(
        Form.Field,
        {
          control: form.control,
          name: `${fieldScope}budget.currency_code`,
          render: ({ field: { onChange, ref, ...field } }) => {
            return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
              (0, import_jsx_runtime.jsx)(
                Form.Label,
                {
                  tooltip: (fieldScope == null ? void 0 : fieldScope.length) && !currency ? t("promotions.campaign_currency.tooltip") : void 0,
                  children: t("fields.currency")
                }
              ),
              (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsxs)(
                Select,
                {
                  ...field,
                  onValueChange: onChange,
                  disabled: !!fieldScope.length,
                  children: [
                    (0, import_jsx_runtime.jsx)(Select.Trigger, { ref, children: (0, import_jsx_runtime.jsx)(Select.Value, {}) }),
                    (0, import_jsx_runtime.jsx)(Select.Content, { children: Object.values(currencies).filter(
                      (currency2) => {
                        var _a2;
                        return !!((_a2 = store == null ? void 0 : store.supported_currencies) == null ? void 0 : _a2.find(
                          (c) => c.currency_code === currency2.code.toLocaleLowerCase()
                        ));
                      }
                    ).map((currency2) => (0, import_jsx_runtime.jsx)(
                      Select.Item,
                      {
                        value: currency2.code.toLowerCase(),
                        children: currency2.name
                      },
                      currency2.code
                    )) })
                  ]
                }
              ) }),
              (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
            ] });
          }
        }
      ),
      (0, import_jsx_runtime.jsx)(
        Form.Field,
        {
          control: form.control,
          name: `${fieldScope}budget.limit`,
          render: ({ field: { onChange, value, ...field } }) => {
            return (0, import_jsx_runtime.jsxs)(Form.Item, { className: "basis-1/2", children: [
              (0, import_jsx_runtime.jsx)(
                Form.Label,
                {
                  tooltip: !currency && isTypeSpend ? t("promotions.fields.amount.tooltip") : void 0,
                  children: t("campaigns.budget.fields.limit")
                }
              ),
              (0, import_jsx_runtime.jsx)(Form.Control, { children: isTypeSpend ? (0, import_jsx_runtime.jsx)(
                CurrencyInput,
                {
                  min: 0,
                  onValueChange: (value2) => onChange(value2 ? parseInt(value2) : ""),
                  code: currencyValue,
                  symbol: currencyValue ? getCurrencySymbol(currencyValue) : "",
                  ...field,
                  value,
                  disabled: !currency && isTypeSpend
                }
              ) : (0, import_jsx_runtime.jsx)(
                Input,
                {
                  type: "number",
                  ...field,
                  min: 0,
                  value,
                  onChange: (e) => {
                    onChange(
                      e.target.value === "" ? null : parseInt(e.target.value)
                    );
                  }
                },
                "usage"
              ) }),
              (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
            ] });
          }
        }
      )
    ] })
  ] });
};

export {
  CreateCampaignFormFields
};
//# sourceMappingURL=chunk-ENAZYTQU.js.map
