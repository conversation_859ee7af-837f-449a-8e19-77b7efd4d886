import {
  ChipGroup
} from "./chunk-V2W2XNHI.js";
import {
  HandleInput
} from "./chunk-ABBBWI4J.js";
import {
  ChipInput
} from "./chunk-EFXWVIHH.js";
import {
  DataGrid,
  createDataGridHelper,
  createDataGridPriceColumns
} from "./chunk-ZOK7LZDT.js";
import "./chunk-H3DTEG3J.js";
import {
  SwitchBox
} from "./chunk-4LGSZLHH.js";
import {
  useSalesChannelTableColumns,
  useSalesChannelTableEmptyState,
  useSalesChannelTableFilters,
  useSalesChannelTableQuery
} from "./chunk-MX262YWC.js";
import "./chunk-FSBMOACR.js";
import {
  DataTable
} from "./chunk-GNGA6ZKR.js";
import {
  CategoryCombobox
} from "./chunk-UF5DOWAO.js";
import {
  useComboboxData
} from "./chunk-CFEMRZCK.js";
import {
  Combobox
} from "./chunk-RC2DY4WG.js";
import {
  PRODUCT_CREATE_FORM_DEFAULTS,
  ProductCreateSchema,
  UploadMediaFormItem,
  decorateVariantsWithDefaultValues,
  normalizeProductFormValues
} from "./chunk-2CNQV5MH.js";
import {
  CSS,
  DndContext,
  DragOverlay,
  KeyboardSensor,
  PointerSensor,
  SortableContext,
  arrayMove,
  defaultDropAnimationSideEffects,
  sortableKeyboardCoordinates,
  useSensor,
  useSensors,
  useSortable
} from "./chunk-RVJLUPRX.js";
import "./chunk-7LOZU53L.js";
import "./chunk-EZLR4STK.js";
import "./chunk-EGRHWZRV.js";
import {
  KeyboundForm
} from "./chunk-DP54EP6X.js";
import "./chunk-32T72GVU.js";
import "./chunk-WDXTIEQI.js";
import "./chunk-7ANVLPZR.js";
import "./chunk-2E2FUO6N.js";
import {
  RouteFocusModal,
  StackedFocusModal,
  useRouteModal,
  useStackedModal
} from "./chunk-MVVOBQIC.js";
import {
  FormExtensionZone,
  useExtendableForm
} from "./chunk-BS6QEWFI.js";
import "./chunk-MM7T76RN.js";
import "./chunk-WTOMBGNF.js";
import "./chunk-EGRHWZRV.js";
import "./chunk-EPRCCFRP.js";
import "./chunk-YM3FRBGU.js";
import "./chunk-7M4ICL3D.js";
import "./chunk-RQF55WOK.js";
import "./chunk-5QX4V4M4.js";
import "./chunk-HPGXK5DQ.js";
import "./chunk-IA4ROPJA.js";
import "./chunk-SP3VUFZN.js";
import "./chunk-WHQIBI5S.js";
import "./chunk-4XXECALA.js";
import "./chunk-6GQUHAET.js";
import {
  useExtension
} from "./chunk-FTD3ZWHZ.js";
import "./chunk-YXT43UJF.js";
import "./chunk-EIZ27OVL.js";
import {
  ActionMenu
} from "./chunk-CFRQOB2M.js";
import "./chunk-66DVUN72.js";
import "./chunk-DL4QDYPT.js";
import "./chunk-NV2N3EWM.js";
import "./chunk-BF7OBKIN.js";
import "./chunk-7UAYECTW.js";
import "./chunk-Y3NYV3NU.js";
import "./chunk-MPXR7HT5.js";
import {
  Controller,
  Form,
  useFieldArray,
  useWatch
} from "./chunk-XXJU43CK.js";
import "./chunk-WISME5HP.js";
import "./chunk-WAUSOHFQ.js";
import "./chunk-3ORK3QZ4.js";
import "./chunk-3A5TVVNI.js";
import "./chunk-EZ62MM7J.js";
import "./chunk-3R6LYIXM.js";
import "./chunk-2P2O4TMQ.js";
import "./chunk-KI3HEITX.js";
import "./chunk-2VUCAO7L.js";
import "./chunk-PTIULTKD.js";
import "./chunk-KISXY2LD.js";
import "./chunk-L7C3XPHJ.js";
import "./chunk-T7MG2EIJ.js";
import "./chunk-MER4VYPR.js";
import "./chunk-2AWKOUCD.js";
import "./chunk-YL3TYBFR.js";
import "./chunk-TZWW72YW.js";
import "./chunk-MSQ25CWB.js";
import "./chunk-ONYSAQ5Z.js";
import "./chunk-NIH7SAXN.js";
import "./chunk-UEJFWAFE.js";
import "./chunk-GR55MFKT.js";
import {
  useStore
} from "./chunk-AAFHKNJG.js";
import {
  useRegions
} from "./chunk-7XDBFDTZ.js";
import {
  usePricePreferences
} from "./chunk-3JKGO5XL.js";
import {
  useSalesChannel,
  useSalesChannels
} from "./chunk-OISUTS7G.js";
import {
  useCreateProduct
} from "./chunk-AJYMIHLQ.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import {
  sdk
} from "./chunk-JFCIYOH4.js";
import {
  Trans,
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-5GF3RGIE.js";
import "./chunk-T7YBVUWZ.js";
import {
  Alert,
  Button,
  Checkbox,
  Divider,
  DotsSix,
  Heading,
  Hint,
  IconButton,
  InlineTip,
  Input,
  Label,
  ProgressTabs,
  StackPerspective,
  Text,
  Textarea,
  ThumbnailBadge,
  Trash,
  XMark,
  XMarkMini,
  clx,
  createDataTableColumnHelper,
  toast
} from "./chunk-YP2LLXWB.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-ZSQ362B4.js";
import {
  keepPreviousData
} from "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/product-create-6GX4SVXG.mjs
var import_react = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_react2 = __toESM(require_react(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var import_react3 = __toESM(require_react(), 1);
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime4 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime5 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime6 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime7 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime8 = __toESM(require_jsx_runtime(), 1);
var import_react4 = __toESM(require_react(), 1);
var import_jsx_runtime9 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime10 = __toESM(require_jsx_runtime(), 1);
var import_react5 = __toESM(require_react(), 1);
var import_jsx_runtime11 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime12 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime13 = __toESM(require_jsx_runtime(), 1);
var ProductCreateGeneralSection = ({
  form
}) => {
  const { t } = useTranslation();
  return (0, import_jsx_runtime.jsxs)("div", { id: "general", className: "flex flex-col gap-y-6", children: [
    (0, import_jsx_runtime.jsx)("div", { className: "flex flex-col gap-y-2", children: (0, import_jsx_runtime.jsxs)("div", { className: "grid grid-cols-1 gap-4 md:grid-cols-3", children: [
      (0, import_jsx_runtime.jsx)(
        Form.Field,
        {
          control: form.control,
          name: "title",
          render: ({ field }) => {
            return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
              (0, import_jsx_runtime.jsx)(Form.Label, { children: t("products.fields.title.label") }),
              (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(Input, { ...field, placeholder: t("products.fields.title.placeholder") }) })
            ] });
          }
        }
      ),
      (0, import_jsx_runtime.jsx)(
        Form.Field,
        {
          control: form.control,
          name: "subtitle",
          render: ({ field }) => {
            return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
              (0, import_jsx_runtime.jsx)(Form.Label, { optional: true, children: t("products.fields.subtitle.label") }),
              (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(Input, { ...field, placeholder: t("products.fields.subtitle.placeholder") }) })
            ] });
          }
        }
      ),
      (0, import_jsx_runtime.jsx)(
        Form.Field,
        {
          control: form.control,
          name: "handle",
          render: ({ field }) => {
            return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
              (0, import_jsx_runtime.jsx)(
                Form.Label,
                {
                  tooltip: t("products.fields.handle.tooltip"),
                  optional: true,
                  children: t("fields.handle")
                }
              ),
              (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(HandleInput, { ...field, placeholder: t("products.fields.handle.placeholder") }) })
            ] });
          }
        }
      )
    ] }) }),
    (0, import_jsx_runtime.jsx)(
      Form.Field,
      {
        control: form.control,
        name: "description",
        render: ({ field }) => {
          return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
            (0, import_jsx_runtime.jsx)(Form.Label, { optional: true, children: t("products.fields.description.label") }),
            (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(Textarea, { ...field, placeholder: t("products.fields.description.placeholder") }) })
          ] });
        }
      }
    )
  ] });
};
var dropAnimationConfig = {
  sideEffects: defaultDropAnimationSideEffects({
    styles: {
      active: {
        opacity: "0.4"
      }
    }
  })
};
var ProductCreateMediaSection = ({
  form
}) => {
  const { fields, append, remove } = useFieldArray({
    name: "media",
    control: form.control,
    keyName: "field_id"
  });
  const [activeId, setActiveId] = (0, import_react2.useState)(null);
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates
    })
  );
  const handleDragStart = (event) => {
    setActiveId(event.active.id);
  };
  const handleDragEnd = (event) => {
    setActiveId(null);
    const { active, over } = event;
    if (active.id !== (over == null ? void 0 : over.id)) {
      const oldIndex = fields.findIndex((item) => item.field_id === active.id);
      const newIndex = fields.findIndex((item) => item.field_id === (over == null ? void 0 : over.id));
      form.setValue("media", arrayMove(fields, oldIndex, newIndex), {
        shouldDirty: true,
        shouldTouch: true
      });
    }
  };
  const handleDragCancel = () => {
    setActiveId(null);
  };
  const getOnDelete = (index) => {
    return () => {
      remove(index);
    };
  };
  const getMakeThumbnail = (index) => {
    return () => {
      const newFields = fields.map((field, i) => {
        return {
          ...field,
          isThumbnail: i === index
        };
      });
      form.setValue("media", newFields, {
        shouldDirty: true,
        shouldTouch: true
      });
    };
  };
  const getItemHandlers = (index) => {
    return {
      onDelete: getOnDelete(index),
      onMakeThumbnail: getMakeThumbnail(index)
    };
  };
  return (0, import_jsx_runtime2.jsxs)("div", { id: "media", className: "flex flex-col gap-y-2", children: [
    (0, import_jsx_runtime2.jsx)(UploadMediaFormItem, { form, append, showHint: false }),
    (0, import_jsx_runtime2.jsxs)(
      DndContext,
      {
        sensors,
        onDragEnd: handleDragEnd,
        onDragStart: handleDragStart,
        onDragCancel: handleDragCancel,
        children: [
          (0, import_jsx_runtime2.jsx)(DragOverlay, { dropAnimation: dropAnimationConfig, children: activeId ? (0, import_jsx_runtime2.jsx)(
            MediaGridItemOverlay,
            {
              field: fields.find((m) => m.field_id === activeId)
            }
          ) : null }),
          (0, import_jsx_runtime2.jsx)("ul", { className: "flex flex-col gap-y-2", children: (0, import_jsx_runtime2.jsx)(SortableContext, { items: fields.map((field) => field.field_id), children: fields.map((field, index) => {
            const { onDelete, onMakeThumbnail } = getItemHandlers(index);
            return (0, import_jsx_runtime2.jsx)(
              MediaItem,
              {
                field,
                onDelete,
                onMakeThumbnail
              },
              field.field_id
            );
          }) }) })
        ]
      }
    )
  ] });
};
var MediaItem = ({ field, onDelete, onMakeThumbnail }) => {
  const { t } = useTranslation();
  const {
    attributes,
    listeners,
    setNodeRef,
    setActivatorNodeRef,
    transform,
    transition,
    isDragging
  } = useSortable({ id: field.field_id });
  const style = {
    opacity: isDragging ? 0.4 : void 0,
    transform: CSS.Translate.toString(transform),
    transition
  };
  if (!field.file) {
    return null;
  }
  return (0, import_jsx_runtime2.jsxs)(
    "li",
    {
      className: "bg-ui-bg-component shadow-elevation-card-rest flex items-center justify-between rounded-lg px-3 py-2",
      ref: setNodeRef,
      style,
      children: [
        (0, import_jsx_runtime2.jsxs)("div", { className: "flex items-center gap-x-2", children: [
          (0, import_jsx_runtime2.jsx)(
            IconButton,
            {
              variant: "transparent",
              type: "button",
              size: "small",
              ...attributes,
              ...listeners,
              ref: setActivatorNodeRef,
              className: "cursor-grab touch-none active:cursor-grabbing",
              children: (0, import_jsx_runtime2.jsx)(DotsSix, { className: "text-ui-fg-muted" })
            }
          ),
          (0, import_jsx_runtime2.jsxs)("div", { className: "flex items-center gap-x-3", children: [
            (0, import_jsx_runtime2.jsx)("div", { className: "bg-ui-bg-base h-10 w-[30px] overflow-hidden rounded-md", children: (0, import_jsx_runtime2.jsx)(ThumbnailPreview, { url: field.url }) }),
            (0, import_jsx_runtime2.jsxs)("div", { className: "flex flex-col", children: [
              (0, import_jsx_runtime2.jsx)(Text, { size: "small", leading: "compact", children: field.file.name }),
              (0, import_jsx_runtime2.jsxs)("div", { className: "flex items-center gap-x-1", children: [
                field.isThumbnail && (0, import_jsx_runtime2.jsx)(ThumbnailBadge, {}),
                (0, import_jsx_runtime2.jsx)(
                  Text,
                  {
                    size: "xsmall",
                    leading: "compact",
                    className: "text-ui-fg-subtle",
                    children: formatFileSize(field.file.size)
                  }
                )
              ] })
            ] })
          ] })
        ] }),
        (0, import_jsx_runtime2.jsxs)("div", { className: "flex items-center gap-x-1", children: [
          (0, import_jsx_runtime2.jsx)(
            ActionMenu,
            {
              groups: [
                {
                  actions: [
                    {
                      label: t("products.media.makeThumbnail"),
                      icon: (0, import_jsx_runtime2.jsx)(StackPerspective, {}),
                      onClick: onMakeThumbnail
                    }
                  ]
                },
                {
                  actions: [
                    {
                      icon: (0, import_jsx_runtime2.jsx)(Trash, {}),
                      label: t("actions.delete"),
                      onClick: onDelete
                    }
                  ]
                }
              ]
            }
          ),
          (0, import_jsx_runtime2.jsx)(
            IconButton,
            {
              type: "button",
              size: "small",
              variant: "transparent",
              onClick: onDelete,
              children: (0, import_jsx_runtime2.jsx)(XMark, {})
            }
          )
        ] })
      ]
    }
  );
};
var MediaGridItemOverlay = ({ field }) => {
  var _a, _b;
  return (0, import_jsx_runtime2.jsxs)("li", { className: "bg-ui-bg-component shadow-elevation-card-rest flex items-center justify-between rounded-lg px-3 py-2", children: [
    (0, import_jsx_runtime2.jsxs)("div", { className: "flex items-center gap-x-2", children: [
      (0, import_jsx_runtime2.jsx)(
        IconButton,
        {
          variant: "transparent",
          size: "small",
          className: "cursor-grab touch-none active:cursor-grabbing",
          children: (0, import_jsx_runtime2.jsx)(DotsSix, { className: "text-ui-fg-muted" })
        }
      ),
      (0, import_jsx_runtime2.jsxs)("div", { className: "flex items-center gap-x-3", children: [
        (0, import_jsx_runtime2.jsx)("div", { className: "bg-ui-bg-base h-10 w-[30px] overflow-hidden rounded-md", children: (0, import_jsx_runtime2.jsx)(ThumbnailPreview, { url: field.url }) }),
        (0, import_jsx_runtime2.jsxs)("div", { className: "flex flex-col", children: [
          (0, import_jsx_runtime2.jsx)(Text, { size: "small", leading: "compact", children: (_a = field.file) == null ? void 0 : _a.name }),
          (0, import_jsx_runtime2.jsxs)("div", { className: "flex items-center gap-x-1", children: [
            field.isThumbnail && (0, import_jsx_runtime2.jsx)(ThumbnailBadge, {}),
            (0, import_jsx_runtime2.jsx)(
              Text,
              {
                size: "xsmall",
                leading: "compact",
                className: "text-ui-fg-subtle",
                children: formatFileSize(((_b = field.file) == null ? void 0 : _b.size) ?? 0)
              }
            )
          ] })
        ] })
      ] })
    ] }),
    (0, import_jsx_runtime2.jsxs)("div", { className: "flex items-center gap-x-1", children: [
      (0, import_jsx_runtime2.jsx)(ActionMenu, { groups: [] }),
      (0, import_jsx_runtime2.jsx)(
        IconButton,
        {
          type: "button",
          size: "small",
          variant: "transparent",
          onClick: () => {
          },
          children: (0, import_jsx_runtime2.jsx)(XMark, {})
        }
      )
    ] })
  ] });
};
var ThumbnailPreview = ({ url }) => {
  if (!url) {
    return null;
  }
  return (0, import_jsx_runtime2.jsx)("img", { src: url, alt: "", className: "size-full object-cover object-center" });
};
function formatFileSize(bytes, decimalPlaces = 2) {
  if (bytes === 0) {
    return "0 Bytes";
  }
  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(decimalPlaces)) + " " + sizes[i];
}
var List = ({
  items,
  onChange,
  renderItem
}) => {
  const [active, setActive] = (0, import_react3.useState)(null);
  const [activeItem, activeIndex] = (0, import_react3.useMemo)(() => {
    if (active === null) {
      return [null, null];
    }
    const index = items.findIndex(({ id }) => id === active.id);
    return [items[index], index];
  }, [active, items]);
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates
    })
  );
  const handleDragStart = ({ active: active2 }) => {
    setActive(active2);
  };
  const handleDragEnd = ({ active: active2, over }) => {
    if (over && active2.id !== over.id) {
      const activeIndex2 = items.findIndex(({ id }) => id === active2.id);
      const overIndex = items.findIndex(({ id }) => id === over.id);
      onChange(arrayMove(items, activeIndex2, overIndex));
    }
    setActive(null);
  };
  const handleDragCancel = () => {
    setActive(null);
  };
  return (0, import_jsx_runtime3.jsxs)(
    DndContext,
    {
      sensors,
      onDragStart: handleDragStart,
      onDragEnd: handleDragEnd,
      onDragCancel: handleDragCancel,
      children: [
        (0, import_jsx_runtime3.jsx)(Overlay, { children: activeItem && activeIndex !== null ? renderItem(activeItem, activeIndex) : null }),
        (0, import_jsx_runtime3.jsx)(SortableContext, { items, children: (0, import_jsx_runtime3.jsx)(
          "ul",
          {
            role: "application",
            className: "flex list-inside list-none list-image-none flex-col p-0",
            children: items.map((item, index) => (0, import_jsx_runtime3.jsx)(import_react3.Fragment, { children: renderItem(item, index) }, item.id))
          }
        ) })
      ]
    }
  );
};
var dropAnimationConfig2 = {
  sideEffects: defaultDropAnimationSideEffects({
    styles: {
      active: {
        opacity: "0.4"
      }
    }
  })
};
var Overlay = ({ children }) => {
  return (0, import_jsx_runtime3.jsx)(
    DragOverlay,
    {
      className: "shadow-elevation-card-hover overflow-hidden rounded-md [&>li]:border-b-0",
      dropAnimation: dropAnimationConfig2,
      children
    }
  );
};
var SortableItemContext = (0, import_react3.createContext)(null);
var useSortableItemContext = () => {
  const context = (0, import_react3.useContext)(SortableItemContext);
  if (!context) {
    throw new Error(
      "useSortableItemContext must be used within a SortableItemContext"
    );
  }
  return context;
};
var Item = ({
  id,
  className,
  children
}) => {
  const {
    attributes,
    isDragging,
    listeners,
    setNodeRef,
    setActivatorNodeRef,
    transform,
    transition
  } = useSortable({ id });
  const context = (0, import_react3.useMemo)(
    () => ({
      attributes,
      listeners,
      ref: setActivatorNodeRef,
      isDragging
    }),
    [attributes, listeners, setActivatorNodeRef, isDragging]
  );
  const style = {
    opacity: isDragging ? 0.4 : void 0,
    transform: CSS.Translate.toString(transform),
    transition
  };
  return (0, import_jsx_runtime3.jsx)(SortableItemContext.Provider, { value: context, children: (0, import_jsx_runtime3.jsx)(
    "li",
    {
      className: clx("transition-fg flex flex-1 list-none", className),
      ref: setNodeRef,
      style,
      children
    }
  ) });
};
var DragHandle = () => {
  const { attributes, listeners, ref } = useSortableItemContext();
  return (0, import_jsx_runtime3.jsx)(
    IconButton,
    {
      variant: "transparent",
      size: "small",
      ...attributes,
      ...listeners,
      ref,
      className: "cursor-grab touch-none active:cursor-grabbing",
      children: (0, import_jsx_runtime3.jsx)(DotsSix, { className: "text-ui-fg-muted" })
    }
  );
};
var SortableList = Object.assign(List, {
  Item,
  DragHandle
});
var getPermutations = (data) => {
  if (data.length === 0) {
    return [];
  }
  if (data.length === 1) {
    return data[0].values.map((value) => ({ [data[0].title]: value }));
  }
  const toProcess = data[0];
  const rest = data.slice(1);
  return toProcess.values.flatMap((value) => {
    return getPermutations(rest).map((permutation) => {
      return {
        [toProcess.title]: value,
        ...permutation
      };
    });
  });
};
var getVariantName = (options) => {
  return Object.values(options).join(" / ");
};
var ProductCreateVariantsSection = ({
  form
}) => {
  var _a, _b, _c;
  const { t } = useTranslation();
  const options = useFieldArray({
    control: form.control,
    name: "options"
  });
  const variants = useFieldArray({
    control: form.control,
    name: "variants"
  });
  const watchedAreVariantsEnabled = useWatch({
    control: form.control,
    name: "enable_variants",
    defaultValue: false
  });
  const watchedOptions = useWatch({
    control: form.control,
    name: "options",
    defaultValue: []
  });
  const watchedVariants = useWatch({
    control: form.control,
    name: "variants",
    defaultValue: []
  });
  const showInvalidOptionsMessage = !!((_a = form.formState.errors.options) == null ? void 0 : _a.length);
  const showInvalidVariantsMessage = ((_c = (_b = form.formState.errors.variants) == null ? void 0 : _b.root) == null ? void 0 : _c.message) === "invalid_length";
  const handleOptionValueUpdate = (index, value) => {
    const { isTouched: hasUserSelectedVariants } = form.getFieldState("variants");
    const newOptions = [...watchedOptions];
    newOptions[index].values = value;
    const permutations = getPermutations(newOptions);
    const oldVariants = [...watchedVariants];
    const findMatchingPermutation = (options2) => {
      return permutations.find(
        (permutation) => Object.keys(options2).every((key) => options2[key] === permutation[key])
      );
    };
    const newVariants = oldVariants.reduce((variants2, variant) => {
      const match = findMatchingPermutation(variant.options);
      if (match) {
        variants2.push({
          ...variant,
          title: getVariantName(match),
          options: match
        });
      }
      return variants2;
    }, []);
    const usedPermutations = new Set(
      newVariants.map((variant) => variant.options)
    );
    const unusedPermutations = permutations.filter(
      (permutation) => !usedPermutations.has(permutation)
    );
    unusedPermutations.forEach((permutation) => {
      newVariants.push({
        title: getVariantName(permutation),
        options: permutation,
        should_create: hasUserSelectedVariants ? false : true,
        variant_rank: newVariants.length,
        // NOTE - prepare inventory array here for now so we prevent rendering issue if we append the items later
        inventory: [{ inventory_item_id: "", required_quantity: "" }]
      });
    });
    form.setValue("variants", newVariants);
  };
  const handleRemoveOption = (index) => {
    if (index === 0) {
      return;
    }
    options.remove(index);
    const newOptions = [...watchedOptions];
    newOptions.splice(index, 1);
    const permutations = getPermutations(newOptions);
    const oldVariants = [...watchedVariants];
    const findMatchingPermutation = (options2) => {
      return permutations.find(
        (permutation) => Object.keys(options2).every((key) => options2[key] === permutation[key])
      );
    };
    const newVariants = oldVariants.reduce((variants2, variant) => {
      const match = findMatchingPermutation(variant.options);
      if (match) {
        variants2.push({
          ...variant,
          title: getVariantName(match),
          options: match
        });
      }
      return variants2;
    }, []);
    const usedPermutations = new Set(
      newVariants.map((variant) => variant.options)
    );
    const unusedPermutations = permutations.filter(
      (permutation) => !usedPermutations.has(permutation)
    );
    unusedPermutations.forEach((permutation) => {
      newVariants.push({
        title: getVariantName(permutation),
        options: permutation,
        should_create: false,
        variant_rank: newVariants.length
      });
    });
    form.setValue("variants", newVariants);
  };
  const handleRankChange = (items) => {
    const update = items.map((item, index) => {
      const variant = watchedVariants.find((v) => v.title === item.title);
      return {
        id: item.id,
        ...variant || item,
        variant_rank: index
      };
    });
    variants.replace(update);
  };
  const getCheckboxState = (variants2) => {
    if (variants2.every((variant) => variant.should_create)) {
      return true;
    }
    if (variants2.some((variant) => variant.should_create)) {
      return "indeterminate";
    }
    return false;
  };
  const onCheckboxChange = (value) => {
    switch (value) {
      case true: {
        const update = watchedVariants.map((variant) => {
          return {
            ...variant,
            should_create: true
          };
        });
        form.setValue("variants", update);
        break;
      }
      case false: {
        const update = watchedVariants.map((variant) => {
          return {
            ...variant,
            should_create: false
          };
        });
        form.setValue("variants", decorateVariantsWithDefaultValues(update));
        break;
      }
      case "indeterminate":
        break;
    }
  };
  const createDefaultOptionAndVariant = () => {
    form.setValue("options", [
      {
        title: "Default option",
        values: ["Default option value"]
      }
    ]);
    form.setValue(
      "variants",
      decorateVariantsWithDefaultValues([
        {
          title: "Default variant",
          should_create: true,
          variant_rank: 0,
          options: {
            "Default option": "Default option value"
          },
          inventory: [{ inventory_item_id: "", required_quantity: "" }],
          is_default: true
        }
      ])
    );
  };
  return (0, import_jsx_runtime4.jsxs)("div", { id: "variants", className: "flex flex-col gap-y-8", children: [
    (0, import_jsx_runtime4.jsxs)("div", { className: "flex flex-col gap-y-6", children: [
      (0, import_jsx_runtime4.jsx)(Heading, { level: "h2", children: t("products.create.variants.header") }),
      (0, import_jsx_runtime4.jsx)(
        SwitchBox,
        {
          control: form.control,
          name: "enable_variants",
          label: t("products.create.variants.subHeadingTitle"),
          description: t("products.create.variants.subHeadingDescription"),
          onCheckedChange: (checked) => {
            if (checked) {
              form.setValue("options", [
                {
                  title: "",
                  values: []
                }
              ]);
              form.setValue("variants", []);
            } else {
              createDefaultOptionAndVariant();
            }
          }
        }
      )
    ] }),
    watchedAreVariantsEnabled && (0, import_jsx_runtime4.jsxs)(import_jsx_runtime4.Fragment, { children: [
      (0, import_jsx_runtime4.jsx)("div", { className: "flex flex-col gap-y-6", children: (0, import_jsx_runtime4.jsx)(
        Form.Field,
        {
          control: form.control,
          name: "options",
          render: () => {
            return (0, import_jsx_runtime4.jsx)(Form.Item, { children: (0, import_jsx_runtime4.jsxs)("div", { className: "flex flex-col gap-y-6", children: [
              (0, import_jsx_runtime4.jsxs)("div", { className: "flex items-start justify-between gap-x-4", children: [
                (0, import_jsx_runtime4.jsxs)("div", { className: "flex flex-col", children: [
                  (0, import_jsx_runtime4.jsx)(Form.Label, { children: t("products.create.variants.productOptions.label") }),
                  (0, import_jsx_runtime4.jsx)(Form.Hint, { children: t("products.create.variants.productOptions.hint") })
                ] }),
                (0, import_jsx_runtime4.jsx)(
                  Button,
                  {
                    size: "small",
                    variant: "secondary",
                    type: "button",
                    onClick: () => {
                      options.append({
                        title: "",
                        values: []
                      });
                    },
                    children: t("actions.add")
                  }
                )
              ] }),
              showInvalidOptionsMessage && (0, import_jsx_runtime4.jsx)(Alert, { dismissible: true, variant: "error", children: t("products.create.errors.options") }),
              (0, import_jsx_runtime4.jsx)("ul", { className: "flex flex-col gap-y-4", children: options.fields.map((option, index) => {
                return (0, import_jsx_runtime4.jsxs)(
                  "li",
                  {
                    className: "bg-ui-bg-component shadow-elevation-card-rest grid grid-cols-[1fr_28px] items-center gap-1.5 rounded-xl p-1.5",
                    children: [
                      (0, import_jsx_runtime4.jsxs)("div", { className: "grid grid-cols-[min-content,1fr] items-center gap-1.5", children: [
                        (0, import_jsx_runtime4.jsx)("div", { className: "flex items-center px-2 py-1.5", children: (0, import_jsx_runtime4.jsx)(
                          Label,
                          {
                            size: "xsmall",
                            weight: "plus",
                            className: "text-ui-fg-subtle",
                            htmlFor: `options.${index}.title`,
                            children: t("fields.title")
                          }
                        ) }),
                        (0, import_jsx_runtime4.jsx)(
                          Input,
                          {
                            className: "bg-ui-bg-field-component hover:bg-ui-bg-field-component-hover",
                            ...form.register(
                              `options.${index}.title`
                            ),
                            placeholder: t(
                              "products.fields.options.optionTitlePlaceholder"
                            )
                          }
                        ),
                        (0, import_jsx_runtime4.jsx)("div", { className: "flex items-center px-2 py-1.5", children: (0, import_jsx_runtime4.jsx)(
                          Label,
                          {
                            size: "xsmall",
                            weight: "plus",
                            className: "text-ui-fg-subtle",
                            htmlFor: `options.${index}.values`,
                            children: t("fields.values")
                          }
                        ) }),
                        (0, import_jsx_runtime4.jsx)(
                          Controller,
                          {
                            control: form.control,
                            name: `options.${index}.values`,
                            render: ({
                              field: { onChange, ...field }
                            }) => {
                              const handleValueChange = (value) => {
                                handleOptionValueUpdate(index, value);
                                onChange(value);
                              };
                              return (0, import_jsx_runtime4.jsx)(
                                ChipInput,
                                {
                                  ...field,
                                  variant: "contrast",
                                  onChange: handleValueChange,
                                  placeholder: t(
                                    "products.fields.options.variantionsPlaceholder"
                                  )
                                }
                              );
                            }
                          }
                        )
                      ] }),
                      (0, import_jsx_runtime4.jsx)(
                        IconButton,
                        {
                          type: "button",
                          size: "small",
                          variant: "transparent",
                          className: "text-ui-fg-muted",
                          disabled: index === 0,
                          onClick: () => handleRemoveOption(index),
                          children: (0, import_jsx_runtime4.jsx)(XMarkMini, {})
                        }
                      )
                    ]
                  },
                  option.id
                );
              }) })
            ] }) });
          }
        }
      ) }),
      (0, import_jsx_runtime4.jsx)("div", { className: "grid grid-cols-1 gap-x-4 gap-y-8", children: (0, import_jsx_runtime4.jsxs)("div", { className: "flex flex-col gap-y-6", children: [
        (0, import_jsx_runtime4.jsxs)("div", { className: "flex flex-col", children: [
          (0, import_jsx_runtime4.jsx)(Label, { weight: "plus", children: t("products.create.variants.productVariants.label") }),
          (0, import_jsx_runtime4.jsx)(Hint, { children: t("products.create.variants.productVariants.hint") })
        ] }),
        !showInvalidOptionsMessage && showInvalidVariantsMessage && (0, import_jsx_runtime4.jsx)(Alert, { dismissible: true, variant: "error", children: t("products.create.errors.variants") }),
        variants.fields.length > 0 ? (0, import_jsx_runtime4.jsxs)("div", { className: "overflow-hidden rounded-xl border", children: [
          (0, import_jsx_runtime4.jsxs)(
            "div",
            {
              className: "bg-ui-bg-component text-ui-fg-subtle grid items-center gap-3 border-b px-6 py-2.5",
              style: {
                gridTemplateColumns: `20px 28px repeat(${watchedOptions.length}, 1fr)`
              },
              children: [
                (0, import_jsx_runtime4.jsx)("div", { children: (0, import_jsx_runtime4.jsx)(
                  Checkbox,
                  {
                    className: "relative",
                    checked: getCheckboxState(watchedVariants),
                    onCheckedChange: onCheckboxChange
                  }
                ) }),
                (0, import_jsx_runtime4.jsx)("div", {}),
                watchedOptions.map((option, index) => (0, import_jsx_runtime4.jsx)("div", { children: (0, import_jsx_runtime4.jsx)(Text, { size: "small", leading: "compact", weight: "plus", children: option.title }) }, index))
              ]
            }
          ),
          (0, import_jsx_runtime4.jsx)(
            SortableList,
            {
              items: variants.fields,
              onChange: handleRankChange,
              renderItem: (item, index) => {
                return (0, import_jsx_runtime4.jsx)(
                  SortableList.Item,
                  {
                    id: item.id,
                    className: clx("bg-ui-bg-base border-b", {
                      "border-b-0": index === variants.fields.length - 1
                    }),
                    children: (0, import_jsx_runtime4.jsxs)(
                      "div",
                      {
                        className: "text-ui-fg-subtle grid w-full items-center gap-3 px-6 py-2.5",
                        style: {
                          gridTemplateColumns: `20px 28px repeat(${watchedOptions.length}, 1fr)`
                        },
                        children: [
                          (0, import_jsx_runtime4.jsx)(
                            Form.Field,
                            {
                              control: form.control,
                              name: `variants.${index}.should_create`,
                              render: ({
                                field: { value, onChange, ...field }
                              }) => {
                                return (0, import_jsx_runtime4.jsx)(Form.Item, { children: (0, import_jsx_runtime4.jsx)(Form.Control, { children: (0, import_jsx_runtime4.jsx)(
                                  Checkbox,
                                  {
                                    className: "relative",
                                    ...field,
                                    checked: value,
                                    onCheckedChange: onChange
                                  }
                                ) }) });
                              }
                            }
                          ),
                          (0, import_jsx_runtime4.jsx)(SortableList.DragHandle, {}),
                          Object.values(item.options).map((value, index2) => (0, import_jsx_runtime4.jsx)(Text, { size: "small", leading: "compact", children: value }, index2))
                        ]
                      }
                    )
                  }
                );
              }
            }
          )
        ] }) : (0, import_jsx_runtime4.jsx)(Alert, { children: t("products.create.variants.productVariants.alert") }),
        variants.fields.length > 0 && (0, import_jsx_runtime4.jsx)(InlineTip, { label: t("general.tip"), children: t("products.create.variants.productVariants.tip") })
      ] }) })
    ] })
  ] });
};
var ProductCreateDetailsForm = ({ form }) => {
  const { getFormFields } = useExtension();
  const fields = getFormFields("product", "create", "general");
  return (0, import_jsx_runtime5.jsx)("div", { className: "flex flex-col items-center p-16", children: (0, import_jsx_runtime5.jsxs)("div", { className: "flex w-full max-w-[720px] flex-col gap-y-8", children: [
    (0, import_jsx_runtime5.jsx)(Header, {}),
    (0, import_jsx_runtime5.jsxs)("div", { className: "flex flex-col gap-y-6", children: [
      (0, import_jsx_runtime5.jsx)(ProductCreateGeneralSection, { form }),
      (0, import_jsx_runtime5.jsx)(FormExtensionZone, { fields, form }),
      (0, import_jsx_runtime5.jsx)(ProductCreateMediaSection, { form })
    ] }),
    (0, import_jsx_runtime5.jsx)(Divider, {}),
    (0, import_jsx_runtime5.jsx)(ProductCreateVariantsSection, { form })
  ] }) });
};
var Header = () => {
  const { t } = useTranslation();
  return (0, import_jsx_runtime5.jsx)("div", { className: "flex flex-col", children: (0, import_jsx_runtime5.jsx)(Heading, { children: t("products.create.header") }) });
};
function InventoryItemRow({
  form,
  variantIndex,
  inventoryIndex,
  inventoryItem,
  isItemOptionDisabled,
  onRemove
}) {
  const { t } = useTranslation();
  const items = useComboboxData({
    queryKey: ["inventory_items"],
    defaultValueKey: "id",
    defaultValue: inventoryItem.inventory_item_id,
    // prefetch existing inventory items
    queryFn: (params) => sdk.admin.inventoryItem.list(params),
    getOptions: (data) => data.inventory_items.map((item) => ({
      label: `${item.title} ${item.sku ? `(${item.sku})` : ""}`,
      value: item.id
    }))
  });
  return (0, import_jsx_runtime6.jsxs)(
    "li",
    {
      className: "bg-ui-bg-component shadow-elevation-card-rest grid grid-cols-[1fr_28px] items-center gap-1.5 rounded-xl p-1.5",
      children: [
        (0, import_jsx_runtime6.jsxs)("div", { className: "grid grid-cols-[min-content,1fr] items-center gap-1.5", children: [
          (0, import_jsx_runtime6.jsx)("div", { className: "flex items-center px-2 py-1.5", children: (0, import_jsx_runtime6.jsx)(
            Label,
            {
              size: "xsmall",
              weight: "plus",
              className: "text-ui-fg-subtle",
              htmlFor: `variants.${variantIndex}.inventory.${inventoryIndex}.inventory_item_id`,
              children: t("fields.item")
            }
          ) }),
          (0, import_jsx_runtime6.jsx)(
            Form.Field,
            {
              control: form.control,
              name: `variants.${variantIndex}.inventory.${inventoryIndex}.inventory_item_id`,
              render: ({ field }) => {
                return (0, import_jsx_runtime6.jsx)(Form.Item, { children: (0, import_jsx_runtime6.jsx)(Form.Control, { children: (0, import_jsx_runtime6.jsx)(
                  Combobox,
                  {
                    ...field,
                    options: items.options.map((o) => ({
                      ...o,
                      disabled: isItemOptionDisabled(o, inventoryIndex)
                    })),
                    searchValue: items.searchValue,
                    onBlur: () => items.onSearchValueChange(""),
                    onSearchValueChange: items.onSearchValueChange,
                    fetchNextPage: items.fetchNextPage,
                    className: "bg-ui-bg-field-component hover:bg-ui-bg-field-component-hover",
                    placeholder: t("products.create.inventory.itemPlaceholder")
                  }
                ) }) });
              }
            }
          ),
          (0, import_jsx_runtime6.jsx)("div", { className: "flex items-center px-2 py-1.5", children: (0, import_jsx_runtime6.jsx)(
            Label,
            {
              size: "xsmall",
              weight: "plus",
              className: "text-ui-fg-subtle",
              htmlFor: `variants.${variantIndex}.inventory.${inventoryIndex}.required_quantity`,
              children: t("fields.quantity")
            }
          ) }),
          (0, import_jsx_runtime6.jsx)(
            Form.Field,
            {
              control: form.control,
              name: `variants.${variantIndex}.inventory.${inventoryIndex}.required_quantity`,
              render: ({ field: { onChange, value, ...field } }) => {
                return (0, import_jsx_runtime6.jsxs)(Form.Item, { children: [
                  (0, import_jsx_runtime6.jsx)(Form.Control, { children: (0, import_jsx_runtime6.jsx)(
                    Input,
                    {
                      type: "number",
                      className: "bg-ui-bg-field-component",
                      min: 0,
                      value,
                      onChange: (e) => {
                        const value2 = e.target.value;
                        if (value2 === "") {
                          onChange(null);
                        } else {
                          onChange(Number(value2));
                        }
                      },
                      ...field,
                      placeholder: t(
                        "products.create.inventory.quantityPlaceholder"
                      )
                    }
                  ) }),
                  (0, import_jsx_runtime6.jsx)(Form.ErrorMessage, {})
                ] });
              }
            }
          )
        ] }),
        (0, import_jsx_runtime6.jsx)(
          IconButton,
          {
            type: "button",
            size: "small",
            variant: "transparent",
            className: "text-ui-fg-muted",
            onClick: onRemove,
            children: (0, import_jsx_runtime6.jsx)(XMarkMini, {})
          }
        )
      ]
    },
    inventoryItem.id
  );
}
function VariantSection({ form, variant, index }) {
  const { t } = useTranslation();
  const inventory = useFieldArray({
    control: form.control,
    name: `variants.${index}.inventory`
  });
  const inventoryFormData = useWatch({
    control: form.control,
    name: `variants.${index}.inventory`
  });
  const isItemOptionDisabled = (option, inventoryIndex) => {
    return !!(inventoryFormData == null ? void 0 : inventoryFormData.some(
      (i, index2) => index2 != inventoryIndex && i.inventory_item_id === option.value
    ));
  };
  return (0, import_jsx_runtime6.jsxs)("div", { className: "grid gap-y-4", children: [
    (0, import_jsx_runtime6.jsxs)("div", { className: "flex items-start justify-between gap-x-4", children: [
      (0, import_jsx_runtime6.jsxs)("div", { className: "flex flex-col", children: [
        (0, import_jsx_runtime6.jsx)(Form.Label, { children: variant.title }),
        (0, import_jsx_runtime6.jsx)(Form.Hint, { children: t("products.create.inventory.label") })
      ] }),
      (0, import_jsx_runtime6.jsx)(
        Button,
        {
          size: "small",
          variant: "secondary",
          type: "button",
          onClick: () => {
            inventory.append({
              inventory_item_id: "",
              required_quantity: ""
            });
          },
          children: t("actions.add")
        }
      )
    ] }),
    inventory.fields.map((inventoryItem, inventoryIndex) => (0, import_jsx_runtime6.jsx)(
      InventoryItemRow,
      {
        form,
        variantIndex: index,
        inventoryIndex,
        inventoryItem,
        isItemOptionDisabled,
        onRemove: () => inventory.remove(inventoryIndex)
      },
      inventoryItem.id
    ))
  ] });
}
var ProductCreateInventoryKitSection = ({
  form
}) => {
  const { t } = useTranslation();
  const variants = useFieldArray({
    control: form.control,
    name: "variants"
  });
  return (0, import_jsx_runtime6.jsxs)("div", { id: "organize", className: "flex flex-col gap-y-8", children: [
    (0, import_jsx_runtime6.jsx)(Heading, { children: t("products.create.inventory.heading") }),
    variants.fields.filter((v) => v.inventory_kit).map((variant, variantIndex) => (0, import_jsx_runtime6.jsx)(
      VariantSection,
      {
        form,
        variant,
        index: variantIndex
      },
      variant.id
    ))
  ] });
};
var ProductCreateInventoryKitForm = ({
  form
}) => {
  return (0, import_jsx_runtime7.jsx)("div", { className: "flex flex-col items-center p-16", children: (0, import_jsx_runtime7.jsx)("div", { className: "flex w-full max-w-[720px] flex-col gap-y-8", children: (0, import_jsx_runtime7.jsx)(ProductCreateInventoryKitSection, { form }) }) });
};
var ProductCreateOrganizationSection = ({
  form
}) => {
  const { t } = useTranslation();
  const collections = useComboboxData({
    queryKey: ["product_collections"],
    queryFn: (params) => sdk.admin.productCollection.list(params),
    getOptions: (data) => data.collections.map((collection) => ({
      label: collection.title,
      value: collection.id
    }))
  });
  const types = useComboboxData({
    queryKey: ["product_types"],
    queryFn: (params) => sdk.admin.productType.list(params),
    getOptions: (data) => data.product_types.map((type) => ({
      label: type.value,
      value: type.id
    }))
  });
  const tags = useComboboxData({
    queryKey: ["product_tags"],
    queryFn: (params) => sdk.admin.productTag.list(params),
    getOptions: (data) => data.product_tags.map((tag) => ({
      label: tag.value,
      value: tag.id
    }))
  });
  const shippingProfiles = useComboboxData({
    queryKey: ["shipping_profiles"],
    queryFn: (params) => sdk.admin.shippingProfile.list(params),
    getOptions: (data) => data.shipping_profiles.map((shippingProfile) => ({
      label: shippingProfile.name,
      value: shippingProfile.id
    }))
  });
  const { fields, remove, replace } = useFieldArray({
    control: form.control,
    name: "sales_channels",
    keyName: "key"
  });
  const handleClearAllSalesChannels = () => {
    replace([]);
  };
  return (0, import_jsx_runtime8.jsxs)("div", { id: "organize", className: "flex flex-col gap-y-8", children: [
    (0, import_jsx_runtime8.jsx)(Heading, { children: t("products.organization.header") }),
    (0, import_jsx_runtime8.jsx)(
      SwitchBox,
      {
        control: form.control,
        name: "discountable",
        label: t("products.fields.discountable.label"),
        description: t("products.fields.discountable.hint"),
        optional: true
      }
    ),
    (0, import_jsx_runtime8.jsxs)("div", { className: "grid grid-cols-1 gap-4 md:grid-cols-2", children: [
      (0, import_jsx_runtime8.jsx)(
        Form.Field,
        {
          control: form.control,
          name: "type_id",
          render: ({ field }) => {
            return (0, import_jsx_runtime8.jsxs)(Form.Item, { children: [
              (0, import_jsx_runtime8.jsx)(Form.Label, { optional: true, children: t("products.fields.type.label") }),
              (0, import_jsx_runtime8.jsx)(Form.Control, { children: (0, import_jsx_runtime8.jsx)(
                Combobox,
                {
                  ...field,
                  options: types.options,
                  searchValue: types.searchValue,
                  onSearchValueChange: types.onSearchValueChange,
                  fetchNextPage: types.fetchNextPage
                }
              ) }),
              (0, import_jsx_runtime8.jsx)(Form.ErrorMessage, {})
            ] });
          }
        }
      ),
      (0, import_jsx_runtime8.jsx)(
        Form.Field,
        {
          control: form.control,
          name: "collection_id",
          render: ({ field }) => {
            return (0, import_jsx_runtime8.jsxs)(Form.Item, { children: [
              (0, import_jsx_runtime8.jsx)(Form.Label, { optional: true, children: t("products.fields.collection.label") }),
              (0, import_jsx_runtime8.jsx)(Form.Control, { children: (0, import_jsx_runtime8.jsx)(
                Combobox,
                {
                  ...field,
                  options: collections.options,
                  searchValue: collections.searchValue,
                  onSearchValueChange: collections.onSearchValueChange,
                  fetchNextPage: collections.fetchNextPage
                }
              ) }),
              (0, import_jsx_runtime8.jsx)(Form.ErrorMessage, {})
            ] });
          }
        }
      )
    ] }),
    (0, import_jsx_runtime8.jsxs)("div", { className: "grid grid-cols-1 gap-4 md:grid-cols-2", children: [
      (0, import_jsx_runtime8.jsx)(
        Form.Field,
        {
          control: form.control,
          name: "categories",
          render: ({ field }) => {
            return (0, import_jsx_runtime8.jsxs)(Form.Item, { children: [
              (0, import_jsx_runtime8.jsx)(Form.Label, { optional: true, children: t("products.fields.categories.label") }),
              (0, import_jsx_runtime8.jsx)(Form.Control, { children: (0, import_jsx_runtime8.jsx)(CategoryCombobox, { ...field }) }),
              (0, import_jsx_runtime8.jsx)(Form.ErrorMessage, {})
            ] });
          }
        }
      ),
      (0, import_jsx_runtime8.jsx)(
        Form.Field,
        {
          control: form.control,
          name: "tags",
          render: ({ field }) => {
            return (0, import_jsx_runtime8.jsxs)(Form.Item, { children: [
              (0, import_jsx_runtime8.jsx)(Form.Label, { optional: true, children: t("products.fields.tags.label") }),
              (0, import_jsx_runtime8.jsx)(Form.Control, { children: (0, import_jsx_runtime8.jsx)(
                Combobox,
                {
                  ...field,
                  options: tags.options,
                  searchValue: tags.searchValue,
                  onSearchValueChange: tags.onSearchValueChange,
                  fetchNextPage: tags.fetchNextPage
                }
              ) }),
              (0, import_jsx_runtime8.jsx)(Form.ErrorMessage, {})
            ] });
          }
        }
      )
    ] }),
    (0, import_jsx_runtime8.jsxs)("div", { className: "grid grid-cols-1 gap-4 md:grid-cols-2", children: [
      (0, import_jsx_runtime8.jsxs)("div", { children: [
        (0, import_jsx_runtime8.jsx)(Form.Label, { optional: true, children: t("products.fields.shipping_profile.label") }),
        (0, import_jsx_runtime8.jsx)(Form.Hint, { children: (0, import_jsx_runtime8.jsx)(Trans, { i18nKey: "products.fields.shipping_profile.hint" }) })
      ] }),
      (0, import_jsx_runtime8.jsx)(
        Form.Field,
        {
          control: form.control,
          name: "shipping_profile_id",
          render: ({ field }) => {
            return (0, import_jsx_runtime8.jsxs)(Form.Item, { children: [
              (0, import_jsx_runtime8.jsx)(Form.Control, { children: (0, import_jsx_runtime8.jsx)(
                Combobox,
                {
                  ...field,
                  options: shippingProfiles.options,
                  searchValue: shippingProfiles.searchValue,
                  onSearchValueChange: shippingProfiles.onSearchValueChange,
                  fetchNextPage: shippingProfiles.fetchNextPage
                }
              ) }),
              (0, import_jsx_runtime8.jsx)(Form.ErrorMessage, {})
            ] });
          }
        }
      )
    ] }),
    (0, import_jsx_runtime8.jsx)("div", { className: "grid grid-cols-1 gap-y-4", children: (0, import_jsx_runtime8.jsx)(
      Form.Field,
      {
        control: form.control,
        name: "sales_channels",
        render: () => {
          return (0, import_jsx_runtime8.jsxs)(Form.Item, { children: [
            (0, import_jsx_runtime8.jsxs)("div", { className: "flex items-start justify-between gap-x-4", children: [
              (0, import_jsx_runtime8.jsxs)("div", { children: [
                (0, import_jsx_runtime8.jsx)(Form.Label, { optional: true, children: t("products.fields.sales_channels.label") }),
                (0, import_jsx_runtime8.jsx)(Form.Hint, { children: (0, import_jsx_runtime8.jsx)(Trans, { i18nKey: "products.fields.sales_channels.hint" }) })
              ] }),
              (0, import_jsx_runtime8.jsx)(StackedFocusModal.Trigger, { asChild: true, children: (0, import_jsx_runtime8.jsx)(Button, { size: "small", variant: "secondary", type: "button", children: t("actions.add") }) })
            ] }),
            (0, import_jsx_runtime8.jsx)(Form.Control, { className: "mt-0", children: fields.length > 0 && (0, import_jsx_runtime8.jsx)(
              ChipGroup,
              {
                onClearAll: handleClearAllSalesChannels,
                onRemove: remove,
                className: "py-4",
                children: fields.map((field, index) => (0, import_jsx_runtime8.jsx)(ChipGroup.Chip, { index, children: field.name }, field.key))
              }
            ) })
          ] });
        }
      }
    ) })
  ] });
};
var SC_STACKED_MODAL_ID = "sc";
var PAGE_SIZE = 20;
var ProductCreateSalesChannelStackedModal = ({
  form
}) => {
  const { t } = useTranslation();
  const { getValues, setValue } = form;
  const { setIsOpen, getIsOpen } = useStackedModal();
  const [rowSelection, setRowSelection] = (0, import_react4.useState)(
    {}
  );
  const [state, setState] = (0, import_react4.useState)([]);
  const searchParams = useSalesChannelTableQuery({
    pageSize: PAGE_SIZE,
    prefix: SC_STACKED_MODAL_ID
  });
  const { sales_channels, count, isLoading, isError, error } = useSalesChannels(
    searchParams,
    {
      placeholderData: keepPreviousData
    }
  );
  const open = getIsOpen(SC_STACKED_MODAL_ID);
  (0, import_react4.useEffect)(() => {
    if (!open) {
      return;
    }
    const salesChannels = getValues("sales_channels");
    if (salesChannels) {
      setState(
        salesChannels.map((channel) => ({
          id: channel.id,
          name: channel.name
        }))
      );
      setRowSelection(
        salesChannels.reduce(
          (acc, channel) => ({
            ...acc,
            [channel.id]: true
          }),
          {}
        )
      );
    }
  }, [open, getValues]);
  const onRowSelectionChange = (state2) => {
    const ids = Object.keys(state2);
    const addedIdsSet = new Set(
      ids.filter((id) => state2[id] && !rowSelection[id])
    );
    let addedSalesChannels = [];
    if (addedIdsSet.size > 0) {
      addedSalesChannels = (sales_channels == null ? void 0 : sales_channels.filter((channel) => addedIdsSet.has(channel.id))) ?? [];
    }
    setState((prev) => {
      const filteredPrev = prev.filter((channel) => state2[channel.id]);
      return Array.from(/* @__PURE__ */ new Set([...filteredPrev, ...addedSalesChannels]));
    });
    setRowSelection(state2);
  };
  const handleAdd = () => {
    setValue("sales_channels", state, {
      shouldDirty: true,
      shouldTouch: true
    });
    setIsOpen(SC_STACKED_MODAL_ID, false);
  };
  const filters = useSalesChannelTableFilters();
  const columns = useColumns();
  const emptyState = useSalesChannelTableEmptyState();
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime9.jsxs)(StackedFocusModal.Content, { className: "flex flex-col overflow-hidden", children: [
    (0, import_jsx_runtime9.jsx)(StackedFocusModal.Header, {}),
    (0, import_jsx_runtime9.jsx)(StackedFocusModal.Body, { className: "flex-1 overflow-hidden", children: (0, import_jsx_runtime9.jsx)(
      DataTable,
      {
        data: sales_channels,
        columns,
        filters,
        emptyState,
        rowCount: count,
        pageSize: PAGE_SIZE,
        getRowId: (row) => row.id,
        rowSelection: {
          state: rowSelection,
          onRowSelectionChange
        },
        isLoading,
        layout: "fill",
        prefix: SC_STACKED_MODAL_ID
      }
    ) }),
    (0, import_jsx_runtime9.jsx)(StackedFocusModal.Footer, { children: (0, import_jsx_runtime9.jsxs)("div", { className: "flex items-center justify-end gap-x-2", children: [
      (0, import_jsx_runtime9.jsx)(StackedFocusModal.Close, { asChild: true, children: (0, import_jsx_runtime9.jsx)(Button, { size: "small", variant: "secondary", type: "button", children: t("actions.cancel") }) }),
      (0, import_jsx_runtime9.jsx)(Button, { size: "small", onClick: handleAdd, type: "button", children: t("actions.save") })
    ] }) })
  ] });
};
var columnHelper = createDataTableColumnHelper();
var useColumns = () => {
  const base = useSalesChannelTableColumns();
  return (0, import_react4.useMemo)(() => [columnHelper.select(), ...base], [base]);
};
var ProductCreateOrganizeForm = ({ form }) => {
  const { getFormFields } = useExtension();
  const fields = getFormFields("product", "create", "organize");
  return (0, import_jsx_runtime10.jsxs)(StackedFocusModal, { id: SC_STACKED_MODAL_ID, children: [
    (0, import_jsx_runtime10.jsx)("div", { className: "flex flex-col items-center p-16", children: (0, import_jsx_runtime10.jsxs)("div", { className: "flex w-full max-w-[720px] flex-col gap-y-8", children: [
      (0, import_jsx_runtime10.jsx)(ProductCreateOrganizationSection, { form }),
      (0, import_jsx_runtime10.jsx)(FormExtensionZone, { fields, form })
    ] }) }),
    (0, import_jsx_runtime10.jsx)(ProductCreateSalesChannelStackedModal, { form })
  ] });
};
var ProductCreateVariantsForm = ({
  form,
  regions,
  store,
  pricePreferences
}) => {
  const { setCloseOnEscape } = useRouteModal();
  const currencyCodes = (0, import_react5.useMemo)(
    () => {
      var _a;
      return ((_a = store == null ? void 0 : store.supported_currencies) == null ? void 0 : _a.map((c) => c.currency_code)) || [];
    },
    [store]
  );
  const variants = useWatch({
    control: form.control,
    name: "variants",
    defaultValue: []
  });
  const options = useWatch({
    control: form.control,
    name: "options",
    defaultValue: []
  });
  const columns = useColumns2({
    options,
    currencies: currencyCodes,
    regions,
    pricePreferences
  });
  const variantData = (0, import_react5.useMemo)(() => {
    const ret = [];
    variants.forEach((v, i) => {
      if (v.should_create) {
        ret.push({ ...v, originalIndex: i });
      }
    });
    return ret;
  }, [variants]);
  return (0, import_jsx_runtime11.jsx)("div", { className: "flex size-full flex-col divide-y overflow-hidden", children: (0, import_jsx_runtime11.jsx)(
    DataGrid,
    {
      columns,
      data: variantData,
      state: form,
      onEditingChange: (editing) => setCloseOnEscape(!editing)
    }
  ) });
};
var columnHelper2 = createDataGridHelper();
var useColumns2 = ({
  options,
  currencies = [],
  regions = [],
  pricePreferences = []
}) => {
  const { t } = useTranslation();
  return (0, import_react5.useMemo)(
    () => [
      columnHelper2.column({
        id: "options",
        header: () => (0, import_jsx_runtime11.jsx)("div", { className: "flex size-full items-center overflow-hidden", children: (0, import_jsx_runtime11.jsx)("span", { className: "truncate", children: options.map((o) => o.title).join(" / ") }) }),
        cell: (context) => {
          return (0, import_jsx_runtime11.jsx)(DataGrid.ReadonlyCell, { context, children: options.map((o) => context.row.original.options[o.title]).join(" / ") });
        },
        disableHiding: true
      }),
      columnHelper2.column({
        id: "title",
        name: t("fields.title"),
        header: t("fields.title"),
        field: (context) => `variants.${context.row.original.originalIndex}.title`,
        type: "text",
        cell: (context) => {
          return (0, import_jsx_runtime11.jsx)(DataGrid.TextCell, { context });
        }
      }),
      columnHelper2.column({
        id: "sku",
        name: t("fields.sku"),
        header: t("fields.sku"),
        field: (context) => `variants.${context.row.original.originalIndex}.sku`,
        type: "text",
        cell: (context) => {
          return (0, import_jsx_runtime11.jsx)(DataGrid.TextCell, { context });
        }
      }),
      columnHelper2.column({
        id: "manage_inventory",
        name: t("fields.managedInventory"),
        header: t("fields.managedInventory"),
        field: (context) => `variants.${context.row.original.originalIndex}.manage_inventory`,
        type: "boolean",
        cell: (context) => {
          return (0, import_jsx_runtime11.jsx)(DataGrid.BooleanCell, { context });
        }
      }),
      columnHelper2.column({
        id: "allow_backorder",
        name: t("fields.allowBackorder"),
        header: t("fields.allowBackorder"),
        field: (context) => `variants.${context.row.original.originalIndex}.allow_backorder`,
        type: "boolean",
        cell: (context) => {
          return (0, import_jsx_runtime11.jsx)(DataGrid.BooleanCell, { context });
        }
      }),
      columnHelper2.column({
        id: "inventory_kit",
        name: t("fields.inventoryKit"),
        header: t("fields.inventoryKit"),
        field: (context) => `variants.${context.row.original.originalIndex}.inventory_kit`,
        type: "boolean",
        cell: (context) => {
          return (0, import_jsx_runtime11.jsx)(
            DataGrid.BooleanCell,
            {
              context,
              disabled: !context.row.original.manage_inventory
            }
          );
        }
      }),
      ...createDataGridPriceColumns({
        currencies,
        regions,
        pricePreferences,
        getFieldName: (context, value) => {
          var _a;
          if ((_a = context.column.id) == null ? void 0 : _a.startsWith("currency_prices")) {
            return `variants.${context.row.original.originalIndex}.prices.${value}`;
          }
          return `variants.${context.row.original.originalIndex}.prices.${value}`;
        },
        t
      })
    ],
    [currencies, regions, options, pricePreferences, t]
  );
};
var SAVE_DRAFT_BUTTON = "save-draft-button";
var ProductCreateForm = ({
  defaultChannel,
  regions,
  store,
  pricePreferences
}) => {
  const [tab, setTab] = (0, import_react.useState)(
    "details"
    /* DETAILS */
  );
  const [tabState, setTabState] = (0, import_react.useState)({
    [
      "details"
      /* DETAILS */
    ]: "in-progress",
    [
      "organize"
      /* ORGANIZE */
    ]: "not-started",
    [
      "variants"
      /* VARIANTS */
    ]: "not-started",
    [
      "inventory"
      /* INVENTORY */
    ]: "not-started"
  });
  const { t } = useTranslation();
  const { handleSuccess } = useRouteModal();
  const { getFormConfigs } = useExtension();
  const configs = getFormConfigs("product", "create");
  const form = useExtendableForm({
    defaultValues: {
      ...PRODUCT_CREATE_FORM_DEFAULTS,
      sales_channels: defaultChannel ? [{ id: defaultChannel.id, name: defaultChannel.name }] : []
    },
    schema: ProductCreateSchema,
    configs
  });
  const { mutateAsync, isPending } = useCreateProduct();
  const regionsCurrencyMap = (0, import_react.useMemo)(() => {
    if (!(regions == null ? void 0 : regions.length)) {
      return {};
    }
    return regions.reduce(
      (acc, reg) => {
        acc[reg.id] = reg.currency_code;
        return acc;
      },
      {}
    );
  }, [regions]);
  const watchedVariants = useWatch({
    control: form.control,
    name: "variants"
  });
  const showInventoryTab = (0, import_react.useMemo)(
    () => watchedVariants.some((v) => v.manage_inventory && v.inventory_kit),
    [watchedVariants]
  );
  const handleSubmit = form.handleSubmit(async (values, e) => {
    var _a;
    let isDraftSubmission = false;
    if ((e == null ? void 0 : e.nativeEvent) instanceof SubmitEvent) {
      const submitter = (_a = e == null ? void 0 : e.nativeEvent) == null ? void 0 : _a.submitter;
      isDraftSubmission = submitter.dataset.name === SAVE_DRAFT_BUTTON;
    }
    const media = values.media || [];
    const payload = { ...values, media: void 0 };
    let uploadedMedia = [];
    try {
      if (media.length) {
        const thumbnailReq = media.find((m) => m.isThumbnail);
        const otherMediaReq = media.filter((m) => !m.isThumbnail);
        const fileReqs = [];
        if (thumbnailReq) {
          fileReqs.push(
            sdk.admin.upload.create({ files: [thumbnailReq.file] }).then((r) => r.files.map((f) => ({ ...f, isThumbnail: true })))
          );
        }
        if (otherMediaReq == null ? void 0 : otherMediaReq.length) {
          fileReqs.push(
            sdk.admin.upload.create({
              files: otherMediaReq.map((m) => m.file)
            }).then((r) => r.files.map((f) => ({ ...f, isThumbnail: false })))
          );
        }
        uploadedMedia = (await Promise.all(fileReqs)).flat();
      }
    } catch (error) {
      if (error instanceof Error) {
        toast.error(error.message);
      }
    }
    await mutateAsync(
      normalizeProductFormValues({
        ...payload,
        media: uploadedMedia,
        status: isDraftSubmission ? "draft" : "published",
        regionsCurrencyMap
      }),
      {
        onSuccess: (data) => {
          toast.success(
            t("products.create.successToast", {
              title: data.product.title
            })
          );
          handleSuccess(`../${data.product.id}`);
        },
        onError: (error) => {
          toast.error(error.message);
        }
      }
    );
  });
  const onNext = async (currentTab) => {
    const valid = await form.trigger();
    if (!valid) {
      return;
    }
    if (currentTab === "details") {
      setTab(
        "organize"
        /* ORGANIZE */
      );
    }
    if (currentTab === "organize") {
      setTab(
        "variants"
        /* VARIANTS */
      );
    }
    if (currentTab === "variants") {
      setTab(
        "inventory"
        /* INVENTORY */
      );
    }
  };
  (0, import_react.useEffect)(() => {
    const currentState = { ...tabState };
    if (tab === "details") {
      currentState[
        "details"
        /* DETAILS */
      ] = "in-progress";
    }
    if (tab === "organize") {
      currentState[
        "details"
        /* DETAILS */
      ] = "completed";
      currentState[
        "organize"
        /* ORGANIZE */
      ] = "in-progress";
    }
    if (tab === "variants") {
      currentState[
        "details"
        /* DETAILS */
      ] = "completed";
      currentState[
        "organize"
        /* ORGANIZE */
      ] = "completed";
      currentState[
        "variants"
        /* VARIANTS */
      ] = "in-progress";
    }
    if (tab === "inventory") {
      currentState[
        "details"
        /* DETAILS */
      ] = "completed";
      currentState[
        "organize"
        /* ORGANIZE */
      ] = "completed";
      currentState[
        "variants"
        /* VARIANTS */
      ] = "completed";
      currentState[
        "inventory"
        /* INVENTORY */
      ] = "in-progress";
    }
    setTabState({ ...currentState });
  }, [tab]);
  return (0, import_jsx_runtime12.jsx)(RouteFocusModal.Form, { form, children: (0, import_jsx_runtime12.jsxs)(
    KeyboundForm,
    {
      onKeyDown: (e) => {
        if (e.key === "Enter") {
          if (e.target instanceof HTMLTextAreaElement && !(e.metaKey || e.ctrlKey)) {
            return;
          }
          e.preventDefault();
          if (e.metaKey || e.ctrlKey) {
            if (tab !== "variants") {
              e.preventDefault();
              e.stopPropagation();
              onNext(tab);
              return;
            }
            handleSubmit();
          }
        }
      },
      onSubmit: handleSubmit,
      className: "flex h-full flex-col",
      children: [
        (0, import_jsx_runtime12.jsxs)(
          ProgressTabs,
          {
            value: tab,
            onValueChange: async (tab2) => {
              const valid = await form.trigger();
              if (!valid) {
                return;
              }
              setTab(tab2);
            },
            className: "flex h-full flex-col overflow-hidden",
            children: [
              (0, import_jsx_runtime12.jsx)(RouteFocusModal.Header, { children: (0, import_jsx_runtime12.jsx)("div", { className: "-my-2 w-full border-l", children: (0, import_jsx_runtime12.jsxs)(ProgressTabs.List, { className: "justify-start-start flex w-full items-center", children: [
                (0, import_jsx_runtime12.jsx)(
                  ProgressTabs.Trigger,
                  {
                    status: tabState[
                      "details"
                      /* DETAILS */
                    ],
                    value: "details",
                    className: "max-w-[200px] truncate",
                    children: t("products.create.tabs.details")
                  }
                ),
                (0, import_jsx_runtime12.jsx)(
                  ProgressTabs.Trigger,
                  {
                    status: tabState[
                      "organize"
                      /* ORGANIZE */
                    ],
                    value: "organize",
                    className: "max-w-[200px] truncate",
                    children: t("products.create.tabs.organize")
                  }
                ),
                (0, import_jsx_runtime12.jsx)(
                  ProgressTabs.Trigger,
                  {
                    status: tabState[
                      "variants"
                      /* VARIANTS */
                    ],
                    value: "variants",
                    className: "max-w-[200px] truncate",
                    children: t("products.create.tabs.variants")
                  }
                ),
                showInventoryTab && (0, import_jsx_runtime12.jsx)(
                  ProgressTabs.Trigger,
                  {
                    status: tabState[
                      "inventory"
                      /* INVENTORY */
                    ],
                    value: "inventory",
                    className: "max-w-[200px] truncate",
                    children: t("products.create.tabs.inventory")
                  }
                )
              ] }) }) }),
              (0, import_jsx_runtime12.jsxs)(RouteFocusModal.Body, { className: "size-full overflow-hidden", children: [
                (0, import_jsx_runtime12.jsx)(
                  ProgressTabs.Content,
                  {
                    className: "size-full overflow-y-auto",
                    value: "details",
                    children: (0, import_jsx_runtime12.jsx)(ProductCreateDetailsForm, { form })
                  }
                ),
                (0, import_jsx_runtime12.jsx)(
                  ProgressTabs.Content,
                  {
                    className: "size-full overflow-y-auto",
                    value: "organize",
                    children: (0, import_jsx_runtime12.jsx)(ProductCreateOrganizeForm, { form })
                  }
                ),
                (0, import_jsx_runtime12.jsx)(
                  ProgressTabs.Content,
                  {
                    className: "size-full overflow-y-auto",
                    value: "variants",
                    children: (0, import_jsx_runtime12.jsx)(
                      ProductCreateVariantsForm,
                      {
                        form,
                        store,
                        regions,
                        pricePreferences
                      }
                    )
                  }
                ),
                showInventoryTab && (0, import_jsx_runtime12.jsx)(
                  ProgressTabs.Content,
                  {
                    className: "size-full overflow-y-auto",
                    value: "inventory",
                    children: (0, import_jsx_runtime12.jsx)(ProductCreateInventoryKitForm, { form })
                  }
                )
              ] })
            ]
          }
        ),
        (0, import_jsx_runtime12.jsx)(RouteFocusModal.Footer, { children: (0, import_jsx_runtime12.jsxs)("div", { className: "flex items-center justify-end gap-x-2", children: [
          (0, import_jsx_runtime12.jsx)(RouteFocusModal.Close, { asChild: true, children: (0, import_jsx_runtime12.jsx)(Button, { variant: "secondary", size: "small", children: t("actions.cancel") }) }),
          (0, import_jsx_runtime12.jsx)(
            Button,
            {
              "data-name": SAVE_DRAFT_BUTTON,
              size: "small",
              type: "submit",
              isLoading: isPending,
              className: "whitespace-nowrap",
              children: t("actions.saveAsDraft")
            }
          ),
          (0, import_jsx_runtime12.jsx)(
            PrimaryButton,
            {
              tab,
              next: onNext,
              isLoading: isPending,
              showInventoryTab
            }
          )
        ] }) })
      ]
    }
  ) });
};
var PrimaryButton = ({
  tab,
  next,
  isLoading,
  showInventoryTab
}) => {
  const { t } = useTranslation();
  if (tab === "variants" && !showInventoryTab || tab === "inventory" && showInventoryTab) {
    return (0, import_jsx_runtime12.jsx)(
      Button,
      {
        "data-name": "publish-button",
        type: "submit",
        variant: "primary",
        size: "small",
        isLoading,
        children: t("actions.publish")
      },
      "submit-button"
    );
  }
  return (0, import_jsx_runtime12.jsx)(
    Button,
    {
      type: "button",
      variant: "primary",
      size: "small",
      onClick: () => next(tab),
      children: t("actions.continue")
    },
    "next-button"
  );
};
var ProductCreate = () => {
  const { t } = useTranslation();
  const {
    store,
    isPending: isStorePending,
    isError: isStoreError,
    error: storeError
  } = useStore({
    fields: "+default_sales_channel"
  });
  const {
    sales_channel,
    isPending: isSalesChannelPending,
    isError: isSalesChannelError,
    error: salesChannelError
  } = useSalesChannel(store == null ? void 0 : store.default_sales_channel_id, {
    enabled: !!(store == null ? void 0 : store.default_sales_channel_id)
  });
  const {
    regions,
    isPending: isRegionsPending,
    isError: isRegionsError,
    error: regionsError
  } = useRegions({ limit: 9999 });
  const {
    price_preferences,
    isPending: isPricePreferencesPending,
    isError: isPricePreferencesError,
    error: pricePreferencesError
  } = usePricePreferences({
    limit: 9999
  });
  const ready = !!store && !isStorePending && !!regions && !isRegionsPending && !!sales_channel && !isSalesChannelPending && !!price_preferences && !isPricePreferencesPending;
  if (isStoreError) {
    throw storeError;
  }
  if (isRegionsError) {
    throw regionsError;
  }
  if (isSalesChannelError) {
    throw salesChannelError;
  }
  if (isPricePreferencesError) {
    throw pricePreferencesError;
  }
  return (0, import_jsx_runtime13.jsxs)(RouteFocusModal, { children: [
    (0, import_jsx_runtime13.jsx)(RouteFocusModal.Title, { asChild: true, children: (0, import_jsx_runtime13.jsx)("span", { className: "sr-only", children: t("products.create.title") }) }),
    (0, import_jsx_runtime13.jsx)(RouteFocusModal.Description, { asChild: true, children: (0, import_jsx_runtime13.jsx)("span", { className: "sr-only", children: t("products.create.description") }) }),
    ready && (0, import_jsx_runtime13.jsx)(
      ProductCreateForm,
      {
        defaultChannel: sales_channel,
        store,
        pricePreferences: price_preferences,
        regions
      }
    )
  ] });
};
export {
  ProductCreate as Component
};
//# sourceMappingURL=product-create-6GX4SVXG-AGEP7Q4M.js.map
