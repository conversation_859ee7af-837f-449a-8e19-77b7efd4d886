import {
  DateCell
} from "./chunk-ZJX5R5NM.js";
import {
  TextCell
} from "./chunk-C43B7AQX.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import {
  Badge,
  createColumnHelper
} from "./chunk-YP2LLXWB.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/chunk-R2HUTVC3.mjs
var import_react = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_react2 = __toESM(require_react(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var import_react3 = __toESM(require_react(), 1);
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime4 = __toESM(require_jsx_runtime(), 1);
var columnHelper = createColumnHelper();
var useProductTagTableColumns = () => {
  const { t } = useTranslation();
  return (0, import_react.useMemo)(
    () => [
      columnHelper.accessor("value", {
        header: () => t("fields.value"),
        cell: ({ getValue }) => (0, import_jsx_runtime.jsx)(TextCell, { text: getValue() })
      }),
      columnHelper.accessor("created_at", {
        header: () => t("fields.createdAt"),
        cell: ({ getValue }) => {
          return (0, import_jsx_runtime.jsx)(DateCell, { date: getValue() });
        }
      }),
      columnHelper.accessor("updated_at", {
        header: () => t("fields.updatedAt"),
        cell: ({ getValue }) => {
          return (0, import_jsx_runtime.jsx)(DateCell, { date: getValue() });
        }
      })
    ],
    [t]
  );
};
var columnHelper2 = createColumnHelper();
var useReturnReasonTableColumns = () => {
  return (0, import_react2.useMemo)(
    () => [
      columnHelper2.accessor("value", {
        cell: ({ getValue }) => (0, import_jsx_runtime2.jsx)(Badge, { size: "2xsmall", children: getValue() })
      }),
      columnHelper2.accessor("label", {
        cell: ({ row }) => {
          const { label, description } = row.original;
          return (0, import_jsx_runtime2.jsx)("div", { className: " py-4", children: (0, import_jsx_runtime2.jsxs)("div", { className: "flex h-full w-full flex-col justify-center", children: [
            (0, import_jsx_runtime2.jsx)("span", { className: "truncate font-medium", children: label }),
            (0, import_jsx_runtime2.jsx)("span", { className: "truncate", children: description ? description : "-" })
          ] }) });
        }
      })
    ],
    []
  );
};
var columnHelper3 = createColumnHelper();

export {
  useProductTagTableColumns,
  useReturnReasonTableColumns
};
//# sourceMappingURL=chunk-U3YCCVIX.js.map
