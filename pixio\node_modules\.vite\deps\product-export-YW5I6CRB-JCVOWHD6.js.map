{"version": 3, "sources": ["../../@medusajs/dashboard/dist/product-export-YW5I6CRB.mjs"], "sourcesContent": ["import \"./chunk-GW6TVOAA.mjs\";\nimport \"./chunk-CBSCX7RE.mjs\";\nimport \"./chunk-LT4MVCA7.mjs\";\nimport \"./chunk-A2UMBW3V.mjs\";\nimport \"./chunk-W7625H47.mjs\";\nimport \"./chunk-DLZWPHHO.mjs\";\nimport \"./chunk-LSEYENCI.mjs\";\nimport \"./chunk-FVK4ZYYM.mjs\";\nimport {\n  useProductTableFilters\n} from \"./chunk-FZRIVT5D.mjs\";\nimport {\n  DataTableFilter\n} from \"./chunk-SXYXTC2L.mjs\";\nimport \"./chunk-M3VFKDXJ.mjs\";\nimport \"./chunk-KSV3NQOT.mjs\";\nimport {\n  RouteDrawer,\n  useRouteModal\n} from \"./chunk-4TC5YS65.mjs\";\nimport \"./chunk-NYXYHZE6.mjs\";\nimport \"./chunk-OBQI23QM.mjs\";\nimport \"./chunk-Z5UDPQIH.mjs\";\nimport \"./chunk-KOSCMAIC.mjs\";\nimport \"./chunk-X6DSNTTX.mjs\";\nimport \"./chunk-I6E6CALJ.mjs\";\nimport \"./chunk-B4GODIOW.mjs\";\nimport \"./chunk-F6IJV2I2.mjs\";\nimport \"./chunk-QTCZFYFH.mjs\";\nimport \"./chunk-ENV6YVOM.mjs\";\nimport \"./chunk-PIR2H25N.mjs\";\nimport \"./chunk-RLY2SL5E.mjs\";\nimport \"./chunk-C5LYZZZ5.mjs\";\nimport \"./chunk-2ZKVRTBW.mjs\";\nimport \"./chunk-FO3VP56P.mjs\";\nimport \"./chunk-YS65UGPC.mjs\";\nimport \"./chunk-F6PXCY3N.mjs\";\nimport \"./chunk-3OHH43G6.mjs\";\nimport \"./chunk-G2H6MAK7.mjs\";\nimport \"./chunk-GRT22PE5.mjs\";\nimport \"./chunk-32IQRUVY.mjs\";\nimport \"./chunk-FNYASI54.mjs\";\nimport \"./chunk-FVC7M755.mjs\";\nimport \"./chunk-ZJ3OFMHB.mjs\";\nimport \"./chunk-PNU5HPGY.mjs\";\nimport \"./chunk-V2LANK5S.mjs\";\nimport \"./chunk-QZ6PT4QV.mjs\";\nimport \"./chunk-QL4XKIVL.mjs\";\nimport {\n  useExportProducts\n} from \"./chunk-6I62UDJA.mjs\";\nimport \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-RPUOO7AV.mjs\";\n\n// src/routes/products/product-export/product-export.tsx\nimport { Button, Heading as Heading2, toast } from \"@medusajs/ui\";\nimport { useTranslation as useTranslation2 } from \"react-i18next\";\n\n// src/routes/products/product-export/components/export-filters.tsx\nimport { Heading, Text } from \"@medusajs/ui\";\nimport { useTranslation } from \"react-i18next\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar ExportFilters = () => {\n  const { t } = useTranslation();\n  const filters = useProductTableFilters();\n  return /* @__PURE__ */ jsxs(\"div\", { children: [\n    /* @__PURE__ */ jsx(Heading, { level: \"h2\", children: t(\"products.export.filters.title\") }),\n    /* @__PURE__ */ jsx(Text, { size: \"small\", className: \"text-ui-fg-subtle\", children: t(\"products.export.filters.description\") }),\n    /* @__PURE__ */ jsx(\"div\", { className: \"mt-4\", children: /* @__PURE__ */ jsx(DataTableFilter, { filters, readonly: true }) })\n  ] });\n};\n\n// src/routes/products/product-export/product-export.tsx\nimport { Fragment, jsx as jsx2, jsxs as jsxs2 } from \"react/jsx-runtime\";\nvar ProductExport = () => {\n  const { t } = useTranslation2();\n  return /* @__PURE__ */ jsxs2(RouteDrawer, { children: [\n    /* @__PURE__ */ jsxs2(RouteDrawer.Header, { children: [\n      /* @__PURE__ */ jsx2(RouteDrawer.Title, { asChild: true, children: /* @__PURE__ */ jsx2(Heading2, { children: t(\"products.export.header\") }) }),\n      /* @__PURE__ */ jsx2(RouteDrawer.Description, { className: \"sr-only\", children: t(\"products.export.description\") })\n    ] }),\n    /* @__PURE__ */ jsx2(ProductExportContent, {})\n  ] });\n};\nvar ProductExportContent = () => {\n  const { t } = useTranslation2();\n  const { mutateAsync } = useExportProducts();\n  const { handleSuccess } = useRouteModal();\n  const handleExportRequest = async () => {\n    await mutateAsync(\n      {},\n      {\n        onSuccess: () => {\n          toast.info(t(\"products.export.success.title\"), {\n            description: t(\"products.export.success.description\")\n          });\n          handleSuccess();\n        },\n        onError: (err) => {\n          toast.error(err.message);\n        }\n      }\n    );\n  };\n  return /* @__PURE__ */ jsxs2(Fragment, { children: [\n    /* @__PURE__ */ jsx2(RouteDrawer.Body, { children: /* @__PURE__ */ jsx2(ExportFilters, {}) }),\n    /* @__PURE__ */ jsx2(RouteDrawer.Footer, { children: /* @__PURE__ */ jsxs2(\"div\", { className: \"flex items-center gap-x-2\", children: [\n      /* @__PURE__ */ jsx2(RouteDrawer.Close, { asChild: true, children: /* @__PURE__ */ jsx2(Button, { size: \"small\", variant: \"secondary\", children: t(\"actions.cancel\") }) }),\n      /* @__PURE__ */ jsx2(Button, { onClick: handleExportRequest, size: \"small\", children: t(\"actions.export\") })\n    ] }) })\n  ] });\n};\nexport {\n  ProductExport as Component\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+DA,yBAA0B;AAY1B,IAAAA,sBAAqD;AAXrD,IAAI,gBAAgB,MAAM;AACxB,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,QAAM,UAAU,uBAAuB;AACvC,aAAuB,yBAAK,OAAO,EAAE,UAAU;AAAA,QAC7B,wBAAI,SAAS,EAAE,OAAO,MAAM,UAAU,EAAE,+BAA+B,EAAE,CAAC;AAAA,QAC1E,wBAAI,MAAM,EAAE,MAAM,SAAS,WAAW,qBAAqB,UAAU,EAAE,qCAAqC,EAAE,CAAC;AAAA,QAC/G,wBAAI,OAAO,EAAE,WAAW,QAAQ,cAA0B,wBAAI,iBAAiB,EAAE,SAAS,UAAU,KAAK,CAAC,EAAE,CAAC;AAAA,EAC/H,EAAE,CAAC;AACL;AAIA,IAAI,gBAAgB,MAAM;AACxB,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,aAAuB,oBAAAC,MAAM,aAAa,EAAE,UAAU;AAAA,QACpC,oBAAAA,MAAM,YAAY,QAAQ,EAAE,UAAU;AAAA,UACpC,oBAAAC,KAAK,YAAY,OAAO,EAAE,SAAS,MAAM,cAA0B,oBAAAA,KAAK,SAAU,EAAE,UAAU,EAAE,wBAAwB,EAAE,CAAC,EAAE,CAAC;AAAA,UAC9H,oBAAAA,KAAK,YAAY,aAAa,EAAE,WAAW,WAAW,UAAU,EAAE,6BAA6B,EAAE,CAAC;AAAA,IACpH,EAAE,CAAC;AAAA,QACa,oBAAAA,KAAK,sBAAsB,CAAC,CAAC;AAAA,EAC/C,EAAE,CAAC;AACL;AACA,IAAI,uBAAuB,MAAM;AAC/B,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,QAAM,EAAE,YAAY,IAAI,kBAAkB;AAC1C,QAAM,EAAE,cAAc,IAAI,cAAc;AACxC,QAAM,sBAAsB,YAAY;AACtC,UAAM;AAAA,MACJ,CAAC;AAAA,MACD;AAAA,QACE,WAAW,MAAM;AACf,gBAAM,KAAK,EAAE,+BAA+B,GAAG;AAAA,YAC7C,aAAa,EAAE,qCAAqC;AAAA,UACtD,CAAC;AACD,wBAAc;AAAA,QAChB;AAAA,QACA,SAAS,CAAC,QAAQ;AAChB,gBAAM,MAAM,IAAI,OAAO;AAAA,QACzB;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,aAAuB,oBAAAD,MAAM,8BAAU,EAAE,UAAU;AAAA,QACjC,oBAAAC,KAAK,YAAY,MAAM,EAAE,cAA0B,oBAAAA,KAAK,eAAe,CAAC,CAAC,EAAE,CAAC;AAAA,QAC5E,oBAAAA,KAAK,YAAY,QAAQ,EAAE,cAA0B,oBAAAD,MAAM,OAAO,EAAE,WAAW,6BAA6B,UAAU;AAAA,UACpH,oBAAAC,KAAK,YAAY,OAAO,EAAE,SAAS,MAAM,cAA0B,oBAAAA,KAAK,QAAQ,EAAE,MAAM,SAAS,SAAS,aAAa,UAAU,EAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;AAAA,UACzJ,oBAAAA,KAAK,QAAQ,EAAE,SAAS,qBAAqB,MAAM,SAAS,UAAU,EAAE,gBAAgB,EAAE,CAAC;AAAA,IAC7G,EAAE,CAAC,EAAE,CAAC;AAAA,EACR,EAAE,CAAC;AACL;", "names": ["import_jsx_runtime", "jsxs2", "jsx2"]}