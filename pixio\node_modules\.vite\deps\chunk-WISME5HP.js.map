{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-Z5UDPQIH.mjs"], "sourcesContent": ["import {\n  ordersQueryKeys\n} from \"./chunk-FNYASI54.mjs\";\nimport {\n  queryClient\n} from \"./chunk-FXYH54JP.mjs\";\nimport {\n  queryKeysFactory\n} from \"./chunk-774WSTCC.mjs\";\nimport {\n  sdk\n} from \"./chunk-DEQUVHHE.mjs\";\n\n// src/hooks/api/product-variants.tsx\nimport { useQuery } from \"@tanstack/react-query\";\nvar PRODUCT_VARIANT_QUERY_KEY = \"product_variant\";\nvar productVariantQueryKeys = queryKeysFactory(\n  PRODUCT_VARIANT_QUERY_KEY\n);\nvar useVariants = (query, options) => {\n  const { data, ...rest } = useQuery({\n    queryFn: () => sdk.admin.productVariant.list(query),\n    queryKey: productVariantQueryKeys.list(query),\n    ...options\n  });\n  return { ...data, ...rest };\n};\n\n// src/hooks/api/refund-reasons.tsx\nimport { useQuery as useQuery2 } from \"@tanstack/react-query\";\nvar REFUND_REASON_QUERY_KEY = \"refund-reason\";\nvar refundReasonQueryKeys = queryKeysFactory(REFUND_REASON_QUERY_KEY);\nvar useRefundReasons = (query, options) => {\n  const { data, ...rest } = useQuery2({\n    queryFn: () => sdk.admin.refundReason.list(query),\n    queryKey: [],\n    ...options\n  });\n  return { ...data, ...rest };\n};\n\n// src/hooks/api/tags.tsx\nimport {\n  useMutation,\n  useQuery as useQuery3\n} from \"@tanstack/react-query\";\nvar TAGS_QUERY_KEY = \"tags\";\nvar productTagsQueryKeys = queryKeysFactory(TAGS_QUERY_KEY);\nvar useProductTag = (id, query, options) => {\n  const { data, ...rest } = useQuery3({\n    queryKey: productTagsQueryKeys.detail(id, query),\n    queryFn: async () => sdk.admin.productTag.retrieve(id),\n    ...options\n  });\n  return { ...data, ...rest };\n};\nvar useProductTags = (query, options) => {\n  const { data, ...rest } = useQuery3({\n    queryKey: productTagsQueryKeys.list(query),\n    queryFn: async () => sdk.admin.productTag.list(query),\n    ...options\n  });\n  return { ...data, ...rest };\n};\nvar useCreateProductTag = (query, options) => {\n  return useMutation({\n    mutationFn: async (data) => sdk.admin.productTag.create(data, query),\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries({\n        queryKey: productTagsQueryKeys.lists()\n      });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\nvar useUpdateProductTag = (id, query, options) => {\n  return useMutation({\n    mutationFn: async (data) => sdk.admin.productTag.update(id, data, query),\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries({\n        queryKey: productTagsQueryKeys.lists()\n      });\n      queryClient.invalidateQueries({\n        queryKey: productTagsQueryKeys.detail(data.product_tag.id, query)\n      });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\nvar useDeleteProductTag = (id, options) => {\n  return useMutation({\n    mutationFn: async () => sdk.admin.productTag.delete(id),\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries({\n        queryKey: productTagsQueryKeys.lists()\n      });\n      queryClient.invalidateQueries({\n        queryKey: productTagsQueryKeys.detail(id)\n      });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\n\n// src/hooks/api/fulfillment.tsx\nimport { useMutation as useMutation2 } from \"@tanstack/react-query\";\nvar FULFILLMENTS_QUERY_KEY = \"fulfillments\";\nvar fulfillmentsQueryKeys = queryKeysFactory(FULFILLMENTS_QUERY_KEY);\n\n// src/hooks/api/notification.tsx\nimport { useQuery as useQuery4 } from \"@tanstack/react-query\";\nvar NOTIFICATION_QUERY_KEY = \"notification\";\nvar notificationQueryKeys = queryKeysFactory(NOTIFICATION_QUERY_KEY);\nvar useNotifications = (query, options) => {\n  const { data, ...rest } = useQuery4({\n    queryFn: () => sdk.admin.notification.list(query),\n    queryKey: notificationQueryKeys.list(query),\n    ...options\n  });\n  return { ...data, ...rest };\n};\n\n// src/hooks/api/payment-collections.tsx\nimport { useMutation as useMutation3 } from \"@tanstack/react-query\";\nvar PAYMENT_COLLECTION_QUERY_KEY = \"payment-collection\";\nvar paymentCollectionQueryKeys = queryKeysFactory(\n  PAYMENT_COLLECTION_QUERY_KEY\n);\nvar useMarkPaymentCollectionAsPaid = (orderId, paymentCollectionId, options) => {\n  return useMutation3({\n    mutationFn: (payload) => sdk.admin.paymentCollection.markAsPaid(paymentCollectionId, payload),\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries({\n        queryKey: ordersQueryKeys.details()\n      });\n      queryClient.invalidateQueries({\n        queryKey: ordersQueryKeys.preview(orderId)\n      });\n      queryClient.invalidateQueries({\n        queryKey: paymentCollectionQueryKeys.all\n      });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\n\nexport {\n  notificationQueryKeys,\n  useNotifications,\n  useMarkPaymentCollectionAsPaid,\n  productVariantQueryKeys,\n  useVariants,\n  useRefundReasons,\n  productTagsQueryKeys,\n  useProductTag,\n  useProductTags,\n  useCreateProductTag,\n  useUpdateProductTag,\n  useDeleteProductTag\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAeA,IAAI,4BAA4B;AAChC,IAAI,0BAA0B;AAAA,EAC5B;AACF;AACA,IAAI,cAAc,CAAC,OAAO,YAAY;AACpC,QAAM,EAAE,MAAM,GAAG,KAAK,IAAI,SAAS;AAAA,IACjC,SAAS,MAAM,IAAI,MAAM,eAAe,KAAK,KAAK;AAAA,IAClD,UAAU,wBAAwB,KAAK,KAAK;AAAA,IAC5C,GAAG;AAAA,EACL,CAAC;AACD,SAAO,EAAE,GAAG,MAAM,GAAG,KAAK;AAC5B;AAIA,IAAI,0BAA0B;AAC9B,IAAI,wBAAwB,iBAAiB,uBAAuB;AACpE,IAAI,mBAAmB,CAAC,OAAO,YAAY;AACzC,QAAM,EAAE,MAAM,GAAG,KAAK,IAAI,SAAU;AAAA,IAClC,SAAS,MAAM,IAAI,MAAM,aAAa,KAAK,KAAK;AAAA,IAChD,UAAU,CAAC;AAAA,IACX,GAAG;AAAA,EACL,CAAC;AACD,SAAO,EAAE,GAAG,MAAM,GAAG,KAAK;AAC5B;AAOA,IAAI,iBAAiB;AACrB,IAAI,uBAAuB,iBAAiB,cAAc;AAC1D,IAAI,gBAAgB,CAAC,IAAI,OAAO,YAAY;AAC1C,QAAM,EAAE,MAAM,GAAG,KAAK,IAAI,SAAU;AAAA,IAClC,UAAU,qBAAqB,OAAO,IAAI,KAAK;AAAA,IAC/C,SAAS,YAAY,IAAI,MAAM,WAAW,SAAS,EAAE;AAAA,IACrD,GAAG;AAAA,EACL,CAAC;AACD,SAAO,EAAE,GAAG,MAAM,GAAG,KAAK;AAC5B;AACA,IAAI,iBAAiB,CAAC,OAAO,YAAY;AACvC,QAAM,EAAE,MAAM,GAAG,KAAK,IAAI,SAAU;AAAA,IAClC,UAAU,qBAAqB,KAAK,KAAK;AAAA,IACzC,SAAS,YAAY,IAAI,MAAM,WAAW,KAAK,KAAK;AAAA,IACpD,GAAG;AAAA,EACL,CAAC;AACD,SAAO,EAAE,GAAG,MAAM,GAAG,KAAK;AAC5B;AACA,IAAI,sBAAsB,CAAC,OAAO,YAAY;AAC5C,SAAO,YAAY;AAAA,IACjB,YAAY,OAAO,SAAS,IAAI,MAAM,WAAW,OAAO,MAAM,KAAK;AAAA,IACnE,WAAW,CAAC,MAAM,WAAW,YAAY;AAnE7C;AAoEM,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,qBAAqB,MAAM;AAAA,MACvC,CAAC;AACD,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;AACA,IAAI,sBAAsB,CAAC,IAAI,OAAO,YAAY;AAChD,SAAO,YAAY;AAAA,IACjB,YAAY,OAAO,SAAS,IAAI,MAAM,WAAW,OAAO,IAAI,MAAM,KAAK;AAAA,IACvE,WAAW,CAAC,MAAM,WAAW,YAAY;AA/E7C;AAgFM,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,qBAAqB,MAAM;AAAA,MACvC,CAAC;AACD,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,qBAAqB,OAAO,KAAK,YAAY,IAAI,KAAK;AAAA,MAClE,CAAC;AACD,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;AACA,IAAI,sBAAsB,CAAC,IAAI,YAAY;AACzC,SAAO,YAAY;AAAA,IACjB,YAAY,YAAY,IAAI,MAAM,WAAW,OAAO,EAAE;AAAA,IACtD,WAAW,CAAC,MAAM,WAAW,YAAY;AA9F7C;AA+FM,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,qBAAqB,MAAM;AAAA,MACvC,CAAC;AACD,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,qBAAqB,OAAO,EAAE;AAAA,MAC1C,CAAC;AACD,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;AAIA,IAAI,yBAAyB;AAC7B,IAAI,wBAAwB,iBAAiB,sBAAsB;AAInE,IAAI,yBAAyB;AAC7B,IAAI,wBAAwB,iBAAiB,sBAAsB;AACnE,IAAI,mBAAmB,CAAC,OAAO,YAAY;AACzC,QAAM,EAAE,MAAM,GAAG,KAAK,IAAI,SAAU;AAAA,IAClC,SAAS,MAAM,IAAI,MAAM,aAAa,KAAK,KAAK;AAAA,IAChD,UAAU,sBAAsB,KAAK,KAAK;AAAA,IAC1C,GAAG;AAAA,EACL,CAAC;AACD,SAAO,EAAE,GAAG,MAAM,GAAG,KAAK;AAC5B;AAIA,IAAI,+BAA+B;AACnC,IAAI,6BAA6B;AAAA,EAC/B;AACF;AACA,IAAI,iCAAiC,CAAC,SAAS,qBAAqB,YAAY;AAC9E,SAAO,YAAa;AAAA,IAClB,YAAY,CAAC,YAAY,IAAI,MAAM,kBAAkB,WAAW,qBAAqB,OAAO;AAAA,IAC5F,WAAW,CAAC,MAAM,WAAW,YAAY;AAtI7C;AAuIM,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,gBAAgB,QAAQ;AAAA,MACpC,CAAC;AACD,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,gBAAgB,QAAQ,OAAO;AAAA,MAC3C,CAAC;AACD,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,2BAA2B;AAAA,MACvC,CAAC;AACD,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;", "names": []}