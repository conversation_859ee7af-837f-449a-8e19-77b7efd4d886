import {
  ordersQueryKeys
} from "./chunk-NIH7SAXN.js";
import {
  queryClient
} from "./chunk-VMLNCWLE.js";
import {
  queryKeysFactory
} from "./chunk-CUPZIPFX.js";
import {
  sdk
} from "./chunk-JFCIYOH4.js";
import {
  useMutation,
  useQuery
} from "./chunk-R35JBZ3G.js";

// node_modules/@medusajs/dashboard/dist/chunk-FO3VP56P.mjs
var PAYMENT_QUERY_KEY = "payment";
var paymentQueryKeys = queryKeysFactory(PAYMENT_QUERY_KEY);
var usePaymentProviders = (query, options) => {
  const { data, ...rest } = useQuery({
    queryFn: async () => sdk.admin.payment.listPaymentProviders(query),
    queryKey: [],
    ...options
  });
  return { ...data, ...rest };
};
var useCapturePayment = (orderId, paymentId, options) => {
  return useMutation({
    mutationFn: (payload) => sdk.admin.payment.capture(paymentId, payload),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({
        queryKey: ordersQueryKeys.details()
      });
      queryClient.invalidateQueries({
        queryKey: ordersQueryKeys.preview(orderId)
      });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useRefundPayment = (orderId, paymentId, options) => {
  return useMutation({
    mutationFn: (payload) => sdk.admin.payment.refund(paymentId, payload),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({
        queryKey: ordersQueryKeys.details()
      });
      queryClient.invalidateQueries({
        queryKey: ordersQueryKeys.preview(orderId)
      });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};

export {
  usePaymentProviders,
  useCapturePayment,
  useRefundPayment
};
//# sourceMappingURL=chunk-L7C3XPHJ.js.map
