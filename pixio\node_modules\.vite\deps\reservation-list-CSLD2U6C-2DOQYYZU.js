import {
  DateCell
} from "./chunk-ZJX5R5NM.js";
import "./chunk-EGRHWZRV.js";
import {
  PlaceholderCell
} from "./chunk-S5JEKJNE.js";
import {
  _DataTable,
  useDataTable
} from "./chunk-VCX3BVQR.js";
import "./chunk-VCBFQV64.js";
import "./chunk-QLJZR2JY.js";
import "./chunk-X3TOWPPJ.js";
import {
  SingleColumnPage
} from "./chunk-3LNIL4XX.js";
import {
  useQueryParams
} from "./chunk-32T72GVU.js";
import "./chunk-MX43XOWY.js";
import "./chunk-QX6SXRUW.js";
import "./chunk-7ANVLPZR.js";
import "./chunk-2E2FUO6N.js";
import {
  useExtension
} from "./chunk-FTD3ZWHZ.js";
import "./chunk-YXT43UJF.js";
import {
  ActionMenu
} from "./chunk-CFRQOB2M.js";
import "./chunk-66DVUN72.js";
import "./chunk-DL4QDYPT.js";
import "./chunk-NV2N3EWM.js";
import "./chunk-BF7OBKIN.js";
import "./chunk-7UAYECTW.js";
import "./chunk-Y3NYV3NU.js";
import "./chunk-MPXR7HT5.js";
import {
  useStockLocations
} from "./chunk-ONYSAQ5Z.js";
import {
  useDeleteReservationItem,
  useReservationItems
} from "./chunk-UEJFWAFE.js";
import "./chunk-AJYMIHLQ.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import "./chunk-JFCIYOH4.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-5GF3RGIE.js";
import {
  Link
} from "./chunk-T7YBVUWZ.js";
import {
  Button,
  Container,
  Heading,
  PencilSquare,
  Text,
  Trash,
  createColumnHelper,
  usePrompt
} from "./chunk-YP2LLXWB.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-ZSQ362B4.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/reservation-list-CSLD2U6C.mjs
var import_react = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime4 = __toESM(require_jsx_runtime(), 1);
var ReservationActions = ({
  reservation
}) => {
  const { t } = useTranslation();
  const prompt = usePrompt();
  const { mutateAsync } = useDeleteReservationItem(reservation.id);
  const handleDelete = async () => {
    const res = await prompt({
      title: t("general.areYouSure"),
      description: t("reservations.deleteWarning"),
      confirmText: t("actions.delete"),
      cancelText: t("actions.cancel")
    });
    if (!res) {
      return;
    }
    await mutateAsync();
  };
  return (0, import_jsx_runtime.jsx)(
    ActionMenu,
    {
      groups: [
        {
          actions: [
            {
              label: t("actions.edit"),
              to: `${reservation.id}/edit`,
              icon: (0, import_jsx_runtime.jsx)(PencilSquare, {})
            }
          ]
        },
        {
          actions: [
            {
              label: t("actions.delete"),
              onClick: handleDelete,
              icon: (0, import_jsx_runtime.jsx)(Trash, {})
            }
          ]
        }
      ]
    }
  );
};
var columnHelper = createColumnHelper();
var useReservationTableColumns = () => {
  const { t } = useTranslation();
  return (0, import_react.useMemo)(
    () => [
      columnHelper.accessor("inventory_item", {
        header: t("fields.sku"),
        cell: ({ getValue }) => {
          const inventoryItem = getValue();
          if (!inventoryItem || !inventoryItem.sku) {
            return (0, import_jsx_runtime2.jsx)(PlaceholderCell, {});
          }
          return (0, import_jsx_runtime2.jsx)("div", { className: "flex size-full items-center overflow-hidden", children: (0, import_jsx_runtime2.jsx)("span", { className: "truncate", children: inventoryItem.sku }) });
        }
      }),
      /**
       * TEMP: hide this column until a link is added
       */
      // columnHelper.accessor("line_item", {
      //   header: t("fields.order"),
      //   cell: ({ getValue }) => {
      //     const inventoryItem = getValue()
      //
      //     if (!inventoryItem || !inventoryItem.order?.display_id) {
      //       return <PlaceholderCell />
      //     }
      //
      //     return (
      //       <div className="flex size-full items-center overflow-hidden">
      //         <LinkButton to={`/orders/${inventoryItem.order.id}`}>
      //           <span className="truncate">
      //             #{inventoryItem.order.display_id}
      //           </span>
      //         </LinkButton>
      //       </div>
      //     )
      //   },
      // }),
      columnHelper.accessor("description", {
        header: t("fields.description"),
        cell: ({ getValue }) => {
          const description = getValue();
          if (!description) {
            return (0, import_jsx_runtime2.jsx)(PlaceholderCell, {});
          }
          return (0, import_jsx_runtime2.jsx)("div", { className: "flex size-full items-center overflow-hidden", children: (0, import_jsx_runtime2.jsx)("span", { className: "truncate", children: description }) });
        }
      }),
      columnHelper.accessor("created_at", {
        header: t("fields.created"),
        cell: ({ getValue }) => {
          const created = getValue();
          return (0, import_jsx_runtime2.jsx)(DateCell, { date: created });
        }
      }),
      columnHelper.accessor("quantity", {
        header: () => (0, import_jsx_runtime2.jsx)("div", { className: "flex size-full items-center justify-end overflow-hidden text-right", children: (0, import_jsx_runtime2.jsx)("span", { className: "truncate", children: t("fields.quantity") }) }),
        cell: ({ getValue }) => {
          const quantity = getValue();
          return (0, import_jsx_runtime2.jsx)("div", { className: "flex size-full items-center justify-end overflow-hidden text-right", children: (0, import_jsx_runtime2.jsx)("span", { className: "truncate", children: quantity }) });
        }
      }),
      columnHelper.display({
        id: "actions",
        cell: ({ row }) => {
          const reservation = row.original;
          return (0, import_jsx_runtime2.jsx)(ReservationActions, { reservation });
        }
      })
    ],
    [t]
  );
};
var useReservationTableFilters = () => {
  const { t } = useTranslation();
  const { stock_locations } = useStockLocations({
    limit: 1e3
  });
  const filters = [];
  if (stock_locations) {
    const stockLocationFilter = {
      type: "select",
      options: stock_locations.map((s) => ({
        label: s.name,
        value: s.id
      })),
      key: "location_id",
      searchable: true,
      label: t("fields.location")
    };
    filters.push(stockLocationFilter);
  }
  filters.push({
    type: "date",
    key: "created_at",
    label: t("fields.createdAt")
  });
  return filters;
};
var useReservationTableQuery = ({
  pageSize = 20,
  prefix
}) => {
  const raw = useQueryParams(
    ["location_id", "offset", "created_at", "quantity", "updated_at", "order"],
    prefix
  );
  const { location_id, created_at, updated_at, quantity, offset, ...rest } = raw;
  const searchParams = {
    limit: pageSize,
    offset: offset ? parseInt(offset) : void 0,
    location_id,
    created_at: created_at ? JSON.parse(created_at) : void 0,
    updated_at: updated_at ? JSON.parse(updated_at) : void 0,
    ...rest
  };
  return {
    searchParams,
    raw
  };
};
var PAGE_SIZE = 20;
var ReservationListTable = () => {
  const { t } = useTranslation();
  const { searchParams } = useReservationTableQuery({
    pageSize: PAGE_SIZE
  });
  const { reservations, count, isPending, isError, error } = useReservationItems({
    ...searchParams
  });
  const filters = useReservationTableFilters();
  const columns = useReservationTableColumns();
  const { table } = useDataTable({
    data: reservations || [],
    columns,
    count,
    enablePagination: true,
    getRowId: (row) => row.id,
    pageSize: PAGE_SIZE
  });
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime3.jsxs)(Container, { className: "divide-y p-0", children: [
    (0, import_jsx_runtime3.jsxs)("div", { className: "flex items-center justify-between px-6 py-4", children: [
      (0, import_jsx_runtime3.jsxs)("div", { children: [
        (0, import_jsx_runtime3.jsx)(Heading, { children: t("reservations.domain") }),
        (0, import_jsx_runtime3.jsx)(Text, { className: "text-ui-fg-subtle", size: "small", children: t("reservations.subtitle") })
      ] }),
      (0, import_jsx_runtime3.jsx)(Button, { variant: "secondary", size: "small", asChild: true, children: (0, import_jsx_runtime3.jsx)(Link, { to: "create", children: t("actions.create") }) })
    ] }),
    (0, import_jsx_runtime3.jsx)(
      _DataTable,
      {
        table,
        columns,
        pageSize: PAGE_SIZE,
        count,
        isLoading: isPending,
        filters,
        pagination: true,
        navigateTo: (row) => row.id,
        search: false
      }
    )
  ] });
};
var ReservationList = () => {
  const { getWidgets } = useExtension();
  return (0, import_jsx_runtime4.jsx)(
    SingleColumnPage,
    {
      widgets: {
        before: getWidgets("reservation.list.before"),
        after: getWidgets("reservation.list.after")
      },
      children: (0, import_jsx_runtime4.jsx)(ReservationListTable, {})
    }
  );
};
export {
  ReservationList as Component
};
//# sourceMappingURL=reservation-list-CSLD2U6C-2DOQYYZU.js.map
