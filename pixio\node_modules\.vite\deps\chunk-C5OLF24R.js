import {
  getPromotionStatus
} from "./chunk-ZVJAIKLK.js";
import {
  TextCell,
  TextHeader
} from "./chunk-C43B7AQX.js";
import {
  StatusCell
} from "./chunk-OVCKROM5.js";
import {
  useQueryParams
} from "./chunk-32T72GVU.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import {
  Badge,
  createColumnHelper
} from "./chunk-YP2LLXWB.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/chunk-T45SMQIQ.mjs
var import_react = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var CodeCell = ({ code }) => {
  return (0, import_jsx_runtime.jsx)("div", { className: "flex h-full w-full items-center gap-x-3 overflow-hidden", children: (0, import_jsx_runtime.jsx)(Badge, { size: "2xsmall", className: "truncate", children: code }) });
};
var CodeHeader = ({ text }) => {
  return (0, import_jsx_runtime.jsx)("div", { className: " flex h-full w-full items-center ", children: (0, import_jsx_runtime.jsx)("span", { children: text }) });
};
var StatusCell2 = ({ promotion }) => {
  const [color, text] = getPromotionStatus(promotion);
  return (0, import_jsx_runtime2.jsx)(StatusCell, { color, children: text });
};
var columnHelper = createColumnHelper();
var usePromotionTableColumns = () => {
  const { t } = useTranslation();
  return (0, import_react.useMemo)(
    () => [
      columnHelper.display({
        id: "code",
        header: () => (0, import_jsx_runtime3.jsx)(CodeHeader, { text: t("fields.code") }),
        cell: ({ row }) => (0, import_jsx_runtime3.jsx)(CodeCell, { code: row.original.code })
      }),
      columnHelper.display({
        id: "method",
        header: () => (0, import_jsx_runtime3.jsx)(TextHeader, { text: t("promotions.fields.method") }),
        cell: ({ row }) => {
          const text = row.original.is_automatic ? t("promotions.form.method.automatic.title") : t("promotions.form.method.code.title");
          return (0, import_jsx_runtime3.jsx)(TextCell, { text });
        }
      }),
      columnHelper.display({
        id: "status",
        header: () => (0, import_jsx_runtime3.jsx)(TextHeader, { text: t("fields.status") }),
        cell: ({ row }) => (0, import_jsx_runtime3.jsx)(StatusCell2, { promotion: row.original })
      })
    ],
    [t]
  );
};
var usePromotionTableQuery = ({
  prefix,
  pageSize = 20
}) => {
  const queryObject = useQueryParams(
    ["offset", "q", "created_at", "updated_at"],
    prefix
  );
  const { offset, q, created_at, updated_at } = queryObject;
  const searchParams = {
    limit: pageSize,
    created_at: created_at ? JSON.parse(created_at) : void 0,
    updated_at: updated_at ? JSON.parse(updated_at) : void 0,
    offset: offset ? Number(offset) : 0,
    q
  };
  return {
    searchParams,
    raw: queryObject
  };
};

export {
  usePromotionTableColumns,
  usePromotionTableQuery
};
//# sourceMappingURL=chunk-C5OLF24R.js.map
