{"version": 3, "sources": ["../../@medusajs/dashboard/dist/region-detail-NKX4R7DX.mjs"], "sourcesContent": ["import {\n  useCountries,\n  useCountryTableColumns,\n  useCountryTableQuery\n} from \"./chunk-NOAFLTPV.mjs\";\nimport {\n  ListSummary\n} from \"./chunk-I3VB6NM2.mjs\";\nimport {\n  formatProvider\n} from \"./chunk-IR5DHEKS.mjs\";\nimport \"./chunk-LQTHYS2Z.mjs\";\nimport {\n  _DataTable,\n  useDataTable\n} from \"./chunk-UE6PO4FK.mjs\";\nimport \"./chunk-YEDAFXMB.mjs\";\nimport \"./chunk-AOFGTNG6.mjs\";\nimport \"./chunk-EMIHDNB7.mjs\";\nimport {\n  currencies\n} from \"./chunk-MWVM4TYO.mjs\";\nimport \"./chunk-SXYXTC2L.mjs\";\nimport \"./chunk-M3VFKDXJ.mjs\";\nimport {\n  SectionRow\n} from \"./chunk-LFLGEXIG.mjs\";\nimport {\n  SingleColumnPage\n} from \"./chunk-2RQLKDBF.mjs\";\nimport \"./chunk-C76H5USB.mjs\";\nimport \"./chunk-KSV3NQOT.mjs\";\nimport {\n  useExtension\n} from \"./chunk-C5P5PL3E.mjs\";\nimport {\n  SingleColumnPageSkeleton\n} from \"./chunk-LPEUYMRK.mjs\";\nimport {\n  ActionMenu\n} from \"./chunk-3NJTXRIY.mjs\";\nimport \"./chunk-OC7BQLYI.mjs\";\nimport \"./chunk-NYXYHZE6.mjs\";\nimport {\n  regionsQueryKeys,\n  useDeleteRegion,\n  useRegion,\n  useUpdateRegion\n} from \"./chunk-QZ6PT4QV.mjs\";\nimport {\n  usePricePreferences\n} from \"./chunk-QL4XKIVL.mjs\";\nimport {\n  queryClient\n} from \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport {\n  sdk\n} from \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-RPUOO7AV.mjs\";\n\n// src/routes/regions/region-detail/constants.ts\nvar REGION_DETAIL_FIELDS = \"*payment_providers,*countries,+automatic_taxes\";\n\n// src/routes/regions/region-detail/breadcrumb.tsx\nimport { jsx } from \"react/jsx-runtime\";\nvar RegionDetailBreadcrumb = (props) => {\n  const { id } = props.params || {};\n  const { region } = useRegion(\n    id,\n    {\n      fields: REGION_DETAIL_FIELDS\n    },\n    {\n      initialData: props.data,\n      enabled: Boolean(id)\n    }\n  );\n  if (!region) {\n    return null;\n  }\n  return /* @__PURE__ */ jsx(\"span\", { children: region.name });\n};\n\n// src/routes/regions/region-detail/loader.ts\nvar regionQuery = (id) => ({\n  queryKey: regionsQueryKeys.detail(id),\n  queryFn: async () => sdk.admin.region.retrieve(id, {\n    fields: REGION_DETAIL_FIELDS\n  })\n});\nvar regionLoader = async ({ params }) => {\n  const id = params.id;\n  const query = regionQuery(id);\n  return queryClient.getQueryData(\n    query.queryKey\n  ) ?? await queryClient.fetchQuery(query);\n};\n\n// src/routes/regions/region-detail/region-detail.tsx\nimport { useLoaderData, useParams } from \"react-router-dom\";\n\n// src/routes/regions/region-detail/components/region-country-section/region-country-section.tsx\nimport { PlusMini, Trash } from \"@medusajs/icons\";\nimport { Checkbox, Container, Heading, toast, usePrompt } from \"@medusajs/ui\";\nimport {\n  createColumnHelper\n} from \"@tanstack/react-table\";\nimport { useMemo, useState } from \"react\";\nimport { useTranslation } from \"react-i18next\";\nimport { jsx as jsx2, jsxs } from \"react/jsx-runtime\";\nvar PREFIX = \"c\";\nvar PAGE_SIZE = 10;\nvar RegionCountrySection = ({ region }) => {\n  const { t } = useTranslation();\n  const prompt = usePrompt();\n  const [rowSelection, setRowSelection] = useState({});\n  const { searchParams, raw } = useCountryTableQuery({\n    pageSize: PAGE_SIZE,\n    prefix: PREFIX\n  });\n  const { countries, count } = useCountries({\n    countries: region.countries || [],\n    ...searchParams\n  });\n  const columns = useColumns();\n  const { table } = useDataTable({\n    data: countries || [],\n    columns,\n    count,\n    enablePagination: true,\n    enableRowSelection: true,\n    getRowId: (row) => row.iso_2,\n    pageSize: PAGE_SIZE,\n    rowSelection: {\n      state: rowSelection,\n      updater: setRowSelection\n    },\n    prefix: PREFIX,\n    meta: {\n      region\n    }\n  });\n  const { mutateAsync } = useUpdateRegion(region.id);\n  const handleRemoveCountries = async () => {\n    const ids = Object.keys(rowSelection).filter((k) => rowSelection[k]);\n    const res = await prompt({\n      title: t(\"general.areYouSure\"),\n      description: t(\"regions.removeCountriesWarning\", {\n        count: ids.length\n      }),\n      verificationText: t(\"actions.remove\"),\n      verificationInstruction: t(\"general.typeToConfirm\"),\n      cancelText: t(\"actions.cancel\"),\n      confirmText: t(\"actions.remove\")\n    });\n    if (!res) {\n      return;\n    }\n    const payload = region.countries?.filter((c) => !ids.includes(c.iso_2)).map((c) => c.iso_2);\n    await mutateAsync(\n      {\n        countries: payload\n      },\n      {\n        onSuccess: () => {\n          toast.success(t(\"regions.toast.countries\"));\n        },\n        onError: (e) => {\n          toast.error(e.message);\n        }\n      }\n    );\n  };\n  return /* @__PURE__ */ jsxs(Container, { className: \"divide-y p-0\", children: [\n    /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center justify-between px-6 py-4\", children: [\n      /* @__PURE__ */ jsx2(Heading, { level: \"h2\", children: t(\"fields.countries\") }),\n      /* @__PURE__ */ jsx2(\n        ActionMenu,\n        {\n          groups: [\n            {\n              actions: [\n                {\n                  label: t(\"regions.addCountries\"),\n                  icon: /* @__PURE__ */ jsx2(PlusMini, {}),\n                  to: \"countries/add\"\n                }\n              ]\n            }\n          ]\n        }\n      )\n    ] }),\n    /* @__PURE__ */ jsx2(\n      _DataTable,\n      {\n        table,\n        columns,\n        pageSize: PAGE_SIZE,\n        count,\n        orderBy: [\n          { key: \"display_name\", label: t(\"fields.name\") },\n          { key: \"iso_2\", label: t(\"fields.code\") }\n        ],\n        search: true,\n        pagination: true,\n        queryObject: raw,\n        prefix: PREFIX,\n        commands: [\n          {\n            action: handleRemoveCountries,\n            label: t(\"actions.remove\"),\n            shortcut: \"r\"\n          }\n        ]\n      }\n    )\n  ] });\n};\nvar CountryActions = ({\n  country,\n  region\n}) => {\n  const { t } = useTranslation();\n  const prompt = usePrompt();\n  const { mutateAsync } = useUpdateRegion(region.id);\n  const payload = region.countries?.filter((c) => c.iso_2 !== country.iso_2).map((c) => c.iso_2);\n  const handleRemove = async () => {\n    const res = await prompt({\n      title: t(\"general.areYouSure\"),\n      description: t(\"regions.removeCountryWarning\", {\n        name: country.display_name\n      }),\n      verificationText: country.display_name,\n      verificationInstruction: t(\"general.typeToConfirm\"),\n      cancelText: t(\"actions.cancel\"),\n      confirmText: t(\"actions.remove\")\n    });\n    if (!res) {\n      return;\n    }\n    await mutateAsync(\n      {\n        countries: payload\n      },\n      {\n        onSuccess: () => {\n          toast.success(t(\"regions.toast.countries\"));\n        },\n        onError: (e) => {\n          toast.error(e.message);\n        }\n      }\n    );\n  };\n  return /* @__PURE__ */ jsx2(\n    ActionMenu,\n    {\n      groups: [\n        {\n          actions: [\n            {\n              label: t(\"actions.remove\"),\n              onClick: handleRemove,\n              icon: /* @__PURE__ */ jsx2(Trash, {})\n            }\n          ]\n        }\n      ]\n    }\n  );\n};\nvar columnHelper = createColumnHelper();\nvar useColumns = () => {\n  const base = useCountryTableColumns();\n  return useMemo(\n    () => [\n      columnHelper.display({\n        id: \"select\",\n        header: ({ table }) => {\n          return /* @__PURE__ */ jsx2(\n            Checkbox,\n            {\n              checked: table.getIsSomePageRowsSelected() ? \"indeterminate\" : table.getIsAllPageRowsSelected(),\n              onCheckedChange: (value) => table.toggleAllPageRowsSelected(!!value)\n            }\n          );\n        },\n        cell: ({ row }) => {\n          return /* @__PURE__ */ jsx2(\n            Checkbox,\n            {\n              checked: row.getIsSelected(),\n              onCheckedChange: (value) => row.toggleSelected(!!value),\n              onClick: (e) => {\n                e.stopPropagation();\n              }\n            }\n          );\n        }\n      }),\n      ...base,\n      columnHelper.display({\n        id: \"actions\",\n        cell: ({ row, table }) => {\n          const { region } = table.options.meta;\n          return /* @__PURE__ */ jsx2(CountryActions, { country: row.original, region });\n        }\n      })\n    ],\n    [base]\n  );\n};\n\n// src/routes/regions/region-detail/components/region-general-section/region-general-section.tsx\nimport { PencilSquare, Trash as Trash2 } from \"@medusajs/icons\";\nimport { Badge, Container as Container2, Heading as Heading2, Text, toast as toast2, usePrompt as usePrompt2 } from \"@medusajs/ui\";\nimport { useTranslation as useTranslation2 } from \"react-i18next\";\nimport { useNavigate } from \"react-router-dom\";\nimport { jsx as jsx3, jsxs as jsxs2 } from \"react/jsx-runtime\";\nvar RegionGeneralSection = ({\n  region,\n  pricePreferences\n}) => {\n  const { t } = useTranslation2();\n  const pricePreferenceForRegion = pricePreferences?.find(\n    (preference) => preference.attribute === \"region_id\" && preference.value === region.id\n  );\n  return /* @__PURE__ */ jsxs2(Container2, { className: \"divide-y p-0\", children: [\n    /* @__PURE__ */ jsxs2(\"div\", { className: \"flex items-center justify-between px-6 py-4\", children: [\n      /* @__PURE__ */ jsx3(Heading2, { children: region.name }),\n      /* @__PURE__ */ jsx3(RegionActions, { region })\n    ] }),\n    /* @__PURE__ */ jsx3(\n      SectionRow,\n      {\n        title: t(\"fields.currency\"),\n        value: /* @__PURE__ */ jsxs2(\"div\", { className: \"flex items-center gap-x-2\", children: [\n          /* @__PURE__ */ jsx3(Badge, { size: \"2xsmall\", className: \"uppercase\", children: region.currency_code }),\n          /* @__PURE__ */ jsx3(Text, { size: \"small\", leading: \"compact\", children: currencies[region.currency_code.toUpperCase()].name })\n        ] })\n      }\n    ),\n    /* @__PURE__ */ jsx3(\n      SectionRow,\n      {\n        title: t(\"fields.automaticTaxes\"),\n        value: region.automatic_taxes ? t(\"fields.true\") : t(\"fields.false\")\n      }\n    ),\n    /* @__PURE__ */ jsx3(\n      SectionRow,\n      {\n        title: t(\"fields.taxInclusivePricing\"),\n        value: pricePreferenceForRegion?.is_tax_inclusive ? t(\"fields.true\") : t(\"fields.false\")\n      }\n    ),\n    /* @__PURE__ */ jsx3(\n      SectionRow,\n      {\n        title: t(\"fields.paymentProviders\"),\n        value: /* @__PURE__ */ jsx3(\"div\", { className: \"inline-flex\", children: region.payment_providers?.length ? /* @__PURE__ */ jsx3(\n          ListSummary,\n          {\n            list: region.payment_providers.map((p) => formatProvider(p.id))\n          }\n        ) : \"-\" })\n      }\n    )\n  ] });\n};\nvar RegionActions = ({ region }) => {\n  const navigate = useNavigate();\n  const { t } = useTranslation2();\n  const { mutateAsync } = useDeleteRegion(region.id);\n  const prompt = usePrompt2();\n  const handleDelete = async () => {\n    const res = await prompt({\n      title: t(\"general.areYouSure\"),\n      description: t(\"regions.deleteRegionWarning\", {\n        name: region.name\n      }),\n      verificationText: region.name,\n      verificationInstruction: t(\"general.typeToConfirm\"),\n      confirmText: t(\"actions.delete\"),\n      cancelText: t(\"actions.cancel\")\n    });\n    if (!res) {\n      return;\n    }\n    await mutateAsync(void 0, {\n      onSuccess: () => {\n        toast2.success(t(\"regions.toast.delete\"));\n        navigate(\"/settings/regions\", { replace: true });\n      },\n      onError: (e) => {\n        toast2.error(e.message);\n      }\n    });\n  };\n  return /* @__PURE__ */ jsx3(\n    ActionMenu,\n    {\n      groups: [\n        {\n          actions: [\n            {\n              icon: /* @__PURE__ */ jsx3(PencilSquare, {}),\n              label: t(\"actions.edit\"),\n              to: `/settings/regions/${region.id}/edit`\n            }\n          ]\n        },\n        {\n          actions: [\n            {\n              icon: /* @__PURE__ */ jsx3(Trash2, {}),\n              label: t(\"actions.delete\"),\n              onClick: handleDelete\n            }\n          ]\n        }\n      ]\n    }\n  );\n};\n\n// src/routes/regions/region-detail/region-detail.tsx\nimport { jsx as jsx4, jsxs as jsxs3 } from \"react/jsx-runtime\";\nvar RegionDetail = () => {\n  const initialData = useLoaderData();\n  const { id } = useParams();\n  const {\n    region,\n    isPending: isLoading,\n    isError: isRegionError,\n    error: regionError\n  } = useRegion(\n    id,\n    { fields: REGION_DETAIL_FIELDS },\n    {\n      initialData\n    }\n  );\n  const {\n    price_preferences: pricePreferences,\n    isPending: isLoadingPreferences,\n    isError: isPreferencesError,\n    error: preferencesError\n  } = usePricePreferences(\n    {\n      attribute: \"region_id\",\n      value: id\n    },\n    { enabled: !!region }\n  );\n  const { getWidgets } = useExtension();\n  if (isLoading || isLoadingPreferences || !region) {\n    return /* @__PURE__ */ jsx4(SingleColumnPageSkeleton, { sections: 2, showJSON: true, showMetadata: true });\n  }\n  if (isRegionError) {\n    throw regionError;\n  }\n  if (isPreferencesError) {\n    throw preferencesError;\n  }\n  return /* @__PURE__ */ jsxs3(\n    SingleColumnPage,\n    {\n      widgets: {\n        before: getWidgets(\"region.details.before\"),\n        after: getWidgets(\"region.details.after\")\n      },\n      data: region,\n      showMetadata: true,\n      showJSON: true,\n      children: [\n        /* @__PURE__ */ jsx4(\n          RegionGeneralSection,\n          {\n            region,\n            pricePreferences: pricePreferences ?? []\n          }\n        ),\n        /* @__PURE__ */ jsx4(RegionCountrySection, { region })\n      ]\n    }\n  );\n};\nexport {\n  RegionDetailBreadcrumb as Breadcrumb,\n  RegionDetail as Component,\n  regionLoader as loader\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiEA,yBAAoB;AA2CpB,mBAAkC;AAElC,IAAAA,sBAAkC;AAkNlC,IAAAC,sBAA2C;AA6G3C,IAAAA,sBAA2C;AA/W3C,IAAI,uBAAuB;AAI3B,IAAI,yBAAyB,CAAC,UAAU;AACtC,QAAM,EAAE,GAAG,IAAI,MAAM,UAAU,CAAC;AAChC,QAAM,EAAE,OAAO,IAAI;AAAA,IACjB;AAAA,IACA;AAAA,MACE,QAAQ;AAAA,IACV;AAAA,IACA;AAAA,MACE,aAAa,MAAM;AAAA,MACnB,SAAS,QAAQ,EAAE;AAAA,IACrB;AAAA,EACF;AACA,MAAI,CAAC,QAAQ;AACX,WAAO;AAAA,EACT;AACA,aAAuB,wBAAI,QAAQ,EAAE,UAAU,OAAO,KAAK,CAAC;AAC9D;AAGA,IAAI,cAAc,CAAC,QAAQ;AAAA,EACzB,UAAU,iBAAiB,OAAO,EAAE;AAAA,EACpC,SAAS,YAAY,IAAI,MAAM,OAAO,SAAS,IAAI;AAAA,IACjD,QAAQ;AAAA,EACV,CAAC;AACH;AACA,IAAI,eAAe,OAAO,EAAE,OAAO,MAAM;AACvC,QAAM,KAAK,OAAO;AAClB,QAAM,QAAQ,YAAY,EAAE;AAC5B,SAAO,YAAY;AAAA,IACjB,MAAM;AAAA,EACR,KAAK,MAAM,YAAY,WAAW,KAAK;AACzC;AAcA,IAAI,SAAS;AACb,IAAI,YAAY;AAChB,IAAI,uBAAuB,CAAC,EAAE,OAAO,MAAM;AACzC,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,QAAM,SAAS,UAAU;AACzB,QAAM,CAAC,cAAc,eAAe,QAAI,uBAAS,CAAC,CAAC;AACnD,QAAM,EAAE,cAAc,IAAI,IAAI,qBAAqB;AAAA,IACjD,UAAU;AAAA,IACV,QAAQ;AAAA,EACV,CAAC;AACD,QAAM,EAAE,WAAW,MAAM,IAAI,aAAa;AAAA,IACxC,WAAW,OAAO,aAAa,CAAC;AAAA,IAChC,GAAG;AAAA,EACL,CAAC;AACD,QAAM,UAAU,WAAW;AAC3B,QAAM,EAAE,MAAM,IAAI,aAAa;AAAA,IAC7B,MAAM,aAAa,CAAC;AAAA,IACpB;AAAA,IACA;AAAA,IACA,kBAAkB;AAAA,IAClB,oBAAoB;AAAA,IACpB,UAAU,CAAC,QAAQ,IAAI;AAAA,IACvB,UAAU;AAAA,IACV,cAAc;AAAA,MACZ,OAAO;AAAA,MACP,SAAS;AAAA,IACX;AAAA,IACA,QAAQ;AAAA,IACR,MAAM;AAAA,MACJ;AAAA,IACF;AAAA,EACF,CAAC;AACD,QAAM,EAAE,YAAY,IAAI,gBAAgB,OAAO,EAAE;AACjD,QAAM,wBAAwB,YAAY;AAhJ5C;AAiJI,UAAM,MAAM,OAAO,KAAK,YAAY,EAAE,OAAO,CAAC,MAAM,aAAa,CAAC,CAAC;AACnE,UAAM,MAAM,MAAM,OAAO;AAAA,MACvB,OAAO,EAAE,oBAAoB;AAAA,MAC7B,aAAa,EAAE,kCAAkC;AAAA,QAC/C,OAAO,IAAI;AAAA,MACb,CAAC;AAAA,MACD,kBAAkB,EAAE,gBAAgB;AAAA,MACpC,yBAAyB,EAAE,uBAAuB;AAAA,MAClD,YAAY,EAAE,gBAAgB;AAAA,MAC9B,aAAa,EAAE,gBAAgB;AAAA,IACjC,CAAC;AACD,QAAI,CAAC,KAAK;AACR;AAAA,IACF;AACA,UAAM,WAAU,YAAO,cAAP,mBAAkB,OAAO,CAAC,MAAM,CAAC,IAAI,SAAS,EAAE,KAAK,GAAG,IAAI,CAAC,MAAM,EAAE;AACrF,UAAM;AAAA,MACJ;AAAA,QACE,WAAW;AAAA,MACb;AAAA,MACA;AAAA,QACE,WAAW,MAAM;AACf,gBAAM,QAAQ,EAAE,yBAAyB,CAAC;AAAA,QAC5C;AAAA,QACA,SAAS,CAAC,MAAM;AACd,gBAAM,MAAM,EAAE,OAAO;AAAA,QACvB;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,aAAuB,0BAAK,WAAW,EAAE,WAAW,gBAAgB,UAAU;AAAA,QAC5D,0BAAK,OAAO,EAAE,WAAW,+CAA+C,UAAU;AAAA,UAChF,oBAAAC,KAAK,SAAS,EAAE,OAAO,MAAM,UAAU,EAAE,kBAAkB,EAAE,CAAC;AAAA,UAC9D,oBAAAA;AAAA,QACd;AAAA,QACA;AAAA,UACE,QAAQ;AAAA,YACN;AAAA,cACE,SAAS;AAAA,gBACP;AAAA,kBACE,OAAO,EAAE,sBAAsB;AAAA,kBAC/B,UAAsB,oBAAAA,KAAK,UAAU,CAAC,CAAC;AAAA,kBACvC,IAAI;AAAA,gBACN;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF,EAAE,CAAC;AAAA,QACa,oBAAAA;AAAA,MACd;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,QACA,UAAU;AAAA,QACV;AAAA,QACA,SAAS;AAAA,UACP,EAAE,KAAK,gBAAgB,OAAO,EAAE,aAAa,EAAE;AAAA,UAC/C,EAAE,KAAK,SAAS,OAAO,EAAE,aAAa,EAAE;AAAA,QAC1C;AAAA,QACA,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,QAAQ;AAAA,QACR,UAAU;AAAA,UACR;AAAA,YACE,QAAQ;AAAA,YACR,OAAO,EAAE,gBAAgB;AAAA,YACzB,UAAU;AAAA,UACZ;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;AACA,IAAI,iBAAiB,CAAC;AAAA,EACpB;AAAA,EACA;AACF,MAAM;AA/NN;AAgOE,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,QAAM,SAAS,UAAU;AACzB,QAAM,EAAE,YAAY,IAAI,gBAAgB,OAAO,EAAE;AACjD,QAAM,WAAU,YAAO,cAAP,mBAAkB,OAAO,CAAC,MAAM,EAAE,UAAU,QAAQ,OAAO,IAAI,CAAC,MAAM,EAAE;AACxF,QAAM,eAAe,YAAY;AAC/B,UAAM,MAAM,MAAM,OAAO;AAAA,MACvB,OAAO,EAAE,oBAAoB;AAAA,MAC7B,aAAa,EAAE,gCAAgC;AAAA,QAC7C,MAAM,QAAQ;AAAA,MAChB,CAAC;AAAA,MACD,kBAAkB,QAAQ;AAAA,MAC1B,yBAAyB,EAAE,uBAAuB;AAAA,MAClD,YAAY,EAAE,gBAAgB;AAAA,MAC9B,aAAa,EAAE,gBAAgB;AAAA,IACjC,CAAC;AACD,QAAI,CAAC,KAAK;AACR;AAAA,IACF;AACA,UAAM;AAAA,MACJ;AAAA,QACE,WAAW;AAAA,MACb;AAAA,MACA;AAAA,QACE,WAAW,MAAM;AACf,gBAAM,QAAQ,EAAE,yBAAyB,CAAC;AAAA,QAC5C;AAAA,QACA,SAAS,CAAC,MAAM;AACd,gBAAM,MAAM,EAAE,OAAO;AAAA,QACvB;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,aAAuB,oBAAAA;AAAA,IACrB;AAAA,IACA;AAAA,MACE,QAAQ;AAAA,QACN;AAAA,UACE,SAAS;AAAA,YACP;AAAA,cACE,OAAO,EAAE,gBAAgB;AAAA,cACzB,SAAS;AAAA,cACT,UAAsB,oBAAAA,KAAK,OAAO,CAAC,CAAC;AAAA,YACtC;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAI,eAAe,mBAAmB;AACtC,IAAI,aAAa,MAAM;AACrB,QAAM,OAAO,uBAAuB;AACpC,aAAO;AAAA,IACL,MAAM;AAAA,MACJ,aAAa,QAAQ;AAAA,QACnB,IAAI;AAAA,QACJ,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,qBAAuB,oBAAAA;AAAA,YACrB;AAAA,YACA;AAAA,cACE,SAAS,MAAM,0BAA0B,IAAI,kBAAkB,MAAM,yBAAyB;AAAA,cAC9F,iBAAiB,CAAC,UAAU,MAAM,0BAA0B,CAAC,CAAC,KAAK;AAAA,YACrE;AAAA,UACF;AAAA,QACF;AAAA,QACA,MAAM,CAAC,EAAE,IAAI,MAAM;AACjB,qBAAuB,oBAAAA;AAAA,YACrB;AAAA,YACA;AAAA,cACE,SAAS,IAAI,cAAc;AAAA,cAC3B,iBAAiB,CAAC,UAAU,IAAI,eAAe,CAAC,CAAC,KAAK;AAAA,cACtD,SAAS,CAAC,MAAM;AACd,kBAAE,gBAAgB;AAAA,cACpB;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AAAA,MACD,GAAG;AAAA,MACH,aAAa,QAAQ;AAAA,QACnB,IAAI;AAAA,QACJ,MAAM,CAAC,EAAE,KAAK,MAAM,MAAM;AACxB,gBAAM,EAAE,OAAO,IAAI,MAAM,QAAQ;AACjC,qBAAuB,oBAAAA,KAAK,gBAAgB,EAAE,SAAS,IAAI,UAAU,OAAO,CAAC;AAAA,QAC/E;AAAA,MACF,CAAC;AAAA,IACH;AAAA,IACA,CAAC,IAAI;AAAA,EACP;AACF;AAQA,IAAI,uBAAuB,CAAC;AAAA,EAC1B;AAAA,EACA;AACF,MAAM;AApUN;AAqUE,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,QAAM,2BAA2B,qDAAkB;AAAA,IACjD,CAAC,eAAe,WAAW,cAAc,eAAe,WAAW,UAAU,OAAO;AAAA;AAEtF,aAAuB,oBAAAC,MAAM,WAAY,EAAE,WAAW,gBAAgB,UAAU;AAAA,QAC9D,oBAAAA,MAAM,OAAO,EAAE,WAAW,+CAA+C,UAAU;AAAA,UACjF,oBAAAC,KAAK,SAAU,EAAE,UAAU,OAAO,KAAK,CAAC;AAAA,UACxC,oBAAAA,KAAK,eAAe,EAAE,OAAO,CAAC;AAAA,IAChD,EAAE,CAAC;AAAA,QACa,oBAAAA;AAAA,MACd;AAAA,MACA;AAAA,QACE,OAAO,EAAE,iBAAiB;AAAA,QAC1B,WAAuB,oBAAAD,MAAM,OAAO,EAAE,WAAW,6BAA6B,UAAU;AAAA,cACtE,oBAAAC,KAAK,OAAO,EAAE,MAAM,WAAW,WAAW,aAAa,UAAU,OAAO,cAAc,CAAC;AAAA,cACvF,oBAAAA,KAAK,MAAM,EAAE,MAAM,SAAS,SAAS,WAAW,UAAU,WAAW,OAAO,cAAc,YAAY,CAAC,EAAE,KAAK,CAAC;AAAA,QACjI,EAAE,CAAC;AAAA,MACL;AAAA,IACF;AAAA,QACgB,oBAAAA;AAAA,MACd;AAAA,MACA;AAAA,QACE,OAAO,EAAE,uBAAuB;AAAA,QAChC,OAAO,OAAO,kBAAkB,EAAE,aAAa,IAAI,EAAE,cAAc;AAAA,MACrE;AAAA,IACF;AAAA,QACgB,oBAAAA;AAAA,MACd;AAAA,MACA;AAAA,QACE,OAAO,EAAE,4BAA4B;AAAA,QACrC,QAAO,qEAA0B,oBAAmB,EAAE,aAAa,IAAI,EAAE,cAAc;AAAA,MACzF;AAAA,IACF;AAAA,QACgB,oBAAAA;AAAA,MACd;AAAA,MACA;AAAA,QACE,OAAO,EAAE,yBAAyB;AAAA,QAClC,WAAuB,oBAAAA,KAAK,OAAO,EAAE,WAAW,eAAe,YAAU,YAAO,sBAAP,mBAA0B,cAAyB,oBAAAA;AAAA,UAC1H;AAAA,UACA;AAAA,YACE,MAAM,OAAO,kBAAkB,IAAI,CAAC,MAAM,eAAe,EAAE,EAAE,CAAC;AAAA,UAChE;AAAA,QACF,IAAI,IAAI,CAAC;AAAA,MACX;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;AACA,IAAI,gBAAgB,CAAC,EAAE,OAAO,MAAM;AAClC,QAAM,WAAW,YAAY;AAC7B,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,QAAM,EAAE,YAAY,IAAI,gBAAgB,OAAO,EAAE;AACjD,QAAM,SAAS,UAAW;AAC1B,QAAM,eAAe,YAAY;AAC/B,UAAM,MAAM,MAAM,OAAO;AAAA,MACvB,OAAO,EAAE,oBAAoB;AAAA,MAC7B,aAAa,EAAE,+BAA+B;AAAA,QAC5C,MAAM,OAAO;AAAA,MACf,CAAC;AAAA,MACD,kBAAkB,OAAO;AAAA,MACzB,yBAAyB,EAAE,uBAAuB;AAAA,MAClD,aAAa,EAAE,gBAAgB;AAAA,MAC/B,YAAY,EAAE,gBAAgB;AAAA,IAChC,CAAC;AACD,QAAI,CAAC,KAAK;AACR;AAAA,IACF;AACA,UAAM,YAAY,QAAQ;AAAA,MACxB,WAAW,MAAM;AACf,cAAO,QAAQ,EAAE,sBAAsB,CAAC;AACxC,iBAAS,qBAAqB,EAAE,SAAS,KAAK,CAAC;AAAA,MACjD;AAAA,MACA,SAAS,CAAC,MAAM;AACd,cAAO,MAAM,EAAE,OAAO;AAAA,MACxB;AAAA,IACF,CAAC;AAAA,EACH;AACA,aAAuB,oBAAAA;AAAA,IACrB;AAAA,IACA;AAAA,MACE,QAAQ;AAAA,QACN;AAAA,UACE,SAAS;AAAA,YACP;AAAA,cACE,UAAsB,oBAAAA,KAAK,cAAc,CAAC,CAAC;AAAA,cAC3C,OAAO,EAAE,cAAc;AAAA,cACvB,IAAI,qBAAqB,OAAO,EAAE;AAAA,YACpC;AAAA,UACF;AAAA,QACF;AAAA,QACA;AAAA,UACE,SAAS;AAAA,YACP;AAAA,cACE,UAAsB,oBAAAA,KAAK,OAAQ,CAAC,CAAC;AAAA,cACrC,OAAO,EAAE,gBAAgB;AAAA,cACzB,SAAS;AAAA,YACX;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAIA,IAAI,eAAe,MAAM;AACvB,QAAM,cAAc,cAAc;AAClC,QAAM,EAAE,GAAG,IAAI,UAAU;AACzB,QAAM;AAAA,IACJ;AAAA,IACA,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT,IAAI;AAAA,IACF;AAAA,IACA,EAAE,QAAQ,qBAAqB;AAAA,IAC/B;AAAA,MACE;AAAA,IACF;AAAA,EACF;AACA,QAAM;AAAA,IACJ,mBAAmB;AAAA,IACnB,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT,IAAI;AAAA,IACF;AAAA,MACE,WAAW;AAAA,MACX,OAAO;AAAA,IACT;AAAA,IACA,EAAE,SAAS,CAAC,CAAC,OAAO;AAAA,EACtB;AACA,QAAM,EAAE,WAAW,IAAI,aAAa;AACpC,MAAI,aAAa,wBAAwB,CAAC,QAAQ;AAChD,eAAuB,oBAAAC,KAAK,0BAA0B,EAAE,UAAU,GAAG,UAAU,MAAM,cAAc,KAAK,CAAC;AAAA,EAC3G;AACA,MAAI,eAAe;AACjB,UAAM;AAAA,EACR;AACA,MAAI,oBAAoB;AACtB,UAAM;AAAA,EACR;AACA,aAAuB,oBAAAC;AAAA,IACrB;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP,QAAQ,WAAW,uBAAuB;AAAA,QAC1C,OAAO,WAAW,sBAAsB;AAAA,MAC1C;AAAA,MACA,MAAM;AAAA,MACN,cAAc;AAAA,MACd,UAAU;AAAA,MACV,UAAU;AAAA,YACQ,oBAAAD;AAAA,UACd;AAAA,UACA;AAAA,YACE;AAAA,YACA,kBAAkB,oBAAoB,CAAC;AAAA,UACzC;AAAA,QACF;AAAA,YACgB,oBAAAA,KAAK,sBAAsB,EAAE,OAAO,CAAC;AAAA,MACvD;AAAA,IACF;AAAA,EACF;AACF;", "names": ["import_jsx_runtime", "import_jsx_runtime", "jsx2", "jsxs2", "jsx3", "jsx4", "jsxs3"]}