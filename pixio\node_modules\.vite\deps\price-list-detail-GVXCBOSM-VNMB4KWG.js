import {
  ListSummary
} from "./chunk-IP6YNP6I.js";
import {
  useDeletePriceListAction
} from "./chunk-DF2KKW2F.js";
import {
  getPriceListStatus
} from "./chunk-DC7EOWEK.js";
import "./chunk-EG6IR476.js";
import {
  DateRangeDisplay
} from "./chunk-ZANDFM5G.js";
import {
  useProductTableColumns
} from "./chunk-7WCGWU4N.js";
import {
  useProductTableQuery
} from "./chunk-3M3PHA2D.js";
import "./chunk-NVCSASGM.js";
import "./chunk-OVCKROM5.js";
import "./chunk-EGRHWZRV.js";
import "./chunk-S5JEKJNE.js";
import {
  _DataTable,
  useDataTable
} from "./chunk-VCX3BVQR.js";
import "./chunk-VCBFQV64.js";
import "./chunk-QLJZR2JY.js";
import "./chunk-X3TOWPPJ.js";
import {
  TwoColumnPage
} from "./chunk-3LNIL4XX.js";
import "./chunk-EZLR4STK.js";
import "./chunk-32T72GVU.js";
import {
  useProductTableFilters
} from "./chunk-4VJTA5FM.js";
import "./chunk-MX43XOWY.js";
import "./chunk-QX6SXRUW.js";
import "./chunk-7ANVLPZR.js";
import "./chunk-2E2FUO6N.js";
import "./chunk-6GQUHAET.js";
import {
  useExtension
} from "./chunk-FTD3ZWHZ.js";
import {
  Skeleton,
  TwoColumnPageSkeleton
} from "./chunk-YXT43UJF.js";
import {
  ActionMenu
} from "./chunk-CFRQOB2M.js";
import "./chunk-66DVUN72.js";
import "./chunk-DL4QDYPT.js";
import "./chunk-NV2N3EWM.js";
import "./chunk-BF7OBKIN.js";
import "./chunk-7UAYECTW.js";
import "./chunk-Y3NYV3NU.js";
import "./chunk-MPXR7HT5.js";
import "./chunk-WISME5HP.js";
import "./chunk-WAUSOHFQ.js";
import "./chunk-3ORK3QZ4.js";
import "./chunk-3A5TVVNI.js";
import "./chunk-EZ62MM7J.js";
import "./chunk-3R6LYIXM.js";
import "./chunk-2P2O4TMQ.js";
import "./chunk-KI3HEITX.js";
import "./chunk-2VUCAO7L.js";
import "./chunk-PTIULTKD.js";
import "./chunk-KISXY2LD.js";
import "./chunk-L7C3XPHJ.js";
import "./chunk-T7MG2EIJ.js";
import {
  priceListsQueryKeys,
  usePriceList,
  usePriceListLinkProducts
} from "./chunk-MER4VYPR.js";
import {
  useCustomerGroups
} from "./chunk-2AWKOUCD.js";
import "./chunk-YL3TYBFR.js";
import "./chunk-TZWW72YW.js";
import "./chunk-MSQ25CWB.js";
import "./chunk-ONYSAQ5Z.js";
import "./chunk-NIH7SAXN.js";
import "./chunk-UEJFWAFE.js";
import "./chunk-GR55MFKT.js";
import "./chunk-AAFHKNJG.js";
import "./chunk-7XDBFDTZ.js";
import "./chunk-3JKGO5XL.js";
import "./chunk-OISUTS7G.js";
import {
  useProducts
} from "./chunk-AJYMIHLQ.js";
import {
  queryClient
} from "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import {
  sdk
} from "./chunk-JFCIYOH4.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-5GF3RGIE.js";
import {
  useNavigate,
  useParams
} from "./chunk-T7YBVUWZ.js";
import {
  Checkbox,
  Container,
  Heading,
  PencilSquare,
  Plus,
  StatusBadge,
  Text,
  Trash,
  createColumnHelper,
  toast,
  usePrompt
} from "./chunk-YP2LLXWB.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-ZSQ362B4.js";
import {
  keepPreviousData
} from "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/price-list-detail-GVXCBOSM.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var import_react = __toESM(require_react(), 1);
var import_jsx_runtime4 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime5 = __toESM(require_jsx_runtime(), 1);
var PriceListDetailBreadcrumb = (props) => {
  const { id } = props.params || {};
  const { price_list } = usePriceList(id, void 0, {
    initialData: props.data,
    enabled: Boolean(id)
  });
  if (!price_list) {
    return null;
  }
  return (0, import_jsx_runtime.jsx)("span", { children: price_list.title });
};
var pricingDetailQuery = (id) => ({
  queryKey: priceListsQueryKeys.detail(id),
  queryFn: async () => sdk.admin.priceList.retrieve(id)
});
var pricingLoader = async ({ params }) => {
  const id = params.id;
  const query = pricingDetailQuery(id);
  return queryClient.ensureQueryData(query);
};
var PriceListConfigurationSection = ({
  priceList
}) => {
  const { t } = useTranslation();
  return (0, import_jsx_runtime2.jsxs)(Container, { className: "flex flex-col gap-y-4", children: [
    (0, import_jsx_runtime2.jsxs)("div", { className: "flex items-center justify-between", children: [
      (0, import_jsx_runtime2.jsxs)("div", { children: [
        (0, import_jsx_runtime2.jsx)(Heading, { level: "h2", children: t("priceLists.configuration.header") }),
        (0, import_jsx_runtime2.jsx)(CustomerGroupDisplay, { priceList })
      ] }),
      (0, import_jsx_runtime2.jsx)(
        ActionMenu,
        {
          groups: [
            {
              actions: [
                {
                  label: t("actions.edit"),
                  to: "configuration",
                  icon: (0, import_jsx_runtime2.jsx)(PencilSquare, {})
                }
              ]
            }
          ]
        }
      )
    ] }),
    (0, import_jsx_runtime2.jsx)(
      DateRangeDisplay,
      {
        endsAt: priceList.ends_at,
        startsAt: priceList.starts_at,
        showTime: true
      }
    )
  ] });
};
var CustomerGroupDisplay = ({
  priceList
}) => {
  const { t } = useTranslation();
  const customerGroupIds = priceList.rules["customer.groups.id"];
  const { customer_groups, isPending, isError, error } = useCustomerGroups(
    {
      id: customerGroupIds
    },
    {
      enabled: !!(customerGroupIds == null ? void 0 : customerGroupIds.length)
    }
  );
  if (isError) {
    throw error;
  }
  if (!(customerGroupIds == null ? void 0 : customerGroupIds.length)) {
    return null;
  }
  if (isPending || !customer_groups) {
    return (0, import_jsx_runtime2.jsx)(Skeleton, { className: "h-5 w-full max-w-48" });
  }
  return (0, import_jsx_runtime2.jsxs)("div", { className: "txt-small-plus text-ui-fg-muted flex items-center gap-x-1.5", children: [
    (0, import_jsx_runtime2.jsx)("span", { className: "text-ui-fg-subtle", children: t("priceLists.fields.customerAvailability.attribute") }),
    (0, import_jsx_runtime2.jsx)("span", { children: "·" }),
    (0, import_jsx_runtime2.jsx)(
      ListSummary,
      {
        list: customer_groups.map((group) => group.name),
        n: 1,
        className: "txt-small-plus text-ui-fg-muted"
      }
    )
  ] });
};
var PriceListGeneralSection = ({
  priceList
}) => {
  var _a;
  const { t } = useTranslation();
  const overrideCount = ((_a = priceList.prices) == null ? void 0 : _a.length) || 0;
  const { color, text } = getPriceListStatus(t, priceList);
  const handleDelete = useDeletePriceListAction({ priceList });
  const type = priceList.type === "sale" ? t("priceLists.fields.type.options.sale.label") : t("priceLists.fields.type.options.override.label");
  return (0, import_jsx_runtime3.jsxs)(Container, { className: "divide-y p-0", children: [
    (0, import_jsx_runtime3.jsxs)("div", { className: "flex items-center justify-between px-6 py-4", children: [
      (0, import_jsx_runtime3.jsx)(Heading, { children: priceList.title }),
      (0, import_jsx_runtime3.jsxs)("div", { className: "flex items-center gap-x-4", children: [
        (0, import_jsx_runtime3.jsx)(StatusBadge, { color, children: text }),
        (0, import_jsx_runtime3.jsx)(
          ActionMenu,
          {
            groups: [
              {
                actions: [
                  {
                    label: t("actions.edit"),
                    to: "edit",
                    icon: (0, import_jsx_runtime3.jsx)(PencilSquare, {})
                  }
                ]
              },
              {
                actions: [
                  {
                    label: t("actions.delete"),
                    onClick: handleDelete,
                    icon: (0, import_jsx_runtime3.jsx)(Trash, {})
                  }
                ]
              }
            ]
          }
        )
      ] })
    ] }),
    (0, import_jsx_runtime3.jsxs)("div", { className: "text-ui-fg-subtle grid grid-cols-2 items-center px-6 py-4", children: [
      (0, import_jsx_runtime3.jsx)(Text, { leading: "compact", size: "small", weight: "plus", children: t("fields.type") }),
      (0, import_jsx_runtime3.jsx)(Text, { size: "small", className: "text-pretty", children: type })
    ] }),
    (0, import_jsx_runtime3.jsxs)("div", { className: "text-ui-fg-subtle grid grid-cols-2 items-center px-6 py-4", children: [
      (0, import_jsx_runtime3.jsx)(Text, { leading: "compact", size: "small", weight: "plus", children: t("fields.description") }),
      (0, import_jsx_runtime3.jsx)(Text, { size: "small", className: "text-pretty", children: priceList.description })
    ] }),
    (0, import_jsx_runtime3.jsxs)("div", { className: "text-ui-fg-subtle grid grid-cols-2 items-center px-6 py-4", children: [
      (0, import_jsx_runtime3.jsx)(Text, { leading: "compact", size: "small", weight: "plus", children: t("priceLists.fields.priceOverrides.label") }),
      (0, import_jsx_runtime3.jsx)(Text, { size: "small", className: "text-pretty", children: overrideCount || "-" })
    ] })
  ] });
};
var PAGE_SIZE = 10;
var PREFIX = "p";
var PriceListProductSection = ({
  priceList
}) => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const prompt = usePrompt();
  const [rowSelection, setRowSelection] = (0, import_react.useState)({});
  const { searchParams, raw } = useProductTableQuery({
    pageSize: PAGE_SIZE,
    prefix: PREFIX
  });
  const { products, count, isLoading, isError, error } = useProducts(
    {
      ...searchParams,
      price_list_id: [priceList.id]
    },
    {
      placeholderData: keepPreviousData
    }
  );
  const filters = useProductTableFilters();
  const columns = useColumns(priceList);
  const { mutateAsync } = usePriceListLinkProducts(priceList.id);
  const { table } = useDataTable({
    data: products || [],
    count,
    columns,
    enablePagination: true,
    enableRowSelection: true,
    pageSize: PAGE_SIZE,
    getRowId: (row) => row.id,
    rowSelection: {
      state: rowSelection,
      updater: setRowSelection
    },
    prefix: PREFIX
  });
  const handleDelete = async () => {
    const res = await prompt({
      title: t("general.areYouSure"),
      description: t("priceLists.products.delete.confirmation", {
        count: Object.keys(rowSelection).length
      }),
      confirmText: t("actions.delete"),
      cancelText: t("actions.cancel")
    });
    if (!res) {
      return;
    }
    mutateAsync(
      {
        remove: Object.keys(rowSelection)
      },
      {
        onSuccess: () => {
          toast.success(
            t("priceLists.products.delete.successToast", {
              count: Object.keys(rowSelection).length
            })
          );
          setRowSelection({});
        },
        onError: (e) => {
          toast.error(e.message);
        }
      }
    );
  };
  const handleEdit = async () => {
    const ids = Object.keys(rowSelection).join(",");
    navigate(`products/edit?ids[]=${ids}`);
  };
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime4.jsxs)(Container, { className: "divide-y p-0", children: [
    (0, import_jsx_runtime4.jsxs)("div", { className: "flex items-center justify-between px-6 py-4", children: [
      (0, import_jsx_runtime4.jsx)(Heading, { children: t("priceLists.products.header") }),
      (0, import_jsx_runtime4.jsx)(
        ActionMenu,
        {
          groups: [
            {
              actions: [
                {
                  label: t("priceLists.products.actions.addProducts"),
                  to: "products/add",
                  icon: (0, import_jsx_runtime4.jsx)(Plus, {})
                },
                {
                  label: t("priceLists.products.actions.editPrices"),
                  to: "products/edit",
                  icon: (0, import_jsx_runtime4.jsx)(PencilSquare, {})
                }
              ]
            }
          ]
        }
      )
    ] }),
    (0, import_jsx_runtime4.jsx)(
      _DataTable,
      {
        table,
        filters,
        columns,
        count,
        pageSize: PAGE_SIZE,
        isLoading,
        navigateTo: (row) => `/products/${row.original.id}`,
        orderBy: [
          { key: "title", label: t("fields.title") },
          { key: "created_at", label: t("fields.createdAt") },
          { key: "updated_at", label: t("fields.updatedAt") }
        ],
        commands: [
          {
            action: handleEdit,
            label: t("actions.edit"),
            shortcut: "e"
          },
          {
            action: handleDelete,
            label: t("actions.delete"),
            shortcut: "d"
          }
        ],
        pagination: true,
        search: true,
        prefix: PREFIX,
        queryObject: raw
      }
    )
  ] });
};
var ProductRowAction = ({
  product,
  priceList
}) => {
  const { t } = useTranslation();
  const prompt = usePrompt();
  const { mutateAsync } = usePriceListLinkProducts(priceList.id);
  const handleDelete = async () => {
    const res = await prompt({
      title: t("general.areYouSure"),
      description: t("priceLists.products.delete.confirmation", {
        count: 1
      }),
      confirmText: t("actions.delete"),
      cancelText: t("actions.cancel")
    });
    if (!res) {
      return;
    }
    mutateAsync(
      {
        remove: [product.id]
      },
      {
        onSuccess: () => {
          toast.success(
            t("priceLists.products.delete.successToast", {
              count: 1
            })
          );
        },
        onError: (e) => {
          toast.error(e.message);
        }
      }
    );
  };
  return (0, import_jsx_runtime4.jsx)(
    ActionMenu,
    {
      groups: [
        {
          actions: [
            {
              icon: (0, import_jsx_runtime4.jsx)(PencilSquare, {}),
              label: t("priceLists.products.actions.editPrices"),
              to: `products/edit?ids[]=${product.id}`
            }
          ]
        },
        {
          actions: [
            {
              icon: (0, import_jsx_runtime4.jsx)(Trash, {}),
              label: t("actions.remove"),
              onClick: handleDelete
            }
          ]
        }
      ]
    }
  );
};
var columnHelper = createColumnHelper();
var useColumns = (priceList) => {
  const base = useProductTableColumns();
  return (0, import_react.useMemo)(
    () => [
      columnHelper.display({
        id: "select",
        header: ({ table }) => {
          return (0, import_jsx_runtime4.jsx)(
            Checkbox,
            {
              checked: table.getIsSomePageRowsSelected() ? "indeterminate" : table.getIsAllPageRowsSelected(),
              onCheckedChange: (value) => table.toggleAllPageRowsSelected(!!value)
            }
          );
        },
        cell: ({ row }) => {
          return (0, import_jsx_runtime4.jsx)(
            Checkbox,
            {
              checked: row.getIsSelected(),
              onCheckedChange: (value) => row.toggleSelected(!!value),
              onClick: (e) => {
                e.stopPropagation();
              }
            }
          );
        }
      }),
      ...base,
      columnHelper.display({
        id: "actions",
        cell: ({ row }) => (0, import_jsx_runtime4.jsx)(ProductRowAction, { product: row.original, priceList })
      })
    ],
    [base, priceList]
  );
};
var PriceListDetails = () => {
  const { id } = useParams();
  const { price_list, isLoading, isError, error } = usePriceList(id);
  const { getWidgets } = useExtension();
  if (isLoading || !price_list) {
    return (0, import_jsx_runtime5.jsx)(TwoColumnPageSkeleton, { mainSections: 2, sidebarSections: 1, showJSON: true });
  }
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime5.jsxs)(
    TwoColumnPage,
    {
      widgets: {
        after: getWidgets("price_list.details.after"),
        before: getWidgets("price_list.details.before"),
        sideAfter: getWidgets("price_list.details.side.after"),
        sideBefore: getWidgets("price_list.details.side.before")
      },
      data: price_list,
      showJSON: true,
      children: [
        (0, import_jsx_runtime5.jsxs)(TwoColumnPage.Main, { children: [
          (0, import_jsx_runtime5.jsx)(PriceListGeneralSection, { priceList: price_list }),
          (0, import_jsx_runtime5.jsx)(PriceListProductSection, { priceList: price_list })
        ] }),
        (0, import_jsx_runtime5.jsx)(TwoColumnPage.Sidebar, { children: (0, import_jsx_runtime5.jsx)(PriceListConfigurationSection, { priceList: price_list }) })
      ]
    }
  );
};
export {
  PriceListDetailBreadcrumb as Breadcrumb,
  PriceListDetails as Component,
  pricingLoader as loader
};
//# sourceMappingURL=price-list-detail-GVXCBOSM-VNMB4KWG.js.map
