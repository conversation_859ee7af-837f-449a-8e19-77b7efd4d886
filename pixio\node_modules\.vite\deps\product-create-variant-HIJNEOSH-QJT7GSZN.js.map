{"version": 3, "sources": ["../../@medusajs/dashboard/dist/product-create-variant-HIJNEOSH.mjs"], "sourcesContent": ["import {\n  useComboboxData\n} from \"./chunk-YIZSVS2R.mjs\";\nimport {\n  Combobox\n} from \"./chunk-GZBFGV7Y.mjs\";\nimport {\n  optionalInt,\n  partialFormValidation\n} from \"./chunk-ZQRKUG6J.mjs\";\nimport {\n  DataGrid,\n  createDataGridHelper,\n  createDataGridPriceColumns\n} from \"./chunk-53RYGJCD.mjs\";\nimport \"./chunk-MWVM4TYO.mjs\";\nimport {\n  castNumber\n} from \"./chunk-6GU6IDUA.mjs\";\nimport \"./chunk-IUCDCPJU.mjs\";\nimport {\n  KeyboundForm\n} from \"./chunk-6HTZNHPT.mjs\";\nimport {\n  RouteDrawer,\n  RouteFocusModal,\n  useRouteModal\n} from \"./chunk-4TC5YS65.mjs\";\nimport \"./chunk-LPEUYMRK.mjs\";\nimport \"./chunk-OC7BQLYI.mjs\";\nimport {\n  Form\n} from \"./chunk-OBQI23QM.mjs\";\nimport \"./chunk-Z5UDPQIH.mjs\";\nimport \"./chunk-KOSCMAIC.mjs\";\nimport \"./chunk-X6DSNTTX.mjs\";\nimport \"./chunk-I6E6CALJ.mjs\";\nimport \"./chunk-B4GODIOW.mjs\";\nimport \"./chunk-F6IJV2I2.mjs\";\nimport \"./chunk-QTCZFYFH.mjs\";\nimport \"./chunk-ENV6YVOM.mjs\";\nimport \"./chunk-PIR2H25N.mjs\";\nimport \"./chunk-RLY2SL5E.mjs\";\nimport \"./chunk-C5LYZZZ5.mjs\";\nimport \"./chunk-2ZKVRTBW.mjs\";\nimport \"./chunk-FO3VP56P.mjs\";\nimport \"./chunk-YS65UGPC.mjs\";\nimport \"./chunk-F6PXCY3N.mjs\";\nimport \"./chunk-3OHH43G6.mjs\";\nimport \"./chunk-G2H6MAK7.mjs\";\nimport \"./chunk-GRT22PE5.mjs\";\nimport \"./chunk-32IQRUVY.mjs\";\nimport \"./chunk-FNYASI54.mjs\";\nimport \"./chunk-FVC7M755.mjs\";\nimport \"./chunk-ZJ3OFMHB.mjs\";\nimport \"./chunk-PNU5HPGY.mjs\";\nimport {\n  useStore\n} from \"./chunk-V2LANK5S.mjs\";\nimport {\n  useRegions\n} from \"./chunk-QZ6PT4QV.mjs\";\nimport {\n  usePricePreferences\n} from \"./chunk-QL4XKIVL.mjs\";\nimport {\n  useCreateProductVariant,\n  useProduct\n} from \"./chunk-6I62UDJA.mjs\";\nimport \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport {\n  sdk\n} from \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-RPUOO7AV.mjs\";\n\n// src/routes/products/product-create-variant/product-create-variant.tsx\nimport { useParams } from \"react-router-dom\";\n\n// src/routes/products/product-create-variant/components/create-product-variant-form/create-product-variant-form.tsx\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { Button as Button2, ProgressTabs, toast } from \"@medusajs/ui\";\nimport { useEffect, useMemo as useMemo2, useState } from \"react\";\nimport { useFieldArray as useFieldArray2, useForm, useWatch as useWatch3 } from \"react-hook-form\";\nimport { useTranslation as useTranslation4 } from \"react-i18next\";\n\n// src/routes/products/product-create-variant/components/create-product-variant-form/constants.ts\nimport { z } from \"zod\";\nimport * as zod from \"zod\";\nvar CreateProductVariantSchema = z.object({\n  title: z.string().min(1),\n  sku: z.string().optional(),\n  manage_inventory: z.boolean().optional(),\n  allow_backorder: z.boolean().optional(),\n  inventory_kit: z.boolean().optional(),\n  options: z.record(z.string()),\n  prices: zod.record(zod.string(), zod.string().or(zod.number()).optional()).optional(),\n  inventory: z.array(\n    z.object({\n      inventory_item_id: z.string(),\n      required_quantity: optionalInt\n    })\n  ).optional()\n});\nvar CreateVariantDetailsSchema = CreateProductVariantSchema.pick({\n  title: true,\n  sku: true,\n  manage_inventory: true,\n  allow_backorder: true,\n  inventory_kit: true,\n  options: true\n});\nvar CreateVariantDetailsFields = Object.keys(\n  CreateVariantDetailsSchema.shape\n);\nvar CreateVariantPriceSchema = CreateProductVariantSchema.pick({\n  prices: true\n});\nvar CreateVariantPriceFields = Object.keys(\n  CreateVariantPriceSchema.shape\n);\n\n// src/routes/products/product-create-variant/components/create-product-variant-form/details-tab.tsx\nimport { Heading, Input, Switch } from \"@medusajs/ui\";\nimport { useWatch } from \"react-hook-form\";\nimport { useTranslation } from \"react-i18next\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nfunction DetailsTab({ form, product }) {\n  const { t } = useTranslation();\n  const manageInventoryEnabled = useWatch({\n    control: form.control,\n    name: \"manage_inventory\"\n  });\n  return /* @__PURE__ */ jsx(\"div\", { className: \"flex flex-1 flex-col items-center overflow-y-auto\", children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex w-full max-w-[720px] flex-col gap-y-8 px-8 py-16\", children: [\n    /* @__PURE__ */ jsx(Heading, { level: \"h1\", children: t(\"products.variant.create.header\") }),\n    /* @__PURE__ */ jsxs(\"div\", { className: \"my-8 grid grid-cols-1 gap-4 md:grid-cols-2\", children: [\n      /* @__PURE__ */ jsx(\n        Form.Field,\n        {\n          control: form.control,\n          name: \"title\",\n          render: ({ field }) => {\n            return /* @__PURE__ */ jsxs(Form.Item, { children: [\n              /* @__PURE__ */ jsx(Form.Label, { children: t(\"fields.title\") }),\n              /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(Input, { ...field }) }),\n              /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n            ] });\n          }\n        }\n      ),\n      /* @__PURE__ */ jsx(\n        Form.Field,\n        {\n          control: form.control,\n          name: \"sku\",\n          render: ({ field }) => {\n            return /* @__PURE__ */ jsxs(Form.Item, { children: [\n              /* @__PURE__ */ jsx(Form.Label, { optional: true, children: t(\"fields.sku\") }),\n              /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(Input, { ...field }) }),\n              /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n            ] });\n          }\n        }\n      ),\n      product.options.map((option) => /* @__PURE__ */ jsx(\n        Form.Field,\n        {\n          control: form.control,\n          name: `options.${option.title}`,\n          render: ({ field: { value, onChange, ...field } }) => {\n            return /* @__PURE__ */ jsxs(Form.Item, { children: [\n              /* @__PURE__ */ jsx(Form.Label, { children: option.title }),\n              /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(\n                Combobox,\n                {\n                  value,\n                  onChange: (v) => {\n                    onChange(v);\n                  },\n                  ...field,\n                  options: option.values.map((v) => ({\n                    label: v.value,\n                    value: v.value\n                  }))\n                }\n              ) })\n            ] });\n          }\n        },\n        option.id\n      ))\n    ] }),\n    /* @__PURE__ */ jsxs(\"div\", { className: \"flex flex-col gap-y-4\", children: [\n      /* @__PURE__ */ jsx(\n        Form.Field,\n        {\n          control: form.control,\n          name: \"manage_inventory\",\n          render: ({ field: { value, onChange, ...field } }) => {\n            return /* @__PURE__ */ jsxs(Form.Item, { children: [\n              /* @__PURE__ */ jsxs(\"div\", { className: \"bg-ui-bg-component shadow-elevation-card-rest flex gap-x-3 rounded-lg p-4\", children: [\n                /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(\n                  Switch,\n                  {\n                    className: \"mt-[2px]\",\n                    checked: value,\n                    onCheckedChange: (checked) => onChange(!!checked),\n                    ...field\n                  }\n                ) }),\n                /* @__PURE__ */ jsxs(\"div\", { className: \"flex flex-col\", children: [\n                  /* @__PURE__ */ jsx(Form.Label, { children: t(\"products.variant.inventory.manageInventoryLabel\") }),\n                  /* @__PURE__ */ jsx(Form.Hint, { children: t(\"products.variant.inventory.manageInventoryHint\") })\n                ] })\n              ] }),\n              /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n            ] });\n          }\n        }\n      ),\n      /* @__PURE__ */ jsx(\n        Form.Field,\n        {\n          control: form.control,\n          name: \"allow_backorder\",\n          disabled: !manageInventoryEnabled,\n          render: ({ field: { value, onChange, ...field } }) => {\n            return /* @__PURE__ */ jsxs(Form.Item, { children: [\n              /* @__PURE__ */ jsxs(\"div\", { className: \"bg-ui-bg-component shadow-elevation-card-rest flex gap-x-3 rounded-lg p-4\", children: [\n                /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(\n                  Switch,\n                  {\n                    checked: value,\n                    onCheckedChange: (checked) => onChange(!!checked),\n                    ...field,\n                    disabled: !manageInventoryEnabled\n                  }\n                ) }),\n                /* @__PURE__ */ jsxs(\"div\", { className: \"flex flex-col\", children: [\n                  /* @__PURE__ */ jsx(Form.Label, { children: t(\"products.variant.inventory.allowBackordersLabel\") }),\n                  /* @__PURE__ */ jsx(Form.Hint, { children: t(\"products.variant.inventory.allowBackordersHint\") })\n                ] })\n              ] }),\n              /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n            ] });\n          }\n        }\n      ),\n      /* @__PURE__ */ jsx(\n        Form.Field,\n        {\n          control: form.control,\n          name: \"inventory_kit\",\n          render: ({ field: { value, onChange, ...field } }) => {\n            return /* @__PURE__ */ jsxs(Form.Item, { children: [\n              /* @__PURE__ */ jsxs(\"div\", { className: \"bg-ui-bg-component shadow-elevation-card-rest flex gap-x-3 rounded-lg p-4\", children: [\n                /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(\n                  Switch,\n                  {\n                    checked: value,\n                    onCheckedChange: (checked) => onChange(!!checked),\n                    ...field,\n                    disabled: !manageInventoryEnabled\n                  }\n                ) }),\n                /* @__PURE__ */ jsxs(\"div\", { className: \"flex flex-col\", children: [\n                  /* @__PURE__ */ jsx(Form.Label, { children: t(\"products.variant.inventory.inventoryKit\") }),\n                  /* @__PURE__ */ jsx(Form.Hint, { children: t(\"products.variant.inventory.inventoryKitHint\") })\n                ] })\n              ] }),\n              /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n            ] });\n          }\n        }\n      )\n    ] })\n  ] }) });\n}\nvar details_tab_default = DetailsTab;\n\n// src/routes/products/product-create-variant/components/create-product-variant-form/inventory-kit-tab.tsx\nimport { useFieldArray } from \"react-hook-form\";\nimport { Button, Heading as Heading2, IconButton, Input as Input2, Label } from \"@medusajs/ui\";\nimport { XMarkMini } from \"@medusajs/icons\";\nimport { useTranslation as useTranslation2 } from \"react-i18next\";\nimport { jsx as jsx2, jsxs as jsxs2 } from \"react/jsx-runtime\";\nfunction InventoryKitTab({ form }) {\n  const { t } = useTranslation2();\n  const inventory = useFieldArray({\n    control: form.control,\n    name: `inventory`\n  });\n  const inventoryFormData = inventory.fields;\n  const items = useComboboxData({\n    queryKey: [\"inventory_items\"],\n    queryFn: (params) => sdk.admin.inventoryItem.list(params),\n    getOptions: (data) => data.inventory_items.map((item) => ({\n      label: item.title,\n      value: item.id\n    }))\n  });\n  const isItemOptionDisabled = (option, inventoryIndex) => {\n    return inventoryFormData?.some(\n      (i, index) => index != inventoryIndex && i.inventory_item_id === option.value\n    );\n  };\n  return /* @__PURE__ */ jsx2(\"div\", { className: \"flex flex-col items-center p-16\", children: /* @__PURE__ */ jsx2(\"div\", { className: \"flex w-full max-w-[720px] flex-col gap-y-8\", children: /* @__PURE__ */ jsxs2(\"div\", { id: \"organize\", className: \"flex flex-col gap-y-8\", children: [\n    /* @__PURE__ */ jsx2(Heading2, { children: t(\"products.create.inventory.heading\") }),\n    /* @__PURE__ */ jsxs2(\"div\", { className: \"grid gap-y-4\", children: [\n      /* @__PURE__ */ jsxs2(\"div\", { className: \"flex items-start justify-between gap-x-4\", children: [\n        /* @__PURE__ */ jsxs2(\"div\", { className: \"flex flex-col\", children: [\n          /* @__PURE__ */ jsx2(Form.Label, { children: form.getValues(\"title\") }),\n          /* @__PURE__ */ jsx2(Form.Hint, { children: t(\"products.create.inventory.label\") })\n        ] }),\n        /* @__PURE__ */ jsx2(\n          Button,\n          {\n            size: \"small\",\n            variant: \"secondary\",\n            type: \"button\",\n            onClick: () => {\n              inventory.append({\n                inventory_item_id: \"\",\n                required_quantity: \"\"\n              });\n            },\n            children: t(\"actions.add\")\n          }\n        )\n      ] }),\n      inventory.fields.map((inventoryItem, inventoryIndex) => /* @__PURE__ */ jsxs2(\n        \"li\",\n        {\n          className: \"bg-ui-bg-component shadow-elevation-card-rest grid grid-cols-[1fr_28px] items-center gap-1.5 rounded-xl p-1.5\",\n          children: [\n            /* @__PURE__ */ jsxs2(\"div\", { className: \"grid grid-cols-[min-content,1fr] items-center gap-1.5\", children: [\n              /* @__PURE__ */ jsx2(\"div\", { className: \"flex items-center px-2 py-1.5\", children: /* @__PURE__ */ jsx2(\n                Label,\n                {\n                  size: \"xsmall\",\n                  weight: \"plus\",\n                  className: \"text-ui-fg-subtle\",\n                  htmlFor: `inventory.${inventoryIndex}.inventory_item_id`,\n                  children: t(\"fields.item\")\n                }\n              ) }),\n              /* @__PURE__ */ jsx2(\n                Form.Field,\n                {\n                  control: form.control,\n                  name: `inventory.${inventoryIndex}.inventory_item_id`,\n                  render: ({ field }) => {\n                    return /* @__PURE__ */ jsx2(Form.Item, { children: /* @__PURE__ */ jsx2(Form.Control, { children: /* @__PURE__ */ jsx2(\n                      Combobox,\n                      {\n                        ...field,\n                        options: items.options.map((o) => ({\n                          ...o,\n                          disabled: isItemOptionDisabled(\n                            o,\n                            inventoryIndex\n                          )\n                        })),\n                        searchValue: items.searchValue,\n                        onSearchValueChange: items.onSearchValueChange,\n                        fetchNextPage: items.fetchNextPage,\n                        className: \"bg-ui-bg-field-component hover:bg-ui-bg-field-component-hover\",\n                        placeholder: t(\n                          \"products.create.inventory.itemPlaceholder\"\n                        )\n                      }\n                    ) }) });\n                  }\n                }\n              ),\n              /* @__PURE__ */ jsx2(\"div\", { className: \"flex items-center px-2 py-1.5\", children: /* @__PURE__ */ jsx2(\n                Label,\n                {\n                  size: \"xsmall\",\n                  weight: \"plus\",\n                  className: \"text-ui-fg-subtle\",\n                  htmlFor: `inventory.${inventoryIndex}.required_quantity`,\n                  children: t(\"fields.quantity\")\n                }\n              ) }),\n              /* @__PURE__ */ jsx2(\n                Form.Field,\n                {\n                  control: form.control,\n                  name: `inventory.${inventoryIndex}.required_quantity`,\n                  render: ({ field: { onChange, value, ...field } }) => {\n                    return /* @__PURE__ */ jsxs2(Form.Item, { children: [\n                      /* @__PURE__ */ jsx2(Form.Control, { children: /* @__PURE__ */ jsx2(\n                        Input2,\n                        {\n                          type: \"number\",\n                          className: \"bg-ui-bg-field-component\",\n                          min: 0,\n                          value,\n                          onChange: (e) => {\n                            const value2 = e.target.value;\n                            if (value2 === \"\") {\n                              onChange(null);\n                            } else {\n                              onChange(Number(value2));\n                            }\n                          },\n                          ...field,\n                          placeholder: t(\n                            \"products.create.inventory.quantityPlaceholder\"\n                          )\n                        }\n                      ) }),\n                      /* @__PURE__ */ jsx2(Form.ErrorMessage, {})\n                    ] });\n                  }\n                }\n              )\n            ] }),\n            /* @__PURE__ */ jsx2(\n              IconButton,\n              {\n                type: \"button\",\n                size: \"small\",\n                variant: \"transparent\",\n                className: \"text-ui-fg-muted\",\n                onClick: () => inventory.remove(inventoryIndex),\n                children: /* @__PURE__ */ jsx2(XMarkMini, {})\n              }\n            )\n          ]\n        },\n        inventoryItem.id\n      ))\n    ] })\n  ] }) }) });\n}\nvar inventory_kit_tab_default = InventoryKitTab;\n\n// src/routes/products/product-create-variant/components/create-product-variant-form/pricing-tab.tsx\nimport { useMemo } from \"react\";\nimport { useWatch as useWatch2 } from \"react-hook-form\";\nimport { useTranslation as useTranslation3 } from \"react-i18next\";\nimport { jsx as jsx3 } from \"react/jsx-runtime\";\nfunction PricingTab({ form }) {\n  const { store } = useStore();\n  const { regions } = useRegions({ limit: 9999 });\n  const { price_preferences: pricePreferences } = usePricePreferences({});\n  const { setCloseOnEscape } = useRouteModal();\n  const columns = useVariantPriceGridColumns({\n    currencies: store?.supported_currencies,\n    regions,\n    pricePreferences\n  });\n  const variant = useWatch2({\n    control: form.control\n  });\n  return /* @__PURE__ */ jsx3(\n    DataGrid,\n    {\n      columns,\n      data: [variant],\n      state: form,\n      onEditingChange: (editing) => setCloseOnEscape(!editing)\n    }\n  );\n}\nvar columnHelper = createDataGridHelper();\nvar useVariantPriceGridColumns = ({\n  currencies = [],\n  regions = [],\n  pricePreferences = []\n}) => {\n  const { t } = useTranslation3();\n  return useMemo(() => {\n    return [\n      columnHelper.column({\n        id: t(\"fields.title\"),\n        header: t(\"fields.title\"),\n        cell: (context) => {\n          const entity = context.row.original;\n          return /* @__PURE__ */ jsx3(DataGrid.ReadonlyCell, { context, children: /* @__PURE__ */ jsx3(\"div\", { className: \"flex h-full w-full items-center gap-x-2 overflow-hidden\", children: /* @__PURE__ */ jsx3(\"span\", { className: \"truncate\", children: entity.title }) }) });\n        },\n        disableHiding: true\n      }),\n      ...createDataGridPriceColumns({\n        currencies: currencies.map((c) => c.currency_code),\n        regions,\n        pricePreferences,\n        getFieldName: (context, value) => {\n          if (context.column.id?.startsWith(\"currency_prices\")) {\n            return `prices.${value}`;\n          }\n          return `prices.${value}`;\n        },\n        t\n      })\n    ];\n  }, [t, currencies, regions, pricePreferences]);\n};\nvar pricing_tab_default = PricingTab;\n\n// src/routes/products/product-create-variant/components/create-product-variant-form/create-product-variant-form.tsx\nimport { jsx as jsx4, jsxs as jsxs3 } from \"react/jsx-runtime\";\nvar initialTabState = {\n  [\"detail\" /* DETAIL */]: \"in-progress\",\n  [\"price\" /* PRICE */]: \"not-started\",\n  [\"inventory\" /* INVENTORY */]: \"not-started\"\n};\nvar CreateProductVariantForm = ({\n  product\n}) => {\n  const { t } = useTranslation4();\n  const { handleSuccess } = useRouteModal();\n  const [tab, setTab] = useState(\"detail\" /* DETAIL */);\n  const [tabState, setTabState] = useState(initialTabState);\n  const form = useForm({\n    defaultValues: {\n      sku: \"\",\n      title: \"\",\n      manage_inventory: false,\n      allow_backorder: false,\n      inventory_kit: false,\n      options: {}\n    },\n    resolver: zodResolver(CreateProductVariantSchema)\n  });\n  const { mutateAsync, isPending } = useCreateProductVariant(product.id);\n  const { regions } = useRegions({ limit: 9999 });\n  const regionsCurrencyMap = useMemo2(() => {\n    if (!regions?.length) {\n      return {};\n    }\n    return regions.reduce((acc, reg) => {\n      acc[reg.id] = reg.currency_code;\n      return acc;\n    }, {});\n  }, [regions]);\n  const isManageInventoryEnabled = useWatch3({\n    control: form.control,\n    name: \"manage_inventory\"\n  });\n  const isInventoryKitEnabled = useWatch3({\n    control: form.control,\n    name: \"inventory_kit\"\n  });\n  const inventoryField = useFieldArray2({\n    control: form.control,\n    name: `inventory`\n  });\n  const inventoryTabEnabled = isManageInventoryEnabled && isInventoryKitEnabled;\n  const tabOrder = useMemo2(() => {\n    if (inventoryTabEnabled) {\n      return [\"detail\" /* DETAIL */, \"price\" /* PRICE */, \"inventory\" /* INVENTORY */];\n    }\n    return [\"detail\" /* DETAIL */, \"price\" /* PRICE */];\n  }, [inventoryTabEnabled]);\n  useEffect(() => {\n    if (isInventoryKitEnabled && inventoryField.fields.length === 0) {\n      inventoryField.append({\n        inventory_item_id: \"\",\n        required_quantity: void 0\n      });\n    }\n  }, [isInventoryKitEnabled]);\n  const handleChangeTab = (update) => {\n    if (tab === update) {\n      return;\n    }\n    if (tabOrder.indexOf(update) < tabOrder.indexOf(tab)) {\n      const isCurrentTabDirty = false;\n      setTabState((prev) => ({\n        ...prev,\n        [tab]: isCurrentTabDirty ? prev[tab] : \"not-started\",\n        [update]: \"in-progress\"\n      }));\n      setTab(update);\n      return;\n    }\n    const tabs = tabOrder.slice(0, tabOrder.indexOf(update));\n    for (const tab2 of tabs) {\n      if (tab2 === \"detail\" /* DETAIL */) {\n        if (!partialFormValidation(\n          form,\n          CreateVariantDetailsFields,\n          CreateVariantDetailsSchema\n        )) {\n          setTabState((prev) => ({\n            ...prev,\n            [tab2]: \"in-progress\"\n          }));\n          setTab(tab2);\n          return;\n        }\n        setTabState((prev) => ({\n          ...prev,\n          [tab2]: \"completed\"\n        }));\n      } else if (tab2 === \"price\" /* PRICE */) {\n        if (!partialFormValidation(\n          form,\n          CreateVariantPriceFields,\n          CreateVariantPriceSchema\n        )) {\n          setTabState((prev) => ({\n            ...prev,\n            [tab2]: \"in-progress\"\n          }));\n          setTab(tab2);\n          return;\n        }\n        setTabState((prev) => ({\n          ...prev,\n          [tab2]: \"completed\"\n        }));\n      }\n    }\n    setTabState((prev) => ({\n      ...prev,\n      [tab]: \"completed\",\n      [update]: \"in-progress\"\n    }));\n    setTab(update);\n  };\n  const handleNextTab = (tab2) => {\n    if (tabOrder.indexOf(tab2) + 1 >= tabOrder.length) {\n      return;\n    }\n    const nextTab = tabOrder[tabOrder.indexOf(tab2) + 1];\n    handleChangeTab(nextTab);\n  };\n  const handleSubmit = form.handleSubmit(async (data) => {\n    const { allow_backorder, manage_inventory, sku, title } = data;\n    await mutateAsync(\n      {\n        title,\n        sku: sku || void 0,\n        allow_backorder,\n        manage_inventory,\n        options: data.options,\n        prices: Object.entries(data.prices ?? {}).map(([currencyOrRegion, value]) => {\n          if (value === \"\" || value === void 0) {\n            return void 0;\n          }\n          const ret = {};\n          const amount = castNumber(value);\n          if (currencyOrRegion.startsWith(\"reg_\")) {\n            ret.rules = { region_id: currencyOrRegion };\n            ret.currency_code = regionsCurrencyMap[currencyOrRegion];\n          } else {\n            ret.currency_code = currencyOrRegion;\n          }\n          ret.amount = amount;\n          return ret;\n        }).filter(Boolean),\n        inventory_items: (data.inventory || []).map((i) => {\n          if (!i.required_quantity || !i.inventory_item_id) {\n            return false;\n          }\n          return {\n            ...i,\n            required_quantity: castNumber(i.required_quantity)\n          };\n        }).filter(Boolean)\n      },\n      {\n        onSuccess: () => {\n          handleSuccess();\n        },\n        onError: (error) => {\n          toast.error(error.message);\n        }\n      }\n    );\n  });\n  return /* @__PURE__ */ jsx4(RouteFocusModal.Form, { form, children: /* @__PURE__ */ jsx4(\n    ProgressTabs,\n    {\n      value: tab,\n      onValueChange: (tab2) => handleChangeTab(tab2),\n      className: \"flex h-full flex-col overflow-hidden\",\n      children: /* @__PURE__ */ jsxs3(\n        KeyboundForm,\n        {\n          onSubmit: handleSubmit,\n          className: \"flex h-full flex-col overflow-hidden\",\n          children: [\n            /* @__PURE__ */ jsx4(RouteFocusModal.Header, { children: /* @__PURE__ */ jsx4(\"div\", { className: \"flex w-full items-center justify-between gap-x-4\", children: /* @__PURE__ */ jsx4(\"div\", { className: \"-my-2 w-full max-w-[600px] border-l\", children: /* @__PURE__ */ jsxs3(ProgressTabs.List, { className: \"grid w-full grid-cols-3\", children: [\n              /* @__PURE__ */ jsx4(\n                ProgressTabs.Trigger,\n                {\n                  status: tabState.detail,\n                  value: \"detail\" /* DETAIL */,\n                  children: t(\"priceLists.create.tabs.details\")\n                }\n              ),\n              /* @__PURE__ */ jsx4(\n                ProgressTabs.Trigger,\n                {\n                  status: tabState.price,\n                  value: \"price\" /* PRICE */,\n                  children: t(\"priceLists.create.tabs.prices\")\n                }\n              ),\n              !!inventoryTabEnabled && /* @__PURE__ */ jsx4(\n                ProgressTabs.Trigger,\n                {\n                  status: tabState.inventory,\n                  value: \"inventory\" /* INVENTORY */,\n                  children: t(\"products.create.tabs.inventory\")\n                }\n              )\n            ] }) }) }) }),\n            /* @__PURE__ */ jsxs3(RouteFocusModal.Body, { className: \"size-full overflow-hidden\", children: [\n              /* @__PURE__ */ jsx4(\n                ProgressTabs.Content,\n                {\n                  className: \"size-full overflow-y-auto\",\n                  value: \"detail\" /* DETAIL */,\n                  children: /* @__PURE__ */ jsx4(details_tab_default, { form, product })\n                }\n              ),\n              /* @__PURE__ */ jsx4(\n                ProgressTabs.Content,\n                {\n                  className: \"size-full overflow-y-auto\",\n                  value: \"price\" /* PRICE */,\n                  children: /* @__PURE__ */ jsx4(pricing_tab_default, { form })\n                }\n              ),\n              !!inventoryTabEnabled && /* @__PURE__ */ jsx4(\n                ProgressTabs.Content,\n                {\n                  className: \"size-full overflow-hidden\",\n                  value: \"inventory\" /* INVENTORY */,\n                  children: /* @__PURE__ */ jsx4(inventory_kit_tab_default, { form })\n                }\n              )\n            ] }),\n            /* @__PURE__ */ jsx4(RouteFocusModal.Footer, { children: /* @__PURE__ */ jsxs3(\"div\", { className: \"flex items-center justify-end gap-x-2\", children: [\n              /* @__PURE__ */ jsx4(RouteDrawer.Close, { asChild: true, children: /* @__PURE__ */ jsx4(Button2, { variant: \"secondary\", size: \"small\", children: t(\"actions.cancel\") }) }),\n              /* @__PURE__ */ jsx4(\n                PrimaryButton,\n                {\n                  tab,\n                  next: handleNextTab,\n                  isLoading: isPending,\n                  inventoryTabEnabled: !!inventoryTabEnabled\n                }\n              )\n            ] }) })\n          ]\n        }\n      )\n    }\n  ) });\n};\nvar PrimaryButton = ({\n  tab,\n  next,\n  isLoading,\n  inventoryTabEnabled\n}) => {\n  const { t } = useTranslation4();\n  if (inventoryTabEnabled && tab === \"inventory\" /* INVENTORY */ || !inventoryTabEnabled && tab === \"price\" /* PRICE */) {\n    return /* @__PURE__ */ jsx4(\n      Button2,\n      {\n        type: \"submit\",\n        variant: \"primary\",\n        size: \"small\",\n        isLoading,\n        children: t(\"actions.save\")\n      },\n      \"submit-button\"\n    );\n  }\n  return /* @__PURE__ */ jsx4(\n    Button2,\n    {\n      type: \"button\",\n      variant: \"primary\",\n      size: \"small\",\n      onClick: () => next(tab),\n      children: t(\"actions.continue\")\n    },\n    \"next-button\"\n  );\n};\n\n// src/routes/products/product-create-variant/product-create-variant.tsx\nimport { jsx as jsx5 } from \"react/jsx-runtime\";\nvar ProductCreateVariant = () => {\n  const { id } = useParams();\n  const { product, isLoading, isError, error } = useProduct(id);\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsx5(RouteFocusModal, { children: !isLoading && product && /* @__PURE__ */ jsx5(CreateProductVariantForm, { product }) });\n};\nexport {\n  ProductCreateVariant as Component\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkFA,mBAAyD;AA4CzD,yBAA0B;AA+J1B,IAAAA,sBAA2C;AA2J3C,IAAAC,gBAAwB;AAGxB,IAAAC,sBAA4B;AA4D5B,IAAAA,sBAA2C;AAgS3C,IAAAA,sBAA4B;AA9rB5B,IAAI,6BAA6B,EAAE,OAAO;AAAA,EACxC,OAAO,EAAE,OAAO,EAAE,IAAI,CAAC;AAAA,EACvB,KAAK,EAAE,OAAO,EAAE,SAAS;AAAA,EACzB,kBAAkB,EAAE,QAAQ,EAAE,SAAS;AAAA,EACvC,iBAAiB,EAAE,QAAQ,EAAE,SAAS;AAAA,EACtC,eAAe,EAAE,QAAQ,EAAE,SAAS;AAAA,EACpC,SAAS,EAAE,OAAO,EAAE,OAAO,CAAC;AAAA,EAC5B,QAAY,WAAW,WAAO,GAAO,WAAO,EAAE,GAAO,WAAO,CAAC,EAAE,SAAS,CAAC,EAAE,SAAS;AAAA,EACpF,WAAW,EAAE;AAAA,IACX,EAAE,OAAO;AAAA,MACP,mBAAmB,EAAE,OAAO;AAAA,MAC5B,mBAAmB;AAAA,IACrB,CAAC;AAAA,EACH,EAAE,SAAS;AACb,CAAC;AACD,IAAI,6BAA6B,2BAA2B,KAAK;AAAA,EAC/D,OAAO;AAAA,EACP,KAAK;AAAA,EACL,kBAAkB;AAAA,EAClB,iBAAiB;AAAA,EACjB,eAAe;AAAA,EACf,SAAS;AACX,CAAC;AACD,IAAI,6BAA6B,OAAO;AAAA,EACtC,2BAA2B;AAC7B;AACA,IAAI,2BAA2B,2BAA2B,KAAK;AAAA,EAC7D,QAAQ;AACV,CAAC;AACD,IAAI,2BAA2B,OAAO;AAAA,EACpC,yBAAyB;AAC3B;AAOA,SAAS,WAAW,EAAE,MAAM,QAAQ,GAAG;AACrC,QAAM,EAAE,GAAAC,GAAE,IAAI,eAAe;AAC7B,QAAM,yBAAyB,SAAS;AAAA,IACtC,SAAS,KAAK;AAAA,IACd,MAAM;AAAA,EACR,CAAC;AACD,aAAuB,wBAAI,OAAO,EAAE,WAAW,qDAAqD,cAA0B,yBAAK,OAAO,EAAE,WAAW,yDAAyD,UAAU;AAAA,QACxM,wBAAI,SAAS,EAAE,OAAO,MAAM,UAAUA,GAAE,gCAAgC,EAAE,CAAC;AAAA,QAC3E,yBAAK,OAAO,EAAE,WAAW,8CAA8C,UAAU;AAAA,UAC/E;AAAA,QACd,KAAK;AAAA,QACL;AAAA,UACE,SAAS,KAAK;AAAA,UACd,MAAM;AAAA,UACN,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,uBAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,kBACjC,wBAAI,KAAK,OAAO,EAAE,UAAUA,GAAE,cAAc,EAAE,CAAC;AAAA,kBAC/C,wBAAI,KAAK,SAAS,EAAE,cAA0B,wBAAI,OAAO,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC;AAAA,kBACxE,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,YAC3C,EAAE,CAAC;AAAA,UACL;AAAA,QACF;AAAA,MACF;AAAA,UACgB;AAAA,QACd,KAAK;AAAA,QACL;AAAA,UACE,SAAS,KAAK;AAAA,UACd,MAAM;AAAA,UACN,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,uBAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,kBACjC,wBAAI,KAAK,OAAO,EAAE,UAAU,MAAM,UAAUA,GAAE,YAAY,EAAE,CAAC;AAAA,kBAC7D,wBAAI,KAAK,SAAS,EAAE,cAA0B,wBAAI,OAAO,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC;AAAA,kBACxE,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,YAC3C,EAAE,CAAC;AAAA,UACL;AAAA,QACF;AAAA,MACF;AAAA,MACA,QAAQ,QAAQ,IAAI,CAAC,eAA2B;AAAA,QAC9C,KAAK;AAAA,QACL;AAAA,UACE,SAAS,KAAK;AAAA,UACd,MAAM,WAAW,OAAO,KAAK;AAAA,UAC7B,QAAQ,CAAC,EAAE,OAAO,EAAE,OAAO,UAAU,GAAG,MAAM,EAAE,MAAM;AACpD,uBAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,kBACjC,wBAAI,KAAK,OAAO,EAAE,UAAU,OAAO,MAAM,CAAC;AAAA,kBAC1C,wBAAI,KAAK,SAAS,EAAE,cAA0B;AAAA,gBAC5D;AAAA,gBACA;AAAA,kBACE;AAAA,kBACA,UAAU,CAAC,MAAM;AACf,6BAAS,CAAC;AAAA,kBACZ;AAAA,kBACA,GAAG;AAAA,kBACH,SAAS,OAAO,OAAO,IAAI,CAAC,OAAO;AAAA,oBACjC,OAAO,EAAE;AAAA,oBACT,OAAO,EAAE;AAAA,kBACX,EAAE;AAAA,gBACJ;AAAA,cACF,EAAE,CAAC;AAAA,YACL,EAAE,CAAC;AAAA,UACL;AAAA,QACF;AAAA,QACA,OAAO;AAAA,MACT,CAAC;AAAA,IACH,EAAE,CAAC;AAAA,QACa,yBAAK,OAAO,EAAE,WAAW,yBAAyB,UAAU;AAAA,UAC1D;AAAA,QACd,KAAK;AAAA,QACL;AAAA,UACE,SAAS,KAAK;AAAA,UACd,MAAM;AAAA,UACN,QAAQ,CAAC,EAAE,OAAO,EAAE,OAAO,UAAU,GAAG,MAAM,EAAE,MAAM;AACpD,uBAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,kBACjC,yBAAK,OAAO,EAAE,WAAW,6EAA6E,UAAU;AAAA,oBAC9G,wBAAI,KAAK,SAAS,EAAE,cAA0B;AAAA,kBAC5D;AAAA,kBACA;AAAA,oBACE,WAAW;AAAA,oBACX,SAAS;AAAA,oBACT,iBAAiB,CAAC,YAAY,SAAS,CAAC,CAAC,OAAO;AAAA,oBAChD,GAAG;AAAA,kBACL;AAAA,gBACF,EAAE,CAAC;AAAA,oBACa,yBAAK,OAAO,EAAE,WAAW,iBAAiB,UAAU;AAAA,sBAClD,wBAAI,KAAK,OAAO,EAAE,UAAUA,GAAE,iDAAiD,EAAE,CAAC;AAAA,sBAClF,wBAAI,KAAK,MAAM,EAAE,UAAUA,GAAE,gDAAgD,EAAE,CAAC;AAAA,gBAClG,EAAE,CAAC;AAAA,cACL,EAAE,CAAC;AAAA,kBACa,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,YAC3C,EAAE,CAAC;AAAA,UACL;AAAA,QACF;AAAA,MACF;AAAA,UACgB;AAAA,QACd,KAAK;AAAA,QACL;AAAA,UACE,SAAS,KAAK;AAAA,UACd,MAAM;AAAA,UACN,UAAU,CAAC;AAAA,UACX,QAAQ,CAAC,EAAE,OAAO,EAAE,OAAO,UAAU,GAAG,MAAM,EAAE,MAAM;AACpD,uBAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,kBACjC,yBAAK,OAAO,EAAE,WAAW,6EAA6E,UAAU;AAAA,oBAC9G,wBAAI,KAAK,SAAS,EAAE,cAA0B;AAAA,kBAC5D;AAAA,kBACA;AAAA,oBACE,SAAS;AAAA,oBACT,iBAAiB,CAAC,YAAY,SAAS,CAAC,CAAC,OAAO;AAAA,oBAChD,GAAG;AAAA,oBACH,UAAU,CAAC;AAAA,kBACb;AAAA,gBACF,EAAE,CAAC;AAAA,oBACa,yBAAK,OAAO,EAAE,WAAW,iBAAiB,UAAU;AAAA,sBAClD,wBAAI,KAAK,OAAO,EAAE,UAAUA,GAAE,iDAAiD,EAAE,CAAC;AAAA,sBAClF,wBAAI,KAAK,MAAM,EAAE,UAAUA,GAAE,gDAAgD,EAAE,CAAC;AAAA,gBAClG,EAAE,CAAC;AAAA,cACL,EAAE,CAAC;AAAA,kBACa,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,YAC3C,EAAE,CAAC;AAAA,UACL;AAAA,QACF;AAAA,MACF;AAAA,UACgB;AAAA,QACd,KAAK;AAAA,QACL;AAAA,UACE,SAAS,KAAK;AAAA,UACd,MAAM;AAAA,UACN,QAAQ,CAAC,EAAE,OAAO,EAAE,OAAO,UAAU,GAAG,MAAM,EAAE,MAAM;AACpD,uBAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,kBACjC,yBAAK,OAAO,EAAE,WAAW,6EAA6E,UAAU;AAAA,oBAC9G,wBAAI,KAAK,SAAS,EAAE,cAA0B;AAAA,kBAC5D;AAAA,kBACA;AAAA,oBACE,SAAS;AAAA,oBACT,iBAAiB,CAAC,YAAY,SAAS,CAAC,CAAC,OAAO;AAAA,oBAChD,GAAG;AAAA,oBACH,UAAU,CAAC;AAAA,kBACb;AAAA,gBACF,EAAE,CAAC;AAAA,oBACa,yBAAK,OAAO,EAAE,WAAW,iBAAiB,UAAU;AAAA,sBAClD,wBAAI,KAAK,OAAO,EAAE,UAAUA,GAAE,yCAAyC,EAAE,CAAC;AAAA,sBAC1E,wBAAI,KAAK,MAAM,EAAE,UAAUA,GAAE,6CAA6C,EAAE,CAAC;AAAA,gBAC/F,EAAE,CAAC;AAAA,cACL,EAAE,CAAC;AAAA,kBACa,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,YAC3C,EAAE,CAAC;AAAA,UACL;AAAA,QACF;AAAA,MACF;AAAA,IACF,EAAE,CAAC;AAAA,EACL,EAAE,CAAC,EAAE,CAAC;AACR;AACA,IAAI,sBAAsB;AAQ1B,SAAS,gBAAgB,EAAE,KAAK,GAAG;AACjC,QAAM,EAAE,GAAAA,GAAE,IAAI,eAAgB;AAC9B,QAAM,YAAY,cAAc;AAAA,IAC9B,SAAS,KAAK;AAAA,IACd,MAAM;AAAA,EACR,CAAC;AACD,QAAM,oBAAoB,UAAU;AACpC,QAAM,QAAQ,gBAAgB;AAAA,IAC5B,UAAU,CAAC,iBAAiB;AAAA,IAC5B,SAAS,CAAC,WAAW,IAAI,MAAM,cAAc,KAAK,MAAM;AAAA,IACxD,YAAY,CAAC,SAAS,KAAK,gBAAgB,IAAI,CAAC,UAAU;AAAA,MACxD,OAAO,KAAK;AAAA,MACZ,OAAO,KAAK;AAAA,IACd,EAAE;AAAA,EACJ,CAAC;AACD,QAAM,uBAAuB,CAAC,QAAQ,mBAAmB;AACvD,WAAO,uDAAmB;AAAA,MACxB,CAAC,GAAG,UAAU,SAAS,kBAAkB,EAAE,sBAAsB,OAAO;AAAA;AAAA,EAE5E;AACA,aAAuB,oBAAAC,KAAK,OAAO,EAAE,WAAW,mCAAmC,cAA0B,oBAAAA,KAAK,OAAO,EAAE,WAAW,8CAA8C,cAA0B,oBAAAC,MAAM,OAAO,EAAE,IAAI,YAAY,WAAW,yBAAyB,UAAU;AAAA,QACzQ,oBAAAD,KAAK,SAAU,EAAE,UAAUD,GAAE,mCAAmC,EAAE,CAAC;AAAA,QACnE,oBAAAE,MAAM,OAAO,EAAE,WAAW,gBAAgB,UAAU;AAAA,UAClD,oBAAAA,MAAM,OAAO,EAAE,WAAW,4CAA4C,UAAU;AAAA,YAC9E,oBAAAA,MAAM,OAAO,EAAE,WAAW,iBAAiB,UAAU;AAAA,cACnD,oBAAAD,KAAK,KAAK,OAAO,EAAE,UAAU,KAAK,UAAU,OAAO,EAAE,CAAC;AAAA,cACtD,oBAAAA,KAAK,KAAK,MAAM,EAAE,UAAUD,GAAE,iCAAiC,EAAE,CAAC;AAAA,QACpF,EAAE,CAAC;AAAA,YACa,oBAAAC;AAAA,UACd;AAAA,UACA;AAAA,YACE,MAAM;AAAA,YACN,SAAS;AAAA,YACT,MAAM;AAAA,YACN,SAAS,MAAM;AACb,wBAAU,OAAO;AAAA,gBACf,mBAAmB;AAAA,gBACnB,mBAAmB;AAAA,cACrB,CAAC;AAAA,YACH;AAAA,YACA,UAAUD,GAAE,aAAa;AAAA,UAC3B;AAAA,QACF;AAAA,MACF,EAAE,CAAC;AAAA,MACH,UAAU,OAAO,IAAI,CAAC,eAAe,uBAAmC,oBAAAE;AAAA,QACtE;AAAA,QACA;AAAA,UACE,WAAW;AAAA,UACX,UAAU;AAAA,gBACQ,oBAAAA,MAAM,OAAO,EAAE,WAAW,yDAAyD,UAAU;AAAA,kBAC3F,oBAAAD,KAAK,OAAO,EAAE,WAAW,iCAAiC,cAA0B,oBAAAA;AAAA,gBAClG;AAAA,gBACA;AAAA,kBACE,MAAM;AAAA,kBACN,QAAQ;AAAA,kBACR,WAAW;AAAA,kBACX,SAAS,aAAa,cAAc;AAAA,kBACpC,UAAUD,GAAE,aAAa;AAAA,gBAC3B;AAAA,cACF,EAAE,CAAC;AAAA,kBACa,oBAAAC;AAAA,gBACd,KAAK;AAAA,gBACL;AAAA,kBACE,SAAS,KAAK;AAAA,kBACd,MAAM,aAAa,cAAc;AAAA,kBACjC,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,+BAAuB,oBAAAA,KAAK,KAAK,MAAM,EAAE,cAA0B,oBAAAA,KAAK,KAAK,SAAS,EAAE,cAA0B,oBAAAA;AAAA,sBAChH;AAAA,sBACA;AAAA,wBACE,GAAG;AAAA,wBACH,SAAS,MAAM,QAAQ,IAAI,CAAC,OAAO;AAAA,0BACjC,GAAG;AAAA,0BACH,UAAU;AAAA,4BACR;AAAA,4BACA;AAAA,0BACF;AAAA,wBACF,EAAE;AAAA,wBACF,aAAa,MAAM;AAAA,wBACnB,qBAAqB,MAAM;AAAA,wBAC3B,eAAe,MAAM;AAAA,wBACrB,WAAW;AAAA,wBACX,aAAaD;AAAA,0BACX;AAAA,wBACF;AAAA,sBACF;AAAA,oBACF,EAAE,CAAC,EAAE,CAAC;AAAA,kBACR;AAAA,gBACF;AAAA,cACF;AAAA,kBACgB,oBAAAC,KAAK,OAAO,EAAE,WAAW,iCAAiC,cAA0B,oBAAAA;AAAA,gBAClG;AAAA,gBACA;AAAA,kBACE,MAAM;AAAA,kBACN,QAAQ;AAAA,kBACR,WAAW;AAAA,kBACX,SAAS,aAAa,cAAc;AAAA,kBACpC,UAAUD,GAAE,iBAAiB;AAAA,gBAC/B;AAAA,cACF,EAAE,CAAC;AAAA,kBACa,oBAAAC;AAAA,gBACd,KAAK;AAAA,gBACL;AAAA,kBACE,SAAS,KAAK;AAAA,kBACd,MAAM,aAAa,cAAc;AAAA,kBACjC,QAAQ,CAAC,EAAE,OAAO,EAAE,UAAU,OAAO,GAAG,MAAM,EAAE,MAAM;AACpD,+BAAuB,oBAAAC,MAAM,KAAK,MAAM,EAAE,UAAU;AAAA,0BAClC,oBAAAD,KAAK,KAAK,SAAS,EAAE,cAA0B,oBAAAA;AAAA,wBAC7D;AAAA,wBACA;AAAA,0BACE,MAAM;AAAA,0BACN,WAAW;AAAA,0BACX,KAAK;AAAA,0BACL;AAAA,0BACA,UAAU,CAAC,MAAM;AACf,kCAAM,SAAS,EAAE,OAAO;AACxB,gCAAI,WAAW,IAAI;AACjB,uCAAS,IAAI;AAAA,4BACf,OAAO;AACL,uCAAS,OAAO,MAAM,CAAC;AAAA,4BACzB;AAAA,0BACF;AAAA,0BACA,GAAG;AAAA,0BACH,aAAaD;AAAA,4BACX;AAAA,0BACF;AAAA,wBACF;AAAA,sBACF,EAAE,CAAC;AAAA,0BACa,oBAAAC,KAAK,KAAK,cAAc,CAAC,CAAC;AAAA,oBAC5C,EAAE,CAAC;AAAA,kBACL;AAAA,gBACF;AAAA,cACF;AAAA,YACF,EAAE,CAAC;AAAA,gBACa,oBAAAA;AAAA,cACd;AAAA,cACA;AAAA,gBACE,MAAM;AAAA,gBACN,MAAM;AAAA,gBACN,SAAS;AAAA,gBACT,WAAW;AAAA,gBACX,SAAS,MAAM,UAAU,OAAO,cAAc;AAAA,gBAC9C,cAA0B,oBAAAA,KAAK,WAAW,CAAC,CAAC;AAAA,cAC9C;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,QACA,cAAc;AAAA,MAChB,CAAC;AAAA,IACH,EAAE,CAAC;AAAA,EACL,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;AACX;AACA,IAAI,4BAA4B;AAOhC,SAAS,WAAW,EAAE,KAAK,GAAG;AAC5B,QAAM,EAAE,MAAM,IAAI,SAAS;AAC3B,QAAM,EAAE,QAAQ,IAAI,WAAW,EAAE,OAAO,KAAK,CAAC;AAC9C,QAAM,EAAE,mBAAmB,iBAAiB,IAAI,oBAAoB,CAAC,CAAC;AACtE,QAAM,EAAE,iBAAiB,IAAI,cAAc;AAC3C,QAAM,UAAU,2BAA2B;AAAA,IACzC,YAAY,+BAAO;AAAA,IACnB;AAAA,IACA;AAAA,EACF,CAAC;AACD,QAAM,UAAU,SAAU;AAAA,IACxB,SAAS,KAAK;AAAA,EAChB,CAAC;AACD,aAAuB,oBAAAE;AAAA,IACrB;AAAA,IACA;AAAA,MACE;AAAA,MACA,MAAM,CAAC,OAAO;AAAA,MACd,OAAO;AAAA,MACP,iBAAiB,CAAC,YAAY,iBAAiB,CAAC,OAAO;AAAA,IACzD;AAAA,EACF;AACF;AACA,IAAI,eAAe,qBAAqB;AACxC,IAAI,6BAA6B,CAAC;AAAA,EAChC,aAAa,CAAC;AAAA,EACd,UAAU,CAAC;AAAA,EACX,mBAAmB,CAAC;AACtB,MAAM;AACJ,QAAM,EAAE,GAAAH,GAAE,IAAI,eAAgB;AAC9B,aAAO,uBAAQ,MAAM;AACnB,WAAO;AAAA,MACL,aAAa,OAAO;AAAA,QAClB,IAAIA,GAAE,cAAc;AAAA,QACpB,QAAQA,GAAE,cAAc;AAAA,QACxB,MAAM,CAAC,YAAY;AACjB,gBAAM,SAAS,QAAQ,IAAI;AAC3B,qBAAuB,oBAAAG,KAAK,SAAS,cAAc,EAAE,SAAS,cAA0B,oBAAAA,KAAK,OAAO,EAAE,WAAW,2DAA2D,cAA0B,oBAAAA,KAAK,QAAQ,EAAE,WAAW,YAAY,UAAU,OAAO,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC;AAAA,QAC5Q;AAAA,QACA,eAAe;AAAA,MACjB,CAAC;AAAA,MACD,GAAG,2BAA2B;AAAA,QAC5B,YAAY,WAAW,IAAI,CAAC,MAAM,EAAE,aAAa;AAAA,QACjD;AAAA,QACA;AAAA,QACA,cAAc,CAAC,SAAS,UAAU;AAze1C;AA0eU,eAAI,aAAQ,OAAO,OAAf,mBAAmB,WAAW,oBAAoB;AACpD,mBAAO,UAAU,KAAK;AAAA,UACxB;AACA,iBAAO,UAAU,KAAK;AAAA,QACxB;AAAA,QACA,GAAAH;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF,GAAG,CAACA,IAAG,YAAY,SAAS,gBAAgB,CAAC;AAC/C;AACA,IAAI,sBAAsB;AAI1B,IAAI,kBAAkB;AAAA,EACpB;AAAA,IAAC;AAAA;AAAA,EAAqB,GAAG;AAAA,EACzB;AAAA,IAAC;AAAA;AAAA,EAAmB,GAAG;AAAA,EACvB;AAAA,IAAC;AAAA;AAAA,EAA2B,GAAG;AACjC;AACA,IAAI,2BAA2B,CAAC;AAAA,EAC9B;AACF,MAAM;AACJ,QAAM,EAAE,GAAAA,GAAE,IAAI,eAAgB;AAC9B,QAAM,EAAE,cAAc,IAAI,cAAc;AACxC,QAAM,CAAC,KAAK,MAAM,QAAI;AAAA,IAAS;AAAA;AAAA,EAAqB;AACpD,QAAM,CAAC,UAAU,WAAW,QAAI,uBAAS,eAAe;AACxD,QAAM,OAAO,QAAQ;AAAA,IACnB,eAAe;AAAA,MACb,KAAK;AAAA,MACL,OAAO;AAAA,MACP,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,SAAS,CAAC;AAAA,IACZ;AAAA,IACA,UAAU,EAAY,0BAA0B;AAAA,EAClD,CAAC;AACD,QAAM,EAAE,aAAa,UAAU,IAAI,wBAAwB,QAAQ,EAAE;AACrE,QAAM,EAAE,QAAQ,IAAI,WAAW,EAAE,OAAO,KAAK,CAAC;AAC9C,QAAM,yBAAqB,aAAAI,SAAS,MAAM;AACxC,QAAI,EAAC,mCAAS,SAAQ;AACpB,aAAO,CAAC;AAAA,IACV;AACA,WAAO,QAAQ,OAAO,CAAC,KAAK,QAAQ;AAClC,UAAI,IAAI,EAAE,IAAI,IAAI;AAClB,aAAO;AAAA,IACT,GAAG,CAAC,CAAC;AAAA,EACP,GAAG,CAAC,OAAO,CAAC;AACZ,QAAM,2BAA2B,SAAU;AAAA,IACzC,SAAS,KAAK;AAAA,IACd,MAAM;AAAA,EACR,CAAC;AACD,QAAM,wBAAwB,SAAU;AAAA,IACtC,SAAS,KAAK;AAAA,IACd,MAAM;AAAA,EACR,CAAC;AACD,QAAM,iBAAiB,cAAe;AAAA,IACpC,SAAS,KAAK;AAAA,IACd,MAAM;AAAA,EACR,CAAC;AACD,QAAM,sBAAsB,4BAA4B;AACxD,QAAM,eAAW,aAAAA,SAAS,MAAM;AAC9B,QAAI,qBAAqB;AACvB,aAAO;AAAA,QAAC;AAAA,QAAuB;AAAA,QAAqB;AAAA;AAAA,MAA2B;AAAA,IACjF;AACA,WAAO;AAAA,MAAC;AAAA,MAAuB;AAAA;AAAA,IAAmB;AAAA,EACpD,GAAG,CAAC,mBAAmB,CAAC;AACxB,8BAAU,MAAM;AACd,QAAI,yBAAyB,eAAe,OAAO,WAAW,GAAG;AAC/D,qBAAe,OAAO;AAAA,QACpB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,MACrB,CAAC;AAAA,IACH;AAAA,EACF,GAAG,CAAC,qBAAqB,CAAC;AAC1B,QAAM,kBAAkB,CAAC,WAAW;AAClC,QAAI,QAAQ,QAAQ;AAClB;AAAA,IACF;AACA,QAAI,SAAS,QAAQ,MAAM,IAAI,SAAS,QAAQ,GAAG,GAAG;AACpD,YAAM,oBAAoB;AAC1B,kBAAY,CAAC,UAAU;AAAA,QACrB,GAAG;AAAA,QACH,CAAC,GAAG,GAAG,oBAAoB,KAAK,GAAG,IAAI;AAAA,QACvC,CAAC,MAAM,GAAG;AAAA,MACZ,EAAE;AACF,aAAO,MAAM;AACb;AAAA,IACF;AACA,UAAM,OAAO,SAAS,MAAM,GAAG,SAAS,QAAQ,MAAM,CAAC;AACvD,eAAW,QAAQ,MAAM;AACvB,UAAI,SAAS,UAAuB;AAClC,YAAI,CAAC;AAAA,UACH;AAAA,UACA;AAAA,UACA;AAAA,QACF,GAAG;AACD,sBAAY,CAAC,UAAU;AAAA,YACrB,GAAG;AAAA,YACH,CAAC,IAAI,GAAG;AAAA,UACV,EAAE;AACF,iBAAO,IAAI;AACX;AAAA,QACF;AACA,oBAAY,CAAC,UAAU;AAAA,UACrB,GAAG;AAAA,UACH,CAAC,IAAI,GAAG;AAAA,QACV,EAAE;AAAA,MACJ,WAAW,SAAS,SAAqB;AACvC,YAAI,CAAC;AAAA,UACH;AAAA,UACA;AAAA,UACA;AAAA,QACF,GAAG;AACD,sBAAY,CAAC,UAAU;AAAA,YACrB,GAAG;AAAA,YACH,CAAC,IAAI,GAAG;AAAA,UACV,EAAE;AACF,iBAAO,IAAI;AACX;AAAA,QACF;AACA,oBAAY,CAAC,UAAU;AAAA,UACrB,GAAG;AAAA,UACH,CAAC,IAAI,GAAG;AAAA,QACV,EAAE;AAAA,MACJ;AAAA,IACF;AACA,gBAAY,CAAC,UAAU;AAAA,MACrB,GAAG;AAAA,MACH,CAAC,GAAG,GAAG;AAAA,MACP,CAAC,MAAM,GAAG;AAAA,IACZ,EAAE;AACF,WAAO,MAAM;AAAA,EACf;AACA,QAAM,gBAAgB,CAAC,SAAS;AAC9B,QAAI,SAAS,QAAQ,IAAI,IAAI,KAAK,SAAS,QAAQ;AACjD;AAAA,IACF;AACA,UAAM,UAAU,SAAS,SAAS,QAAQ,IAAI,IAAI,CAAC;AACnD,oBAAgB,OAAO;AAAA,EACzB;AACA,QAAM,eAAe,KAAK,aAAa,OAAO,SAAS;AACrD,UAAM,EAAE,iBAAiB,kBAAkB,KAAK,MAAM,IAAI;AAC1D,UAAM;AAAA,MACJ;AAAA,QACE;AAAA,QACA,KAAK,OAAO;AAAA,QACZ;AAAA,QACA;AAAA,QACA,SAAS,KAAK;AAAA,QACd,QAAQ,OAAO,QAAQ,KAAK,UAAU,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,kBAAkB,KAAK,MAAM;AAC3E,cAAI,UAAU,MAAM,UAAU,QAAQ;AACpC,mBAAO;AAAA,UACT;AACA,gBAAM,MAAM,CAAC;AACb,gBAAM,SAAS,WAAW,KAAK;AAC/B,cAAI,iBAAiB,WAAW,MAAM,GAAG;AACvC,gBAAI,QAAQ,EAAE,WAAW,iBAAiB;AAC1C,gBAAI,gBAAgB,mBAAmB,gBAAgB;AAAA,UACzD,OAAO;AACL,gBAAI,gBAAgB;AAAA,UACtB;AACA,cAAI,SAAS;AACb,iBAAO;AAAA,QACT,CAAC,EAAE,OAAO,OAAO;AAAA,QACjB,kBAAkB,KAAK,aAAa,CAAC,GAAG,IAAI,CAAC,MAAM;AACjD,cAAI,CAAC,EAAE,qBAAqB,CAAC,EAAE,mBAAmB;AAChD,mBAAO;AAAA,UACT;AACA,iBAAO;AAAA,YACL,GAAG;AAAA,YACH,mBAAmB,WAAW,EAAE,iBAAiB;AAAA,UACnD;AAAA,QACF,CAAC,EAAE,OAAO,OAAO;AAAA,MACnB;AAAA,MACA;AAAA,QACE,WAAW,MAAM;AACf,wBAAc;AAAA,QAChB;AAAA,QACA,SAAS,CAAC,UAAU;AAClB,gBAAM,MAAM,MAAM,OAAO;AAAA,QAC3B;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AACD,aAAuB,oBAAAC,KAAK,gBAAgB,MAAM,EAAE,MAAM,cAA0B,oBAAAA;AAAA,IAClF;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP,eAAe,CAAC,SAAS,gBAAgB,IAAI;AAAA,MAC7C,WAAW;AAAA,MACX,cAA0B,oBAAAC;AAAA,QACxB;AAAA,QACA;AAAA,UACE,UAAU;AAAA,UACV,WAAW;AAAA,UACX,UAAU;AAAA,gBACQ,oBAAAD,KAAK,gBAAgB,QAAQ,EAAE,cAA0B,oBAAAA,KAAK,OAAO,EAAE,WAAW,oDAAoD,cAA0B,oBAAAA,KAAK,OAAO,EAAE,WAAW,uCAAuC,cAA0B,oBAAAC,MAAM,aAAa,MAAM,EAAE,WAAW,2BAA2B,UAAU;AAAA,kBACnU,oBAAAD;AAAA,gBACd,aAAa;AAAA,gBACb;AAAA,kBACE,QAAQ,SAAS;AAAA,kBACjB,OAAO;AAAA,kBACP,UAAUL,GAAE,gCAAgC;AAAA,gBAC9C;AAAA,cACF;AAAA,kBACgB,oBAAAK;AAAA,gBACd,aAAa;AAAA,gBACb;AAAA,kBACE,QAAQ,SAAS;AAAA,kBACjB,OAAO;AAAA,kBACP,UAAUL,GAAE,+BAA+B;AAAA,gBAC7C;AAAA,cACF;AAAA,cACA,CAAC,CAAC,2BAAuC,oBAAAK;AAAA,gBACvC,aAAa;AAAA,gBACb;AAAA,kBACE,QAAQ,SAAS;AAAA,kBACjB,OAAO;AAAA,kBACP,UAAUL,GAAE,gCAAgC;AAAA,gBAC9C;AAAA,cACF;AAAA,YACF,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;AAAA,gBACI,oBAAAM,MAAM,gBAAgB,MAAM,EAAE,WAAW,6BAA6B,UAAU;AAAA,kBAC9E,oBAAAD;AAAA,gBACd,aAAa;AAAA,gBACb;AAAA,kBACE,WAAW;AAAA,kBACX,OAAO;AAAA,kBACP,cAA0B,oBAAAA,KAAK,qBAAqB,EAAE,MAAM,QAAQ,CAAC;AAAA,gBACvE;AAAA,cACF;AAAA,kBACgB,oBAAAA;AAAA,gBACd,aAAa;AAAA,gBACb;AAAA,kBACE,WAAW;AAAA,kBACX,OAAO;AAAA,kBACP,cAA0B,oBAAAA,KAAK,qBAAqB,EAAE,KAAK,CAAC;AAAA,gBAC9D;AAAA,cACF;AAAA,cACA,CAAC,CAAC,2BAAuC,oBAAAA;AAAA,gBACvC,aAAa;AAAA,gBACb;AAAA,kBACE,WAAW;AAAA,kBACX,OAAO;AAAA,kBACP,cAA0B,oBAAAA,KAAK,2BAA2B,EAAE,KAAK,CAAC;AAAA,gBACpE;AAAA,cACF;AAAA,YACF,EAAE,CAAC;AAAA,gBACa,oBAAAA,KAAK,gBAAgB,QAAQ,EAAE,cAA0B,oBAAAC,MAAM,OAAO,EAAE,WAAW,yCAAyC,UAAU;AAAA,kBACpI,oBAAAD,KAAK,YAAY,OAAO,EAAE,SAAS,MAAM,cAA0B,oBAAAA,KAAK,QAAS,EAAE,SAAS,aAAa,MAAM,SAAS,UAAUL,GAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;AAAA,kBAC1J,oBAAAK;AAAA,gBACd;AAAA,gBACA;AAAA,kBACE;AAAA,kBACA,MAAM;AAAA,kBACN,WAAW;AAAA,kBACX,qBAAqB,CAAC,CAAC;AAAA,gBACzB;AAAA,cACF;AAAA,YACF,EAAE,CAAC,EAAE,CAAC;AAAA,UACR;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;AACA,IAAI,gBAAgB,CAAC;AAAA,EACnB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACJ,QAAM,EAAE,GAAAL,GAAE,IAAI,eAAgB;AAC9B,MAAI,uBAAuB,QAAQ,eAA+B,CAAC,uBAAuB,QAAQ,SAAqB;AACrH,eAAuB,oBAAAK;AAAA,MACrB;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,SAAS;AAAA,QACT,MAAM;AAAA,QACN;AAAA,QACA,UAAUL,GAAE,cAAc;AAAA,MAC5B;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACA,aAAuB,oBAAAK;AAAA,IACrB;AAAA,IACA;AAAA,MACE,MAAM;AAAA,MACN,SAAS;AAAA,MACT,MAAM;AAAA,MACN,SAAS,MAAM,KAAK,GAAG;AAAA,MACvB,UAAUL,GAAE,kBAAkB;AAAA,IAChC;AAAA,IACA;AAAA,EACF;AACF;AAIA,IAAI,uBAAuB,MAAM;AAC/B,QAAM,EAAE,GAAG,IAAI,UAAU;AACzB,QAAM,EAAE,SAAS,WAAW,SAAS,MAAM,IAAI,WAAW,EAAE;AAC5D,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,oBAAAO,KAAK,iBAAiB,EAAE,UAAU,CAAC,aAAa,eAA2B,oBAAAA,KAAK,0BAA0B,EAAE,QAAQ,CAAC,EAAE,CAAC;AACjJ;", "names": ["import_jsx_runtime", "import_react", "import_jsx_runtime", "t", "jsx2", "jsxs2", "jsx3", "useMemo2", "jsx4", "jsxs3", "jsx5"]}