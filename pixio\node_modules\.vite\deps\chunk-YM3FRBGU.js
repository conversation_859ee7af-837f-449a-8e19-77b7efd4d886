import {
  motion
} from "./chunk-7M4ICL3D.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/chunk-D3YQN7HV.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var ProgressBar = ({ duration = 2 }) => {
  return (0, import_jsx_runtime.jsx)(
    motion.div,
    {
      className: "bg-ui-fg-subtle size-full",
      initial: {
        width: "0%"
      },
      transition: {
        delay: 0.2,
        duration,
        ease: "linear"
      },
      animate: {
        width: "90%"
      },
      exit: {
        width: "100%",
        transition: { duration: 0.2, ease: "linear" }
      }
    }
  );
};

export {
  ProgressBar
};
//# sourceMappingURL=chunk-YM3FRBGU.js.map
