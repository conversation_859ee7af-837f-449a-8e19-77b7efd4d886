import {
  Trans,
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import {
  Link
} from "./chunk-T7YBVUWZ.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/chunk-P3DRE4IY.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var ReturnShippingPlaceholder = () => {
  const { t } = useTranslation();
  return (0, import_jsx_runtime.jsxs)("div", { className: "flex h-[120px] flex-col items-center justify-center gap-2 p-2 text-center", children: [
    (0, import_jsx_runtime.jsx)("span", { className: "txt-small text-ui-fg-subtle font-medium", children: t("orders.returns.placeholders.noReturnShippingOptions.title") }),
    (0, import_jsx_runtime.jsx)("span", { className: "txt-small text-ui-fg-muted", children: (0, import_jsx_runtime.jsx)(
      Trans,
      {
        i18nKey: "orders.returns.placeholders.noReturnShippingOptions.hint",
        components: {
          LinkComponent: (0, import_jsx_runtime.jsx)(Link, { to: `/settings/locations`, className: "text-blue-500" })
        }
      }
    ) })
  ] });
};
var OutboundShippingPlaceholder = () => {
  const { t } = useTranslation();
  return (0, import_jsx_runtime.jsxs)("div", { className: "flex h-[120px] flex-col items-center justify-center gap-2 p-2 text-center", children: [
    (0, import_jsx_runtime.jsx)("span", { className: "txt-small text-ui-fg-subtle font-medium", children: t("orders.returns.placeholders.outboundShippingOptions.title") }),
    (0, import_jsx_runtime.jsx)("span", { className: "txt-small text-ui-fg-muted", children: (0, import_jsx_runtime.jsx)(
      Trans,
      {
        i18nKey: "orders.returns.placeholders.outboundShippingOptions.hint",
        components: {
          LinkComponent: (0, import_jsx_runtime.jsx)(Link, { to: `/settings/locations`, className: "text-blue-500" })
        }
      }
    ) })
  ] });
};

export {
  ReturnShippingPlaceholder,
  OutboundShippingPlaceholder
};
//# sourceMappingURL=chunk-YD5FTRQU.js.map
