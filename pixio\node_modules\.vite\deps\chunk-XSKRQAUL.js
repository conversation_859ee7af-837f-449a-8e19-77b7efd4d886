import {
  Visually<PERSON><PERSON><PERSON>
} from "./chunk-TAVTGMIK.js";
import {
  CreateCampaignFormFields
} from "./chunk-ENAZYTQU.js";
import {
  KeyboundForm
} from "./chunk-DP54EP6X.js";
import {
  RouteFocusModal,
  useRouteModal
} from "./chunk-MVVOBQIC.js";
import {
  t
} from "./chunk-WHQIBI5S.js";
import {
  dateType,
  enumType,
  numberType,
  objectType,
  stringType
} from "./chunk-4XXECALA.js";
import {
  useForm
} from "./chunk-XXJU43CK.js";
import {
  useCreateCampaign
} from "./chunk-TZWW72YW.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import {
  Button,
  toast
} from "./chunk-YP2LLXWB.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/chunk-EMJCDNY5.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var DEFAULT_CAMPAIGN_VALUES = {
  name: "",
  description: "",
  campaign_identifier: "",
  starts_at: null,
  ends_at: null,
  budget: {
    type: "usage",
    currency_code: null,
    limit: null
  }
};
var CreateCampaignSchema = objectType({
  name: stringType().min(1),
  description: stringType().optional(),
  campaign_identifier: stringType().min(1),
  starts_at: dateType().nullable(),
  ends_at: dateType().nullable(),
  budget: objectType({
    limit: numberType().min(0).nullish(),
    type: enumType(["spend", "usage"]),
    currency_code: stringType().nullish()
  })
});
var CreateCampaignForm = () => {
  const { t: t2 } = useTranslation();
  const { handleSuccess } = useRouteModal();
  const { mutateAsync, isPending } = useCreateCampaign();
  const form = useForm({
    defaultValues: DEFAULT_CAMPAIGN_VALUES,
    resolver: t(CreateCampaignSchema)
  });
  const handleSubmit = form.handleSubmit(async (data) => {
    await mutateAsync(
      {
        name: data.name,
        description: data.description,
        campaign_identifier: data.campaign_identifier,
        starts_at: data.starts_at,
        ends_at: data.ends_at,
        budget: {
          type: data.budget.type,
          limit: data.budget.limit ? data.budget.limit : void 0,
          currency_code: data.budget.currency_code
        }
      },
      {
        onSuccess: ({ campaign }) => {
          toast.success(
            t2("campaigns.create.successToast", {
              name: campaign.name
            })
          );
          handleSuccess(`/campaigns/${campaign.id}`);
        },
        onError: (error) => {
          toast.error(error.message);
        }
      }
    );
  });
  return (0, import_jsx_runtime.jsx)(RouteFocusModal.Form, { form, children: (0, import_jsx_runtime.jsxs)(
    KeyboundForm,
    {
      onSubmit: handleSubmit,
      className: "flex size-full flex-col overflow-hidden",
      children: [
        (0, import_jsx_runtime.jsxs)(RouteFocusModal.Header, { children: [
          (0, import_jsx_runtime.jsx)(RouteFocusModal.Title, { asChild: true, children: (0, import_jsx_runtime.jsx)(VisuallyHidden, { children: t2("campaigns.create.title") }) }),
          (0, import_jsx_runtime.jsx)(RouteFocusModal.Description, { asChild: true, children: (0, import_jsx_runtime.jsx)(VisuallyHidden, { children: t2("campaigns.create.description") }) })
        ] }),
        (0, import_jsx_runtime.jsx)(RouteFocusModal.Body, { className: "flex size-full flex-col items-center overflow-auto py-16", children: (0, import_jsx_runtime.jsx)(CreateCampaignFormFields, { form }) }),
        (0, import_jsx_runtime.jsx)(RouteFocusModal.Footer, { children: (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center justify-end gap-x-2", children: [
          (0, import_jsx_runtime.jsx)(RouteFocusModal.Close, { asChild: true, children: (0, import_jsx_runtime.jsx)(Button, { size: "small", variant: "secondary", children: t2("actions.cancel") }) }),
          (0, import_jsx_runtime.jsx)(
            Button,
            {
              size: "small",
              variant: "primary",
              type: "submit",
              isLoading: isPending,
              children: t2("actions.create")
            }
          )
        ] }) })
      ]
    }
  ) });
};

export {
  DEFAULT_CAMPAIGN_VALUES,
  CreateCampaignSchema,
  CreateCampaignForm
};
//# sourceMappingURL=chunk-XSKRQAUL.js.map
