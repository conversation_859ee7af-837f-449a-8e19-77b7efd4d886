{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-RLY2SL5E.mjs"], "sourcesContent": ["import {\n  queryClient\n} from \"./chunk-FXYH54JP.mjs\";\nimport {\n  queryKeysFactory\n} from \"./chunk-774WSTCC.mjs\";\nimport {\n  sdk\n} from \"./chunk-DEQUVHHE.mjs\";\n\n// src/hooks/api/invites.tsx\nimport {\n  useMutation,\n  useQuery\n} from \"@tanstack/react-query\";\nvar INVITES_QUERY_KEY = \"invites\";\nvar invitesQueryKeys = queryKeysFactory(INVITES_QUERY_KEY);\nvar useInvites = (query, options) => {\n  const { data, ...rest } = useQuery({\n    queryFn: () => sdk.admin.invite.list(query),\n    queryKey: invitesQueryKeys.list(query),\n    ...options\n  });\n  return { ...data, ...rest };\n};\nvar useCreateInvite = (options) => {\n  return useMutation({\n    mutationFn: (payload) => sdk.admin.invite.create(payload),\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries({ queryKey: invitesQueryKeys.lists() });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\nvar useResendInvite = (id, options) => {\n  return useMutation({\n    mutationFn: () => sdk.admin.invite.resend(id),\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries({ queryKey: invitesQueryKeys.lists() });\n      queryClient.invalidateQueries({ queryKey: invitesQueryKeys.detail(id) });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\nvar useDeleteInvite = (id, options) => {\n  return useMutation({\n    mutationFn: () => sdk.admin.invite.delete(id),\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries({ queryKey: invitesQueryKeys.lists() });\n      queryClient.invalidateQueries({ queryKey: invitesQueryKeys.detail(id) });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\nvar useAcceptInvite = (inviteToken, options) => {\n  return useMutation({\n    mutationFn: (payload) => {\n      const { auth_token, ...rest } = payload;\n      return sdk.admin.invite.accept(\n        { invite_token: inviteToken, ...rest },\n        {},\n        {\n          Authorization: `Bearer ${auth_token}`\n        }\n      );\n    },\n    onSuccess: (data, variables, context) => {\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\n\nexport {\n  useInvites,\n  useCreateInvite,\n  useResendInvite,\n  useDeleteInvite,\n  useAcceptInvite\n};\n"], "mappings": ";;;;;;;;;;;;;;;AAeA,IAAI,oBAAoB;AACxB,IAAI,mBAAmB,iBAAiB,iBAAiB;AACzD,IAAI,aAAa,CAAC,OAAO,YAAY;AACnC,QAAM,EAAE,MAAM,GAAG,KAAK,IAAI,SAAS;AAAA,IACjC,SAAS,MAAM,IAAI,MAAM,OAAO,KAAK,KAAK;AAAA,IAC1C,UAAU,iBAAiB,KAAK,KAAK;AAAA,IACrC,GAAG;AAAA,EACL,CAAC;AACD,SAAO,EAAE,GAAG,MAAM,GAAG,KAAK;AAC5B;AACA,IAAI,kBAAkB,CAAC,YAAY;AACjC,SAAO,YAAY;AAAA,IACjB,YAAY,CAAC,YAAY,IAAI,MAAM,OAAO,OAAO,OAAO;AAAA,IACxD,WAAW,CAAC,MAAM,WAAW,YAAY;AA5B7C;AA6BM,kBAAY,kBAAkB,EAAE,UAAU,iBAAiB,MAAM,EAAE,CAAC;AACpE,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;AACA,IAAI,kBAAkB,CAAC,IAAI,YAAY;AACrC,SAAO,YAAY;AAAA,IACjB,YAAY,MAAM,IAAI,MAAM,OAAO,OAAO,EAAE;AAAA,IAC5C,WAAW,CAAC,MAAM,WAAW,YAAY;AAtC7C;AAuCM,kBAAY,kBAAkB,EAAE,UAAU,iBAAiB,MAAM,EAAE,CAAC;AACpE,kBAAY,kBAAkB,EAAE,UAAU,iBAAiB,OAAO,EAAE,EAAE,CAAC;AACvE,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;AACA,IAAI,kBAAkB,CAAC,IAAI,YAAY;AACrC,SAAO,YAAY;AAAA,IACjB,YAAY,MAAM,IAAI,MAAM,OAAO,OAAO,EAAE;AAAA,IAC5C,WAAW,CAAC,MAAM,WAAW,YAAY;AAjD7C;AAkDM,kBAAY,kBAAkB,EAAE,UAAU,iBAAiB,MAAM,EAAE,CAAC;AACpE,kBAAY,kBAAkB,EAAE,UAAU,iBAAiB,OAAO,EAAE,EAAE,CAAC;AACvE,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;AACA,IAAI,kBAAkB,CAAC,aAAa,YAAY;AAC9C,SAAO,YAAY;AAAA,IACjB,YAAY,CAAC,YAAY;AACvB,YAAM,EAAE,YAAY,GAAG,KAAK,IAAI;AAChC,aAAO,IAAI,MAAM,OAAO;AAAA,QACtB,EAAE,cAAc,aAAa,GAAG,KAAK;AAAA,QACrC,CAAC;AAAA,QACD;AAAA,UACE,eAAe,UAAU,UAAU;AAAA,QACrC;AAAA,MACF;AAAA,IACF;AAAA,IACA,WAAW,CAAC,MAAM,WAAW,YAAY;AArE7C;AAsEM,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;", "names": []}