{"version": 3, "sources": ["../../@medusajs/dashboard/dist/location-service-zone-shipping-option-edit-QGVO2QGV.mjs"], "sourcesContent": ["import {\n  isOptionEnabledInStore\n} from \"./chunk-R2O6QX4D.mjs\";\nimport {\n  ShippingOptionPriceType\n} from \"./chunk-PYIO3TDQ.mjs\";\nimport {\n  useComboboxData\n} from \"./chunk-YIZSVS2R.mjs\";\nimport {\n  Combobox\n} from \"./chunk-GZBFGV7Y.mjs\";\nimport {\n  SwitchBox\n} from \"./chunk-D7H6ZNK4.mjs\";\nimport \"./chunk-IUCDCPJU.mjs\";\nimport {\n  KeyboundForm\n} from \"./chunk-6HTZNHPT.mjs\";\nimport {\n  RouteDrawer,\n  useRouteModal\n} from \"./chunk-4TC5YS65.mjs\";\nimport {\n  Form\n} from \"./chunk-OBQI23QM.mjs\";\nimport {\n  useShippingOptions,\n  useUpdateShippingOptions\n} from \"./chunk-GRT22PE5.mjs\";\nimport \"./chunk-32IQRUVY.mjs\";\nimport \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport {\n  sdk\n} from \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-RPUOO7AV.mjs\";\n\n// src/routes/locations/location-service-zone-shipping-option-edit/location-service-zone-shipping-option-edit.tsx\nimport { Heading } from \"@medusajs/ui\";\nimport { useTranslation as useTranslation2 } from \"react-i18next\";\nimport { json, useParams } from \"react-router-dom\";\n\n// src/routes/locations/location-service-zone-shipping-option-edit/components/edit-region-form/edit-shipping-option-form.tsx\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { Button, Divider, Input, RadioGroup, toast } from \"@medusajs/ui\";\nimport { useForm } from \"react-hook-form\";\nimport { useTranslation } from \"react-i18next\";\nimport * as zod from \"zod\";\n\n// src/lib/common.ts\nfunction pick(obj, keys) {\n  const ret = {};\n  keys.forEach((k) => {\n    if (k in obj) {\n      ret[k] = obj[k];\n    }\n  });\n  return ret;\n}\n\n// src/routes/locations/location-service-zone-shipping-option-edit/components/edit-region-form/edit-shipping-option-form.tsx\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar EditShippingOptionSchema = zod.object({\n  name: zod.string().min(1),\n  price_type: zod.nativeEnum(ShippingOptionPriceType),\n  enabled_in_store: zod.boolean().optional(),\n  shipping_profile_id: zod.string()\n});\nvar EditShippingOptionForm = ({\n  locationId,\n  shippingOption,\n  type\n}) => {\n  const { t } = useTranslation();\n  const { handleSuccess } = useRouteModal();\n  const isPickup = type === \"pickup\" /* Pickup */;\n  const shippingProfiles = useComboboxData({\n    queryFn: (params) => sdk.admin.shippingProfile.list(params),\n    queryKey: [\"shipping_profiles\"],\n    getOptions: (data) => data.shipping_profiles.map((profile) => ({\n      label: profile.name,\n      value: profile.id\n    })),\n    defaultValue: shippingOption.shipping_profile_id\n  });\n  const form = useForm({\n    defaultValues: {\n      name: shippingOption.name,\n      price_type: shippingOption.price_type,\n      enabled_in_store: isOptionEnabledInStore(shippingOption),\n      shipping_profile_id: shippingOption.shipping_profile_id\n    },\n    resolver: zodResolver(EditShippingOptionSchema)\n  });\n  const { mutateAsync, isPending: isLoading } = useUpdateShippingOptions(\n    shippingOption.id\n  );\n  const handleSubmit = form.handleSubmit(async (values) => {\n    const rules = shippingOption.rules.map((r) => ({\n      ...pick(r, [\"id\", \"attribute\", \"operator\", \"value\"])\n    }));\n    const storeRule = rules.find((r) => r.attribute === \"enabled_in_store\");\n    if (!storeRule) {\n      rules.push({\n        value: values.enabled_in_store ? \"true\" : \"false\",\n        attribute: \"enabled_in_store\",\n        operator: \"eq\"\n      });\n    } else {\n      storeRule.value = values.enabled_in_store ? \"true\" : \"false\";\n    }\n    await mutateAsync(\n      {\n        name: values.name,\n        price_type: values.price_type,\n        shipping_profile_id: values.shipping_profile_id,\n        rules\n      },\n      {\n        onSuccess: ({ shipping_option }) => {\n          toast.success(\n            t(\"stockLocations.shippingOptions.edit.successToast\", {\n              name: shipping_option.name\n            })\n          );\n          handleSuccess(`/settings/locations/${locationId}`);\n        },\n        onError: (e) => {\n          toast.error(e.message);\n        }\n      }\n    );\n  });\n  return /* @__PURE__ */ jsx(RouteDrawer.Form, { form, children: /* @__PURE__ */ jsxs(KeyboundForm, { onSubmit: handleSubmit, className: \"flex flex-1 flex-col\", children: [\n    /* @__PURE__ */ jsx(RouteDrawer.Body, { children: /* @__PURE__ */ jsx(\"div\", { className: \"flex flex-col gap-y-8\", children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex flex-col gap-y-8\", children: [\n      !isPickup && /* @__PURE__ */ jsx(\n        Form.Field,\n        {\n          control: form.control,\n          name: \"price_type\",\n          render: ({ field }) => {\n            return /* @__PURE__ */ jsxs(Form.Item, { children: [\n              /* @__PURE__ */ jsx(Form.Label, { children: t(\n                \"stockLocations.shippingOptions.fields.priceType.label\"\n              ) }),\n              /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsxs(RadioGroup, { ...field, onValueChange: field.onChange, children: [\n                /* @__PURE__ */ jsx(\n                  RadioGroup.ChoiceBox,\n                  {\n                    className: \"flex-1\",\n                    value: \"flat\" /* FlatRate */,\n                    label: t(\n                      \"stockLocations.shippingOptions.fields.priceType.options.fixed.label\"\n                    ),\n                    description: t(\n                      \"stockLocations.shippingOptions.fields.priceType.options.fixed.hint\"\n                    )\n                  }\n                ),\n                /* @__PURE__ */ jsx(\n                  RadioGroup.ChoiceBox,\n                  {\n                    className: \"flex-1\",\n                    value: \"calculated\" /* Calculated */,\n                    label: t(\n                      \"stockLocations.shippingOptions.fields.priceType.options.calculated.label\"\n                    ),\n                    description: t(\n                      \"stockLocations.shippingOptions.fields.priceType.options.calculated.hint\"\n                    )\n                  }\n                )\n              ] }) }),\n              /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n            ] });\n          }\n        }\n      ),\n      /* @__PURE__ */ jsxs(\"div\", { className: \"grid gap-y-4\", children: [\n        /* @__PURE__ */ jsx(\n          Form.Field,\n          {\n            control: form.control,\n            name: \"name\",\n            render: ({ field }) => {\n              return /* @__PURE__ */ jsxs(Form.Item, { children: [\n                /* @__PURE__ */ jsx(Form.Label, { children: t(\"fields.name\") }),\n                /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(Input, { ...field }) }),\n                /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n              ] });\n            }\n          }\n        ),\n        /* @__PURE__ */ jsx(\n          Form.Field,\n          {\n            control: form.control,\n            name: \"shipping_profile_id\",\n            render: ({ field }) => {\n              return /* @__PURE__ */ jsxs(Form.Item, { children: [\n                /* @__PURE__ */ jsx(Form.Label, { children: t(\"stockLocations.shippingOptions.fields.profile\") }),\n                /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(\n                  Combobox,\n                  {\n                    ...field,\n                    options: shippingProfiles.options,\n                    searchValue: shippingProfiles.searchValue,\n                    onSearchValueChange: shippingProfiles.onSearchValueChange,\n                    disabled: shippingProfiles.disabled\n                  }\n                ) }),\n                /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n              ] });\n            }\n          }\n        )\n      ] }),\n      /* @__PURE__ */ jsx(Divider, {}),\n      /* @__PURE__ */ jsx(\n        SwitchBox,\n        {\n          control: form.control,\n          name: \"enabled_in_store\",\n          label: t(\n            \"stockLocations.shippingOptions.fields.enableInStore.label\"\n          ),\n          description: t(\n            \"stockLocations.shippingOptions.fields.enableInStore.hint\"\n          )\n        }\n      )\n    ] }) }) }),\n    /* @__PURE__ */ jsx(RouteDrawer.Footer, { children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center gap-x-2\", children: [\n      /* @__PURE__ */ jsx(RouteDrawer.Close, { asChild: true, children: /* @__PURE__ */ jsx(Button, { size: \"small\", variant: \"secondary\", children: t(\"actions.cancel\") }) }),\n      /* @__PURE__ */ jsx(Button, { size: \"small\", type: \"submit\", isLoading, children: t(\"actions.save\") })\n    ] }) })\n  ] }) });\n};\n\n// src/routes/locations/location-service-zone-shipping-option-edit/location-service-zone-shipping-option-edit.tsx\nimport { jsx as jsx2, jsxs as jsxs2 } from \"react/jsx-runtime\";\nvar LocationServiceZoneShippingOptionEdit = () => {\n  const { t } = useTranslation2();\n  const { location_id, so_id } = useParams();\n  const { shipping_options, isPending, isFetching, isError, error } = useShippingOptions({\n    id: so_id,\n    fields: \"+service_zone.fulfillment_set.type\"\n  });\n  const shippingOption = shipping_options?.find((so) => so.id === so_id);\n  if (!isPending && !isFetching && !shippingOption) {\n    throw json(\n      { message: `Shipping option with ID ${so_id} was not found` },\n      404\n    );\n  }\n  if (isError) {\n    throw error;\n  }\n  const isPickup = shippingOption?.service_zone.fulfillment_set.type === \"pickup\" /* Pickup */;\n  return /* @__PURE__ */ jsxs2(RouteDrawer, { children: [\n    /* @__PURE__ */ jsx2(RouteDrawer.Header, { children: /* @__PURE__ */ jsx2(Heading, { children: t(\n      `stockLocations.${isPickup ? \"pickupOptions\" : \"shippingOptions\"}.edit.header`\n    ) }) }),\n    shippingOption && /* @__PURE__ */ jsx2(\n      EditShippingOptionForm,\n      {\n        shippingOption,\n        locationId: location_id,\n        type: shippingOption.service_zone.fulfillment_set.type\n      }\n    )\n  ] });\n};\nexport {\n  LocationServiceZoneShippingOptionEdit as Component\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8DA,yBAA0B;AAmL1B,IAAAA,sBAA2C;AA9L3C,SAAS,KAAK,KAAK,MAAM;AACvB,QAAM,MAAM,CAAC;AACb,OAAK,QAAQ,CAAC,MAAM;AAClB,QAAI,KAAK,KAAK;AACZ,UAAI,CAAC,IAAI,IAAI,CAAC;AAAA,IAChB;AAAA,EACF,CAAC;AACD,SAAO;AACT;AAIA,IAAI,2BAA+B,WAAO;AAAA,EACxC,MAAU,WAAO,EAAE,IAAI,CAAC;AAAA,EACxB,YAAgB,eAAW,uBAAuB;AAAA,EAClD,kBAAsB,YAAQ,EAAE,SAAS;AAAA,EACzC,qBAAyB,WAAO;AAClC,CAAC;AACD,IAAI,yBAAyB,CAAC;AAAA,EAC5B;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACJ,QAAM,EAAE,GAAAC,GAAE,IAAI,eAAe;AAC7B,QAAM,EAAE,cAAc,IAAI,cAAc;AACxC,QAAM,WAAW,SAAS;AAC1B,QAAM,mBAAmB,gBAAgB;AAAA,IACvC,SAAS,CAAC,WAAW,IAAI,MAAM,gBAAgB,KAAK,MAAM;AAAA,IAC1D,UAAU,CAAC,mBAAmB;AAAA,IAC9B,YAAY,CAAC,SAAS,KAAK,kBAAkB,IAAI,CAAC,aAAa;AAAA,MAC7D,OAAO,QAAQ;AAAA,MACf,OAAO,QAAQ;AAAA,IACjB,EAAE;AAAA,IACF,cAAc,eAAe;AAAA,EAC/B,CAAC;AACD,QAAM,OAAO,QAAQ;AAAA,IACnB,eAAe;AAAA,MACb,MAAM,eAAe;AAAA,MACrB,YAAY,eAAe;AAAA,MAC3B,kBAAkB,uBAAuB,cAAc;AAAA,MACvD,qBAAqB,eAAe;AAAA,IACtC;AAAA,IACA,UAAU,EAAY,wBAAwB;AAAA,EAChD,CAAC;AACD,QAAM,EAAE,aAAa,WAAW,UAAU,IAAI;AAAA,IAC5C,eAAe;AAAA,EACjB;AACA,QAAM,eAAe,KAAK,aAAa,OAAO,WAAW;AACvD,UAAM,QAAQ,eAAe,MAAM,IAAI,CAAC,OAAO;AAAA,MAC7C,GAAG,KAAK,GAAG,CAAC,MAAM,aAAa,YAAY,OAAO,CAAC;AAAA,IACrD,EAAE;AACF,UAAM,YAAY,MAAM,KAAK,CAAC,MAAM,EAAE,cAAc,kBAAkB;AACtE,QAAI,CAAC,WAAW;AACd,YAAM,KAAK;AAAA,QACT,OAAO,OAAO,mBAAmB,SAAS;AAAA,QAC1C,WAAW;AAAA,QACX,UAAU;AAAA,MACZ,CAAC;AAAA,IACH,OAAO;AACL,gBAAU,QAAQ,OAAO,mBAAmB,SAAS;AAAA,IACvD;AACA,UAAM;AAAA,MACJ;AAAA,QACE,MAAM,OAAO;AAAA,QACb,YAAY,OAAO;AAAA,QACnB,qBAAqB,OAAO;AAAA,QAC5B;AAAA,MACF;AAAA,MACA;AAAA,QACE,WAAW,CAAC,EAAE,gBAAgB,MAAM;AAClC,gBAAM;AAAA,YACJA,GAAE,oDAAoD;AAAA,cACpD,MAAM,gBAAgB;AAAA,YACxB,CAAC;AAAA,UACH;AACA,wBAAc,uBAAuB,UAAU,EAAE;AAAA,QACnD;AAAA,QACA,SAAS,CAAC,MAAM;AACd,gBAAM,MAAM,EAAE,OAAO;AAAA,QACvB;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AACD,aAAuB,wBAAI,YAAY,MAAM,EAAE,MAAM,cAA0B,yBAAK,cAAc,EAAE,UAAU,cAAc,WAAW,wBAAwB,UAAU;AAAA,QACvJ,wBAAI,YAAY,MAAM,EAAE,cAA0B,wBAAI,OAAO,EAAE,WAAW,yBAAyB,cAA0B,yBAAK,OAAO,EAAE,WAAW,yBAAyB,UAAU;AAAA,MACvM,CAAC,gBAA4B;AAAA,QAC3B,KAAK;AAAA,QACL;AAAA,UACE,SAAS,KAAK;AAAA,UACd,MAAM;AAAA,UACN,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,uBAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,kBACjC,wBAAI,KAAK,OAAO,EAAE,UAAUA;AAAA,gBAC1C;AAAA,cACF,EAAE,CAAC;AAAA,kBACa,wBAAI,KAAK,SAAS,EAAE,cAA0B,yBAAK,YAAY,EAAE,GAAG,OAAO,eAAe,MAAM,UAAU,UAAU;AAAA,oBAClH;AAAA,kBACd,WAAW;AAAA,kBACX;AAAA,oBACE,WAAW;AAAA,oBACX,OAAO;AAAA,oBACP,OAAOA;AAAA,sBACL;AAAA,oBACF;AAAA,oBACA,aAAaA;AAAA,sBACX;AAAA,oBACF;AAAA,kBACF;AAAA,gBACF;AAAA,oBACgB;AAAA,kBACd,WAAW;AAAA,kBACX;AAAA,oBACE,WAAW;AAAA,oBACX,OAAO;AAAA,oBACP,OAAOA;AAAA,sBACL;AAAA,oBACF;AAAA,oBACA,aAAaA;AAAA,sBACX;AAAA,oBACF;AAAA,kBACF;AAAA,gBACF;AAAA,cACF,EAAE,CAAC,EAAE,CAAC;AAAA,kBACU,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,YAC3C,EAAE,CAAC;AAAA,UACL;AAAA,QACF;AAAA,MACF;AAAA,UACgB,yBAAK,OAAO,EAAE,WAAW,gBAAgB,UAAU;AAAA,YACjD;AAAA,UACd,KAAK;AAAA,UACL;AAAA,YACE,SAAS,KAAK;AAAA,YACd,MAAM;AAAA,YACN,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,yBAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,oBACjC,wBAAI,KAAK,OAAO,EAAE,UAAUA,GAAE,aAAa,EAAE,CAAC;AAAA,oBAC9C,wBAAI,KAAK,SAAS,EAAE,cAA0B,wBAAI,OAAO,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC;AAAA,oBACxE,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,cAC3C,EAAE,CAAC;AAAA,YACL;AAAA,UACF;AAAA,QACF;AAAA,YACgB;AAAA,UACd,KAAK;AAAA,UACL;AAAA,YACE,SAAS,KAAK;AAAA,YACd,MAAM;AAAA,YACN,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,yBAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,oBACjC,wBAAI,KAAK,OAAO,EAAE,UAAUA,GAAE,+CAA+C,EAAE,CAAC;AAAA,oBAChF,wBAAI,KAAK,SAAS,EAAE,cAA0B;AAAA,kBAC5D;AAAA,kBACA;AAAA,oBACE,GAAG;AAAA,oBACH,SAAS,iBAAiB;AAAA,oBAC1B,aAAa,iBAAiB;AAAA,oBAC9B,qBAAqB,iBAAiB;AAAA,oBACtC,UAAU,iBAAiB;AAAA,kBAC7B;AAAA,gBACF,EAAE,CAAC;AAAA,oBACa,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,cAC3C,EAAE,CAAC;AAAA,YACL;AAAA,UACF;AAAA,QACF;AAAA,MACF,EAAE,CAAC;AAAA,UACa,wBAAI,SAAS,CAAC,CAAC;AAAA,UACf;AAAA,QACd;AAAA,QACA;AAAA,UACE,SAAS,KAAK;AAAA,UACd,MAAM;AAAA,UACN,OAAOA;AAAA,YACL;AAAA,UACF;AAAA,UACA,aAAaA;AAAA,YACX;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;AAAA,QACO,wBAAI,YAAY,QAAQ,EAAE,cAA0B,yBAAK,OAAO,EAAE,WAAW,6BAA6B,UAAU;AAAA,UAClH,wBAAI,YAAY,OAAO,EAAE,SAAS,MAAM,cAA0B,wBAAI,QAAQ,EAAE,MAAM,SAAS,SAAS,aAAa,UAAUA,GAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;AAAA,UACvJ,wBAAI,QAAQ,EAAE,MAAM,SAAS,MAAM,UAAU,WAAW,UAAUA,GAAE,cAAc,EAAE,CAAC;AAAA,IACvG,EAAE,CAAC,EAAE,CAAC;AAAA,EACR,EAAE,CAAC,EAAE,CAAC;AACR;AAIA,IAAI,wCAAwC,MAAM;AAChD,QAAM,EAAE,GAAAA,GAAE,IAAI,eAAgB;AAC9B,QAAM,EAAE,aAAa,MAAM,IAAI,UAAU;AACzC,QAAM,EAAE,kBAAkB,WAAW,YAAY,SAAS,MAAM,IAAI,mBAAmB;AAAA,IACrF,IAAI;AAAA,IACJ,QAAQ;AAAA,EACV,CAAC;AACD,QAAM,iBAAiB,qDAAkB,KAAK,CAAC,OAAO,GAAG,OAAO;AAChE,MAAI,CAAC,aAAa,CAAC,cAAc,CAAC,gBAAgB;AAChD,UAAM;AAAA,MACJ,EAAE,SAAS,2BAA2B,KAAK,iBAAiB;AAAA,MAC5D;AAAA,IACF;AAAA,EACF;AACA,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,QAAM,YAAW,iDAAgB,aAAa,gBAAgB,UAAS;AACvE,aAAuB,oBAAAC,MAAM,aAAa,EAAE,UAAU;AAAA,QACpC,oBAAAC,KAAK,YAAY,QAAQ,EAAE,cAA0B,oBAAAA,KAAK,SAAS,EAAE,UAAUF;AAAA,MAC7F,kBAAkB,WAAW,kBAAkB,iBAAiB;AAAA,IAClE,EAAE,CAAC,EAAE,CAAC;AAAA,IACN,sBAAkC,oBAAAE;AAAA,MAChC;AAAA,MACA;AAAA,QACE;AAAA,QACA,YAAY;AAAA,QACZ,MAAM,eAAe,aAAa,gBAAgB;AAAA,MACpD;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;", "names": ["import_jsx_runtime", "t", "jsxs2", "jsx2"]}