import {
  l
} from "./chunk-T6Z4MBXP.js";
import {
  AvatarBox
} from "./chunk-5FT4TRK2.js";
import "./chunk-D2VV3NDE.js";
import {
  isFetchError
} from "./chunk-WTOMBGNF.js";
import {
  AnimatePresence,
  motion
} from "./chunk-7M4ICL3D.js";
import {
  t
} from "./chunk-WHQIBI5S.js";
import {
  ZodIssueCode,
  numberType,
  objectType,
  stringType
} from "./chunk-4XXECALA.js";
import {
  instance
} from "./chunk-MPXR7HT5.js";
import {
  Form,
  useForm
} from "./chunk-XXJU43CK.js";
import {
  useSignUpWithEmailPass
} from "./chunk-WAUSOHFQ.js";
import {
  useAcceptInvite
} from "./chunk-KISXY2LD.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import "./chunk-JFCIYOH4.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-5GF3RGIE.js";
import {
  Link,
  useSearchParams
} from "./chunk-T7YBVUWZ.js";
import {
  Alert,
  Button,
  Heading,
  Hint,
  Input,
  Text,
  toast
} from "./chunk-YP2LLXWB.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-ZSQ362B4.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/invite-IQITJV62.mjs
var import_react2 = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var CreateAccountSchema = objectType({
  email: stringType().email(),
  first_name: stringType().min(1),
  last_name: stringType().min(1),
  password: stringType().min(1),
  repeat_password: stringType().min(1)
}).superRefine(({ password, repeat_password }, ctx) => {
  if (password !== repeat_password) {
    ctx.addIssue({
      code: ZodIssueCode.custom,
      message: instance.t("invite.passwordMismatch"),
      path: ["repeat_password"]
    });
  }
});
var Invite = () => {
  const [searchParams] = useSearchParams();
  const [success, setSuccess] = (0, import_react2.useState)(false);
  const token = searchParams.get("token");
  const invite = token ? l(token) : null;
  const isValidInvite = invite && validateDecodedInvite(invite);
  return (0, import_jsx_runtime.jsx)("div", { className: "bg-ui-bg-subtle relative flex min-h-dvh w-dvw items-center justify-center p-4", children: (0, import_jsx_runtime.jsxs)("div", { className: "flex w-full max-w-[360px] flex-col items-center", children: [
    (0, import_jsx_runtime.jsx)(AvatarBox, { checked: success }),
    (0, import_jsx_runtime.jsx)("div", { className: "max-h-[557px] w-full will-change-contents", children: isValidInvite ? (0, import_jsx_runtime.jsx)(AnimatePresence, { children: !success ? (0, import_jsx_runtime.jsx)(
      motion.div,
      {
        initial: false,
        animate: {
          height: "557px",
          y: 0
        },
        exit: {
          height: 0,
          y: 40
        },
        transition: {
          duration: 0.8,
          delay: 0.6,
          ease: [0, 0.71, 0.2, 1.01]
        },
        className: "w-full will-change-transform",
        children: (0, import_jsx_runtime.jsx)(
          motion.div,
          {
            initial: false,
            animate: {
              opacity: 1,
              scale: 1
            },
            exit: {
              opacity: 0,
              scale: 0.7
            },
            transition: {
              duration: 0.6,
              delay: 0,
              ease: [0, 0.71, 0.2, 1.01]
            },
            children: (0, import_jsx_runtime.jsx)(
              CreateView,
              {
                onSuccess: () => setSuccess(true),
                token,
                invite
              }
            )
          },
          "inner-create-account"
        )
      },
      "create-account"
    ) : (0, import_jsx_runtime.jsx)(
      motion.div,
      {
        initial: {
          opacity: 0,
          scale: 0.4
        },
        animate: {
          opacity: 1,
          scale: 1
        },
        transition: {
          duration: 1,
          delay: 0.6,
          ease: [0, 0.71, 0.2, 1.01]
        },
        className: "w-full",
        children: (0, import_jsx_runtime.jsx)(SuccessView, {})
      },
      "success-view"
    ) }) : (0, import_jsx_runtime.jsx)(InvalidView, {}) })
  ] }) });
};
var LoginLink = () => {
  const { t: t2 } = useTranslation();
  return (0, import_jsx_runtime.jsxs)("div", { className: "flex w-full flex-col items-center", children: [
    (0, import_jsx_runtime.jsx)("div", { className: "my-6 h-px w-full border-b border-dotted" }),
    (0, import_jsx_runtime.jsx)(
      Link,
      {
        to: "/login",
        className: "txt-small text-ui-fg-base transition-fg hover:text-ui-fg-base-hover focus-visible:text-ui-fg-base-hover font-medium outline-none",
        children: t2("invite.backToLogin")
      },
      "login-link"
    )
  ] });
};
var InvalidView = () => {
  const { t: t2 } = useTranslation();
  return (0, import_jsx_runtime.jsxs)("div", { className: "flex flex-col items-center", children: [
    (0, import_jsx_runtime.jsxs)("div", { className: "flex flex-col items-center gap-y-1", children: [
      (0, import_jsx_runtime.jsx)(Heading, { children: t2("invite.invalidTokenTitle") }),
      (0, import_jsx_runtime.jsx)(Text, { size: "small", className: "text-ui-fg-subtle text-center", children: t2("invite.invalidTokenHint") })
    ] }),
    (0, import_jsx_runtime.jsx)(LoginLink, {})
  ] });
};
var CreateView = ({
  onSuccess,
  token,
  invite
}) => {
  var _a, _b, _c, _d, _e, _f;
  const { t: t2 } = useTranslation();
  const [invalid, setInvalid] = (0, import_react2.useState)(false);
  const [params] = useSearchParams();
  const isFirstRun = params.get("first_run") === "true";
  const form = useForm({
    resolver: t(CreateAccountSchema),
    defaultValues: {
      email: isFirstRun ? "" : invite.email || "",
      first_name: "",
      last_name: "",
      password: "",
      repeat_password: ""
    }
  });
  const { mutateAsync: signUpEmailPass, isPending: isCreatingAuthUser } = useSignUpWithEmailPass();
  const { mutateAsync: acceptInvite, isPending: isAcceptingInvite } = useAcceptInvite(token);
  const handleSubmit = form.handleSubmit(async (data) => {
    try {
      const authToken = await signUpEmailPass({
        email: data.email,
        password: data.password
      });
      const invitePayload = {
        email: data.email,
        first_name: data.first_name,
        last_name: data.last_name
      };
      await acceptInvite({
        ...invitePayload,
        auth_token: authToken
      });
      toast.success(t2("invite.toast.accepted"));
      onSuccess();
    } catch (error) {
      if (isFetchError(error) && error.status === 400) {
        form.setError("root", {
          type: "manual",
          message: t2("invite.invalidInvite")
        });
        setInvalid(true);
        return;
      }
      form.setError("root", {
        type: "manual",
        message: t2("errors.serverError")
      });
    }
  });
  const serverError = (_a = form.formState.errors.root) == null ? void 0 : _a.message;
  const validationError = ((_b = form.formState.errors.email) == null ? void 0 : _b.message) || ((_c = form.formState.errors.password) == null ? void 0 : _c.message) || ((_d = form.formState.errors.repeat_password) == null ? void 0 : _d.message) || ((_e = form.formState.errors.first_name) == null ? void 0 : _e.message) || ((_f = form.formState.errors.last_name) == null ? void 0 : _f.message);
  return (0, import_jsx_runtime.jsxs)("div", { className: "flex w-full flex-col items-center", children: [
    (0, import_jsx_runtime.jsxs)("div", { className: "mb-4 flex flex-col items-center", children: [
      (0, import_jsx_runtime.jsx)(Heading, { children: t2("invite.title") }),
      (0, import_jsx_runtime.jsx)(Text, { size: "small", className: "text-ui-fg-subtle text-center", children: t2("invite.hint") })
    ] }),
    (0, import_jsx_runtime.jsx)(Form, { ...form, children: (0, import_jsx_runtime.jsxs)("form", { onSubmit: handleSubmit, className: "flex w-full flex-col gap-y-6", children: [
      (0, import_jsx_runtime.jsxs)("div", { className: "flex flex-col gap-y-2", children: [
        (0, import_jsx_runtime.jsx)(
          Form.Field,
          {
            control: form.control,
            name: "email",
            render: ({ field }) => {
              return (0, import_jsx_runtime.jsx)(Form.Item, { children: (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(
                Input,
                {
                  autoComplete: "off",
                  ...field,
                  className: "bg-ui-bg-field-component",
                  placeholder: t2("fields.email")
                }
              ) }) });
            }
          }
        ),
        (0, import_jsx_runtime.jsx)(
          Form.Field,
          {
            control: form.control,
            name: "first_name",
            render: ({ field }) => {
              return (0, import_jsx_runtime.jsx)(Form.Item, { children: (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(
                Input,
                {
                  autoComplete: "given-name",
                  ...field,
                  className: "bg-ui-bg-field-component",
                  placeholder: t2("fields.firstName")
                }
              ) }) });
            }
          }
        ),
        (0, import_jsx_runtime.jsx)(
          Form.Field,
          {
            control: form.control,
            name: "last_name",
            render: ({ field }) => {
              return (0, import_jsx_runtime.jsx)(Form.Item, { children: (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(
                Input,
                {
                  autoComplete: "family-name",
                  ...field,
                  className: "bg-ui-bg-field-component",
                  placeholder: t2("fields.lastName")
                }
              ) }) });
            }
          }
        ),
        (0, import_jsx_runtime.jsx)(
          Form.Field,
          {
            control: form.control,
            name: "password",
            render: ({ field }) => {
              return (0, import_jsx_runtime.jsx)(Form.Item, { children: (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(
                Input,
                {
                  autoComplete: "new-password",
                  type: "password",
                  ...field,
                  className: "bg-ui-bg-field-component",
                  placeholder: t2("fields.password")
                }
              ) }) });
            }
          }
        ),
        (0, import_jsx_runtime.jsx)(
          Form.Field,
          {
            control: form.control,
            name: "repeat_password",
            render: ({ field }) => {
              return (0, import_jsx_runtime.jsx)(Form.Item, { children: (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(
                Input,
                {
                  autoComplete: "off",
                  type: "password",
                  ...field,
                  className: "bg-ui-bg-field-component",
                  placeholder: t2("fields.repeatPassword")
                }
              ) }) });
            }
          }
        ),
        validationError && (0, import_jsx_runtime.jsx)("div", { className: "mt-6 text-center", children: (0, import_jsx_runtime.jsx)(Hint, { className: "inline-flex", variant: "error", children: validationError }) }),
        serverError && (0, import_jsx_runtime.jsx)(
          Alert,
          {
            className: "bg-ui-bg-base items-center p-2",
            dismissible: true,
            variant: "error",
            children: serverError
          }
        )
      ] }),
      (0, import_jsx_runtime.jsx)(
        Button,
        {
          className: "w-full",
          type: "submit",
          isLoading: isCreatingAuthUser || isAcceptingInvite,
          disabled: invalid,
          children: t2("invite.createAccount")
        }
      )
    ] }) }),
    (0, import_jsx_runtime.jsx)(LoginLink, {})
  ] });
};
var SuccessView = () => {
  const { t: t2 } = useTranslation();
  return (0, import_jsx_runtime.jsxs)("div", { className: "flex w-full flex-col items-center gap-y-6", children: [
    (0, import_jsx_runtime.jsxs)("div", { className: "flex flex-col items-center gap-y-1", children: [
      (0, import_jsx_runtime.jsx)(Heading, { className: "text-center", children: t2("invite.successTitle") }),
      (0, import_jsx_runtime.jsx)(Text, { size: "small", className: "text-ui-fg-subtle text-center", children: t2("invite.successHint") })
    ] }),
    (0, import_jsx_runtime.jsx)(Button, { variant: "secondary", asChild: true, className: "w-full", children: (0, import_jsx_runtime.jsx)(Link, { to: "/login", replace: true, children: t2("invite.successAction") }) }),
    (0, import_jsx_runtime.jsx)(
      Link,
      {
        to: "/login",
        className: "txt-small text-ui-fg-base transition-fg hover:text-ui-fg-base-hover focus-visible:text-ui-fg-base-hover font-medium outline-none",
        children: t2("invite.backToLogin")
      },
      "login-link"
    )
  ] });
};
var InviteSchema = objectType({
  id: stringType(),
  jti: stringType(),
  exp: numberType(),
  iat: numberType()
});
var validateDecodedInvite = (decoded) => {
  return InviteSchema.safeParse(decoded).success;
};
export {
  Invite as Component
};
//# sourceMappingURL=invite-IQITJV62-3BXN5ZB7.js.map
