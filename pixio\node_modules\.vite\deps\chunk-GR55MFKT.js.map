{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-ZJ3OFMHB.mjs"], "sourcesContent": ["import {\n  productsQueryKeys\n} from \"./chunk-6I62UDJA.mjs\";\nimport {\n  queryClient\n} from \"./chunk-FXYH54JP.mjs\";\nimport {\n  queryKeysFactory\n} from \"./chunk-774WSTCC.mjs\";\nimport {\n  sdk\n} from \"./chunk-DEQUVHHE.mjs\";\n\n// src/hooks/api/categories.tsx\nimport {\n  useMutation,\n  useQuery\n} from \"@tanstack/react-query\";\nvar CATEGORIES_QUERY_KEY = \"categories\";\nvar categoriesQueryKeys = queryKeysFactory(CATEGORIES_QUERY_KEY);\nvar useProductCategory = (id, query, options) => {\n  const { data, ...rest } = useQuery({\n    queryKey: categoriesQueryKeys.detail(id, query),\n    queryFn: () => sdk.admin.productCategory.retrieve(id, query),\n    ...options\n  });\n  return { ...data, ...rest };\n};\nvar useProductCategories = (query, options) => {\n  const { data, ...rest } = useQuery({\n    queryKey: categoriesQueryKeys.list(query),\n    queryFn: () => sdk.admin.productCategory.list(query),\n    ...options\n  });\n  return { ...data, ...rest };\n};\nvar useCreateProductCategory = (options) => {\n  return useMutation({\n    mutationFn: (payload) => sdk.admin.productCategory.create(payload),\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries({ queryKey: categoriesQueryKeys.lists() });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\nvar useUpdateProductCategory = (id, options) => {\n  return useMutation({\n    mutationFn: (payload) => sdk.admin.productCategory.update(id, payload),\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries({ queryKey: categoriesQueryKeys.lists() });\n      queryClient.invalidateQueries({\n        queryKey: categoriesQueryKeys.detail(id)\n      });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\nvar useDeleteProductCategory = (id, options) => {\n  return useMutation({\n    mutationFn: () => sdk.admin.productCategory.delete(id),\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries({\n        queryKey: categoriesQueryKeys.detail(id)\n      });\n      queryClient.invalidateQueries({ queryKey: categoriesQueryKeys.lists() });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\nvar useUpdateProductCategoryProducts = (id, options) => {\n  return useMutation({\n    mutationFn: (payload) => sdk.admin.productCategory.updateProducts(id, payload),\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries({ queryKey: categoriesQueryKeys.lists() });\n      queryClient.invalidateQueries({\n        queryKey: categoriesQueryKeys.details()\n      });\n      queryClient.invalidateQueries({\n        queryKey: productsQueryKeys.lists()\n      });\n      queryClient.invalidateQueries({\n        queryKey: productsQueryKeys.details()\n      });\n      options?.onSuccess?.(data, variables, context);\n    },\n    ...options\n  });\n};\n\nexport {\n  categoriesQueryKeys,\n  useProductCategory,\n  useProductCategories,\n  useCreateProductCategory,\n  useUpdateProductCategory,\n  useDeleteProductCategory,\n  useUpdateProductCategoryProducts\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAkBA,IAAI,uBAAuB;AAC3B,IAAI,sBAAsB,iBAAiB,oBAAoB;AAC/D,IAAI,qBAAqB,CAAC,IAAI,OAAO,YAAY;AAC/C,QAAM,EAAE,MAAM,GAAG,KAAK,IAAI,SAAS;AAAA,IACjC,UAAU,oBAAoB,OAAO,IAAI,KAAK;AAAA,IAC9C,SAAS,MAAM,IAAI,MAAM,gBAAgB,SAAS,IAAI,KAAK;AAAA,IAC3D,GAAG;AAAA,EACL,CAAC;AACD,SAAO,EAAE,GAAG,MAAM,GAAG,KAAK;AAC5B;AACA,IAAI,uBAAuB,CAAC,OAAO,YAAY;AAC7C,QAAM,EAAE,MAAM,GAAG,KAAK,IAAI,SAAS;AAAA,IACjC,UAAU,oBAAoB,KAAK,KAAK;AAAA,IACxC,SAAS,MAAM,IAAI,MAAM,gBAAgB,KAAK,KAAK;AAAA,IACnD,GAAG;AAAA,EACL,CAAC;AACD,SAAO,EAAE,GAAG,MAAM,GAAG,KAAK;AAC5B;AACA,IAAI,2BAA2B,CAAC,YAAY;AAC1C,SAAO,YAAY;AAAA,IACjB,YAAY,CAAC,YAAY,IAAI,MAAM,gBAAgB,OAAO,OAAO;AAAA,IACjE,WAAW,CAAC,MAAM,WAAW,YAAY;AAvC7C;AAwCM,kBAAY,kBAAkB,EAAE,UAAU,oBAAoB,MAAM,EAAE,CAAC;AACvE,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;AACA,IAAI,2BAA2B,CAAC,IAAI,YAAY;AAC9C,SAAO,YAAY;AAAA,IACjB,YAAY,CAAC,YAAY,IAAI,MAAM,gBAAgB,OAAO,IAAI,OAAO;AAAA,IACrE,WAAW,CAAC,MAAM,WAAW,YAAY;AAjD7C;AAkDM,kBAAY,kBAAkB,EAAE,UAAU,oBAAoB,MAAM,EAAE,CAAC;AACvE,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,oBAAoB,OAAO,EAAE;AAAA,MACzC,CAAC;AACD,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;AACA,IAAI,2BAA2B,CAAC,IAAI,YAAY;AAC9C,SAAO,YAAY;AAAA,IACjB,YAAY,MAAM,IAAI,MAAM,gBAAgB,OAAO,EAAE;AAAA,IACrD,WAAW,CAAC,MAAM,WAAW,YAAY;AA9D7C;AA+DM,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,oBAAoB,OAAO,EAAE;AAAA,MACzC,CAAC;AACD,kBAAY,kBAAkB,EAAE,UAAU,oBAAoB,MAAM,EAAE,CAAC;AACvE,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;AACA,IAAI,mCAAmC,CAAC,IAAI,YAAY;AACtD,SAAO,YAAY;AAAA,IACjB,YAAY,CAAC,YAAY,IAAI,MAAM,gBAAgB,eAAe,IAAI,OAAO;AAAA,IAC7E,WAAW,CAAC,MAAM,WAAW,YAAY;AA3E7C;AA4EM,kBAAY,kBAAkB,EAAE,UAAU,oBAAoB,MAAM,EAAE,CAAC;AACvE,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,oBAAoB,QAAQ;AAAA,MACxC,CAAC;AACD,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,kBAAkB,MAAM;AAAA,MACpC,CAAC;AACD,kBAAY,kBAAkB;AAAA,QAC5B,UAAU,kBAAkB,QAAQ;AAAA,MACtC,CAAC;AACD,+CAAS,cAAT,iCAAqB,MAAM,WAAW;AAAA,IACxC;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;", "names": []}