{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-YEDAFXMB.mjs"], "sourcesContent": ["import {\n  useSelectedParams\n} from \"./chunk-M3VFKDXJ.mjs\";\n\n// src/components/table/data-table/data-table-search/data-table-search.tsx\nimport { Input } from \"@medusajs/ui\";\nimport { useCallback, useEffect } from \"react\";\nimport { useTranslation } from \"react-i18next\";\nimport { debounce } from \"lodash\";\nimport { jsx } from \"react/jsx-runtime\";\nvar DataTableSearch = ({\n  placeholder,\n  prefix,\n  autofocus\n}) => {\n  const { t } = useTranslation();\n  const placeholderText = placeholder || t(\"general.search\");\n  const selectedParams = useSelectedParams({\n    param: \"q\",\n    prefix,\n    multiple: false\n  });\n  const query = selectedParams.get();\n  const debouncedOnChange = useCallback(\n    debounce((e) => {\n      const value = e.target.value;\n      if (!value) {\n        selectedParams.delete();\n      } else {\n        selectedParams.add(value);\n      }\n    }, 500),\n    [selectedParams]\n  );\n  useEffect(() => {\n    return () => {\n      debouncedOnChange.cancel();\n    };\n  }, [debouncedOnChange]);\n  return /* @__PURE__ */ jsx(\n    Input,\n    {\n      autoComplete: \"off\",\n      name: \"q\",\n      type: \"search\",\n      size: \"small\",\n      autoFocus: autofocus,\n      defaultValue: query?.[0] || void 0,\n      onChange: debouncedOnChange,\n      placeholder: placeholderText\n    }\n  );\n};\n\nexport {\n  DataTableSearch\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAMA,mBAAuC;AAEvC,oBAAyB;AACzB,yBAAoB;AACpB,IAAI,kBAAkB,CAAC;AAAA,EACrB;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACJ,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,QAAM,kBAAkB,eAAe,EAAE,gBAAgB;AACzD,QAAM,iBAAiB,kBAAkB;AAAA,IACvC,OAAO;AAAA,IACP;AAAA,IACA,UAAU;AAAA,EACZ,CAAC;AACD,QAAM,QAAQ,eAAe,IAAI;AACjC,QAAM,wBAAoB;AAAA,QACxB,wBAAS,CAAC,MAAM;AACd,YAAM,QAAQ,EAAE,OAAO;AACvB,UAAI,CAAC,OAAO;AACV,uBAAe,OAAO;AAAA,MACxB,OAAO;AACL,uBAAe,IAAI,KAAK;AAAA,MAC1B;AAAA,IACF,GAAG,GAAG;AAAA,IACN,CAAC,cAAc;AAAA,EACjB;AACA,8BAAU,MAAM;AACd,WAAO,MAAM;AACX,wBAAkB,OAAO;AAAA,IAC3B;AAAA,EACF,GAAG,CAAC,iBAAiB,CAAC;AACtB,aAAuB;AAAA,IACrB;AAAA,IACA;AAAA,MACE,cAAc;AAAA,MACd,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,WAAW;AAAA,MACX,eAAc,+BAAQ,OAAM;AAAA,MAC5B,UAAU;AAAA,MACV,aAAa;AAAA,IACf;AAAA,EACF;AACF;", "names": []}