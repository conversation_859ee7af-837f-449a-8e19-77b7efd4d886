{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-LPEUYMRK.mjs"], "sourcesContent": ["// src/components/common/skeleton/skeleton.tsx\nimport { Container, clx } from \"@medusajs/ui\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar Skeleton = ({ className, style }) => {\n  return /* @__PURE__ */ jsx(\n    \"div\",\n    {\n      \"aria-hidden\": true,\n      className: clx(\n        \"bg-ui-bg-component h-3 w-3 animate-pulse rounded-[4px]\",\n        className\n      ),\n      style\n    }\n  );\n};\nvar TextSkeleton = ({\n  size = \"small\",\n  leading = \"compact\",\n  characters = 10\n}) => {\n  let charWidth = 9;\n  switch (size) {\n    case \"xlarge\":\n      charWidth = 13;\n      break;\n    case \"large\":\n      charWidth = 11;\n      break;\n    case \"base\":\n      charWidth = 10;\n      break;\n    case \"small\":\n      charWidth = 9;\n      break;\n    case \"xsmall\":\n      charWidth = 8;\n      break;\n  }\n  return /* @__PURE__ */ jsx(\n    Skeleton,\n    {\n      className: clx({\n        \"h-5\": size === \"xsmall\",\n        \"h-6\": size === \"small\",\n        \"h-7\": size === \"base\",\n        \"h-8\": size === \"xlarge\",\n        \"!h-5\": leading === \"compact\"\n      }),\n      style: {\n        width: `${charWidth * characters}px`\n      }\n    }\n  );\n};\nvar TableFooterSkeleton = ({ layout }) => {\n  return /* @__PURE__ */ jsxs(\n    \"div\",\n    {\n      className: clx(\"flex items-center justify-between p-4\", {\n        \"border-t\": layout === \"fill\"\n      }),\n      children: [\n        /* @__PURE__ */ jsx(Skeleton, { className: \"h-7 w-[138px]\" }),\n        /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center gap-x-2\", children: [\n          /* @__PURE__ */ jsx(Skeleton, { className: \"h-7 w-24\" }),\n          /* @__PURE__ */ jsx(Skeleton, { className: \"h-7 w-11\" }),\n          /* @__PURE__ */ jsx(Skeleton, { className: \"h-7 w-11\" })\n        ] })\n      ]\n    }\n  );\n};\nvar TableSkeleton = ({\n  rowCount = 10,\n  search = true,\n  filters = true,\n  orderBy = true,\n  pagination = true,\n  layout = \"fit\"\n}) => {\n  const totalRowCount = rowCount + 1;\n  const rows = Array.from({ length: totalRowCount }, (_, i) => i);\n  const hasToolbar = search || filters || orderBy;\n  return /* @__PURE__ */ jsxs(\n    \"div\",\n    {\n      \"aria-hidden\": true,\n      className: clx({\n        \"flex h-full flex-col overflow-hidden\": layout === \"fill\"\n      }),\n      children: [\n        hasToolbar && /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center justify-between px-6 py-4\", children: [\n          filters && /* @__PURE__ */ jsx(Skeleton, { className: \"h-7 w-full max-w-[135px]\" }),\n          (search || orderBy) && /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center gap-x-2\", children: [\n            search && /* @__PURE__ */ jsx(Skeleton, { className: \"h-7 w-[160px]\" }),\n            orderBy && /* @__PURE__ */ jsx(Skeleton, { className: \"h-7 w-7\" })\n          ] })\n        ] }),\n        /* @__PURE__ */ jsx(\"div\", { className: \"flex flex-col divide-y border-y\", children: rows.map((row) => /* @__PURE__ */ jsx(Skeleton, { className: \"h-10 w-full rounded-none\" }, row)) }),\n        pagination && /* @__PURE__ */ jsx(TableFooterSkeleton, { layout })\n      ]\n    }\n  );\n};\nvar SingleColumnPageSkeleton = ({\n  sections = 2,\n  showJSON = false,\n  showMetadata = false\n}) => {\n  return /* @__PURE__ */ jsxs(\"div\", { className: \"flex flex-col gap-y-3\", children: [\n    Array.from({ length: sections }, (_, i) => i).map((section) => {\n      return /* @__PURE__ */ jsx(\n        Skeleton,\n        {\n          className: clx(\"h-full max-h-[460px] w-full rounded-lg\", {\n            // First section is smaller on most pages, this gives us less\n            // layout shifting in general,\n            \"max-h-[219px]\": section === 0\n          })\n        },\n        section\n      );\n    }),\n    showMetadata && /* @__PURE__ */ jsx(Skeleton, { className: \"h-[60px] w-full rounded-lg\" }),\n    showJSON && /* @__PURE__ */ jsx(Skeleton, { className: \"h-[60px] w-full rounded-lg\" })\n  ] });\n};\nvar TwoColumnPageSkeleton = ({\n  mainSections = 2,\n  sidebarSections = 1,\n  showJSON = false,\n  showMetadata = true\n}) => {\n  const showExtraData = showJSON || showMetadata;\n  return /* @__PURE__ */ jsx(\"div\", { className: \"flex flex-col gap-y-3\", children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex flex-col gap-x-4 gap-y-3 xl:flex-row xl:items-start\", children: [\n    /* @__PURE__ */ jsxs(\"div\", { className: \"flex w-full flex-col gap-y-3\", children: [\n      Array.from({ length: mainSections }, (_, i) => i).map((section) => {\n        return /* @__PURE__ */ jsx(\n          Skeleton,\n          {\n            className: clx(\"h-full max-h-[460px] w-full rounded-lg\", {\n              \"max-h-[219px]\": section === 0\n            })\n          },\n          section\n        );\n      }),\n      showExtraData && /* @__PURE__ */ jsxs(\"div\", { className: \"hidden flex-col gap-y-3 xl:flex\", children: [\n        showMetadata && /* @__PURE__ */ jsx(Skeleton, { className: \"h-[60px] w-full rounded-lg\" }),\n        showJSON && /* @__PURE__ */ jsx(Skeleton, { className: \"h-[60px] w-full rounded-lg\" })\n      ] })\n    ] }),\n    /* @__PURE__ */ jsxs(\"div\", { className: \"flex w-full max-w-[100%] flex-col gap-y-3 xl:mt-0 xl:max-w-[440px]\", children: [\n      Array.from({ length: sidebarSections }, (_, i) => i).map(\n        (section) => {\n          return /* @__PURE__ */ jsx(\n            Skeleton,\n            {\n              className: clx(\"h-full max-h-[320px] w-full rounded-lg\", {\n                \"max-h-[140px]\": section === 0\n              })\n            },\n            section\n          );\n        }\n      ),\n      showExtraData && /* @__PURE__ */ jsxs(\"div\", { className: \"flex flex-col gap-y-3 xl:hidden\", children: [\n        showMetadata && /* @__PURE__ */ jsx(Skeleton, { className: \"h-[60px] w-full rounded-lg\" }),\n        showJSON && /* @__PURE__ */ jsx(Skeleton, { className: \"h-[60px] w-full rounded-lg\" })\n      ] })\n    ] })\n  ] }) });\n};\n\nexport {\n  Skeleton,\n  TextSkeleton,\n  TableFooterSkeleton,\n  TableSkeleton,\n  SingleColumnPageSkeleton,\n  TwoColumnPageSkeleton\n};\n"], "mappings": ";;;;;;;;;;;AAEA,yBAA0B;AAC1B,IAAI,WAAW,CAAC,EAAE,WAAW,MAAM,MAAM;AACvC,aAAuB;AAAA,IACrB;AAAA,IACA;AAAA,MACE,eAAe;AAAA,MACf,WAAW;AAAA,QACT;AAAA,QACA;AAAA,MACF;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAI,eAAe,CAAC;AAAA,EAClB,OAAO;AAAA,EACP,UAAU;AAAA,EACV,aAAa;AACf,MAAM;AACJ,MAAI,YAAY;AAChB,UAAQ,MAAM;AAAA,IACZ,KAAK;AACH,kBAAY;AACZ;AAAA,IACF,KAAK;AACH,kBAAY;AACZ;AAAA,IACF,KAAK;AACH,kBAAY;AACZ;AAAA,IACF,KAAK;AACH,kBAAY;AACZ;AAAA,IACF,KAAK;AACH,kBAAY;AACZ;AAAA,EACJ;AACA,aAAuB;AAAA,IACrB;AAAA,IACA;AAAA,MACE,WAAW,IAAI;AAAA,QACb,OAAO,SAAS;AAAA,QAChB,OAAO,SAAS;AAAA,QAChB,OAAO,SAAS;AAAA,QAChB,OAAO,SAAS;AAAA,QAChB,QAAQ,YAAY;AAAA,MACtB,CAAC;AAAA,MACD,OAAO;AAAA,QACL,OAAO,GAAG,YAAY,UAAU;AAAA,MAClC;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAI,sBAAsB,CAAC,EAAE,OAAO,MAAM;AACxC,aAAuB;AAAA,IACrB;AAAA,IACA;AAAA,MACE,WAAW,IAAI,yCAAyC;AAAA,QACtD,YAAY,WAAW;AAAA,MACzB,CAAC;AAAA,MACD,UAAU;AAAA,YACQ,wBAAI,UAAU,EAAE,WAAW,gBAAgB,CAAC;AAAA,YAC5C,yBAAK,OAAO,EAAE,WAAW,6BAA6B,UAAU;AAAA,cAC9D,wBAAI,UAAU,EAAE,WAAW,WAAW,CAAC;AAAA,cACvC,wBAAI,UAAU,EAAE,WAAW,WAAW,CAAC;AAAA,cACvC,wBAAI,UAAU,EAAE,WAAW,WAAW,CAAC;AAAA,QACzD,EAAE,CAAC;AAAA,MACL;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAI,gBAAgB,CAAC;AAAA,EACnB,WAAW;AAAA,EACX,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,aAAa;AAAA,EACb,SAAS;AACX,MAAM;AACJ,QAAM,gBAAgB,WAAW;AACjC,QAAM,OAAO,MAAM,KAAK,EAAE,QAAQ,cAAc,GAAG,CAAC,GAAG,MAAM,CAAC;AAC9D,QAAM,aAAa,UAAU,WAAW;AACxC,aAAuB;AAAA,IACrB;AAAA,IACA;AAAA,MACE,eAAe;AAAA,MACf,WAAW,IAAI;AAAA,QACb,wCAAwC,WAAW;AAAA,MACrD,CAAC;AAAA,MACD,UAAU;AAAA,QACR,kBAA8B,yBAAK,OAAO,EAAE,WAAW,+CAA+C,UAAU;AAAA,UAC9G,eAA2B,wBAAI,UAAU,EAAE,WAAW,2BAA2B,CAAC;AAAA,WACjF,UAAU,gBAA4B,yBAAK,OAAO,EAAE,WAAW,6BAA6B,UAAU;AAAA,YACrG,cAA0B,wBAAI,UAAU,EAAE,WAAW,gBAAgB,CAAC;AAAA,YACtE,eAA2B,wBAAI,UAAU,EAAE,WAAW,UAAU,CAAC;AAAA,UACnE,EAAE,CAAC;AAAA,QACL,EAAE,CAAC;AAAA,YACa,wBAAI,OAAO,EAAE,WAAW,mCAAmC,UAAU,KAAK,IAAI,CAAC,YAAwB,wBAAI,UAAU,EAAE,WAAW,2BAA2B,GAAG,GAAG,CAAC,EAAE,CAAC;AAAA,QACvL,kBAA8B,wBAAI,qBAAqB,EAAE,OAAO,CAAC;AAAA,MACnE;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAI,2BAA2B,CAAC;AAAA,EAC9B,WAAW;AAAA,EACX,WAAW;AAAA,EACX,eAAe;AACjB,MAAM;AACJ,aAAuB,yBAAK,OAAO,EAAE,WAAW,yBAAyB,UAAU;AAAA,IACjF,MAAM,KAAK,EAAE,QAAQ,SAAS,GAAG,CAAC,GAAG,MAAM,CAAC,EAAE,IAAI,CAAC,YAAY;AAC7D,iBAAuB;AAAA,QACrB;AAAA,QACA;AAAA,UACE,WAAW,IAAI,0CAA0C;AAAA;AAAA;AAAA,YAGvD,iBAAiB,YAAY;AAAA,UAC/B,CAAC;AAAA,QACH;AAAA,QACA;AAAA,MACF;AAAA,IACF,CAAC;AAAA,IACD,oBAAgC,wBAAI,UAAU,EAAE,WAAW,6BAA6B,CAAC;AAAA,IACzF,gBAA4B,wBAAI,UAAU,EAAE,WAAW,6BAA6B,CAAC;AAAA,EACvF,EAAE,CAAC;AACL;AACA,IAAI,wBAAwB,CAAC;AAAA,EAC3B,eAAe;AAAA,EACf,kBAAkB;AAAA,EAClB,WAAW;AAAA,EACX,eAAe;AACjB,MAAM;AACJ,QAAM,gBAAgB,YAAY;AAClC,aAAuB,wBAAI,OAAO,EAAE,WAAW,yBAAyB,cAA0B,yBAAK,OAAO,EAAE,WAAW,4DAA4D,UAAU;AAAA,QAC/K,yBAAK,OAAO,EAAE,WAAW,gCAAgC,UAAU;AAAA,MACjF,MAAM,KAAK,EAAE,QAAQ,aAAa,GAAG,CAAC,GAAG,MAAM,CAAC,EAAE,IAAI,CAAC,YAAY;AACjE,mBAAuB;AAAA,UACrB;AAAA,UACA;AAAA,YACE,WAAW,IAAI,0CAA0C;AAAA,cACvD,iBAAiB,YAAY;AAAA,YAC/B,CAAC;AAAA,UACH;AAAA,UACA;AAAA,QACF;AAAA,MACF,CAAC;AAAA,MACD,qBAAiC,yBAAK,OAAO,EAAE,WAAW,mCAAmC,UAAU;AAAA,QACrG,oBAAgC,wBAAI,UAAU,EAAE,WAAW,6BAA6B,CAAC;AAAA,QACzF,gBAA4B,wBAAI,UAAU,EAAE,WAAW,6BAA6B,CAAC;AAAA,MACvF,EAAE,CAAC;AAAA,IACL,EAAE,CAAC;AAAA,QACa,yBAAK,OAAO,EAAE,WAAW,sEAAsE,UAAU;AAAA,MACvH,MAAM,KAAK,EAAE,QAAQ,gBAAgB,GAAG,CAAC,GAAG,MAAM,CAAC,EAAE;AAAA,QACnD,CAAC,YAAY;AACX,qBAAuB;AAAA,YACrB;AAAA,YACA;AAAA,cACE,WAAW,IAAI,0CAA0C;AAAA,gBACvD,iBAAiB,YAAY;AAAA,cAC/B,CAAC;AAAA,YACH;AAAA,YACA;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,MACA,qBAAiC,yBAAK,OAAO,EAAE,WAAW,mCAAmC,UAAU;AAAA,QACrG,oBAAgC,wBAAI,UAAU,EAAE,WAAW,6BAA6B,CAAC;AAAA,QACzF,gBAA4B,wBAAI,UAAU,EAAE,WAAW,6BAA6B,CAAC;AAAA,MACvF,EAAE,CAAC;AAAA,IACL,EAAE,CAAC;AAAA,EACL,EAAE,CAAC,EAAE,CAAC;AACR;", "names": []}