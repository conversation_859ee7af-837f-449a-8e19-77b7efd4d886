import {
  SingleColumnPage
} from "./chunk-3LNIL4XX.js";
import {
  useSalesChannelTableColumns,
  useSalesChannelTableEmptyState,
  useSalesChannelTableFilters,
  useSalesChannelTableQuery
} from "./chunk-MX262YWC.js";
import "./chunk-FSBMOACR.js";
import {
  DataTable
} from "./chunk-GNGA6ZKR.js";
import "./chunk-32T72GVU.js";
import "./chunk-2E2FUO6N.js";
import {
  useExtension
} from "./chunk-FTD3ZWHZ.js";
import "./chunk-CFRQOB2M.js";
import "./chunk-66DVUN72.js";
import "./chunk-BF7OBKIN.js";
import "./chunk-7UAYECTW.js";
import "./chunk-Y3NYV3NU.js";
import "./chunk-WISME5HP.js";
import "./chunk-WAUSOHFQ.js";
import "./chunk-3ORK3QZ4.js";
import "./chunk-3A5TVVNI.js";
import "./chunk-EZ62MM7J.js";
import "./chunk-3R6LYIXM.js";
import "./chunk-2P2O4TMQ.js";
import "./chunk-KI3HEITX.js";
import "./chunk-2VUCAO7L.js";
import "./chunk-PTIULTKD.js";
import "./chunk-KISXY2LD.js";
import "./chunk-L7C3XPHJ.js";
import "./chunk-T7MG2EIJ.js";
import "./chunk-MER4VYPR.js";
import "./chunk-2AWKOUCD.js";
import "./chunk-YL3TYBFR.js";
import "./chunk-TZWW72YW.js";
import "./chunk-MSQ25CWB.js";
import "./chunk-ONYSAQ5Z.js";
import "./chunk-NIH7SAXN.js";
import "./chunk-UEJFWAFE.js";
import "./chunk-GR55MFKT.js";
import {
  useStore
} from "./chunk-AAFHKNJG.js";
import "./chunk-7XDBFDTZ.js";
import "./chunk-3JKGO5XL.js";
import {
  useDeleteSalesChannelLazy,
  useSalesChannels
} from "./chunk-OISUTS7G.js";
import "./chunk-AJYMIHLQ.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import "./chunk-JFCIYOH4.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-5GF3RGIE.js";
import {
  useNavigate
} from "./chunk-T7YBVUWZ.js";
import {
  Container,
  PencilSquare,
  Trash,
  createDataTableColumnHelper,
  toast,
  usePrompt
} from "./chunk-YP2LLXWB.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-ZSQ362B4.js";
import {
  keepPreviousData
} from "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/sales-channel-list-GUWAETTZ.mjs
var import_react = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var PAGE_SIZE = 20;
var SalesChannelListTable = () => {
  const { t } = useTranslation();
  const { store } = useStore();
  const searchParams = useSalesChannelTableQuery({
    pageSize: PAGE_SIZE
  });
  const { sales_channels, count, isPending, isError, error } = useSalesChannels(
    searchParams,
    {
      placeholderData: keepPreviousData
    }
  );
  const columns = useColumns();
  const filters = useSalesChannelTableFilters();
  const emptyState = useSalesChannelTableEmptyState();
  const sales_channels_data = (sales_channels == null ? void 0 : sales_channels.map((sales_channel) => {
    return {
      ...sales_channel,
      is_default: (store == null ? void 0 : store.default_sales_channel_id) === sales_channel.id
    };
  })) ?? [];
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime.jsx)(Container, { className: "p-0", children: (0, import_jsx_runtime.jsx)(
    DataTable,
    {
      data: sales_channels_data,
      columns,
      rowCount: count,
      getRowId: (row) => row.id,
      pageSize: PAGE_SIZE,
      filters,
      isLoading: isPending,
      emptyState,
      heading: t("salesChannels.domain"),
      subHeading: t("salesChannels.subtitle"),
      action: {
        label: t("actions.create"),
        to: "/settings/sales-channels/create"
      },
      rowHref: (row) => `/settings/sales-channels/${row.id}`
    }
  ) });
};
var columnHelper = createDataTableColumnHelper();
var useColumns = () => {
  const { t } = useTranslation();
  const prompt = usePrompt();
  const navigate = useNavigate();
  const base = useSalesChannelTableColumns();
  const { mutateAsync } = useDeleteSalesChannelLazy();
  const handleDelete = (0, import_react.useCallback)(
    async (salesChannel) => {
      const confirm = await prompt({
        title: t("general.areYouSure"),
        description: t("salesChannels.deleteSalesChannelWarning", {
          name: salesChannel.name
        }),
        verificationInstruction: t("general.typeToConfirm"),
        verificationText: salesChannel.name,
        confirmText: t("actions.delete"),
        cancelText: t("actions.cancel")
      });
      if (!confirm) {
        return;
      }
      await mutateAsync(salesChannel.id, {
        onSuccess: () => {
          toast.success(t("salesChannels.toast.delete"));
        },
        onError: (e) => {
          toast.error(e.message);
        }
      });
    },
    [t, prompt, mutateAsync]
  );
  return (0, import_react.useMemo)(
    () => [
      ...base,
      columnHelper.action({
        actions: (ctx) => {
          const disabledTooltip = ctx.row.original.is_default ? t("salesChannels.tooltip.cannotDeleteDefault") : void 0;
          return [
            [
              {
                icon: (0, import_jsx_runtime.jsx)(PencilSquare, {}),
                label: t("actions.edit"),
                onClick: () => navigate(
                  `/settings/sales-channels/${ctx.row.original.id}/edit`
                )
              }
            ],
            [
              {
                icon: (0, import_jsx_runtime.jsx)(Trash, {}),
                label: t("actions.delete"),
                onClick: () => handleDelete(ctx.row.original),
                disabled: ctx.row.original.is_default,
                disabledTooltip
              }
            ]
          ];
        }
      })
    ],
    [base, handleDelete, navigate, t]
  );
};
var SalesChannelList = () => {
  const { getWidgets } = useExtension();
  return (0, import_jsx_runtime2.jsx)(
    SingleColumnPage,
    {
      widgets: {
        before: getWidgets("sales_channel.list.before"),
        after: getWidgets("sales_channel.list.after")
      },
      hasOutlet: true,
      children: (0, import_jsx_runtime2.jsx)(SalesChannelListTable, {})
    }
  );
};
export {
  SalesChannelList as Component
};
//# sourceMappingURL=sales-channel-list-GUWAETTZ-BRWGPSB7.js.map
