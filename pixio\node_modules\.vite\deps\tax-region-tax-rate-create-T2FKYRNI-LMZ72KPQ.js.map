{"version": 3, "sources": ["../../@medusajs/dashboard/dist/tax-region-tax-rate-create-T2FKYRNI.mjs"], "sourcesContent": ["import {\n  PercentageInput\n} from \"./chunk-YRY2CZ6I.mjs\";\nimport {\n  SwitchBox\n} from \"./chunk-D7H6ZNK4.mjs\";\nimport \"./chunk-IUCDCPJU.mjs\";\nimport {\n  KeyboundForm\n} from \"./chunk-6HTZNHPT.mjs\";\nimport {\n  RouteFocusModal,\n  useRouteModal\n} from \"./chunk-4TC5YS65.mjs\";\nimport {\n  Form\n} from \"./chunk-OBQI23QM.mjs\";\nimport {\n  useCreateTaxRate\n} from \"./chunk-X6DSNTTX.mjs\";\nimport {\n  useTaxRegion\n} from \"./chunk-I6E6CALJ.mjs\";\nimport \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-RPUOO7AV.mjs\";\n\n// src/routes/tax-regions/tax-region-tax-rate-create/tax-region-tax-rate-create.tsx\nimport { useParams } from \"react-router-dom\";\n\n// src/routes/tax-regions/tax-region-tax-rate-create/components/tax-region-tax-rate-create-form/tax-region-tax-rate-create-form.tsx\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { Button, Heading, Input, Text, toast } from \"@medusajs/ui\";\nimport { useForm } from \"react-hook-form\";\nimport { useTranslation } from \"react-i18next\";\nimport { z } from \"zod\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar TaxRegionTaxRateCreateSchema = z.object({\n  name: z.string().min(1),\n  code: z.string().min(1),\n  rate: z.object({\n    float: z.number().optional(),\n    value: z.string().optional()\n  }).optional(),\n  is_combinable: z.boolean().optional()\n});\nvar TaxRegionTaxRateCreateForm = ({\n  taxRegion,\n  isSublevel = false\n}) => {\n  const { t } = useTranslation();\n  const { handleSuccess } = useRouteModal();\n  const form = useForm({\n    defaultValues: {\n      name: \"\",\n      code: \"\",\n      rate: {\n        value: \"\"\n      },\n      is_combinable: false\n    },\n    resolver: zodResolver(TaxRegionTaxRateCreateSchema)\n  });\n  const { mutateAsync, isPending } = useCreateTaxRate();\n  const handleSubmit = form.handleSubmit(async (values) => {\n    await mutateAsync(\n      {\n        tax_region_id: taxRegion.id,\n        is_default: true,\n        name: values.name,\n        code: values.code,\n        rate: values.rate?.float,\n        is_combinable: values.is_combinable\n      },\n      {\n        onSuccess: () => {\n          toast.success(t(\"taxRegions.taxRates.create.successToast\"));\n          handleSuccess();\n        },\n        onError: (error) => {\n          toast.error(error.message);\n        }\n      }\n    );\n  });\n  return /* @__PURE__ */ jsx(RouteFocusModal.Form, { form, children: /* @__PURE__ */ jsxs(\n    KeyboundForm,\n    {\n      onSubmit: handleSubmit,\n      className: \"flex h-full flex-col overflow-hidden\",\n      children: [\n        /* @__PURE__ */ jsx(RouteFocusModal.Header, {}),\n        /* @__PURE__ */ jsx(RouteFocusModal.Body, { className: \"flex flex-1 flex-col overflow-hidden\", children: /* @__PURE__ */ jsx(\"div\", { className: \"flex flex-1 flex-col items-center overflow-y-auto\", children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex w-full max-w-[720px] flex-col gap-y-8 px-2 py-16\", children: [\n          /* @__PURE__ */ jsxs(\"div\", { children: [\n            /* @__PURE__ */ jsx(Heading, { children: t(`taxRegions.taxRates.create.header`) }),\n            /* @__PURE__ */ jsx(Text, { size: \"small\", className: \"text-ui-fg-subtle\", children: t(`taxRegions.taxRates.create.hint`) })\n          ] }),\n          /* @__PURE__ */ jsxs(\"div\", { className: \"grid grid-cols-1 gap-4 md:grid-cols-2\", children: [\n            /* @__PURE__ */ jsx(\n              Form.Field,\n              {\n                control: form.control,\n                name: \"name\",\n                render: ({ field }) => {\n                  return /* @__PURE__ */ jsxs(Form.Item, { children: [\n                    /* @__PURE__ */ jsx(Form.Label, { children: t(\"fields.name\") }),\n                    /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(Input, { ...field }) }),\n                    /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n                  ] });\n                }\n              }\n            ),\n            /* @__PURE__ */ jsx(\n              Form.Field,\n              {\n                control: form.control,\n                name: \"rate\",\n                render: ({ field: { value, onChange, ...field } }) => {\n                  return /* @__PURE__ */ jsxs(Form.Item, { children: [\n                    /* @__PURE__ */ jsx(Form.Label, { children: t(\"taxRegions.fields.taxRate\") }),\n                    /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(\n                      PercentageInput,\n                      {\n                        ...field,\n                        value: value?.value,\n                        onValueChange: (value2, _name, values) => onChange({\n                          value: value2,\n                          float: values?.float\n                        })\n                      }\n                    ) }),\n                    /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n                  ] });\n                }\n              }\n            ),\n            /* @__PURE__ */ jsx(\n              Form.Field,\n              {\n                control: form.control,\n                name: \"code\",\n                render: ({ field }) => {\n                  return /* @__PURE__ */ jsxs(Form.Item, { children: [\n                    /* @__PURE__ */ jsx(Form.Label, { children: t(\"taxRegions.fields.taxCode\") }),\n                    /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(Input, { ...field }) }),\n                    /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n                  ] });\n                }\n              }\n            )\n          ] }),\n          isSublevel && /* @__PURE__ */ jsx(\n            SwitchBox,\n            {\n              control: form.control,\n              name: \"is_combinable\",\n              label: t(\"taxRegions.fields.isCombinable.label\"),\n              description: t(\"taxRegions.fields.isCombinable.hint\")\n            }\n          )\n        ] }) }) }),\n        /* @__PURE__ */ jsx(RouteFocusModal.Footer, { children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center justify-end gap-x-2\", children: [\n          /* @__PURE__ */ jsx(RouteFocusModal.Close, { asChild: true, children: /* @__PURE__ */ jsx(Button, { size: \"small\", variant: \"secondary\", children: t(\"actions.cancel\") }) }),\n          /* @__PURE__ */ jsx(Button, { size: \"small\", type: \"submit\", isLoading: isPending, children: t(\"actions.save\") })\n        ] }) })\n      ]\n    }\n  ) });\n};\n\n// src/routes/tax-regions/tax-region-tax-rate-create/tax-region-tax-rate-create.tsx\nimport { jsx as jsx2 } from \"react/jsx-runtime\";\nvar TaxRegionTaxRateCreate = () => {\n  const { id, province_id } = useParams();\n  const { tax_region, isPending, isError, error } = useTaxRegion(\n    province_id || id\n  );\n  const ready = !isPending && !!tax_region;\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsx2(RouteFocusModal, { children: ready && /* @__PURE__ */ jsx2(\n    TaxRegionTaxRateCreateForm,\n    {\n      taxRegion: tax_region,\n      isSublevel: !!province_id\n    }\n  ) });\n};\nexport {\n  TaxRegionTaxRateCreate as Component\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqCA,yBAA0B;AAuI1B,IAAAA,sBAA4B;AAtI5B,IAAI,+BAA+B,EAAE,OAAO;AAAA,EAC1C,MAAM,EAAE,OAAO,EAAE,IAAI,CAAC;AAAA,EACtB,MAAM,EAAE,OAAO,EAAE,IAAI,CAAC;AAAA,EACtB,MAAM,EAAE,OAAO;AAAA,IACb,OAAO,EAAE,OAAO,EAAE,SAAS;AAAA,IAC3B,OAAO,EAAE,OAAO,EAAE,SAAS;AAAA,EAC7B,CAAC,EAAE,SAAS;AAAA,EACZ,eAAe,EAAE,QAAQ,EAAE,SAAS;AACtC,CAAC;AACD,IAAI,6BAA6B,CAAC;AAAA,EAChC;AAAA,EACA,aAAa;AACf,MAAM;AACJ,QAAM,EAAE,GAAAC,GAAE,IAAI,eAAe;AAC7B,QAAM,EAAE,cAAc,IAAI,cAAc;AACxC,QAAM,OAAO,QAAQ;AAAA,IACnB,eAAe;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,MACA,eAAe;AAAA,IACjB;AAAA,IACA,UAAU,EAAY,4BAA4B;AAAA,EACpD,CAAC;AACD,QAAM,EAAE,aAAa,UAAU,IAAI,iBAAiB;AACpD,QAAM,eAAe,KAAK,aAAa,OAAO,WAAW;AAjE3D;AAkEI,UAAM;AAAA,MACJ;AAAA,QACE,eAAe,UAAU;AAAA,QACzB,YAAY;AAAA,QACZ,MAAM,OAAO;AAAA,QACb,MAAM,OAAO;AAAA,QACb,OAAM,YAAO,SAAP,mBAAa;AAAA,QACnB,eAAe,OAAO;AAAA,MACxB;AAAA,MACA;AAAA,QACE,WAAW,MAAM;AACf,gBAAM,QAAQA,GAAE,yCAAyC,CAAC;AAC1D,wBAAc;AAAA,QAChB;AAAA,QACA,SAAS,CAAC,UAAU;AAClB,gBAAM,MAAM,MAAM,OAAO;AAAA,QAC3B;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AACD,aAAuB,wBAAI,gBAAgB,MAAM,EAAE,MAAM,cAA0B;AAAA,IACjF;AAAA,IACA;AAAA,MACE,UAAU;AAAA,MACV,WAAW;AAAA,MACX,UAAU;AAAA,YACQ,wBAAI,gBAAgB,QAAQ,CAAC,CAAC;AAAA,YAC9B,wBAAI,gBAAgB,MAAM,EAAE,WAAW,wCAAwC,cAA0B,wBAAI,OAAO,EAAE,WAAW,qDAAqD,cAA0B,yBAAK,OAAO,EAAE,WAAW,yDAAyD,UAAU;AAAA,cAC1S,yBAAK,OAAO,EAAE,UAAU;AAAA,gBACtB,wBAAI,SAAS,EAAE,UAAUA,GAAE,mCAAmC,EAAE,CAAC;AAAA,gBACjE,wBAAI,MAAM,EAAE,MAAM,SAAS,WAAW,qBAAqB,UAAUA,GAAE,iCAAiC,EAAE,CAAC;AAAA,UAC7H,EAAE,CAAC;AAAA,cACa,yBAAK,OAAO,EAAE,WAAW,yCAAyC,UAAU;AAAA,gBAC1E;AAAA,cACd,KAAK;AAAA,cACL;AAAA,gBACE,SAAS,KAAK;AAAA,gBACd,MAAM;AAAA,gBACN,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,6BAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,wBACjC,wBAAI,KAAK,OAAO,EAAE,UAAUA,GAAE,aAAa,EAAE,CAAC;AAAA,wBAC9C,wBAAI,KAAK,SAAS,EAAE,cAA0B,wBAAI,OAAO,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC;AAAA,wBACxE,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,kBAC3C,EAAE,CAAC;AAAA,gBACL;AAAA,cACF;AAAA,YACF;AAAA,gBACgB;AAAA,cACd,KAAK;AAAA,cACL;AAAA,gBACE,SAAS,KAAK;AAAA,gBACd,MAAM;AAAA,gBACN,QAAQ,CAAC,EAAE,OAAO,EAAE,OAAO,UAAU,GAAG,MAAM,EAAE,MAAM;AACpD,6BAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,wBACjC,wBAAI,KAAK,OAAO,EAAE,UAAUA,GAAE,2BAA2B,EAAE,CAAC;AAAA,wBAC5D,wBAAI,KAAK,SAAS,EAAE,cAA0B;AAAA,sBAC5D;AAAA,sBACA;AAAA,wBACE,GAAG;AAAA,wBACH,OAAO,+BAAO;AAAA,wBACd,eAAe,CAAC,QAAQ,OAAO,WAAW,SAAS;AAAA,0BACjD,OAAO;AAAA,0BACP,OAAO,iCAAQ;AAAA,wBACjB,CAAC;AAAA,sBACH;AAAA,oBACF,EAAE,CAAC;AAAA,wBACa,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,kBAC3C,EAAE,CAAC;AAAA,gBACL;AAAA,cACF;AAAA,YACF;AAAA,gBACgB;AAAA,cACd,KAAK;AAAA,cACL;AAAA,gBACE,SAAS,KAAK;AAAA,gBACd,MAAM;AAAA,gBACN,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,6BAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,wBACjC,wBAAI,KAAK,OAAO,EAAE,UAAUA,GAAE,2BAA2B,EAAE,CAAC;AAAA,wBAC5D,wBAAI,KAAK,SAAS,EAAE,cAA0B,wBAAI,OAAO,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC;AAAA,wBACxE,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,kBAC3C,EAAE,CAAC;AAAA,gBACL;AAAA,cACF;AAAA,YACF;AAAA,UACF,EAAE,CAAC;AAAA,UACH,kBAA8B;AAAA,YAC5B;AAAA,YACA;AAAA,cACE,SAAS,KAAK;AAAA,cACd,MAAM;AAAA,cACN,OAAOA,GAAE,sCAAsC;AAAA,cAC/C,aAAaA,GAAE,qCAAqC;AAAA,YACtD;AAAA,UACF;AAAA,QACF,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;AAAA,YACO,wBAAI,gBAAgB,QAAQ,EAAE,cAA0B,yBAAK,OAAO,EAAE,WAAW,yCAAyC,UAAU;AAAA,cAClI,wBAAI,gBAAgB,OAAO,EAAE,SAAS,MAAM,cAA0B,wBAAI,QAAQ,EAAE,MAAM,SAAS,SAAS,aAAa,UAAUA,GAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;AAAA,cAC3J,wBAAI,QAAQ,EAAE,MAAM,SAAS,MAAM,UAAU,WAAW,WAAW,UAAUA,GAAE,cAAc,EAAE,CAAC;AAAA,QAClH,EAAE,CAAC,EAAE,CAAC;AAAA,MACR;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;AAIA,IAAI,yBAAyB,MAAM;AACjC,QAAM,EAAE,IAAI,YAAY,IAAI,UAAU;AACtC,QAAM,EAAE,YAAY,WAAW,SAAS,MAAM,IAAI;AAAA,IAChD,eAAe;AAAA,EACjB;AACA,QAAM,QAAQ,CAAC,aAAa,CAAC,CAAC;AAC9B,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,oBAAAC,KAAK,iBAAiB,EAAE,UAAU,aAAyB,oBAAAA;AAAA,IAChF;AAAA,IACA;AAAA,MACE,WAAW;AAAA,MACX,YAAY,CAAC,CAAC;AAAA,IAChB;AAAA,EACF,EAAE,CAAC;AACL;", "names": ["import_jsx_runtime", "t", "jsx2"]}