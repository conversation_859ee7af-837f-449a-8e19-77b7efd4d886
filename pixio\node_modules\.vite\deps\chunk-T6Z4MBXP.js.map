{"version": 3, "sources": ["../../react-jwt/src/helpers/base64.ts", "../../react-jwt/src/jwt/index.ts", "../../react-jwt/src/hooks/index.tsx"], "sourcesContent": ["const map = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/\";\nconst reverseMap = new Map();\n\nfor (let i = 0; i < map.length; i++) {\n  let bits: string = i.toString(2);\n  const padding: number = 6 - bits.length;\n  bits = \"0\".repeat(padding) + bits;\n\n  reverseMap.set(map.charCodeAt(i), bits);\n}\n\n/**\n * Convert base64 string to an array of bytes\n * @param base64Str - Base64 string\n * @returns Array of 1-byte elements\n */\nfunction toByteArray(base64Str: string): string[] {\n  let bits: string = \"\";\n\n  // convert base64 string to bits\n  for (let i = 0; i < base64Str.length; i++) {\n    bits += reverseMap.get(base64Str.charCodeAt(i));\n  }\n\n  // Remove padding (\"=\" characters)\n  bits = bits.slice(0, bits.length - (bits.length % 8));\n\n  const bytesArray = [];\n\n  // Separate string by 8-bit groups\n  for (let i = 0; i < bits.length / 8; i++) {\n    bytesArray.push(bits.slice(i * 8, i * 8 + 8));\n  }\n\n  return bytesArray;\n}\n\n/**\n * Convert a base64 string to an UTF-8 array\n * @param base64Str - Base64 string\n * @returns UTF-8 array\n */\nexport function base64DecToArray(base64Str: string): number[] {\n  // Replace - _ and remove padding\n  base64Str = base64Str.replaceAll(\"=\", \"\");\n  base64Str = base64Str.replaceAll(\"-\", \"+\");\n  base64Str = base64Str.replaceAll(\"_\", \"/\");\n\n  const charCodes: string[] = toByteArray(base64Str);\n\n  return charCodes.map((code) => parseInt(code, 2));\n}\n\n/**\n * Convert a UTF-8 array to string\n * @param bytes\n * @returns Decoded string\n */\nexport function UTF8ArrToStr(bytes: number[]): string {\n  let decoded: string = \"\"; // Decoded string\n  let nPart: number;\n  const arrayLength: number = bytes.length;\n\n  for (let i = 0; i < arrayLength; i++) {\n    nPart = bytes[i];\n    decoded += String.fromCodePoint(\n      nPart > 251 && nPart < 254 && i + 5 < arrayLength /* six bytes */\n        ? /* (nPart - 252 << 30) may be not so safe in ECMAScript! So... */\n          (nPart - 252) * 1073741824 +\n            ((bytes[++i] - 128) << 24) +\n            ((bytes[++i] - 128) << 18) +\n            ((bytes[++i] - 128) << 12) +\n            ((bytes[++i] - 128) << 6) +\n            bytes[++i] -\n            128\n        : nPart > 247 && nPart < 252 && i + 4 < arrayLength /* five bytes */\n        ? ((nPart - 248) << 24) +\n          ((bytes[++i] - 128) << 18) +\n          ((bytes[++i] - 128) << 12) +\n          ((bytes[++i] - 128) << 6) +\n          bytes[++i] -\n          128\n        : nPart > 239 && nPart < 248 && i + 3 < arrayLength /* four bytes */\n        ? ((nPart - 240) << 18) +\n          ((bytes[++i] - 128) << 12) +\n          ((bytes[++i] - 128) << 6) +\n          bytes[++i] -\n          128\n        : nPart > 223 && nPart < 240 && i + 2 < arrayLength /* three bytes */\n        ? ((nPart - 224) << 12) + ((bytes[++i] - 128) << 6) + bytes[++i] - 128\n        : nPart > 191 && nPart < 224 && i + 1 < arrayLength /* two bytes */\n        ? ((nPart - 192) << 6) + bytes[++i] - 128 /* nPart < 127 ? */\n        : /* one byte */\n          nPart\n    );\n  }\n\n  return decoded;\n}\n", "import { base64DecToArray, UTF8ArrToStr } from \"../helpers/base64\";\n\n/**\n * Try to decode a JWT. If the token is valid you'll get an object otherwise you'll get null\n * @param token - The JWT that you want to decode\n * @returns Decoded token\n */\nexport function decodeToken<T>(token: string): T | null;\nexport function decodeToken(token: string): Object | null;\nexport function decodeToken<T = Object>(token: string): T | null {\n  try {\n    // if the token has more or less than 3 parts or is not a string\n    // then is not a valid token\n    if (typeof token !== \"string\" || token.split(\".\").length !== 3) {\n      return null;\n    }\n\n    // payload ( index 1 ) has the data stored and\n    // data about the expiration time\n    const payload: string = token.split(\".\")[1];\n\n    const base64Bytes: number[] = base64DecToArray(payload);\n    // Convert utf-8 array to string\n    const jsonPayload: string = UTF8ArrToStr(base64Bytes);\n    // Parse JSON\n    return JSON.parse(jsonPayload);\n  } catch (error) {\n    console.error(\"There was an error decoding token: \", error);\n    // Return null if something goes wrong\n    return null;\n  }\n}\n\n/**\n * Verify if the token is expired or not\n * @param token - Your JWT\n * @returns boolean\n */\nexport function isTokenExpired(token: string): boolean {\n  const decodedToken: any = decodeToken(token);\n  let result: boolean = true;\n\n  if (decodedToken && decodedToken.exp) {\n    const expirationDate: Date = new Date(0);\n    expirationDate.setUTCSeconds(decodedToken.exp); // sets the expiration seconds\n    // compare the expiration time and the current time\n    result = expirationDate.valueOf() < new Date().valueOf();\n  }\n\n  return result;\n}\n", "import { useState, useEffect } from \"react\";\nimport { decodeToken, isTokenExpired } from \"../jwt\";\n\n/**\n * This function will help you to decode a JWT and know if it's expired or not\n * @param userJwt - Your JWT\n * @returns An object containing the properties isExpired, decodedToken and reEvaluateToken\n */\nexport function useJwt<T>(userJwt: string): IUseJwt<T>;\nexport function useJwt(userJwt: string): IUseJwt;\nexport function useJwt<T>(userJwt: string): IUseJwt<T> {\n  const [isExpired, setIsExpired] = useState<boolean>(false);\n  const [decodedToken, setDecodedToken] = useState<T | null>(null);\n\n  useEffect(() => {\n    evaluateToken(userJwt);\n  }, [userJwt]);\n\n  const evaluateToken = (token: string) => {\n    setDecodedToken(decodeToken<T>(token));\n    setIsExpired(isTokenExpired(token));\n  };\n\n  return { isExpired, decodedToken, reEvaluateToken: evaluateToken };\n}\n\ninterface IUseJwt<T = Object> {\n  isExpired: boolean;\n  decodedToken: T | null;\n  reEvaluateToken: (token: string) => void;\n}\n"], "mappings": ";;;;;;;;;AAGA,KAHA,IAAY,oEAAA,IACO,oBAAA,OAAA,IAEN,GAAGA,IAAIC,EAAIC,QAAQF,KAAK;AACnC,MAAmBA,EAAEG,SAAS,CAAA;AAE9BC,MAAO,IAAIC,OADa,IAAID,EAAKF,MAAAA,IACJE,GAE7BE,EAAWC,IAAIN,EAAIO,WAAWR,CAAAA,GAAII,CAAAA;AAAAA;AAJlC;AAJF;AAAY;AACO;AAOiBA,SAAAA,ECCIK,IAAAA;AACtC,MAAA;AAGE,QAAqB,YAAA,OAAAC,MAAwC,MAA5BD,GAAME,MAAM,GAAA,EAAKT,OAChD,QAAA;AAKF,QAAAU,KAAA,SDuCyBC,IAAAA;AAK3B,eAJAD,IAAA,IAAsB,IAAA,IAEMC,GAAMX,QAAAA,IAErB,GAAGF,IAAIc,GAAad,IAC/Be,CAAAA,KAAQF,GAAMb,CAAAA,GACdgB,KAAWC,OAAOC,cAChBH,KAAQ,OAAOA,KAAQ,OAAOf,IAAI,IAAIc,IAElB,cAAfC,KAAQ,QACLF,GAAAA,EAAQb,CAAAA,IAAK,OAAQ,OACrBa,GAAAA,EAAQb,CAAAA,IAAK,OAAQ,OACrBa,GAAAA,EAAQb,CAAAA,IAAK,OAAQ,OACrBa,GAAAA,EAAQb,CAAAA,IAAK,OAAQ,KACvBa,GAAAA,EAAQb,CAAAA,IACR,MACFe,KAAQ,OAAOA,KAAQ,OAAOf,IAAI,IAAIc,KACpCC,KAAQ,OAAQ,OAChBF,GAAAA,EAAQb,CAAAA,IAAK,OAAQ,OACrBa,GAAAA,EAAQb,CAAAA,IAAK,OAAQ,OACrBa,GAAAA,EAAQb,CAAAA,IAAK,OAAQ,KACvBa,GAAAA,EAAQb,CAAAA,IACR,MACAe,KAAQ,OAAOA,KAAQ,OAAOf,IAAI,IAAIc,KACpCC,KAAQ,OAAQ,OAChBF,GAAAA,EAAQb,CAAAA,IAAK,OAAQ,OACrBa,GAAAA,EAAQb,CAAAA,IAAK,OAAQ,KACvBa,GAAAA,EAAQb,CAAAA,IACR,MACAe,KAAQ,OAAOA,KAAQ,OAAOf,IAAI,IAAIc,KACpCC,KAAQ,OAAQ,OAAQF,GAAAA,EAAQb,CAAAA,IAAK,OAAQ,KAAKa,GAAAA,EAAQb,CAAAA,IAAK,MACjEe,KAAQ,OAAOA,KAAQ,OAAOf,IAAI,IAAIc,KACpCC,KAAQ,OAAQ,KAAKF,GAAAA,EAAQb,CAAAA,IAAK,MAEpCe,EAAAA;AAIR,aAAA;IAAA,EAjFF,SAAqBI,IAAAA;AAInB,eAHAP,KAAmB,IAAA,IAGN,GAAGZ,IAAImB,GAAUjB,QAAQF,IACpCI,CAAAA,MAAQE,EAAWc,IAAID,GAAUX,WAAWR,CAAAA,CAAAA;AAI9CI,MAAAA,KAAOA,GAAKiB,MAAM,GAAGjB,GAAKF,SAAUE,GAAKF,SAAS,CAAA;AAKlD,eAHA,IAAmB,CAAA,GAAA,IAGN,GAAGF,IAAII,GAAKF,SAAS,GAAGF,IACnCsB,GAAWC,KAAKnB,GAAKiB,MAAU,IAAJrB,GAAW,IAAJA,IAAQ,CAAA,CAAA;AAG5C,aAAA;IAAA,ECf0BS,GAAME,MAAM,GAAA,EAAK,CAAA,EDyBrBa,WAAW,KAAK,EAAA,EAChBA,WAAW,KAAK,GAAA,EAChBA,WAAW,KAAK,GAAA,CAAA,EAIrBvB,IAAI,SAACwB,IAAAA;AAAAA,aAAAA,SAAkBA,IAAM,CAAA;IAAA,CAAA,CAAA;ACzB5C,WAAA,KAAYC,MAAMC,EAAAA;EAAAA,SACXC,IAAAA;AAGP,WAFAC,QAAQD,MAAM,uCAAuCA,EAAAA,GAAAA;EAAAA;AAAAA;", "names": ["i", "map", "length", "toString", "bits", "repeat", "reverseMap", "set", "charCodeAt", "token", "e", "split", "r", "bytes", "array<PERSON>ength", "nPart", "decoded", "String", "fromCodePoint", "base64Str", "get", "slice", "bytesArray", "push", "replaceAll", "code", "parse", "jsonPayload", "error", "console"]}