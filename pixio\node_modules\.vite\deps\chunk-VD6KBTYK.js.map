{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-V3MOBCDF.mjs"], "sourcesContent": ["// src/routes/tax-regions/common/constants.ts\nvar TaxRateRuleReferenceType = /* @__PURE__ */ ((TaxRateRuleReferenceType2) => {\n  TaxRateRuleReferenceType2[\"PRODUCT\"] = \"product\";\n  TaxRateRuleReferenceType2[\"PRODUCT_TYPE\"] = \"product_type\";\n  return TaxRateRuleReferenceType2;\n})(TaxRateRuleReferenceType || {});\n\nexport {\n  TaxRateRuleReferenceType\n};\n"], "mappings": ";AACA,IAAI,4BAA4C,CAAC,8BAA8B;AAC7E,4BAA0B,SAAS,IAAI;AACvC,4BAA0B,cAAc,IAAI;AAC5C,SAAO;AACT,GAAG,4BAA4B,CAAC,CAAC;", "names": []}