{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-6HTZNHPT.mjs"], "sourcesContent": ["// src/components/utilities/keybound-form/keybound-form.tsx\nimport React from \"react\";\nimport { jsx } from \"react/jsx-runtime\";\nvar KeyboundForm = React.forwardRef(({ onSubmit, onKeyDown, ...rest }, ref) => {\n  const handleSubmit = (event) => {\n    event.preventDefault();\n    onSubmit?.(event);\n  };\n  const handleKeyDown = (event) => {\n    if (event.key === \"Enter\") {\n      if (event.target instanceof HTMLTextAreaElement && !(event.metaKey || event.ctrlKey)) {\n        return;\n      }\n      event.preventDefault();\n      if (event.metaKey || event.ctrlKey) {\n        handleSubmit(event);\n      }\n    }\n  };\n  return /* @__PURE__ */ jsx(\n    \"form\",\n    {\n      ...rest,\n      onSubmit: handleSubmit,\n      onKeyDown: onKeyDown ?? handleKeyDown,\n      ref\n    }\n  );\n});\nKeyboundForm.displayName = \"KeyboundForm\";\n\nexport {\n  KeyboundForm\n};\n"], "mappings": ";;;;;;;;;;;AACA,mBAAkB;AAClB,yBAAoB;AACpB,IAAI,eAAe,aAAAA,QAAM,WAAW,CAAC,EAAE,UAAU,WAAW,GAAG,KAAK,GAAG,QAAQ;AAC7E,QAAM,eAAe,CAAC,UAAU;AAC9B,UAAM,eAAe;AACrB,yCAAW;AAAA,EACb;AACA,QAAM,gBAAgB,CAAC,UAAU;AAC/B,QAAI,MAAM,QAAQ,SAAS;AACzB,UAAI,MAAM,kBAAkB,uBAAuB,EAAE,MAAM,WAAW,MAAM,UAAU;AACpF;AAAA,MACF;AACA,YAAM,eAAe;AACrB,UAAI,MAAM,WAAW,MAAM,SAAS;AAClC,qBAAa,KAAK;AAAA,MACpB;AAAA,IACF;AAAA,EACF;AACA,aAAuB;AAAA,IACrB;AAAA,IACA;AAAA,MACE,GAAG;AAAA,MACH,UAAU;AAAA,MACV,WAAW,aAAa;AAAA,MACxB;AAAA,IACF;AAAA,EACF;AACF,CAAC;AACD,aAAa,cAAc;", "names": ["React"]}