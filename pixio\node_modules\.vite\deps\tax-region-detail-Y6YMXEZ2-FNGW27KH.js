import {
  TaxOverrideTable,
  TaxRateLine,
  useTaxOverrideTable
} from "./chunk-VKJZRWGQ.js";
import "./chunk-VD6KBTYK.js";
import {
  TaxRegionTable,
  useTaxRegionTable
} from "./chunk-473JZMR2.js";
import {
  TaxRegionCard
} from "./chunk-K7VPWFD7.js";
import {
  getCountryProvinceObjectByIso2
} from "./chunk-KL72CHML.js";
import {
  useTaxRateTableQuery
} from "./chunk-GDXEFZZY.js";
import {
  useTaxRegionTableQuery
} from "./chunk-GEC36FCE.js";
import "./chunk-XYHEMHQ5.js";
import "./chunk-QF476XOZ.js";
import {
  formatProvider
} from "./chunk-LVAKEKGS.js";
import "./chunk-D2VV3NDE.js";
import "./chunk-VCBFQV64.js";
import "./chunk-QLJZR2JY.js";
import "./chunk-X3TOWPPJ.js";
import {
  SingleColumnPage
} from "./chunk-3LNIL4XX.js";
import "./chunk-32T72GVU.js";
import "./chunk-QX6SXRUW.js";
import {
  TaxRegionDetailBreadcrumb,
  taxRegionLoader
} from "./chunk-MM7T76RN.js";
import "./chunk-HPGXK5DQ.js";
import {
  useExtension
} from "./chunk-FTD3ZWHZ.js";
import {
  SingleColumnPageSkeleton
} from "./chunk-YXT43UJF.js";
import "./chunk-CFRQOB2M.js";
import "./chunk-66DVUN72.js";
import "./chunk-WISME5HP.js";
import "./chunk-WAUSOHFQ.js";
import "./chunk-3ORK3QZ4.js";
import {
  useTaxRates
} from "./chunk-3A5TVVNI.js";
import {
  useTaxRegion,
  useTaxRegions
} from "./chunk-EZ62MM7J.js";
import "./chunk-3R6LYIXM.js";
import "./chunk-2P2O4TMQ.js";
import "./chunk-KI3HEITX.js";
import "./chunk-2VUCAO7L.js";
import "./chunk-PTIULTKD.js";
import "./chunk-KISXY2LD.js";
import "./chunk-L7C3XPHJ.js";
import "./chunk-T7MG2EIJ.js";
import "./chunk-MER4VYPR.js";
import "./chunk-2AWKOUCD.js";
import "./chunk-YL3TYBFR.js";
import "./chunk-TZWW72YW.js";
import "./chunk-MSQ25CWB.js";
import "./chunk-ONYSAQ5Z.js";
import "./chunk-NIH7SAXN.js";
import "./chunk-UEJFWAFE.js";
import "./chunk-GR55MFKT.js";
import "./chunk-AAFHKNJG.js";
import "./chunk-7XDBFDTZ.js";
import "./chunk-3JKGO5XL.js";
import "./chunk-OISUTS7G.js";
import "./chunk-AJYMIHLQ.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import "./chunk-JFCIYOH4.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-5GF3RGIE.js";
import {
  useLoaderData,
  useParams
} from "./chunk-T7YBVUWZ.js";
import {
  Alert,
  Badge,
  Button,
  Container,
  Heading,
  Text,
  Tooltip
} from "./chunk-YP2LLXWB.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-ZSQ362B4.js";
import {
  keepPreviousData
} from "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/tax-region-detail-Y6YMXEZ2.mjs
var import_react = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var import_react2 = __toESM(require_react(), 1);
var import_jsx_runtime4 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime5 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime6 = __toESM(require_jsx_runtime(), 1);
var TaxRegionDetailSection = ({
  taxRegion
}) => {
  const { t } = useTranslation();
  const defaultRates = taxRegion.tax_rates.filter((r) => r.is_default === true);
  const showBage = defaultRates.length === 0;
  return (0, import_jsx_runtime.jsxs)(Container, { className: "divide-y p-0", children: [
    (0, import_jsx_runtime.jsx)(
      TaxRegionCard,
      {
        taxRegion,
        type: "header",
        asLink: false,
        badge: showBage && (0, import_jsx_runtime.jsx)(Tooltip, { content: t("taxRegions.fields.noDefaultRate.tooltip"), children: (0, import_jsx_runtime.jsx)(Badge, { color: "orange", size: "2xsmall", className: "cursor-default", children: t("taxRegions.fields.noDefaultRate.label") }) })
      }
    ),
    defaultRates.map((rate) => {
      return (0, import_jsx_runtime.jsx)(TaxRateLine, { taxRate: rate }, rate.id);
    })
  ] });
};
var PAGE_SIZE = 10;
var PREFIX = "p";
var TaxRegionProvinceSection = ({
  taxRegion,
  showSublevelRegions
}) => {
  const { t } = useTranslation();
  const { searchParams, raw } = useTaxRegionTableQuery({
    pageSize: PAGE_SIZE,
    prefix: PREFIX
  });
  const { tax_regions, count, isPending, isError, error } = useTaxRegions(
    {
      ...searchParams,
      parent_id: taxRegion.id
    },
    {
      placeholderData: keepPreviousData
    }
  );
  const { table } = useTaxRegionTable({
    count,
    data: tax_regions,
    pageSize: PAGE_SIZE,
    prefix: PREFIX
  });
  const provinceObject = getCountryProvinceObjectByIso2(taxRegion.country_code);
  if (!provinceObject && !showSublevelRegions && !taxRegion.children.length) {
    return null;
  }
  const type = (provinceObject == null ? void 0 : provinceObject.type) || "sublevel";
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime2.jsx)(Container, { className: "divide-y p-0", children: (0, import_jsx_runtime2.jsx)(
    TaxRegionTable,
    {
      variant: "province",
      action: { to: `provinces/create`, label: t("actions.create") },
      table,
      isPending,
      queryObject: raw,
      count,
      children: (0, import_jsx_runtime2.jsx)(Heading, { level: "h2", children: t(`taxRegions.${type}.header`) })
    }
  ) });
};
var PAGE_SIZE2 = 10;
var PREFIX2 = "o";
var TaxRegionOverrideSection = ({
  taxRegion
}) => {
  const { t } = useTranslation();
  const { searchParams, raw } = useTaxRateTableQuery({
    pageSize: PAGE_SIZE2,
    prefix: PREFIX2
  });
  const { tax_rates, count, isPending, isError, error } = useTaxRates(
    {
      ...searchParams,
      tax_region_id: taxRegion.id,
      is_default: false
    },
    {
      placeholderData: keepPreviousData
    }
  );
  const { table } = useTaxOverrideTable({
    count,
    data: tax_rates,
    pageSize: PAGE_SIZE2,
    prefix: PREFIX2
  });
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime3.jsx)(Container, { className: "p-0", children: (0, import_jsx_runtime3.jsx)(
    TaxOverrideTable,
    {
      isPending,
      table,
      count,
      action: {
        label: t("actions.create"),
        to: "overrides/create"
      },
      queryObject: raw,
      prefix: PREFIX2,
      children: (0, import_jsx_runtime3.jsx)(Heading, { level: "h2", children: t("taxRegions.taxOverrides.header") })
    }
  ) });
};
var TaxRegionSublevelAlert = ({
  taxRegion,
  showSublevelRegions,
  setShowSublevelRegions
}) => {
  const { t } = useTranslation();
  const [dismissed, setDismissed] = (0, import_react2.useState)(false);
  const provinceObject = getCountryProvinceObjectByIso2(taxRegion.country_code);
  if (provinceObject || showSublevelRegions || dismissed || taxRegion.children.length) {
    return null;
  }
  return (0, import_jsx_runtime4.jsx)(Alert, { dismissible: true, variant: "info", className: "bg-ui-bg-base", children: (0, import_jsx_runtime4.jsxs)("div", { className: "flex flex-col gap-y-3", children: [
    (0, import_jsx_runtime4.jsxs)("div", { className: "flex flex-col", children: [
      (0, import_jsx_runtime4.jsx)(Text, { size: "small", leading: "compact", weight: "plus", asChild: true, children: (0, import_jsx_runtime4.jsx)("h2", { children: t("taxRegions.fields.sublevels.alert.header") }) }),
      (0, import_jsx_runtime4.jsx)(Text, { size: "small", leading: "compact", className: "text-pretty", children: t("taxRegions.fields.sublevels.alert.description") })
    ] }),
    (0, import_jsx_runtime4.jsxs)("div", { className: "flex items-center gap-x-3", children: [
      (0, import_jsx_runtime4.jsx)(
        Button,
        {
          variant: "secondary",
          size: "small",
          onClick: () => setShowSublevelRegions(true),
          children: t("taxRegions.fields.sublevels.alert.action")
        }
      ),
      (0, import_jsx_runtime4.jsx)(
        Button,
        {
          variant: "transparent",
          size: "small",
          onClick: () => setDismissed(true),
          children: t("actions.hide")
        }
      )
    ] })
  ] }) });
};
function TaxRegionProviderSection({
  taxRegion
}) {
  const { t } = useTranslation();
  return (0, import_jsx_runtime5.jsxs)(Container, { className: "divide-y p-0", children: [
    (0, import_jsx_runtime5.jsx)(Heading, { level: "h2", className: "px-6 py-4", children: t("taxRegions.provider.header") }),
    (0, import_jsx_runtime5.jsx)("div", { className: "px-6 py-4", children: taxRegion.provider_id && (0, import_jsx_runtime5.jsx)("span", { className: "text-ui-fg-subtle", children: formatProvider(taxRegion.provider_id) }) })
  ] });
}
var TaxRegionDetail = () => {
  const { id } = useParams();
  const [showSublevelRegions, setShowSublevelRegions] = (0, import_react.useState)(false);
  const initialData = useLoaderData();
  const {
    tax_region: taxRegion,
    isLoading,
    isError,
    error
  } = useTaxRegion(id, void 0, { initialData });
  const { getWidgets } = useExtension();
  if (isLoading || !taxRegion) {
    return (0, import_jsx_runtime6.jsx)(SingleColumnPageSkeleton, { sections: 4, showJSON: true });
  }
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime6.jsxs)(
    SingleColumnPage,
    {
      data: taxRegion,
      showJSON: true,
      widgets: {
        after: getWidgets("tax.details.after"),
        before: getWidgets("tax.details.before")
      },
      children: [
        (0, import_jsx_runtime6.jsx)(
          TaxRegionSublevelAlert,
          {
            taxRegion,
            showSublevelRegions,
            setShowSublevelRegions
          }
        ),
        (0, import_jsx_runtime6.jsx)(TaxRegionDetailSection, { taxRegion }),
        (0, import_jsx_runtime6.jsx)(
          TaxRegionProvinceSection,
          {
            taxRegion,
            showSublevelRegions
          }
        ),
        (0, import_jsx_runtime6.jsx)(TaxRegionOverrideSection, { taxRegion }),
        (0, import_jsx_runtime6.jsx)(TaxRegionProviderSection, { taxRegion })
      ]
    }
  );
};
export {
  TaxRegionDetailBreadcrumb as Breadcrumb,
  TaxRegionDetail as Component,
  TaxRegionDetail,
  taxRegionLoader as loader
};
//# sourceMappingURL=tax-region-detail-Y6YMXEZ2-FNGW27KH.js.map
