import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";

// node_modules/@medusajs/dashboard/dist/chunk-LSEYENCI.mjs
var usePromotionTableFilters = () => {
  const { t } = useTranslation();
  let filters = [
    { label: t("fields.createdAt"), key: "created_at", type: "date" },
    { label: t("fields.updatedAt"), key: "updated_at", type: "date" }
  ];
  return filters;
};

export {
  usePromotionTableFilters
};
//# sourceMappingURL=chunk-ZIXJCBL3.js.map
