import {
  useQueryParams
} from "./chunk-32T72GVU.js";

// node_modules/@medusajs/dashboard/dist/chunk-I5HYE2RW.mjs
var useTaxRateTableQuery = ({
  prefix,
  pageSize = 20
}) => {
  const queryObject = useQueryParams(
    ["offset", "q", "order", "created_at", "updated_at"],
    prefix
  );
  const { offset, q, order, created_at, updated_at } = queryObject;
  const searchParams = {
    limit: pageSize,
    offset: offset ? Number(offset) : 0,
    order,
    created_at: created_at ? JSON.parse(created_at) : void 0,
    updated_at: updated_at ? JSON.parse(updated_at) : void 0,
    q
  };
  return {
    searchParams,
    raw: queryObject
  };
};

export {
  useTaxRateTableQuery
};
//# sourceMappingURL=chunk-GDXEFZZY.js.map
