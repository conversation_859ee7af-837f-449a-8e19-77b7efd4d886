import {
  getFulfillableQuantity
} from "./chunk-ABXWNTVT.js";
import "./chunk-EGRHWZRV.js";
import {
  KeyboundForm
} from "./chunk-DP54EP6X.js";
import {
  RouteFocusModal,
  useRouteModal
} from "./chunk-MVVOBQIC.js";
import {
  t
} from "./chunk-WHQIBI5S.js";
import {
  z
} from "./chunk-4XXECALA.js";
import {
  Thumbnail
} from "./chunk-6GQUHAET.js";
import {
  Form,
  useForm,
  useWatch
} from "./chunk-XXJU43CK.js";
import {
  useStockLocations
} from "./chunk-ONYSAQ5Z.js";
import {
  ordersQueryKeys,
  useOrder
} from "./chunk-NIH7SAXN.js";
import {
  useCreateReservationItem
} from "./chunk-UEJFWAFE.js";
import "./chunk-AJYMIHLQ.js";
import {
  queryClient
} from "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import "./chunk-JFCIYOH4.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-5GF3RGIE.js";
import {
  useParams
} from "./chunk-T7YBVUWZ.js";
import {
  Alert,
  Button,
  Component,
  ExclamationCircleSolid,
  Heading,
  Input,
  Select,
  Text,
  TriangleDownMini,
  clx,
  toast
} from "./chunk-YP2LLXWB.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-ZSQ362B4.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/order-allocate-items-NFV3KZAY.mjs
var import_react = __toESM(require_react(), 1);
var import_react2 = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var AllocateItemsSchema = z.object({
  location_id: z.string(),
  quantity: z.record(z.string(), z.number().or(z.string()))
});
function OrderAllocateItemsItem({
  item,
  form,
  locationId,
  onQuantityChange
}) {
  var _a, _b, _c, _d, _e, _f;
  const { t: t2 } = useTranslation();
  const variant = item.variant;
  const inventory = ((_a = item.variant) == null ? void 0 : _a.inventory) || [];
  const [isOpen, setIsOpen] = (0, import_react2.useState)(false);
  const quantityField = useWatch({
    control: form.control,
    name: "quantity"
  });
  const hasInventoryKit = !!(variant == null ? void 0 : variant.inventory_items.length) && (variant == null ? void 0 : variant.inventory_items.length) > 1;
  const { availableQuantity, inStockQuantity } = (0, import_react2.useMemo)(() => {
    var _a2, _b2;
    if (!variant || !locationId) {
      return {};
    }
    const locationInventory = (_b2 = (_a2 = inventory[0]) == null ? void 0 : _a2.location_levels) == null ? void 0 : _b2.find(
      (inv) => inv.location_id === locationId
    );
    if (!locationInventory) {
      return {};
    }
    return {
      availableQuantity: locationInventory.available_quantity,
      inStockQuantity: locationInventory.stocked_quantity
    };
  }, [variant, locationId]);
  const hasQuantityError = !hasInventoryKit && availableQuantity && quantityField[`${item.id}-${(_b = item.variant) == null ? void 0 : _b.inventory[0].id}`] && quantityField[`${item.id}-${(_c = item.variant) == null ? void 0 : _c.inventory[0].id}`] > availableQuantity;
  const minValue = 0;
  const maxValue = Math.min(
    getFulfillableQuantity(item),
    availableQuantity || Number.MAX_SAFE_INTEGER
  );
  return (0, import_jsx_runtime.jsxs)("div", { className: "bg-ui-bg-subtle shadow-elevation-card-rest my-2 min-w-[720px] divide-y divide-dashed rounded-xl", children: [
    (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center gap-x-3 p-3 text-sm", children: [
      (0, import_jsx_runtime.jsx)("div", { className: "flex flex-1 items-center", children: (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center gap-x-3", children: [
        hasQuantityError && (0, import_jsx_runtime.jsx)(ExclamationCircleSolid, { className: "text-ui-fg-error" }),
        (0, import_jsx_runtime.jsx)(Thumbnail, { src: item.thumbnail }),
        (0, import_jsx_runtime.jsxs)("div", { className: "flex flex-col", children: [
          (0, import_jsx_runtime.jsxs)("div", { className: "flex flex-row", children: [
            (0, import_jsx_runtime.jsx)(Text, { className: "txt-small flex", as: "span", weight: "plus", children: item.product_title }),
            item.variant_sku && (0, import_jsx_runtime.jsxs)("span", { className: "text-ui-fg-subtle", children: [
              " ",
              "(",
              item.variant_sku,
              ")"
            ] }),
            hasInventoryKit && (0, import_jsx_runtime.jsx)(Component, { className: "text-ui-fg-muted ml-2 overflow-visible pt-[2px]" })
          ] }),
          (0, import_jsx_runtime.jsx)(Text, { as: "div", className: "text-ui-fg-subtle txt-small", children: item.title })
        ] })
      ] }) }),
      (0, import_jsx_runtime.jsxs)(
        "div",
        {
          className: clx(
            "flex flex-1 items-center gap-x-3",
            hasInventoryKit ? "justify-end" : "justify-between"
          ),
          children: [
            !hasInventoryKit && (0, import_jsx_runtime.jsxs)(import_jsx_runtime.Fragment, { children: [
              (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center gap-3", children: [
                (0, import_jsx_runtime.jsx)("div", { className: "bg-ui-border-strong block h-[12px] w-[1px]" }),
                (0, import_jsx_runtime.jsxs)("div", { className: "txt-small flex flex-col", children: [
                  (0, import_jsx_runtime.jsx)("span", { className: "text-ui-fg-subtle font-medium", children: t2("labels.available") }),
                  (0, import_jsx_runtime.jsxs)("span", { className: "text-ui-fg-muted", children: [
                    availableQuantity || "-",
                    availableQuantity && !hasInventoryKit && quantityField[`${item.id}-${(_d = item.variant) == null ? void 0 : _d.inventory[0].id}`] && (0, import_jsx_runtime.jsxs)("span", { className: "text-ui-fg-error txt-small ml-1", children: [
                      "-",
                      quantityField[`${item.id}-${(_e = item.variant) == null ? void 0 : _e.inventory[0].id}`]
                    ] })
                  ] })
                ] })
              ] }),
              (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center gap-3", children: [
                (0, import_jsx_runtime.jsx)("div", { className: "bg-ui-border-strong block h-[12px] w-[1px]" }),
                (0, import_jsx_runtime.jsxs)("div", { className: "txt-small flex flex-col", children: [
                  (0, import_jsx_runtime.jsx)("span", { className: "text-ui-fg-subtle font-medium", children: t2("labels.inStock") }),
                  (0, import_jsx_runtime.jsx)("span", { className: "text-ui-fg-muted", children: inStockQuantity || "-" })
                ] })
              ] })
            ] }),
            (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center gap-3", children: [
              (0, import_jsx_runtime.jsx)("div", { className: "bg-ui-border-strong block h-[12px] w-[1px]" }),
              (0, import_jsx_runtime.jsxs)("div", { className: "text-ui-fg-subtle txt-small mr-2 flex flex-row items-center gap-2", children: [
                (0, import_jsx_runtime.jsx)(
                  Form.Field,
                  {
                    control: form.control,
                    name: hasInventoryKit ? `quantity.${item.id}-` : `quantity.${item.id}-${(_f = item.variant) == null ? void 0 : _f.inventory[0].id}`,
                    rules: {
                      required: !hasInventoryKit,
                      min: !hasInventoryKit && minValue,
                      max: maxValue
                    },
                    render: ({ field }) => {
                      return (0, import_jsx_runtime.jsx)(Form.Item, { children: (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(
                        Input,
                        {
                          className: "bg-ui-bg-base txt-small w-[46px] rounded-lg text-right [appearance:textfield] [&::-webkit-inner-spin-button]:appearance-none [&::-webkit-outer-spin-button]:appearance-none",
                          type: "number",
                          ...field,
                          disabled: !locationId,
                          onChange: (e) => {
                            var _a2;
                            const val = e.target.value === "" ? null : Number(e.target.value);
                            onQuantityChange(
                              (_a2 = item.variant) == null ? void 0 : _a2.inventory[0],
                              item,
                              hasInventoryKit,
                              val,
                              true
                            );
                          }
                        }
                      ) }) });
                    }
                  }
                ),
                " ",
                "/ ",
                item.quantity,
                " ",
                t2("fields.qty")
              ] })
            ] })
          ]
        }
      )
    ] }),
    hasInventoryKit && (0, import_jsx_runtime.jsx)("div", { className: "px-4 py-2", children: (0, import_jsx_runtime.jsxs)(
      "div",
      {
        onClick: () => setIsOpen((o) => !o),
        className: "flex items-center gap-x-2",
        children: [
          (0, import_jsx_runtime.jsx)(
            TriangleDownMini,
            {
              style: { transform: `rotate(${isOpen ? -90 : 0}deg)` },
              className: "text-ui-fg-muted -mt-[1px]"
            }
          ),
          (0, import_jsx_runtime.jsx)("span", { className: "txt-small text-ui-fg-muted cursor-pointer", children: t2("orders.allocateItems.consistsOf", {
            num: inventory.length
          }) })
        ]
      }
    ) }),
    isOpen && variant.inventory.map((i, ind) => {
      const location = i.location_levels.find(
        (l) => l.location_id === locationId
      );
      const hasQuantityError2 = !!quantityField[`${item.id}-${i.id}`] && quantityField[`${item.id}-${i.id}`] > location.available_quantity;
      return (0, import_jsx_runtime.jsxs)("div", { className: "txt-small flex items-center gap-x-3 p-4", children: [
        (0, import_jsx_runtime.jsxs)("div", { className: "flex flex-1 flex-row items-center gap-3", children: [
          hasQuantityError2 && (0, import_jsx_runtime.jsx)(ExclamationCircleSolid, { className: "text-ui-fg-error" }),
          (0, import_jsx_runtime.jsxs)("div", { className: "flex flex-col", children: [
            (0, import_jsx_runtime.jsx)("span", { className: "text-ui-fg-subtle", children: i.title }),
            (0, import_jsx_runtime.jsx)("span", { className: "text-ui-fg-muted", children: t2("orders.allocateItems.requires", {
              num: variant.inventory_items[ind].required_quantity
            }) })
          ] })
        ] }),
        (0, import_jsx_runtime.jsxs)("div", { className: "flex flex-1 flex-row justify-between", children: [
          (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center gap-3", children: [
            (0, import_jsx_runtime.jsx)("div", { className: "bg-ui-border-strong block h-[12px] w-[1px]" }),
            (0, import_jsx_runtime.jsxs)("div", { className: "txt-small flex flex-col", children: [
              (0, import_jsx_runtime.jsx)("span", { className: "text-ui-fg-subtle font-medium", children: t2("labels.available") }),
              (0, import_jsx_runtime.jsxs)("span", { className: "text-ui-fg-muted", children: [
                (location == null ? void 0 : location.available_quantity) || "-",
                (location == null ? void 0 : location.available_quantity) && quantityField[`${item.id}-${i.id}`] && (0, import_jsx_runtime.jsxs)("span", { className: "text-ui-fg-error txt-small ml-1", children: [
                  "-",
                  quantityField[`${item.id}-${i.id}`]
                ] })
              ] })
            ] })
          ] }),
          (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center gap-3", children: [
            (0, import_jsx_runtime.jsx)("div", { className: "bg-ui-border-strong block h-[12px] w-[1px]" }),
            (0, import_jsx_runtime.jsxs)("div", { className: "txt-small flex flex-col", children: [
              (0, import_jsx_runtime.jsx)("span", { className: "text-ui-fg-subtle font-medium", children: t2("labels.inStock") }),
              (0, import_jsx_runtime.jsx)("span", { className: "text-ui-fg-muted", children: (location == null ? void 0 : location.stocked_quantity) || "-" })
            ] })
          ] }),
          (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center gap-3", children: [
            (0, import_jsx_runtime.jsx)("div", { className: "bg-ui-border-strong block h-[12px] w-[1px]" }),
            (0, import_jsx_runtime.jsxs)("div", { className: "text-ui-fg-subtle txt-small mr-1 flex flex-row items-center gap-2", children: [
              (0, import_jsx_runtime.jsx)(
                Form.Field,
                {
                  control: form.control,
                  name: `quantity.${item.id}-${i.id}`,
                  rules: {
                    required: true,
                    min: 0,
                    max: location == null ? void 0 : location.available_quantity
                  },
                  render: ({ field }) => {
                    return (0, import_jsx_runtime.jsx)(Form.Item, { children: (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(
                      Input,
                      {
                        className: "bg-ui-bg-base txt-small w-[46px] rounded-lg text-right [appearance:textfield] [&::-webkit-inner-spin-button]:appearance-none [&::-webkit-outer-spin-button]:appearance-none",
                        type: "number",
                        ...field,
                        disabled: !locationId,
                        onChange: (e) => {
                          const val = e.target.value === "" ? null : Number(e.target.value);
                          onQuantityChange(
                            i,
                            item,
                            hasInventoryKit,
                            val
                          );
                        }
                      }
                    ) }) });
                  }
                }
              ),
              "/",
              " ",
              item.quantity * variant.inventory_items[ind].required_quantity,
              " ",
              t2("fields.qty")
            ] })
          ] })
        ] })
      ] }, i.id);
    })
  ] });
}
function OrderAllocateItemsForm({ order }) {
  var _a, _b, _c;
  const { t: t2 } = useTranslation();
  const { handleSuccess } = useRouteModal();
  const [disableSubmit, setDisableSubmit] = (0, import_react.useState)(false);
  const [filterTerm, setFilterTerm] = (0, import_react.useState)("");
  const { mutateAsync: allocateItems, isPending: isMutating } = useCreateReservationItem();
  const itemsToAllocate = (0, import_react.useMemo)(
    () => order.items.filter(
      (item) => {
        var _a2, _b2;
        return ((_a2 = item.variant) == null ? void 0 : _a2.manage_inventory) && ((_b2 = item.variant) == null ? void 0 : _b2.inventory.length) && item.quantity - item.detail.fulfilled_quantity > 0;
      }
    ),
    [order.items]
  );
  const filteredItems = (0, import_react.useMemo)(() => {
    return itemsToAllocate.filter(
      (i) => i.variant_title.toLowerCase().includes(filterTerm) || i.product_title.toLowerCase().includes(filterTerm)
    );
  }, [itemsToAllocate, filterTerm]);
  const noItemsToAllocate = !itemsToAllocate.length;
  const form = useForm({
    defaultValues: {
      location_id: "",
      quantity: defaultAllocations(itemsToAllocate)
    },
    resolver: t(AllocateItemsSchema)
  });
  const { stock_locations = [] } = useStockLocations();
  const handleSubmit = form.handleSubmit(async (data) => {
    try {
      const payload = Object.entries(data.quantity).filter(([key]) => !key.endsWith("-")).map(([key, quantity]) => [...key.split("-"), quantity]);
      if (payload.some((d) => d[2] === "")) {
        form.setError("root.quantityNotAllocated", {
          type: "manual",
          message: t2("orders.allocateItems.error.quantityNotAllocated")
        });
        return;
      }
      const promises = payload.map(
        ([itemId, inventoryId, quantity]) => allocateItems({
          location_id: data.location_id,
          inventory_item_id: inventoryId,
          line_item_id: itemId,
          quantity
        })
      );
      await Promise.all(promises);
      await queryClient.invalidateQueries({
        queryKey: ordersQueryKeys.details()
      });
      handleSuccess(`/orders/${order.id}`);
      toast.success(t2("general.success"), {
        description: t2("orders.allocateItems.toast.created"),
        dismissLabel: t2("actions.close")
      });
    } catch (e) {
      toast.error(t2("general.error"), {
        description: e.message,
        dismissLabel: t2("actions.close")
      });
    }
  });
  const onQuantityChange = (inventoryItem, lineItem, hasInventoryKit, value, isRoot) => {
    var _a2;
    let shouldDisableSubmit = false;
    const key = isRoot && hasInventoryKit ? `quantity.${lineItem.id}-` : `quantity.${lineItem.id}-${inventoryItem.id}`;
    form.setValue(key, value);
    if (value) {
      const location = inventoryItem.location_levels.find(
        (l) => l.location_id === selectedLocationId
      );
      if (location) {
        if (location.available_quantity < value) {
          shouldDisableSubmit = true;
        }
      }
    }
    if (hasInventoryKit && !isRoot) {
      form.resetField(`quantity.${lineItem.id}-`, { defaultValue: "" });
    }
    if (hasInventoryKit && isRoot) {
      const item = itemsToAllocate.find((i) => i.id === lineItem.id);
      (_a2 = item.variant) == null ? void 0 : _a2.inventory_items.forEach((ii, ind) => {
        var _a3;
        const num = value || 0;
        const inventory = (_a3 = item.variant) == null ? void 0 : _a3.inventory[ind];
        form.setValue(
          `quantity.${lineItem.id}-${inventory.id}`,
          num * ii.required_quantity
        );
        if (value) {
          const location = inventory == null ? void 0 : inventory.location_levels.find(
            (l) => l.location_id === selectedLocationId
          );
          if (location) {
            if (location.available_quantity < value) {
              shouldDisableSubmit = true;
            }
          }
        }
      });
    }
    form.clearErrors("root.quantityNotAllocated");
    setDisableSubmit(shouldDisableSubmit);
  };
  const selectedLocationId = useWatch({
    name: "location_id",
    control: form.control
  });
  (0, import_react.useEffect)(() => {
    if (selectedLocationId) {
      form.setValue("quantity", defaultAllocations(itemsToAllocate));
    }
  }, [selectedLocationId]);
  const allocationError = (_c = (_b = (_a = form.formState.errors) == null ? void 0 : _a.root) == null ? void 0 : _b.quantityNotAllocated) == null ? void 0 : _c.message;
  return (0, import_jsx_runtime2.jsx)(RouteFocusModal.Form, { form, children: (0, import_jsx_runtime2.jsxs)(
    KeyboundForm,
    {
      onSubmit: handleSubmit,
      className: "flex h-full flex-col overflow-hidden",
      children: [
        (0, import_jsx_runtime2.jsx)(RouteFocusModal.Header, {}),
        (0, import_jsx_runtime2.jsx)(RouteFocusModal.Body, { className: "flex h-full w-full flex-col items-center divide-y overflow-y-auto", children: (0, import_jsx_runtime2.jsx)("div", { className: "flex size-full flex-col items-center overflow-auto p-16", children: (0, import_jsx_runtime2.jsx)("div", { className: "flex w-full max-w-[736px] flex-col justify-center px-2 pb-2", children: (0, import_jsx_runtime2.jsxs)("div", { className: "flex flex-col gap-8 divide-y divide-dashed", children: [
          (0, import_jsx_runtime2.jsx)(Heading, { children: t2("orders.allocateItems.title") }),
          (0, import_jsx_runtime2.jsxs)("div", { className: "flex-1 divide-y divide-dashed pt-8", children: [
            (0, import_jsx_runtime2.jsx)(
              Form.Field,
              {
                control: form.control,
                name: "location_id",
                render: ({ field: { onChange, ref, ...field } }) => {
                  return (0, import_jsx_runtime2.jsxs)(Form.Item, { children: [
                    (0, import_jsx_runtime2.jsxs)("div", { className: "flex items-center gap-3", children: [
                      (0, import_jsx_runtime2.jsxs)("div", { className: "flex-1", children: [
                        (0, import_jsx_runtime2.jsx)(Form.Label, { children: t2("fields.location") }),
                        (0, import_jsx_runtime2.jsx)(Form.Hint, { children: t2("orders.allocateItems.locationDescription") })
                      ] }),
                      (0, import_jsx_runtime2.jsx)("div", { className: "flex-1", children: (0, import_jsx_runtime2.jsx)(Form.Control, { children: (0, import_jsx_runtime2.jsxs)(Select, { onValueChange: onChange, ...field, children: [
                        (0, import_jsx_runtime2.jsx)(
                          Select.Trigger,
                          {
                            className: "bg-ui-bg-base",
                            ref,
                            children: (0, import_jsx_runtime2.jsx)(Select.Value, {})
                          }
                        ),
                        (0, import_jsx_runtime2.jsx)(Select.Content, { children: stock_locations.map((l) => (0, import_jsx_runtime2.jsx)(Select.Item, { value: l.id, children: l.name }, l.id)) })
                      ] }) }) })
                    ] }),
                    (0, import_jsx_runtime2.jsx)(Form.ErrorMessage, {})
                  ] });
                }
              }
            ),
            (0, import_jsx_runtime2.jsxs)(Form.Item, { className: "mt-8 pt-8", children: [
              (0, import_jsx_runtime2.jsxs)("div", { className: "flex flex-row items-center", children: [
                (0, import_jsx_runtime2.jsxs)("div", { className: "flex-1", children: [
                  (0, import_jsx_runtime2.jsx)(Form.Label, { children: t2("orders.allocateItems.itemsToAllocate") }),
                  (0, import_jsx_runtime2.jsx)(Form.Hint, { children: t2("orders.allocateItems.itemsToAllocateDesc") })
                ] }),
                (0, import_jsx_runtime2.jsx)("div", { className: "flex-1", children: (0, import_jsx_runtime2.jsx)(
                  Input,
                  {
                    value: filterTerm,
                    onChange: (e) => setFilterTerm(e.target.value),
                    placeholder: t2("orders.allocateItems.search"),
                    autoComplete: "off",
                    type: "search"
                  }
                ) })
              ] }),
              allocationError && (0, import_jsx_runtime2.jsx)(Alert, { className: "mb-4", dismissible: true, variant: "error", children: allocationError }),
              (0, import_jsx_runtime2.jsx)("div", { className: "flex flex-col gap-y-1", children: filteredItems.map((item) => (0, import_jsx_runtime2.jsx)(
                OrderAllocateItemsItem,
                {
                  form,
                  item,
                  locationId: selectedLocationId,
                  onQuantityChange
                },
                item.id
              )) })
            ] })
          ] })
        ] }) }) }) }),
        (0, import_jsx_runtime2.jsx)(RouteFocusModal.Footer, { children: (0, import_jsx_runtime2.jsxs)("div", { className: "flex items-center justify-end gap-x-2", children: [
          (0, import_jsx_runtime2.jsx)(RouteFocusModal.Close, { asChild: true, children: (0, import_jsx_runtime2.jsx)(Button, { size: "small", variant: "secondary", children: t2("actions.cancel") }) }),
          (0, import_jsx_runtime2.jsx)(
            Button,
            {
              size: "small",
              type: "submit",
              isLoading: isMutating,
              disabled: !selectedLocationId || disableSubmit,
              children: t2("orders.allocateItems.action")
            }
          )
        ] }) })
      ]
    }
  ) });
}
function defaultAllocations(items) {
  const ret = {};
  items.forEach((item) => {
    var _a, _b, _c;
    const hasInventoryKit = ((_a = item.variant) == null ? void 0 : _a.inventory_items.length) > 1;
    ret[hasInventoryKit ? `${item.id}-` : `${item.id}-${(_b = item.variant) == null ? void 0 : _b.inventory[0].id}`] = "";
    if (hasInventoryKit) {
      (_c = item.variant) == null ? void 0 : _c.inventory.forEach((i) => {
        ret[`${item.id}-${i.id}`] = "";
      });
    }
  });
  return ret;
}
function OrderAllocateItems() {
  const { id } = useParams();
  const { order, isLoading, isError, error } = useOrder(id, {
    fields: "currency_code,*items,*items.variant,+items.variant.product.title,*items.variant.inventory,*items.variant.inventory.location_levels,*items.variant.inventory_items,*shipping_address"
  });
  if (isError) {
    throw error;
  }
  const ready = !isLoading && order;
  return (0, import_jsx_runtime3.jsx)(RouteFocusModal, { children: ready && (0, import_jsx_runtime3.jsx)(OrderAllocateItemsForm, { order }) });
}
export {
  OrderAllocateItems as Component
};
//# sourceMappingURL=order-allocate-items-NFV3KZAY-V3BWWLET.js.map
