{"version": 3, "sources": ["../../react-country-flag/src/index.tsx"], "sourcesContent": ["import * as React from 'react';\n\nconst DEFAULT_CDN_URL =\n  'https://cdn.jsdelivr.net/gh/lipis/flag-icons/flags/4x3/';\nconst DEFAULT_CDN_SUFFIX = 'svg';\n\n// offset between uppercase ascii and regional indicator symbols\nconst OFFSET = 127397;\n\ninterface EmojiProps extends React.HTMLAttributes<HTMLSpanElement> {\n  cdnSuffix?: string;\n  cdnUrl?: string;\n  countryCode: string;\n  style?: React.CSSProperties;\n  svg?: false;\n}\n\ninterface ImgProps extends React.ImgHTMLAttributes<HTMLImageElement> {\n  cdnSuffix?: string;\n  cdnUrl?: string;\n  countryCode: string;\n  style?: React.CSSProperties;\n  svg?: true;\n}\n\nexport type ReactCountryFlagProps = EmojiProps | ImgProps;\n\nexport const ReactCountryFlag = ({\n  cdnSuffix = DEFAULT_CDN_SUFFIX,\n  cdnUrl = DEFAULT_CDN_URL,\n  countryCode,\n  style,\n  svg = false,\n  ...props\n}: ReactCountryFlagProps) => {\n  if (typeof countryCode !== 'string') {\n    return null;\n  }\n\n  if (svg) {\n    const flagUrl = `${cdnUrl}${countryCode.toLowerCase()}.${cdnSuffix}`;\n\n    return (\n      <img\n        {...props}\n        src={flagUrl}\n        style={{\n          display: 'inline-block',\n          width: '1em',\n          height: '1em',\n          verticalAlign: 'middle',\n          ...style,\n        }}\n      />\n    );\n  }\n\n  const emoji = countryCode\n    .toUpperCase()\n    .replace(/./g, char => String.fromCodePoint(char.charCodeAt(0) + OFFSET));\n\n  return (\n    <span\n      role=\"img\"\n      {...props}\n      style={{\n        display: 'inline-block',\n        fontSize: '1em',\n        lineHeight: '1em',\n        verticalAlign: 'middle',\n        ...style,\n      }}\n    >\n      {emoji}\n    </span>\n  );\n};\n\nexport default ReactCountryFlag;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,IAAMA,kBACJ;AACF,IAAMC,qBAAqB;AAG3B,IAAMC,SAAS;IAoBFC,mBAAmB,SAAnBA,kBAAgBC,MAAA;4BAC3BC,WAAAA,YAASC,mBAAA,SAAGL,qBAAkBK,gBAAAC,cAAAH,KAC9BI,QAAAA,SAAMD,gBAAA,SAAGP,kBAAeO,aACxBE,cAAWL,KAAXK,aACAC,QAAKN,KAALM,OAAKC,WAAAP,KACLQ,KAAAA,MAAGD,aAAA,SAAG,QAAKA,UACRE,QAAKC,8BAAAV,MAAAW,SAAA;AAER,MAAI,OAAON,gBAAgB,UAAU;AACnC,WAAO;;AAGT,MAAIG,KAAK;AACP,QAAMI,UAAO,KAAMR,SAASC,YAAYQ,YAAW,IAAE,MAAIZ;AAEzD,eACEa,4BAAAA,OAAAA,OAAAA,OAAAA,CAAAA,GACML,OAAK;MACTM,KAAKH;MACLN,OAAKU,SAAA;QACHC,SAAS;QACTC,OAAO;QACPC,QAAQ;QACRC,eAAe;SACZd,KAAK;;;AAMhB,MAAMe,QAAQhB,YACXiB,YAAW,EACXC,QAAQ,MAAM,SAAAC,OAAI;AAAA,WAAIC,OAAOC,cAAcF,MAAKG,WAAW,CAAC,IAAI7B,MAAM;;AAEzE,aACEgB,4BAAAA,QAAAA,OAAAA,OAAAA;IACEc,MAAK;KACDnB,OAAK;IACTH,OAAKU,SAAA;MACHC,SAAS;MACTY,UAAU;MACVC,YAAY;MACZV,eAAe;OACZd,KAAK;MAGTe,KAAK;AAGZ;;", "names": ["DEFAULT_CDN_URL", "DEFAULT_CDN_SUFFIX", "OFFSET", "ReactCountryFlag", "_ref", "cdnSuffix", "_ref$cdnSuffix", "_ref$cdnUrl", "cdnUrl", "countryCode", "style", "_ref$svg", "svg", "props", "_objectWithoutPropertiesLoose", "_excluded", "flagUrl", "toLowerCase", "React", "src", "_extends", "display", "width", "height", "verticalAlign", "emoji", "toUpperCase", "replace", "char", "String", "fromCodePoint", "charCodeAt", "role", "fontSize", "lineHeight"]}