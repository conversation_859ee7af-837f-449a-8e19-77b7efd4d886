// node_modules/@medusajs/dashboard/dist/chunk-774WSTCC.mjs
var queryKeysFactory = (globalKey) => {
  const queryKeyFactory = {
    all: [globalKey],
    lists: () => [...queryKeyFactory.all, "list"],
    list: (query) => [...queryKeyFactory.lists(), query ? { query } : void 0].filter(
      (k) => !!k
    ),
    details: () => [...queryKeyFactory.all, "detail"],
    detail: (id, query) => [...queryKeyFactory.details(), id, query ? { query } : void 0].filter(
      (k) => !!k
    )
  };
  return queryKeyFactory;
};

export {
  queryKeysFactory
};
//# sourceMappingURL=chunk-CUPZIPFX.js.map
