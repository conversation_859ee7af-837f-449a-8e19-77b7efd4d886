{"version": 3, "sources": ["../../@medusajs/dashboard/dist/customer-metadata-5NPUMPZ2.mjs"], "sourcesContent": ["import \"./chunk-XRTVFYCW.mjs\";\nimport {\n  MetadataForm\n} from \"./chunk-S2UEWRDA.mjs\";\nimport \"./chunk-IUCDCPJU.mjs\";\nimport \"./chunk-6HTZNHPT.mjs\";\nimport \"./chunk-4TC5YS65.mjs\";\nimport \"./chunk-LPEUYMRK.mjs\";\nimport \"./chunk-OC7BQLYI.mjs\";\nimport \"./chunk-OBQI23QM.mjs\";\nimport {\n  useCustomer,\n  useUpdateCustomer\n} from \"./chunk-F6PXCY3N.mjs\";\nimport \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-RPUOO7AV.mjs\";\n\n// src/routes/customers/customer-metadata/customer-metadata.tsx\nimport { useParams } from \"react-router-dom\";\nimport { jsx } from \"react/jsx-runtime\";\nvar CustomerMetadata = () => {\n  const { id } = useParams();\n  const { customer, isPending, isError, error } = useCustomer(id);\n  const { mutateAsync, isPending: isMutating } = useUpdateCustomer(id);\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsx(\n    MetadataForm,\n    {\n      metadata: customer?.metadata,\n      hook: mutateAsync,\n      isPending,\n      isMutating\n    }\n  );\n};\nexport {\n  CustomerMetadata as Component\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqBA,yBAAoB;AACpB,IAAI,mBAAmB,MAAM;AAC3B,QAAM,EAAE,GAAG,IAAI,UAAU;AACzB,QAAM,EAAE,UAAU,WAAW,SAAS,MAAM,IAAI,YAAY,EAAE;AAC9D,QAAM,EAAE,aAAa,WAAW,WAAW,IAAI,kBAAkB,EAAE;AACnE,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB;AAAA,IACrB;AAAA,IACA;AAAA,MACE,UAAU,qCAAU;AAAA,MACpB,MAAM;AAAA,MACN;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACF;", "names": []}