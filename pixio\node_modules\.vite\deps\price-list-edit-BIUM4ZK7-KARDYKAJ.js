import {
  PriceListStatus,
  PriceListType
} from "./chunk-EG6IR476.js";
import "./chunk-EGRHWZRV.js";
import {
  KeyboundForm
} from "./chunk-DP54EP6X.js";
import {
  RouteDrawer,
  useRouteModal
} from "./chunk-MVVOBQIC.js";
import {
  t
} from "./chunk-WHQIBI5S.js";
import {
  z
} from "./chunk-4XXECALA.js";
import {
  Form,
  useForm
} from "./chunk-XXJU43CK.js";
import {
  usePriceList,
  useUpdatePriceList
} from "./chunk-MER4VYPR.js";
import "./chunk-2AWKOUCD.js";
import "./chunk-AJYMIHLQ.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import "./chunk-JFCIYOH4.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-5GF3RGIE.js";
import {
  useParams
} from "./chunk-T7YBVUWZ.js";
import {
  Button,
  Heading,
  Input,
  RadioGroup,
  Select,
  Textarea,
  toast
} from "./chunk-YP2LLXWB.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-ZSQ362B4.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/price-list-edit-BIUM4ZK7.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var PriceListEditSchema = z.object({
  status: z.nativeEnum(PriceListStatus),
  type: z.nativeEnum(PriceListType),
  title: z.string().min(1),
  description: z.string().min(1)
});
var PriceListEditForm = ({ priceList }) => {
  const { t: t2 } = useTranslation();
  const { handleSuccess } = useRouteModal();
  const form = useForm({
    defaultValues: {
      type: priceList.type,
      title: priceList.title,
      description: priceList.description,
      status: priceList.status
    },
    resolver: t(PriceListEditSchema)
  });
  const { mutateAsync, isPending } = useUpdatePriceList(priceList.id);
  const handleSubmit = form.handleSubmit(async (values) => {
    await mutateAsync(values, {
      onSuccess: ({ price_list }) => {
        toast.success(
          t2("priceLists.edit.successToast", {
            title: price_list.title
          })
        );
        handleSuccess();
      },
      onError: (error) => {
        toast.error(error.message);
      }
    });
  });
  return (0, import_jsx_runtime.jsx)(RouteDrawer.Form, { form, children: (0, import_jsx_runtime.jsxs)(
    KeyboundForm,
    {
      className: "flex flex-1 flex-col overflow-hidden",
      onSubmit: handleSubmit,
      children: [
        (0, import_jsx_runtime.jsxs)(RouteDrawer.Body, { className: "flex flex-1 flex-col gap-y-6 overflow-auto", children: [
          (0, import_jsx_runtime.jsx)(
            Form.Field,
            {
              control: form.control,
              name: "type",
              render: ({ field: { onChange, ...field } }) => {
                return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                  (0, import_jsx_runtime.jsxs)("div", { children: [
                    (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("priceLists.fields.type.label") }),
                    (0, import_jsx_runtime.jsx)(Form.Hint, { children: t2("priceLists.fields.type.hint") })
                  ] }),
                  (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsxs)(RadioGroup, { ...field, onValueChange: onChange, children: [
                    (0, import_jsx_runtime.jsx)(
                      RadioGroup.ChoiceBox,
                      {
                        value: "sale",
                        label: t2("priceLists.fields.type.options.sale.label"),
                        description: t2(
                          "priceLists.fields.type.options.sale.description"
                        )
                      }
                    ),
                    (0, import_jsx_runtime.jsx)(
                      RadioGroup.ChoiceBox,
                      {
                        value: "override",
                        label: t2(
                          "priceLists.fields.type.options.override.label"
                        ),
                        description: t2(
                          "priceLists.fields.type.options.override.description"
                        )
                      }
                    )
                  ] }) }),
                  (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                ] });
              }
            }
          ),
          (0, import_jsx_runtime.jsxs)("div", { className: "flex flex-col gap-y-4", children: [
            (0, import_jsx_runtime.jsx)(
              Form.Field,
              {
                control: form.control,
                name: "title",
                render: ({ field }) => {
                  return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                    (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("fields.title") }),
                    (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(Input, { ...field }) }),
                    (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                  ] });
                }
              }
            ),
            (0, import_jsx_runtime.jsx)(
              Form.Field,
              {
                control: form.control,
                name: "status",
                render: ({ field: { onChange, ref, ...field } }) => {
                  return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                    (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("priceLists.fields.status.label") }),
                    (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsxs)(Select, { ...field, onValueChange: onChange, children: [
                      (0, import_jsx_runtime.jsx)(Select.Trigger, { ref, children: (0, import_jsx_runtime.jsx)(Select.Value, {}) }),
                      (0, import_jsx_runtime.jsxs)(Select.Content, { children: [
                        (0, import_jsx_runtime.jsx)(Select.Item, { value: "active", children: t2("priceLists.fields.status.options.active") }),
                        (0, import_jsx_runtime.jsx)(Select.Item, { value: "draft", children: t2("priceLists.fields.status.options.draft") })
                      ] })
                    ] }) }),
                    (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                  ] });
                }
              }
            ),
            (0, import_jsx_runtime.jsx)(
              Form.Field,
              {
                control: form.control,
                name: "description",
                render: ({ field }) => {
                  return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                    (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("fields.description") }),
                    (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(Textarea, { ...field }) }),
                    (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                  ] });
                }
              }
            )
          ] })
        ] }),
        (0, import_jsx_runtime.jsx)(RouteDrawer.Footer, { className: "shrink-0", children: (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center justify-end gap-x-2", children: [
          (0, import_jsx_runtime.jsx)(RouteDrawer.Close, { asChild: true, children: (0, import_jsx_runtime.jsx)(Button, { size: "small", variant: "secondary", children: t2("actions.cancel") }) }),
          (0, import_jsx_runtime.jsx)(Button, { size: "small", type: "submit", isLoading: isPending, children: t2("actions.save") })
        ] }) })
      ]
    }
  ) });
};
var PriceListEdit = () => {
  const { t: t2 } = useTranslation();
  const { id } = useParams();
  const { price_list, isLoading, isError, error } = usePriceList(id);
  const ready = !isLoading && price_list;
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime2.jsxs)(RouteDrawer, { children: [
    (0, import_jsx_runtime2.jsx)(RouteDrawer.Header, { children: (0, import_jsx_runtime2.jsx)(Heading, { children: t2("priceLists.edit.header") }) }),
    ready && (0, import_jsx_runtime2.jsx)(PriceListEditForm, { priceList: price_list })
  ] });
};
export {
  PriceListEdit as Component
};
//# sourceMappingURL=price-list-edit-BIUM4ZK7-KARDYKAJ.js.map
