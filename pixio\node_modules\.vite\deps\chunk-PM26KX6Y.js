// node_modules/@medusajs/dashboard/dist/chunk-PXZ7QYKX.mjs
function getReturnableQuantity(item) {
  const {
    delivered_quantity,
    return_received_quantity,
    return_dismissed_quantity,
    return_requested_quantity
  } = item.detail;
  return delivered_quantity - (return_received_quantity + return_requested_quantity + return_dismissed_quantity);
}

export {
  getReturnableQuantity
};
//# sourceMappingURL=chunk-PM26KX6Y.js.map
