import {
  AvatarBox
} from "./chunk-5FT4TRK2.js";
import "./chunk-D2VV3NDE.js";
import {
  isFetchError
} from "./chunk-WTOMBGNF.js";
import "./chunk-7M4ICL3D.js";
import {
  t
} from "./chunk-WHQIBI5S.js";
import {
  objectType,
  stringType
} from "./chunk-4XXECALA.js";
import {
  useExtension
} from "./chunk-FTD3ZWHZ.js";
import {
  Form,
  useForm
} from "./chunk-XXJU43CK.js";
import "./chunk-WISME5HP.js";
import {
  useSignInWithEmailPass
} from "./chunk-WAUSOHFQ.js";
import "./chunk-3ORK3QZ4.js";
import "./chunk-3A5TVVNI.js";
import "./chunk-EZ62MM7J.js";
import "./chunk-3R6LYIXM.js";
import "./chunk-2P2O4TMQ.js";
import "./chunk-KI3HEITX.js";
import "./chunk-2VUCAO7L.js";
import "./chunk-PTIULTKD.js";
import "./chunk-KISXY2LD.js";
import "./chunk-L7C3XPHJ.js";
import "./chunk-T7MG2EIJ.js";
import "./chunk-MER4VYPR.js";
import "./chunk-2AWKOUCD.js";
import "./chunk-YL3TYBFR.js";
import "./chunk-TZWW72YW.js";
import "./chunk-MSQ25CWB.js";
import "./chunk-ONYSAQ5Z.js";
import "./chunk-NIH7SAXN.js";
import "./chunk-UEJFWAFE.js";
import "./chunk-GR55MFKT.js";
import "./chunk-AAFHKNJG.js";
import "./chunk-7XDBFDTZ.js";
import "./chunk-3JKGO5XL.js";
import "./chunk-OISUTS7G.js";
import "./chunk-AJYMIHLQ.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import "./chunk-JFCIYOH4.js";
import {
  Trans,
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-5GF3RGIE.js";
import {
  Link,
  useLocation,
  useNavigate
} from "./chunk-T7YBVUWZ.js";
import {
  Alert,
  Button,
  Heading,
  Hint,
  Input,
  Text
} from "./chunk-YP2LLXWB.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-ZSQ362B4.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/login-4HHGORJD.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var LoginSchema = objectType({
  email: stringType().email(),
  password: stringType()
});
var Login = () => {
  var _a, _b, _c, _d, _e, _f, _g;
  const { t: t2 } = useTranslation();
  const location = useLocation();
  const navigate = useNavigate();
  const { getWidgets } = useExtension();
  const from = ((_b = (_a = location.state) == null ? void 0 : _a.from) == null ? void 0 : _b.pathname) || "/orders";
  const form = useForm({
    resolver: t(LoginSchema),
    defaultValues: {
      email: "",
      password: ""
    }
  });
  const { mutateAsync, isPending } = useSignInWithEmailPass();
  const handleSubmit = form.handleSubmit(async ({ email, password }) => {
    await mutateAsync(
      {
        email,
        password
      },
      {
        onError: (error) => {
          if (isFetchError(error)) {
            if (error.status === 401) {
              form.setError("email", {
                type: "manual",
                message: error.message
              });
              return;
            }
          }
          form.setError("root.serverError", {
            type: "manual",
            message: error.message
          });
        },
        onSuccess: () => {
          navigate(from, { replace: true });
        }
      }
    );
  });
  const serverError = (_e = (_d = (_c = form.formState.errors) == null ? void 0 : _c.root) == null ? void 0 : _d.serverError) == null ? void 0 : _e.message;
  const validationError = ((_f = form.formState.errors.email) == null ? void 0 : _f.message) || ((_g = form.formState.errors.password) == null ? void 0 : _g.message);
  return (0, import_jsx_runtime.jsx)("div", { className: "bg-ui-bg-subtle flex min-h-dvh w-dvw items-center justify-center", children: (0, import_jsx_runtime.jsxs)("div", { className: "m-4 flex w-full max-w-[280px] flex-col items-center", children: [
    (0, import_jsx_runtime.jsx)(AvatarBox, {}),
    (0, import_jsx_runtime.jsxs)("div", { className: "mb-4 flex flex-col items-center", children: [
      (0, import_jsx_runtime.jsx)(Heading, { children: t2("login.title") }),
      (0, import_jsx_runtime.jsx)(Text, { size: "small", className: "text-ui-fg-subtle text-center", children: t2("login.hint") })
    ] }),
    (0, import_jsx_runtime.jsxs)("div", { className: "flex w-full flex-col gap-y-3", children: [
      getWidgets("login.before").map((Component, i) => {
        return (0, import_jsx_runtime.jsx)(Component, {}, i);
      }),
      (0, import_jsx_runtime.jsx)(Form, { ...form, children: (0, import_jsx_runtime.jsxs)(
        "form",
        {
          onSubmit: handleSubmit,
          className: "flex w-full flex-col gap-y-6",
          children: [
            (0, import_jsx_runtime.jsxs)("div", { className: "flex flex-col gap-y-1", children: [
              (0, import_jsx_runtime.jsx)(
                Form.Field,
                {
                  control: form.control,
                  name: "email",
                  render: ({ field }) => {
                    return (0, import_jsx_runtime.jsx)(Form.Item, { children: (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(
                      Input,
                      {
                        autoComplete: "email",
                        ...field,
                        className: "bg-ui-bg-field-component",
                        placeholder: t2("fields.email")
                      }
                    ) }) });
                  }
                }
              ),
              (0, import_jsx_runtime.jsx)(
                Form.Field,
                {
                  control: form.control,
                  name: "password",
                  render: ({ field }) => {
                    return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                      (0, import_jsx_runtime.jsx)(Form.Label, {}),
                      (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(
                        Input,
                        {
                          type: "password",
                          autoComplete: "current-password",
                          ...field,
                          className: "bg-ui-bg-field-component",
                          placeholder: t2("fields.password")
                        }
                      ) })
                    ] });
                  }
                }
              )
            ] }),
            validationError && (0, import_jsx_runtime.jsx)("div", { className: "text-center", children: (0, import_jsx_runtime.jsx)(Hint, { className: "inline-flex", variant: "error", children: validationError }) }),
            serverError && (0, import_jsx_runtime.jsx)(
              Alert,
              {
                className: "bg-ui-bg-base items-center p-2",
                dismissible: true,
                variant: "error",
                children: serverError
              }
            ),
            (0, import_jsx_runtime.jsx)(Button, { className: "w-full", type: "submit", isLoading: isPending, children: t2("actions.continueWithEmail") })
          ]
        }
      ) }),
      getWidgets("login.after").map((Component, i) => {
        return (0, import_jsx_runtime.jsx)(Component, {}, i);
      })
    ] }),
    (0, import_jsx_runtime.jsx)("span", { className: "text-ui-fg-muted txt-small my-6", children: (0, import_jsx_runtime.jsx)(
      Trans,
      {
        i18nKey: "login.forgotPassword",
        components: [
          (0, import_jsx_runtime.jsx)(
            Link,
            {
              to: "/reset-password",
              className: "text-ui-fg-interactive transition-fg hover:text-ui-fg-interactive-hover focus-visible:text-ui-fg-interactive-hover font-medium outline-none"
            },
            "reset-password-link"
          )
        ]
      }
    ) })
  ] }) });
};
export {
  Login as Component
};
//# sourceMappingURL=login-4HHGORJD-P7WIZIUS.js.map
