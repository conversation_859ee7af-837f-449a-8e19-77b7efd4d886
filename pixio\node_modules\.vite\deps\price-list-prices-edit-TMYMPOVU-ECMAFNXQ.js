import {
  PriceListUpdateProductsSchema,
  usePriceListCurrencyData,
  usePriceListGridColumns
} from "./chunk-AVGWDTVM.js";
import {
  isProductRow
} from "./chunk-DC7EOWEK.js";
import "./chunk-EG6IR476.js";
import {
  DataGrid
} from "./chunk-ZOK7LZDT.js";
import "./chunk-H3DTEG3J.js";
import {
  castNumber
} from "./chunk-EZLR4STK.js";
import "./chunk-EGRHWZRV.js";
import {
  KeyboundForm
} from "./chunk-DP54EP6X.js";
import "./chunk-7ANVLPZR.js";
import {
  RouteFocusModal,
  useRouteModal
} from "./chunk-MVVOBQIC.js";
import "./chunk-5QX4V4M4.js";
import {
  t
} from "./chunk-WHQIBI5S.js";
import {
  z
} from "./chunk-4XXECALA.js";
import "./chunk-6GQUHAET.js";
import "./chunk-YXT43UJF.js";
import "./chunk-66DVUN72.js";
import "./chunk-NV2N3EWM.js";
import {
  useForm
} from "./chunk-XXJU43CK.js";
import {
  useBatchPriceListPrices,
  usePriceList
} from "./chunk-MER4VYPR.js";
import "./chunk-2AWKOUCD.js";
import "./chunk-AAFHKNJG.js";
import "./chunk-7XDBFDTZ.js";
import "./chunk-3JKGO5XL.js";
import {
  useProducts
} from "./chunk-AJYMIHLQ.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import "./chunk-JFCIYOH4.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-5GF3RGIE.js";
import {
  useParams,
  useSearchParams
} from "./chunk-T7YBVUWZ.js";
import {
  Button,
  toast
} from "./chunk-YP2LLXWB.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-ZSQ362B4.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/price-list-prices-edit-TMYMPOVU.mjs
var import_react = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var PricingProductPricesSchema = z.object({
  products: PriceListUpdateProductsSchema
});
var PriceListPricesEditForm = ({
  priceList,
  products,
  regions,
  currencies,
  pricePreferences
}) => {
  const { t: t2 } = useTranslation();
  const { handleSuccess, setCloseOnEscape } = useRouteModal();
  const initialValue = (0, import_react.useRef)(initRecord(priceList, products));
  const form = useForm({
    defaultValues: {
      products: initialValue.current
    },
    resolver: t(PricingProductPricesSchema)
  });
  const { mutateAsync, isPending } = useBatchPriceListPrices(priceList.id);
  const handleSubmit = form.handleSubmit(async (values) => {
    const { products: products2 } = values;
    const { pricesToDelete, pricesToCreate, pricesToUpdate } = sortPrices(
      products2,
      initialValue.current,
      regions
    );
    mutateAsync(
      {
        delete: pricesToDelete,
        update: pricesToUpdate,
        create: pricesToCreate
      },
      {
        onSuccess: () => {
          toast.success(t2("priceLists.products.edit.successToast"));
          handleSuccess();
        },
        onError: (error) => {
          toast.error(error.message);
        }
      }
    );
  });
  const columns = usePriceListGridColumns({
    currencies,
    regions,
    pricePreferences
  });
  return (0, import_jsx_runtime.jsx)(RouteFocusModal.Form, { form, children: (0, import_jsx_runtime.jsxs)(KeyboundForm, { onSubmit: handleSubmit, className: "flex size-full flex-col", children: [
    (0, import_jsx_runtime.jsx)(RouteFocusModal.Header, {}),
    (0, import_jsx_runtime.jsx)(RouteFocusModal.Body, { className: "flex flex-col overflow-hidden", children: (0, import_jsx_runtime.jsx)(
      DataGrid,
      {
        columns,
        data: products,
        getSubRows: (row) => {
          if (isProductRow(row) && row.variants) {
            return row.variants;
          }
        },
        state: form,
        onEditingChange: (editing) => setCloseOnEscape(!editing)
      }
    ) }),
    (0, import_jsx_runtime.jsx)(RouteFocusModal.Footer, { children: (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center justify-end gap-x-2", children: [
      (0, import_jsx_runtime.jsx)(RouteFocusModal.Close, { asChild: true, children: (0, import_jsx_runtime.jsx)(Button, { size: "small", variant: "secondary", children: t2("actions.cancel") }) }),
      (0, import_jsx_runtime.jsx)(Button, { size: "small", type: "submit", isLoading: isPending, children: t2("actions.save") })
    ] }) })
  ] }) });
};
function initRecord(priceList, products) {
  var _a, _b;
  const record = {};
  const variantPrices = (_a = priceList.prices) == null ? void 0 : _a.reduce((variants, price) => {
    var _a2;
    const variantObject = variants[price.variant_id] || {};
    const isRegionPrice = !!((_a2 = price.rules) == null ? void 0 : _a2.region_id);
    if (isRegionPrice) {
      const regionId = price.rules.region_id;
      variantObject.region_prices = {
        ...variantObject.region_prices,
        [regionId]: {
          amount: price.amount.toString(),
          id: price.id
        }
      };
    } else {
      variantObject.currency_prices = {
        ...variantObject.currency_prices,
        [price.currency_code]: {
          amount: price.amount.toString(),
          id: price.id
        }
      };
    }
    variants[price.variant_id] = variantObject;
    return variants;
  }, {});
  for (const product of products) {
    record[product.id] = {
      variants: ((_b = product.variants) == null ? void 0 : _b.reduce((variants, variant) => {
        const prices = variantPrices[variant.id] || {};
        variants[variant.id] = prices;
        return variants;
      }, {})) || {}
    };
  }
  return record;
}
function convertToPriceArray(data, regions) {
  const prices = [];
  const regionCurrencyMap = regions.reduce((map, region) => {
    map[region.id] = region.currency_code;
    return map;
  }, {});
  for (const [_productId, product] of Object.entries(data || {})) {
    const { variants } = product || {};
    for (const [variantId, variant] of Object.entries(variants || {})) {
      const { currency_prices: currencyPrices, region_prices: regionPrices } = variant || {};
      for (const [currencyCode, currencyPrice] of Object.entries(
        currencyPrices || {}
      )) {
        if ((currencyPrice == null ? void 0 : currencyPrice.amount) !== "" && typeof (currencyPrice == null ? void 0 : currencyPrice.amount) !== "undefined") {
          prices.push({
            variantId,
            currencyCode,
            amount: castNumber(currencyPrice.amount),
            id: currencyPrice.id
          });
        }
      }
      for (const [regionId, regionPrice] of Object.entries(
        regionPrices || {}
      )) {
        if ((regionPrice == null ? void 0 : regionPrice.amount) !== "" && typeof (regionPrice == null ? void 0 : regionPrice.amount) !== "undefined") {
          prices.push({
            variantId,
            regionId,
            currencyCode: regionCurrencyMap[regionId],
            amount: castNumber(regionPrice.amount),
            id: regionPrice.id
          });
        }
      }
    }
  }
  return prices;
}
function createMapKey(obj) {
  return `${obj.variantId}-${obj.currencyCode}-${obj.regionId || "none"}-${obj.id || "none"}`;
}
function comparePrices(initialPrices, newPrices) {
  const pricesToUpdate = [];
  const pricesToCreate = [];
  const pricesToDelete = [];
  const initialPriceMap = initialPrices.reduce((map, price) => {
    map[createMapKey(price)] = price;
    return map;
  }, {});
  const newPriceMap = newPrices.reduce((map, price) => {
    map[createMapKey(price)] = price;
    return map;
  }, {});
  const keys = /* @__PURE__ */ new Set([
    ...Object.keys(initialPriceMap),
    ...Object.keys(newPriceMap)
  ]);
  for (const key of keys) {
    const initialPrice = initialPriceMap[key];
    const newPrice = newPriceMap[key];
    if (initialPrice && newPrice) {
      if (isNaN(newPrice.amount) && newPrice.id) {
        pricesToDelete.push(newPrice.id);
      }
      if (initialPrice.amount !== newPrice.amount && newPrice.id) {
        pricesToUpdate.push({
          id: newPrice.id,
          variant_id: newPrice.variantId,
          currency_code: newPrice.currencyCode,
          rules: newPrice.regionId ? { region_id: newPrice.regionId } : void 0,
          amount: newPrice.amount
        });
      }
    }
    if (!initialPrice && newPrice) {
      pricesToCreate.push({
        variant_id: newPrice.variantId,
        currency_code: newPrice.currencyCode,
        rules: newPrice.regionId ? { region_id: newPrice.regionId } : void 0,
        amount: newPrice.amount
      });
    }
    if (initialPrice && !newPrice && initialPrice.id) {
      pricesToDelete.push(initialPrice.id);
    }
  }
  return { pricesToDelete, pricesToCreate, pricesToUpdate };
}
function sortPrices(data, initialValue, regions) {
  const initialPrices = convertToPriceArray(initialValue, regions);
  const newPrices = convertToPriceArray(data, regions);
  return comparePrices(initialPrices, newPrices);
}
var PriceListPricesEdit = () => {
  const { id } = useParams();
  const [searchParams] = useSearchParams();
  const ids = searchParams.get("ids[]");
  const { price_list, isLoading, isError, error } = usePriceList(id);
  const productIds = ids == null ? void 0 : ids.split(",");
  const {
    products,
    isLoading: isProductsLoading,
    isError: isProductsError,
    error: productError
  } = useProducts({
    id: productIds,
    limit: (productIds == null ? void 0 : productIds.length) || 9999,
    // Temporary until we support lazy loading in the DataGrid
    price_list_id: [id],
    fields: "title,thumbnail,*variants"
  });
  const { isReady, regions, currencies, pricePreferences } = usePriceListCurrencyData();
  const ready = !isLoading && !!price_list && !isProductsLoading && !!products && isReady;
  if (isError) {
    throw error;
  }
  if (isProductsError) {
    throw productError;
  }
  return (0, import_jsx_runtime2.jsxs)(RouteFocusModal, { children: [
    (0, import_jsx_runtime2.jsx)(RouteFocusModal.Title, { asChild: true, children: (0, import_jsx_runtime2.jsxs)("span", { className: "sr-only", children: [
      "Edit Prices for ",
      price_list == null ? void 0 : price_list.title
    ] }) }),
    (0, import_jsx_runtime2.jsx)(RouteFocusModal.Description, { className: "sr-only", children: "Update prices for products in the price list" }),
    ready && (0, import_jsx_runtime2.jsx)(
      PriceListPricesEditForm,
      {
        priceList: price_list,
        products,
        regions,
        currencies,
        pricePreferences
      }
    )
  ] });
};
export {
  PriceListPricesEdit as Component
};
//# sourceMappingURL=price-list-prices-edit-TMYMPOVU-ECMAFNXQ.js.map
