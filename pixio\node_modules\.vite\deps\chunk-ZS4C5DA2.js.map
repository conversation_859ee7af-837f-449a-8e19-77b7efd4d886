{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-EA4G7XL6.mjs"], "sourcesContent": ["import {\n  formatCurrency\n} from \"./chunk-OV5NMSY6.mjs\";\nimport {\n  getOrderPaymentStatus\n} from \"./chunk-7DXVXBSA.mjs\";\nimport {\n  getLocaleAmount,\n  getStylizedAmount\n} from \"./chunk-PDWBYQOW.mjs\";\nimport {\n  ActionMenu\n} from \"./chunk-3NJTXRIY.mjs\";\nimport {\n  useCapturePayment\n} from \"./chunk-FO3VP56P.mjs\";\n\n// src/routes/orders/order-detail/components/order-payment-section/order-payment-section.tsx\nimport { ArrowDownRightMini, DocumentText, XCircle } from \"@medusajs/icons\";\nimport {\n  Badge,\n  Button,\n  Container,\n  Heading,\n  StatusBadge,\n  Text,\n  toast as toast2,\n  Tooltip as Tooltip2,\n  usePrompt\n} from \"@medusajs/ui\";\nimport { format } from \"date-fns\";\nimport { Trans, useTranslation as useTranslation2 } from \"react-i18next\";\n\n// src/components/common/display-id/display-id.tsx\nimport { useTranslation } from \"react-i18next\";\nimport { useState } from \"react\";\nimport copy from \"copy-to-clipboard\";\nimport { clx, toast, Tooltip } from \"@medusajs/ui\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nfunction DisplayId({ id, className }) {\n  const { t } = useTranslation();\n  const [open, setOpen] = useState(false);\n  const onClick = () => {\n    copy(id);\n    toast.success(t(\"actions.idCopiedToClipboard\"));\n  };\n  return /* @__PURE__ */ jsx(Tooltip, { maxWidth: 260, content: id, open, onOpenChange: setOpen, children: /* @__PURE__ */ jsxs(\"span\", { onClick, className: clx(\"cursor-pointer\", className), children: [\n    \"#\",\n    id.slice(-7)\n  ] }) });\n}\nvar display_id_default = DisplayId;\n\n// src/lib/payment.ts\nvar getTotalCaptured = (paymentCollections) => paymentCollections.reduce((acc, paymentCollection) => {\n  acc = acc + (paymentCollection.captured_amount - paymentCollection.refunded_amount);\n  return acc;\n}, 0);\nvar getTotalPending = (paymentCollections) => paymentCollections.filter((pc) => pc.status !== \"canceled\").reduce((acc, paymentCollection) => {\n  acc += paymentCollection.amount - paymentCollection.captured_amount;\n  return acc;\n}, 0);\n\n// src/routes/orders/order-detail/components/order-payment-section/order-payment-section.tsx\nimport { jsx as jsx2, jsxs as jsxs2 } from \"react/jsx-runtime\";\nvar getPaymentsFromOrder = (order) => {\n  return order.payment_collections.map((collection) => collection.payments).flat(1).filter(Boolean);\n};\nvar OrderPaymentSection = ({ order }) => {\n  const payments = getPaymentsFromOrder(order);\n  const refunds = payments.map((payment) => payment?.refunds).flat(1).filter(Boolean);\n  return /* @__PURE__ */ jsxs2(Container, { className: \"divide-y divide-dashed p-0\", children: [\n    /* @__PURE__ */ jsx2(Header, { order }),\n    /* @__PURE__ */ jsx2(\n      PaymentBreakdown,\n      {\n        order,\n        payments,\n        refunds,\n        currencyCode: order.currency_code\n      }\n    ),\n    /* @__PURE__ */ jsx2(Total, { order })\n  ] });\n};\nvar Header = ({ order }) => {\n  const { t } = useTranslation2();\n  const { label, color } = getOrderPaymentStatus(t, order.payment_status);\n  return /* @__PURE__ */ jsxs2(\"div\", { className: \"flex items-center justify-between px-6 py-4\", children: [\n    /* @__PURE__ */ jsx2(Heading, { level: \"h2\", children: t(\"orders.payment.title\") }),\n    /* @__PURE__ */ jsx2(StatusBadge, { color, className: \"text-nowrap\", children: label })\n  ] });\n};\nvar Refund = ({\n  refund,\n  currencyCode\n}) => {\n  const { t } = useTranslation2();\n  const RefundReasonBadge = refund?.refund_reason && /* @__PURE__ */ jsx2(\n    Badge,\n    {\n      size: \"2xsmall\",\n      className: \"cursor-default select-none capitalize\",\n      rounded: \"full\",\n      children: refund.refund_reason.label\n    }\n  );\n  const RefundNoteIndicator = refund.note && /* @__PURE__ */ jsx2(Tooltip2, { content: refund.note, children: /* @__PURE__ */ jsx2(DocumentText, { className: \"text-ui-tag-neutral-icon ml-1 inline\" }) });\n  return /* @__PURE__ */ jsxs2(\"div\", { className: \"bg-ui-bg-subtle text-ui-fg-subtle grid grid-cols-[1fr_1fr_1fr_20px] items-center gap-x-4 px-6 py-4\", children: [\n    /* @__PURE__ */ jsxs2(\"div\", { className: \"flex flex-row\", children: [\n      /* @__PURE__ */ jsx2(\"div\", { className: \"self-center pr-3\", children: /* @__PURE__ */ jsx2(ArrowDownRightMini, { className: \"text-ui-fg-muted\" }) }),\n      /* @__PURE__ */ jsxs2(\"div\", { children: [\n        /* @__PURE__ */ jsxs2(Text, { size: \"small\", leading: \"compact\", weight: \"plus\", children: [\n          t(\"orders.payment.refund\"),\n          \" \",\n          RefundNoteIndicator\n        ] }),\n        /* @__PURE__ */ jsx2(Text, { size: \"small\", leading: \"compact\", children: format(new Date(refund.created_at), \"dd MMM, yyyy, HH:mm:ss\") })\n      ] })\n    ] }),\n    /* @__PURE__ */ jsx2(\"div\", { className: \"flex items-center justify-end\", children: RefundReasonBadge }),\n    /* @__PURE__ */ jsx2(\"div\", { className: \"flex items-center justify-end\", children: /* @__PURE__ */ jsxs2(Text, { size: \"small\", leading: \"compact\", children: [\n      \"- \",\n      getLocaleAmount(refund.amount, currencyCode)\n    ] }) })\n  ] });\n};\nvar Payment = ({\n  order,\n  payment,\n  refunds,\n  currencyCode\n}) => {\n  const { t } = useTranslation2();\n  const prompt = usePrompt();\n  const { mutateAsync } = useCapturePayment(order.id, payment.id);\n  const handleCapture = async () => {\n    const res = await prompt({\n      title: t(\"orders.payment.capture\"),\n      description: t(\"orders.payment.capturePayment\", {\n        amount: formatCurrency(payment.amount, currencyCode)\n      }),\n      confirmText: t(\"actions.confirm\"),\n      cancelText: t(\"actions.cancel\"),\n      variant: \"confirmation\"\n    });\n    if (!res) {\n      return;\n    }\n    await mutateAsync(\n      { amount: payment.amount },\n      {\n        onSuccess: () => {\n          toast2.success(\n            t(\"orders.payment.capturePaymentSuccess\", {\n              amount: formatCurrency(payment.amount, currencyCode)\n            })\n          );\n        },\n        onError: (error) => {\n          toast2.error(error.message);\n        }\n      }\n    );\n  };\n  const getPaymentStatusAttributes = (payment2) => {\n    if (payment2.canceled_at) {\n      return [\"Canceled\", \"red\"];\n    } else if (payment2.captured_at) {\n      return [\"Captured\", \"green\"];\n    } else {\n      return [\"Pending\", \"orange\"];\n    }\n  };\n  const [status, color] = getPaymentStatusAttributes(payment);\n  const showCapture = payment.captured_at === null && payment.canceled_at === null;\n  const totalRefunded = payment.refunds.reduce(\n    (acc, next) => next.amount + acc,\n    0\n  );\n  return /* @__PURE__ */ jsxs2(\"div\", { className: \"divide-y divide-dashed\", children: [\n    /* @__PURE__ */ jsxs2(\"div\", { className: \"text-ui-fg-subtle grid grid-cols-[1fr_1fr_1fr_20px] items-center gap-x-4 px-6 py-4 sm:grid-cols-[1fr_1fr_1fr_1fr_20px]\", children: [\n      /* @__PURE__ */ jsxs2(\"div\", { className: \"w-full min-w-[60px] overflow-hidden\", children: [\n        /* @__PURE__ */ jsx2(\n          Text,\n          {\n            size: \"small\",\n            leading: \"compact\",\n            weight: \"plus\",\n            className: \"truncate\",\n            children: /* @__PURE__ */ jsx2(display_id_default, { id: payment.id })\n          }\n        ),\n        /* @__PURE__ */ jsx2(Text, { size: \"small\", leading: \"compact\", children: format(\n          new Date(payment.created_at),\n          \"dd MMM, yyyy, HH:mm:ss\"\n        ) })\n      ] }),\n      /* @__PURE__ */ jsx2(\"div\", { className: \"hidden items-center justify-end sm:flex\", children: /* @__PURE__ */ jsx2(Text, { size: \"small\", leading: \"compact\", className: \"capitalize\", children: payment.provider_id }) }),\n      /* @__PURE__ */ jsx2(\"div\", { className: \"flex items-center justify-end\", children: /* @__PURE__ */ jsx2(StatusBadge, { color, className: \"text-nowrap\", children: status }) }),\n      /* @__PURE__ */ jsx2(\"div\", { className: \"flex items-center justify-end\", children: /* @__PURE__ */ jsx2(Text, { size: \"small\", leading: \"compact\", children: getLocaleAmount(payment.amount, payment.currency_code) }) }),\n      /* @__PURE__ */ jsx2(\n        ActionMenu,\n        {\n          groups: [\n            {\n              actions: [\n                {\n                  label: t(\"orders.payment.refund\"),\n                  icon: /* @__PURE__ */ jsx2(XCircle, {}),\n                  to: `/orders/${order.id}/refund?paymentId=${payment.id}`,\n                  disabled: !payment.captured_at || !!payment.canceled_at || totalRefunded >= payment.amount\n                }\n              ]\n            }\n          ]\n        }\n      )\n    ] }),\n    showCapture && /* @__PURE__ */ jsxs2(\"div\", { className: \"bg-ui-bg-subtle flex items-center justify-between px-6 py-4\", children: [\n      /* @__PURE__ */ jsxs2(\"div\", { className: \"flex items-center gap-x-2\", children: [\n        /* @__PURE__ */ jsx2(ArrowDownRightMini, { className: \"text-ui-fg-muted shrink-0\" }),\n        /* @__PURE__ */ jsx2(Text, { size: \"small\", leading: \"compact\", children: /* @__PURE__ */ jsx2(\n          Trans,\n          {\n            i18nKey: \"orders.payment.isReadyToBeCaptured\",\n            components: [/* @__PURE__ */ jsx2(display_id_default, { id: payment.id })]\n          }\n        ) })\n      ] }),\n      /* @__PURE__ */ jsxs2(\n        Button,\n        {\n          className: \"shrink-0\",\n          size: \"small\",\n          variant: \"secondary\",\n          onClick: handleCapture,\n          children: [\n            /* @__PURE__ */ jsx2(\"span\", { className: \"hidden sm:block\", children: t(\"orders.payment.capture\") }),\n            /* @__PURE__ */ jsx2(\"span\", { className: \"sm:hidden\", children: t(\"orders.payment.capture_short\") })\n          ]\n        }\n      )\n    ] }),\n    refunds.map((refund) => /* @__PURE__ */ jsx2(Refund, { refund, currencyCode }, refund.id))\n  ] });\n};\nvar PaymentBreakdown = ({\n  order,\n  payments,\n  refunds,\n  currencyCode\n}) => {\n  const orderRefunds = refunds.filter((refund) => refund.payment_id === null);\n  const entries = [...orderRefunds, ...payments].sort((a, b) => {\n    return new Date(a.created_at).getTime() - new Date(b.created_at).getTime();\n  }).map((entry) => {\n    return {\n      event: entry,\n      type: entry.id.startsWith(\"pay_\") ? \"payment\" : \"refund\"\n    };\n  });\n  return /* @__PURE__ */ jsx2(\"div\", { className: \"flex flex-col divide-y divide-dashed\", children: entries.map(({ type, event }) => {\n    switch (type) {\n      case \"payment\":\n        return /* @__PURE__ */ jsx2(\n          Payment,\n          {\n            order,\n            payment: event,\n            refunds: refunds.filter(\n              (refund) => refund.payment_id === event.id\n            ),\n            currencyCode\n          },\n          event.id\n        );\n      case \"refund\":\n        return /* @__PURE__ */ jsx2(\n          Refund,\n          {\n            refund: event,\n            currencyCode\n          },\n          event.id\n        );\n    }\n  }) });\n};\nvar Total = ({ order }) => {\n  const { t } = useTranslation2();\n  const totalPending = getTotalPending(order.payment_collections);\n  return /* @__PURE__ */ jsxs2(\"div\", { children: [\n    /* @__PURE__ */ jsxs2(\"div\", { className: \"flex items-center justify-between px-6 py-4\", children: [\n      /* @__PURE__ */ jsx2(Text, { size: \"small\", weight: \"plus\", leading: \"compact\", children: t(\"orders.payment.totalPaidByCustomer\") }),\n      /* @__PURE__ */ jsx2(Text, { size: \"small\", weight: \"plus\", leading: \"compact\", children: getStylizedAmount(\n        getTotalCaptured(order.payment_collections),\n        order.currency_code\n      ) })\n    ] }),\n    order.status !== \"canceled\" && totalPending > 0 && /* @__PURE__ */ jsxs2(\"div\", { className: \"flex items-center justify-between px-6 py-4\", children: [\n      /* @__PURE__ */ jsx2(Text, { size: \"small\", weight: \"plus\", leading: \"compact\", children: \"Total pending\" }),\n      /* @__PURE__ */ jsx2(Text, { size: \"small\", weight: \"plus\", leading: \"compact\", children: getStylizedAmount(totalPending, order.currency_code) })\n    ] })\n  ] });\n};\n\nexport {\n  getTotalCaptured,\n  getPaymentsFromOrder,\n  OrderPaymentSection\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmCA,mBAAyB;AACzB,+BAAiB;AAEjB,yBAA0B;AA0B1B,IAAAA,sBAA2C;AAzB3C,SAAS,UAAU,EAAE,IAAI,UAAU,GAAG;AACpC,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,QAAM,CAAC,MAAM,OAAO,QAAI,uBAAS,KAAK;AACtC,QAAM,UAAU,MAAM;AACpB,iCAAAC,SAAK,EAAE;AACP,UAAM,QAAQ,EAAE,6BAA6B,CAAC;AAAA,EAChD;AACA,aAAuB,wBAAI,SAAS,EAAE,UAAU,KAAK,SAAS,IAAI,MAAM,cAAc,SAAS,cAA0B,yBAAK,QAAQ,EAAE,SAAS,WAAW,IAAI,kBAAkB,SAAS,GAAG,UAAU;AAAA,IACtM;AAAA,IACA,GAAG,MAAM,EAAE;AAAA,EACb,EAAE,CAAC,EAAE,CAAC;AACR;AACA,IAAI,qBAAqB;AAGzB,IAAI,mBAAmB,CAAC,uBAAuB,mBAAmB,OAAO,CAAC,KAAK,sBAAsB;AACnG,QAAM,OAAO,kBAAkB,kBAAkB,kBAAkB;AACnE,SAAO;AACT,GAAG,CAAC;AACJ,IAAI,kBAAkB,CAAC,uBAAuB,mBAAmB,OAAO,CAAC,OAAO,GAAG,WAAW,UAAU,EAAE,OAAO,CAAC,KAAK,sBAAsB;AAC3I,SAAO,kBAAkB,SAAS,kBAAkB;AACpD,SAAO;AACT,GAAG,CAAC;AAIJ,IAAI,uBAAuB,CAAC,UAAU;AACpC,SAAO,MAAM,oBAAoB,IAAI,CAAC,eAAe,WAAW,QAAQ,EAAE,KAAK,CAAC,EAAE,OAAO,OAAO;AAClG;AACA,IAAI,sBAAsB,CAAC,EAAE,MAAM,MAAM;AACvC,QAAM,WAAW,qBAAqB,KAAK;AAC3C,QAAM,UAAU,SAAS,IAAI,CAAC,YAAY,mCAAS,OAAO,EAAE,KAAK,CAAC,EAAE,OAAO,OAAO;AAClF,aAAuB,oBAAAC,MAAM,WAAW,EAAE,WAAW,8BAA8B,UAAU;AAAA,QAC3E,oBAAAC,KAAK,QAAQ,EAAE,MAAM,CAAC;AAAA,QACtB,oBAAAA;AAAA,MACd;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,QACA;AAAA,QACA,cAAc,MAAM;AAAA,MACtB;AAAA,IACF;AAAA,QACgB,oBAAAA,KAAK,OAAO,EAAE,MAAM,CAAC;AAAA,EACvC,EAAE,CAAC;AACL;AACA,IAAI,SAAS,CAAC,EAAE,MAAM,MAAM;AAC1B,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,QAAM,EAAE,OAAO,MAAM,IAAI,sBAAsB,GAAG,MAAM,cAAc;AACtE,aAAuB,oBAAAD,MAAM,OAAO,EAAE,WAAW,+CAA+C,UAAU;AAAA,QACxF,oBAAAC,KAAK,SAAS,EAAE,OAAO,MAAM,UAAU,EAAE,sBAAsB,EAAE,CAAC;AAAA,QAClE,oBAAAA,KAAK,aAAa,EAAE,OAAO,WAAW,eAAe,UAAU,MAAM,CAAC;AAAA,EACxF,EAAE,CAAC;AACL;AACA,IAAI,SAAS,CAAC;AAAA,EACZ;AAAA,EACA;AACF,MAAM;AACJ,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,QAAM,qBAAoB,iCAAQ,sBAAiC,oBAAAA;AAAA,IACjE;AAAA,IACA;AAAA,MACE,MAAM;AAAA,MACN,WAAW;AAAA,MACX,SAAS;AAAA,MACT,UAAU,OAAO,cAAc;AAAA,IACjC;AAAA,EACF;AACA,QAAM,sBAAsB,OAAO,YAAwB,oBAAAA,KAAK,SAAU,EAAE,SAAS,OAAO,MAAM,cAA0B,oBAAAA,KAAK,cAAc,EAAE,WAAW,uCAAuC,CAAC,EAAE,CAAC;AACvM,aAAuB,oBAAAD,MAAM,OAAO,EAAE,WAAW,sGAAsG,UAAU;AAAA,QAC/I,oBAAAA,MAAM,OAAO,EAAE,WAAW,iBAAiB,UAAU;AAAA,UACnD,oBAAAC,KAAK,OAAO,EAAE,WAAW,oBAAoB,cAA0B,oBAAAA,KAAK,oBAAoB,EAAE,WAAW,mBAAmB,CAAC,EAAE,CAAC;AAAA,UACpI,oBAAAD,MAAM,OAAO,EAAE,UAAU;AAAA,YACvB,oBAAAA,MAAM,MAAM,EAAE,MAAM,SAAS,SAAS,WAAW,QAAQ,QAAQ,UAAU;AAAA,UACzF,EAAE,uBAAuB;AAAA,UACzB;AAAA,UACA;AAAA,QACF,EAAE,CAAC;AAAA,YACa,oBAAAC,KAAK,MAAM,EAAE,MAAM,SAAS,SAAS,WAAW,UAAU,OAAO,IAAI,KAAK,OAAO,UAAU,GAAG,wBAAwB,EAAE,CAAC;AAAA,MAC3I,EAAE,CAAC;AAAA,IACL,EAAE,CAAC;AAAA,QACa,oBAAAA,KAAK,OAAO,EAAE,WAAW,iCAAiC,UAAU,kBAAkB,CAAC;AAAA,QACvF,oBAAAA,KAAK,OAAO,EAAE,WAAW,iCAAiC,cAA0B,oBAAAD,MAAM,MAAM,EAAE,MAAM,SAAS,SAAS,WAAW,UAAU;AAAA,MAC7J;AAAA,MACA,gBAAgB,OAAO,QAAQ,YAAY;AAAA,IAC7C,EAAE,CAAC,EAAE,CAAC;AAAA,EACR,EAAE,CAAC;AACL;AACA,IAAI,UAAU,CAAC;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACJ,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,QAAM,SAAS,UAAU;AACzB,QAAM,EAAE,YAAY,IAAI,kBAAkB,MAAM,IAAI,QAAQ,EAAE;AAC9D,QAAM,gBAAgB,YAAY;AAChC,UAAM,MAAM,MAAM,OAAO;AAAA,MACvB,OAAO,EAAE,wBAAwB;AAAA,MACjC,aAAa,EAAE,iCAAiC;AAAA,QAC9C,QAAQ,eAAe,QAAQ,QAAQ,YAAY;AAAA,MACrD,CAAC;AAAA,MACD,aAAa,EAAE,iBAAiB;AAAA,MAChC,YAAY,EAAE,gBAAgB;AAAA,MAC9B,SAAS;AAAA,IACX,CAAC;AACD,QAAI,CAAC,KAAK;AACR;AAAA,IACF;AACA,UAAM;AAAA,MACJ,EAAE,QAAQ,QAAQ,OAAO;AAAA,MACzB;AAAA,QACE,WAAW,MAAM;AACf,gBAAO;AAAA,YACL,EAAE,wCAAwC;AAAA,cACxC,QAAQ,eAAe,QAAQ,QAAQ,YAAY;AAAA,YACrD,CAAC;AAAA,UACH;AAAA,QACF;AAAA,QACA,SAAS,CAAC,UAAU;AAClB,gBAAO,MAAM,MAAM,OAAO;AAAA,QAC5B;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,QAAM,6BAA6B,CAAC,aAAa;AAC/C,QAAI,SAAS,aAAa;AACxB,aAAO,CAAC,YAAY,KAAK;AAAA,IAC3B,WAAW,SAAS,aAAa;AAC/B,aAAO,CAAC,YAAY,OAAO;AAAA,IAC7B,OAAO;AACL,aAAO,CAAC,WAAW,QAAQ;AAAA,IAC7B;AAAA,EACF;AACA,QAAM,CAAC,QAAQ,KAAK,IAAI,2BAA2B,OAAO;AAC1D,QAAM,cAAc,QAAQ,gBAAgB,QAAQ,QAAQ,gBAAgB;AAC5E,QAAM,gBAAgB,QAAQ,QAAQ;AAAA,IACpC,CAAC,KAAK,SAAS,KAAK,SAAS;AAAA,IAC7B;AAAA,EACF;AACA,aAAuB,oBAAAA,MAAM,OAAO,EAAE,WAAW,0BAA0B,UAAU;AAAA,QACnE,oBAAAA,MAAM,OAAO,EAAE,WAAW,0HAA0H,UAAU;AAAA,UAC5J,oBAAAA,MAAM,OAAO,EAAE,WAAW,uCAAuC,UAAU;AAAA,YACzE,oBAAAC;AAAA,UACd;AAAA,UACA;AAAA,YACE,MAAM;AAAA,YACN,SAAS;AAAA,YACT,QAAQ;AAAA,YACR,WAAW;AAAA,YACX,cAA0B,oBAAAA,KAAK,oBAAoB,EAAE,IAAI,QAAQ,GAAG,CAAC;AAAA,UACvE;AAAA,QACF;AAAA,YACgB,oBAAAA,KAAK,MAAM,EAAE,MAAM,SAAS,SAAS,WAAW,UAAU;AAAA,UACxE,IAAI,KAAK,QAAQ,UAAU;AAAA,UAC3B;AAAA,QACF,EAAE,CAAC;AAAA,MACL,EAAE,CAAC;AAAA,UACa,oBAAAA,KAAK,OAAO,EAAE,WAAW,2CAA2C,cAA0B,oBAAAA,KAAK,MAAM,EAAE,MAAM,SAAS,SAAS,WAAW,WAAW,cAAc,UAAU,QAAQ,YAAY,CAAC,EAAE,CAAC;AAAA,UACzM,oBAAAA,KAAK,OAAO,EAAE,WAAW,iCAAiC,cAA0B,oBAAAA,KAAK,aAAa,EAAE,OAAO,WAAW,eAAe,UAAU,OAAO,CAAC,EAAE,CAAC;AAAA,UAC9J,oBAAAA,KAAK,OAAO,EAAE,WAAW,iCAAiC,cAA0B,oBAAAA,KAAK,MAAM,EAAE,MAAM,SAAS,SAAS,WAAW,UAAU,gBAAgB,QAAQ,QAAQ,QAAQ,aAAa,EAAE,CAAC,EAAE,CAAC;AAAA,UACzM,oBAAAA;AAAA,QACd;AAAA,QACA;AAAA,UACE,QAAQ;AAAA,YACN;AAAA,cACE,SAAS;AAAA,gBACP;AAAA,kBACE,OAAO,EAAE,uBAAuB;AAAA,kBAChC,UAAsB,oBAAAA,KAAK,SAAS,CAAC,CAAC;AAAA,kBACtC,IAAI,WAAW,MAAM,EAAE,qBAAqB,QAAQ,EAAE;AAAA,kBACtD,UAAU,CAAC,QAAQ,eAAe,CAAC,CAAC,QAAQ,eAAe,iBAAiB,QAAQ;AAAA,gBACtF;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF,EAAE,CAAC;AAAA,IACH,mBAA+B,oBAAAD,MAAM,OAAO,EAAE,WAAW,+DAA+D,UAAU;AAAA,UAChH,oBAAAA,MAAM,OAAO,EAAE,WAAW,6BAA6B,UAAU;AAAA,YAC/D,oBAAAC,KAAK,oBAAoB,EAAE,WAAW,4BAA4B,CAAC;AAAA,YACnE,oBAAAA,KAAK,MAAM,EAAE,MAAM,SAAS,SAAS,WAAW,cAA0B,oBAAAA;AAAA,UACxF;AAAA,UACA;AAAA,YACE,SAAS;AAAA,YACT,YAAY,KAAiB,oBAAAA,KAAK,oBAAoB,EAAE,IAAI,QAAQ,GAAG,CAAC,CAAC;AAAA,UAC3E;AAAA,QACF,EAAE,CAAC;AAAA,MACL,EAAE,CAAC;AAAA,UACa,oBAAAD;AAAA,QACd;AAAA,QACA;AAAA,UACE,WAAW;AAAA,UACX,MAAM;AAAA,UACN,SAAS;AAAA,UACT,SAAS;AAAA,UACT,UAAU;AAAA,gBACQ,oBAAAC,KAAK,QAAQ,EAAE,WAAW,mBAAmB,UAAU,EAAE,wBAAwB,EAAE,CAAC;AAAA,gBACpF,oBAAAA,KAAK,QAAQ,EAAE,WAAW,aAAa,UAAU,EAAE,8BAA8B,EAAE,CAAC;AAAA,UACtG;AAAA,QACF;AAAA,MACF;AAAA,IACF,EAAE,CAAC;AAAA,IACH,QAAQ,IAAI,CAAC,eAA2B,oBAAAA,KAAK,QAAQ,EAAE,QAAQ,aAAa,GAAG,OAAO,EAAE,CAAC;AAAA,EAC3F,EAAE,CAAC;AACL;AACA,IAAI,mBAAmB,CAAC;AAAA,EACtB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACJ,QAAM,eAAe,QAAQ,OAAO,CAAC,WAAW,OAAO,eAAe,IAAI;AAC1E,QAAM,UAAU,CAAC,GAAG,cAAc,GAAG,QAAQ,EAAE,KAAK,CAAC,GAAG,MAAM;AAC5D,WAAO,IAAI,KAAK,EAAE,UAAU,EAAE,QAAQ,IAAI,IAAI,KAAK,EAAE,UAAU,EAAE,QAAQ;AAAA,EAC3E,CAAC,EAAE,IAAI,CAAC,UAAU;AAChB,WAAO;AAAA,MACL,OAAO;AAAA,MACP,MAAM,MAAM,GAAG,WAAW,MAAM,IAAI,YAAY;AAAA,IAClD;AAAA,EACF,CAAC;AACD,aAAuB,oBAAAA,KAAK,OAAO,EAAE,WAAW,wCAAwC,UAAU,QAAQ,IAAI,CAAC,EAAE,MAAM,MAAM,MAAM;AACjI,YAAQ,MAAM;AAAA,MACZ,KAAK;AACH,mBAAuB,oBAAAA;AAAA,UACrB;AAAA,UACA;AAAA,YACE;AAAA,YACA,SAAS;AAAA,YACT,SAAS,QAAQ;AAAA,cACf,CAAC,WAAW,OAAO,eAAe,MAAM;AAAA,YAC1C;AAAA,YACA;AAAA,UACF;AAAA,UACA,MAAM;AAAA,QACR;AAAA,MACF,KAAK;AACH,mBAAuB,oBAAAA;AAAA,UACrB;AAAA,UACA;AAAA,YACE,QAAQ;AAAA,YACR;AAAA,UACF;AAAA,UACA,MAAM;AAAA,QACR;AAAA,IACJ;AAAA,EACF,CAAC,EAAE,CAAC;AACN;AACA,IAAI,QAAQ,CAAC,EAAE,MAAM,MAAM;AACzB,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,QAAM,eAAe,gBAAgB,MAAM,mBAAmB;AAC9D,aAAuB,oBAAAD,MAAM,OAAO,EAAE,UAAU;AAAA,QAC9B,oBAAAA,MAAM,OAAO,EAAE,WAAW,+CAA+C,UAAU;AAAA,UACjF,oBAAAC,KAAK,MAAM,EAAE,MAAM,SAAS,QAAQ,QAAQ,SAAS,WAAW,UAAU,EAAE,oCAAoC,EAAE,CAAC;AAAA,UACnH,oBAAAA,KAAK,MAAM,EAAE,MAAM,SAAS,QAAQ,QAAQ,SAAS,WAAW,UAAU;AAAA,QACxF,iBAAiB,MAAM,mBAAmB;AAAA,QAC1C,MAAM;AAAA,MACR,EAAE,CAAC;AAAA,IACL,EAAE,CAAC;AAAA,IACH,MAAM,WAAW,cAAc,eAAe,SAAqB,oBAAAD,MAAM,OAAO,EAAE,WAAW,+CAA+C,UAAU;AAAA,UACpI,oBAAAC,KAAK,MAAM,EAAE,MAAM,SAAS,QAAQ,QAAQ,SAAS,WAAW,UAAU,gBAAgB,CAAC;AAAA,UAC3F,oBAAAA,KAAK,MAAM,EAAE,MAAM,SAAS,QAAQ,QAAQ,SAAS,WAAW,UAAU,kBAAkB,cAAc,MAAM,aAAa,EAAE,CAAC;AAAA,IAClJ,EAAE,CAAC;AAAA,EACL,EAAE,CAAC;AACL;", "names": ["import_jsx_runtime", "copy", "jsxs2", "jsx2"]}