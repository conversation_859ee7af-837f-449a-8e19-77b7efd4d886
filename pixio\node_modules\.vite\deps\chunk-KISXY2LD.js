import {
  queryClient
} from "./chunk-VMLNCWLE.js";
import {
  queryKeysFactory
} from "./chunk-CUPZIPFX.js";
import {
  sdk
} from "./chunk-JFCIYOH4.js";
import {
  useMutation,
  useQuery
} from "./chunk-R35JBZ3G.js";

// node_modules/@medusajs/dashboard/dist/chunk-RLY2SL5E.mjs
var INVITES_QUERY_KEY = "invites";
var invitesQueryKeys = queryKeysFactory(INVITES_QUERY_KEY);
var useInvites = (query, options) => {
  const { data, ...rest } = useQuery({
    queryFn: () => sdk.admin.invite.list(query),
    queryKey: invitesQueryKeys.list(query),
    ...options
  });
  return { ...data, ...rest };
};
var useCreateInvite = (options) => {
  return useMutation({
    mutationFn: (payload) => sdk.admin.invite.create(payload),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({ queryKey: invitesQueryKeys.lists() });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useResendInvite = (id, options) => {
  return useMutation({
    mutationFn: () => sdk.admin.invite.resend(id),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({ queryKey: invitesQueryKeys.lists() });
      queryClient.invalidateQueries({ queryKey: invitesQueryKeys.detail(id) });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useDeleteInvite = (id, options) => {
  return useMutation({
    mutationFn: () => sdk.admin.invite.delete(id),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({ queryKey: invitesQueryKeys.lists() });
      queryClient.invalidateQueries({ queryKey: invitesQueryKeys.detail(id) });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useAcceptInvite = (inviteToken, options) => {
  return useMutation({
    mutationFn: (payload) => {
      const { auth_token, ...rest } = payload;
      return sdk.admin.invite.accept(
        { invite_token: inviteToken, ...rest },
        {},
        {
          Authorization: `Bearer ${auth_token}`
        }
      );
    },
    onSuccess: (data, variables, context) => {
      var _a;
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};

export {
  useInvites,
  useCreateInvite,
  useResendInvite,
  useDeleteInvite,
  useAcceptInvite
};
//# sourceMappingURL=chunk-KISXY2LD.js.map
