import {
  useDeleteProductTagAction
} from "./chunk-KGZNQ46H.js";
import {
  useProductTagTableColumns
} from "./chunk-U3YCCVIX.js";
import "./chunk-J7NC22T4.js";
import "./chunk-UEYKZWIS.js";
import "./chunk-5BDJR5GO.js";
import "./chunk-IP6YNP6I.js";
import "./chunk-EHZUZSWH.js";
import {
  useProductTagTableQuery
} from "./chunk-TPBCRBDT.js";
import "./chunk-GDXEFZZY.js";
import "./chunk-GEC36FCE.js";
import "./chunk-ITWRYKT3.js";
import "./chunk-AYBSQXJR.js";
import "./chunk-ASBI7JIX.js";
import "./chunk-QG4LHCCG.js";
import "./chunk-I2ZOQM4X.js";
import "./chunk-EGZX7QZE.js";
import "./chunk-I45JH6GR.js";
import "./chunk-4FV466FW.js";
import "./chunk-QF476XOZ.js";
import "./chunk-MKZD3R7Z.js";
import "./chunk-ZJX5R5NM.js";
import "./chunk-LVAKEKGS.js";
import "./chunk-5ZQBU3TD.js";
import "./chunk-5AXVXNEZ.js";
import "./chunk-C43B7AQX.js";
import "./chunk-UDMOPZAP.js";
import "./chunk-7WCGWU4N.js";
import "./chunk-3M3PHA2D.js";
import "./chunk-NVCSASGM.js";
import "./chunk-OVCKROM5.js";
import "./chunk-EGRHWZRV.js";
import "./chunk-S5JEKJNE.js";
import {
  _DataTable,
  useDataTable
} from "./chunk-VCX3BVQR.js";
import "./chunk-VCBFQV64.js";
import "./chunk-QLJZR2JY.js";
import "./chunk-X3TOWPPJ.js";
import {
  SingleColumnPage
} from "./chunk-3LNIL4XX.js";
import "./chunk-H3DTEG3J.js";
import "./chunk-32T72GVU.js";
import {
  useProductTagTableFilters
} from "./chunk-3LRISSP5.js";
import "./chunk-P5T2IZP5.js";
import "./chunk-I7242KR3.js";
import "./chunk-EB3HY52D.js";
import "./chunk-IONS3C54.js";
import "./chunk-XNFM7P3M.js";
import "./chunk-ZIXJCBL3.js";
import "./chunk-2TO4KOWC.js";
import "./chunk-4VJTA5FM.js";
import "./chunk-MX43XOWY.js";
import "./chunk-QX6SXRUW.js";
import "./chunk-7ANVLPZR.js";
import "./chunk-2E2FUO6N.js";
import "./chunk-HPGXK5DQ.js";
import "./chunk-6GQUHAET.js";
import {
  useExtension
} from "./chunk-FTD3ZWHZ.js";
import "./chunk-YXT43UJF.js";
import {
  ActionMenu
} from "./chunk-CFRQOB2M.js";
import "./chunk-66DVUN72.js";
import "./chunk-DL4QDYPT.js";
import "./chunk-NV2N3EWM.js";
import "./chunk-BF7OBKIN.js";
import "./chunk-7UAYECTW.js";
import "./chunk-Y3NYV3NU.js";
import "./chunk-MPXR7HT5.js";
import {
  productTagsQueryKeys,
  useProductTags
} from "./chunk-WISME5HP.js";
import "./chunk-WAUSOHFQ.js";
import "./chunk-3ORK3QZ4.js";
import "./chunk-3A5TVVNI.js";
import "./chunk-EZ62MM7J.js";
import "./chunk-3R6LYIXM.js";
import "./chunk-2P2O4TMQ.js";
import "./chunk-KI3HEITX.js";
import "./chunk-2VUCAO7L.js";
import "./chunk-PTIULTKD.js";
import "./chunk-KISXY2LD.js";
import "./chunk-L7C3XPHJ.js";
import "./chunk-T7MG2EIJ.js";
import "./chunk-MER4VYPR.js";
import "./chunk-2AWKOUCD.js";
import "./chunk-YL3TYBFR.js";
import "./chunk-TZWW72YW.js";
import "./chunk-MSQ25CWB.js";
import "./chunk-ONYSAQ5Z.js";
import "./chunk-NIH7SAXN.js";
import "./chunk-UEJFWAFE.js";
import "./chunk-GR55MFKT.js";
import "./chunk-AAFHKNJG.js";
import "./chunk-7XDBFDTZ.js";
import "./chunk-3JKGO5XL.js";
import "./chunk-OISUTS7G.js";
import "./chunk-AJYMIHLQ.js";
import {
  queryClient
} from "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import {
  sdk
} from "./chunk-JFCIYOH4.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-5GF3RGIE.js";
import {
  Link,
  useLoaderData
} from "./chunk-T7YBVUWZ.js";
import {
  Button,
  Container,
  Heading,
  PencilSquare,
  Trash,
  createColumnHelper
} from "./chunk-YP2LLXWB.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-ZSQ362B4.js";
import {
  keepPreviousData
} from "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/product-tag-list-6PIJUDED.mjs
var import_react = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var productTagListQuery = (query) => ({
  queryKey: productTagsQueryKeys.list(query),
  queryFn: async () => sdk.admin.productTag.list(query)
});
var productTagListLoader = async ({ request }) => {
  const searchParams = new URL(request.url).searchParams;
  const queryObject = {};
  searchParams.forEach((value, key) => {
    try {
      queryObject[key] = JSON.parse(value);
    } catch (_e) {
      queryObject[key] = value;
    }
  });
  const query = productTagListQuery(
    queryObject
  );
  return queryClient.getQueryData(query.queryKey) ?? await queryClient.fetchQuery(query);
};
var PAGE_SIZE = 20;
var ProductTagListTable = () => {
  const { t } = useTranslation();
  const { searchParams, raw } = useProductTagTableQuery({
    pageSize: PAGE_SIZE
  });
  const initialData = useLoaderData();
  const { product_tags, count, isPending, isError, error } = useProductTags(
    searchParams,
    {
      initialData,
      placeholderData: keepPreviousData
    }
  );
  const columns = useColumns();
  const filters = useProductTagTableFilters();
  const { table } = useDataTable({
    data: product_tags,
    count,
    columns,
    getRowId: (row) => row.id,
    pageSize: PAGE_SIZE
  });
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime.jsxs)(Container, { className: "divide-y px-0 py-0", children: [
    (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center justify-between px-6 py-4", children: [
      (0, import_jsx_runtime.jsx)(Heading, { children: t("productTags.domain") }),
      (0, import_jsx_runtime.jsx)(Button, { variant: "secondary", size: "small", asChild: true, children: (0, import_jsx_runtime.jsx)(Link, { to: "create", children: t("actions.create") }) })
    ] }),
    (0, import_jsx_runtime.jsx)(
      _DataTable,
      {
        table,
        filters,
        queryObject: raw,
        isLoading: isPending,
        columns,
        pageSize: PAGE_SIZE,
        count,
        navigateTo: (row) => row.original.id,
        search: true,
        pagination: true,
        orderBy: [
          { key: "value", label: t("fields.value") },
          { key: "created_at", label: t("fields.createdAt") },
          { key: "updated_at", label: t("fields.updatedAt") }
        ]
      }
    )
  ] });
};
var ProductTagRowActions = ({
  productTag
}) => {
  const { t } = useTranslation();
  const handleDelete = useDeleteProductTagAction({ productTag });
  return (0, import_jsx_runtime.jsx)(
    ActionMenu,
    {
      groups: [
        {
          actions: [
            {
              icon: (0, import_jsx_runtime.jsx)(PencilSquare, {}),
              label: t("actions.edit"),
              to: `${productTag.id}/edit`
            }
          ]
        },
        {
          actions: [
            {
              icon: (0, import_jsx_runtime.jsx)(Trash, {}),
              label: t("actions.delete"),
              onClick: handleDelete
            }
          ]
        }
      ]
    }
  );
};
var columnHelper = createColumnHelper();
var useColumns = () => {
  const base = useProductTagTableColumns();
  return (0, import_react.useMemo)(
    () => [
      ...base,
      columnHelper.display({
        id: "actions",
        cell: ({ row }) => (0, import_jsx_runtime.jsx)(ProductTagRowActions, { productTag: row.original })
      })
    ],
    [base]
  );
};
var ProductTagList = () => {
  const { getWidgets } = useExtension();
  return (0, import_jsx_runtime2.jsx)(
    SingleColumnPage,
    {
      showMetadata: false,
      showJSON: false,
      hasOutlet: true,
      widgets: {
        after: getWidgets("product_tag.list.after"),
        before: getWidgets("product_tag.list.before")
      },
      children: (0, import_jsx_runtime2.jsx)(ProductTagListTable, {})
    }
  );
};
export {
  ProductTagList as Component,
  productTagListLoader as loader
};
//# sourceMappingURL=product-tag-list-6PIJUDED-R2HQJPRS.js.map
