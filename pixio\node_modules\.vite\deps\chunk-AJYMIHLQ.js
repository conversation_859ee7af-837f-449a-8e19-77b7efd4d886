import {
  queryClient
} from "./chunk-VMLNCWLE.js";
import {
  queryKeysFactory
} from "./chunk-CUPZIPFX.js";
import {
  sdk
} from "./chunk-JFCIYOH4.js";
import {
  useMutation,
  useQuery
} from "./chunk-R35JBZ3G.js";

// node_modules/@medusajs/dashboard/dist/chunk-6I62UDJA.mjs
var PRODUCTS_QUERY_KEY = "products";
var productsQueryKeys = queryKeysFactory(PRODUCTS_QUERY_KEY);
var VARIANTS_QUERY_KEY = "product_variants";
var variantsQueryKeys = queryKeysFactory(VARIANTS_QUERY_KEY);
var OPTIONS_QUERY_KEY = "product_options";
var optionsQueryKeys = queryKeysFactory(OPTIONS_QUERY_KEY);
var useCreateProductOption = (productId, options) => {
  return useMutation({
    mutationFn: (payload) => sdk.admin.product.createOption(productId, payload),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({ queryKey: optionsQueryKeys.lists() });
      queryClient.invalidateQueries({
        queryKey: productsQueryKeys.detail(productId)
      });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useUpdateProductOption = (productId, optionId, options) => {
  return useMutation({
    mutationFn: (payload) => sdk.admin.product.updateOption(productId, optionId, payload),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({ queryKey: optionsQueryKeys.lists() });
      queryClient.invalidateQueries({
        queryKey: optionsQueryKeys.detail(optionId)
      });
      queryClient.invalidateQueries({
        queryKey: productsQueryKeys.detail(productId)
      });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useDeleteProductOption = (productId, optionId, options) => {
  return useMutation({
    mutationFn: () => sdk.admin.product.deleteOption(productId, optionId),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({ queryKey: optionsQueryKeys.lists() });
      queryClient.invalidateQueries({
        queryKey: optionsQueryKeys.detail(optionId)
      });
      queryClient.invalidateQueries({
        queryKey: productsQueryKeys.detail(productId)
      });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useProductVariant = (productId, variantId, query, options) => {
  const { data, ...rest } = useQuery({
    queryFn: () => sdk.admin.product.retrieveVariant(productId, variantId, query),
    queryKey: variantsQueryKeys.detail(variantId, query),
    ...options
  });
  return { ...data, ...rest };
};
var useProductVariants = (productId, query, options) => {
  const { data, ...rest } = useQuery({
    queryFn: () => sdk.admin.product.listVariants(productId, query),
    queryKey: variantsQueryKeys.list({ productId, ...query }),
    ...options
  });
  return { ...data, ...rest };
};
var useCreateProductVariant = (productId, options) => {
  return useMutation({
    mutationFn: (payload) => sdk.admin.product.createVariant(productId, payload),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({ queryKey: variantsQueryKeys.lists() });
      queryClient.invalidateQueries({
        queryKey: productsQueryKeys.detail(productId)
      });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useUpdateProductVariant = (productId, variantId, options) => {
  return useMutation({
    mutationFn: (payload) => sdk.admin.product.updateVariant(productId, variantId, payload),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({ queryKey: variantsQueryKeys.lists() });
      queryClient.invalidateQueries({
        queryKey: variantsQueryKeys.detail(variantId)
      });
      queryClient.invalidateQueries({
        queryKey: productsQueryKeys.detail(productId)
      });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useUpdateProductVariantsBatch = (productId, options) => {
  return useMutation({
    mutationFn: (payload) => sdk.admin.product.batchVariants(productId, {
      update: payload
    }),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({ queryKey: variantsQueryKeys.lists() });
      queryClient.invalidateQueries({ queryKey: variantsQueryKeys.details() });
      queryClient.invalidateQueries({
        queryKey: productsQueryKeys.detail(productId)
      });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useProductVariantsInventoryItemsBatch = (productId, options) => {
  return useMutation({
    mutationFn: (payload) => sdk.admin.product.batchVariantInventoryItems(productId, payload),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({ queryKey: variantsQueryKeys.lists() });
      queryClient.invalidateQueries({ queryKey: variantsQueryKeys.details() });
      queryClient.invalidateQueries({
        queryKey: productsQueryKeys.detail(productId)
      });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useDeleteVariant = (productId, variantId, options) => {
  return useMutation({
    mutationFn: () => sdk.admin.product.deleteVariant(productId, variantId),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({ queryKey: variantsQueryKeys.lists() });
      queryClient.invalidateQueries({
        queryKey: variantsQueryKeys.detail(variantId)
      });
      queryClient.invalidateQueries({
        queryKey: productsQueryKeys.detail(productId)
      });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useDeleteVariantLazy = (productId, options) => {
  return useMutation({
    mutationFn: ({ variantId }) => sdk.admin.product.deleteVariant(productId, variantId),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({ queryKey: variantsQueryKeys.lists() });
      queryClient.invalidateQueries({
        queryKey: variantsQueryKeys.detail(variables.variantId)
      });
      queryClient.invalidateQueries({
        queryKey: productsQueryKeys.detail(productId)
      });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useProduct = (id, query, options) => {
  const { data, ...rest } = useQuery({
    queryFn: () => sdk.admin.product.retrieve(id, query),
    queryKey: productsQueryKeys.detail(id, query),
    ...options
  });
  return { ...data, ...rest };
};
var useProducts = (query, options) => {
  const { data, ...rest } = useQuery({
    queryFn: () => sdk.admin.product.list(query),
    queryKey: productsQueryKeys.list(query),
    ...options
  });
  return { ...data, ...rest };
};
var useCreateProduct = (options) => {
  return useMutation({
    mutationFn: (payload) => sdk.admin.product.create(payload),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({ queryKey: productsQueryKeys.lists() });
      queryClient.invalidateQueries({
        queryKey: inventoryItemsQueryKeys.lists()
      });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useUpdateProduct = (id, options) => {
  return useMutation({
    mutationFn: (payload) => sdk.admin.product.update(id, payload),
    onSuccess: async (data, variables, context) => {
      var _a;
      await queryClient.invalidateQueries({
        queryKey: productsQueryKeys.lists()
      });
      await queryClient.invalidateQueries({
        queryKey: productsQueryKeys.detail(id)
      });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useDeleteProduct = (id, options) => {
  return useMutation({
    mutationFn: () => sdk.admin.product.delete(id),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({ queryKey: productsQueryKeys.lists() });
      queryClient.invalidateQueries({ queryKey: productsQueryKeys.detail(id) });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useExportProducts = (query, options) => {
  return useMutation({
    mutationFn: (payload) => sdk.admin.product.export(payload, query),
    onSuccess: (data, variables, context) => {
      var _a;
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useImportProducts = (options) => {
  return useMutation({
    mutationFn: (payload) => sdk.admin.product.import(payload),
    onSuccess: (data, variables, context) => {
      var _a;
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useConfirmImportProducts = (options) => {
  return useMutation({
    mutationFn: (payload) => sdk.admin.product.confirmImport(payload),
    onSuccess: (data, variables, context) => {
      var _a;
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var INVENTORY_ITEMS_QUERY_KEY = "inventory_items";
var inventoryItemsQueryKeys = queryKeysFactory(
  INVENTORY_ITEMS_QUERY_KEY
);
var INVENTORY_ITEM_LEVELS_QUERY_KEY = "inventory_item_levels";
var inventoryItemLevelsQueryKeys = queryKeysFactory(
  INVENTORY_ITEM_LEVELS_QUERY_KEY
);
var useInventoryItems = (query, options) => {
  const { data, ...rest } = useQuery({
    queryFn: () => sdk.admin.inventoryItem.list(query),
    queryKey: inventoryItemsQueryKeys.list(query),
    ...options
  });
  return { ...data, ...rest };
};
var useInventoryItem = (id, query, options) => {
  const { data, ...rest } = useQuery({
    queryFn: () => sdk.admin.inventoryItem.retrieve(id, query),
    queryKey: inventoryItemsQueryKeys.detail(id),
    ...options
  });
  return { ...data, ...rest };
};
var useCreateInventoryItem = (options) => {
  return useMutation({
    mutationFn: (payload) => sdk.admin.inventoryItem.create(payload),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({
        queryKey: inventoryItemsQueryKeys.lists()
      });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useUpdateInventoryItem = (id, options) => {
  return useMutation({
    mutationFn: (payload) => sdk.admin.inventoryItem.update(id, payload),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({
        queryKey: inventoryItemsQueryKeys.lists()
      });
      queryClient.invalidateQueries({
        queryKey: inventoryItemsQueryKeys.detail(id)
      });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useDeleteInventoryItem = (id, options) => {
  return useMutation({
    mutationFn: () => sdk.admin.inventoryItem.delete(id),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({
        queryKey: inventoryItemsQueryKeys.lists()
      });
      queryClient.invalidateQueries({
        queryKey: inventoryItemsQueryKeys.detail(id)
      });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useDeleteInventoryItemLevel = (inventoryItemId, locationId, options) => {
  return useMutation({
    mutationFn: () => sdk.admin.inventoryItem.deleteLevel(inventoryItemId, locationId),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({
        queryKey: inventoryItemsQueryKeys.lists()
      });
      queryClient.invalidateQueries({
        queryKey: inventoryItemsQueryKeys.detail(inventoryItemId)
      });
      queryClient.invalidateQueries({
        queryKey: inventoryItemLevelsQueryKeys.detail(inventoryItemId)
      });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useInventoryItemLevels = (inventoryItemId, query, options) => {
  const { data, ...rest } = useQuery({
    queryFn: () => sdk.admin.inventoryItem.listLevels(inventoryItemId, query),
    queryKey: inventoryItemLevelsQueryKeys.detail(inventoryItemId),
    ...options
  });
  return { ...data, ...rest };
};
var useUpdateInventoryLevel = (inventoryItemId, locationId, options) => {
  return useMutation({
    mutationFn: (payload) => sdk.admin.inventoryItem.updateLevel(inventoryItemId, locationId, payload),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({
        queryKey: inventoryItemsQueryKeys.lists()
      });
      queryClient.invalidateQueries({
        queryKey: inventoryItemsQueryKeys.detail(inventoryItemId)
      });
      queryClient.invalidateQueries({
        queryKey: inventoryItemLevelsQueryKeys.detail(inventoryItemId)
      });
      queryClient.invalidateQueries({
        queryKey: variantsQueryKeys.details()
      });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useBatchInventoryItemLocationLevels = (inventoryItemId, options) => {
  return useMutation({
    mutationFn: (payload) => sdk.admin.inventoryItem.batchInventoryItemLocationLevels(
      inventoryItemId,
      payload
    ),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({
        queryKey: inventoryItemsQueryKeys.lists()
      });
      queryClient.invalidateQueries({
        queryKey: inventoryItemsQueryKeys.detail(inventoryItemId)
      });
      queryClient.invalidateQueries({
        queryKey: inventoryItemLevelsQueryKeys.detail(inventoryItemId)
      });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useBatchInventoryItemsLocationLevels = (options) => {
  return useMutation({
    mutationFn: (payload) => sdk.admin.inventoryItem.batchInventoryItemsLocationLevels(payload),
    onSuccess: (data, variables, context) => {
      var _a;
      queryClient.invalidateQueries({
        queryKey: inventoryItemsQueryKeys.all
      });
      queryClient.invalidateQueries({
        queryKey: variantsQueryKeys.lists()
      });
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};

export {
  productsQueryKeys,
  variantsQueryKeys,
  useCreateProductOption,
  useUpdateProductOption,
  useDeleteProductOption,
  useProductVariant,
  useProductVariants,
  useCreateProductVariant,
  useUpdateProductVariant,
  useUpdateProductVariantsBatch,
  useProductVariantsInventoryItemsBatch,
  useDeleteVariant,
  useDeleteVariantLazy,
  useProduct,
  useProducts,
  useCreateProduct,
  useUpdateProduct,
  useDeleteProduct,
  useExportProducts,
  useImportProducts,
  useConfirmImportProducts,
  inventoryItemsQueryKeys,
  inventoryItemLevelsQueryKeys,
  useInventoryItems,
  useInventoryItem,
  useCreateInventoryItem,
  useUpdateInventoryItem,
  useDeleteInventoryItem,
  useDeleteInventoryItemLevel,
  useInventoryItemLevels,
  useUpdateInventoryLevel,
  useBatchInventoryItemLocationLevels,
  useBatchInventoryItemsLocationLevels
};
//# sourceMappingURL=chunk-AJYMIHLQ.js.map
