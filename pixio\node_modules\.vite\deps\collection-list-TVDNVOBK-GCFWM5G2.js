import {
  useCollectionTableColumns,
  useCollectionTableQuery
} from "./chunk-TPBCRBDT.js";
import "./chunk-GDXEFZZY.js";
import "./chunk-GEC36FCE.js";
import "./chunk-ITWRYKT3.js";
import "./chunk-AYBSQXJR.js";
import "./chunk-ASBI7JIX.js";
import "./chunk-QG4LHCCG.js";
import "./chunk-I2ZOQM4X.js";
import "./chunk-I45JH6GR.js";
import "./chunk-MKZD3R7Z.js";
import "./chunk-C43B7AQX.js";
import "./chunk-3M3PHA2D.js";
import "./chunk-EGRHWZRV.js";
import "./chunk-S5JEKJNE.js";
import {
  _DataTable,
  useDataTable
} from "./chunk-VCX3BVQR.js";
import "./chunk-VCBFQV64.js";
import "./chunk-QLJZR2JY.js";
import "./chunk-X3TOWPPJ.js";
import {
  SingleColumnPage
} from "./chunk-3LNIL4XX.js";
import "./chunk-32T72GVU.js";
import {
  useCollectionTableFilters
} from "./chunk-3LRISSP5.js";
import "./chunk-P5T2IZP5.js";
import "./chunk-I7242KR3.js";
import "./chunk-EB3HY52D.js";
import "./chunk-IONS3C54.js";
import "./chunk-XNFM7P3M.js";
import "./chunk-ZIXJCBL3.js";
import "./chunk-2TO4KOWC.js";
import "./chunk-4VJTA5FM.js";
import "./chunk-MX43XOWY.js";
import "./chunk-QX6SXRUW.js";
import "./chunk-7ANVLPZR.js";
import "./chunk-2E2FUO6N.js";
import {
  useExtension
} from "./chunk-FTD3ZWHZ.js";
import "./chunk-YXT43UJF.js";
import {
  ActionMenu
} from "./chunk-CFRQOB2M.js";
import "./chunk-66DVUN72.js";
import "./chunk-DL4QDYPT.js";
import "./chunk-NV2N3EWM.js";
import "./chunk-BF7OBKIN.js";
import "./chunk-7UAYECTW.js";
import "./chunk-Y3NYV3NU.js";
import "./chunk-MPXR7HT5.js";
import "./chunk-WISME5HP.js";
import "./chunk-WAUSOHFQ.js";
import "./chunk-3ORK3QZ4.js";
import "./chunk-3A5TVVNI.js";
import "./chunk-EZ62MM7J.js";
import "./chunk-3R6LYIXM.js";
import "./chunk-2P2O4TMQ.js";
import "./chunk-KI3HEITX.js";
import "./chunk-2VUCAO7L.js";
import "./chunk-PTIULTKD.js";
import "./chunk-KISXY2LD.js";
import "./chunk-L7C3XPHJ.js";
import "./chunk-T7MG2EIJ.js";
import "./chunk-MER4VYPR.js";
import "./chunk-2AWKOUCD.js";
import {
  useCollections,
  useDeleteCollection
} from "./chunk-YL3TYBFR.js";
import "./chunk-TZWW72YW.js";
import "./chunk-MSQ25CWB.js";
import "./chunk-ONYSAQ5Z.js";
import "./chunk-NIH7SAXN.js";
import "./chunk-UEJFWAFE.js";
import "./chunk-GR55MFKT.js";
import "./chunk-AAFHKNJG.js";
import "./chunk-7XDBFDTZ.js";
import "./chunk-3JKGO5XL.js";
import "./chunk-OISUTS7G.js";
import "./chunk-AJYMIHLQ.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import "./chunk-JFCIYOH4.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-5GF3RGIE.js";
import {
  Link
} from "./chunk-T7YBVUWZ.js";
import {
  Button,
  Container,
  Heading,
  PencilSquare,
  Text,
  Trash,
  createColumnHelper,
  usePrompt
} from "./chunk-YP2LLXWB.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-ZSQ362B4.js";
import {
  keepPreviousData
} from "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/collection-list-TVDNVOBK.mjs
var import_react = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var CollectionRowActions = ({
  collection
}) => {
  const { t } = useTranslation();
  const prompt = usePrompt();
  const { mutateAsync } = useDeleteCollection(collection.id);
  const handleDeleteCollection = async () => {
    const res = await prompt({
      title: t("general.areYouSure"),
      description: t("collections.deleteWarning", {
        title: collection.title
      }),
      verificationText: collection.title,
      verificationInstruction: t("general.typeToConfirm"),
      confirmText: t("actions.delete"),
      cancelText: t("actions.cancel")
    });
    if (!res) {
      return;
    }
    await mutateAsync();
  };
  return (0, import_jsx_runtime.jsx)(
    ActionMenu,
    {
      groups: [
        {
          actions: [
            {
              label: t("actions.edit"),
              to: `/collections/${collection.id}/edit`,
              icon: (0, import_jsx_runtime.jsx)(PencilSquare, {})
            }
          ]
        },
        {
          actions: [
            {
              label: t("actions.delete"),
              onClick: handleDeleteCollection,
              icon: (0, import_jsx_runtime.jsx)(Trash, {}),
              disabled: !collection.id
            }
          ]
        }
      ]
    }
  );
};
var PAGE_SIZE = 20;
var CollectionListTable = () => {
  const { t } = useTranslation();
  const { searchParams, raw } = useCollectionTableQuery({ pageSize: PAGE_SIZE });
  const { collections, count, isError, error, isLoading } = useCollections(
    {
      ...searchParams,
      fields: "+products.id"
    },
    {
      placeholderData: keepPreviousData
    }
  );
  const filters = useCollectionTableFilters();
  const columns = useColumns();
  const { table } = useDataTable({
    data: collections ?? [],
    columns,
    count,
    enablePagination: true,
    getRowId: (row, index) => row.id ?? `${index}`,
    pageSize: PAGE_SIZE
  });
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime2.jsxs)(Container, { className: "divide-y p-0", children: [
    (0, import_jsx_runtime2.jsxs)("div", { className: "flex items-center justify-between px-6 py-4", children: [
      (0, import_jsx_runtime2.jsxs)("div", { children: [
        (0, import_jsx_runtime2.jsx)(Heading, { children: t("collections.domain") }),
        (0, import_jsx_runtime2.jsx)(Text, { className: "text-ui-fg-subtle", size: "small", children: t("collections.subtitle") })
      ] }),
      (0, import_jsx_runtime2.jsx)(Link, { to: "/collections/create", children: (0, import_jsx_runtime2.jsx)(Button, { size: "small", variant: "secondary", children: t("actions.create") }) })
    ] }),
    (0, import_jsx_runtime2.jsx)(
      _DataTable,
      {
        table,
        columns,
        pageSize: PAGE_SIZE,
        count,
        filters,
        orderBy: [
          { key: "title", label: t("fields.title") },
          { key: "handle", label: t("fields.handle") },
          { key: "created_at", label: t("fields.createdAt") },
          { key: "updated_at", label: t("fields.updatedAt") }
        ],
        search: true,
        navigateTo: (row) => `/collections/${row.original.id}`,
        queryObject: raw,
        isLoading
      }
    )
  ] });
};
var columnHelper = createColumnHelper();
var useColumns = () => {
  const base = useCollectionTableColumns();
  return (0, import_react.useMemo)(
    () => [
      ...base,
      columnHelper.display({
        id: "actions",
        cell: ({ row }) => (0, import_jsx_runtime2.jsx)(CollectionRowActions, { collection: row.original })
      })
    ],
    [base]
  );
};
var CollectionList = () => {
  const { getWidgets } = useExtension();
  return (0, import_jsx_runtime3.jsx)(
    SingleColumnPage,
    {
      widgets: {
        after: getWidgets("product_collection.list.after"),
        before: getWidgets("product_collection.list.before")
      },
      children: (0, import_jsx_runtime3.jsx)(CollectionListTable, {})
    }
  );
};
export {
  CollectionList as Component
};
//# sourceMappingURL=collection-list-TVDNVOBK-GCFWM5G2.js.map
