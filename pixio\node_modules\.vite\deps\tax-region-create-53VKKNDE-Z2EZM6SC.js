import {
  PercentageInput
} from "./chunk-LFH2BKKX.js";
import {
  formatProvider
} from "./chunk-LVAKEKGS.js";
import {
  CountrySelect
} from "./chunk-LULCCYRV.js";
import {
  useComboboxData
} from "./chunk-CFEMRZCK.js";
import {
  Combobox
} from "./chunk-RC2DY4WG.js";
import "./chunk-EGRHWZRV.js";
import {
  KeyboundForm
} from "./chunk-DP54EP6X.js";
import {
  RouteFocusModal,
  useRouteModal
} from "./chunk-MVVOBQIC.js";
import "./chunk-EGRHWZRV.js";
import "./chunk-RQF55WOK.js";
import "./chunk-5QX4V4M4.js";
import "./chunk-HPGXK5DQ.js";
import "./chunk-IA4ROPJA.js";
import "./chunk-SP3VUFZN.js";
import {
  t
} from "./chunk-WHQIBI5S.js";
import {
  z
} from "./chunk-4XXECALA.js";
import "./chunk-NV2N3EWM.js";
import {
  instance
} from "./chunk-MPXR7HT5.js";
import {
  Form,
  useForm
} from "./chunk-XXJU43CK.js";
import "./chunk-WISME5HP.js";
import "./chunk-WAUSOHFQ.js";
import "./chunk-3ORK3QZ4.js";
import "./chunk-3A5TVVNI.js";
import {
  useCreateTaxRegion
} from "./chunk-EZ62MM7J.js";
import "./chunk-3R6LYIXM.js";
import "./chunk-2P2O4TMQ.js";
import "./chunk-KI3HEITX.js";
import "./chunk-2VUCAO7L.js";
import "./chunk-PTIULTKD.js";
import "./chunk-KISXY2LD.js";
import "./chunk-L7C3XPHJ.js";
import "./chunk-T7MG2EIJ.js";
import "./chunk-MER4VYPR.js";
import "./chunk-2AWKOUCD.js";
import "./chunk-YL3TYBFR.js";
import "./chunk-TZWW72YW.js";
import "./chunk-MSQ25CWB.js";
import "./chunk-ONYSAQ5Z.js";
import "./chunk-NIH7SAXN.js";
import "./chunk-UEJFWAFE.js";
import "./chunk-GR55MFKT.js";
import "./chunk-AAFHKNJG.js";
import "./chunk-7XDBFDTZ.js";
import "./chunk-3JKGO5XL.js";
import "./chunk-OISUTS7G.js";
import "./chunk-AJYMIHLQ.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import {
  sdk
} from "./chunk-JFCIYOH4.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-5GF3RGIE.js";
import "./chunk-T7YBVUWZ.js";
import {
  Button,
  Heading,
  InformationCircleSolid,
  Input,
  Text,
  Tooltip,
  toast
} from "./chunk-YP2LLXWB.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-ZSQ362B4.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/tax-region-create-53VKKNDE.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var TaxRegionCreateSchema = z.object({
  name: z.string().optional(),
  code: z.string().optional(),
  rate: z.object({
    float: z.number().optional(),
    value: z.string().optional()
  }),
  country_code: z.string(),
  provider_id: z.string()
}).superRefine(({ provider_id, country_code }, ctx) => {
  if (!provider_id) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: instance.t("taxRegions.create.errors.missingProvider"),
      path: ["provider_id"]
    });
  }
  if (!country_code) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: instance.t("taxRegions.create.errors.missingCountry"),
      path: ["country_code"]
    });
  }
});
var TaxRegionCreateForm = ({ parentId }) => {
  const { t: t2 } = useTranslation();
  const { handleSuccess } = useRouteModal();
  const taxProviders = useComboboxData({
    queryKey: ["tax_providers"],
    queryFn: (params) => sdk.admin.taxProvider.list(params),
    getOptions: (data) => data.tax_providers.map((provider) => ({
      label: formatProvider(provider.id),
      value: provider.id
    }))
  });
  const form = useForm({
    defaultValues: {
      name: "",
      rate: {
        value: ""
      },
      code: "",
      country_code: "",
      provider_id: ""
    },
    resolver: t(TaxRegionCreateSchema)
  });
  const { mutateAsync, isPending } = useCreateTaxRegion();
  const handleSubmit = form.handleSubmit(async (values) => {
    var _a;
    const defaultRate = values.name ? {
      name: values.name,
      rate: ((_a = values.rate) == null ? void 0 : _a.value) === "" ? void 0 : parseFloat(values.rate.value),
      code: values.code
    } : void 0;
    await mutateAsync(
      {
        country_code: values.country_code,
        parent_id: parentId,
        default_tax_rate: defaultRate,
        provider_id: values.provider_id
      },
      {
        onSuccess: ({ tax_region }) => {
          toast.success(t2("taxRegions.create.successToast"));
          handleSuccess(`../${tax_region.id}`);
        },
        onError: (error) => {
          toast.error(error.message);
        }
      }
    );
  });
  return (0, import_jsx_runtime.jsx)(RouteFocusModal.Form, { form, children: (0, import_jsx_runtime.jsxs)(
    KeyboundForm,
    {
      onSubmit: handleSubmit,
      className: "flex h-full flex-col overflow-hidden",
      children: [
        (0, import_jsx_runtime.jsx)(RouteFocusModal.Header, {}),
        (0, import_jsx_runtime.jsx)(RouteFocusModal.Body, { className: "flex flex-1 flex-col overflow-hidden", children: (0, import_jsx_runtime.jsx)("div", { className: "flex flex-1 flex-col items-center overflow-y-auto", children: (0, import_jsx_runtime.jsxs)("div", { className: "flex w-full max-w-[720px] flex-col gap-y-8 px-2 py-16", children: [
          (0, import_jsx_runtime.jsxs)("div", { children: [
            (0, import_jsx_runtime.jsx)(Heading, { className: "capitalize", children: t2("taxRegions.create.header") }),
            (0, import_jsx_runtime.jsx)(Text, { size: "small", className: "text-ui-fg-subtle", children: t2("taxRegions.create.hint") })
          ] }),
          (0, import_jsx_runtime.jsx)("div", { className: "flex flex-col gap-y-4", children: (0, import_jsx_runtime.jsxs)("div", { className: "grid grid-cols-1 gap-4 md:grid-cols-2", children: [
            (0, import_jsx_runtime.jsx)(
              Form.Field,
              {
                control: form.control,
                name: "country_code",
                render: ({ field }) => {
                  return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                    (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("fields.country") }),
                    (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(CountrySelect, { ...field }) }),
                    (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                  ] });
                }
              }
            ),
            (0, import_jsx_runtime.jsx)(
              Form.Field,
              {
                control: form.control,
                name: "provider_id",
                render: ({ field }) => (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                  (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("taxRegions.fields.taxProvider") }),
                  (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(
                    Combobox,
                    {
                      ...field,
                      options: taxProviders.options,
                      searchValue: taxProviders.searchValue,
                      onSearchValueChange: taxProviders.onSearchValueChange,
                      fetchNextPage: taxProviders.fetchNextPage
                    }
                  ) }),
                  (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                ] })
              }
            )
          ] }) }),
          (0, import_jsx_runtime.jsxs)("div", { className: "flex flex-col gap-4", children: [
            (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center gap-x-1", children: [
              (0, import_jsx_runtime.jsx)(Heading, { level: "h2", className: "!txt-compact-small-plus", children: t2("taxRegions.fields.defaultTaxRate.label") }),
              (0, import_jsx_runtime.jsxs)(
                Text,
                {
                  size: "small",
                  leading: "compact",
                  className: "text-ui-fg-muted",
                  children: [
                    "(",
                    t2("fields.optional"),
                    ")"
                  ]
                }
              ),
              (0, import_jsx_runtime.jsx)(
                Tooltip,
                {
                  content: t2("taxRegions.fields.defaultTaxRate.tooltip"),
                  children: (0, import_jsx_runtime.jsx)(InformationCircleSolid, { className: "text-ui-fg-muted" })
                }
              )
            ] }),
            (0, import_jsx_runtime.jsx)("div", { className: "flex flex-col gap-y-4", children: (0, import_jsx_runtime.jsxs)("div", { className: "grid grid-cols-1 gap-4 md:grid-cols-2", children: [
              (0, import_jsx_runtime.jsx)(
                Form.Field,
                {
                  control: form.control,
                  name: "name",
                  render: ({ field }) => {
                    return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                      (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("fields.name") }),
                      (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(Input, { ...field }) }),
                      (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                    ] });
                  }
                }
              ),
              (0, import_jsx_runtime.jsx)(
                Form.Field,
                {
                  control: form.control,
                  name: "rate",
                  render: ({ field: { value, onChange, ...field } }) => {
                    return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                      (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("taxRegions.fields.taxRate") }),
                      (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(
                        PercentageInput,
                        {
                          ...field,
                          value: value == null ? void 0 : value.value,
                          onValueChange: (value2, _name, values) => onChange({
                            value: value2,
                            float: values == null ? void 0 : values.float
                          })
                        }
                      ) }),
                      (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                    ] });
                  }
                }
              ),
              (0, import_jsx_runtime.jsx)(
                Form.Field,
                {
                  control: form.control,
                  name: "code",
                  render: ({ field }) => {
                    return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                      (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("taxRegions.fields.taxCode") }),
                      (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(Input, { ...field }) }),
                      (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                    ] });
                  }
                }
              )
            ] }) })
          ] })
        ] }) }) }),
        (0, import_jsx_runtime.jsx)(RouteFocusModal.Footer, { children: (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center justify-end gap-x-2", children: [
          (0, import_jsx_runtime.jsx)(RouteFocusModal.Close, { asChild: true, children: (0, import_jsx_runtime.jsx)(Button, { size: "small", variant: "secondary", children: t2("actions.cancel") }) }),
          (0, import_jsx_runtime.jsx)(Button, { size: "small", type: "submit", isLoading: isPending, children: t2("actions.save") })
        ] }) })
      ]
    }
  ) });
};
var TaxRegionCreate = () => {
  return (0, import_jsx_runtime2.jsx)(RouteFocusModal, { children: (0, import_jsx_runtime2.jsx)(TaxRegionCreateForm, {}) });
};
export {
  TaxRegionCreate as Component,
  TaxRegionCreate
};
//# sourceMappingURL=tax-region-create-53VKKNDE-Z2EZM6SC.js.map
