import {
  sdk
} from "./chunk-JFCIYOH4.js";
import {
  useMutation
} from "./chunk-R35JBZ3G.js";

// node_modules/@medusajs/dashboard/dist/chunk-KOSCMAIC.mjs
var useSignInWithEmailPass = (options) => {
  return useMutation({
    mutationFn: (payload) => sdk.auth.login("user", "emailpass", payload),
    onSuccess: async (data, variables, context) => {
      var _a;
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useSignUpWithEmailPass = (options) => {
  return useMutation({
    mutationFn: (payload) => sdk.auth.register("user", "emailpass", payload),
    onSuccess: async (data, variables, context) => {
      var _a;
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useResetPasswordForEmailPass = (options) => {
  return useMutation({
    mutationFn: (payload) => sdk.auth.resetPassword("user", "emailpass", {
      identifier: payload.email
    }),
    onSuccess: async (data, variables, context) => {
      var _a;
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};
var useLogout = (options) => {
  return useMutation({
    mutationFn: () => sdk.auth.logout(),
    ...options
  });
};
var useUpdateProviderForEmailPass = (token, options) => {
  return useMutation({
    mutationFn: (payload) => sdk.auth.updateProvider("user", "emailpass", payload, token),
    onSuccess: async (data, variables, context) => {
      var _a;
      (_a = options == null ? void 0 : options.onSuccess) == null ? void 0 : _a.call(options, data, variables, context);
    },
    ...options
  });
};

export {
  useSignInWithEmailPass,
  useSignUpWithEmailPass,
  useResetPasswordForEmailPass,
  useLogout,
  useUpdateProviderForEmailPass
};
//# sourceMappingURL=chunk-WAUSOHFQ.js.map
