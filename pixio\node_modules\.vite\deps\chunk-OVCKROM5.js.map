{"version": 3, "sources": ["../../@medusajs/dashboard/dist/chunk-ADOCJB6L.mjs"], "sourcesContent": ["// src/components/table/table-cells/common/status-cell/status-cell.tsx\nimport { clx } from \"@medusajs/ui\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar StatusCell = ({ color, children }) => {\n  return /* @__PURE__ */ jsxs(\"div\", { className: \"txt-compact-small text-ui-fg-subtle flex h-full w-full items-center gap-x-2 overflow-hidden\", children: [\n    /* @__PURE__ */ jsx(\n      \"div\",\n      {\n        role: \"presentation\",\n        className: \"flex h-5 w-2 items-center justify-center\",\n        children: /* @__PURE__ */ jsx(\n          \"div\",\n          {\n            className: clx(\n              \"h-2 w-2 rounded-sm shadow-[0px_0px_0px_1px_rgba(0,0,0,0.12)_inset]\",\n              {\n                \"bg-ui-tag-neutral-icon\": color === \"grey\",\n                \"bg-ui-tag-green-icon\": color === \"green\",\n                \"bg-ui-tag-red-icon\": color === \"red\",\n                \"bg-ui-tag-blue-icon\": color === \"blue\",\n                \"bg-ui-tag-orange-icon\": color === \"orange\",\n                \"bg-ui-tag-purple-icon\": color === \"purple\"\n              }\n            )\n          }\n        )\n      }\n    ),\n    /* @__PURE__ */ jsx(\"span\", { className: \"truncate\", children })\n  ] });\n};\n\nexport {\n  StatusCell\n};\n"], "mappings": ";;;;;;;;;;;AAEA,yBAA0B;AAC1B,IAAI,aAAa,CAAC,EAAE,OAAO,SAAS,MAAM;AACxC,aAAuB,yBAAK,OAAO,EAAE,WAAW,+FAA+F,UAAU;AAAA,QACvI;AAAA,MACd;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,WAAW;AAAA,QACX,cAA0B;AAAA,UACxB;AAAA,UACA;AAAA,YACE,WAAW;AAAA,cACT;AAAA,cACA;AAAA,gBACE,0BAA0B,UAAU;AAAA,gBACpC,wBAAwB,UAAU;AAAA,gBAClC,sBAAsB,UAAU;AAAA,gBAChC,uBAAuB,UAAU;AAAA,gBACjC,yBAAyB,UAAU;AAAA,gBACnC,yBAAyB,UAAU;AAAA,cACrC;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,QACgB,wBAAI,QAAQ,EAAE,WAAW,YAAY,SAAS,CAAC;AAAA,EACjE,EAAE,CAAC;AACL;", "names": []}