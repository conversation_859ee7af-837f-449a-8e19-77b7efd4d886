import {
  useOrderTableColumns
} from "./chunk-4FV466FW.js";
import "./chunk-QF476XOZ.js";
import {
  useOrderTableQuery
} from "./chunk-MKZD3R7Z.js";
import "./chunk-ZJX5R5NM.js";
import "./chunk-5ZQBU3TD.js";
import "./chunk-5AXVXNEZ.js";
import "./chunk-UDMOPZAP.js";
import "./chunk-OVCKROM5.js";
import "./chunk-S5JEKJNE.js";
import {
  _DataTable,
  useDataTable
} from "./chunk-VCX3BVQR.js";
import "./chunk-VCBFQV64.js";
import "./chunk-QLJZR2JY.js";
import "./chunk-X3TOWPPJ.js";
import {
  SingleColumnPage
} from "./chunk-3LNIL4XX.js";
import "./chunk-H3DTEG3J.js";
import "./chunk-32T72GVU.js";
import {
  useOrderTableFilters
} from "./chunk-2TO4KOWC.js";
import "./chunk-MX43XOWY.js";
import "./chunk-QX6SXRUW.js";
import "./chunk-7ANVLPZR.js";
import "./chunk-2E2FUO6N.js";
import {
  useExtension
} from "./chunk-FTD3ZWHZ.js";
import "./chunk-YXT43UJF.js";
import "./chunk-DL4QDYPT.js";
import "./chunk-NV2N3EWM.js";
import "./chunk-BF7OBKIN.js";
import "./chunk-7UAYECTW.js";
import "./chunk-Y3NYV3NU.js";
import "./chunk-MPXR7HT5.js";
import {
  useOrders
} from "./chunk-NIH7SAXN.js";
import "./chunk-UEJFWAFE.js";
import "./chunk-7XDBFDTZ.js";
import "./chunk-3JKGO5XL.js";
import "./chunk-OISUTS7G.js";
import "./chunk-AJYMIHLQ.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import "./chunk-JFCIYOH4.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-5GF3RGIE.js";
import "./chunk-T7YBVUWZ.js";
import {
  Container,
  Heading
} from "./chunk-YP2LLXWB.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-ZSQ362B4.js";
import {
  keepPreviousData
} from "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/order-list-KTYCJ4NW.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var DEFAULT_PROPERTIES = [
  "id",
  "status",
  "created_at",
  "email",
  "display_id",
  "payment_status",
  "fulfillment_status",
  "total",
  "currency_code"
];
var DEFAULT_RELATIONS = ["*customer", "*sales_channel"];
var DEFAULT_FIELDS = `${DEFAULT_PROPERTIES.join(
  ","
)},${DEFAULT_RELATIONS.join(",")}`;
var PAGE_SIZE = 20;
var OrderListTable = () => {
  const { t } = useTranslation();
  const { searchParams, raw } = useOrderTableQuery({
    pageSize: PAGE_SIZE
  });
  const { orders, count, isError, error, isLoading } = useOrders(
    {
      fields: DEFAULT_FIELDS,
      ...searchParams
    },
    {
      placeholderData: keepPreviousData
    }
  );
  const filters = useOrderTableFilters();
  const columns = useOrderTableColumns({});
  const { table } = useDataTable({
    data: orders ?? [],
    columns,
    enablePagination: true,
    count,
    pageSize: PAGE_SIZE
  });
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime.jsxs)(Container, { className: "divide-y p-0", children: [
    (0, import_jsx_runtime.jsx)("div", { className: "flex items-center justify-between px-6 py-4", children: (0, import_jsx_runtime.jsx)(Heading, { children: t("orders.domain") }) }),
    (0, import_jsx_runtime.jsx)(
      _DataTable,
      {
        columns,
        table,
        pagination: true,
        navigateTo: (row) => `/orders/${row.original.id}`,
        filters,
        count,
        search: true,
        isLoading,
        pageSize: PAGE_SIZE,
        orderBy: [
          { key: "display_id", label: t("orders.fields.displayId") },
          { key: "created_at", label: t("fields.createdAt") },
          { key: "updated_at", label: t("fields.updatedAt") }
        ],
        queryObject: raw,
        noRecords: {
          message: t("orders.list.noRecordsMessage")
        }
      }
    )
  ] });
};
var OrderList = () => {
  const { getWidgets } = useExtension();
  return (0, import_jsx_runtime2.jsx)(
    SingleColumnPage,
    {
      widgets: {
        after: getWidgets("order.list.after"),
        before: getWidgets("order.list.before")
      },
      hasOutlet: false,
      children: (0, import_jsx_runtime2.jsx)(OrderListTable, {})
    }
  );
};
export {
  OrderList as Component
};
//# sourceMappingURL=order-list-KTYCJ4NW-H62NEJQC.js.map
