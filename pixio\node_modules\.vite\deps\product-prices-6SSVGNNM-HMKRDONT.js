import {
  Data<PERSON>rid,
  createD<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  createDataGridPriceColumns
} from "./chunk-ZOK7LZDT.js";
import "./chunk-H3DTEG3J.js";
import {
  castNumber
} from "./chunk-EZLR4STK.js";
import "./chunk-EGRHWZRV.js";
import {
  KeyboundForm
} from "./chunk-DP54EP6X.js";
import "./chunk-7ANVLPZR.js";
import {
  RouteFocusModal,
  useRouteModal
} from "./chunk-MVVOBQIC.js";
import "./chunk-5QX4V4M4.js";
import {
  t
} from "./chunk-WHQIBI5S.js";
import {
  arrayType,
  numberType,
  objectType,
  recordType,
  stringType
} from "./chunk-4XXECALA.js";
import "./chunk-YXT43UJF.js";
import "./chunk-66DVUN72.js";
import "./chunk-NV2N3EWM.js";
import {
  useForm,
  useWatch
} from "./chunk-XXJU43CK.js";
import {
  useStore
} from "./chunk-AAFHKNJG.js";
import {
  useRegions
} from "./chunk-7XDBFDTZ.js";
import {
  usePricePreferences
} from "./chunk-3JKGO5XL.js";
import {
  useProduct,
  useUpdateProductVariantsBatch
} from "./chunk-AJYMIHLQ.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import "./chunk-JFCIYOH4.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-5GF3RGIE.js";
import {
  useParams
} from "./chunk-T7YBVUWZ.js";
import {
  Button
} from "./chunk-YP2LLXWB.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-ZSQ362B4.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/product-prices-6SSVGNNM.mjs
var import_react = __toESM(require_react(), 1);
var import_react2 = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var VariantPricingForm = ({ form }) => {
  const { store } = useStore();
  const { regions } = useRegions({ limit: 9999 });
  const { price_preferences: pricePreferences } = usePricePreferences({});
  const { setCloseOnEscape } = useRouteModal();
  const columns = useVariantPriceGridColumns({
    currencies: store == null ? void 0 : store.supported_currencies,
    regions,
    pricePreferences
  });
  const variants = useWatch({
    control: form.control,
    name: "variants"
  });
  return (0, import_jsx_runtime.jsx)(
    DataGrid,
    {
      columns,
      data: variants,
      state: form,
      onEditingChange: (editing) => setCloseOnEscape(!editing)
    }
  );
};
var columnHelper = createDataGridHelper();
var useVariantPriceGridColumns = ({
  currencies = [],
  regions = [],
  pricePreferences = []
}) => {
  const { t: t2 } = useTranslation();
  return (0, import_react2.useMemo)(() => {
    return [
      columnHelper.column({
        id: t2("fields.title"),
        header: t2("fields.title"),
        cell: (context) => {
          const entity = context.row.original;
          return (0, import_jsx_runtime.jsx)(DataGrid.ReadonlyCell, { context, children: (0, import_jsx_runtime.jsx)("div", { className: "flex h-full w-full items-center gap-x-2 overflow-hidden", children: (0, import_jsx_runtime.jsx)("span", { className: "truncate", children: entity.title }) }) });
        },
        disableHiding: true
      }),
      ...createDataGridPriceColumns({
        currencies: currencies.map((c) => c.currency_code),
        regions,
        pricePreferences,
        getFieldName: (context, value) => {
          var _a;
          if ((_a = context.column.id) == null ? void 0 : _a.startsWith("currency_prices")) {
            return `variants.${context.row.index}.prices.${value}`;
          }
          return `variants.${context.row.index}.prices.${value}`;
        },
        t: t2
      })
    ];
  }, [t2, currencies, regions, pricePreferences]);
};
var UpdateVariantPricesSchema = objectType({
  variants: arrayType(
    objectType({
      prices: recordType(stringType(), stringType().or(numberType()).optional()).optional()
    })
  )
});
var PricingEdit = ({
  product,
  variantId
}) => {
  var _a;
  const { t: t2 } = useTranslation();
  const { handleSuccess } = useRouteModal();
  const { mutateAsync, isPending } = useUpdateProductVariantsBatch(product.id);
  const { regions } = useRegions({ limit: 9999 });
  const regionsCurrencyMap = (0, import_react.useMemo)(() => {
    if (!(regions == null ? void 0 : regions.length)) {
      return {};
    }
    return regions.reduce((acc, reg) => {
      acc[reg.id] = reg.currency_code;
      return acc;
    }, {});
  }, [regions]);
  const variants = variantId ? (_a = product.variants) == null ? void 0 : _a.filter((v) => v.id === variantId) : product.variants;
  const form = useForm({
    defaultValues: {
      variants: variants == null ? void 0 : variants.map((variant) => ({
        title: variant.title,
        prices: variant.prices.reduce((acc, price) => {
          var _a2;
          if ((_a2 = price.rules) == null ? void 0 : _a2.region_id) {
            acc[price.rules.region_id] = price.amount;
          } else {
            acc[price.currency_code] = price.amount;
          }
          return acc;
        }, {})
      }))
    },
    resolver: t(UpdateVariantPricesSchema, {})
  });
  const handleSubmit = form.handleSubmit(async (values) => {
    const reqData = values.variants.map((variant, ind) => ({
      id: variants[ind].id,
      prices: Object.entries(variant.prices || {}).filter(
        ([_, value]) => value !== "" && typeof value !== "undefined"
        // deleted cells
      ).map(([currencyCodeOrRegionId, value]) => {
        var _a2, _b, _c, _d, _e, _f;
        const regionId = currencyCodeOrRegionId.startsWith("reg_") ? currencyCodeOrRegionId : void 0;
        const currencyCode = currencyCodeOrRegionId.startsWith("reg_") ? regionsCurrencyMap[regionId] : currencyCodeOrRegionId;
        let existingId = void 0;
        if (regionId) {
          existingId = (_c = (_b = (_a2 = variants == null ? void 0 : variants[ind]) == null ? void 0 : _a2.prices) == null ? void 0 : _b.find(
            (p) => p.rules["region_id"] === regionId
          )) == null ? void 0 : _c.id;
        } else {
          existingId = (_f = (_e = (_d = variants == null ? void 0 : variants[ind]) == null ? void 0 : _d.prices) == null ? void 0 : _e.find(
            (p) => p.currency_code === currencyCode && Object.keys(p.rules ?? {}).length === 0
          )) == null ? void 0 : _f.id;
        }
        const amount = castNumber(value);
        return {
          id: existingId,
          currency_code: currencyCode,
          amount,
          ...regionId ? { rules: { region_id: regionId } } : {}
        };
      })
    }));
    await mutateAsync(reqData, {
      onSuccess: () => {
        handleSuccess("..");
      }
    });
  });
  return (0, import_jsx_runtime2.jsx)(RouteFocusModal.Form, { form, children: (0, import_jsx_runtime2.jsxs)(KeyboundForm, { onSubmit: handleSubmit, className: "flex size-full flex-col", children: [
    (0, import_jsx_runtime2.jsx)(RouteFocusModal.Header, {}),
    (0, import_jsx_runtime2.jsx)(RouteFocusModal.Body, { className: "flex flex-col overflow-hidden", children: (0, import_jsx_runtime2.jsx)(VariantPricingForm, { form }) }),
    (0, import_jsx_runtime2.jsx)(RouteFocusModal.Footer, { children: (0, import_jsx_runtime2.jsxs)("div", { className: "flex w-full items-center justify-end gap-x-2", children: [
      (0, import_jsx_runtime2.jsx)(RouteFocusModal.Close, { asChild: true, children: (0, import_jsx_runtime2.jsx)(Button, { variant: "secondary", size: "small", children: t2("actions.cancel") }) }),
      (0, import_jsx_runtime2.jsx)(
        Button,
        {
          type: "submit",
          variant: "primary",
          size: "small",
          isLoading: isPending,
          children: t2("actions.save")
        }
      )
    ] }) })
  ] }) });
};
var ProductPrices = () => {
  const { id, variant_id } = useParams();
  const { product, isLoading, isError, error } = useProduct(id);
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime3.jsx)(RouteFocusModal, { children: !isLoading && product && (0, import_jsx_runtime3.jsx)(PricingEdit, { product, variantId: variant_id }) });
};
export {
  ProductPrices as Component
};
//# sourceMappingURL=product-prices-6SSVGNNM-HMKRDONT.js.map
