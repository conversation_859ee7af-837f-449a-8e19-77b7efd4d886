import {
  ItemPlaceholder
} from "./chunk-WZHDB5JF.js";
import {
  useAddClaimInboundItems,
  useAddClaimInboundShipping,
  useAddClaimOutboundItems,
  useAddClaimOutboundShipping,
  useCancelClaimRequest,
  useClaim,
  useClaimConfirmRequest,
  useCreateClaim,
  useDeleteClaimInboundShipping,
  useDeleteClaimOutboundShipping,
  useRemoveClaimInboundItem,
  useRemoveClaimOutboundItem,
  useUpdateClaimInboundItem,
  useUpdateClaimInboundShipping,
  useUpdateClaimOutboundItems,
  useUpdateClaimOutboundShipping
} from "./chunk-UWYBVEMG.js";
import {
  OutboundShippingPlaceholder,
  ReturnShippingPlaceholder
} from "./chunk-YD5FTRQU.js";
import {
  MoneyAmountCell
} from "./chunk-5AXVXNEZ.js";
import {
  getReturnableQuantity
} from "./chunk-PM26KX6Y.js";
import {
  DEFAULT_FIELDS
} from "./chunk-UKYP5JCA.js";
import {
  useReturn,
  useUpdateReturn
} from "./chunk-YJDYMRQS.js";
import {
  getStylizedAmount
} from "./chunk-UDMOPZAP.js";
import {
  ProductCell,
  ProductHeader
} from "./chunk-NVCSASGM.js";
import "./chunk-EGRHWZRV.js";
import "./chunk-S5JEKJNE.js";
import {
  _DataTable,
  useDataTable
} from "./chunk-VCX3BVQR.js";
import "./chunk-VCBFQV64.js";
import "./chunk-QLJZR2JY.js";
import "./chunk-X3TOWPPJ.js";
import {
  currencies
} from "./chunk-H3DTEG3J.js";
import {
  Combobox
} from "./chunk-RC2DY4WG.js";
import {
  KeyboundForm
} from "./chunk-DP54EP6X.js";
import {
  useQueryParams
} from "./chunk-32T72GVU.js";
import "./chunk-MX43XOWY.js";
import "./chunk-QX6SXRUW.js";
import "./chunk-7ANVLPZR.js";
import "./chunk-2E2FUO6N.js";
import {
  RouteFocusModal,
  StackedFocusModal,
  useRouteModal,
  useStackedModal
} from "./chunk-MVVOBQIC.js";
import {
  useReturnReasons
} from "./chunk-EPRCCFRP.js";
import "./chunk-IA4ROPJA.js";
import {
  t
} from "./chunk-WHQIBI5S.js";
import {
  z
} from "./chunk-4XXECALA.js";
import {
  Thumbnail
} from "./chunk-6GQUHAET.js";
import "./chunk-YXT43UJF.js";
import {
  ActionMenu
} from "./chunk-CFRQOB2M.js";
import "./chunk-66DVUN72.js";
import "./chunk-DL4QDYPT.js";
import "./chunk-NV2N3EWM.js";
import "./chunk-BF7OBKIN.js";
import "./chunk-7UAYECTW.js";
import "./chunk-Y3NYV3NU.js";
import "./chunk-MPXR7HT5.js";
import {
  Form,
  useFieldArray,
  useForm
} from "./chunk-XXJU43CK.js";
import {
  useVariants
} from "./chunk-WISME5HP.js";
import "./chunk-WAUSOHFQ.js";
import "./chunk-3ORK3QZ4.js";
import "./chunk-3A5TVVNI.js";
import "./chunk-EZ62MM7J.js";
import "./chunk-3R6LYIXM.js";
import "./chunk-2P2O4TMQ.js";
import "./chunk-KI3HEITX.js";
import "./chunk-2VUCAO7L.js";
import "./chunk-PTIULTKD.js";
import "./chunk-KISXY2LD.js";
import "./chunk-L7C3XPHJ.js";
import "./chunk-T7MG2EIJ.js";
import "./chunk-MER4VYPR.js";
import "./chunk-2AWKOUCD.js";
import "./chunk-YL3TYBFR.js";
import "./chunk-TZWW72YW.js";
import {
  useShippingOptions
} from "./chunk-MSQ25CWB.js";
import {
  useStockLocations
} from "./chunk-ONYSAQ5Z.js";
import {
  useOrder,
  useOrderPreview
} from "./chunk-NIH7SAXN.js";
import "./chunk-UEJFWAFE.js";
import "./chunk-GR55MFKT.js";
import "./chunk-AAFHKNJG.js";
import "./chunk-7XDBFDTZ.js";
import "./chunk-3JKGO5XL.js";
import "./chunk-OISUTS7G.js";
import "./chunk-AJYMIHLQ.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import {
  sdk
} from "./chunk-JFCIYOH4.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-5GF3RGIE.js";
import {
  useNavigate,
  useParams
} from "./chunk-T7YBVUWZ.js";
import {
  Alert,
  Button,
  ChatBubble,
  Checkbox,
  CurrencyInput2 as CurrencyInput,
  DocumentText,
  Heading,
  IconButton,
  Input,
  PencilSquare,
  Switch,
  Text,
  XCircle,
  XMark,
  createColumnHelper,
  toast,
  usePrompt
} from "./chunk-YP2LLXWB.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-ZSQ362B4.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/order-create-claim-NHBXMO53.mjs
var import_react = __toESM(require_react(), 1);
var import_react2 = __toESM(require_react(), 1);
var import_react3 = __toESM(require_react(), 1);
var import_react4 = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var import_react5 = __toESM(require_react(), 1);
var import_react6 = __toESM(require_react(), 1);
var import_react7 = __toESM(require_react(), 1);
var import_jsx_runtime4 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime5 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime6 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime7 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime8 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime9 = __toESM(require_jsx_runtime(), 1);
var columnHelper = createColumnHelper();
var useClaimItemTableColumns = (currencyCode) => {
  const { t: t2 } = useTranslation();
  return (0, import_react4.useMemo)(
    () => [
      columnHelper.display({
        id: "select",
        header: ({ table }) => {
          return (0, import_jsx_runtime.jsx)(
            Checkbox,
            {
              checked: table.getIsSomePageRowsSelected() ? "indeterminate" : table.getIsAllPageRowsSelected(),
              onCheckedChange: (value) => table.toggleAllPageRowsSelected(!!value)
            }
          );
        },
        cell: ({ row }) => {
          const isSelectable = row.getCanSelect();
          return (0, import_jsx_runtime.jsx)(
            Checkbox,
            {
              disabled: !isSelectable,
              checked: row.getIsSelected(),
              onCheckedChange: (value) => row.toggleSelected(!!value),
              onClick: (e) => {
                e.stopPropagation();
              }
            }
          );
        }
      }),
      columnHelper.display({
        id: "product",
        header: () => (0, import_jsx_runtime.jsx)(ProductHeader, {}),
        cell: ({ row }) => (0, import_jsx_runtime.jsx)(
          ProductCell,
          {
            product: {
              thumbnail: row.original.thumbnail,
              title: row.original.product_title
            }
          }
        )
      }),
      columnHelper.accessor("variant.sku", {
        header: t2("fields.sku"),
        cell: ({ getValue }) => {
          return getValue() || "-";
        }
      }),
      columnHelper.accessor("variant.title", {
        header: t2("fields.variant")
      }),
      columnHelper.accessor("quantity", {
        header: () => (0, import_jsx_runtime.jsx)("div", { className: "flex size-full items-center overflow-hidden text-right", children: (0, import_jsx_runtime.jsx)("span", { className: "truncate", children: t2("fields.quantity") }) }),
        cell: ({ getValue, row }) => {
          return getReturnableQuantity(row.original);
        }
      }),
      columnHelper.accessor("refundable_total", {
        header: () => (0, import_jsx_runtime.jsx)("div", { className: "flex size-full items-center justify-end overflow-hidden text-right", children: (0, import_jsx_runtime.jsx)("span", { className: "truncate", children: t2("fields.price") }) }),
        cell: ({ getValue }) => {
          const amount = getValue() || 0;
          const stylized = getStylizedAmount(amount, currencyCode);
          return (0, import_jsx_runtime.jsx)("div", { className: "flex size-full items-center justify-end overflow-hidden text-right", children: (0, import_jsx_runtime.jsx)("span", { className: "truncate", children: stylized }) });
        }
      })
    ],
    [t2, currencyCode]
  );
};
var useClaimItemTableFilters = () => {
  const { t: t2 } = useTranslation();
  const filters = [
    {
      key: "returnable_quantity",
      label: t2("orders.returns.returnableQuantityLabel"),
      type: "number"
    },
    {
      key: "refundable_amount",
      label: t2("orders.returns.refundableAmountLabel"),
      type: "number"
    },
    {
      key: "created_at",
      label: t2("fields.createdAt"),
      type: "date"
    },
    {
      key: "updated_at",
      label: t2("fields.updatedAt"),
      type: "date"
    }
  ];
  return filters;
};
var useClaimItemTableQuery = ({
  pageSize = 50,
  prefix
}) => {
  const raw = useQueryParams(
    [
      "q",
      "offset",
      "order",
      "created_at",
      "updated_at",
      "returnable_quantity",
      "refundable_amount"
    ],
    prefix
  );
  const {
    offset,
    created_at,
    updated_at,
    refundable_amount,
    returnable_quantity,
    ...rest
  } = raw;
  const searchParams = {
    ...rest,
    limit: pageSize,
    offset: offset ? Number(offset) : 0,
    created_at: created_at ? JSON.parse(created_at) : void 0,
    updated_at: updated_at ? JSON.parse(updated_at) : void 0,
    refundable_amount: refundable_amount ? JSON.parse(refundable_amount) : void 0,
    returnable_quantity: returnable_quantity ? JSON.parse(returnable_quantity) : void 0
  };
  return { searchParams, raw };
};
var PAGE_SIZE = 50;
var PREFIX = "rit";
var AddClaimItemsTable = ({
  onSelectionChange,
  selectedItems,
  items,
  currencyCode
}) => {
  const { t: t2 } = useTranslation();
  const [rowSelection, setRowSelection] = (0, import_react3.useState)(
    selectedItems.reduce((acc, id) => {
      acc[id] = true;
      return acc;
    }, {})
  );
  const updater = (fn) => {
    const newState = typeof fn === "function" ? fn(rowSelection) : fn;
    setRowSelection(newState);
    onSelectionChange(Object.keys(newState));
  };
  const { searchParams, raw } = useClaimItemTableQuery({
    pageSize: PAGE_SIZE,
    prefix: PREFIX
  });
  const queriedItems = (0, import_react3.useMemo)(() => {
    const {
      order,
      offset,
      limit,
      q,
      created_at,
      updated_at,
      refundable_amount,
      returnable_quantity
    } = searchParams;
    let results = items;
    if (q) {
      results = results.filter((i) => {
        var _a;
        return i.product_title.toLowerCase().includes(q.toLowerCase()) || i.variant_title.toLowerCase().includes(q.toLowerCase()) || ((_a = i.variant_sku) == null ? void 0 : _a.toLowerCase().includes(q.toLowerCase()));
      });
    }
    if (order) {
      const direction = order[0] === "-" ? "desc" : "asc";
      const field = order.replace("-", "");
      results = sortItems(results, field, direction);
    }
    if (created_at) {
      results = filterByDate(results, created_at, "created_at");
    }
    if (updated_at) {
      results = filterByDate(results, updated_at, "updated_at");
    }
    if (returnable_quantity) {
      results = filterByNumber(
        results,
        returnable_quantity,
        "returnable_quantity",
        currencyCode
      );
    }
    if (refundable_amount) {
      results = filterByNumber(
        results,
        refundable_amount,
        "refundable_amount",
        currencyCode
      );
    }
    return results.slice(offset, offset + limit);
  }, [items, currencyCode, searchParams]);
  const columns = useClaimItemTableColumns(currencyCode);
  const filters = useClaimItemTableFilters();
  const { table } = useDataTable({
    data: queriedItems,
    columns,
    count: queriedItems.length,
    enablePagination: true,
    getRowId: (row) => row.id,
    pageSize: PAGE_SIZE,
    enableRowSelection: (row) => {
      return getReturnableQuantity(row.original) > 0;
    },
    rowSelection: {
      state: rowSelection,
      updater
    }
  });
  return (0, import_jsx_runtime2.jsx)("div", { className: "flex size-full flex-col overflow-hidden", children: (0, import_jsx_runtime2.jsx)(
    _DataTable,
    {
      table,
      columns,
      pageSize: PAGE_SIZE,
      count: queriedItems.length,
      filters,
      pagination: true,
      layout: "fill",
      search: true,
      orderBy: [
        { key: "product_title", label: t2("fields.product") },
        { key: "variant_title", label: t2("fields.variant") },
        { key: "sku", label: t2("fields.sku") },
        {
          key: "returnable_quantity",
          label: t2("orders.fields.returnableQuantity")
        },
        {
          key: "refundable_amount",
          label: t2("orders.fields.refundableAmount")
        }
      ],
      prefix: PREFIX,
      queryObject: raw
    }
  ) });
};
var sortItems = (items, field, direction) => {
  return items.sort((a, b) => {
    let aValue;
    let bValue;
    if (field === "product_title") {
      aValue = a.product_title;
      bValue = b.product_title;
    } else if (field === "variant_title") {
      aValue = a.variant_title;
      bValue = b.variant_title;
    } else if (field === "sku") {
      aValue = a.variant_sku;
      bValue = b.variant_sku;
    } else if (field === "returnable_quantity") {
      aValue = a.quantity - (a.returned_quantity || 0);
      bValue = b.quantity - (b.returned_quantity || 0);
    } else if (field === "refundable_amount") {
      aValue = a.refundable || 0;
      bValue = b.refundable || 0;
    }
    if (aValue < bValue) {
      return direction === "asc" ? -1 : 1;
    }
    if (aValue > bValue) {
      return direction === "asc" ? 1 : -1;
    }
    return 0;
  });
};
var filterByDate = (items, date, field) => {
  const { gt, gte, lt, lte } = date;
  return items.filter((i) => {
    const itemDate = new Date(i[field]);
    let isValid = true;
    if (gt) {
      isValid = isValid && itemDate > new Date(gt);
    }
    if (gte) {
      isValid = isValid && itemDate >= new Date(gte);
    }
    if (lt) {
      isValid = isValid && itemDate < new Date(lt);
    }
    if (lte) {
      isValid = isValid && itemDate <= new Date(lte);
    }
    return isValid;
  });
};
var defaultOperators = {
  eq: void 0,
  gt: void 0,
  gte: void 0,
  lt: void 0,
  lte: void 0
};
var filterByNumber = (items, value, field, currency_code) => {
  const { eq, gt, lt, gte, lte } = typeof value === "object" ? { ...defaultOperators, ...value } : { ...defaultOperators, eq: value };
  return items.filter((i) => {
    const returnableQuantity = i.quantity - (i.returned_quantity || 0);
    const refundableAmount = getStylizedAmount(i.refundable || 0, currency_code);
    const itemValue = field === "returnable_quantity" ? returnableQuantity : refundableAmount;
    if (eq) {
      return itemValue === eq;
    }
    let isValid = true;
    if (gt) {
      isValid = isValid && itemValue > gt;
    }
    if (gte) {
      isValid = isValid && itemValue >= gte;
    }
    if (lt) {
      isValid = isValid && itemValue < lt;
    }
    if (lte) {
      isValid = isValid && itemValue <= lte;
    }
    return isValid;
  });
};
function ClaimInboundItem({
  item,
  previewItem,
  currencyCode,
  form,
  onRemove,
  onUpdate,
  index
}) {
  const { t: t2 } = useTranslation();
  const { return_reasons = [] } = useReturnReasons({ fields: "+label" });
  const formItem = form.watch(`inbound_items.${index}`);
  const showReturnReason = typeof formItem.reason_id === "string";
  const showNote = typeof formItem.note === "string";
  return (0, import_jsx_runtime3.jsxs)("div", { className: "bg-ui-bg-subtle shadow-elevation-card-rest my-2 rounded-xl ", children: [
    (0, import_jsx_runtime3.jsxs)("div", { className: "flex flex-col items-center gap-x-2 gap-y-2 border-b p-3 text-sm md:flex-row", children: [
      (0, import_jsx_runtime3.jsxs)("div", { className: "flex flex-1 items-center gap-x-3", children: [
        (0, import_jsx_runtime3.jsx)(Thumbnail, { src: item.thumbnail }),
        (0, import_jsx_runtime3.jsxs)("div", { className: "flex flex-col", children: [
          (0, import_jsx_runtime3.jsxs)("div", { children: [
            (0, import_jsx_runtime3.jsxs)(Text, { className: "txt-small", as: "span", weight: "plus", children: [
              item.title,
              " "
            ] }),
            item.variant_sku && (0, import_jsx_runtime3.jsxs)("span", { children: [
              "(",
              item.variant_sku,
              ")"
            ] })
          ] }),
          (0, import_jsx_runtime3.jsx)(Text, { as: "div", className: "text-ui-fg-subtle txt-small", children: item.product_title })
        ] })
      ] }),
      (0, import_jsx_runtime3.jsxs)("div", { className: "flex flex-1 justify-between", children: [
        (0, import_jsx_runtime3.jsxs)("div", { className: "flex flex-grow items-center gap-2", children: [
          (0, import_jsx_runtime3.jsx)(
            Form.Field,
            {
              control: form.control,
              name: `inbound_items.${index}.quantity`,
              render: ({ field }) => {
                return (0, import_jsx_runtime3.jsxs)(Form.Item, { children: [
                  (0, import_jsx_runtime3.jsx)(Form.Control, { children: (0, import_jsx_runtime3.jsx)(
                    Input,
                    {
                      ...field,
                      className: "bg-ui-bg-base txt-small w-[67px] rounded-lg",
                      min: 1,
                      max: item.quantity,
                      type: "number",
                      onBlur: (e) => {
                        const val = e.target.value;
                        const payload = val === "" ? null : Number(val);
                        field.onChange(payload);
                        if (payload) {
                          onUpdate({ quantity: payload });
                        }
                      }
                    }
                  ) }),
                  (0, import_jsx_runtime3.jsx)(Form.ErrorMessage, {})
                ] });
              }
            }
          ),
          (0, import_jsx_runtime3.jsx)(Text, { className: "txt-small text-ui-fg-subtle", children: t2("fields.qty") })
        ] }),
        (0, import_jsx_runtime3.jsx)("div", { className: "text-ui-fg-subtle txt-small mr-2 flex flex-shrink-0", children: (0, import_jsx_runtime3.jsx)(
          MoneyAmountCell,
          {
            currencyCode,
            amount: previewItem.return_requested_total
          }
        ) }),
        (0, import_jsx_runtime3.jsx)(
          ActionMenu,
          {
            groups: [
              {
                actions: [
                  !showReturnReason && {
                    label: t2("actions.addReason"),
                    onClick: () => form.setValue(`inbound_items.${index}.reason_id`, ""),
                    icon: (0, import_jsx_runtime3.jsx)(ChatBubble, {})
                  },
                  !showNote && {
                    label: t2("actions.addNote"),
                    onClick: () => form.setValue(`inbound_items.${index}.note`, ""),
                    icon: (0, import_jsx_runtime3.jsx)(DocumentText, {})
                  },
                  {
                    label: t2("actions.remove"),
                    onClick: onRemove,
                    icon: (0, import_jsx_runtime3.jsx)(XCircle, {})
                  }
                ].filter(Boolean)
              }
            ]
          }
        )
      ] })
    ] }),
    (0, import_jsx_runtime3.jsxs)(import_jsx_runtime3.Fragment, { children: [
      showReturnReason && (0, import_jsx_runtime3.jsxs)("div", { className: "grid grid-cols-1 gap-2 p-3 md:grid-cols-2", children: [
        (0, import_jsx_runtime3.jsxs)("div", { children: [
          (0, import_jsx_runtime3.jsx)(Form.Label, { children: t2("orders.returns.reason") }),
          (0, import_jsx_runtime3.jsx)(Form.Hint, { className: "!mt-1", children: t2("orders.returns.reasonHint") })
        ] }),
        (0, import_jsx_runtime3.jsxs)("div", { className: "flex items-center gap-1", children: [
          (0, import_jsx_runtime3.jsx)("div", { className: "flex-grow", children: (0, import_jsx_runtime3.jsx)(
            Form.Field,
            {
              control: form.control,
              name: `inbound_items.${index}.reason_id`,
              render: ({ field: { ref, value, onChange, ...field } }) => {
                return (0, import_jsx_runtime3.jsxs)(Form.Item, { children: [
                  (0, import_jsx_runtime3.jsx)(Form.Control, { children: (0, import_jsx_runtime3.jsx)(
                    Combobox,
                    {
                      className: "bg-ui-bg-field-component hover:bg-ui-bg-field-component-hover",
                      value,
                      onChange: (v) => {
                        onUpdate({ reason_id: v });
                        onChange(v);
                      },
                      ...field,
                      options: return_reasons.map((reason) => ({
                        label: reason.label,
                        value: reason.id
                      }))
                    }
                  ) }),
                  (0, import_jsx_runtime3.jsx)(Form.ErrorMessage, {})
                ] });
              }
            }
          ) }),
          (0, import_jsx_runtime3.jsx)(
            IconButton,
            {
              type: "button",
              className: "flex-shrink",
              variant: "transparent",
              onClick: () => {
                form.setValue(`inbound_items.${index}.reason_id`, null);
                onUpdate({ reason_id: null });
              },
              children: (0, import_jsx_runtime3.jsx)(XMark, { className: "text-ui-fg-muted" })
            }
          )
        ] })
      ] }),
      showNote && (0, import_jsx_runtime3.jsxs)("div", { className: "grid grid-cols-1 gap-2 p-3 md:grid-cols-2", children: [
        (0, import_jsx_runtime3.jsxs)("div", { children: [
          (0, import_jsx_runtime3.jsx)(Form.Label, { children: t2("orders.returns.note") }),
          (0, import_jsx_runtime3.jsx)(Form.Hint, { className: "!mt-1", children: t2("orders.returns.noteHint") })
        ] }),
        (0, import_jsx_runtime3.jsxs)("div", { className: "flex items-center gap-1", children: [
          (0, import_jsx_runtime3.jsx)("div", { className: "flex-grow", children: (0, import_jsx_runtime3.jsx)(
            Form.Field,
            {
              control: form.control,
              name: `inbound_items.${index}.note`,
              render: ({ field: { ref, ...field } }) => {
                return (0, import_jsx_runtime3.jsxs)(Form.Item, { children: [
                  (0, import_jsx_runtime3.jsx)(Form.Control, { children: (0, import_jsx_runtime3.jsx)(
                    Input,
                    {
                      ...field,
                      onBlur: () => {
                        field.onChange(field.value);
                        onUpdate({ internal_note: field.value });
                      },
                      className: "bg-ui-bg-field-component hover:bg-ui-bg-field-component-hover"
                    }
                  ) }),
                  (0, import_jsx_runtime3.jsx)(Form.ErrorMessage, {})
                ] });
              }
            }
          ) }),
          (0, import_jsx_runtime3.jsx)(
            IconButton,
            {
              type: "button",
              className: "flex-shrink",
              variant: "transparent",
              onClick: () => {
                form.setValue(`inbound_items.${index}.note`, null);
                onUpdate({ internal_note: null });
              },
              children: (0, import_jsx_runtime3.jsx)(XMark, { className: "text-ui-fg-muted" })
            }
          )
        ] })
      ] })
    ] })
  ] });
}
var ClaimCreateSchema = z.object({
  inbound_items: z.array(
    z.object({
      item_id: z.string(),
      quantity: z.number(),
      reason_id: z.string().nullish(),
      note: z.string().nullish()
    })
  ),
  outbound_items: z.array(
    z.object({
      item_id: z.string(),
      // TODO: variant id?
      quantity: z.number()
    })
  ),
  location_id: z.string().optional(),
  inbound_option_id: z.string().nullish(),
  outbound_option_id: z.string().nullish(),
  send_notification: z.boolean().optional()
});
var columnHelper2 = createColumnHelper();
var useClaimOutboundItemTableColumns = (currencyCode) => {
  const { t: t2 } = useTranslation();
  return (0, import_react7.useMemo)(
    () => [
      columnHelper2.display({
        id: "select",
        header: ({ table }) => {
          return (0, import_jsx_runtime4.jsx)(
            Checkbox,
            {
              checked: table.getIsSomePageRowsSelected() ? "indeterminate" : table.getIsAllPageRowsSelected(),
              onCheckedChange: (value) => table.toggleAllPageRowsSelected(!!value)
            }
          );
        },
        cell: ({ row }) => {
          const isSelectable = row.getCanSelect();
          return (0, import_jsx_runtime4.jsx)(
            Checkbox,
            {
              disabled: !isSelectable,
              checked: row.getIsSelected(),
              onCheckedChange: (value) => row.toggleSelected(!!value),
              onClick: (e) => {
                e.stopPropagation();
              }
            }
          );
        }
      }),
      columnHelper2.display({
        id: "product",
        header: () => (0, import_jsx_runtime4.jsx)(ProductHeader, {}),
        cell: ({ row }) => {
          return (0, import_jsx_runtime4.jsx)(ProductCell, { product: row.original.product });
        }
      }),
      columnHelper2.accessor("sku", {
        header: t2("fields.sku"),
        cell: ({ getValue }) => {
          return getValue() || "-";
        }
      }),
      columnHelper2.accessor("title", {
        header: t2("fields.title")
      })
    ],
    [t2, currencyCode]
  );
};
var useClaimOutboundItemTableFilters = () => {
  const { t: t2 } = useTranslation();
  const filters = [
    {
      key: "created_at",
      label: t2("fields.createdAt"),
      type: "date"
    },
    {
      key: "updated_at",
      label: t2("fields.updatedAt"),
      type: "date"
    }
  ];
  return filters;
};
var useClaimOutboundItemTableQuery = ({
  pageSize = 50,
  prefix
}) => {
  const raw = useQueryParams(
    ["q", "offset", "order", "created_at", "updated_at"],
    prefix
  );
  const { offset, created_at, updated_at, ...rest } = raw;
  const searchParams = {
    ...rest,
    limit: pageSize,
    offset: offset ? Number(offset) : 0,
    created_at: created_at ? JSON.parse(created_at) : void 0,
    updated_at: updated_at ? JSON.parse(updated_at) : void 0
  };
  return { searchParams, raw };
};
var PAGE_SIZE2 = 50;
var PREFIX2 = "rit";
var AddClaimOutboundItemsTable = ({
  onSelectionChange,
  selectedItems,
  currencyCode
}) => {
  const { t: t2 } = useTranslation();
  const [rowSelection, setRowSelection] = (0, import_react6.useState)(
    selectedItems.reduce((acc, id) => {
      acc[id] = true;
      return acc;
    }, {})
  );
  const updater = (fn) => {
    const newState = typeof fn === "function" ? fn(rowSelection) : fn;
    setRowSelection(newState);
    onSelectionChange(Object.keys(newState));
  };
  const { searchParams, raw } = useClaimOutboundItemTableQuery({
    pageSize: PAGE_SIZE2,
    prefix: PREFIX2
  });
  const { variants = [], count } = useVariants({
    ...searchParams,
    fields: "*inventory_items.inventory.location_levels,+inventory_quantity"
  });
  const columns = useClaimOutboundItemTableColumns(currencyCode);
  const filters = useClaimOutboundItemTableFilters();
  const { table } = useDataTable({
    data: variants,
    columns,
    count,
    enablePagination: true,
    getRowId: (row) => row.id,
    pageSize: PAGE_SIZE2,
    enableRowSelection: (_row) => {
      return true;
    },
    rowSelection: {
      state: rowSelection,
      updater
    }
  });
  return (0, import_jsx_runtime5.jsx)("div", { className: "flex size-full flex-col overflow-hidden", children: (0, import_jsx_runtime5.jsx)(
    _DataTable,
    {
      table,
      columns,
      pageSize: PAGE_SIZE2,
      count,
      filters,
      pagination: true,
      layout: "fill",
      search: true,
      orderBy: [
        { key: "product_id", label: t2("fields.product") },
        { key: "title", label: t2("fields.title") },
        { key: "sku", label: t2("fields.sku") }
      ],
      prefix: PREFIX2,
      queryObject: raw
    }
  ) });
};
function ClaimOutboundItem({
  previewItem,
  currencyCode,
  form,
  onRemove,
  onUpdate,
  index
}) {
  const { t: t2 } = useTranslation();
  return (0, import_jsx_runtime6.jsx)("div", { className: "bg-ui-bg-subtle shadow-elevation-card-rest my-2 rounded-xl ", children: (0, import_jsx_runtime6.jsxs)("div", { className: "flex flex-col items-center gap-x-2 gap-y-2 border-b p-3 text-sm md:flex-row", children: [
    (0, import_jsx_runtime6.jsxs)("div", { className: "flex flex-1 items-center gap-x-3", children: [
      (0, import_jsx_runtime6.jsx)(Thumbnail, { src: previewItem.thumbnail }),
      (0, import_jsx_runtime6.jsxs)("div", { className: "flex flex-col", children: [
        (0, import_jsx_runtime6.jsxs)("div", { children: [
          (0, import_jsx_runtime6.jsxs)(Text, { className: "txt-small", as: "span", weight: "plus", children: [
            previewItem.title,
            " "
          ] }),
          previewItem.variant_sku && (0, import_jsx_runtime6.jsxs)("span", { children: [
            "(",
            previewItem.variant_sku,
            ")"
          ] })
        ] }),
        (0, import_jsx_runtime6.jsx)(Text, { as: "div", className: "text-ui-fg-subtle txt-small", children: previewItem.subtitle })
      ] })
    ] }),
    (0, import_jsx_runtime6.jsxs)("div", { className: "flex flex-1 justify-between", children: [
      (0, import_jsx_runtime6.jsxs)("div", { className: "flex flex-grow items-center gap-2", children: [
        (0, import_jsx_runtime6.jsx)(
          Form.Field,
          {
            control: form.control,
            name: `outbound_items.${index}.quantity`,
            render: ({ field }) => {
              return (0, import_jsx_runtime6.jsxs)(Form.Item, { children: [
                (0, import_jsx_runtime6.jsx)(Form.Control, { children: (0, import_jsx_runtime6.jsx)(
                  Input,
                  {
                    ...field,
                    className: "bg-ui-bg-base txt-small w-[67px] rounded-lg",
                    min: 1,
                    type: "number",
                    onBlur: (e) => {
                      const val = e.target.value;
                      const payload = val === "" ? null : Number(val);
                      field.onChange(payload);
                      if (payload) {
                        onUpdate({ quantity: payload });
                      }
                    }
                  }
                ) }),
                (0, import_jsx_runtime6.jsx)(Form.ErrorMessage, {})
              ] });
            }
          }
        ),
        (0, import_jsx_runtime6.jsx)(Text, { className: "txt-small text-ui-fg-subtle", children: t2("fields.qty") })
      ] }),
      (0, import_jsx_runtime6.jsx)("div", { className: "text-ui-fg-subtle txt-small mr-2 flex flex-shrink-0", children: (0, import_jsx_runtime6.jsx)(
        MoneyAmountCell,
        {
          currencyCode,
          amount: previewItem.total
        }
      ) }),
      (0, import_jsx_runtime6.jsx)(
        ActionMenu,
        {
          groups: [
            {
              actions: [
                {
                  label: t2("actions.remove"),
                  onClick: onRemove,
                  icon: (0, import_jsx_runtime6.jsx)(XCircle, {})
                }
              ].filter(Boolean)
            }
          ]
        }
      )
    ] })
  ] }) });
}
var itemsToAdd = [];
var itemsToRemove = [];
var ClaimOutboundSection = ({
  order,
  preview,
  claim,
  form
}) => {
  const { t: t2 } = useTranslation();
  const { setIsOpen } = useStackedModal();
  const [inventoryMap, setInventoryMap] = (0, import_react5.useState)({});
  const { shipping_options = [] } = useShippingOptions({
    limit: 999,
    fields: "*prices,+service_zone.fulfillment_set.location.id"
  });
  const outboundShippingOptions = shipping_options.filter(
    (shippingOption) => !!shippingOption.rules.find(
      (r) => r.attribute === "is_return" && r.value === "false"
    )
  );
  const { mutateAsync: addOutboundShipping } = useAddClaimOutboundShipping(
    claim.id,
    order.id
  );
  const { mutateAsync: deleteOutboundShipping } = useDeleteClaimOutboundShipping(claim.id, order.id);
  const { mutateAsync: addOutboundItem } = useAddClaimOutboundItems(
    claim.id,
    order.id
  );
  const { mutateAsync: updateOutboundItem } = useUpdateClaimOutboundItems(
    claim.id,
    order.id
  );
  const { mutateAsync: removeOutboundItem } = useRemoveClaimOutboundItem(
    claim.id,
    order.id
  );
  const previewOutboundItems = (0, import_react5.useMemo)(
    () => {
      var _a;
      return (_a = preview == null ? void 0 : preview.items) == null ? void 0 : _a.filter(
        (i) => {
          var _a2;
          return !!((_a2 = i.actions) == null ? void 0 : _a2.find(
            (a) => a.claim_id === claim.id && a.action === "ITEM_ADD"
          ));
        }
      );
    },
    [preview.items]
  );
  const variantItemMap = (0, import_react5.useMemo)(
    () => {
      var _a;
      return new Map((_a = order == null ? void 0 : order.items) == null ? void 0 : _a.map((i) => [i.variant_id, i]));
    },
    [order.items]
  );
  const {
    fields: outboundItems,
    append,
    remove,
    update
  } = useFieldArray({
    name: "outbound_items",
    control: form.control
  });
  const variantOutboundMap = (0, import_react5.useMemo)(
    () => new Map(previewOutboundItems.map((i) => [i.variant_id, i])),
    [previewOutboundItems, outboundItems]
  );
  (0, import_react5.useEffect)(() => {
    const existingItemsMap = {};
    previewOutboundItems.forEach((i) => {
      const ind = outboundItems.findIndex((field) => field.item_id === i.id);
      existingItemsMap[i.id] = true;
      if (ind > -1) {
        if (outboundItems[ind].quantity !== i.detail.quantity) {
          update(ind, {
            ...outboundItems[ind],
            quantity: i.detail.quantity
          });
        }
      } else {
        append(
          {
            item_id: i.id,
            quantity: i.detail.quantity,
            variant_id: i.variant_id
          },
          { shouldFocus: false }
        );
      }
    });
    outboundItems.forEach((i, ind) => {
      if (!(i.item_id in existingItemsMap)) {
        remove(ind);
      }
    });
  }, [previewOutboundItems]);
  const locationId = form.watch("location_id");
  const showOutboundItemsPlaceholder = !outboundItems.length;
  const onItemsSelected = async () => {
    var _a, _b;
    itemsToAdd.length && await addOutboundItem(
      {
        items: itemsToAdd.map((variantId) => ({
          variant_id: variantId,
          quantity: 1
        }))
      },
      {
        onError: (error) => {
          toast.error(error.message);
        }
      }
    );
    for (const itemToRemove of itemsToRemove) {
      const action = (_b = (_a = previewOutboundItems.find((i) => i.variant_id === itemToRemove)) == null ? void 0 : _a.actions) == null ? void 0 : _b.find((a) => a.action === "ITEM_ADD");
      if (action == null ? void 0 : action.id) {
        await removeOutboundItem(action == null ? void 0 : action.id, {
          onError: (error) => {
            toast.error(error.message);
          }
        });
      }
    }
    setIsOpen("outbound-items", false);
  };
  const onShippingOptionChange = async (selectedOptionId) => {
    const outboundShippingMethods = preview.shipping_methods.filter((s) => {
      var _a;
      const action = (_a = s.actions) == null ? void 0 : _a.find(
        (a) => a.action === "SHIPPING_ADD" && !a.return_id
      );
      return action && !!!(action == null ? void 0 : action.return_id);
    });
    const promises = outboundShippingMethods.filter(Boolean).map((outboundShippingMethod) => {
      var _a;
      const action = (_a = outboundShippingMethod.actions) == null ? void 0 : _a.find(
        (a) => a.action === "SHIPPING_ADD" && !a.return_id
      );
      if (action) {
        return deleteOutboundShipping(action.id);
      }
    });
    await Promise.all(promises);
    if (selectedOptionId) {
      await addOutboundShipping(
        { shipping_option_id: selectedOptionId },
        {
          onError: (error) => {
            toast.error(error.message);
          }
        }
      );
    }
  };
  const showLevelsWarning = (0, import_react5.useMemo)(() => {
    if (!locationId) {
      return false;
    }
    const allItemsHaveLocation = outboundItems.map((i) => {
      var _a, _b;
      const item = variantItemMap.get(i.variant_id);
      if (!(item == null ? void 0 : item.variant_id) || !(item == null ? void 0 : item.variant)) {
        return true;
      }
      if (!((_a = item.variant) == null ? void 0 : _a.manage_inventory)) {
        return true;
      }
      return (_b = inventoryMap[item.variant_id]) == null ? void 0 : _b.find(
        (l) => l.location_id === locationId
      );
    }).every(Boolean);
    return !allItemsHaveLocation;
  }, [outboundItems, inventoryMap, locationId]);
  (0, import_react5.useEffect)(() => {
    const getInventoryMap = async () => {
      const ret = {};
      if (!outboundItems.length) {
        return ret;
      }
      const variantIds = outboundItems.map((item) => item == null ? void 0 : item.variant_id).filter(Boolean);
      const variants = (await sdk.admin.productVariant.list({
        id: variantIds,
        fields: "*inventory.location_levels"
      })).variants;
      variants.forEach((variant) => {
        var _a, _b;
        ret[variant.id] = ((_b = (_a = variant.inventory) == null ? void 0 : _a[0]) == null ? void 0 : _b.location_levels) || [];
      });
      return ret;
    };
    getInventoryMap().then((map) => {
      setInventoryMap(map);
    });
  }, [outboundItems]);
  return (0, import_jsx_runtime7.jsxs)("div", { children: [
    (0, import_jsx_runtime7.jsxs)("div", { className: "mt-8 flex items-center justify-between", children: [
      (0, import_jsx_runtime7.jsx)(Heading, { level: "h2", children: t2("orders.returns.outbound") }),
      (0, import_jsx_runtime7.jsxs)(StackedFocusModal, { id: "outbound-items", children: [
        (0, import_jsx_runtime7.jsx)(StackedFocusModal.Trigger, { asChild: true, children: (0, import_jsx_runtime7.jsx)("a", { className: "focus-visible:shadow-borders-focus transition-fg txt-compact-small-plus cursor-pointer text-blue-500 outline-none hover:text-blue-400", children: t2("actions.addItems") }) }),
        (0, import_jsx_runtime7.jsxs)(StackedFocusModal.Content, { children: [
          (0, import_jsx_runtime7.jsx)(StackedFocusModal.Header, {}),
          (0, import_jsx_runtime7.jsx)(
            AddClaimOutboundItemsTable,
            {
              selectedItems: outboundItems.map((i) => i.variant_id),
              currencyCode: order.currency_code,
              onSelectionChange: (finalSelection) => {
                const alreadySelected = outboundItems.map((i) => i.variant_id);
                itemsToAdd = finalSelection.filter(
                  (selection) => !alreadySelected.includes(selection)
                );
                itemsToRemove = alreadySelected.filter(
                  (selection) => !finalSelection.includes(selection)
                );
              }
            }
          ),
          (0, import_jsx_runtime7.jsx)(StackedFocusModal.Footer, { children: (0, import_jsx_runtime7.jsx)("div", { className: "flex w-full items-center justify-end gap-x-4", children: (0, import_jsx_runtime7.jsxs)("div", { className: "flex items-center justify-end gap-x-2", children: [
            (0, import_jsx_runtime7.jsx)(RouteFocusModal.Close, { asChild: true, children: (0, import_jsx_runtime7.jsx)(Button, { type: "button", variant: "secondary", size: "small", children: t2("actions.cancel") }) }),
            (0, import_jsx_runtime7.jsx)(
              Button,
              {
                type: "submit",
                variant: "primary",
                size: "small",
                role: "button",
                onClick: async () => await onItemsSelected(),
                children: t2("actions.save")
              },
              "submit-button"
            )
          ] }) }) })
        ] })
      ] })
    ] }),
    showOutboundItemsPlaceholder && (0, import_jsx_runtime7.jsx)(ItemPlaceholder, {}),
    outboundItems.map(
      (item, index) => variantOutboundMap.get(item.variant_id) && (0, import_jsx_runtime7.jsx)(
        ClaimOutboundItem,
        {
          previewItem: variantOutboundMap.get(item.variant_id),
          currencyCode: order.currency_code,
          form,
          onRemove: () => {
            var _a, _b, _c;
            const actionId = (_c = (_b = (_a = previewOutboundItems.find((i) => i.id === item.item_id)) == null ? void 0 : _a.actions) == null ? void 0 : _b.find((a) => a.action === "ITEM_ADD")) == null ? void 0 : _c.id;
            if (actionId) {
              removeOutboundItem(actionId, {
                onError: (error) => {
                  toast.error(error.message);
                }
              });
            }
          },
          onUpdate: (payload) => {
            var _a, _b, _c;
            const actionId = (_c = (_b = (_a = previewOutboundItems.find((i) => i.id === item.item_id)) == null ? void 0 : _a.actions) == null ? void 0 : _b.find((a) => a.action === "ITEM_ADD")) == null ? void 0 : _c.id;
            if (actionId) {
              updateOutboundItem(
                { ...payload, actionId },
                {
                  onError: (error) => {
                    toast.error(error.message);
                  }
                }
              );
            }
          },
          index
        },
        item.id
      )
    ),
    !showOutboundItemsPlaceholder && (0, import_jsx_runtime7.jsx)("div", { className: "mt-8 flex flex-col gap-y-4", children: (0, import_jsx_runtime7.jsxs)("div", { className: "grid grid-cols-1 gap-2 md:grid-cols-2", children: [
      (0, import_jsx_runtime7.jsxs)("div", { children: [
        (0, import_jsx_runtime7.jsx)(Form.Label, { children: t2("orders.claims.outboundShipping") }),
        (0, import_jsx_runtime7.jsx)(Form.Hint, { className: "!mt-1", children: t2("orders.claims.outboundShippingHint") })
      ] }),
      (0, import_jsx_runtime7.jsx)(
        Form.Field,
        {
          control: form.control,
          name: "outbound_option_id",
          render: ({ field: { value, onChange, ...field } }) => {
            return (0, import_jsx_runtime7.jsx)(Form.Item, { children: (0, import_jsx_runtime7.jsx)(Form.Control, { children: (0, import_jsx_runtime7.jsx)(
              Combobox,
              {
                allowClear: true,
                value: value ?? void 0,
                onChange: (val) => {
                  onChange(val);
                  onShippingOptionChange(val);
                },
                ...field,
                options: outboundShippingOptions.map((so) => ({
                  label: so.name,
                  value: so.id
                })),
                disabled: !outboundShippingOptions.length,
                noResultsPlaceholder: (0, import_jsx_runtime7.jsx)(OutboundShippingPlaceholder, {})
              }
            ) }) });
          }
        }
      )
    ] }) }),
    showLevelsWarning && (0, import_jsx_runtime7.jsxs)(Alert, { variant: "warning", dismissible: true, className: "mt-4 p-5", children: [
      (0, import_jsx_runtime7.jsx)("div", { className: "text-ui-fg-subtle txt-small pb-2 font-medium leading-[20px]", children: t2("orders.returns.noInventoryLevel") }),
      (0, import_jsx_runtime7.jsx)(Text, { className: "text-ui-fg-subtle txt-small leading-normal", children: t2("orders.returns.noInventoryLevelDesc") })
    ] })
  ] });
};
var itemsToAdd2 = [];
var itemsToRemove2 = [];
var IS_CANCELING = false;
var ClaimCreateForm = ({
  order,
  preview,
  claim,
  orderReturn
}) => {
  var _a;
  const { t: t2 } = useTranslation();
  const { handleSuccess } = useRouteModal();
  const { setIsOpen } = useStackedModal();
  const [isShippingInboundPriceEdit, setIsShippingInboundPriceEdit] = (0, import_react2.useState)(false);
  const [isShippingOutboundPriceEdit, setIsShippingOutboundPriceEdit] = (0, import_react2.useState)(false);
  const [customInboundShippingAmount, setCustomInboundShippingAmount] = (0, import_react2.useState)(0);
  const [customOutboundShippingAmount, setCustomOutboundShippingAmount] = (0, import_react2.useState)(0);
  const [inventoryMap, setInventoryMap] = (0, import_react2.useState)({});
  const { mutateAsync: confirmClaimRequest, isPending: isConfirming } = useClaimConfirmRequest(claim.id, order.id);
  const { mutateAsync: cancelClaimRequest, isPending: isCanceling } = useCancelClaimRequest(claim.id, order.id);
  const { mutateAsync: updateReturn, isPending: isUpdating } = useUpdateReturn(
    (_a = preview == null ? void 0 : preview.order_change) == null ? void 0 : _a.return_id,
    order.id
  );
  const {
    mutateAsync: addInboundShipping,
    isPending: isAddingInboundShipping
  } = useAddClaimInboundShipping(claim.id, order.id);
  const {
    mutateAsync: updateInboundShipping,
    isPending: isUpdatingInboundShipping
  } = useUpdateClaimInboundShipping(claim.id, order.id);
  const {
    mutateAsync: updateOutboundShipping,
    isPending: isUpdatingOutboundShipping
  } = useUpdateClaimOutboundShipping(claim.id, order.id);
  const {
    mutateAsync: deleteInboundShipping,
    isPending: isDeletingInboundShipping
  } = useDeleteClaimInboundShipping(claim.id, order.id);
  const { mutateAsync: addInboundItem, isPending: isAddingInboundItem } = useAddClaimInboundItems(claim.id, order.id);
  const { mutateAsync: updateInboundItem, isPending: isUpdatingInboundItem } = useUpdateClaimInboundItem(claim.id, order.id);
  const { mutateAsync: removeInboundItem, isPending: isRemovingInboundItem } = useRemoveClaimInboundItem(claim.id, order.id);
  const isRequestLoading = isConfirming || isCanceling || isAddingInboundShipping || isUpdatingInboundShipping || isDeletingInboundShipping || isUpdatingOutboundShipping || isAddingInboundItem || isRemovingInboundItem || isUpdatingInboundItem || isUpdating;
  const previewItems = (0, import_react2.useMemo)(
    () => {
      var _a2;
      return (_a2 = preview == null ? void 0 : preview.items) == null ? void 0 : _a2.filter(
        (i) => {
          var _a3;
          return !!((_a3 = i.actions) == null ? void 0 : _a3.find((a) => a.claim_id === claim.id));
        }
      );
    },
    [preview.items]
  );
  const inboundPreviewItems = previewItems.filter(
    (item) => {
      var _a2;
      return !!((_a2 = item.actions) == null ? void 0 : _a2.find((a) => a.action === "RETURN_ITEM"));
    }
  );
  const outboundPreviewItems = previewItems.filter(
    (item) => {
      var _a2;
      return !!((_a2 = item.actions) == null ? void 0 : _a2.find((a) => a.action === "ITEM_ADD"));
    }
  );
  const itemsMap = (0, import_react2.useMemo)(
    () => {
      var _a2;
      return new Map((_a2 = order == null ? void 0 : order.items) == null ? void 0 : _a2.map((i) => [i.id, i]));
    },
    [order.items]
  );
  const form = useForm({
    defaultValues: () => {
      const inboundShippingMethod = preview.shipping_methods.find((s) => {
        var _a2;
        return !!((_a2 = s.actions) == null ? void 0 : _a2.find(
          (a) => a.action === "SHIPPING_ADD" && !!a.return_id
        ));
      });
      const outboundShippingMethod = preview.shipping_methods.find((s) => {
        var _a2;
        return !!((_a2 = s.actions) == null ? void 0 : _a2.find(
          (a) => a.action === "SHIPPING_ADD" && !a.return_id
        ));
      });
      return Promise.resolve({
        inbound_items: inboundPreviewItems.map((i) => {
          var _a2, _b;
          const inboundAction = (_a2 = i.actions) == null ? void 0 : _a2.find(
            (a) => a.action === "RETURN_ITEM"
          );
          return {
            item_id: i.id,
            variant_id: i.variant_id,
            quantity: i.detail.return_requested_quantity,
            note: inboundAction == null ? void 0 : inboundAction.internal_note,
            reason_id: (_b = inboundAction == null ? void 0 : inboundAction.details) == null ? void 0 : _b.reason_id
          };
        }),
        outbound_items: outboundPreviewItems.map((i) => ({
          item_id: i.id,
          variant_id: i.variant_id,
          quantity: i.detail.quantity
        })),
        inbound_option_id: inboundShippingMethod ? inboundShippingMethod.shipping_option_id : "",
        outbound_option_id: outboundShippingMethod ? outboundShippingMethod.shipping_option_id : "",
        location_id: orderReturn == null ? void 0 : orderReturn.location_id,
        send_notification: false
      });
    },
    resolver: t(ClaimCreateSchema)
  });
  const locationId = form.watch("location_id");
  const { stock_locations = [] } = useStockLocations({ limit: 999 });
  const { shipping_options = [] } = useShippingOptions(
    {
      limit: 999,
      fields: "*prices,+service_zone.fulfillment_set.location.id",
      stock_location_id: locationId
    },
    {
      enabled: !!locationId
    }
  );
  const inboundShippingOptions = shipping_options.filter(
    (shippingOption) => !!shippingOption.rules.find(
      (r) => r.attribute === "is_return" && r.value === "true"
    )
  );
  const inboundShipping = preview.shipping_methods.find((s) => {
    var _a2;
    return !!((_a2 = s.actions) == null ? void 0 : _a2.find(
      (a) => a.action === "SHIPPING_ADD" && !!a.return_id
    ));
  });
  const outboundShipping = preview.shipping_methods.find((s) => {
    var _a2;
    return !!((_a2 = s.actions) == null ? void 0 : _a2.find((a) => a.action === "SHIPPING_ADD" && !a.return_id));
  });
  (0, import_react2.useEffect)(() => {
    if (inboundShipping) {
      setCustomInboundShippingAmount(inboundShipping.total);
    }
  }, [inboundShipping]);
  (0, import_react2.useEffect)(() => {
    if (outboundShipping) {
      setCustomOutboundShippingAmount(outboundShipping.total);
    }
  }, [outboundShipping]);
  const {
    fields: inboundItems,
    append,
    remove,
    update
  } = useFieldArray({
    name: "inbound_items",
    control: form.control
  });
  const previewItemsMap = (0, import_react2.useMemo)(
    () => new Map(previewItems.map((i) => [i.id, i])),
    [previewItems, inboundItems]
  );
  (0, import_react2.useEffect)(() => {
    const existingItemsMap = {};
    inboundPreviewItems.forEach((i) => {
      var _a2, _b;
      const ind = inboundItems.findIndex((field) => field.item_id === i.id);
      existingItemsMap[i.id] = true;
      if (ind > -1) {
        if (inboundItems[ind].quantity !== i.detail.return_requested_quantity) {
          const returnItemAction = (_a2 = i.actions) == null ? void 0 : _a2.find(
            (a) => a.action === "RETURN_ITEM"
          );
          update(ind, {
            ...inboundItems[ind],
            quantity: i.detail.return_requested_quantity,
            note: returnItemAction == null ? void 0 : returnItemAction.internal_note,
            reason_id: (_b = returnItemAction == null ? void 0 : returnItemAction.details) == null ? void 0 : _b.reason_id
          });
        }
      } else {
        append(
          { item_id: i.id, quantity: i.detail.return_requested_quantity },
          { shouldFocus: false }
        );
      }
    });
    inboundItems.forEach((i, ind) => {
      if (!(i.item_id in existingItemsMap)) {
        remove(ind);
      }
    });
  }, [previewItems]);
  (0, import_react2.useEffect)(() => {
    const inboundShipping2 = preview.shipping_methods.find(
      (s) => {
        var _a2;
        return !!((_a2 = s.actions) == null ? void 0 : _a2.find((a) => a.action === "SHIPPING_ADD" && !!a.return_id));
      }
    );
    if (inboundShipping2) {
      form.setValue("inbound_option_id", inboundShipping2.shipping_option_id);
    } else {
      form.setValue("inbound_option_id", null);
    }
    const outboundShipping2 = preview.shipping_methods.find(
      (s) => {
        var _a2;
        return !!((_a2 = s.actions) == null ? void 0 : _a2.find((a) => a.action === "SHIPPING_ADD" && !a.return_id));
      }
    );
    if (outboundShipping2) {
      form.setValue("outbound_option_id", outboundShipping2.shipping_option_id);
    } else {
      form.setValue("outbound_option_id", null);
    }
  }, [preview.shipping_methods]);
  (0, import_react2.useEffect)(() => {
    form.setValue("location_id", orderReturn == null ? void 0 : orderReturn.location_id);
  }, [orderReturn]);
  const showInboundItemsPlaceholder = !inboundPreviewItems.length;
  const showOutboundItemsPlaceholder = !outboundPreviewItems.length;
  const inboundShippingOptionId = form.watch("inbound_option_id");
  const outboundShippingOptionId = form.watch("outbound_option_id");
  const prompt = usePrompt();
  const handleSubmit = form.handleSubmit(async (data) => {
    const res = await prompt({
      title: t2("general.areYouSure"),
      description: t2("orders.claims.confirmText"),
      confirmText: t2("actions.continue"),
      cancelText: t2("actions.cancel"),
      variant: "confirmation"
    });
    if (!res) {
      return;
    }
    await confirmClaimRequest(
      { no_notification: !data.send_notification },
      {
        onSuccess: () => {
          toast.success(t2("orders.claims.toast.confirmedSuccessfully"));
          handleSuccess();
        },
        onError: (error) => {
          toast.error(error.message);
        }
      }
    );
  });
  const onItemsSelected = async () => {
    var _a2, _b, _c;
    itemsToAdd2.length && await addInboundItem(
      {
        items: itemsToAdd2.map((id) => ({
          id,
          quantity: 1
        }))
      },
      {
        onError: (error) => {
          toast.error(error.message);
        }
      }
    );
    for (const itemToRemove of itemsToRemove2) {
      const actionId = (_c = (_b = (_a2 = previewItems.find((i) => i.id === itemToRemove)) == null ? void 0 : _a2.actions) == null ? void 0 : _b.find((a) => a.action === "RETURN_ITEM")) == null ? void 0 : _c.id;
      if (actionId) {
        await removeInboundItem(actionId, {
          onError: (error) => {
            toast.error(error.message);
          }
        });
      }
    }
    setIsOpen("inbound-items", false);
  };
  const onLocationChange = async (selectedLocationId) => {
    await updateReturn({ location_id: selectedLocationId });
  };
  const onShippingOptionChange = async (selectedOptionId) => {
    const inboundShippingMethods = preview.shipping_methods.filter((s) => {
      var _a2;
      const action = (_a2 = s.actions) == null ? void 0 : _a2.find(
        (a) => a.action === "SHIPPING_ADD" && !!a.return_id
      );
      return action && !!(action == null ? void 0 : action.return_id);
    });
    const promises = inboundShippingMethods.filter(Boolean).map((inboundShippingMethod) => {
      var _a2;
      const action = (_a2 = inboundShippingMethod.actions) == null ? void 0 : _a2.find(
        (a) => a.action === "SHIPPING_ADD" && !!a.return_id
      );
      if (action) {
        return deleteInboundShipping(action.id);
      }
    });
    await Promise.all(promises);
    if (selectedOptionId) {
      await addInboundShipping(
        { shipping_option_id: selectedOptionId },
        {
          onError: (error) => {
            toast.error(error.message);
          }
        }
      );
    }
  };
  (0, import_react2.useEffect)(() => {
    var _a2;
    if (isShippingInboundPriceEdit) {
      (_a2 = document.getElementById("js-shipping-inbound-input")) == null ? void 0 : _a2.focus();
    }
  }, [isShippingInboundPriceEdit]);
  (0, import_react2.useEffect)(() => {
    var _a2;
    if (isShippingOutboundPriceEdit) {
      (_a2 = document.getElementById("js-shipping-outbound-input")) == null ? void 0 : _a2.focus();
    }
  }, [isShippingOutboundPriceEdit]);
  const showLevelsWarning = (0, import_react2.useMemo)(() => {
    if (!locationId) {
      return false;
    }
    const allItemsHaveLocation = inboundItems.map((_i) => {
      var _a2, _b;
      const item = itemsMap.get(_i.item_id);
      if (!(item == null ? void 0 : item.variant_id) || !(item == null ? void 0 : item.variant)) {
        return true;
      }
      if (!((_a2 = item.variant) == null ? void 0 : _a2.manage_inventory)) {
        return true;
      }
      return (_b = inventoryMap[item.variant_id]) == null ? void 0 : _b.find(
        (l) => l.location_id === locationId
      );
    }).every(Boolean);
    return !allItemsHaveLocation;
  }, [inboundItems, inventoryMap, locationId]);
  (0, import_react2.useEffect)(() => {
    const getInventoryMap = async () => {
      const ret = {};
      if (!inboundItems.length) {
        return ret;
      }
      const variantIds = inboundItems.map((item) => item == null ? void 0 : item.variant_id).filter(Boolean);
      const variants = (await sdk.admin.productVariant.list({
        id: variantIds,
        fields: "*inventory.location_levels"
      })).variants;
      variants.forEach((variant) => {
        var _a2, _b;
        ret[variant.id] = ((_b = (_a2 = variant.inventory) == null ? void 0 : _a2[0]) == null ? void 0 : _b.location_levels) || [];
      });
      return ret;
    };
    getInventoryMap().then((map) => {
      setInventoryMap(map);
    });
  }, [inboundItems]);
  (0, import_react2.useEffect)(() => {
    return () => {
      if (IS_CANCELING) {
        cancelClaimRequest(void 0, {
          onSuccess: () => {
            toast.success(t2("orders.claims.actions.cancelClaim.successToast"));
          },
          onError: (error) => {
            toast.error(error.message);
          }
        });
        IS_CANCELING = false;
      }
    };
  }, []);
  const inboundShippingTotal = (0, import_react2.useMemo)(() => {
    const method = preview.shipping_methods.find(
      (sm) => {
        var _a2;
        return !!((_a2 = sm.actions) == null ? void 0 : _a2.find((a) => a.action === "SHIPPING_ADD" && !!a.return_id));
      }
    );
    return (method == null ? void 0 : method.total) || 0;
  }, [preview.shipping_methods]);
  return (0, import_jsx_runtime8.jsx)(RouteFocusModal.Form, { form, children: (0, import_jsx_runtime8.jsxs)(KeyboundForm, { onSubmit: handleSubmit, className: "flex h-full flex-col", children: [
    (0, import_jsx_runtime8.jsx)(RouteFocusModal.Header, {}),
    (0, import_jsx_runtime8.jsx)(RouteFocusModal.Body, { className: "flex size-full justify-center overflow-y-auto", children: (0, import_jsx_runtime8.jsxs)("div", { className: "mt-16 w-[720px] max-w-[100%] px-4 md:p-0", children: [
      (0, import_jsx_runtime8.jsx)(Heading, { level: "h1", children: t2("orders.claims.create") }),
      (0, import_jsx_runtime8.jsxs)("div", { className: "mt-8 flex items-center justify-between", children: [
        (0, import_jsx_runtime8.jsx)(Heading, { level: "h2", children: t2("orders.returns.inbound") }),
        (0, import_jsx_runtime8.jsxs)(StackedFocusModal, { id: "inbound-items", children: [
          (0, import_jsx_runtime8.jsx)(StackedFocusModal.Trigger, { asChild: true, children: (0, import_jsx_runtime8.jsx)("a", { className: "focus-visible:shadow-borders-focus transition-fg txt-compact-small-plus cursor-pointer text-blue-500 outline-none hover:text-blue-400", children: t2("actions.addItems") }) }),
          (0, import_jsx_runtime8.jsxs)(StackedFocusModal.Content, { children: [
            (0, import_jsx_runtime8.jsx)(StackedFocusModal.Header, {}),
            (0, import_jsx_runtime8.jsx)(
              AddClaimItemsTable,
              {
                items: order.items,
                selectedItems: inboundItems.map((i) => i.item_id),
                currencyCode: order.currency_code,
                onSelectionChange: (finalSelection) => {
                  const alreadySelected = inboundItems.map((i) => i.item_id);
                  itemsToAdd2 = finalSelection.filter(
                    (selection) => !alreadySelected.includes(selection)
                  );
                  itemsToRemove2 = alreadySelected.filter(
                    (selection) => !finalSelection.includes(selection)
                  );
                }
              }
            ),
            (0, import_jsx_runtime8.jsx)(StackedFocusModal.Footer, { children: (0, import_jsx_runtime8.jsx)("div", { className: "flex w-full items-center justify-end gap-x-4", children: (0, import_jsx_runtime8.jsxs)("div", { className: "flex items-center justify-end gap-x-2", children: [
              (0, import_jsx_runtime8.jsx)(RouteFocusModal.Close, { asChild: true, children: (0, import_jsx_runtime8.jsx)(
                Button,
                {
                  type: "button",
                  variant: "secondary",
                  size: "small",
                  children: t2("actions.cancel")
                }
              ) }),
              (0, import_jsx_runtime8.jsx)(
                Button,
                {
                  type: "submit",
                  variant: "primary",
                  size: "small",
                  role: "button",
                  onClick: async () => await onItemsSelected(),
                  children: t2("actions.save")
                },
                "submit-button"
              )
            ] }) }) })
          ] })
        ] })
      ] }),
      showInboundItemsPlaceholder && (0, import_jsx_runtime8.jsx)(ItemPlaceholder, {}),
      inboundItems.map(
        (item, index) => previewItemsMap.get(item.item_id) && itemsMap.get(item.item_id) && (0, import_jsx_runtime8.jsx)(
          ClaimInboundItem,
          {
            item: itemsMap.get(item.item_id),
            previewItem: previewItemsMap.get(item.item_id),
            currencyCode: order.currency_code,
            form,
            onRemove: () => {
              var _a2, _b, _c;
              const actionId = (_c = (_b = (_a2 = previewItems.find((i) => i.id === item.item_id)) == null ? void 0 : _a2.actions) == null ? void 0 : _b.find((a) => a.action === "RETURN_ITEM")) == null ? void 0 : _c.id;
              if (actionId) {
                removeInboundItem(actionId, {
                  onError: (error) => {
                    toast.error(error.message);
                  }
                });
              }
            },
            onUpdate: (payload) => {
              var _a2, _b;
              const action = (_b = (_a2 = previewItems.find((i) => i.id === item.item_id)) == null ? void 0 : _a2.actions) == null ? void 0 : _b.find((a) => a.action === "RETURN_ITEM");
              if (action) {
                updateInboundItem(
                  { ...payload, actionId: action.id },
                  {
                    onError: (error) => {
                      var _a3, _b2;
                      if (((_a3 = action.details) == null ? void 0 : _a3.quantity) && payload.quantity) {
                        form.setValue(
                          `inbound_items.${index}.quantity`,
                          (_b2 = action.details) == null ? void 0 : _b2.quantity
                        );
                      }
                      toast.error(error.message);
                    }
                  }
                );
              }
            },
            index
          },
          item.id
        )
      ),
      !showInboundItemsPlaceholder && (0, import_jsx_runtime8.jsxs)("div", { className: "mt-8 flex flex-col gap-y-4", children: [
        (0, import_jsx_runtime8.jsxs)("div", { className: "grid grid-cols-1 gap-2 md:grid-cols-2", children: [
          (0, import_jsx_runtime8.jsxs)("div", { children: [
            (0, import_jsx_runtime8.jsx)(Form.Label, { children: t2("orders.returns.location") }),
            (0, import_jsx_runtime8.jsx)(Form.Hint, { className: "!mt-1", children: t2("orders.returns.locationHint") })
          ] }),
          (0, import_jsx_runtime8.jsx)(
            Form.Field,
            {
              control: form.control,
              name: "location_id",
              render: ({ field: { value, onChange, ...field } }) => {
                return (0, import_jsx_runtime8.jsx)(Form.Item, { children: (0, import_jsx_runtime8.jsx)(Form.Control, { children: (0, import_jsx_runtime8.jsx)(
                  Combobox,
                  {
                    ...field,
                    value: value ?? void 0,
                    onChange: (v) => {
                      onChange(v);
                      onLocationChange(v);
                    },
                    options: (stock_locations ?? []).map(
                      (stockLocation) => ({
                        label: stockLocation.name,
                        value: stockLocation.id
                      })
                    )
                  }
                ) }) });
              }
            }
          )
        ] }),
        (0, import_jsx_runtime8.jsxs)("div", { className: "grid grid-cols-1 gap-2 md:grid-cols-2", children: [
          (0, import_jsx_runtime8.jsxs)("div", { children: [
            (0, import_jsx_runtime8.jsxs)(Form.Label, { children: [
              t2("orders.returns.inboundShipping"),
              (0, import_jsx_runtime8.jsxs)(
                Text,
                {
                  size: "small",
                  leading: "compact",
                  className: "text-ui-fg-muted ml-1 inline",
                  children: [
                    "(",
                    t2("fields.optional"),
                    ")"
                  ]
                }
              )
            ] }),
            (0, import_jsx_runtime8.jsx)(Form.Hint, { className: "!mt-1", children: t2("orders.returns.inboundShippingHint") })
          ] }),
          (0, import_jsx_runtime8.jsx)(
            Form.Field,
            {
              control: form.control,
              name: "inbound_option_id",
              render: ({ field: { value, onChange, ...field } }) => {
                return (0, import_jsx_runtime8.jsx)(Form.Item, { children: (0, import_jsx_runtime8.jsx)(Form.Control, { children: (0, import_jsx_runtime8.jsx)(
                  Combobox,
                  {
                    allowClear: true,
                    value: value ?? void 0,
                    onChange: (val) => {
                      onChange(val);
                      onShippingOptionChange(val);
                    },
                    ...field,
                    options: inboundShippingOptions.map((so) => ({
                      label: so.name,
                      value: so.id
                    })),
                    disabled: !locationId,
                    noResultsPlaceholder: (0, import_jsx_runtime8.jsx)(ReturnShippingPlaceholder, {})
                  }
                ) }) });
              }
            }
          )
        ] })
      ] }),
      showLevelsWarning && (0, import_jsx_runtime8.jsxs)(Alert, { variant: "warning", dismissible: true, className: "mt-4 p-5", children: [
        (0, import_jsx_runtime8.jsx)("div", { className: "text-ui-fg-subtle txt-small pb-2 font-medium leading-[20px]", children: t2("orders.returns.noInventoryLevel") }),
        (0, import_jsx_runtime8.jsx)(Text, { className: "text-ui-fg-subtle txt-small leading-normal", children: t2("orders.returns.noInventoryLevelDesc") })
      ] }),
      (0, import_jsx_runtime8.jsx)(
        ClaimOutboundSection,
        {
          form,
          preview,
          order,
          claim
        }
      ),
      (0, import_jsx_runtime8.jsxs)("div", { className: "mt-8 border-y border-dotted py-4", children: [
        (0, import_jsx_runtime8.jsxs)("div", { className: "mb-2 flex items-center justify-between", children: [
          (0, import_jsx_runtime8.jsx)("span", { className: "txt-small text-ui-fg-subtle", children: t2("orders.returns.inboundTotal") }),
          (0, import_jsx_runtime8.jsx)("span", { className: "txt-small text-ui-fg-subtle", children: getStylizedAmount(
            inboundPreviewItems.reduce((acc, item) => {
              var _a2;
              const action = (_a2 = item.actions) == null ? void 0 : _a2.find(
                (act) => act.action === "RETURN_ITEM"
              );
              acc = acc + ((action == null ? void 0 : action.amount) || 0);
              return acc;
            }, 0) * -1,
            order.currency_code
          ) })
        ] }),
        (0, import_jsx_runtime8.jsxs)("div", { className: "mb-2 flex items-center justify-between", children: [
          (0, import_jsx_runtime8.jsx)("span", { className: "txt-small text-ui-fg-subtle", children: t2("orders.claims.outboundTotal") }),
          (0, import_jsx_runtime8.jsx)("span", { className: "txt-small text-ui-fg-subtle", children: getStylizedAmount(
            outboundPreviewItems.reduce((acc, item) => {
              var _a2;
              const action = (_a2 = item.actions) == null ? void 0 : _a2.find(
                (act) => act.action === "ITEM_ADD"
              );
              acc = acc + ((action == null ? void 0 : action.amount) || 0);
              return acc;
            }, 0),
            order.currency_code
          ) })
        ] }),
        (0, import_jsx_runtime8.jsxs)("div", { className: "mb-2 flex items-center justify-between", children: [
          (0, import_jsx_runtime8.jsx)("span", { className: "txt-small text-ui-fg-subtle", children: t2("orders.returns.inboundShipping") }),
          (0, import_jsx_runtime8.jsxs)("span", { className: "txt-small text-ui-fg-subtle flex items-center", children: [
            !isShippingInboundPriceEdit && (0, import_jsx_runtime8.jsx)(
              IconButton,
              {
                onClick: () => setIsShippingInboundPriceEdit(true),
                variant: "transparent",
                className: "text-ui-fg-muted",
                disabled: showInboundItemsPlaceholder || !inboundShippingOptionId,
                children: (0, import_jsx_runtime8.jsx)(PencilSquare, {})
              }
            ),
            isShippingInboundPriceEdit ? (0, import_jsx_runtime8.jsx)(
              CurrencyInput,
              {
                id: "js-shipping-inbound-input",
                onBlur: () => {
                  let actionId;
                  preview.shipping_methods.forEach((s) => {
                    if (s.actions) {
                      for (const a of s.actions) {
                        if (a.action === "SHIPPING_ADD" && !!a.return_id) {
                          actionId = a.id;
                        }
                      }
                    }
                  });
                  const customPrice = customInboundShippingAmount === "" ? null : parseFloat(customInboundShippingAmount);
                  if (actionId) {
                    updateInboundShipping(
                      {
                        actionId,
                        custom_amount: customPrice
                      },
                      {
                        onError: (error) => {
                          toast.error(error.message);
                        }
                      }
                    );
                  }
                  setIsShippingInboundPriceEdit(false);
                },
                symbol: currencies[order.currency_code.toUpperCase()].symbol_native,
                code: order.currency_code,
                onValueChange: setCustomInboundShippingAmount,
                value: customInboundShippingAmount,
                disabled: showInboundItemsPlaceholder
              }
            ) : getStylizedAmount(inboundShippingTotal, order.currency_code)
          ] })
        ] }),
        (0, import_jsx_runtime8.jsxs)("div", { className: "flex items-center justify-between", children: [
          (0, import_jsx_runtime8.jsx)("span", { className: "txt-small text-ui-fg-subtle", children: t2("orders.claims.outboundShipping") }),
          (0, import_jsx_runtime8.jsxs)("span", { className: "txt-small text-ui-fg-subtle flex items-center", children: [
            !isShippingOutboundPriceEdit && (0, import_jsx_runtime8.jsx)(
              IconButton,
              {
                onClick: () => setIsShippingOutboundPriceEdit(true),
                variant: "transparent",
                className: "text-ui-fg-muted",
                disabled: showOutboundItemsPlaceholder || !outboundShippingOptionId,
                children: (0, import_jsx_runtime8.jsx)(PencilSquare, {})
              }
            ),
            isShippingOutboundPriceEdit ? (0, import_jsx_runtime8.jsx)(
              CurrencyInput,
              {
                id: "js-shipping-outbound-input",
                onBlur: () => {
                  let actionId;
                  preview.shipping_methods.forEach((s) => {
                    if (s.actions) {
                      for (const a of s.actions) {
                        if (a.action === "SHIPPING_ADD" && !a.return_id) {
                          actionId = a.id;
                        }
                      }
                    }
                  });
                  const customPrice = customOutboundShippingAmount === "" ? null : parseFloat(customOutboundShippingAmount);
                  if (actionId) {
                    updateOutboundShipping(
                      {
                        actionId,
                        custom_amount: customPrice
                      },
                      {
                        onError: (error) => {
                          toast.error(error.message);
                        }
                      }
                    );
                  }
                  setIsShippingOutboundPriceEdit(false);
                },
                symbol: currencies[order.currency_code.toUpperCase()].symbol_native,
                code: order.currency_code,
                onValueChange: setCustomOutboundShippingAmount,
                value: customOutboundShippingAmount,
                disabled: showOutboundItemsPlaceholder
              }
            ) : getStylizedAmount(
              (outboundShipping == null ? void 0 : outboundShipping.amount) ?? 0,
              order.currency_code
            )
          ] })
        ] }),
        (0, import_jsx_runtime8.jsxs)("div", { className: "mt-4 flex items-center justify-between border-t border-dotted pt-4", children: [
          (0, import_jsx_runtime8.jsx)("span", { className: "txt-small font-medium", children: t2("orders.claims.refundAmount") }),
          (0, import_jsx_runtime8.jsx)("span", { className: "txt-small font-medium", children: getStylizedAmount(
            preview.summary.pending_difference,
            order.currency_code
          ) })
        ] })
      ] }),
      (0, import_jsx_runtime8.jsx)("div", { className: "bg-ui-bg-field mt-8 rounded-lg border py-2 pl-2 pr-4", children: (0, import_jsx_runtime8.jsx)(
        Form.Field,
        {
          control: form.control,
          name: "send_notification",
          render: ({ field: { onChange, value, ...field } }) => {
            return (0, import_jsx_runtime8.jsxs)(Form.Item, { children: [
              (0, import_jsx_runtime8.jsxs)("div", { className: "flex items-center", children: [
                (0, import_jsx_runtime8.jsx)(Form.Control, { className: "mr-4 self-start", children: (0, import_jsx_runtime8.jsx)(
                  Switch,
                  {
                    className: "mt-[2px]",
                    checked: !!value,
                    onCheckedChange: onChange,
                    ...field
                  }
                ) }),
                (0, import_jsx_runtime8.jsxs)("div", { className: "block", children: [
                  (0, import_jsx_runtime8.jsx)(Form.Label, { children: t2("orders.returns.sendNotification") }),
                  (0, import_jsx_runtime8.jsx)(Form.Hint, { className: "!mt-1", children: t2("orders.returns.sendNotificationHint") })
                ] })
              ] }),
              (0, import_jsx_runtime8.jsx)(Form.ErrorMessage, {})
            ] });
          }
        }
      ) }),
      (0, import_jsx_runtime8.jsx)("div", { className: "p-8" })
    ] }) }),
    (0, import_jsx_runtime8.jsx)(RouteFocusModal.Footer, { children: (0, import_jsx_runtime8.jsx)("div", { className: "flex w-full items-center justify-end gap-x-4", children: (0, import_jsx_runtime8.jsxs)("div", { className: "flex items-center justify-end gap-x-2", children: [
      (0, import_jsx_runtime8.jsx)(RouteFocusModal.Close, { asChild: true, children: (0, import_jsx_runtime8.jsx)(
        Button,
        {
          type: "button",
          onClick: () => IS_CANCELING = true,
          variant: "secondary",
          size: "small",
          children: t2("orders.claims.cancel.title")
        }
      ) }),
      (0, import_jsx_runtime8.jsx)(
        Button,
        {
          type: "submit",
          variant: "primary",
          size: "small",
          isLoading: isRequestLoading,
          children: t2("orders.claims.confirm")
        },
        "submit-button"
      )
    ] }) }) })
  ] }) });
};
var IS_REQUEST_RUNNING = false;
var ClaimCreate = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { t: t2 } = useTranslation();
  const { order } = useOrder(id, {
    fields: DEFAULT_FIELDS
  });
  const { order: preview } = useOrderPreview(id);
  const [activeClaimId, setActiveClaimId] = (0, import_react.useState)();
  const { mutateAsync: createClaim } = useCreateClaim(order.id);
  const { claim } = useClaim(activeClaimId, void 0, {
    enabled: !!activeClaimId
  });
  const { return: orderReturn } = useReturn(claim == null ? void 0 : claim.return_id, void 0, {
    enabled: !!(claim == null ? void 0 : claim.return_id)
  });
  (0, import_react.useEffect)(() => {
    async function run() {
      if (IS_REQUEST_RUNNING || !preview) {
        return;
      }
      if (preview.order_change) {
        if (preview.order_change.change_type === "claim") {
          setActiveClaimId(preview.order_change.claim_id);
        } else {
          navigate(`/orders/${preview.id}`, { replace: true });
          toast.error(t2("orders.claims.activeChangeError"));
        }
        return;
      }
      IS_REQUEST_RUNNING = true;
      try {
        const { claim: createdClaim } = await createClaim({
          order_id: preview.id,
          type: "replace"
        });
        setActiveClaimId(createdClaim.id);
      } catch (e) {
        toast.error(e.message);
        navigate(`/orders/${preview.id}`, { replace: true });
      } finally {
        IS_REQUEST_RUNNING = false;
      }
    }
    run();
  }, [preview]);
  return (0, import_jsx_runtime9.jsx)(RouteFocusModal, { children: claim && preview && order && (0, import_jsx_runtime9.jsx)(
    ClaimCreateForm,
    {
      order,
      claim,
      preview,
      orderReturn
    }
  ) });
};
export {
  ClaimCreate as Component
};
//# sourceMappingURL=order-create-claim-NHBXMO53-MMBRRI7Z.js.map
