import {
  formatProvider
} from "./chunk-LVAKEKGS.js";
import {
  Text<PERSON><PERSON>,
  TextHeader
} from "./chunk-C43B7AQX.js";
import "./chunk-EGRHWZRV.js";
import "./chunk-S5JEKJNE.js";
import {
  _DataTable,
  useDataTable
} from "./chunk-VCX3BVQR.js";
import "./chunk-VCBFQV64.js";
import "./chunk-QLJZR2JY.js";
import "./chunk-X3TOWPPJ.js";
import "./chunk-EGRHWZRV.js";
import {
  KeyboundForm
} from "./chunk-DP54EP6X.js";
import {
  useQueryParams
} from "./chunk-32T72GVU.js";
import "./chunk-3LRISSP5.js";
import "./chunk-P5T2IZP5.js";
import "./chunk-I7242KR3.js";
import "./chunk-EB3HY52D.js";
import {
  useDateTableFilters
} from "./chunk-IONS3C54.js";
import "./chunk-XNFM7P3M.js";
import "./chunk-ZIXJCBL3.js";
import "./chunk-2TO4KOWC.js";
import "./chunk-4VJTA5FM.js";
import "./chunk-MX43XOWY.js";
import "./chunk-QX6SXRUW.js";
import "./chunk-7ANVLPZR.js";
import "./chunk-2E2FUO6N.js";
import {
  RouteFocusModal,
  useRouteModal
} from "./chunk-MVVOBQIC.js";
import {
  t
} from "./chunk-WHQIBI5S.js";
import {
  arrayType,
  objectType,
  stringType
} from "./chunk-4XXECALA.js";
import "./chunk-YXT43UJF.js";
import "./chunk-66DVUN72.js";
import "./chunk-DL4QDYPT.js";
import "./chunk-NV2N3EWM.js";
import "./chunk-BF7OBKIN.js";
import "./chunk-7UAYECTW.js";
import "./chunk-Y3NYV3NU.js";
import "./chunk-MPXR7HT5.js";
import {
  useForm
} from "./chunk-XXJU43CK.js";
import "./chunk-WISME5HP.js";
import "./chunk-WAUSOHFQ.js";
import "./chunk-3ORK3QZ4.js";
import "./chunk-3A5TVVNI.js";
import "./chunk-EZ62MM7J.js";
import "./chunk-3R6LYIXM.js";
import "./chunk-2P2O4TMQ.js";
import "./chunk-KI3HEITX.js";
import "./chunk-2VUCAO7L.js";
import "./chunk-PTIULTKD.js";
import "./chunk-KISXY2LD.js";
import "./chunk-L7C3XPHJ.js";
import "./chunk-T7MG2EIJ.js";
import "./chunk-MER4VYPR.js";
import "./chunk-2AWKOUCD.js";
import "./chunk-YL3TYBFR.js";
import "./chunk-TZWW72YW.js";
import "./chunk-MSQ25CWB.js";
import {
  useFulfillmentProviders,
  useStockLocation,
  useUpdateStockLocationFulfillmentProviders
} from "./chunk-ONYSAQ5Z.js";
import "./chunk-NIH7SAXN.js";
import "./chunk-UEJFWAFE.js";
import "./chunk-GR55MFKT.js";
import "./chunk-AAFHKNJG.js";
import "./chunk-7XDBFDTZ.js";
import "./chunk-3JKGO5XL.js";
import "./chunk-OISUTS7G.js";
import "./chunk-AJYMIHLQ.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import "./chunk-JFCIYOH4.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-5GF3RGIE.js";
import {
  useParams
} from "./chunk-T7YBVUWZ.js";
import {
  Button,
  Checkbox,
  createColumnHelper,
  toast
} from "./chunk-YP2LLXWB.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-ZSQ362B4.js";
import {
  keepPreviousData
} from "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/location-fulfillment-providers-AMCSLQEZ.mjs
var import_react = __toESM(require_react(), 1);
var import_react2 = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var columnHelper = createColumnHelper();
var useFulfillmentProviderTableColumns = () => {
  const { t: t2 } = useTranslation();
  return (0, import_react2.useMemo)(
    () => [
      columnHelper.accessor("id", {
        header: () => (0, import_jsx_runtime.jsx)(TextHeader, { text: "Provider" }),
        cell: ({ getValue }) => (0, import_jsx_runtime.jsx)(TextCell, { text: formatProvider(getValue()) })
      })
    ],
    [t2]
  );
};
var useFulfillmentProvidersTableQuery = ({
  prefix,
  pageSize = 20
}) => {
  const queryObject = useQueryParams(
    ["offset", "q", "stock_location_id"],
    prefix
  );
  const { offset, q, stock_location_id } = queryObject;
  const searchParams = {
    limit: pageSize,
    offset: offset ? Number(offset) : 0,
    stock_location_id,
    q
  };
  return {
    searchParams,
    raw: queryObject
  };
};
var EditFulfillmentProvidersFormSchema = objectType({
  fulfillment_providers: arrayType(stringType()).optional()
});
var PAGE_SIZE = 50;
var LocationEditFulfillmentProvidersForm = ({
  location
}) => {
  var _a, _b;
  const { t: t2 } = useTranslation();
  const { handleSuccess } = useRouteModal();
  const form = useForm({
    defaultValues: {
      fulfillment_providers: ((_a = location.fulfillment_providers) == null ? void 0 : _a.map((fp) => fp.id)) ?? []
    },
    resolver: t(EditFulfillmentProvidersFormSchema)
  });
  const { setValue } = form;
  const initialState = ((_b = location.fulfillment_providers) == null ? void 0 : _b.reduce((acc, curr) => {
    acc[curr.id] = true;
    return acc;
  }, {})) ?? {};
  const [rowSelection, setRowSelection] = (0, import_react.useState)(initialState);
  const handleRowSelectionChange = (updater) => {
    const ids = typeof updater === "function" ? updater(rowSelection) : updater;
    setValue("fulfillment_providers", Object.keys(ids), {
      shouldDirty: true,
      shouldTouch: true
    });
    setRowSelection(ids);
  };
  const { searchParams, raw } = useFulfillmentProvidersTableQuery({
    pageSize: PAGE_SIZE
  });
  const { fulfillment_providers, count, isLoading, isError, error } = useFulfillmentProviders(
    { ...searchParams, is_enabled: true },
    { placeholderData: keepPreviousData }
  );
  const filters = useDateTableFilters();
  const columns = useColumns();
  const { table } = useDataTable({
    data: fulfillment_providers ?? [],
    columns,
    count,
    enablePagination: true,
    enableRowSelection: true,
    rowSelection: {
      state: rowSelection,
      updater: handleRowSelectionChange
    },
    getRowId: (row) => row.id,
    pageSize: PAGE_SIZE
  });
  const { mutateAsync, isPending: isMutating } = useUpdateStockLocationFulfillmentProviders(location.id);
  const handleSubmit = form.handleSubmit(async (data) => {
    var _a2;
    const originalIds = (_a2 = location.fulfillment_providers) == null ? void 0 : _a2.map((sc) => sc.id);
    const arr = data.fulfillment_providers ?? [];
    await mutateAsync(
      {
        add: arr.filter((i) => !(originalIds == null ? void 0 : originalIds.includes(i))),
        remove: originalIds == null ? void 0 : originalIds.filter((i) => !arr.includes(i))
      },
      {
        onSuccess: ({ stock_location }) => {
          toast.success(t2("stockLocations.fulfillmentProviders.successToast"));
          handleSuccess(`/settings/locations/${stock_location.id}`);
        },
        onError: (e) => {
          toast.error(e.message);
        }
      }
    );
  });
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime2.jsx)(RouteFocusModal.Form, { form, children: (0, import_jsx_runtime2.jsxs)(KeyboundForm, { onSubmit: handleSubmit, className: "flex size-full flex-col", children: [
    (0, import_jsx_runtime2.jsx)(RouteFocusModal.Header, {}),
    (0, import_jsx_runtime2.jsx)(RouteFocusModal.Body, { className: "flex flex-1 flex-col overflow-auto", children: (0, import_jsx_runtime2.jsx)(
      _DataTable,
      {
        table,
        columns,
        pageSize: PAGE_SIZE,
        isLoading,
        count,
        filters,
        search: "autofocus",
        pagination: true,
        orderBy: [{ key: "id", label: t2("fields.id") }],
        queryObject: raw,
        layout: "fill"
      }
    ) }),
    (0, import_jsx_runtime2.jsx)(RouteFocusModal.Footer, { children: (0, import_jsx_runtime2.jsxs)("div", { className: "flex items-center justify-end gap-x-2", children: [
      (0, import_jsx_runtime2.jsx)(RouteFocusModal.Close, { asChild: true, children: (0, import_jsx_runtime2.jsx)(Button, { size: "small", variant: "secondary", type: "button", children: t2("actions.cancel") }) }),
      (0, import_jsx_runtime2.jsx)(Button, { size: "small", isLoading: isMutating, type: "submit", children: t2("actions.save") })
    ] }) })
  ] }) });
};
var columnHelper2 = createColumnHelper();
var useColumns = () => {
  const columns = useFulfillmentProviderTableColumns();
  return (0, import_react.useMemo)(
    () => [
      columnHelper2.display({
        id: "select",
        header: ({ table }) => {
          return (0, import_jsx_runtime2.jsx)(
            Checkbox,
            {
              checked: table.getIsSomePageRowsSelected() ? "indeterminate" : table.getIsAllPageRowsSelected(),
              onCheckedChange: (value) => table.toggleAllPageRowsSelected(!!value)
            }
          );
        },
        cell: ({ row }) => {
          return (0, import_jsx_runtime2.jsx)(
            Checkbox,
            {
              checked: row.getIsSelected(),
              onCheckedChange: (value) => row.toggleSelected(!!value),
              onClick: (e) => {
                e.stopPropagation();
              }
            }
          );
        }
      }),
      ...columns
    ],
    [columns]
  );
};
var LocationFulfillmentProviders = () => {
  const { location_id } = useParams();
  const { stock_location, isPending, isError, error } = useStockLocation(
    location_id,
    { fields: "id,*fulfillment_providers" }
  );
  const ready = !isPending && !!stock_location;
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime3.jsx)(RouteFocusModal, { children: ready && (0, import_jsx_runtime3.jsx)(LocationEditFulfillmentProvidersForm, { location: stock_location }) });
};
export {
  LocationFulfillmentProviders as Component
};
//# sourceMappingURL=location-fulfillment-providers-AMCSLQEZ-J6BUK7TF.js.map
