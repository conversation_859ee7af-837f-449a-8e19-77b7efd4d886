import {
  require_debounce
} from "./chunk-RQF55WOK.js";
import {
  keepPreviousData,
  useInfiniteQuery,
  useQuery
} from "./chunk-R35JBZ3G.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/chunk-YIZSVS2R.mjs
var import_debounce = __toESM(require_debounce(), 1);
var import_react = __toESM(require_react(), 1);
var useDebouncedSearch = () => {
  const [searchValue, onSearchValueChange] = (0, import_react.useState)("");
  const [debouncedQuery, setDebouncedQuery] = (0, import_react.useState)("");
  const debouncedUpdate = (0, import_react.useCallback)(
    (0, import_debounce.default)((query) => setDebouncedQuery(query), 300),
    []
  );
  (0, import_react.useEffect)(() => {
    debouncedUpdate(searchValue);
    return () => debouncedUpdate.cancel();
  }, [searchValue, debouncedUpdate]);
  return {
    searchValue,
    onSearchValueChange,
    query: debouncedQuery || void 0
  };
};
var useComboboxData = ({
  queryKey,
  queryFn,
  getOptions,
  defaultValue,
  defaultValueKey,
  pageSize = 10,
  enabled = true
}) => {
  const { searchValue, onSearchValueChange, query } = useDebouncedSearch();
  const queryInitialDataBy = defaultValueKey || "id";
  const { data: initialData } = useQuery({
    queryKey,
    queryFn: async () => {
      return queryFn({
        [queryInitialDataBy]: defaultValue,
        limit: Array.isArray(defaultValue) ? defaultValue.length : 1
      });
    },
    enabled: !!defaultValue && enabled
  });
  const { data, ...rest } = useInfiniteQuery({
    queryKey: [...queryKey, query],
    queryFn: async ({ pageParam = 0 }) => {
      return await queryFn({
        q: query,
        limit: pageSize,
        offset: pageParam
      });
    },
    initialPageParam: 0,
    getNextPageParam: (lastPage) => {
      const moreItemsExist = lastPage.count > lastPage.offset + lastPage.limit;
      return moreItemsExist ? lastPage.offset + lastPage.limit : void 0;
    },
    placeholderData: keepPreviousData,
    enabled
  });
  const options = (data == null ? void 0 : data.pages.flatMap((page) => getOptions(page))) ?? [];
  const defaultOptions = initialData ? getOptions(initialData) : [];
  const disabled = !rest.isPending && !options.length && !searchValue || !enabled;
  if (defaultValue && defaultOptions.length && !searchValue) {
    defaultOptions.forEach((option) => {
      if (!options.find((o) => o.value === option.value)) {
        options.unshift(option);
      }
    });
  }
  return {
    options,
    searchValue,
    onSearchValueChange,
    disabled,
    ...rest
  };
};

export {
  useDebouncedSearch,
  useComboboxData
};
//# sourceMappingURL=chunk-CFEMRZCK.js.map
