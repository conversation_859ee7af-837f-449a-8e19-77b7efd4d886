import {
  KeyboundForm
} from "./chunk-DP54EP6X.js";
import {
  RouteDrawer,
  useRouteModal
} from "./chunk-MVVOBQIC.js";
import {
  t
} from "./chunk-WHQIBI5S.js";
import {
  objectType,
  stringType
} from "./chunk-4XXECALA.js";
import {
  ConditionalTooltip
} from "./chunk-66DVUN72.js";
import {
  Form,
  useForm
} from "./chunk-XXJU43CK.js";
import {
  useCustomer,
  useUpdateCustomer
} from "./chunk-2AWKOUCD.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import "./chunk-JFCIYOH4.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-5GF3RGIE.js";
import {
  useParams
} from "./chunk-T7YBVUWZ.js";
import {
  <PERSON><PERSON>,
  Heading,
  Input,
  toast
} from "./chunk-YP2LLXWB.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-ZSQ362B4.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/customer-edit-FJNPYFCK.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var EditCustomerSchema = objectType({
  email: stringType().email(),
  first_name: stringType().optional(),
  last_name: stringType().optional(),
  company_name: stringType().optional(),
  phone: stringType().optional()
});
var EditCustomerForm = ({ customer }) => {
  const { t: t2 } = useTranslation();
  const { handleSuccess } = useRouteModal();
  const form = useForm({
    defaultValues: {
      email: customer.email || "",
      first_name: customer.first_name || "",
      last_name: customer.last_name || "",
      company_name: customer.company_name || "",
      phone: customer.phone || ""
    },
    resolver: t(EditCustomerSchema)
  });
  const { mutateAsync, isPending } = useUpdateCustomer(customer.id);
  const handleSubmit = form.handleSubmit(async (data) => {
    await mutateAsync(
      {
        email: customer.has_account ? void 0 : data.email,
        first_name: data.first_name || null,
        last_name: data.last_name || null,
        phone: data.phone || null,
        company_name: data.company_name || null
      },
      {
        onSuccess: ({ customer: customer2 }) => {
          toast.success(
            t2("customers.edit.successToast", {
              email: customer2.email
            })
          );
          handleSuccess();
        },
        onError: (error) => {
          toast.error(error.message);
        }
      }
    );
  });
  return (0, import_jsx_runtime.jsx)(RouteDrawer.Form, { form, children: (0, import_jsx_runtime.jsxs)(KeyboundForm, { onSubmit: handleSubmit, className: "flex flex-1 flex-col", children: [
    (0, import_jsx_runtime.jsx)(RouteDrawer.Body, { children: (0, import_jsx_runtime.jsxs)("div", { className: "flex flex-col gap-y-4", children: [
      (0, import_jsx_runtime.jsx)(
        Form.Field,
        {
          control: form.control,
          name: "email",
          render: ({ field }) => {
            return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
              (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("fields.email") }),
              (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(
                ConditionalTooltip,
                {
                  showTooltip: customer.has_account,
                  content: t2("customers.edit.emailDisabledTooltip"),
                  children: (0, import_jsx_runtime.jsx)(Input, { ...field, disabled: customer.has_account })
                }
              ) }),
              (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
            ] });
          }
        }
      ),
      (0, import_jsx_runtime.jsx)(
        Form.Field,
        {
          control: form.control,
          name: "first_name",
          render: ({ field }) => {
            return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
              (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("fields.firstName") }),
              (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(Input, { ...field }) }),
              (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
            ] });
          }
        }
      ),
      (0, import_jsx_runtime.jsx)(
        Form.Field,
        {
          control: form.control,
          name: "last_name",
          render: ({ field }) => {
            return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
              (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("fields.lastName") }),
              (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(Input, { ...field }) }),
              (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
            ] });
          }
        }
      ),
      (0, import_jsx_runtime.jsx)(
        Form.Field,
        {
          control: form.control,
          name: "company_name",
          render: ({ field }) => {
            return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
              (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("fields.company") }),
              (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(Input, { ...field }) }),
              (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
            ] });
          }
        }
      ),
      (0, import_jsx_runtime.jsx)(
        Form.Field,
        {
          control: form.control,
          name: "phone",
          render: ({ field }) => {
            return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
              (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("fields.phone") }),
              (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(Input, { ...field }) }),
              (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
            ] });
          }
        }
      )
    ] }) }),
    (0, import_jsx_runtime.jsx)(RouteDrawer.Footer, { children: (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center justify-end gap-x-2", children: [
      (0, import_jsx_runtime.jsx)(RouteDrawer.Close, { asChild: true, children: (0, import_jsx_runtime.jsx)(Button, { variant: "secondary", size: "small", children: t2("actions.cancel") }) }),
      (0, import_jsx_runtime.jsx)(
        Button,
        {
          isLoading: isPending,
          type: "submit",
          variant: "primary",
          size: "small",
          children: t2("actions.save")
        }
      )
    ] }) })
  ] }) });
};
var CustomerEdit = () => {
  const { t: t2 } = useTranslation();
  const { id } = useParams();
  const { customer, isLoading, isError, error } = useCustomer(id);
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime2.jsxs)(RouteDrawer, { children: [
    (0, import_jsx_runtime2.jsx)(RouteDrawer.Header, { children: (0, import_jsx_runtime2.jsx)(Heading, { children: t2("customers.edit.header") }) }),
    !isLoading && customer && (0, import_jsx_runtime2.jsx)(EditCustomerForm, { customer })
  ] });
};
export {
  CustomerEdit as Component
};
//# sourceMappingURL=customer-edit-FJNPYFCK-JJO7Z4XX.js.map
