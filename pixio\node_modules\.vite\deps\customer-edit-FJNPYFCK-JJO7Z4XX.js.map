{"version": 3, "sources": ["../../@medusajs/dashboard/dist/customer-edit-FJNPYFCK.mjs"], "sourcesContent": ["import {\n  KeyboundForm\n} from \"./chunk-6HTZNHPT.mjs\";\nimport {\n  RouteDrawer,\n  useRouteModal\n} from \"./chunk-4TC5YS65.mjs\";\nimport {\n  ConditionalTooltip\n} from \"./chunk-OC7BQLYI.mjs\";\nimport {\n  Form\n} from \"./chunk-OBQI23QM.mjs\";\nimport {\n  useCustomer,\n  useUpdateCustomer\n} from \"./chunk-F6PXCY3N.mjs\";\nimport \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-RPUOO7AV.mjs\";\n\n// src/routes/customers/customer-edit/customer-edit.tsx\nimport { Heading } from \"@medusajs/ui\";\nimport { useTranslation as useTranslation2 } from \"react-i18next\";\nimport { useParams } from \"react-router-dom\";\n\n// src/routes/customers/customer-edit/components/edit-customer-form/edit-customer-form.tsx\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { Button, Input, toast } from \"@medusajs/ui\";\nimport { useForm } from \"react-hook-form\";\nimport { useTranslation } from \"react-i18next\";\nimport * as zod from \"zod\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar EditCustomerSchema = zod.object({\n  email: zod.string().email(),\n  first_name: zod.string().optional(),\n  last_name: zod.string().optional(),\n  company_name: zod.string().optional(),\n  phone: zod.string().optional()\n});\nvar EditCustomerForm = ({ customer }) => {\n  const { t } = useTranslation();\n  const { handleSuccess } = useRouteModal();\n  const form = useForm({\n    defaultValues: {\n      email: customer.email || \"\",\n      first_name: customer.first_name || \"\",\n      last_name: customer.last_name || \"\",\n      company_name: customer.company_name || \"\",\n      phone: customer.phone || \"\"\n    },\n    resolver: zodResolver(EditCustomerSchema)\n  });\n  const { mutateAsync, isPending } = useUpdateCustomer(customer.id);\n  const handleSubmit = form.handleSubmit(async (data) => {\n    await mutateAsync(\n      {\n        email: customer.has_account ? void 0 : data.email,\n        first_name: data.first_name || null,\n        last_name: data.last_name || null,\n        phone: data.phone || null,\n        company_name: data.company_name || null\n      },\n      {\n        onSuccess: ({ customer: customer2 }) => {\n          toast.success(\n            t(\"customers.edit.successToast\", {\n              email: customer2.email\n            })\n          );\n          handleSuccess();\n        },\n        onError: (error) => {\n          toast.error(error.message);\n        }\n      }\n    );\n  });\n  return /* @__PURE__ */ jsx(RouteDrawer.Form, { form, children: /* @__PURE__ */ jsxs(KeyboundForm, { onSubmit: handleSubmit, className: \"flex flex-1 flex-col\", children: [\n    /* @__PURE__ */ jsx(RouteDrawer.Body, { children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex flex-col gap-y-4\", children: [\n      /* @__PURE__ */ jsx(\n        Form.Field,\n        {\n          control: form.control,\n          name: \"email\",\n          render: ({ field }) => {\n            return /* @__PURE__ */ jsxs(Form.Item, { children: [\n              /* @__PURE__ */ jsx(Form.Label, { children: t(\"fields.email\") }),\n              /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(\n                ConditionalTooltip,\n                {\n                  showTooltip: customer.has_account,\n                  content: t(\"customers.edit.emailDisabledTooltip\"),\n                  children: /* @__PURE__ */ jsx(Input, { ...field, disabled: customer.has_account })\n                }\n              ) }),\n              /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n            ] });\n          }\n        }\n      ),\n      /* @__PURE__ */ jsx(\n        Form.Field,\n        {\n          control: form.control,\n          name: \"first_name\",\n          render: ({ field }) => {\n            return /* @__PURE__ */ jsxs(Form.Item, { children: [\n              /* @__PURE__ */ jsx(Form.Label, { children: t(\"fields.firstName\") }),\n              /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(Input, { ...field }) }),\n              /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n            ] });\n          }\n        }\n      ),\n      /* @__PURE__ */ jsx(\n        Form.Field,\n        {\n          control: form.control,\n          name: \"last_name\",\n          render: ({ field }) => {\n            return /* @__PURE__ */ jsxs(Form.Item, { children: [\n              /* @__PURE__ */ jsx(Form.Label, { children: t(\"fields.lastName\") }),\n              /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(Input, { ...field }) }),\n              /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n            ] });\n          }\n        }\n      ),\n      /* @__PURE__ */ jsx(\n        Form.Field,\n        {\n          control: form.control,\n          name: \"company_name\",\n          render: ({ field }) => {\n            return /* @__PURE__ */ jsxs(Form.Item, { children: [\n              /* @__PURE__ */ jsx(Form.Label, { children: t(\"fields.company\") }),\n              /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(Input, { ...field }) }),\n              /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n            ] });\n          }\n        }\n      ),\n      /* @__PURE__ */ jsx(\n        Form.Field,\n        {\n          control: form.control,\n          name: \"phone\",\n          render: ({ field }) => {\n            return /* @__PURE__ */ jsxs(Form.Item, { children: [\n              /* @__PURE__ */ jsx(Form.Label, { children: t(\"fields.phone\") }),\n              /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(Input, { ...field }) }),\n              /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n            ] });\n          }\n        }\n      )\n    ] }) }),\n    /* @__PURE__ */ jsx(RouteDrawer.Footer, { children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center justify-end gap-x-2\", children: [\n      /* @__PURE__ */ jsx(RouteDrawer.Close, { asChild: true, children: /* @__PURE__ */ jsx(Button, { variant: \"secondary\", size: \"small\", children: t(\"actions.cancel\") }) }),\n      /* @__PURE__ */ jsx(\n        Button,\n        {\n          isLoading: isPending,\n          type: \"submit\",\n          variant: \"primary\",\n          size: \"small\",\n          children: t(\"actions.save\")\n        }\n      )\n    ] }) })\n  ] }) });\n};\n\n// src/routes/customers/customer-edit/customer-edit.tsx\nimport { jsx as jsx2, jsxs as jsxs2 } from \"react/jsx-runtime\";\nvar CustomerEdit = () => {\n  const { t } = useTranslation2();\n  const { id } = useParams();\n  const { customer, isLoading, isError, error } = useCustomer(id);\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsxs2(RouteDrawer, { children: [\n    /* @__PURE__ */ jsx2(RouteDrawer.Header, { children: /* @__PURE__ */ jsx2(Heading, { children: t(\"customers.edit.header\") }) }),\n    !isLoading && customer && /* @__PURE__ */ jsx2(EditCustomerForm, { customer })\n  ] });\n};\nexport {\n  CustomerEdit as Component\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiCA,yBAA0B;AA+I1B,IAAAA,sBAA2C;AA9I3C,IAAI,qBAAyB,WAAO;AAAA,EAClC,OAAW,WAAO,EAAE,MAAM;AAAA,EAC1B,YAAgB,WAAO,EAAE,SAAS;AAAA,EAClC,WAAe,WAAO,EAAE,SAAS;AAAA,EACjC,cAAkB,WAAO,EAAE,SAAS;AAAA,EACpC,OAAW,WAAO,EAAE,SAAS;AAC/B,CAAC;AACD,IAAI,mBAAmB,CAAC,EAAE,SAAS,MAAM;AACvC,QAAM,EAAE,GAAAC,GAAE,IAAI,eAAe;AAC7B,QAAM,EAAE,cAAc,IAAI,cAAc;AACxC,QAAM,OAAO,QAAQ;AAAA,IACnB,eAAe;AAAA,MACb,OAAO,SAAS,SAAS;AAAA,MACzB,YAAY,SAAS,cAAc;AAAA,MACnC,WAAW,SAAS,aAAa;AAAA,MACjC,cAAc,SAAS,gBAAgB;AAAA,MACvC,OAAO,SAAS,SAAS;AAAA,IAC3B;AAAA,IACA,UAAU,EAAY,kBAAkB;AAAA,EAC1C,CAAC;AACD,QAAM,EAAE,aAAa,UAAU,IAAI,kBAAkB,SAAS,EAAE;AAChE,QAAM,eAAe,KAAK,aAAa,OAAO,SAAS;AACrD,UAAM;AAAA,MACJ;AAAA,QACE,OAAO,SAAS,cAAc,SAAS,KAAK;AAAA,QAC5C,YAAY,KAAK,cAAc;AAAA,QAC/B,WAAW,KAAK,aAAa;AAAA,QAC7B,OAAO,KAAK,SAAS;AAAA,QACrB,cAAc,KAAK,gBAAgB;AAAA,MACrC;AAAA,MACA;AAAA,QACE,WAAW,CAAC,EAAE,UAAU,UAAU,MAAM;AACtC,gBAAM;AAAA,YACJA,GAAE,+BAA+B;AAAA,cAC/B,OAAO,UAAU;AAAA,YACnB,CAAC;AAAA,UACH;AACA,wBAAc;AAAA,QAChB;AAAA,QACA,SAAS,CAAC,UAAU;AAClB,gBAAM,MAAM,MAAM,OAAO;AAAA,QAC3B;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AACD,aAAuB,wBAAI,YAAY,MAAM,EAAE,MAAM,cAA0B,yBAAK,cAAc,EAAE,UAAU,cAAc,WAAW,wBAAwB,UAAU;AAAA,QACvJ,wBAAI,YAAY,MAAM,EAAE,cAA0B,yBAAK,OAAO,EAAE,WAAW,yBAAyB,UAAU;AAAA,UAC5G;AAAA,QACd,KAAK;AAAA,QACL;AAAA,UACE,SAAS,KAAK;AAAA,UACd,MAAM;AAAA,UACN,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,uBAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,kBACjC,wBAAI,KAAK,OAAO,EAAE,UAAUA,GAAE,cAAc,EAAE,CAAC;AAAA,kBAC/C,wBAAI,KAAK,SAAS,EAAE,cAA0B;AAAA,gBAC5D;AAAA,gBACA;AAAA,kBACE,aAAa,SAAS;AAAA,kBACtB,SAASA,GAAE,qCAAqC;AAAA,kBAChD,cAA0B,wBAAI,OAAO,EAAE,GAAG,OAAO,UAAU,SAAS,YAAY,CAAC;AAAA,gBACnF;AAAA,cACF,EAAE,CAAC;AAAA,kBACa,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,YAC3C,EAAE,CAAC;AAAA,UACL;AAAA,QACF;AAAA,MACF;AAAA,UACgB;AAAA,QACd,KAAK;AAAA,QACL;AAAA,UACE,SAAS,KAAK;AAAA,UACd,MAAM;AAAA,UACN,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,uBAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,kBACjC,wBAAI,KAAK,OAAO,EAAE,UAAUA,GAAE,kBAAkB,EAAE,CAAC;AAAA,kBACnD,wBAAI,KAAK,SAAS,EAAE,cAA0B,wBAAI,OAAO,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC;AAAA,kBACxE,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,YAC3C,EAAE,CAAC;AAAA,UACL;AAAA,QACF;AAAA,MACF;AAAA,UACgB;AAAA,QACd,KAAK;AAAA,QACL;AAAA,UACE,SAAS,KAAK;AAAA,UACd,MAAM;AAAA,UACN,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,uBAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,kBACjC,wBAAI,KAAK,OAAO,EAAE,UAAUA,GAAE,iBAAiB,EAAE,CAAC;AAAA,kBAClD,wBAAI,KAAK,SAAS,EAAE,cAA0B,wBAAI,OAAO,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC;AAAA,kBACxE,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,YAC3C,EAAE,CAAC;AAAA,UACL;AAAA,QACF;AAAA,MACF;AAAA,UACgB;AAAA,QACd,KAAK;AAAA,QACL;AAAA,UACE,SAAS,KAAK;AAAA,UACd,MAAM;AAAA,UACN,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,uBAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,kBACjC,wBAAI,KAAK,OAAO,EAAE,UAAUA,GAAE,gBAAgB,EAAE,CAAC;AAAA,kBACjD,wBAAI,KAAK,SAAS,EAAE,cAA0B,wBAAI,OAAO,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC;AAAA,kBACxE,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,YAC3C,EAAE,CAAC;AAAA,UACL;AAAA,QACF;AAAA,MACF;AAAA,UACgB;AAAA,QACd,KAAK;AAAA,QACL;AAAA,UACE,SAAS,KAAK;AAAA,UACd,MAAM;AAAA,UACN,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,uBAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,kBACjC,wBAAI,KAAK,OAAO,EAAE,UAAUA,GAAE,cAAc,EAAE,CAAC;AAAA,kBAC/C,wBAAI,KAAK,SAAS,EAAE,cAA0B,wBAAI,OAAO,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC;AAAA,kBACxE,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,YAC3C,EAAE,CAAC;AAAA,UACL;AAAA,QACF;AAAA,MACF;AAAA,IACF,EAAE,CAAC,EAAE,CAAC;AAAA,QACU,wBAAI,YAAY,QAAQ,EAAE,cAA0B,yBAAK,OAAO,EAAE,WAAW,yCAAyC,UAAU;AAAA,UAC9H,wBAAI,YAAY,OAAO,EAAE,SAAS,MAAM,cAA0B,wBAAI,QAAQ,EAAE,SAAS,aAAa,MAAM,SAAS,UAAUA,GAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;AAAA,UACvJ;AAAA,QACd;AAAA,QACA;AAAA,UACE,WAAW;AAAA,UACX,MAAM;AAAA,UACN,SAAS;AAAA,UACT,MAAM;AAAA,UACN,UAAUA,GAAE,cAAc;AAAA,QAC5B;AAAA,MACF;AAAA,IACF,EAAE,CAAC,EAAE,CAAC;AAAA,EACR,EAAE,CAAC,EAAE,CAAC;AACR;AAIA,IAAI,eAAe,MAAM;AACvB,QAAM,EAAE,GAAAA,GAAE,IAAI,eAAgB;AAC9B,QAAM,EAAE,GAAG,IAAI,UAAU;AACzB,QAAM,EAAE,UAAU,WAAW,SAAS,MAAM,IAAI,YAAY,EAAE;AAC9D,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,oBAAAC,MAAM,aAAa,EAAE,UAAU;AAAA,QACpC,oBAAAC,KAAK,YAAY,QAAQ,EAAE,cAA0B,oBAAAA,KAAK,SAAS,EAAE,UAAUF,GAAE,uBAAuB,EAAE,CAAC,EAAE,CAAC;AAAA,IAC9H,CAAC,aAAa,gBAA4B,oBAAAE,KAAK,kBAAkB,EAAE,SAAS,CAAC;AAAA,EAC/E,EAAE,CAAC;AACL;", "names": ["import_jsx_runtime", "t", "jsxs2", "jsx2"]}