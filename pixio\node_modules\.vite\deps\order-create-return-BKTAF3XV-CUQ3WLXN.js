import {
  ReturnShippingPlaceholder
} from "./chunk-YD5FTRQU.js";
import {
  MoneyAmountCell
} from "./chunk-5AXVXNEZ.js";
import {
  getReturnableQuantity
} from "./chunk-PM26KX6Y.js";
import {
  DEFAULT_FIELDS
} from "./chunk-UKYP5JCA.js";
import {
  useAddReturnItem,
  useAddReturnShipping,
  useCancelReturnRequest,
  useConfirmReturnRequest,
  useDeleteReturnShipping,
  useInitiateReturn,
  useRemoveReturnItem,
  useReturn,
  useUpdateReturn,
  useUpdateReturnItem,
  useUpdateReturnShipping
} from "./chunk-YJDYMRQS.js";
import {
  getStylizedAmount
} from "./chunk-UDMOPZAP.js";
import {
  ProductCell,
  ProductHeader
} from "./chunk-NVCSASGM.js";
import "./chunk-EGRHWZRV.js";
import "./chunk-S5JEKJNE.js";
import {
  _DataTable,
  useDataTable
} from "./chunk-VCX3BVQR.js";
import "./chunk-VCBFQV64.js";
import "./chunk-QLJZR2JY.js";
import "./chunk-X3TOWPPJ.js";
import {
  currencies
} from "./chunk-H3DTEG3J.js";
import {
  Combobox
} from "./chunk-RC2DY4WG.js";
import "./chunk-EGRHWZRV.js";
import {
  KeyboundForm
} from "./chunk-DP54EP6X.js";
import {
  useQueryParams
} from "./chunk-32T72GVU.js";
import "./chunk-MX43XOWY.js";
import "./chunk-QX6SXRUW.js";
import "./chunk-7ANVLPZR.js";
import "./chunk-2E2FUO6N.js";
import {
  RouteFocusModal,
  StackedFocusModal,
  useRouteModal,
  useStackedModal
} from "./chunk-MVVOBQIC.js";
import {
  useReturnReasons
} from "./chunk-EPRCCFRP.js";
import "./chunk-IA4ROPJA.js";
import {
  t
} from "./chunk-WHQIBI5S.js";
import {
  z
} from "./chunk-4XXECALA.js";
import {
  Thumbnail
} from "./chunk-6GQUHAET.js";
import "./chunk-YXT43UJF.js";
import {
  ActionMenu
} from "./chunk-CFRQOB2M.js";
import "./chunk-66DVUN72.js";
import "./chunk-DL4QDYPT.js";
import "./chunk-NV2N3EWM.js";
import "./chunk-BF7OBKIN.js";
import "./chunk-7UAYECTW.js";
import "./chunk-Y3NYV3NU.js";
import "./chunk-MPXR7HT5.js";
import {
  Form,
  useFieldArray,
  useForm
} from "./chunk-XXJU43CK.js";
import {
  useShippingOptions
} from "./chunk-MSQ25CWB.js";
import {
  useStockLocations
} from "./chunk-ONYSAQ5Z.js";
import {
  useOrder,
  useOrderPreview
} from "./chunk-NIH7SAXN.js";
import "./chunk-UEJFWAFE.js";
import "./chunk-AJYMIHLQ.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import {
  sdk
} from "./chunk-JFCIYOH4.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-5GF3RGIE.js";
import {
  useNavigate,
  useParams
} from "./chunk-T7YBVUWZ.js";
import {
  Alert,
  Button,
  ChatBubble,
  Checkbox,
  CurrencyInput2 as CurrencyInput,
  DocumentText,
  Heading,
  IconButton,
  Input,
  PencilSquare,
  Switch,
  Text,
  XCircle,
  XMark,
  createColumnHelper,
  toast,
  usePrompt
} from "./chunk-YP2LLXWB.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-ZSQ362B4.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/order-create-return-BKTAF3XV.mjs
var import_react = __toESM(require_react(), 1);
var import_react2 = __toESM(require_react(), 1);
var import_react3 = __toESM(require_react(), 1);
var import_react4 = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime4 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime5 = __toESM(require_jsx_runtime(), 1);
var columnHelper = createColumnHelper();
var useReturnItemTableColumns = (currencyCode) => {
  const { t: t2 } = useTranslation();
  return (0, import_react4.useMemo)(
    () => [
      columnHelper.display({
        id: "select",
        header: ({ table }) => {
          return (0, import_jsx_runtime.jsx)(
            Checkbox,
            {
              checked: table.getIsSomePageRowsSelected() ? "indeterminate" : table.getIsAllPageRowsSelected(),
              onCheckedChange: (value) => table.toggleAllPageRowsSelected(!!value)
            }
          );
        },
        cell: ({ row }) => {
          const isSelectable = row.getCanSelect();
          return (0, import_jsx_runtime.jsx)(
            Checkbox,
            {
              disabled: !isSelectable,
              checked: row.getIsSelected(),
              onCheckedChange: (value) => row.toggleSelected(!!value),
              onClick: (e) => {
                e.stopPropagation();
              }
            }
          );
        }
      }),
      columnHelper.display({
        id: "product",
        header: () => (0, import_jsx_runtime.jsx)(ProductHeader, {}),
        cell: ({ row }) => (0, import_jsx_runtime.jsx)(
          ProductCell,
          {
            product: {
              thumbnail: row.original.thumbnail,
              title: row.original.product_title
            }
          }
        )
      }),
      columnHelper.accessor("variant.sku", {
        header: t2("fields.sku"),
        cell: ({ getValue }) => {
          return getValue() || "-";
        }
      }),
      columnHelper.accessor("variant.title", {
        header: t2("fields.variant")
      }),
      columnHelper.accessor("quantity", {
        header: () => (0, import_jsx_runtime.jsx)("div", { className: "flex size-full items-center overflow-hidden text-right", children: (0, import_jsx_runtime.jsx)("span", { className: "truncate", children: t2("fields.quantity") }) }),
        cell: ({ getValue, row }) => {
          return getReturnableQuantity(row.original);
        }
      }),
      columnHelper.accessor("refundable_total", {
        header: () => (0, import_jsx_runtime.jsx)("div", { className: "flex size-full items-center justify-end overflow-hidden text-right", children: (0, import_jsx_runtime.jsx)("span", { className: "truncate", children: t2("fields.price") }) }),
        cell: ({ getValue }) => {
          const amount = getValue() || 0;
          const stylized = getStylizedAmount(amount, currencyCode);
          return (0, import_jsx_runtime.jsx)("div", { className: "flex size-full items-center justify-end overflow-hidden text-right", children: (0, import_jsx_runtime.jsx)("span", { className: "truncate", children: stylized }) });
        }
      })
    ],
    [t2, currencyCode]
  );
};
var useReturnItemTableFilters = () => {
  const { t: t2 } = useTranslation();
  const filters = [
    {
      key: "returnable_quantity",
      label: t2("orders.returns.returnableQuantityLabel"),
      type: "number"
    },
    {
      key: "refundable_amount",
      label: t2("orders.returns.refundableAmountLabel"),
      type: "number"
    },
    {
      key: "created_at",
      label: t2("fields.createdAt"),
      type: "date"
    },
    {
      key: "updated_at",
      label: t2("fields.updatedAt"),
      type: "date"
    }
  ];
  return filters;
};
var useReturnItemTableQuery = ({
  pageSize = 50,
  prefix
}) => {
  const raw = useQueryParams(
    [
      "q",
      "offset",
      "order",
      "created_at",
      "updated_at",
      "returnable_quantity",
      "refundable_amount"
    ],
    prefix
  );
  const {
    offset,
    created_at,
    updated_at,
    refundable_amount,
    returnable_quantity,
    ...rest
  } = raw;
  const searchParams = {
    ...rest,
    limit: pageSize,
    offset: offset ? Number(offset) : 0,
    created_at: created_at ? JSON.parse(created_at) : void 0,
    updated_at: updated_at ? JSON.parse(updated_at) : void 0,
    refundable_amount: refundable_amount ? JSON.parse(refundable_amount) : void 0,
    returnable_quantity: returnable_quantity ? JSON.parse(returnable_quantity) : void 0
  };
  return { searchParams, raw };
};
var PAGE_SIZE = 50;
var PREFIX = "rit";
var AddReturnItemsTable = ({
  onSelectionChange,
  selectedItems: selectedItems2,
  items,
  currencyCode
}) => {
  const { t: t2 } = useTranslation();
  const [rowSelection, setRowSelection] = (0, import_react3.useState)(
    selectedItems2.reduce((acc, id) => {
      acc[id] = true;
      return acc;
    }, {})
  );
  const updater = (fn) => {
    const newState = typeof fn === "function" ? fn(rowSelection) : fn;
    setRowSelection(newState);
    onSelectionChange(Object.keys(newState));
  };
  const { searchParams, raw } = useReturnItemTableQuery({
    pageSize: PAGE_SIZE,
    prefix: PREFIX
  });
  const queriedItems = (0, import_react3.useMemo)(() => {
    const {
      order,
      offset,
      limit,
      q,
      created_at,
      updated_at,
      refundable_amount,
      returnable_quantity
    } = searchParams;
    let results = items;
    if (q) {
      results = results.filter((i) => {
        var _a;
        return i.product_title.toLowerCase().includes(q.toLowerCase()) || i.variant_title.toLowerCase().includes(q.toLowerCase()) || ((_a = i.variant_sku) == null ? void 0 : _a.toLowerCase().includes(q.toLowerCase()));
      });
    }
    if (order) {
      const direction = order[0] === "-" ? "desc" : "asc";
      const field = order.replace("-", "");
      results = sortItems(results, field, direction);
    }
    if (created_at) {
      results = filterByDate(results, created_at, "created_at");
    }
    if (updated_at) {
      results = filterByDate(results, updated_at, "updated_at");
    }
    if (returnable_quantity) {
      results = filterByNumber(
        results,
        returnable_quantity,
        "returnable_quantity",
        currencyCode
      );
    }
    if (refundable_amount) {
      results = filterByNumber(
        results,
        refundable_amount,
        "refundable_amount",
        currencyCode
      );
    }
    return results.slice(offset, offset + limit);
  }, [items, currencyCode, searchParams]);
  const columns = useReturnItemTableColumns(currencyCode);
  const filters = useReturnItemTableFilters();
  const { table } = useDataTable({
    data: queriedItems,
    columns,
    count: queriedItems.length,
    enablePagination: true,
    getRowId: (row) => row.id,
    pageSize: PAGE_SIZE,
    enableRowSelection: (row) => {
      return getReturnableQuantity(row.original) > 0;
    },
    rowSelection: {
      state: rowSelection,
      updater
    }
  });
  return (0, import_jsx_runtime2.jsx)("div", { className: "flex size-full flex-col overflow-hidden", children: (0, import_jsx_runtime2.jsx)(
    _DataTable,
    {
      table,
      columns,
      pageSize: PAGE_SIZE,
      count: queriedItems.length,
      filters,
      pagination: true,
      layout: "fill",
      search: true,
      orderBy: [
        { key: "product_title", label: t2("fields.product") },
        { key: "variant_title", label: t2("fields.variant") },
        { key: "sku", label: t2("fields.sku") },
        {
          key: "returnable_quantity",
          label: t2("orders.fields.returnableQuantity")
        },
        {
          key: "refundable_amount",
          label: t2("orders.fields.refundableAmount")
        }
      ],
      prefix: PREFIX,
      queryObject: raw
    }
  ) });
};
var sortItems = (items, field, direction) => {
  return items.sort((a, b) => {
    let aValue;
    let bValue;
    if (field === "product_title") {
      aValue = a.product_title;
      bValue = b.product_title;
    } else if (field === "variant_title") {
      aValue = a.variant_title;
      bValue = b.variant_title;
    } else if (field === "sku") {
      aValue = a.variant_sku;
      bValue = b.variant_sku;
    } else if (field === "returnable_quantity") {
      aValue = a.quantity - (a.returned_quantity || 0);
      bValue = b.quantity - (b.returned_quantity || 0);
    } else if (field === "refundable_amount") {
      aValue = a.refundable || 0;
      bValue = b.refundable || 0;
    }
    if (aValue < bValue) {
      return direction === "asc" ? -1 : 1;
    }
    if (aValue > bValue) {
      return direction === "asc" ? 1 : -1;
    }
    return 0;
  });
};
var filterByDate = (items, date, field) => {
  const { gt, gte, lt, lte } = date;
  return items.filter((i) => {
    const itemDate = new Date(i[field]);
    let isValid = true;
    if (gt) {
      isValid = isValid && itemDate > new Date(gt);
    }
    if (gte) {
      isValid = isValid && itemDate >= new Date(gte);
    }
    if (lt) {
      isValid = isValid && itemDate < new Date(lt);
    }
    if (lte) {
      isValid = isValid && itemDate <= new Date(lte);
    }
    return isValid;
  });
};
var defaultOperators = {
  eq: void 0,
  gt: void 0,
  gte: void 0,
  lt: void 0,
  lte: void 0
};
var filterByNumber = (items, value, field, currency_code) => {
  const { eq, gt, lt, gte, lte } = typeof value === "object" ? { ...defaultOperators, ...value } : { ...defaultOperators, eq: value };
  return items.filter((i) => {
    const returnableQuantity = i.quantity - (i.returned_quantity || 0);
    const refundableAmount = getStylizedAmount(i.refundable || 0, currency_code);
    const itemValue = field === "returnable_quantity" ? returnableQuantity : refundableAmount;
    if (eq) {
      return itemValue === eq;
    }
    let isValid = true;
    if (gt) {
      isValid = isValid && itemValue > gt;
    }
    if (gte) {
      isValid = isValid && itemValue >= gte;
    }
    if (lt) {
      isValid = isValid && itemValue < lt;
    }
    if (lte) {
      isValid = isValid && itemValue <= lte;
    }
    return isValid;
  });
};
function ReturnItem({
  item,
  previewItem,
  currencyCode,
  form,
  onRemove,
  onUpdate,
  index
}) {
  const { t: t2 } = useTranslation();
  const { return_reasons = [] } = useReturnReasons({ fields: "+label" });
  const formItem = form.watch(`items.${index}`);
  const showReturnReason = typeof formItem.reason_id === "string";
  const showNote = typeof formItem.note === "string";
  return (0, import_jsx_runtime3.jsxs)("div", { className: "bg-ui-bg-subtle shadow-elevation-card-rest my-2 rounded-xl ", children: [
    (0, import_jsx_runtime3.jsxs)("div", { className: "flex flex-col items-center gap-x-2 gap-y-2 border-b p-3 text-sm md:flex-row", children: [
      (0, import_jsx_runtime3.jsxs)("div", { className: "flex flex-1 items-center gap-x-3", children: [
        (0, import_jsx_runtime3.jsx)(Thumbnail, { src: item.thumbnail }),
        (0, import_jsx_runtime3.jsxs)("div", { className: "flex flex-col", children: [
          (0, import_jsx_runtime3.jsxs)("div", { children: [
            (0, import_jsx_runtime3.jsxs)(Text, { className: "txt-small", as: "span", weight: "plus", children: [
              item.title,
              " "
            ] }),
            item.variant_sku && (0, import_jsx_runtime3.jsxs)("span", { children: [
              "(",
              item.variant_sku,
              ")"
            ] })
          ] }),
          (0, import_jsx_runtime3.jsx)(Text, { as: "div", className: "text-ui-fg-subtle txt-small", children: item.product_title })
        ] })
      ] }),
      (0, import_jsx_runtime3.jsxs)("div", { className: "flex flex-1 justify-between", children: [
        (0, import_jsx_runtime3.jsxs)("div", { className: "flex flex-grow items-center gap-2", children: [
          (0, import_jsx_runtime3.jsx)(
            Form.Field,
            {
              control: form.control,
              name: `items.${index}.quantity`,
              render: ({ field }) => {
                return (0, import_jsx_runtime3.jsxs)(Form.Item, { children: [
                  (0, import_jsx_runtime3.jsx)(Form.Control, { children: (0, import_jsx_runtime3.jsx)(
                    Input,
                    {
                      className: "bg-ui-bg-base txt-small w-[67px] rounded-lg",
                      min: 1,
                      max: item.quantity,
                      type: "number",
                      ...field,
                      onChange: (e) => {
                        const val = e.target.value;
                        const payload = val === "" ? null : Number(val);
                        field.onChange(payload);
                        if (payload) {
                          onUpdate({ quantity: payload });
                        }
                      }
                    }
                  ) }),
                  (0, import_jsx_runtime3.jsx)(Form.ErrorMessage, {})
                ] });
              }
            }
          ),
          (0, import_jsx_runtime3.jsx)(Text, { className: "txt-small text-ui-fg-subtle", children: t2("fields.qty") })
        ] }),
        (0, import_jsx_runtime3.jsx)("div", { className: "text-ui-fg-subtle txt-small mr-2 flex flex-shrink-0", children: (0, import_jsx_runtime3.jsx)(
          MoneyAmountCell,
          {
            currencyCode,
            amount: previewItem.return_requested_total
          }
        ) }),
        (0, import_jsx_runtime3.jsx)(
          ActionMenu,
          {
            groups: [
              {
                actions: [
                  !showReturnReason && {
                    label: t2("actions.addReason"),
                    onClick: () => form.setValue(`items.${index}.reason_id`, ""),
                    icon: (0, import_jsx_runtime3.jsx)(ChatBubble, {})
                  },
                  !showNote && {
                    label: t2("actions.addNote"),
                    onClick: () => form.setValue(`items.${index}.note`, ""),
                    icon: (0, import_jsx_runtime3.jsx)(DocumentText, {})
                  },
                  {
                    label: t2("actions.remove"),
                    onClick: onRemove,
                    icon: (0, import_jsx_runtime3.jsx)(XCircle, {})
                  }
                ].filter(Boolean)
              }
            ]
          }
        )
      ] })
    ] }),
    (0, import_jsx_runtime3.jsxs)(import_jsx_runtime3.Fragment, { children: [
      showReturnReason && (0, import_jsx_runtime3.jsxs)("div", { className: "grid grid-cols-1 gap-2 p-3 md:grid-cols-2", children: [
        (0, import_jsx_runtime3.jsxs)("div", { children: [
          (0, import_jsx_runtime3.jsx)(Form.Label, { children: t2("orders.returns.reason") }),
          (0, import_jsx_runtime3.jsx)(Form.Hint, { className: "!mt-1", children: t2("orders.returns.reasonHint") })
        ] }),
        (0, import_jsx_runtime3.jsxs)("div", { className: "flex items-center gap-1", children: [
          (0, import_jsx_runtime3.jsx)("div", { className: "flex-grow", children: (0, import_jsx_runtime3.jsx)(
            Form.Field,
            {
              control: form.control,
              name: `items.${index}.reason_id`,
              render: ({ field: { ref, value, onChange, ...field } }) => {
                return (0, import_jsx_runtime3.jsxs)(Form.Item, { children: [
                  (0, import_jsx_runtime3.jsx)(Form.Control, { children: (0, import_jsx_runtime3.jsx)(
                    Combobox,
                    {
                      className: "bg-ui-bg-field-component hover:bg-ui-bg-field-component-hover",
                      value,
                      onChange: (v) => {
                        onUpdate({ reason_id: v });
                        onChange(v);
                      },
                      ...field,
                      options: return_reasons.map((reason) => ({
                        label: reason.label,
                        value: reason.id
                      }))
                    }
                  ) }),
                  (0, import_jsx_runtime3.jsx)(Form.ErrorMessage, {})
                ] });
              }
            }
          ) }),
          (0, import_jsx_runtime3.jsx)(
            IconButton,
            {
              type: "button",
              className: "flex-shrink",
              variant: "transparent",
              onClick: () => {
                onUpdate({ reason_id: null });
                form.setValue(`items.${index}.reason_id`, null);
              },
              children: (0, import_jsx_runtime3.jsx)(XMark, { className: "text-ui-fg-muted" })
            }
          )
        ] })
      ] }),
      showNote && (0, import_jsx_runtime3.jsxs)("div", { className: "grid grid-cols-1 gap-2 p-3 md:grid-cols-2", children: [
        (0, import_jsx_runtime3.jsxs)("div", { children: [
          (0, import_jsx_runtime3.jsx)(Form.Label, { children: t2("orders.returns.note") }),
          (0, import_jsx_runtime3.jsx)(Form.Hint, { className: "!mt-1", children: t2("orders.returns.noteHint") })
        ] }),
        (0, import_jsx_runtime3.jsxs)("div", { className: "flex items-center gap-1", children: [
          (0, import_jsx_runtime3.jsx)("div", { className: "flex-grow", children: (0, import_jsx_runtime3.jsx)(
            Form.Field,
            {
              control: form.control,
              name: `items.${index}.note`,
              render: ({ field: { ref, onChange, ...field } }) => {
                return (0, import_jsx_runtime3.jsxs)(Form.Item, { children: [
                  (0, import_jsx_runtime3.jsx)(Form.Control, { children: (0, import_jsx_runtime3.jsx)(
                    Input,
                    {
                      onChange,
                      ...field,
                      onBlur: () => onUpdate({ internal_note: field.value }),
                      className: "bg-ui-bg-field-component hover:bg-ui-bg-field-component-hover"
                    }
                  ) }),
                  (0, import_jsx_runtime3.jsx)(Form.ErrorMessage, {})
                ] });
              }
            }
          ) }),
          (0, import_jsx_runtime3.jsx)(
            IconButton,
            {
              type: "button",
              className: "flex-shrink",
              variant: "transparent",
              onClick: () => {
                form.setValue(`items.${index}.note`, {
                  shouldDirty: true,
                  shouldTouch: true
                });
                onUpdate({ internal_note: null });
              },
              children: (0, import_jsx_runtime3.jsx)(XMark, { className: "text-ui-fg-muted" })
            }
          )
        ] })
      ] })
    ] })
  ] });
}
var ReturnCreateSchema = z.object({
  items: z.array(
    z.object({
      item_id: z.string(),
      quantity: z.number(),
      reason_id: z.string().optional().nullable(),
      note: z.string().optional().nullable()
    })
  ),
  location_id: z.string().optional(),
  option_id: z.string(),
  send_notification: z.boolean().optional(),
  // TODO: implement this
  receive_now: z.boolean().optional()
});
var selectedItems = [];
var ReturnCreateForm = ({
  order,
  preview,
  activeReturn
}) => {
  const { t: t2 } = useTranslation();
  const { handleSuccess } = useRouteModal();
  const itemsMap = (0, import_react2.useMemo)(
    () => new Map((order.items || []).map((i) => [i.id, i])),
    [order.items]
  );
  const previewItems = (0, import_react2.useMemo)(
    () => preview.items.filter(
      (i) => {
        var _a;
        return !!((_a = i.actions) == null ? void 0 : _a.find((a) => a.return_id === activeReturn.id));
      }
    ),
    [preview.items]
  );
  const previewItemsMap = (0, import_react2.useMemo)(
    () => new Map(previewItems.map((i) => [i.id, i])),
    [previewItems]
  );
  const { setIsOpen } = useStackedModal();
  const [isShippingPriceEdit, setIsShippingPriceEdit] = (0, import_react2.useState)(false);
  const [customShippingAmount, setCustomShippingAmount] = (0, import_react2.useState)(0);
  const [inventoryMap, setInventoryMap] = (0, import_react2.useState)({});
  const { stock_locations = [] } = useStockLocations({ limit: 999 });
  const { shipping_options = [] } = useShippingOptions({
    limit: 999,
    fields: "*prices,+service_zone.fulfillment_set.location.id"
    /**
     * TODO: this should accept filter for location_id
     */
  });
  const { mutateAsync: confirmReturnRequest, isPending: isConfirming } = useConfirmReturnRequest(activeReturn.id, order.id);
  const { mutateAsync: cancelReturnRequest, isPending: isCanceling } = useCancelReturnRequest(activeReturn.id, order.id);
  const { mutateAsync: updateReturnRequest, isPending: isUpdating } = useUpdateReturn(activeReturn.id, order.id);
  const { mutateAsync: addReturnShipping, isPending: isAddingReturnShipping } = useAddReturnShipping(activeReturn.id, order.id);
  const {
    mutateAsync: updateReturnShipping,
    isPending: isUpdatingReturnShipping
  } = useUpdateReturnShipping(activeReturn.id, order.id);
  const {
    mutateAsync: deleteReturnShipping,
    isPending: isDeletingReturnShipping
  } = useDeleteReturnShipping(activeReturn.id, order.id);
  const { mutateAsync: addReturnItem, isPending: isAddingReturnItem } = useAddReturnItem(activeReturn.id, order.id);
  const { mutateAsync: removeReturnItem, isPending: isRemovingReturnItem } = useRemoveReturnItem(activeReturn.id, order.id);
  const { mutateAsync: updateReturnItem, isPending: isUpdatingReturnItem } = useUpdateReturnItem(activeReturn.id, order.id);
  const isRequestLoading = isConfirming || isCanceling || isAddingReturnShipping || isUpdatingReturnShipping || isDeletingReturnShipping || isAddingReturnItem || isRemovingReturnItem || isUpdatingReturnItem || isUpdating;
  const form = useForm({
    /**
     * TODO: reason selection once Return reason settings are added
     */
    defaultValues: () => {
      const method = preview.shipping_methods.find(
        (s) => {
          var _a;
          return !!((_a = s.actions) == null ? void 0 : _a.find((a) => a.action === "SHIPPING_ADD"));
        }
      );
      return Promise.resolve({
        items: previewItems.map((i) => {
          var _a, _b, _c, _d, _e;
          return {
            item_id: i.id,
            quantity: i.detail.return_requested_quantity,
            note: (_b = (_a = i.actions) == null ? void 0 : _a.find((a) => a.action === "RETURN_ITEM")) == null ? void 0 : _b.internal_note,
            reason_id: (_e = (_d = (_c = i.actions) == null ? void 0 : _c.find((a) => a.action === "RETURN_ITEM")) == null ? void 0 : _d.details) == null ? void 0 : _e.reason_id
          };
        }),
        option_id: method ? method.shipping_option_id : "",
        location_id: activeReturn == null ? void 0 : activeReturn.location_id,
        send_notification: false
      });
    },
    resolver: t(ReturnCreateSchema)
  });
  const {
    fields: items,
    append,
    remove,
    update
  } = useFieldArray({
    name: "items",
    control: form.control
  });
  (0, import_react2.useEffect)(() => {
    const existingItemsMap = {};
    previewItems.forEach((i) => {
      var _a, _b;
      const ind = items.findIndex((field) => field.item_id === i.id);
      if (!i.detail.return_requested_quantity) {
        return;
      }
      existingItemsMap[i.id] = true;
      if (ind > -1) {
        if (items[ind].quantity !== i.detail.return_requested_quantity) {
          const returnItemAction = (_a = i.actions) == null ? void 0 : _a.find(
            (a) => a.action === "RETURN_ITEM"
          );
          update(ind, {
            ...items[ind],
            quantity: i.detail.return_requested_quantity,
            note: returnItemAction == null ? void 0 : returnItemAction.internal_note,
            reason_id: (_b = returnItemAction == null ? void 0 : returnItemAction.details) == null ? void 0 : _b.reason_id
          });
        }
      } else {
        append({ item_id: i.id, quantity: i.detail.return_requested_quantity });
      }
    });
    items.forEach((i, ind) => {
      if (!(i.item_id in existingItemsMap)) {
        remove(ind);
      }
    });
  }, [previewItems]);
  (0, import_react2.useEffect)(() => {
    var _a;
    const method = (_a = preview.shipping_methods) == null ? void 0 : _a.find(
      (s) => {
        var _a2;
        return !!((_a2 = s.actions) == null ? void 0 : _a2.find((a) => a.action === "SHIPPING_ADD"));
      }
    );
    if (method) {
      form.setValue("option_id", method.shipping_option_id);
    } else {
      form.setValue("option_id", "");
    }
  }, [preview.shipping_methods]);
  const showPlaceholder = !items.length;
  const locationId = form.watch("location_id");
  const shippingOptionId = form.watch("option_id");
  const prompt = usePrompt();
  const handleSubmit = form.handleSubmit(async (data) => {
    try {
      const res = await prompt({
        title: t2("general.areYouSure"),
        description: t2("orders.returns.confirmText"),
        confirmText: t2("actions.continue"),
        cancelText: t2("actions.cancel"),
        variant: "confirmation"
      });
      if (!res) {
        return;
      }
      await confirmReturnRequest({ no_notification: !data.send_notification });
      handleSuccess();
    } catch (e) {
      toast.error(t2("general.error"), {
        description: e.message,
        dismissLabel: t2("actions.close")
      });
    }
  });
  const onItemsSelected = () => {
    addReturnItem({
      items: selectedItems.map((id) => ({
        id,
        quantity: 1
      }))
    });
    setIsOpen("items", false);
  };
  const onLocationChange = async (selectedLocationId) => {
    await updateReturnRequest({ location_id: selectedLocationId });
  };
  const onShippingOptionChange = async (selectedOptionId) => {
    const promises = preview.shipping_methods.map((s) => {
      var _a, _b;
      return (_b = (_a = s.actions) == null ? void 0 : _a.find((a) => a.action === "SHIPPING_ADD")) == null ? void 0 : _b.id;
    }).filter(Boolean).map(deleteReturnShipping);
    await Promise.all(promises);
    if (selectedOptionId) {
      await addReturnShipping({ shipping_option_id: selectedOptionId });
    }
  };
  (0, import_react2.useEffect)(() => {
    if (isShippingPriceEdit) {
      document.getElementById("js-shipping-input").focus();
    }
  }, [isShippingPriceEdit]);
  (0, import_react2.useEffect)(() => {
    form.setValue("location_id", (activeReturn == null ? void 0 : activeReturn.location_id) || "");
  }, [activeReturn]);
  const showLevelsWarning = (0, import_react2.useMemo)(() => {
    if (!locationId) {
      return false;
    }
    const allItemsHaveLocation = items.map((_i) => {
      var _a, _b;
      const item = itemsMap.get(_i.item_id);
      if (!(item == null ? void 0 : item.variant_id)) {
        return true;
      }
      if (!((_a = item.variant) == null ? void 0 : _a.manage_inventory)) {
        return true;
      }
      return (_b = inventoryMap[item.variant_id]) == null ? void 0 : _b.find(
        (l) => l.location_id === locationId
      );
    }).every(Boolean);
    return !allItemsHaveLocation;
  }, [items, inventoryMap, locationId]);
  (0, import_react2.useEffect)(() => {
    const getInventoryMap = async () => {
      const ret = {};
      if (!items.length) {
        return ret;
      }
      ;
      (await Promise.all(
        items.map(async (_i) => {
          const item = itemsMap.get(_i.item_id);
          if (!item.variant_id) {
            return void 0;
          }
          return await sdk.admin.product.retrieveVariant(
            item.product_id,
            item.variant_id,
            { fields: "*inventory,*inventory.location_levels" }
          );
        })
      )).filter((it) => it == null ? void 0 : it.variant).forEach((item) => {
        var _a;
        const { variant } = item;
        const levels = (_a = variant.inventory[0]) == null ? void 0 : _a.location_levels;
        if (!levels) {
          return;
        }
        ret[variant.id] = levels;
      });
      return ret;
    };
    getInventoryMap().then((map) => {
      setInventoryMap(map);
    });
  }, [items]);
  const returnTotal = preview.return_requested_total;
  const shippingTotal = (0, import_react2.useMemo)(() => {
    const method = preview.shipping_methods.find(
      (sm) => {
        var _a;
        return !!((_a = sm.actions) == null ? void 0 : _a.find((a) => a.action === "SHIPPING_ADD"));
      }
    );
    return (method == null ? void 0 : method.total) || 0;
  }, [preview.shipping_methods]);
  return (0, import_jsx_runtime4.jsx)(
    RouteFocusModal.Form,
    {
      form,
      onClose: (isSubmitSuccessful) => {
        if (!isSubmitSuccessful) {
          cancelReturnRequest();
        }
      },
      children: (0, import_jsx_runtime4.jsxs)(KeyboundForm, { onSubmit: handleSubmit, className: "flex h-full flex-col", children: [
        (0, import_jsx_runtime4.jsx)(RouteFocusModal.Header, {}),
        (0, import_jsx_runtime4.jsx)(RouteFocusModal.Body, { className: "flex size-full justify-center overflow-y-auto", children: (0, import_jsx_runtime4.jsxs)("div", { className: "mt-16 w-[720px] max-w-[100%] px-4 md:p-0", children: [
          (0, import_jsx_runtime4.jsx)(Heading, { level: "h1", children: t2("orders.returns.create") }),
          (0, import_jsx_runtime4.jsxs)("div", { className: "mt-8 flex items-center justify-between", children: [
            (0, import_jsx_runtime4.jsx)(Heading, { level: "h2", children: t2("orders.returns.inbound") }),
            (0, import_jsx_runtime4.jsxs)(StackedFocusModal, { id: "items", children: [
              (0, import_jsx_runtime4.jsx)(StackedFocusModal.Trigger, { asChild: true, children: (0, import_jsx_runtime4.jsx)("a", { className: "focus-visible:shadow-borders-focus transition-fg txt-compact-small-plus cursor-pointer text-blue-500 outline-none hover:text-blue-400", children: t2("actions.addItems") }) }),
              (0, import_jsx_runtime4.jsxs)(StackedFocusModal.Content, { children: [
                (0, import_jsx_runtime4.jsx)(StackedFocusModal.Header, {}),
                (0, import_jsx_runtime4.jsx)(
                  AddReturnItemsTable,
                  {
                    items: order.items,
                    selectedItems: items.map((i) => i.item_id),
                    currencyCode: order.currency_code,
                    onSelectionChange: (s) => selectedItems = s
                  }
                ),
                (0, import_jsx_runtime4.jsx)(StackedFocusModal.Footer, { children: (0, import_jsx_runtime4.jsx)("div", { className: "flex w-full items-center justify-end gap-x-4", children: (0, import_jsx_runtime4.jsxs)("div", { className: "flex items-center justify-end gap-x-2", children: [
                  (0, import_jsx_runtime4.jsx)(RouteFocusModal.Close, { asChild: true, children: (0, import_jsx_runtime4.jsx)(
                    Button,
                    {
                      type: "button",
                      variant: "secondary",
                      size: "small",
                      children: t2("actions.cancel")
                    }
                  ) }),
                  (0, import_jsx_runtime4.jsx)(
                    Button,
                    {
                      type: "submit",
                      variant: "primary",
                      size: "small",
                      role: "button",
                      onClick: () => onItemsSelected(),
                      children: t2("actions.save")
                    },
                    "submit-button"
                  )
                ] }) }) })
              ] })
            ] })
          ] }),
          showPlaceholder && (0, import_jsx_runtime4.jsx)(
            "div",
            {
              style: {
                background: "repeating-linear-gradient(-45deg, rgb(212, 212, 216, 0.15), rgb(212, 212, 216,.15) 10px, transparent 10px, transparent 20px)"
              },
              className: "bg-ui-bg-field mt-4 block h-[56px] w-full rounded-lg border border-dashed"
            }
          ),
          items.filter((item) => !!previewItemsMap.get(item.item_id)).map((item, index) => (0, import_jsx_runtime4.jsx)(
            ReturnItem,
            {
              item: itemsMap.get(item.item_id),
              previewItem: previewItemsMap.get(item.item_id),
              currencyCode: order.currency_code,
              form,
              onRemove: () => {
                var _a, _b, _c;
                const actionId = (_c = (_b = (_a = previewItems.find((i) => i.id === item.item_id)) == null ? void 0 : _a.actions) == null ? void 0 : _b.find((a) => a.action === "RETURN_ITEM")) == null ? void 0 : _c.id;
                if (actionId) {
                  removeReturnItem(actionId);
                }
              },
              onUpdate: (payload) => {
                var _a, _b;
                const action = (_b = (_a = previewItems.find((i) => i.id === item.item_id)) == null ? void 0 : _a.actions) == null ? void 0 : _b.find((a) => a.action === "RETURN_ITEM");
                if (action) {
                  updateReturnItem(
                    { ...payload, actionId: action.id },
                    {
                      onError: (error) => {
                        var _a2, _b2;
                        if (((_a2 = action.details) == null ? void 0 : _a2.quantity) && payload.quantity) {
                          form.setValue(
                            `items.${index}.quantity`,
                            (_b2 = action.details) == null ? void 0 : _b2.quantity
                          );
                        }
                        toast.error(error.message);
                      }
                    }
                  );
                }
              },
              index
            },
            item.id
          )),
          !showPlaceholder && (0, import_jsx_runtime4.jsxs)("div", { className: "mt-8 flex flex-col gap-y-4", children: [
            (0, import_jsx_runtime4.jsxs)("div", { className: "grid grid-cols-1 gap-2 md:grid-cols-2", children: [
              (0, import_jsx_runtime4.jsxs)("div", { children: [
                (0, import_jsx_runtime4.jsx)(Form.Label, { children: t2("orders.returns.location") }),
                (0, import_jsx_runtime4.jsx)(Form.Hint, { className: "!mt-1", children: t2("orders.returns.locationHint") })
              ] }),
              (0, import_jsx_runtime4.jsx)(
                Form.Field,
                {
                  control: form.control,
                  name: "location_id",
                  render: ({ field: { value, onChange, ...field } }) => {
                    return (0, import_jsx_runtime4.jsx)(Form.Item, { children: (0, import_jsx_runtime4.jsx)(Form.Control, { children: (0, import_jsx_runtime4.jsx)(
                      Combobox,
                      {
                        value,
                        onChange: (v) => {
                          onChange(v);
                          onLocationChange(v);
                        },
                        ...field,
                        options: (stock_locations ?? []).map(
                          (stockLocation) => ({
                            label: stockLocation.name,
                            value: stockLocation.id
                          })
                        )
                      }
                    ) }) });
                  }
                }
              )
            ] }),
            (0, import_jsx_runtime4.jsxs)("div", { className: "grid grid-cols-1 gap-2 md:grid-cols-2", children: [
              (0, import_jsx_runtime4.jsxs)("div", { children: [
                (0, import_jsx_runtime4.jsxs)(Form.Label, { children: [
                  t2("orders.returns.inboundShipping"),
                  (0, import_jsx_runtime4.jsxs)(
                    Text,
                    {
                      size: "small",
                      leading: "compact",
                      className: "text-ui-fg-muted ml-1 inline",
                      children: [
                        "(",
                        t2("fields.optional"),
                        ")"
                      ]
                    }
                  )
                ] }),
                (0, import_jsx_runtime4.jsx)(Form.Hint, { className: "!mt-1", children: t2("orders.returns.inboundShippingHint") })
              ] }),
              (0, import_jsx_runtime4.jsx)(
                Form.Field,
                {
                  control: form.control,
                  name: "option_id",
                  render: ({ field: { value, onChange, ...field } }) => {
                    return (0, import_jsx_runtime4.jsx)(Form.Item, { children: (0, import_jsx_runtime4.jsx)(Form.Control, { children: (0, import_jsx_runtime4.jsx)(
                      Combobox,
                      {
                        allowClear: true,
                        value,
                        onChange: (v) => {
                          onChange(v);
                          onShippingOptionChange(v);
                        },
                        ...field,
                        options: (shipping_options ?? []).filter(
                          (so) => (locationId ? so.service_zone.fulfillment_set.location.id === locationId : true) && !!so.rules.find(
                            (r) => r.attribute === "is_return" && r.value === "true"
                          )
                        ).map((so) => ({
                          label: so.name,
                          value: so.id
                        })),
                        disabled: !locationId,
                        noResultsPlaceholder: (0, import_jsx_runtime4.jsx)(ReturnShippingPlaceholder, {})
                      }
                    ) }) });
                  }
                }
              )
            ] })
          ] }),
          showLevelsWarning && (0, import_jsx_runtime4.jsxs)(Alert, { variant: "warning", dismissible: true, className: "mt-4 p-5", children: [
            (0, import_jsx_runtime4.jsx)("div", { className: "text-ui-fg-subtle txt-small pb-2 font-medium leading-[20px]", children: t2("orders.returns.noInventoryLevel") }),
            (0, import_jsx_runtime4.jsx)(Text, { className: "text-ui-fg-subtle txt-small leading-normal", children: t2("orders.returns.noInventoryLevelDesc") })
          ] }),
          (0, import_jsx_runtime4.jsxs)("div", { className: "mt-8 border-y border-dotted py-4", children: [
            (0, import_jsx_runtime4.jsxs)("div", { className: "mb-2 flex items-center justify-between", children: [
              (0, import_jsx_runtime4.jsx)("span", { className: "txt-small text-ui-fg-subtle", children: t2("orders.returns.returnTotal") }),
              (0, import_jsx_runtime4.jsx)("span", { className: "txt-small text-ui-fg-subtle", children: getStylizedAmount(
                returnTotal ? -1 * returnTotal : returnTotal,
                order.currency_code
              ) })
            ] }),
            (0, import_jsx_runtime4.jsxs)("div", { className: "flex items-center justify-between", children: [
              (0, import_jsx_runtime4.jsx)("span", { className: "txt-small text-ui-fg-subtle", children: t2("orders.returns.inboundShipping") }),
              (0, import_jsx_runtime4.jsxs)("span", { className: "txt-small text-ui-fg-subtle flex items-center", children: [
                !isShippingPriceEdit && (0, import_jsx_runtime4.jsx)(
                  IconButton,
                  {
                    onClick: () => setIsShippingPriceEdit(true),
                    variant: "transparent",
                    className: "text-ui-fg-muted",
                    disabled: showPlaceholder || !shippingOptionId,
                    children: (0, import_jsx_runtime4.jsx)(PencilSquare, {})
                  }
                ),
                isShippingPriceEdit ? (0, import_jsx_runtime4.jsx)(
                  CurrencyInput,
                  {
                    id: "js-shipping-input",
                    onBlur: () => {
                      let actionId;
                      preview.shipping_methods.forEach((s) => {
                        if (s.actions) {
                          for (const a of s.actions) {
                            if (a.action === "SHIPPING_ADD") {
                              actionId = a.id;
                            }
                          }
                        }
                      });
                      if (actionId) {
                        updateReturnShipping({
                          actionId,
                          custom_amount: typeof customShippingAmount === "string" ? null : customShippingAmount
                        });
                      }
                      setIsShippingPriceEdit(false);
                    },
                    symbol: currencies[order.currency_code.toUpperCase()].symbol_native,
                    code: order.currency_code,
                    onValueChange: (value) => setCustomShippingAmount(value ? parseFloat(value) : ""),
                    value: customShippingAmount,
                    disabled: showPlaceholder
                  }
                ) : getStylizedAmount(shippingTotal, order.currency_code)
              ] })
            ] }),
            (0, import_jsx_runtime4.jsxs)("div", { className: "mt-4 flex items-center justify-between border-t border-dotted pt-4", children: [
              (0, import_jsx_runtime4.jsx)("span", { className: "txt-small font-medium", children: t2("orders.returns.estDifference") }),
              (0, import_jsx_runtime4.jsx)("span", { className: "txt-small font-medium", children: getStylizedAmount(
                preview.summary.pending_difference,
                order.currency_code
              ) })
            ] })
          ] }),
          (0, import_jsx_runtime4.jsx)("div", { className: "bg-ui-bg-field mt-8 rounded-lg border py-2 pl-2 pr-4", children: (0, import_jsx_runtime4.jsx)(
            Form.Field,
            {
              control: form.control,
              name: "send_notification",
              render: ({ field: { onChange, value, ...field } }) => {
                return (0, import_jsx_runtime4.jsxs)(Form.Item, { children: [
                  (0, import_jsx_runtime4.jsxs)("div", { className: "flex items-center", children: [
                    (0, import_jsx_runtime4.jsx)(Form.Control, { className: "mr-4 self-start", children: (0, import_jsx_runtime4.jsx)(
                      Switch,
                      {
                        className: "mt-[2px]",
                        checked: !!value,
                        onCheckedChange: onChange,
                        ...field
                      }
                    ) }),
                    (0, import_jsx_runtime4.jsxs)("div", { className: "block", children: [
                      (0, import_jsx_runtime4.jsx)(Form.Label, { children: t2("orders.returns.sendNotification") }),
                      (0, import_jsx_runtime4.jsx)(Form.Hint, { className: "!mt-1", children: t2("orders.returns.sendNotificationHint") })
                    ] })
                  ] }),
                  (0, import_jsx_runtime4.jsx)(Form.ErrorMessage, {})
                ] });
              }
            }
          ) }),
          (0, import_jsx_runtime4.jsx)("div", { className: "p-8" })
        ] }) }),
        (0, import_jsx_runtime4.jsx)(RouteFocusModal.Footer, { children: (0, import_jsx_runtime4.jsx)("div", { className: "flex w-full items-center justify-end gap-x-4", children: (0, import_jsx_runtime4.jsxs)("div", { className: "flex items-center justify-end gap-x-2", children: [
          (0, import_jsx_runtime4.jsx)(RouteFocusModal.Close, { asChild: true, children: (0, import_jsx_runtime4.jsx)(Button, { type: "button", variant: "secondary", size: "small", children: t2("orders.returns.cancel.title") }) }),
          (0, import_jsx_runtime4.jsx)(
            Button,
            {
              type: "submit",
              variant: "primary",
              size: "small",
              isLoading: isRequestLoading,
              children: t2("orders.returns.confirm")
            },
            "submit-button"
          )
        ] }) }) })
      ] })
    }
  );
};
var IS_REQUEST_RUNNING = false;
var ReturnCreate = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { t: t2 } = useTranslation();
  const { order } = useOrder(id, {
    fields: DEFAULT_FIELDS
  });
  const { order: preview } = useOrderPreview(id, void 0, {});
  const [activeReturnId, setActiveReturnId] = (0, import_react.useState)();
  const { mutateAsync: initiateReturn } = useInitiateReturn(order.id);
  const { return: activeReturn } = useReturn(activeReturnId, void 0, {
    enabled: !!activeReturnId
  });
  (0, import_react.useEffect)(() => {
    async function run() {
      if (IS_REQUEST_RUNNING || !preview) {
        return;
      }
      if (preview.order_change) {
        if (preview.order_change.change_type === "return_request") {
          setActiveReturnId(preview.order_change.return_id);
        } else {
          navigate(`/orders/${order.id}`, { replace: true });
          toast.error(t2("orders.returns.activeChangeError"));
        }
        return;
      }
      IS_REQUEST_RUNNING = true;
      try {
        const orderReturn = await initiateReturn({ order_id: order.id });
        setActiveReturnId(orderReturn.id);
      } catch (e) {
        navigate(`/orders/${order.id}`, { replace: true });
        toast.error(e.message);
      } finally {
        IS_REQUEST_RUNNING = false;
      }
    }
    run();
  }, [preview]);
  return (0, import_jsx_runtime5.jsx)(RouteFocusModal, { children: activeReturn && preview && order && (0, import_jsx_runtime5.jsx)(
    ReturnCreateForm,
    {
      order,
      activeReturn,
      preview
    }
  ) });
};
export {
  ReturnCreate as Component
};
//# sourceMappingURL=order-create-return-BKTAF3XV-CUQ3WLXN.js.map
