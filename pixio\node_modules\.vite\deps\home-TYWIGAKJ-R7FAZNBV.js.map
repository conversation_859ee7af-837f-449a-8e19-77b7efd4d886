{"version": 3, "sources": ["../../@medusajs/dashboard/dist/home-TYWIGAKJ.mjs"], "sourcesContent": ["import \"./chunk-RPUOO7AV.mjs\";\n\n// src/routes/home/<USER>\nimport { useEffect } from \"react\";\nimport { useNavigate } from \"react-router-dom\";\nimport { jsx } from \"react/jsx-runtime\";\nvar Home = () => {\n  const navigate = useNavigate();\n  useEffect(() => {\n    navigate(\"/orders\", { replace: true });\n  }, [navigate]);\n  return /* @__PURE__ */ jsx(\"div\", {});\n};\nexport {\n  Home as Component\n};\n"], "mappings": ";;;;;;;;;;;;;;;;AAGA,mBAA0B;AAE1B,yBAAoB;AACpB,IAAI,OAAO,MAAM;AACf,QAAM,WAAW,YAAY;AAC7B,8BAAU,MAAM;AACd,aAAS,WAAW,EAAE,SAAS,KAAK,CAAC;AAAA,EACvC,GAAG,CAAC,QAAQ,CAAC;AACb,aAAuB,wBAAI,OAAO,CAAC,CAAC;AACtC;", "names": []}