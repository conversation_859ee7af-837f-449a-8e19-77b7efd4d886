{"version": 3, "sources": ["../../@medusajs/dashboard/dist/shipping-profile-create-KRMI6NUI.mjs"], "sourcesContent": ["import \"./chunk-IUCDCPJU.mjs\";\nimport {\n  KeyboundForm\n} from \"./chunk-6HTZNHPT.mjs\";\nimport {\n  RouteFocusModal,\n  useRouteModal\n} from \"./chunk-4TC5YS65.mjs\";\nimport {\n  Form\n} from \"./chunk-OBQI23QM.mjs\";\nimport {\n  useCreateShippingProfile\n} from \"./chunk-PIR2H25N.mjs\";\nimport \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-RPUOO7AV.mjs\";\n\n// src/routes/shipping-profiles/shipping-profile-create/components/create-shipping-profile-form/create-shipping-profile-form.tsx\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { Button, Heading, Input, Text, toast } from \"@medusajs/ui\";\nimport { useForm } from \"react-hook-form\";\nimport { useTranslation } from \"react-i18next\";\nimport * as zod from \"zod\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar CreateShippingOptionsSchema = zod.object({\n  name: zod.string().min(1),\n  type: zod.string().min(1)\n});\nfunction CreateShippingProfileForm() {\n  const { t } = useTranslation();\n  const { handleSuccess } = useRouteModal();\n  const form = useForm({\n    defaultValues: {\n      name: \"\",\n      type: \"\"\n    },\n    resolver: zodResolver(CreateShippingOptionsSchema)\n  });\n  const { mutateAsync, isPending } = useCreateShippingProfile();\n  const handleSubmit = form.handleSubmit(async (values) => {\n    await mutateAsync(\n      {\n        name: values.name,\n        type: values.type\n      },\n      {\n        onSuccess: ({ shipping_profile }) => {\n          toast.success(\n            t(\"shippingProfile.create.successToast\", {\n              name: shipping_profile.name\n            })\n          );\n          handleSuccess(\n            `/settings/locations/shipping-profiles/${shipping_profile.id}`\n          );\n        },\n        onError: (error) => {\n          toast.error(error.message);\n        }\n      }\n    );\n  });\n  return /* @__PURE__ */ jsx(RouteFocusModal.Form, { form, children: /* @__PURE__ */ jsxs(\n    KeyboundForm,\n    {\n      onSubmit: handleSubmit,\n      className: \"flex h-full flex-col overflow-hidden\",\n      children: [\n        /* @__PURE__ */ jsx(RouteFocusModal.Header, {}),\n        /* @__PURE__ */ jsx(RouteFocusModal.Body, { className: \"flex flex-1 flex-col overflow-hidden\", children: /* @__PURE__ */ jsx(\"div\", { className: \"flex flex-1 flex-col items-center overflow-y-auto\", children: /* @__PURE__ */ jsxs(\"div\", { className: \"mx-auto flex w-full max-w-[720px] flex-col gap-y-8 px-2 py-16\", children: [\n          /* @__PURE__ */ jsxs(\"div\", { children: [\n            /* @__PURE__ */ jsx(Heading, { className: \"capitalize\", children: t(\"shippingProfile.create.header\") }),\n            /* @__PURE__ */ jsx(Text, { size: \"small\", className: \"text-ui-fg-subtle\", children: t(\"shippingProfile.create.hint\") })\n          ] }),\n          /* @__PURE__ */ jsxs(\"div\", { className: \"grid grid-cols-2 gap-4\", children: [\n            /* @__PURE__ */ jsx(\n              Form.Field,\n              {\n                control: form.control,\n                name: \"name\",\n                render: ({ field }) => {\n                  return /* @__PURE__ */ jsxs(Form.Item, { children: [\n                    /* @__PURE__ */ jsx(Form.Label, { children: t(\"fields.name\") }),\n                    /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(Input, { ...field }) }),\n                    /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n                  ] });\n                }\n              }\n            ),\n            /* @__PURE__ */ jsx(\n              Form.Field,\n              {\n                control: form.control,\n                name: \"type\",\n                render: ({ field }) => {\n                  return /* @__PURE__ */ jsxs(Form.Item, { children: [\n                    /* @__PURE__ */ jsx(Form.Label, { tooltip: t(\"shippingProfile.tooltip.type\"), children: t(\"fields.type\") }),\n                    /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(Input, { ...field }) }),\n                    /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n                  ] });\n                }\n              }\n            )\n          ] })\n        ] }) }) }),\n        /* @__PURE__ */ jsx(RouteFocusModal.Footer, { children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center justify-end gap-x-2\", children: [\n          /* @__PURE__ */ jsx(RouteFocusModal.Close, { asChild: true, children: /* @__PURE__ */ jsx(Button, { size: \"small\", variant: \"secondary\", children: t(\"actions.cancel\") }) }),\n          /* @__PURE__ */ jsx(Button, { type: \"submit\", size: \"small\", isLoading: isPending, children: t(\"actions.save\") })\n        ] }) })\n      ]\n    }\n  ) });\n}\n\n// src/routes/shipping-profiles/shipping-profile-create/shipping-profile-create.tsx\nimport { jsx as jsx2 } from \"react/jsx-runtime\";\nfunction ShippingProfileCreate() {\n  return /* @__PURE__ */ jsx2(RouteFocusModal, { children: /* @__PURE__ */ jsx2(CreateShippingProfileForm, {}) });\n}\nexport {\n  ShippingProfileCreate as Component\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyBA,yBAA0B;AA4F1B,IAAAA,sBAA4B;AA3F5B,IAAI,8BAAkC,WAAO;AAAA,EAC3C,MAAU,WAAO,EAAE,IAAI,CAAC;AAAA,EACxB,MAAU,WAAO,EAAE,IAAI,CAAC;AAC1B,CAAC;AACD,SAAS,4BAA4B;AACnC,QAAM,EAAE,GAAAC,GAAE,IAAI,eAAe;AAC7B,QAAM,EAAE,cAAc,IAAI,cAAc;AACxC,QAAM,OAAO,QAAQ;AAAA,IACnB,eAAe;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,IACR;AAAA,IACA,UAAU,EAAY,2BAA2B;AAAA,EACnD,CAAC;AACD,QAAM,EAAE,aAAa,UAAU,IAAI,yBAAyB;AAC5D,QAAM,eAAe,KAAK,aAAa,OAAO,WAAW;AACvD,UAAM;AAAA,MACJ;AAAA,QACE,MAAM,OAAO;AAAA,QACb,MAAM,OAAO;AAAA,MACf;AAAA,MACA;AAAA,QACE,WAAW,CAAC,EAAE,iBAAiB,MAAM;AACnC,gBAAM;AAAA,YACJA,GAAE,uCAAuC;AAAA,cACvC,MAAM,iBAAiB;AAAA,YACzB,CAAC;AAAA,UACH;AACA;AAAA,YACE,yCAAyC,iBAAiB,EAAE;AAAA,UAC9D;AAAA,QACF;AAAA,QACA,SAAS,CAAC,UAAU;AAClB,gBAAM,MAAM,MAAM,OAAO;AAAA,QAC3B;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AACD,aAAuB,wBAAI,gBAAgB,MAAM,EAAE,MAAM,cAA0B;AAAA,IACjF;AAAA,IACA;AAAA,MACE,UAAU;AAAA,MACV,WAAW;AAAA,MACX,UAAU;AAAA,YACQ,wBAAI,gBAAgB,QAAQ,CAAC,CAAC;AAAA,YAC9B,wBAAI,gBAAgB,MAAM,EAAE,WAAW,wCAAwC,cAA0B,wBAAI,OAAO,EAAE,WAAW,qDAAqD,cAA0B,yBAAK,OAAO,EAAE,WAAW,iEAAiE,UAAU;AAAA,cAClT,yBAAK,OAAO,EAAE,UAAU;AAAA,gBACtB,wBAAI,SAAS,EAAE,WAAW,cAAc,UAAUA,GAAE,+BAA+B,EAAE,CAAC;AAAA,gBACtF,wBAAI,MAAM,EAAE,MAAM,SAAS,WAAW,qBAAqB,UAAUA,GAAE,6BAA6B,EAAE,CAAC;AAAA,UACzH,EAAE,CAAC;AAAA,cACa,yBAAK,OAAO,EAAE,WAAW,0BAA0B,UAAU;AAAA,gBAC3D;AAAA,cACd,KAAK;AAAA,cACL;AAAA,gBACE,SAAS,KAAK;AAAA,gBACd,MAAM;AAAA,gBACN,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,6BAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,wBACjC,wBAAI,KAAK,OAAO,EAAE,UAAUA,GAAE,aAAa,EAAE,CAAC;AAAA,wBAC9C,wBAAI,KAAK,SAAS,EAAE,cAA0B,wBAAI,OAAO,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC;AAAA,wBACxE,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,kBAC3C,EAAE,CAAC;AAAA,gBACL;AAAA,cACF;AAAA,YACF;AAAA,gBACgB;AAAA,cACd,KAAK;AAAA,cACL;AAAA,gBACE,SAAS,KAAK;AAAA,gBACd,MAAM;AAAA,gBACN,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,6BAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,wBACjC,wBAAI,KAAK,OAAO,EAAE,SAASA,GAAE,8BAA8B,GAAG,UAAUA,GAAE,aAAa,EAAE,CAAC;AAAA,wBAC1F,wBAAI,KAAK,SAAS,EAAE,cAA0B,wBAAI,OAAO,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC;AAAA,wBACxE,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,kBAC3C,EAAE,CAAC;AAAA,gBACL;AAAA,cACF;AAAA,YACF;AAAA,UACF,EAAE,CAAC;AAAA,QACL,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;AAAA,YACO,wBAAI,gBAAgB,QAAQ,EAAE,cAA0B,yBAAK,OAAO,EAAE,WAAW,yCAAyC,UAAU;AAAA,cAClI,wBAAI,gBAAgB,OAAO,EAAE,SAAS,MAAM,cAA0B,wBAAI,QAAQ,EAAE,MAAM,SAAS,SAAS,aAAa,UAAUA,GAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;AAAA,cAC3J,wBAAI,QAAQ,EAAE,MAAM,UAAU,MAAM,SAAS,WAAW,WAAW,UAAUA,GAAE,cAAc,EAAE,CAAC;AAAA,QAClH,EAAE,CAAC,EAAE,CAAC;AAAA,MACR;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;AAIA,SAAS,wBAAwB;AAC/B,aAAuB,oBAAAC,KAAK,iBAAiB,EAAE,cAA0B,oBAAAA,KAAK,2BAA2B,CAAC,CAAC,EAAE,CAAC;AAChH;", "names": ["import_jsx_runtime", "t", "jsx2"]}