import {
  getCountryByIso2
} from "./chunk-HPGXK5DQ.js";
import {
  taxRegionsQueryKeys,
  useTaxRegion
} from "./chunk-EZ62MM7J.js";
import {
  queryClient
} from "./chunk-VMLNCWLE.js";
import {
  sdk
} from "./chunk-JFCIYOH4.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/chunk-NQIC7ZFS.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var TaxRegionDetailBreadcrumb = (props) => {
  var _a, _b;
  const { id } = props.params || {};
  const { tax_region } = useTaxRegion(id, void 0, {
    initialData: props.data,
    enabled: Boolean(id)
  });
  if (!tax_region) {
    return null;
  }
  return (0, import_jsx_runtime.jsx)("span", { children: ((_a = getCountryByIso2(tax_region.country_code)) == null ? void 0 : _a.display_name) || ((_b = tax_region.country_code) == null ? void 0 : _b.toUpperCase()) });
};
var taxRegionDetailQuery = (id) => ({
  queryKey: taxRegionsQueryKeys.detail(id),
  queryFn: async () => sdk.admin.taxRegion.retrieve(id)
});
var taxRegionLoader = async ({ params }) => {
  const id = params.id;
  const query = taxRegionDetailQuery(id);
  return queryClient.ensureQueryData(query);
};

export {
  TaxRegionDetailBreadcrumb,
  taxRegionLoader
};
//# sourceMappingURL=chunk-MM7T76RN.js.map
