import {
  BadgeListSummary
} from "./chunk-ED62FVEI.js";
import "./chunk-S4XCFSZC.js";
import {
  LinkButton
} from "./chunk-MOY5ZEOS.js";
import {
  SidebarLink
} from "./chunk-5KZIZVHG.js";
import "./chunk-D2VV3NDE.js";
import {
  getFormattedAddress
} from "./chunk-CBWRFFO7.js";
import {
  TwoColumnPage
} from "./chunk-3LNIL4XX.js";
import "./chunk-HPGXK5DQ.js";
import {
  useExtension
} from "./chunk-FTD3ZWHZ.js";
import {
  ActionMenu
} from "./chunk-CFRQOB2M.js";
import "./chunk-66DVUN72.js";
import {
  stockLocationsQueryKeys,
  useDeleteStockLocation,
  useStockLocations
} from "./chunk-ONYSAQ5Z.js";
import {
  queryClient
} from "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import {
  sdk
} from "./chunk-JFCIYOH4.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-5GF3RGIE.js";
import {
  Link,
  redirect,
  useLoaderData
} from "./chunk-T7YBVUWZ.js";
import {
  Buildings,
  Button,
  Container,
  Heading,
  PencilSquare,
  ShoppingBag,
  StatusBadge,
  Text,
  Trash,
  toast,
  usePrompt
} from "./chunk-YP2LLXWB.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-ZSQ362B4.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/location-list-M4RYGH65.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var LOCATION_LIST_FIELDS = "name,*sales_channels,*address,*fulfillment_sets,*fulfillment_sets.service_zones,*fulfillment_sets.service_zones.shipping_options,*fulfillment_sets.service_zones.shipping_options.shipping_profile";
var shippingListQuery = () => ({
  queryKey: stockLocationsQueryKeys.lists(),
  queryFn: async () => {
    return await sdk.admin.stockLocation.list({
      // TODO: change this when RQ is fixed
      fields: LOCATION_LIST_FIELDS
    }).catch((error) => {
      if (error.status === 401) {
        throw redirect("/login");
      }
      throw error;
    });
  }
});
var shippingListLoader = async (_) => {
  const query = shippingListQuery();
  return queryClient.getQueryData(
    query.queryKey
  ) ?? await queryClient.fetchQuery(query);
};
function SalesChannels(props) {
  const { t } = useTranslation();
  const { salesChannels } = props;
  return (0, import_jsx_runtime.jsx)("div", { className: "flex flex-col px-6 py-4", children: (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center justify-between", children: [
    (0, import_jsx_runtime.jsx)(
      Text,
      {
        size: "small",
        weight: "plus",
        className: "text-ui-fg-subtle flex-1",
        as: "div",
        children: t(`stockLocations.salesChannels.label`)
      }
    ),
    (0, import_jsx_runtime.jsx)("div", { className: "flex-1 text-left", children: (salesChannels == null ? void 0 : salesChannels.length) ? (0, import_jsx_runtime.jsx)(
      BadgeListSummary,
      {
        rounded: true,
        inline: true,
        n: 3,
        list: salesChannels.map((s) => s.name)
      }
    ) : "-" })
  ] }) });
}
function FulfillmentSet(props) {
  const { t } = useTranslation();
  const { fulfillmentSet, type } = props;
  const fulfillmentSetExists = !!fulfillmentSet;
  return (0, import_jsx_runtime.jsx)("div", { className: "flex flex-col px-6 py-4", children: (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center justify-between", children: [
    (0, import_jsx_runtime.jsx)(
      Text,
      {
        size: "small",
        weight: "plus",
        className: "text-ui-fg-subtle flex-1",
        as: "div",
        children: t(`stockLocations.fulfillmentSets.${type}.header`)
      }
    ),
    (0, import_jsx_runtime.jsx)("div", { className: "flex-1 text-left", children: (0, import_jsx_runtime.jsx)(StatusBadge, { color: fulfillmentSetExists ? "green" : "grey", children: t(fulfillmentSetExists ? "statuses.enabled" : "statuses.disabled") }) })
  ] }) });
}
function LocationListItem(props) {
  var _a, _b;
  const { location } = props;
  const { t } = useTranslation();
  const prompt = usePrompt();
  const { mutateAsync: deleteLocation } = useDeleteStockLocation(location.id);
  const handleDelete = async () => {
    const result = await prompt({
      title: t("general.areYouSure"),
      description: t("stockLocations.delete.confirmation", {
        name: location.name
      }),
      confirmText: t("actions.remove"),
      cancelText: t("actions.cancel")
    });
    if (!result) {
      return;
    }
    await deleteLocation(void 0, {
      onSuccess: () => {
        toast.success(
          t("shippingProfile.delete.successToast", {
            name: location.name
          })
        );
      },
      onError: (e) => {
        toast.error(e.message);
      }
    });
  };
  return (0, import_jsx_runtime.jsxs)(Container, { className: "flex flex-col divide-y p-0", children: [
    (0, import_jsx_runtime.jsx)("div", { className: "px-6 py-4", children: (0, import_jsx_runtime.jsxs)("div", { className: "flex flex-row items-center justify-between gap-x-4", children: [
      (0, import_jsx_runtime.jsx)("div", { className: "shadow-borders-base flex size-7 items-center justify-center rounded-md", children: (0, import_jsx_runtime.jsx)("div", { className: "bg-ui-bg-field flex size-6 items-center justify-center rounded-[4px]", children: (0, import_jsx_runtime.jsx)(Buildings, { className: "text-ui-fg-subtle" }) }) }),
      (0, import_jsx_runtime.jsxs)("div", { className: "grow-1 flex flex-1 flex-col", children: [
        (0, import_jsx_runtime.jsx)(Text, { weight: "plus", children: location.name }),
        (0, import_jsx_runtime.jsx)(Text, { className: "text-ui-fg-subtle txt-small", children: getFormattedAddress({ address: location.address }).join(", ") })
      ] }),
      (0, import_jsx_runtime.jsxs)("div", { className: "flex grow-0 items-center gap-4", children: [
        (0, import_jsx_runtime.jsx)(
          ActionMenu,
          {
            groups: [
              {
                actions: [
                  {
                    label: t("actions.edit"),
                    icon: (0, import_jsx_runtime.jsx)(PencilSquare, {}),
                    to: `/settings/locations/${location.id}/edit`
                  }
                ]
              },
              {
                actions: [
                  {
                    label: t("actions.delete"),
                    icon: (0, import_jsx_runtime.jsx)(Trash, {}),
                    onClick: handleDelete
                  }
                ]
              }
            ]
          }
        ),
        (0, import_jsx_runtime.jsx)("div", { className: "bg-ui-border-strong h-[12px] w-[1px]" }),
        (0, import_jsx_runtime.jsx)(LinkButton, { to: `/settings/locations/${location.id}`, children: t("actions.viewDetails") })
      ] })
    ] }) }),
    (0, import_jsx_runtime.jsx)(SalesChannels, { salesChannels: location.sales_channels }),
    (0, import_jsx_runtime.jsx)(
      FulfillmentSet,
      {
        type: "pickup",
        fulfillmentSet: (_a = location.fulfillment_sets) == null ? void 0 : _a.find(
          (f) => f.type === "pickup"
        )
      }
    ),
    (0, import_jsx_runtime.jsx)(
      FulfillmentSet,
      {
        type: "shipping",
        fulfillmentSet: (_b = location.fulfillment_sets) == null ? void 0 : _b.find(
          (f) => f.type === "shipping"
        )
      }
    )
  ] });
}
var location_list_item_default = LocationListItem;
var LocationListHeader = () => {
  const { t } = useTranslation();
  return (0, import_jsx_runtime2.jsxs)(Container, { className: "flex h-fit items-center justify-between gap-x-4 px-6 py-4", children: [
    (0, import_jsx_runtime2.jsxs)("div", { children: [
      (0, import_jsx_runtime2.jsx)(Heading, { children: t("stockLocations.domain") }),
      (0, import_jsx_runtime2.jsx)(Text, { className: "text-ui-fg-subtle txt-small", children: t("stockLocations.list.description") })
    ] }),
    (0, import_jsx_runtime2.jsx)(Button, { size: "small", className: "shrink-0", variant: "secondary", asChild: true, children: (0, import_jsx_runtime2.jsx)(Link, { to: "create", children: t("actions.create") }) })
  ] });
};
function LocationList() {
  const initialData = useLoaderData();
  const {
    stock_locations: stockLocations = [],
    isError,
    error
  } = useStockLocations(
    {
      fields: LOCATION_LIST_FIELDS
    },
    { initialData }
  );
  const { getWidgets } = useExtension();
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime3.jsxs)(
    TwoColumnPage,
    {
      widgets: {
        after: getWidgets("location.list.after"),
        before: getWidgets("location.list.before"),
        sideAfter: getWidgets("location.list.side.after"),
        sideBefore: getWidgets("location.list.side.before")
      },
      showJSON: true,
      children: [
        (0, import_jsx_runtime3.jsxs)(TwoColumnPage.Main, { children: [
          (0, import_jsx_runtime3.jsx)(LocationListHeader, {}),
          (0, import_jsx_runtime3.jsx)("div", { className: "flex flex-col gap-3 lg:col-span-2", children: stockLocations.map((location) => (0, import_jsx_runtime3.jsx)(location_list_item_default, { location }, location.id)) })
        ] }),
        (0, import_jsx_runtime3.jsx)(TwoColumnPage.Sidebar, { children: (0, import_jsx_runtime3.jsx)(LinksSection, {}) })
      ]
    }
  );
}
var LinksSection = () => {
  const { t } = useTranslation();
  return (0, import_jsx_runtime3.jsxs)(Container, { className: "p-0", children: [
    (0, import_jsx_runtime3.jsx)("div", { className: "flex items-center justify-between px-6 py-4", children: (0, import_jsx_runtime3.jsx)(Heading, { level: "h2", children: t("stockLocations.sidebar.header") }) }),
    (0, import_jsx_runtime3.jsx)(
      SidebarLink,
      {
        to: "/settings/locations/shipping-profiles",
        labelKey: t("stockLocations.sidebar.shippingProfiles.label"),
        descriptionKey: t(
          "stockLocations.sidebar.shippingProfiles.description"
        ),
        icon: (0, import_jsx_runtime3.jsx)(ShoppingBag, {})
      }
    )
  ] });
};
export {
  LocationList as Component,
  shippingListLoader as loader
};
//# sourceMappingURL=location-list-M4RYGH65-YCH3U7JY.js.map
