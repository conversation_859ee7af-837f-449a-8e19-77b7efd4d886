{"version": 3, "sources": ["../../@medusajs/dashboard/dist/sales-channel-detail-WITSNTXX.mjs"], "sourcesContent": ["import {\n  useProductTableColumns\n} from \"./chunk-G3QXMPRB.mjs\";\nimport {\n  useProductTableQuery\n} from \"./chunk-PCFUZKDS.mjs\";\nimport \"./chunk-IQBAUTU5.mjs\";\nimport \"./chunk-ADOCJB6L.mjs\";\nimport \"./chunk-LQTHYS2Z.mjs\";\nimport \"./chunk-P3UUX2T6.mjs\";\nimport {\n  _DataTable,\n  useDataTable\n} from \"./chunk-UE6PO4FK.mjs\";\nimport \"./chunk-YEDAFXMB.mjs\";\nimport \"./chunk-AOFGTNG6.mjs\";\nimport \"./chunk-EMIHDNB7.mjs\";\nimport {\n  useProductTableFilters\n} from \"./chunk-FZRIVT5D.mjs\";\nimport \"./chunk-SXYXTC2L.mjs\";\nimport \"./chunk-M3VFKDXJ.mjs\";\nimport {\n  SingleColumnPage\n} from \"./chunk-2RQLKDBF.mjs\";\nimport \"./chunk-C76H5USB.mjs\";\nimport \"./chunk-KSV3NQOT.mjs\";\nimport \"./chunk-MNXC6Q4F.mjs\";\nimport {\n  useExtension\n} from \"./chunk-C5P5PL3E.mjs\";\nimport {\n  SingleColumnPageSkeleton\n} from \"./chunk-LPEUYMRK.mjs\";\nimport {\n  ActionMenu\n} from \"./chunk-3NJTXRIY.mjs\";\nimport \"./chunk-OC7BQLYI.mjs\";\nimport \"./chunk-NYXYHZE6.mjs\";\nimport \"./chunk-Z5UDPQIH.mjs\";\nimport \"./chunk-KOSCMAIC.mjs\";\nimport \"./chunk-X6DSNTTX.mjs\";\nimport \"./chunk-I6E6CALJ.mjs\";\nimport \"./chunk-B4GODIOW.mjs\";\nimport \"./chunk-F6IJV2I2.mjs\";\nimport \"./chunk-QTCZFYFH.mjs\";\nimport \"./chunk-ENV6YVOM.mjs\";\nimport \"./chunk-PIR2H25N.mjs\";\nimport \"./chunk-RLY2SL5E.mjs\";\nimport \"./chunk-C5LYZZZ5.mjs\";\nimport \"./chunk-2ZKVRTBW.mjs\";\nimport \"./chunk-FO3VP56P.mjs\";\nimport \"./chunk-YS65UGPC.mjs\";\nimport \"./chunk-F6PXCY3N.mjs\";\nimport \"./chunk-3OHH43G6.mjs\";\nimport \"./chunk-G2H6MAK7.mjs\";\nimport \"./chunk-GRT22PE5.mjs\";\nimport \"./chunk-32IQRUVY.mjs\";\nimport \"./chunk-FNYASI54.mjs\";\nimport \"./chunk-FVC7M755.mjs\";\nimport \"./chunk-ZJ3OFMHB.mjs\";\nimport {\n  useDeleteSalesChannel,\n  useSalesChannel,\n  useSalesChannelRemoveProducts\n} from \"./chunk-PNU5HPGY.mjs\";\nimport \"./chunk-V2LANK5S.mjs\";\nimport \"./chunk-QZ6PT4QV.mjs\";\nimport \"./chunk-QL4XKIVL.mjs\";\nimport {\n  productsQueryKeys,\n  useProducts\n} from \"./chunk-6I62UDJA.mjs\";\nimport {\n  queryClient\n} from \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport {\n  sdk\n} from \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-RPUOO7AV.mjs\";\n\n// src/routes/sales-channels/sales-channel-detail/breadcrumb.tsx\nimport { jsx } from \"react/jsx-runtime\";\nvar SalesChannelDetailBreadcrumb = (props) => {\n  const { id } = props.params || {};\n  const { sales_channel } = useSalesChannel(id, {\n    initialData: props.data,\n    enabled: Boolean(id)\n  });\n  if (!sales_channel) {\n    return null;\n  }\n  return /* @__PURE__ */ jsx(\"span\", { children: sales_channel.name });\n};\n\n// src/routes/sales-channels/sales-channel-detail/loader.ts\nvar salesChannelDetailQuery = (id) => ({\n  queryKey: productsQueryKeys.detail(id),\n  queryFn: async () => sdk.admin.salesChannel.retrieve(id)\n});\nvar salesChannelLoader = async ({ params }) => {\n  const id = params.id;\n  const query = salesChannelDetailQuery(id);\n  return queryClient.ensureQueryData(query);\n};\n\n// src/routes/sales-channels/sales-channel-detail/sales-channel-detail.tsx\nimport { useLoaderData, useParams } from \"react-router-dom\";\n\n// src/routes/sales-channels/sales-channel-detail/components/sales-channel-general-section/sales-channel-general-section.tsx\nimport { PencilSquare, Trash } from \"@medusajs/icons\";\nimport {\n  Container,\n  Heading,\n  StatusBadge,\n  Text,\n  toast,\n  usePrompt\n} from \"@medusajs/ui\";\nimport { useTranslation } from \"react-i18next\";\nimport { useNavigate } from \"react-router-dom\";\nimport { jsx as jsx2, jsxs } from \"react/jsx-runtime\";\nvar SalesChannelGeneralSection = ({\n  salesChannel\n}) => {\n  const { t } = useTranslation();\n  const prompt = usePrompt();\n  const navigate = useNavigate();\n  const { mutateAsync } = useDeleteSalesChannel(salesChannel.id);\n  const handleDelete = async () => {\n    const confirm = await prompt({\n      title: t(\"general.areYouSure\"),\n      description: t(\"salesChannels.deleteSalesChannelWarning\", {\n        name: salesChannel.name\n      }),\n      verificationInstruction: t(\"general.typeToConfirm\"),\n      verificationText: salesChannel.name,\n      confirmText: t(\"actions.delete\"),\n      cancelText: t(\"actions.cancel\")\n    });\n    if (!confirm) {\n      return;\n    }\n    await mutateAsync(void 0, {\n      onSuccess: () => {\n        toast.success(t(\"salesChannels.toast.delete\"));\n        navigate(\"/settings/sales-channels\", { replace: true });\n      },\n      onError: (e) => {\n        toast.error(e.message);\n      }\n    });\n  };\n  return /* @__PURE__ */ jsxs(Container, { className: \"divide-y p-0\", children: [\n    /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center justify-between px-6 py-4\", children: [\n      /* @__PURE__ */ jsx2(Heading, { children: salesChannel.name }),\n      /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center gap-x-2\", children: [\n        /* @__PURE__ */ jsx2(StatusBadge, { color: salesChannel.is_disabled ? \"red\" : \"green\", children: t(`general.${salesChannel.is_disabled ? \"disabled\" : \"enabled\"}`) }),\n        /* @__PURE__ */ jsx2(\n          ActionMenu,\n          {\n            groups: [\n              {\n                actions: [\n                  {\n                    icon: /* @__PURE__ */ jsx2(PencilSquare, {}),\n                    label: t(\"actions.edit\"),\n                    to: `/settings/sales-channels/${salesChannel.id}/edit`\n                  }\n                ]\n              },\n              {\n                actions: [\n                  {\n                    icon: /* @__PURE__ */ jsx2(Trash, {}),\n                    label: t(\"actions.delete\"),\n                    onClick: handleDelete\n                  }\n                ]\n              }\n            ]\n          }\n        )\n      ] })\n    ] }),\n    /* @__PURE__ */ jsxs(\"div\", { className: \"text-ui-fg-subtle grid grid-cols-2 items-start px-6 py-4\", children: [\n      /* @__PURE__ */ jsx2(Text, { size: \"small\", leading: \"compact\", weight: \"plus\", children: t(\"fields.description\") }),\n      /* @__PURE__ */ jsx2(Text, { size: \"small\", leading: \"compact\", children: salesChannel.description || \"-\" })\n    ] })\n  ] });\n};\n\n// src/routes/sales-channels/sales-channel-detail/components/sales-channel-product-section/sales-channel-product-section.tsx\nimport { PencilSquare as PencilSquare2, Trash as Trash2 } from \"@medusajs/icons\";\nimport {\n  Button,\n  Checkbox,\n  Container as Container2,\n  Heading as Heading2,\n  toast as toast2,\n  usePrompt as usePrompt2\n} from \"@medusajs/ui\";\nimport { createColumnHelper } from \"@tanstack/react-table\";\nimport { useMemo, useState } from \"react\";\nimport { useTranslation as useTranslation2 } from \"react-i18next\";\nimport { Link } from \"react-router-dom\";\nimport { keepPreviousData } from \"@tanstack/react-query\";\nimport { jsx as jsx3, jsxs as jsxs2 } from \"react/jsx-runtime\";\nvar PAGE_SIZE = 10;\nvar SalesChannelProductSection = ({\n  salesChannel\n}) => {\n  const [rowSelection, setRowSelection] = useState({});\n  const { searchParams, raw } = useProductTableQuery({ pageSize: PAGE_SIZE });\n  const {\n    products,\n    count,\n    isPending: isLoading,\n    isError,\n    error\n  } = useProducts(\n    {\n      ...searchParams,\n      sales_channel_id: [salesChannel.id]\n    },\n    {\n      placeholderData: keepPreviousData\n    }\n  );\n  const columns = useColumns();\n  const filters = useProductTableFilters([\"sales_channel_id\"]);\n  const { table } = useDataTable({\n    data: products ?? [],\n    columns,\n    count,\n    enablePagination: true,\n    enableRowSelection: true,\n    pageSize: PAGE_SIZE,\n    getRowId: (row) => row.id,\n    rowSelection: {\n      state: rowSelection,\n      updater: setRowSelection\n    },\n    meta: {\n      salesChannelId: salesChannel.id\n    }\n  });\n  const { mutateAsync } = useSalesChannelRemoveProducts(salesChannel.id);\n  const prompt = usePrompt2();\n  const { t } = useTranslation2();\n  const handleRemove = async () => {\n    const ids = Object.keys(rowSelection);\n    const result = await prompt({\n      title: t(\"general.areYouSure\"),\n      description: t(\"salesChannels.removeProductsWarning\", {\n        count: ids.length,\n        sales_channel: salesChannel.name\n      }),\n      confirmText: t(\"actions.delete\"),\n      cancelText: t(\"actions.cancel\")\n    });\n    if (!result) {\n      return;\n    }\n    await mutateAsync(ids, {\n      onSuccess: () => {\n        toast2.success(t(\"salesChannels.toast.update\"));\n        setRowSelection({});\n      },\n      onError: (error2) => {\n        toast2.error(error2.message);\n      }\n    });\n  };\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsxs2(Container2, { className: \"divide-y p-0\", children: [\n    /* @__PURE__ */ jsxs2(\"div\", { className: \"flex items-center justify-between px-6 py-4\", children: [\n      /* @__PURE__ */ jsx3(Heading2, { level: \"h2\", children: t(\"products.domain\") }),\n      /* @__PURE__ */ jsx3(Link, { to: `/settings/sales-channels/${salesChannel.id}/add-products`, children: /* @__PURE__ */ jsx3(Button, { size: \"small\", variant: \"secondary\", children: t(\"general.add\") }) })\n    ] }),\n    /* @__PURE__ */ jsx3(\n      _DataTable,\n      {\n        table,\n        columns,\n        pageSize: PAGE_SIZE,\n        commands: [\n          {\n            action: handleRemove,\n            label: t(\"actions.remove\"),\n            shortcut: \"r\"\n          }\n        ],\n        count,\n        pagination: true,\n        search: true,\n        filters,\n        navigateTo: (row) => `/products/${row.id}`,\n        isLoading,\n        orderBy: [\n          { key: \"title\", label: t(\"fields.title\") },\n          { key: \"status\", label: t(\"fields.status\") },\n          { key: \"created_at\", label: t(\"fields.createdAt\") },\n          { key: \"updated_at\", label: t(\"fields.updatedAt\") }\n        ],\n        queryObject: raw,\n        noRecords: {\n          message: t(\"salesChannels.products.list.noRecordsMessage\")\n        }\n      }\n    )\n  ] });\n};\nvar columnHelper = createColumnHelper();\nvar useColumns = () => {\n  const base = useProductTableColumns();\n  return useMemo(\n    () => [\n      columnHelper.display({\n        id: \"select\",\n        header: ({ table }) => {\n          return /* @__PURE__ */ jsx3(\n            Checkbox,\n            {\n              checked: table.getIsSomePageRowsSelected() ? \"indeterminate\" : table.getIsAllPageRowsSelected(),\n              onCheckedChange: (value) => table.toggleAllPageRowsSelected(!!value)\n            }\n          );\n        },\n        cell: ({ row }) => {\n          return /* @__PURE__ */ jsx3(\n            Checkbox,\n            {\n              checked: row.getIsSelected(),\n              onCheckedChange: (value) => row.toggleSelected(!!value),\n              onClick: (e) => {\n                e.stopPropagation();\n              }\n            }\n          );\n        }\n      }),\n      ...base,\n      columnHelper.display({\n        id: \"actions\",\n        cell: ({ row, table }) => {\n          const { salesChannelId } = table.options.meta;\n          return /* @__PURE__ */ jsx3(\n            ProductListCellActions,\n            {\n              productId: row.original.id,\n              salesChannelId\n            }\n          );\n        }\n      })\n    ],\n    [base]\n  );\n};\nvar ProductListCellActions = ({\n  salesChannelId,\n  productId\n}) => {\n  const { t } = useTranslation2();\n  const { mutateAsync } = useSalesChannelRemoveProducts(salesChannelId);\n  const onRemove = async () => {\n    await mutateAsync([productId], {\n      onSuccess: () => {\n        toast2.success(t(\"salesChannels.toast.update\"));\n      },\n      onError: (e) => {\n        toast2.error(e.message);\n      }\n    });\n  };\n  return /* @__PURE__ */ jsx3(\n    ActionMenu,\n    {\n      groups: [\n        {\n          actions: [\n            {\n              icon: /* @__PURE__ */ jsx3(PencilSquare2, {}),\n              label: t(\"actions.edit\"),\n              to: `/products/${productId}`\n            }\n          ]\n        },\n        {\n          actions: [\n            {\n              icon: /* @__PURE__ */ jsx3(Trash2, {}),\n              label: t(\"actions.remove\"),\n              onClick: onRemove\n            }\n          ]\n        }\n      ]\n    }\n  );\n};\n\n// src/routes/sales-channels/sales-channel-detail/sales-channel-detail.tsx\nimport { jsx as jsx4, jsxs as jsxs3 } from \"react/jsx-runtime\";\nvar SalesChannelDetail = () => {\n  const initialData = useLoaderData();\n  const { id } = useParams();\n  const { sales_channel, isPending: isLoading } = useSalesChannel(id, {\n    initialData\n  });\n  const { getWidgets } = useExtension();\n  if (isLoading || !sales_channel) {\n    return /* @__PURE__ */ jsx4(SingleColumnPageSkeleton, { sections: 2, showJSON: true, showMetadata: true });\n  }\n  return /* @__PURE__ */ jsxs3(\n    SingleColumnPage,\n    {\n      widgets: {\n        before: getWidgets(\"sales_channel.details.before\"),\n        after: getWidgets(\"sales_channel.details.after\")\n      },\n      showJSON: true,\n      showMetadata: true,\n      data: sales_channel,\n      children: [\n        /* @__PURE__ */ jsx4(SalesChannelGeneralSection, { salesChannel: sales_channel }),\n        /* @__PURE__ */ jsx4(SalesChannelProductSection, { salesChannel: sales_channel })\n      ]\n    }\n  );\n};\nexport {\n  SalesChannelDetailBreadcrumb as Breadcrumb,\n  SalesChannelDetail as Component,\n  salesChannelLoader as loader\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmFA,yBAAoB;AAuCpB,IAAAA,sBAAkC;AAkFlC,mBAAkC;AAIlC,IAAAC,sBAA2C;AAuM3C,IAAAA,sBAA2C;AAnU3C,IAAI,+BAA+B,CAAC,UAAU;AAC5C,QAAM,EAAE,GAAG,IAAI,MAAM,UAAU,CAAC;AAChC,QAAM,EAAE,cAAc,IAAI,gBAAgB,IAAI;AAAA,IAC5C,aAAa,MAAM;AAAA,IACnB,SAAS,QAAQ,EAAE;AAAA,EACrB,CAAC;AACD,MAAI,CAAC,eAAe;AAClB,WAAO;AAAA,EACT;AACA,aAAuB,wBAAI,QAAQ,EAAE,UAAU,cAAc,KAAK,CAAC;AACrE;AAGA,IAAI,0BAA0B,CAAC,QAAQ;AAAA,EACrC,UAAU,kBAAkB,OAAO,EAAE;AAAA,EACrC,SAAS,YAAY,IAAI,MAAM,aAAa,SAAS,EAAE;AACzD;AACA,IAAI,qBAAqB,OAAO,EAAE,OAAO,MAAM;AAC7C,QAAM,KAAK,OAAO;AAClB,QAAM,QAAQ,wBAAwB,EAAE;AACxC,SAAO,YAAY,gBAAgB,KAAK;AAC1C;AAkBA,IAAI,6BAA6B,CAAC;AAAA,EAChC;AACF,MAAM;AACJ,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,QAAM,SAAS,UAAU;AACzB,QAAM,WAAW,YAAY;AAC7B,QAAM,EAAE,YAAY,IAAI,sBAAsB,aAAa,EAAE;AAC7D,QAAM,eAAe,YAAY;AAC/B,UAAM,UAAU,MAAM,OAAO;AAAA,MAC3B,OAAO,EAAE,oBAAoB;AAAA,MAC7B,aAAa,EAAE,2CAA2C;AAAA,QACxD,MAAM,aAAa;AAAA,MACrB,CAAC;AAAA,MACD,yBAAyB,EAAE,uBAAuB;AAAA,MAClD,kBAAkB,aAAa;AAAA,MAC/B,aAAa,EAAE,gBAAgB;AAAA,MAC/B,YAAY,EAAE,gBAAgB;AAAA,IAChC,CAAC;AACD,QAAI,CAAC,SAAS;AACZ;AAAA,IACF;AACA,UAAM,YAAY,QAAQ;AAAA,MACxB,WAAW,MAAM;AACf,cAAM,QAAQ,EAAE,4BAA4B,CAAC;AAC7C,iBAAS,4BAA4B,EAAE,SAAS,KAAK,CAAC;AAAA,MACxD;AAAA,MACA,SAAS,CAAC,MAAM;AACd,cAAM,MAAM,EAAE,OAAO;AAAA,MACvB;AAAA,IACF,CAAC;AAAA,EACH;AACA,aAAuB,0BAAK,WAAW,EAAE,WAAW,gBAAgB,UAAU;AAAA,QAC5D,0BAAK,OAAO,EAAE,WAAW,+CAA+C,UAAU;AAAA,UAChF,oBAAAC,KAAK,SAAS,EAAE,UAAU,aAAa,KAAK,CAAC;AAAA,UAC7C,0BAAK,OAAO,EAAE,WAAW,6BAA6B,UAAU;AAAA,YAC9D,oBAAAA,KAAK,aAAa,EAAE,OAAO,aAAa,cAAc,QAAQ,SAAS,UAAU,EAAE,WAAW,aAAa,cAAc,aAAa,SAAS,EAAE,EAAE,CAAC;AAAA,YACpJ,oBAAAA;AAAA,UACd;AAAA,UACA;AAAA,YACE,QAAQ;AAAA,cACN;AAAA,gBACE,SAAS;AAAA,kBACP;AAAA,oBACE,UAAsB,oBAAAA,KAAK,cAAc,CAAC,CAAC;AAAA,oBAC3C,OAAO,EAAE,cAAc;AAAA,oBACvB,IAAI,4BAA4B,aAAa,EAAE;AAAA,kBACjD;AAAA,gBACF;AAAA,cACF;AAAA,cACA;AAAA,gBACE,SAAS;AAAA,kBACP;AAAA,oBACE,UAAsB,oBAAAA,KAAK,OAAO,CAAC,CAAC;AAAA,oBACpC,OAAO,EAAE,gBAAgB;AAAA,oBACzB,SAAS;AAAA,kBACX;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF,EAAE,CAAC;AAAA,IACL,EAAE,CAAC;AAAA,QACa,0BAAK,OAAO,EAAE,WAAW,4DAA4D,UAAU;AAAA,UAC7F,oBAAAA,KAAK,MAAM,EAAE,MAAM,SAAS,SAAS,WAAW,QAAQ,QAAQ,UAAU,EAAE,oBAAoB,EAAE,CAAC;AAAA,UACnG,oBAAAA,KAAK,MAAM,EAAE,MAAM,SAAS,SAAS,WAAW,UAAU,aAAa,eAAe,IAAI,CAAC;AAAA,IAC7G,EAAE,CAAC;AAAA,EACL,EAAE,CAAC;AACL;AAkBA,IAAI,YAAY;AAChB,IAAI,6BAA6B,CAAC;AAAA,EAChC;AACF,MAAM;AACJ,QAAM,CAAC,cAAc,eAAe,QAAI,uBAAS,CAAC,CAAC;AACnD,QAAM,EAAE,cAAc,IAAI,IAAI,qBAAqB,EAAE,UAAU,UAAU,CAAC;AAC1E,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA,WAAW;AAAA,IACX;AAAA,IACA;AAAA,EACF,IAAI;AAAA,IACF;AAAA,MACE,GAAG;AAAA,MACH,kBAAkB,CAAC,aAAa,EAAE;AAAA,IACpC;AAAA,IACA;AAAA,MACE,iBAAiB;AAAA,IACnB;AAAA,EACF;AACA,QAAM,UAAU,WAAW;AAC3B,QAAM,UAAU,uBAAuB,CAAC,kBAAkB,CAAC;AAC3D,QAAM,EAAE,MAAM,IAAI,aAAa;AAAA,IAC7B,MAAM,YAAY,CAAC;AAAA,IACnB;AAAA,IACA;AAAA,IACA,kBAAkB;AAAA,IAClB,oBAAoB;AAAA,IACpB,UAAU;AAAA,IACV,UAAU,CAAC,QAAQ,IAAI;AAAA,IACvB,cAAc;AAAA,MACZ,OAAO;AAAA,MACP,SAAS;AAAA,IACX;AAAA,IACA,MAAM;AAAA,MACJ,gBAAgB,aAAa;AAAA,IAC/B;AAAA,EACF,CAAC;AACD,QAAM,EAAE,YAAY,IAAI,8BAA8B,aAAa,EAAE;AACrE,QAAM,SAAS,UAAW;AAC1B,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,QAAM,eAAe,YAAY;AAC/B,UAAM,MAAM,OAAO,KAAK,YAAY;AACpC,UAAM,SAAS,MAAM,OAAO;AAAA,MAC1B,OAAO,EAAE,oBAAoB;AAAA,MAC7B,aAAa,EAAE,uCAAuC;AAAA,QACpD,OAAO,IAAI;AAAA,QACX,eAAe,aAAa;AAAA,MAC9B,CAAC;AAAA,MACD,aAAa,EAAE,gBAAgB;AAAA,MAC/B,YAAY,EAAE,gBAAgB;AAAA,IAChC,CAAC;AACD,QAAI,CAAC,QAAQ;AACX;AAAA,IACF;AACA,UAAM,YAAY,KAAK;AAAA,MACrB,WAAW,MAAM;AACf,cAAO,QAAQ,EAAE,4BAA4B,CAAC;AAC9C,wBAAgB,CAAC,CAAC;AAAA,MACpB;AAAA,MACA,SAAS,CAAC,WAAW;AACnB,cAAO,MAAM,OAAO,OAAO;AAAA,MAC7B;AAAA,IACF,CAAC;AAAA,EACH;AACA,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,oBAAAC,MAAM,WAAY,EAAE,WAAW,gBAAgB,UAAU;AAAA,QAC9D,oBAAAA,MAAM,OAAO,EAAE,WAAW,+CAA+C,UAAU;AAAA,UACjF,oBAAAC,KAAK,SAAU,EAAE,OAAO,MAAM,UAAU,EAAE,iBAAiB,EAAE,CAAC;AAAA,UAC9D,oBAAAA,KAAK,MAAM,EAAE,IAAI,4BAA4B,aAAa,EAAE,iBAAiB,cAA0B,oBAAAA,KAAK,QAAQ,EAAE,MAAM,SAAS,SAAS,aAAa,UAAU,EAAE,aAAa,EAAE,CAAC,EAAE,CAAC;AAAA,IAC5M,EAAE,CAAC;AAAA,QACa,oBAAAA;AAAA,MACd;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,QACA,UAAU;AAAA,QACV,UAAU;AAAA,UACR;AAAA,YACE,QAAQ;AAAA,YACR,OAAO,EAAE,gBAAgB;AAAA,YACzB,UAAU;AAAA,UACZ;AAAA,QACF;AAAA,QACA;AAAA,QACA,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR;AAAA,QACA,YAAY,CAAC,QAAQ,aAAa,IAAI,EAAE;AAAA,QACxC;AAAA,QACA,SAAS;AAAA,UACP,EAAE,KAAK,SAAS,OAAO,EAAE,cAAc,EAAE;AAAA,UACzC,EAAE,KAAK,UAAU,OAAO,EAAE,eAAe,EAAE;AAAA,UAC3C,EAAE,KAAK,cAAc,OAAO,EAAE,kBAAkB,EAAE;AAAA,UAClD,EAAE,KAAK,cAAc,OAAO,EAAE,kBAAkB,EAAE;AAAA,QACpD;AAAA,QACA,aAAa;AAAA,QACb,WAAW;AAAA,UACT,SAAS,EAAE,8CAA8C;AAAA,QAC3D;AAAA,MACF;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;AACA,IAAI,eAAe,mBAAmB;AACtC,IAAI,aAAa,MAAM;AACrB,QAAM,OAAO,uBAAuB;AACpC,aAAO;AAAA,IACL,MAAM;AAAA,MACJ,aAAa,QAAQ;AAAA,QACnB,IAAI;AAAA,QACJ,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,qBAAuB,oBAAAA;AAAA,YACrB;AAAA,YACA;AAAA,cACE,SAAS,MAAM,0BAA0B,IAAI,kBAAkB,MAAM,yBAAyB;AAAA,cAC9F,iBAAiB,CAAC,UAAU,MAAM,0BAA0B,CAAC,CAAC,KAAK;AAAA,YACrE;AAAA,UACF;AAAA,QACF;AAAA,QACA,MAAM,CAAC,EAAE,IAAI,MAAM;AACjB,qBAAuB,oBAAAA;AAAA,YACrB;AAAA,YACA;AAAA,cACE,SAAS,IAAI,cAAc;AAAA,cAC3B,iBAAiB,CAAC,UAAU,IAAI,eAAe,CAAC,CAAC,KAAK;AAAA,cACtD,SAAS,CAAC,MAAM;AACd,kBAAE,gBAAgB;AAAA,cACpB;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AAAA,MACD,GAAG;AAAA,MACH,aAAa,QAAQ;AAAA,QACnB,IAAI;AAAA,QACJ,MAAM,CAAC,EAAE,KAAK,MAAM,MAAM;AACxB,gBAAM,EAAE,eAAe,IAAI,MAAM,QAAQ;AACzC,qBAAuB,oBAAAA;AAAA,YACrB;AAAA,YACA;AAAA,cACE,WAAW,IAAI,SAAS;AAAA,cACxB;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAAA,IACA,CAAC,IAAI;AAAA,EACP;AACF;AACA,IAAI,yBAAyB,CAAC;AAAA,EAC5B;AAAA,EACA;AACF,MAAM;AACJ,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,QAAM,EAAE,YAAY,IAAI,8BAA8B,cAAc;AACpE,QAAM,WAAW,YAAY;AAC3B,UAAM,YAAY,CAAC,SAAS,GAAG;AAAA,MAC7B,WAAW,MAAM;AACf,cAAO,QAAQ,EAAE,4BAA4B,CAAC;AAAA,MAChD;AAAA,MACA,SAAS,CAAC,MAAM;AACd,cAAO,MAAM,EAAE,OAAO;AAAA,MACxB;AAAA,IACF,CAAC;AAAA,EACH;AACA,aAAuB,oBAAAA;AAAA,IACrB;AAAA,IACA;AAAA,MACE,QAAQ;AAAA,QACN;AAAA,UACE,SAAS;AAAA,YACP;AAAA,cACE,UAAsB,oBAAAA,KAAK,cAAe,CAAC,CAAC;AAAA,cAC5C,OAAO,EAAE,cAAc;AAAA,cACvB,IAAI,aAAa,SAAS;AAAA,YAC5B;AAAA,UACF;AAAA,QACF;AAAA,QACA;AAAA,UACE,SAAS;AAAA,YACP;AAAA,cACE,UAAsB,oBAAAA,KAAK,OAAQ,CAAC,CAAC;AAAA,cACrC,OAAO,EAAE,gBAAgB;AAAA,cACzB,SAAS;AAAA,YACX;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAIA,IAAI,qBAAqB,MAAM;AAC7B,QAAM,cAAc,cAAc;AAClC,QAAM,EAAE,GAAG,IAAI,UAAU;AACzB,QAAM,EAAE,eAAe,WAAW,UAAU,IAAI,gBAAgB,IAAI;AAAA,IAClE;AAAA,EACF,CAAC;AACD,QAAM,EAAE,WAAW,IAAI,aAAa;AACpC,MAAI,aAAa,CAAC,eAAe;AAC/B,eAAuB,oBAAAC,KAAK,0BAA0B,EAAE,UAAU,GAAG,UAAU,MAAM,cAAc,KAAK,CAAC;AAAA,EAC3G;AACA,aAAuB,oBAAAC;AAAA,IACrB;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP,QAAQ,WAAW,8BAA8B;AAAA,QACjD,OAAO,WAAW,6BAA6B;AAAA,MACjD;AAAA,MACA,UAAU;AAAA,MACV,cAAc;AAAA,MACd,MAAM;AAAA,MACN,UAAU;AAAA,YACQ,oBAAAD,KAAK,4BAA4B,EAAE,cAAc,cAAc,CAAC;AAAA,YAChE,oBAAAA,KAAK,4BAA4B,EAAE,cAAc,cAAc,CAAC;AAAA,MAClF;AAAA,IACF;AAAA,EACF;AACF;", "names": ["import_jsx_runtime", "import_jsx_runtime", "jsx2", "jsxs2", "jsx3", "jsx4", "jsxs3"]}