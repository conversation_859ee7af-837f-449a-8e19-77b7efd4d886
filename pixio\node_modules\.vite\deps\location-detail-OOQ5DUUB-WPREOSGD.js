import {
  isOptionEnabledInStore,
  isReturnOption
} from "./chunk-6IU4OEGQ.js";
import {
  ListSummary
} from "./chunk-IP6YNP6I.js";
import "./chunk-S4XCFSZC.js";
import {
  formatProvider
} from "./chunk-LVAKEKGS.js";
import {
  LinkButton
} from "./chunk-MOY5ZEOS.js";
import {
  IconAvatar
} from "./chunk-D2VV3NDE.js";
import {
  getFormattedAddress
} from "./chunk-CBWRFFO7.js";
import {
  NoRecords
} from "./chunk-X3TOWPPJ.js";
import {
  TwoColumnPage
} from "./chunk-3LNIL4XX.js";
import {
  countries
} from "./chunk-HPGXK5DQ.js";
import {
  useExtension
} from "./chunk-FTD3ZWHZ.js";
import {
  TwoColumnPageSkeleton
} from "./chunk-YXT43UJF.js";
import {
  ActionMenu
} from "./chunk-CFRQOB2M.js";
import "./chunk-66DVUN72.js";
import "./chunk-WISME5HP.js";
import "./chunk-WAUSOHFQ.js";
import "./chunk-3ORK3QZ4.js";
import "./chunk-3A5TVVNI.js";
import "./chunk-EZ62MM7J.js";
import "./chunk-3R6LYIXM.js";
import "./chunk-2P2O4TMQ.js";
import {
  useDeleteFulfillmentServiceZone,
  useDeleteFulfillmentSet
} from "./chunk-KI3HEITX.js";
import "./chunk-2VUCAO7L.js";
import "./chunk-PTIULTKD.js";
import "./chunk-KISXY2LD.js";
import "./chunk-L7C3XPHJ.js";
import "./chunk-T7MG2EIJ.js";
import "./chunk-MER4VYPR.js";
import "./chunk-2AWKOUCD.js";
import "./chunk-YL3TYBFR.js";
import "./chunk-TZWW72YW.js";
import {
  useDeleteShippingOption
} from "./chunk-MSQ25CWB.js";
import {
  stockLocationsQueryKeys,
  useCreateStockLocationFulfillmentSet,
  useDeleteStockLocation,
  useFulfillmentProviders,
  useStockLocation
} from "./chunk-ONYSAQ5Z.js";
import "./chunk-NIH7SAXN.js";
import "./chunk-UEJFWAFE.js";
import "./chunk-GR55MFKT.js";
import "./chunk-AAFHKNJG.js";
import "./chunk-7XDBFDTZ.js";
import "./chunk-3JKGO5XL.js";
import {
  useSalesChannels
} from "./chunk-OISUTS7G.js";
import "./chunk-AJYMIHLQ.js";
import {
  queryClient
} from "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import {
  sdk
} from "./chunk-JFCIYOH4.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-5GF3RGIE.js";
import {
  useLoaderData,
  useNavigate,
  useParams
} from "./chunk-T7YBVUWZ.js";
import {
  ArchiveBox,
  Badge,
  Channels,
  Container,
  CurrencyDollar,
  Divider,
  HandTruck,
  Heading,
  IconButton,
  Map,
  PencilSquare,
  Plus,
  StatusBadge,
  Text,
  Trash,
  TriangleDownMini,
  toast,
  usePrompt
} from "./chunk-YP2LLXWB.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-ZSQ362B4.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/location-detail-OOQ5DUUB.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_react = __toESM(require_react(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var import_react2 = __toESM(require_react(), 1);
var import_jsx_runtime4 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime5 = __toESM(require_jsx_runtime(), 1);
var LOCATION_DETAILS_FIELD = "name,*sales_channels,*address,fulfillment_sets.type,fulfillment_sets.name,*fulfillment_sets.service_zones.geo_zones,*fulfillment_sets.service_zones,*fulfillment_sets.service_zones.shipping_options,*fulfillment_sets.service_zones.shipping_options.rules,*fulfillment_sets.service_zones.shipping_options.shipping_profile,*fulfillment_providers";
var LocationDetailBreadcrumb = (props) => {
  const { location_id } = props.params || {};
  const { stock_location } = useStockLocation(
    location_id,
    {
      fields: LOCATION_DETAILS_FIELD
    },
    {
      initialData: props.data,
      enabled: Boolean(location_id)
    }
  );
  if (!stock_location) {
    return null;
  }
  return (0, import_jsx_runtime.jsx)("span", { children: stock_location.name });
};
var locationQuery = (id) => ({
  queryKey: stockLocationsQueryKeys.detail(id, {
    fields: LOCATION_DETAILS_FIELD
  }),
  queryFn: async () => sdk.admin.stockLocation.retrieve(id, {
    fields: LOCATION_DETAILS_FIELD
  })
});
var locationLoader = async ({ params }) => {
  const id = params.location_id;
  const query = locationQuery(id);
  return queryClient.ensureQueryData(query);
};
var LocationGeneralSection = ({
  location
}) => {
  var _a, _b;
  return (0, import_jsx_runtime2.jsxs)(import_jsx_runtime2.Fragment, { children: [
    (0, import_jsx_runtime2.jsx)(Container, { className: "p-0", children: (0, import_jsx_runtime2.jsxs)("div", { className: "flex items-center justify-between px-6 py-4", children: [
      (0, import_jsx_runtime2.jsxs)("div", { children: [
        (0, import_jsx_runtime2.jsx)(Heading, { children: location.name }),
        (0, import_jsx_runtime2.jsx)(Text, { className: "text-ui-fg-subtle txt-small", children: getFormattedAddress({ address: location.address }).join(", ") })
      ] }),
      (0, import_jsx_runtime2.jsx)(Actions, { location })
    ] }) }),
    (0, import_jsx_runtime2.jsx)(
      FulfillmentSet,
      {
        locationId: location.id,
        locationName: location.name,
        type: "pickup",
        fulfillmentSet: (_a = location.fulfillment_sets) == null ? void 0 : _a.find(
          (f) => f.type === "pickup"
        )
      }
    ),
    (0, import_jsx_runtime2.jsx)(
      FulfillmentSet,
      {
        locationId: location.id,
        locationName: location.name,
        type: "shipping",
        fulfillmentSet: (_b = location.fulfillment_sets) == null ? void 0 : _b.find(
          (f) => f.type === "shipping"
        )
      }
    )
  ] });
};
function ShippingOption({
  option,
  fulfillmentSetId,
  locationId
}) {
  const prompt = usePrompt();
  const { t } = useTranslation();
  const isStoreOption = isOptionEnabledInStore(option);
  const { mutateAsync } = useDeleteShippingOption(option.id);
  const handleDelete = async () => {
    const res = await prompt({
      title: t("general.areYouSure"),
      description: t("stockLocations.shippingOptions.delete.confirmation", {
        name: option.name
      }),
      verificationInstruction: t("general.typeToConfirm"),
      verificationText: option.name,
      confirmText: t("actions.delete"),
      cancelText: t("actions.cancel")
    });
    if (!res) {
      return;
    }
    await mutateAsync(void 0, {
      onSuccess: () => {
        toast.success(
          t("stockLocations.shippingOptions.delete.successToast", {
            name: option.name
          })
        );
      },
      onError: (e) => {
        toast.error(e.message);
      }
    });
  };
  return (0, import_jsx_runtime2.jsxs)("div", { className: "flex items-center justify-between px-3 py-2", children: [
    (0, import_jsx_runtime2.jsx)("div", { className: "flex-1", children: (0, import_jsx_runtime2.jsxs)(Text, { size: "small", weight: "plus", children: [
      option.name,
      " - ",
      option.shipping_profile.name,
      " (",
      formatProvider(option.provider_id),
      ")"
    ] }) }),
    (0, import_jsx_runtime2.jsx)(
      Badge,
      {
        className: "mr-4",
        color: isStoreOption ? "grey" : "purple",
        size: "2xsmall",
        rounded: "full",
        children: isStoreOption ? t("general.store") : t("general.admin")
      }
    ),
    (0, import_jsx_runtime2.jsx)(
      ActionMenu,
      {
        groups: [
          {
            actions: [
              {
                icon: (0, import_jsx_runtime2.jsx)(PencilSquare, {}),
                label: t("stockLocations.shippingOptions.edit.action"),
                to: `/settings/locations/${locationId}/fulfillment-set/${fulfillmentSetId}/service-zone/${option.service_zone_id}/shipping-option/${option.id}/edit`
              },
              {
                label: t("stockLocations.shippingOptions.pricing.action"),
                icon: (0, import_jsx_runtime2.jsx)(CurrencyDollar, {}),
                disabled: option.price_type === "calculated",
                to: `/settings/locations/${locationId}/fulfillment-set/${fulfillmentSetId}/service-zone/${option.service_zone_id}/shipping-option/${option.id}/pricing`
              }
            ]
          },
          {
            actions: [
              {
                label: t("actions.delete"),
                icon: (0, import_jsx_runtime2.jsx)(Trash, {}),
                onClick: handleDelete
              }
            ]
          }
        ]
      }
    )
  ] });
}
function ServiceZoneOptions({
  zone,
  locationId,
  fulfillmentSetId,
  type
}) {
  const { t } = useTranslation();
  const shippingOptions = zone.shipping_options.filter(
    (o) => !isReturnOption(o)
  );
  const returnOptions = zone.shipping_options.filter((o) => isReturnOption(o));
  return (0, import_jsx_runtime2.jsxs)("div", { children: [
    (0, import_jsx_runtime2.jsx)(Divider, { variant: "dashed" }),
    (0, import_jsx_runtime2.jsxs)("div", { className: "flex flex-col gap-y-4 px-6 py-4", children: [
      (0, import_jsx_runtime2.jsxs)("div", { className: "item-center flex justify-between", children: [
        (0, import_jsx_runtime2.jsx)("span", { className: "text-ui-fg-subtle txt-small self-center font-medium", children: t(`stockLocations.shippingOptions.create.${type}.label`) }),
        (0, import_jsx_runtime2.jsx)(
          LinkButton,
          {
            to: `/settings/locations/${locationId}/fulfillment-set/${fulfillmentSetId}/service-zone/${zone.id}/shipping-option/create`,
            children: t("stockLocations.shippingOptions.create.action")
          }
        )
      ] }),
      !!shippingOptions.length && (0, import_jsx_runtime2.jsx)("div", { className: "shadow-elevation-card-rest bg-ui-bg-subtle grid divide-y rounded-md", children: shippingOptions.map((o) => (0, import_jsx_runtime2.jsx)(
        ShippingOption,
        {
          option: o,
          locationId,
          fulfillmentSetId
        },
        o.id
      )) })
    ] }),
    (0, import_jsx_runtime2.jsx)(Divider, { variant: "dashed" }),
    (0, import_jsx_runtime2.jsxs)("div", { className: "flex flex-col gap-y-4 px-6 py-4", children: [
      (0, import_jsx_runtime2.jsxs)("div", { className: "item-center flex justify-between", children: [
        (0, import_jsx_runtime2.jsx)("span", { className: "text-ui-fg-subtle txt-small self-center font-medium", children: t("stockLocations.shippingOptions.create.returns.label") }),
        (0, import_jsx_runtime2.jsx)(
          LinkButton,
          {
            to: `/settings/locations/${locationId}/fulfillment-set/${fulfillmentSetId}/service-zone/${zone.id}/shipping-option/create?is_return`,
            children: t("stockLocations.shippingOptions.create.action")
          }
        )
      ] }),
      !!returnOptions.length && (0, import_jsx_runtime2.jsx)("div", { className: "shadow-elevation-card-rest bg-ui-bg-subtle grid divide-y rounded-md", children: returnOptions.map((o) => (0, import_jsx_runtime2.jsx)(
        ShippingOption,
        {
          option: o,
          locationId,
          fulfillmentSetId
        },
        o.id
      )) })
    ] })
  ] });
}
function ServiceZone({
  zone,
  locationId,
  fulfillmentSetId,
  type
}) {
  const { t } = useTranslation();
  const prompt = usePrompt();
  const [open, setOpen] = (0, import_react.useState)(true);
  const { mutateAsync: deleteZone } = useDeleteFulfillmentServiceZone(
    fulfillmentSetId,
    zone.id
  );
  const handleDelete = async () => {
    const res = await prompt({
      title: t("general.areYouSure"),
      description: t("stockLocations.serviceZones.delete.confirmation", {
        name: zone.name
      }),
      confirmText: t("actions.delete"),
      cancelText: t("actions.cancel")
    });
    if (!res) {
      return;
    }
    await deleteZone(void 0, {
      onError: (e) => {
        toast.error(e.message);
      },
      onSuccess: () => {
        toast.success(
          t("stockLocations.serviceZones.delete.successToast", {
            name: zone.name
          })
        );
      }
    });
  };
  const countries2 = (0, import_react.useMemo)(() => {
    const countryGeoZones = zone.geo_zones.filter((g) => g.type === "country");
    const countries3 = countryGeoZones.map(
      ({ country_code }) => countries.find((c) => c.iso_2 === country_code)
    ).filter((c) => !!c);
    if (countryGeoZones.length !== countries3.length) {
      console.warn(
        "Some countries are missing in the static countries list",
        countryGeoZones.filter((g) => !countries3.find((c) => c.iso_2 === g.country_code)).map((g) => g.country_code)
      );
    }
    return countries3.sort((c1, c2) => c1.name.localeCompare(c2.name));
  }, [zone.geo_zones]);
  const [shippingOptionsCount, returnOptionsCount] = (0, import_react.useMemo)(() => {
    const options = zone.shipping_options;
    const optionsCount = options.filter((o) => !isReturnOption(o)).length;
    const returnOptionsCount2 = options.filter(isReturnOption).length;
    return [optionsCount, returnOptionsCount2];
  }, [zone.shipping_options]);
  return (0, import_jsx_runtime2.jsxs)("div", { className: "flex flex-col", children: [
    (0, import_jsx_runtime2.jsxs)("div", { className: "flex flex-row items-center justify-between gap-x-4 px-6 py-4", children: [
      (0, import_jsx_runtime2.jsx)(IconAvatar, { children: (0, import_jsx_runtime2.jsx)(Map, {}) }),
      (0, import_jsx_runtime2.jsxs)("div", { className: "grow-1 flex flex-1 flex-col", children: [
        (0, import_jsx_runtime2.jsx)(Text, { size: "small", leading: "compact", weight: "plus", children: zone.name }),
        (0, import_jsx_runtime2.jsxs)("div", { className: "flex items-center gap-2", children: [
          (0, import_jsx_runtime2.jsx)(
            ListSummary,
            {
              variant: "base",
              list: countries2.map((c) => c.display_name),
              inline: true,
              n: 1
            }
          ),
          (0, import_jsx_runtime2.jsx)("span", { children: "·" }),
          (0, import_jsx_runtime2.jsx)(Text, { className: "text-ui-fg-subtle txt-small", children: t(`stockLocations.shippingOptions.fields.count.${type}`, {
            count: shippingOptionsCount
          }) }),
          (0, import_jsx_runtime2.jsx)("span", { children: "·" }),
          (0, import_jsx_runtime2.jsx)(Text, { className: "text-ui-fg-subtle txt-small", children: t("stockLocations.shippingOptions.fields.count.returns", {
            count: returnOptionsCount
          }) })
        ] })
      ] }),
      (0, import_jsx_runtime2.jsxs)("div", { className: "flex grow-0 items-center gap-4", children: [
        (0, import_jsx_runtime2.jsx)(
          IconButton,
          {
            size: "small",
            onClick: () => setOpen((s) => !s),
            variant: "transparent",
            children: (0, import_jsx_runtime2.jsx)(
              TriangleDownMini,
              {
                style: {
                  transform: `rotate(${!open ? 0 : 180}deg)`,
                  transition: ".2s transform ease-in-out"
                }
              }
            )
          }
        ),
        (0, import_jsx_runtime2.jsx)(
          ActionMenu,
          {
            groups: [
              {
                actions: [
                  {
                    label: t("actions.edit"),
                    icon: (0, import_jsx_runtime2.jsx)(PencilSquare, {}),
                    to: `/settings/locations/${locationId}/fulfillment-set/${fulfillmentSetId}/service-zone/${zone.id}/edit`
                  },
                  {
                    label: t("stockLocations.serviceZones.manageAreas.action"),
                    icon: (0, import_jsx_runtime2.jsx)(Map, {}),
                    to: `/settings/locations/${locationId}/fulfillment-set/${fulfillmentSetId}/service-zone/${zone.id}/areas`
                  }
                ]
              },
              {
                actions: [
                  {
                    label: t("actions.delete"),
                    icon: (0, import_jsx_runtime2.jsx)(Trash, {}),
                    onClick: handleDelete
                  }
                ]
              }
            ]
          }
        )
      ] })
    ] }),
    open && (0, import_jsx_runtime2.jsx)(
      ServiceZoneOptions,
      {
        fulfillmentSetId,
        locationId,
        type,
        zone
      }
    )
  ] });
}
function FulfillmentSet(props) {
  const { t } = useTranslation();
  const prompt = usePrompt();
  const { fulfillmentSet, locationName, locationId, type } = props;
  const fulfillmentSetExists = !!fulfillmentSet;
  const hasServiceZones = !!(fulfillmentSet == null ? void 0 : fulfillmentSet.service_zones.length);
  const { mutateAsync: createFulfillmentSet } = useCreateStockLocationFulfillmentSet(locationId);
  const { mutateAsync: deleteFulfillmentSet } = useDeleteFulfillmentSet(
    fulfillmentSet == null ? void 0 : fulfillmentSet.id
  );
  const handleCreate = async () => {
    await createFulfillmentSet(
      {
        name: `${locationName} ${type === "pickup" ? "pick up" : type}`,
        type
      },
      {
        onSuccess: () => {
          toast.success(t(`stockLocations.fulfillmentSets.enable.${type}`));
        },
        onError: (e) => {
          toast.error(e.message);
        }
      }
    );
  };
  const handleDelete = async () => {
    const res = await prompt({
      title: t("general.areYouSure"),
      description: t(`stockLocations.fulfillmentSets.disable.confirmation`, {
        name: fulfillmentSet == null ? void 0 : fulfillmentSet.name
      }),
      confirmText: t("actions.disable"),
      cancelText: t("actions.cancel")
    });
    if (!res) {
      return;
    }
    await deleteFulfillmentSet(void 0, {
      onSuccess: () => {
        toast.success(t(`stockLocations.fulfillmentSets.disable.${type}`));
      },
      onError: (e) => {
        toast.error(e.message);
      }
    });
  };
  const groups = fulfillmentSet ? [
    {
      actions: [
        {
          icon: (0, import_jsx_runtime2.jsx)(Plus, {}),
          label: t("stockLocations.serviceZones.create.action"),
          to: `/settings/locations/${locationId}/fulfillment-set/${fulfillmentSet.id}/service-zones/create`
        }
      ]
    },
    {
      actions: [
        {
          icon: (0, import_jsx_runtime2.jsx)(Trash, {}),
          label: t("actions.disable"),
          onClick: handleDelete
        }
      ]
    }
  ] : [
    {
      actions: [
        {
          icon: (0, import_jsx_runtime2.jsx)(Plus, {}),
          label: t("actions.enable"),
          onClick: handleCreate
        }
      ]
    }
  ];
  return (0, import_jsx_runtime2.jsx)(Container, { className: "p-0", children: (0, import_jsx_runtime2.jsxs)("div", { className: "flex flex-col divide-y", children: [
    (0, import_jsx_runtime2.jsxs)("div", { className: "flex items-center justify-between px-6 py-4", children: [
      (0, import_jsx_runtime2.jsx)(Heading, { level: "h2", children: t(`stockLocations.fulfillmentSets.${type}.header`) }),
      (0, import_jsx_runtime2.jsxs)("div", { className: "flex items-center gap-4", children: [
        (0, import_jsx_runtime2.jsx)(StatusBadge, { color: fulfillmentSetExists ? "green" : "grey", children: t(
          fulfillmentSetExists ? "statuses.enabled" : "statuses.disabled"
        ) }),
        (0, import_jsx_runtime2.jsx)(ActionMenu, { groups })
      ] })
    ] }),
    fulfillmentSetExists && !hasServiceZones && (0, import_jsx_runtime2.jsx)("div", { className: "flex items-center justify-center py-8 pt-6", children: (0, import_jsx_runtime2.jsx)(
      NoRecords,
      {
        message: t("stockLocations.serviceZones.fields.noRecords"),
        className: "h-fit",
        action: {
          to: `/settings/locations/${locationId}/fulfillment-set/${fulfillmentSet.id}/service-zones/create`,
          label: t("stockLocations.serviceZones.create.action")
        }
      }
    ) }),
    hasServiceZones && (0, import_jsx_runtime2.jsx)("div", { className: "flex flex-col divide-y", children: fulfillmentSet == null ? void 0 : fulfillmentSet.service_zones.map((zone) => (0, import_jsx_runtime2.jsx)(
      ServiceZone,
      {
        zone,
        type,
        locationId,
        fulfillmentSetId: fulfillmentSet.id
      },
      zone.id
    )) })
  ] }) });
}
var Actions = ({ location }) => {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const { mutateAsync } = useDeleteStockLocation(location.id);
  const prompt = usePrompt();
  const handleDelete = async () => {
    const res = await prompt({
      title: t("general.areYouSure"),
      description: t("stockLocations.delete.confirmation", {
        name: location.name
      }),
      verificationText: location.name,
      verificationInstruction: t("general.typeToConfirm"),
      confirmText: t("actions.delete"),
      cancelText: t("actions.cancel")
    });
    if (!res) {
      return;
    }
    await mutateAsync(void 0, {
      onSuccess: () => {
        toast.success(
          t("stockLocations.create.successToast", {
            name: location.name
          })
        );
        navigate("/settings/locations", { replace: true });
      },
      onError: (e) => {
        toast.error(e.message);
      }
    });
  };
  return (0, import_jsx_runtime2.jsx)(
    ActionMenu,
    {
      groups: [
        {
          actions: [
            {
              icon: (0, import_jsx_runtime2.jsx)(PencilSquare, {}),
              label: t("actions.edit"),
              to: `edit`
            },
            {
              icon: (0, import_jsx_runtime2.jsx)(ArchiveBox, {}),
              label: t("stockLocations.edit.viewInventory"),
              to: `/inventory?location_id=${location.id}`
            }
          ]
        },
        {
          actions: [
            {
              icon: (0, import_jsx_runtime2.jsx)(Trash, {}),
              label: t("actions.delete"),
              onClick: handleDelete
            }
          ]
        }
      ]
    }
  );
};
function LocationsSalesChannelsSection({
  location
}) {
  var _a, _b, _c;
  const { t } = useTranslation();
  const { count } = useSalesChannels({ limit: 1, fields: "id" });
  const hasConnectedChannels = !!((_a = location.sales_channels) == null ? void 0 : _a.length);
  return (0, import_jsx_runtime3.jsxs)(Container, { className: "flex flex-col px-6 py-4", children: [
    (0, import_jsx_runtime3.jsxs)("div", { className: "flex items-center justify-between", children: [
      (0, import_jsx_runtime3.jsx)(Heading, { level: "h2", children: t("stockLocations.salesChannels.header") }),
      (0, import_jsx_runtime3.jsx)(
        ActionMenu,
        {
          groups: [
            {
              actions: [
                {
                  label: t("actions.edit"),
                  to: "sales-channels",
                  icon: (0, import_jsx_runtime3.jsx)(PencilSquare, {})
                }
              ]
            }
          ]
        }
      )
    ] }),
    hasConnectedChannels ? (0, import_jsx_runtime3.jsxs)("div", { className: "flex flex-col gap-y-4 pt-4", children: [
      (0, import_jsx_runtime3.jsxs)("div", { className: "grid grid-cols-[28px_1fr] items-center gap-x-3", children: [
        (0, import_jsx_runtime3.jsx)(IconAvatar, { children: (0, import_jsx_runtime3.jsx)(Channels, { className: "text-ui-fg-subtle" }) }),
        (0, import_jsx_runtime3.jsx)(
          ListSummary,
          {
            n: 3,
            className: "text-ui-fg-base",
            inline: true,
            list: ((_b = location.sales_channels) == null ? void 0 : _b.map((sc) => sc.name)) ?? []
          }
        )
      ] }),
      (0, import_jsx_runtime3.jsx)(Text, { className: "text-ui-fg-subtle", size: "small", leading: "compact", children: t("stockLocations.salesChannels.connectedTo", {
        count: (_c = location.sales_channels) == null ? void 0 : _c.length,
        total: count
      }) })
    ] }) : (0, import_jsx_runtime3.jsx)(
      NoRecords,
      {
        className: "h-fit pb-2 pt-6",
        action: {
          label: t("stockLocations.salesChannels.action"),
          to: "sales-channels"
        },
        message: t("stockLocations.salesChannels.noChannels")
      }
    )
  ] });
}
var locations_sales_channels_section_default = LocationsSalesChannelsSection;
function LocationsFulfillmentProvidersSection({
  location
}) {
  const { t } = useTranslation();
  const { fulfillment_providers } = useFulfillmentProviders({
    stock_location_id: location.id,
    fields: "id",
    is_enabled: true
  });
  return (0, import_jsx_runtime4.jsxs)(Container, { className: "flex flex-col px-6 py-4", children: [
    (0, import_jsx_runtime4.jsxs)("div", { className: "flex items-center justify-between", children: [
      (0, import_jsx_runtime4.jsx)(Heading, { level: "h2", children: t("stockLocations.fulfillmentProviders.header") }),
      (0, import_jsx_runtime4.jsx)(
        ActionMenu,
        {
          groups: [
            {
              actions: [
                {
                  label: t("actions.edit"),
                  to: "fulfillment-providers",
                  icon: (0, import_jsx_runtime4.jsx)(PencilSquare, {})
                }
              ]
            }
          ]
        }
      )
    ] }),
    (fulfillment_providers == null ? void 0 : fulfillment_providers.length) ? (0, import_jsx_runtime4.jsx)("div", { className: "flex flex-col gap-y-4 pt-4", children: (0, import_jsx_runtime4.jsx)("div", { className: "grid grid-cols-[28px_1fr] items-center gap-x-3 gap-y-3", children: fulfillment_providers == null ? void 0 : fulfillment_providers.map((fulfillmentProvider) => {
      return (0, import_jsx_runtime4.jsxs)(import_react2.Fragment, { children: [
        (0, import_jsx_runtime4.jsx)(IconAvatar, { children: (0, import_jsx_runtime4.jsx)(HandTruck, { className: "text-ui-fg-subtle" }) }),
        (0, import_jsx_runtime4.jsx)("div", { className: "txt-compact-small", children: formatProvider(fulfillmentProvider.id) })
      ] }, fulfillmentProvider.id);
    }) }) }) : (0, import_jsx_runtime4.jsx)(
      NoRecords,
      {
        className: "h-fit pb-2 pt-6 text-center",
        action: {
          label: t("stockLocations.fulfillmentProviders.action"),
          to: "fulfillment-providers"
        },
        message: t("stockLocations.fulfillmentProviders.noProviders")
      }
    )
  ] });
}
var location_fulfillment_providers_section_default = LocationsFulfillmentProvidersSection;
var LocationDetail = () => {
  const initialData = useLoaderData();
  const { location_id } = useParams();
  const {
    stock_location: location,
    isPending: isLoading,
    isError,
    error
  } = useStockLocation(
    location_id,
    { fields: LOCATION_DETAILS_FIELD },
    { initialData }
  );
  const { getWidgets } = useExtension();
  if (isLoading || !location) {
    return (0, import_jsx_runtime5.jsx)(TwoColumnPageSkeleton, { mainSections: 3, sidebarSections: 2, showJSON: true });
  }
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime5.jsxs)(
    TwoColumnPage,
    {
      widgets: {
        after: getWidgets("location.details.after"),
        before: getWidgets("location.details.before"),
        sideAfter: getWidgets("location.details.side.after"),
        sideBefore: getWidgets("location.details.side.before")
      },
      data: location,
      showJSON: true,
      hasOutlet: true,
      children: [
        (0, import_jsx_runtime5.jsx)(TwoColumnPage.Main, { children: (0, import_jsx_runtime5.jsx)(LocationGeneralSection, { location }) }),
        (0, import_jsx_runtime5.jsxs)(TwoColumnPage.Sidebar, { children: [
          (0, import_jsx_runtime5.jsx)(locations_sales_channels_section_default, { location }),
          (0, import_jsx_runtime5.jsx)(location_fulfillment_providers_section_default, { location })
        ] })
      ]
    }
  );
};
export {
  LocationDetailBreadcrumb as Breadcrumb,
  LocationDetail as Component,
  locationLoader as loader
};
//# sourceMappingURL=location-detail-OOQ5DUUB-WPREOSGD.js.map
