import {
  CountrySelect
} from "./chunk-LULCCYRV.js";
import "./chunk-EGRHWZRV.js";
import {
  KeyboundForm
} from "./chunk-DP54EP6X.js";
import {
  RouteDrawer,
  useRouteModal
} from "./chunk-MVVOBQIC.js";
import "./chunk-HPGXK5DQ.js";
import {
  t
} from "./chunk-WHQIBI5S.js";
import {
  z
} from "./chunk-4XXECALA.js";
import {
  Form,
  useForm
} from "./chunk-XXJU43CK.js";
import {
  useInventoryItem,
  useUpdateInventoryItem
} from "./chunk-AJYMIHLQ.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import "./chunk-JFCIYOH4.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-5GF3RGIE.js";
import {
  useParams
} from "./chunk-T7YBVUWZ.js";
import {
  But<PERSON>,
  Heading,
  Input,
  toast
} from "./chunk-YP2LLXWB.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-ZSQ362B4.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/edit-inventory-item-attributes-B5O5ODS4.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var EditInventoryItemAttributesSchema = z.object({
  height: z.number().positive().optional(),
  width: z.number().positive().optional(),
  length: z.number().positive().optional(),
  weight: z.number().positive().optional(),
  mid_code: z.string().optional(),
  material: z.string().optional(),
  hs_code: z.string().optional(),
  origin_country: z.string().optional()
});
var getDefaultValues = (item) => {
  return {
    height: item.height ?? void 0,
    width: item.width ?? void 0,
    length: item.length ?? void 0,
    weight: item.weight ?? void 0,
    mid_code: item.mid_code ?? void 0,
    material: item.material ?? void 0,
    hs_code: item.hs_code ?? void 0,
    origin_country: item.origin_country ?? void 0
  };
};
var EditInventoryItemAttributesForm = ({
  item
}) => {
  const { t: t2 } = useTranslation();
  const { handleSuccess } = useRouteModal();
  const form = useForm({
    defaultValues: getDefaultValues(item),
    resolver: t(EditInventoryItemAttributesSchema)
  });
  const { mutateAsync, isPending: isLoading } = useUpdateInventoryItem(item.id);
  const handleSubmit = form.handleSubmit(async (values) => {
    await mutateAsync(values, {
      onSuccess: () => {
        toast.success(t2("inventory.toast.updateItem"));
        handleSuccess();
      },
      onError: (error) => toast.error(error.message)
    });
  });
  return (0, import_jsx_runtime.jsx)(RouteDrawer.Form, { form, children: (0, import_jsx_runtime.jsxs)(
    KeyboundForm,
    {
      onSubmit: handleSubmit,
      className: "flex flex-1 flex-col overflow-hidden",
      children: [
        (0, import_jsx_runtime.jsxs)(RouteDrawer.Body, { className: "flex flex-1 flex-col gap-y-4 overflow-auto", children: [
          (0, import_jsx_runtime.jsx)(
            Form.Field,
            {
              control: form.control,
              name: "height",
              render: ({ field: { onChange, value, ...field } }) => {
                return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                  (0, import_jsx_runtime.jsx)(Form.Label, { optional: true, children: t2("fields.height") }),
                  (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(
                    Input,
                    {
                      type: "number",
                      min: 0,
                      value: value || "",
                      onChange: (e) => {
                        const value2 = e.target.value;
                        if (value2 === "") {
                          onChange(null);
                        } else {
                          onChange(parseFloat(value2));
                        }
                      },
                      ...field
                    }
                  ) }),
                  (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                ] });
              }
            }
          ),
          (0, import_jsx_runtime.jsx)(
            Form.Field,
            {
              control: form.control,
              name: "width",
              render: ({ field: { onChange, value, ...field } }) => {
                return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                  (0, import_jsx_runtime.jsx)(Form.Label, { optional: true, children: t2("fields.width") }),
                  (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(
                    Input,
                    {
                      type: "number",
                      min: 0,
                      value: value || "",
                      onChange: (e) => {
                        const value2 = e.target.value;
                        if (value2 === "") {
                          onChange(null);
                        } else {
                          onChange(parseFloat(value2));
                        }
                      },
                      ...field
                    }
                  ) }),
                  (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                ] });
              }
            }
          ),
          (0, import_jsx_runtime.jsx)(
            Form.Field,
            {
              control: form.control,
              name: "length",
              render: ({ field: { onChange, value, ...field } }) => {
                return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                  (0, import_jsx_runtime.jsx)(Form.Label, { optional: true, children: t2("fields.length") }),
                  (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(
                    Input,
                    {
                      type: "number",
                      min: 0,
                      value: value || "",
                      onChange: (e) => {
                        const value2 = e.target.value;
                        if (value2 === "") {
                          onChange(null);
                        } else {
                          onChange(parseFloat(value2));
                        }
                      },
                      ...field
                    }
                  ) }),
                  (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                ] });
              }
            }
          ),
          (0, import_jsx_runtime.jsx)(
            Form.Field,
            {
              control: form.control,
              name: "weight",
              render: ({ field: { onChange, value, ...field } }) => {
                return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                  (0, import_jsx_runtime.jsx)(Form.Label, { optional: true, children: t2("fields.weight") }),
                  (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(
                    Input,
                    {
                      type: "number",
                      min: 0,
                      value: value || "",
                      onChange: (e) => {
                        const value2 = e.target.value;
                        if (value2 === "") {
                          onChange(null);
                        } else {
                          onChange(parseFloat(value2));
                        }
                      },
                      ...field
                    }
                  ) }),
                  (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                ] });
              }
            }
          ),
          (0, import_jsx_runtime.jsx)(
            Form.Field,
            {
              control: form.control,
              name: "mid_code",
              render: ({ field }) => {
                return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                  (0, import_jsx_runtime.jsx)(Form.Label, { optional: true, children: t2("fields.midCode") }),
                  (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(Input, { ...field }) }),
                  (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                ] });
              }
            }
          ),
          (0, import_jsx_runtime.jsx)(
            Form.Field,
            {
              control: form.control,
              name: "hs_code",
              render: ({ field }) => {
                return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                  (0, import_jsx_runtime.jsx)(Form.Label, { optional: true, children: t2("fields.hsCode") }),
                  (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(Input, { ...field }) }),
                  (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                ] });
              }
            }
          ),
          (0, import_jsx_runtime.jsx)(
            Form.Field,
            {
              control: form.control,
              name: "material",
              render: ({ field }) => {
                return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                  (0, import_jsx_runtime.jsx)(Form.Label, { optional: true, children: t2("fields.material") }),
                  (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(Input, { ...field }) }),
                  (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                ] });
              }
            }
          ),
          (0, import_jsx_runtime.jsx)(
            Form.Field,
            {
              control: form.control,
              name: "origin_country",
              render: ({ field }) => {
                return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                  (0, import_jsx_runtime.jsx)(Form.Label, { optional: true, children: t2("fields.countryOfOrigin") }),
                  (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(CountrySelect, { ...field }) }),
                  (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                ] });
              }
            }
          )
        ] }),
        (0, import_jsx_runtime.jsx)(RouteDrawer.Footer, { children: (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center justify-end gap-x-2", children: [
          (0, import_jsx_runtime.jsx)(RouteDrawer.Close, { asChild: true, children: (0, import_jsx_runtime.jsx)(Button, { variant: "secondary", size: "small", children: t2("actions.cancel") }) }),
          (0, import_jsx_runtime.jsx)(Button, { type: "submit", size: "small", isLoading, children: t2("actions.save") })
        ] }) })
      ]
    }
  ) });
};
var InventoryItemAttributesEdit = () => {
  const { id } = useParams();
  const { t: t2 } = useTranslation();
  const {
    inventory_item: inventoryItem,
    isPending: isLoading,
    isError,
    error
  } = useInventoryItem(id);
  const ready = !isLoading && inventoryItem;
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime2.jsxs)(RouteDrawer, { children: [
    (0, import_jsx_runtime2.jsx)(RouteDrawer.Header, { children: (0, import_jsx_runtime2.jsx)(Heading, { children: t2("products.editAttributes") }) }),
    ready && (0, import_jsx_runtime2.jsx)(EditInventoryItemAttributesForm, { item: inventoryItem })
  ] });
};
export {
  InventoryItemAttributesEdit as Component
};
//# sourceMappingURL=edit-inventory-item-attributes-B5O5ODS4-FQM6NFLO.js.map
